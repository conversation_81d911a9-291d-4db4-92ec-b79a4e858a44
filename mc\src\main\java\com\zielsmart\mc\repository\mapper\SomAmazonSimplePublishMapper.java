package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonSimplePublishPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper.SomAmazonSimplePublishMapper
 * @title SomAmazonSimplePublishMapper
 * @description Amazon简单上货管理
 * @date 2025-07-18 10:15:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somAmazonSimplePublish")
public interface SomAmazonSimplePublishMapper extends BaseMapper<SomAmazonSimplePublish> {

    /**
     * 分页查询
     *
     * @param searchVo 查询参数
     * @param pageRequest 分页参数
     * @return 分页结果
     */
    PageResult<SomAmazonSimplePublishVo> queryByPage(@Param("searchVo")SomAmazonSimplePublishPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 查询简单上货导出数据
     *
     * @param searchVo 查询参数
     * @return 导出数据
     */
    List<SomAmazonSimplePublish> querySimplePublishExportData(@Param("searchVo")SomAmazonSimplePublishPageSearchVo searchVo);


    /**
     * 批量更新
     *
     * @param simplePublishes 简单上货集合
     */
    default void importUpdateBatch(@Param("simplePublishes") List<SomAmazonSimplePublish> simplePublishes) {
        this.getSQLManager().updateBatch(SqlId.of("somAmazonSimplePublish.importUpdate"), simplePublishes);
    }


}
