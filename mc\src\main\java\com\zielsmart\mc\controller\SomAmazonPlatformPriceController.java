package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonPlatformPriceService;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceEditDetailVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceOaReturnVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonPlatformPriceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@RequestMapping(value = "/somAmazonPlatformPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Controller
@Tag(name = "亚马逊平台价管理管理")
public class SomAmazonPlatformPriceController extends BasicController {

    @Resource
    SomAmazonPlatformPriceService somAmazonPlatformPriceService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonPlatformPriceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonPlatformPriceVo>> queryByPage(@RequestBody SomAmazonPlatformPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonPlatformPriceService.queryByPage(searchVo));
    }

    @Operation(summary = "编辑详情")
    @PostMapping(value = "/edit-detail")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomAmazonPlatformPriceEditDetailVo> editDetail(@RequestBody SomAmazonPlatformPriceVo somAmazonPlatformPriceVo) throws ValidateException, InterruptedException {
        return ResultVo.ofSuccess(somAmazonPlatformPriceService.editDetail(somAmazonPlatformPriceVo));
    }

    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody SomAmazonPlatformPriceVo somAmazonPlatformPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        //消息在前端只展示  不影响编辑操作
        String msg = somAmazonPlatformPriceService.edit(somAmazonPlatformPriceVo, tokenUser);
        return ResultVo.ofSuccess(null,msg);
    }

    @Operation(summary = "计算成交价")
    @PostMapping(value = "/calc-sale-price")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<BigDecimal> calcSalePrice(@RequestBody SomAmazonPlatformPriceVo somAmazonPlatformPriceVo) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonPlatformPriceService.calcSalePrice(somAmazonPlatformPriceVo));
    }

    @Operation(summary = "批量提交调价申请校验")
    @PostMapping(value = "/batch-adjust-price-validate")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomAmazonPlatformPriceVo>> batchAdjustPriceValidate(@RequestBody SomAmazonPlatformPriceVo priceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, InterruptedException {
        SomAmazonPlatformPriceVo result = somAmazonPlatformPriceService.batchAdjustPriceValidate(priceVo, tokenUser);
        return ResultVo.ofSuccess(result.getBatchAdjustPriceList(), result.getErrorMsg());
    }

    @Operation(summary = "批量提交调价申请")
    @PostMapping(value = "/batch-adjust-price")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchAdjustPrice(@RequestBody SomAmazonPlatformPriceVo priceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somAmazonPlatformPriceService.batchAdjustPrice(priceVo, tokenUser, false);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "批量调价特权人使用")
    @PostMapping(value = "/batch-adjust-price-privileged")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchAdjustPricePrivileged(@RequestBody SomAmazonPlatformPriceVo priceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somAmazonPlatformPriceService.batchAdjustPrice(priceVo, tokenUser, true);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "价格推送")
    @PostMapping(value = "/push-price")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> pushPrice(@RequestBody SomAmazonPlatformPriceVo priceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, InterruptedException {
        somAmazonPlatformPriceService.pushPrice(priceVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "修复OA价格推送")
    @PostMapping(value = "/repair-push-price")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> repairPushPrice(@RequestBody SomAmazonPlatformPriceVo priceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, InterruptedException {
        somAmazonPlatformPriceService.repairPushPrice(priceVo, tokenUser);
        return ResultVo.ofSuccess();
    }


    /**
     * save
     *
     * @param somAmazonPlatformPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomAmazonPlatformPriceVo somAmazonPlatformPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonPlatformPriceService.save(somAmazonPlatformPriceVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somAmazonPlatformPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomAmazonPlatformPriceVo somAmazonPlatformPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonPlatformPriceService.update(somAmazonPlatformPriceVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        importParams.setStartRows(1);

        ExcelImportResult<SomAmazonPlatformPriceVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonPlatformPriceVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String msg = somAmazonPlatformPriceService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess(null,msg);
    }


    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonPlatformPricePageSearchVo searchVo) {
        String data = somAmazonPlatformPriceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载价格导入模板")
    @GetMapping(value = "/download")
    public String downloadPriceExcel() {
        return "forward:/static/excel/AmazonPlatformPriceTemplate.xlsx";
    }


    @Operation(summary = "OA返回审批结果")
    @PostMapping(value = "/sync-som", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> syncSom(@RequestBody SomAmazonPlatformPriceOaReturnVo resultVo) throws ValidateException, InterruptedException {
        somAmazonPlatformPriceService.syncSom(resultVo);
        return ResultVo.ofSuccess();
    }


}
