package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuLocalSku;
import com.zielsmart.mc.vo.SomTemuImageUrlReport;
import com.zielsmart.mc.vo.SomTemuListingReport;
import com.zielsmart.mc.vo.SomTemuLocalSkuPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalSkuVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomTemuLocalSkuMapper
 * @description SomTemuLocalSkuMapper
 * @date 2025-06-26 15:49:15
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuLocalSku")
public interface SomTemuLocalSkuMapper extends BaseMapper<SomTemuLocalSku> {

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return PageResult<SomTemuLocalSkuVo>
     */
    PageResult<SomTemuLocalSkuVo> queryByPage(@Param("searchVo")SomTemuLocalSkuPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 获取URL链接下载需要的sku记录
     *
     * @param searchVo 入参
     * @return List<SomTemuImageUrlReport>
     */
    List<SomTemuImageUrlReport> getUrlExportData(@Param("searchVo") SomTemuLocalSkuPageSearchVo searchVo);

    PageResult<SomTemuListingReport> stockReport(@Param("searchVo")SomTemuLocalSkuPageSearchVo searchVo, PageRequest pageRequest);
}
