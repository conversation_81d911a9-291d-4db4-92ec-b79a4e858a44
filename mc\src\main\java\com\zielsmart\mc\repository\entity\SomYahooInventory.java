package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2023-02-03
 */

@Table(name = "mc.som_yahoo_inventory")
public class SomYahooInventory implements java.io.Serializable {
    /**
     * 主键id
     */
    @AssignID
    private String aid;
    /**
     * 产品编码
     */
    @Column("code")
    private String code;
    /**
     * 子产品编码
     */
    @Column("sub_code")
    private String subCode;
    /**
     * 库存数量
     */
    @Column("quantity")
    private Integer quantity;
    /**
     * 库存不足时，是否还能继续订购。
     * 0.库存不足时，不允许订购。
     * 1.库存不足时，也可以支持订购。
     */
    @Column("allow_overdraft")
    private Integer allowOverdraft;
    /**
     * 如果出现库存关闭标志，则将其视为缺货状态。
     * 0：取消库存关闭状态
     * 如果未指定或为空，则不更新库存关闭标志。
     */
    @Column("stock_close")
    private Integer stockClose;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomYahooInventory() {
    }

    /**
     * 主键id
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键id
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 产品编码
     *
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * 产品编码
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 子产品编码
     *
     * @return
     */
    public String getSubCode() {
        return subCode;
    }

    /**
     * 子产品编码
     *
     * @param subCode
     */
    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    /**
     * 库存数量
     *
     * @return
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 库存数量
     *
     * @param quantity
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 库存不足时，是否还能继续订购。
     * 0.库存不足时，不允许订购。
     * 1.库存不足时，也可以支持订购。
     *
     * @return
     */
    public Integer getAllowOverdraft() {
        return allowOverdraft;
    }

    /**
     * 库存不足时，是否还能继续订购。
     * 0.库存不足时，不允许订购。
     * 1.库存不足时，也可以支持订购。
     *
     * @param allowOverdraft
     */
    public void setAllowOverdraft(Integer allowOverdraft) {
        this.allowOverdraft = allowOverdraft;
    }

    /**
     * 如果出现库存关闭标志，则将其视为缺货状态。
     * 0：取消库存关闭状态
     * 如果未指定或为空，则不更新库存关闭标志。
     *
     * @return
     */
    public Integer getStockClose() {
        return stockClose;
    }

    /**
     * 如果出现库存关闭标志，则将其视为缺货状态。
     * 0：取消库存关闭状态
     * 如果未指定或为空，则不更新库存关闭标志。
     *
     * @param stockClose
     */
    public void setStockClose(Integer stockClose) {
        this.stockClose = stockClose;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
