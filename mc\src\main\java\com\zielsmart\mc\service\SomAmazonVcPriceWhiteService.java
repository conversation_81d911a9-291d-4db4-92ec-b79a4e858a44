package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomAmazonVcPriceWhite;
import com.zielsmart.mc.repository.mapper.SomAmazonVcPriceWhiteMapper;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhiteVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-01-21 15:49:07
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomAmazonVcPriceWhiteService {

    @Resource
    private SomAmazonVcPriceWhiteMapper somAmazonVcPriceWhiteMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomAmazonVcPriceWhiteVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomAmazonVcPriceWhiteVo> queryByPage(SomAmazonVcPriceWhitePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomAmazonVcPriceWhiteVo> pageResult = dynamicSqlManager.getMapper(SomAmazonVcPriceWhiteMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomAmazonVcPriceWhiteVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somAmazonVcPriceWhiteVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomAmazonVcPriceWhiteVo somAmazonVcPriceWhiteVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcPriceWhiteVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String site = somAmazonVcPriceWhiteVo.getSite();
        String sellerSku = somAmazonVcPriceWhiteVo.getSellerSku();
        if (!StrUtil.isAllNotEmpty(site, sellerSku)) {
            throw new ValidateException("站点/展示码不能为空！");
        }
        long count = somAmazonVcPriceWhiteMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("seller_sku", sellerSku)
                .count();
        if (count > 0L) {
            throw new ValidateException("展示码已存在，新增失败！");
        }
        somAmazonVcPriceWhiteVo.setAid(IdUtil.fastSimpleUUID());
        somAmazonVcPriceWhiteVo.setCreateNum(tokenUser.getJobNumber());
        somAmazonVcPriceWhiteVo.setCreateName(tokenUser.getUserName());
        somAmazonVcPriceWhiteVo.setCreateTime(DateTime.now().toJdkDate());
        somAmazonVcPriceWhiteMapper.insert(ConvertUtils.beanConvert(somAmazonVcPriceWhiteVo, SomAmazonVcPriceWhite.class));
    }

    /**
     * update
     * 修改
     *
     * @param somAmazonVcPriceWhiteVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomAmazonVcPriceWhiteVo somAmazonVcPriceWhiteVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcPriceWhiteVo) || StrUtil.isEmpty(somAmazonVcPriceWhiteVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        somAmazonVcPriceWhiteMapper.createLambdaQuery()
                .andEq("aid", somAmazonVcPriceWhiteVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somAmazonVcPriceWhiteVo, SomAmazonVcPriceWhite.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somAmazonVcPriceWhiteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomAmazonVcPriceWhiteVo somAmazonVcPriceWhiteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcPriceWhiteVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somAmazonVcPriceWhiteMapper.createLambdaQuery().andIn("aid", somAmazonVcPriceWhiteVo.getAidList()).delete();
    }

    public String export(SomAmazonVcPriceWhitePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomAmazonVcPriceWhiteVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC比价白名单管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomAmazonVcPriceWhiteVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
