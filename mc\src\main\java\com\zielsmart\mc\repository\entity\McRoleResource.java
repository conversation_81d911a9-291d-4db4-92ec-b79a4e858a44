package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 
* gen by 代码生成器 2021-07-16
*/

@Table(name="mc.mc_role_resource")
public class McRoleResource implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 角色id
	 */
	@Column("role_id")
	private String roleId ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;

	public McRoleResource() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 角色id
	*@return
	*/
	public String getRoleId(){
		return  roleId;
	}
	/**
	* 角色id
	*@param  roleId
	*/
	public void setRoleId(String roleId ){
		this.roleId = roleId;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}

}
