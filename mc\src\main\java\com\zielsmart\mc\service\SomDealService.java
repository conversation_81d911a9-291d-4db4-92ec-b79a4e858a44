package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.SomDealMapper;
import com.zielsmart.mc.repository.mapper.SomDealOfTheDayMapper;
import com.zielsmart.mc.repository.mapper.SomFreeAddDealMapper;
import com.zielsmart.mc.third.IBiService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SomDealService {

    @Resource
    private SomDealMapper mapper;
    @Resource
    private SomFreeAddDealMapper freeAddDealMapper;
    @Resource
    private SomDealOfTheDayMapper dealOfTheDayMapper;
    @Resource
    private McProductSalesMapper mcProductSalesMapper;
    @Resource
    private IBiService biService;
    @Value("${bi.magic.head.token}")
    private String biToken;

    /**
     * searchDealList
     * 查询Deal折扣
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomDealVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public List<SomDealVo> searchDealList(SomDealSearchVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || (StrUtil.isBlank(searchVo.getSite()) && CollectionUtil.isEmpty(searchVo.getSites()))
                || (StrUtil.isBlank(searchVo.getSellerSku()) && CollectionUtil.isEmpty(searchVo.getSellerSkus()))) {
            throw new ValidateException("参数存在空值,请检查");
        }
        if (CollectionUtil.isEmpty(searchVo.getSites())) {
            searchVo.setSites(Collections.singletonList(searchVo.getSite()));
        }
        if (CollectionUtil.isEmpty(searchVo.getSellerSkus())) {
            searchVo.setSellerSkus(Collections.singletonList(searchVo.getSellerSku()));
        }
        return mapper.searchDealList(searchVo);
    }

    /**
     * checkDeal
     * 动态获取LD/7DD/DOTD秒杀校验规则
     *
     * @param id
     * @param site
     * @param asinList
     * @param planStartDate
     * @param configVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void checkDeal(String id, String site, List<String> sellerSkuList, List<String> asinList, Date planStartDate, SomDealRuleConfigVo configVo) throws ValidateException {
        log.info("{}站点于{}时秒杀配置规则为{}", site, DateUtil.formatDate(DateTime.now().toJdkDate()), configVo.getRules());
        JSONArray ruleArr = JSONUtil.parseArray(configVo.getRules());
        List<SomDealRuleConfigExtVo> configList = JSONUtil.toList(ruleArr, SomDealRuleConfigExtVo.class);
        SomDealRuleConfigExtVo configLD = configList.stream().filter(f -> StrUtil.equals(f.getDealType(), "Lighting Deal")).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(configLD) && configLD.getRule() != 0) {
            Date ldBeginDate = DateUtil.offsetDay(planStartDate, 0 - configLD.getRule()).toJdkDate();
            Date ldEndDate = DateUtil.offsetDay(planStartDate, configLD.getRule()).toJdkDate();
            Integer count = freeAddDealMapper.checkLDActivity(id, site, asinList, ldBeginDate, ldEndDate);
            if (0 < count) {
                throw new ValidateException("展示码" + JSONUtil.toJsonStr(sellerSkuList) + "同一ASIN" + JSONUtil.toJsonStr(asinList) + "开跑时间±" + configLD.getRule() + "天，不能有未结束的LD");
            }
        }
        SomDealRuleConfigExtVo config7DD = configList.stream().filter(f -> StrUtil.equals(f.getDealType(), "7 Day Deal")).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(config7DD) && config7DD.getRule() != 0) {
            Date ddBeginDate = DateUtil.offsetDay(planStartDate, 0 - config7DD.getRule()).toJdkDate();
            Date ddEndDate = DateUtil.offsetDay(planStartDate, config7DD.getRule()).toJdkDate();
            Integer count = freeAddDealMapper.check7DDActivity(id, site, asinList, ddBeginDate, ddEndDate);
            if (0 < count) {
                throw new ValidateException("展示码" + JSONUtil.toJsonStr(sellerSkuList) + "同一ASIN" + JSONUtil.toJsonStr(asinList) + "开跑时间±" + config7DD.getRule() + "天，不能有未结束的7DD");
            }
        }
        SomDealRuleConfigExtVo configDOTD = configList.stream().filter(f -> StrUtil.equals(f.getDealType(), "DOTD")).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(configDOTD) && configDOTD.getRule() != 0) {
            Date dotdBeginDate = DateUtil.offsetDay(planStartDate, 0 - configDOTD.getRule()).toJdkDate();
            Date dotdEndDate = DateUtil.offsetDay(planStartDate, configDOTD.getRule()).toJdkDate();
            Integer count = dealOfTheDayMapper.checkRepeat(id, site, asinList.get(0), dotdBeginDate, dotdEndDate);
            if (0 < count) {
                throw new ValidateException("展示码" + JSONUtil.toJsonStr(sellerSkuList) + "同一ASIN" + JSONUtil.toJsonStr(asinList) + "开跑时间±" + configDOTD.getRule() + "天，不能有未结束的DOTD");
            }
        }
    }

    // 暂未使用 保留
    // 已合并至 somCouponItems/queryCouponPromotionDiscount
    public BiAmazonProfitCalculationVo getBiAmazonProfitCalculation(BiAmazonProfitCalculationVo searchExVo) throws ValidateException {
        // 获取productMainCode
        McProductSales mcProductSales = mcProductSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("display_product_code", searchExVo.getDisplayProductCode())
                .andEq("site", searchExVo.getSite())
                .single();

        List<BiAmazonProfitCalculationVo> biBody = new ArrayList<>();
        BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
        bi.setSite(searchExVo.getSite());
        bi.setPrice(searchExVo.getDealPrice());
        bi.setDisplayProductCode(searchExVo.getDisplayProductCode());
        bi.setProductMainCode(mcProductSales.getProductMainCode());
        bi.setPromotionType(searchExVo.getPromotionType());
        biBody.add(bi);

        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x2)
        );
        BigDecimal dealPriceGross = BigDecimal.ZERO; // 秒杀价毛利率
        BigDecimal dealBurstCoefficient = BigDecimal.ZERO; // 活动预计爆发系数
        BigDecimal dmsLast30day = BigDecimal.ZERO; // 近三十天DMS
        BigDecimal categoryGross = BigDecimal.ZERO; // 三级分类近四周毛利率
        String key = searchExVo.getSite() + searchExVo.getDisplayProductCode();
        if (biMap.containsKey(key)) {
            dealPriceGross = biMap.get(key).getGross();
            dealPriceGross = dealPriceGross != null ? dealPriceGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            dealBurstCoefficient = biMap.get(key).getDealBurstCoefficient();
            dealBurstCoefficient = dealBurstCoefficient != null ? dealBurstCoefficient.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            dmsLast30day = biMap.get(key).getDmsLast30day();
            dmsLast30day = dmsLast30day != null ? dmsLast30day.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            categoryGross = biMap.get(key).getCategoryGross();
            categoryGross = categoryGross != null ? categoryGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        }

        searchExVo.setDealPriceGross(dealPriceGross);
        searchExVo.setDealBurstCoefficient(dealBurstCoefficient);
        searchExVo.setDmsLast30day(dmsLast30day);
        searchExVo.setCategoryGross(categoryGross);

        return searchExVo;
    }
}
