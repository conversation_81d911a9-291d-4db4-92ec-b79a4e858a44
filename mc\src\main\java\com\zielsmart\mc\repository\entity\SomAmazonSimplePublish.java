package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;

/*
 * Amazon简单上货
 */
@Table(name = "mc.som_amazon_simple_publish")
public class SomAmazonSimplePublish implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;

    /**
     * 客户编码
     */
    @Column("customer_code")
    private String customerCode;

    /**
     * 站点
     */
    @Column("site")
    private String site;

    /**
     * 平台类目ID
     */
    @Column("platform_category_id")
    private String platformCategoryId;
    /**
     * 平台类目关键字
     */
    @Column("platform_category_type_keyword")
    private String platformCategoryTypeKeyword;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * EAN
     */
    @Column("ean")
    private String ean;
    /**
     * FNSKU
     */
    @Column("fnsku")
    private String fnsku;
    /**
     * 0.非寄售  1.寄售
     */
    @Column("consignment_sales")
    private Integer consignmentSales;
    /**
     * 预计开售日期 = 预计到仓日期
     */
    @Column("estimate_arrival_warehouse_date")
    private Date estimateArrivalWarehouseDate;
    /**
     * 原产国
     */
    @Column("country_of_origin")
    private String countryOfOrigin;
    /**
     * 品牌
     */
    @Column("brand")
    private String brand;
    /**
     * 不可售处理，枚举值： 10 召回  20 销毁
     */
    @Column("consignment_stock_processing")
    private Integer consignmentStockProcessing;
    /**
     * 价格
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * IPQ
     */
    @Column("item_package_quantity")
    private Integer itemPackageQuantity;
    /**
     * 一个订单最多订购数量。默认值为5
     */
    @Column("max_order_quantity")
    private Integer maxOrderQuantity;
    /**
     * PCS
     */
    @Column("unit_count")
    private Integer unitCount;

    /**
     * PCS 单位
     */
    @Column("unit_count_type")
    private String unitCountType;

    /**
     * 组件编号 = 产品编码
     */
    @Column("part_number")
    private String partNumber;
    /**
     * 库存数量。默认为0
     */
    @Column("quantity")
    private Integer quantity;
    /**
     * 首图URL链接
     */
    @Column("main_product_image_locator")
    private String mainProductImageLocator;
    /**
     * 标题
     */
    @Column("item_name")
    private String itemName;
    /**
     * 长描述
     */
    @Column("product_description")
    private String productDescription;
    /**
     * 欧洲能效等级
     */
    @Column("eu_energy_efficiency_rating")
    private String euEnergyEfficiencyRating;
    /**
     * 强制性能效产品注册 注册编号
     */
    @Column("eprel_registration_number")
    private String eprelRegistrationNumber;
    /**
     * 是否需要电池，枚举值： Yes No
     */
    @Column("batteries_required")
    private String batteriesRequired;
    /**
     * 是否包含电池，枚举值：Yes No
     */
    @Column("batteries_included")
    private String batteriesIncluded;
    /**
     * 危险品法规。默认值：Not Applicable
     */
    @Column("dangerous_goods_regulations")
    private String dangerousGoodsRegulations;
    /**
     * GPSR安全标识：Yes No
     */
    @Column("gpsr_safety_attestation")
    private String gpsrSafetyAttestation;
    /**
     * 运费模板
     */
    @Column("shipping_template_group")
    private String shippingTemplateGroup;
    /**
     * 状态，枚举值：10 未发布 20 已发布
     */
    @Column("status")
    private Integer status;
    /**
     * 电子物料是否完备：0 否 1 是
     */
    @Column("material_is_complete")
    private Integer materialIsComplete;
    /**
     * 业务组编码
     */
    @Column("sales_group_code")
    private String salesGroupCode;
    /**
     * 销售负责人工号
     */
    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;
    /**
     * 销售助理工号
     */
    @Column("operation_empt_code")
    private String operationEmptCode;
    /**
     * 发货方式
     */
    @Column("fulfillment_center_id")
    private String fulfillmentCenterId;
    /**
     * 产品ID类型。默认：EAN
     */
    @Column("external_product_id_type")
    private String externalProductIdType;
    /**
     * 模板中的操作标识
     */
    @Column("update_delete")
    private String updateDelete;
    /**
     * 条件类型，默认：New
     */
    @Column("condition_type")
    private String conditionType;
    /**
     * 尺寸信息
     */
    @Column("size_name")
    private String sizeName;
    /**
     * 内箱长
     */
    @Column("package_length")
    private BigDecimal packageLength;
    /**
     * 内箱宽
     */
    @Column("package_width")
    private BigDecimal packageWidth;
    /**
     * 内箱高
     */
    @Column("package_height")
    private BigDecimal packageHeight;
    /**
     * 内箱长单位
     */
    @Column("package_length_unit_of_measure")
    private String packageLengthUnitOfMeasure;
    /**
     * 内箱宽单位
     */
    @Column("package_width_unit_of_measure")
    private String packageWidthUnitOfMeasure;
    /**
     * 内箱高单位
     */
    @Column("package_height_unit_of_measure")
    private String packageHeightUnitOfMeasure;
    /**
     * 内箱毛重
     */
    @Column("package_weight")
    private BigDecimal packageWeight;
    /**
     * 内箱毛重单位
     */
    @Column("package_weight_unit_of_measure")
    private String packageWeightUnitOfMeasure;
    /**
     * 颜色
     */
    @Column("color_name")
    private String colorName;
    /**
     * List Price
     */
    @Column("list_price_with_tax")
    private BigDecimal listPriceWithTax;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;

    public SomAmazonSimplePublish() {
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getPlatformCategoryId() {
        return platformCategoryId;
    }

    public void setPlatformCategoryId(String platformCategoryId) {
        this.platformCategoryId = platformCategoryId;
    }

    public String getPlatformCategoryTypeKeyword() {
        return platformCategoryTypeKeyword;
    }

    public void setPlatformCategoryTypeKeyword(String platformCategoryTypeKeyword) {
        this.platformCategoryTypeKeyword = platformCategoryTypeKeyword;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getEan() {
        return ean;
    }

    public void setEan(String ean) {
        this.ean = ean;
    }

    public String getFnsku() {
        return fnsku;
    }

    public void setFnsku(String fnsku) {
        this.fnsku = fnsku;
    }

    public Integer getConsignmentSales() {
        return consignmentSales;
    }

    public void setConsignmentSales(Integer consignmentSales) {
        this.consignmentSales = consignmentSales;
    }

    public Date getEstimateArrivalWarehouseDate() {
        return estimateArrivalWarehouseDate;
    }

    public void setEstimateArrivalWarehouseDate(Date estimateArrivalWarehouseDate) {
        this.estimateArrivalWarehouseDate = estimateArrivalWarehouseDate;
    }

    public String getCountryOfOrigin() {
        return countryOfOrigin;
    }

    public void setCountryOfOrigin(String countryOfOrigin) {
        this.countryOfOrigin = countryOfOrigin;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getConsignmentStockProcessing() {
        return consignmentStockProcessing;
    }

    public void setConsignmentStockProcessing(Integer consignmentStockProcessing) {
        this.consignmentStockProcessing = consignmentStockProcessing;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getItemPackageQuantity() {
        return itemPackageQuantity;
    }

    public void setItemPackageQuantity(Integer itemPackageQuantity) {
        this.itemPackageQuantity = itemPackageQuantity;
    }

    public Integer getMaxOrderQuantity() {
        return maxOrderQuantity;
    }

    public void setMaxOrderQuantity(Integer maxOrderQuantity) {
        this.maxOrderQuantity = maxOrderQuantity;
    }

    public Integer getUnitCount() {
        return unitCount;
    }

    public void setUnitCount(Integer unitCount) {
        this.unitCount = unitCount;
    }

    public String getUnitCountType() {
        return unitCountType;
    }

    public void setUnitCountType(String unitCountType) {
        this.unitCountType = unitCountType;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getMainProductImageLocator() {
        return mainProductImageLocator;
    }

    public void setMainProductImageLocator(String mainProductImageLocator) {
        this.mainProductImageLocator = mainProductImageLocator;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public String getEuEnergyEfficiencyRating() {
        return euEnergyEfficiencyRating;
    }

    public void setEuEnergyEfficiencyRating(String euEnergyEfficiencyRating) {
        this.euEnergyEfficiencyRating = euEnergyEfficiencyRating;
    }

    public String getEprelRegistrationNumber() {
        return eprelRegistrationNumber;
    }

    public void setEprelRegistrationNumber(String eprelRegistrationNumber) {
        this.eprelRegistrationNumber = eprelRegistrationNumber;
    }

    public String getBatteriesRequired() {
        return batteriesRequired;
    }

    public void setBatteriesRequired(String batteriesRequired) {
        this.batteriesRequired = batteriesRequired;
    }

    public String getBatteriesIncluded() {
        return batteriesIncluded;
    }

    public void setBatteriesIncluded(String batteriesIncluded) {
        this.batteriesIncluded = batteriesIncluded;
    }

    public String getDangerousGoodsRegulations() {
        return dangerousGoodsRegulations;
    }

    public void setDangerousGoodsRegulations(String dangerousGoodsRegulations) {
        this.dangerousGoodsRegulations = dangerousGoodsRegulations;
    }

    public String getGpsrSafetyAttestation() {
        return gpsrSafetyAttestation;
    }

    public void setGpsrSafetyAttestation(String gpsrSafetyAttestation) {
        this.gpsrSafetyAttestation = gpsrSafetyAttestation;
    }

    public String getShippingTemplateGroup() {
        return shippingTemplateGroup;
    }

    public void setShippingTemplateGroup(String shippingTemplateGroup) {
        this.shippingTemplateGroup = shippingTemplateGroup;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMaterialIsComplete() {
        return materialIsComplete;
    }

    public void setMaterialIsComplete(Integer materialIsComplete) {
        this.materialIsComplete = materialIsComplete;
    }

    public String getSalesGroupCode() {
        return salesGroupCode;
    }

    public void setSalesGroupCode(String salesGroupCode) {
        this.salesGroupCode = salesGroupCode;
    }

    public String getSalesGroupEmptCode() {
        return salesGroupEmptCode;
    }

    public void setSalesGroupEmptCode(String salesGroupEmptCode) {
        this.salesGroupEmptCode = salesGroupEmptCode;
    }

    public String getOperationEmptCode() {
        return operationEmptCode;
    }

    public void setOperationEmptCode(String operationEmptCode) {
        this.operationEmptCode = operationEmptCode;
    }

    public String getFulfillmentCenterId() {
        return fulfillmentCenterId;
    }

    public void setFulfillmentCenterId(String fulfillmentCenterId) {
        this.fulfillmentCenterId = fulfillmentCenterId;
    }

    public String getExternalProductIdType() {
        return externalProductIdType;
    }

    public void setExternalProductIdType(String externalProductIdType) {
        this.externalProductIdType = externalProductIdType;
    }

    public String getUpdateDelete() {
        return updateDelete;
    }

    public void setUpdateDelete(String updateDelete) {
        this.updateDelete = updateDelete;
    }

    public String getConditionType() {
        return conditionType;
    }

    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    public String getSizeName() {
        return sizeName;
    }

    public void setSizeName(String sizeName) {
        this.sizeName = sizeName;
    }

    public BigDecimal getPackageLength() {
        return packageLength;
    }

    public void setPackageLength(BigDecimal packageLength) {
        this.packageLength = packageLength;
    }

    public BigDecimal getPackageWidth() {
        return packageWidth;
    }

    public void setPackageWidth(BigDecimal packageWidth) {
        this.packageWidth = packageWidth;
    }

    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    public String getPackageLengthUnitOfMeasure() {
        return packageLengthUnitOfMeasure;
    }

    public void setPackageLengthUnitOfMeasure(String packageLengthUnitOfMeasure) {
        this.packageLengthUnitOfMeasure = packageLengthUnitOfMeasure;
    }

    public String getPackageWidthUnitOfMeasure() {
        return packageWidthUnitOfMeasure;
    }

    public void setPackageWidthUnitOfMeasure(String packageWidthUnitOfMeasure) {
        this.packageWidthUnitOfMeasure = packageWidthUnitOfMeasure;
    }

    public String getPackageHeightUnitOfMeasure() {
        return packageHeightUnitOfMeasure;
    }

    public void setPackageHeightUnitOfMeasure(String packageHeightUnitOfMeasure) {
        this.packageHeightUnitOfMeasure = packageHeightUnitOfMeasure;
    }

    public BigDecimal getPackageWeight() {
        return packageWeight;
    }

    public void setPackageWeight(BigDecimal packageWeight) {
        this.packageWeight = packageWeight;
    }

    public String getPackageWeightUnitOfMeasure() {
        return packageWeightUnitOfMeasure;
    }

    public void setPackageWeightUnitOfMeasure(String packageWeightUnitOfMeasure) {
        this.packageWeightUnitOfMeasure = packageWeightUnitOfMeasure;
    }

    public String getColorName() {
        return colorName;
    }

    public void setColorName(String colorName) {
        this.colorName = colorName;
    }

    public BigDecimal getListPriceWithTax() {
        return listPriceWithTax;
    }

    public void setListPriceWithTax(BigDecimal listPriceWithTax) {
        this.listPriceWithTax = listPriceWithTax;
    }

    public String getCreateNum() {
        return createNum;
    }

    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyNum() {
        return modifyNum;
    }

    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
