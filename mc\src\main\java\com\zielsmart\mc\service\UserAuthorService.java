package com.zielsmart.mc.service;

import com.zielsmart.mc.third.IUserService;
import com.zielsmart.web.basic.exception.UnAuthorizedException;
import com.zielsmart.web.basic.request.TokenUserValidator;
import com.zielsmart.web.basic.tool.JWTTool;
import com.zielsmart.web.basic.vo.JwtPlayLoad;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @version V2.0
 * @title: UserService
 * @package: com.zielsmart.omsb2c.third
 * @description: 用户服务
 * @author: 李耀华
 * @date: 2021-03-1114:12
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class UserAuthorService implements TokenUserValidator {

    @Resource
    private IUserService userService;

    @Resource
    private JWTTool jwtTool;
    /**
     * 已经有默认实现这里不再处理 如果每个应用有特殊处理在这里处理即可
     *
     * @param --token
     * @return
     */
   /* @Override
    public boolean validator(JwtPlayLoad token) {
        return  true;
    }
*/
    @Override
    public TokenUserInfo userInfo(JwtPlayLoad jwtPlayLoad) throws Exception{
        try {
//            return userService.currentUser(jwtTool.createToken(jwtPlayLoad)).getData();
            return userService.currentUser().getData();
        } catch (Exception e) {
            e.printStackTrace();
            throw new UnAuthorizedException();
        }
    }
}
