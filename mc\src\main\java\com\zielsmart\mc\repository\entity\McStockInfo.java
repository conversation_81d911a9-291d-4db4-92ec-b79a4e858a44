package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* 可用库存信息
* gen by 代码生成器 2021-08-10
*/

@Table(name="mc.mc_stock_info")
public class McStockInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 仓库主键
	 */
	@Column("warehouse_id")
	private Integer warehouseId ;
	/**
	 * 仓库编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * SKU
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 仓库发货7天DMS
	 */
	@Column("seven_day_number")
	private BigDecimal sevenDayNumber ;
	/**
	 * 可上架库存
	 */
	@Column("total_stock")
	private Integer totalStock ;
	/**
	 * 同步时间
	 */
	@Column("sync_time")
	private Date syncTime ;
	/**
	 * 仓库编码
	 */
	@Column("sl_code")
	private String slCode;
	/**
	 * 仓库编码
	 */
	@Column("sl_name")
	private String slName;

	public McStockInfo() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 仓库主键
	*@return
	*/
	public Integer getWarehouseId(){
		return  warehouseId;
	}
	/**
	* 仓库主键
	*@param  warehouseId
	*/
	public void setWarehouseId(Integer warehouseId ){
		this.warehouseId = warehouseId;
	}
	/**
	* 仓库编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 仓库编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* SKU
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* SKU
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 仓库发货7天DMS
	*@return
	*/
	public BigDecimal getSevenDayNumber(){
		return  sevenDayNumber;
	}
	/**
	* 仓库发货7天DMS
	*@param  sevenDayNumber
	*/
	public void setSevenDayNumber(BigDecimal sevenDayNumber ){
		this.sevenDayNumber = sevenDayNumber;
	}
	/**
	* 可上架库存
	*@return
	*/
	public Integer getTotalStock(){
		return  totalStock;
	}
	/**
	* 可上架库存
	*@param  totalStock
	*/
	public void setTotalStock(Integer totalStock ){
		this.totalStock = totalStock;
	}
	/**
	* 同步时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 同步时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}

	public String getSlCode() {
		return slCode;
	}

	public void setSlCode(String slCode) {
		this.slCode = slCode;
	}

	public String getSlName() {
		return slName;
	}

	public void setSlName(String slName) {
		this.slName = slName;
	}
}
