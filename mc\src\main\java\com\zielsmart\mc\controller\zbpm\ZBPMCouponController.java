package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMCouponService;
import com.zielsmart.mc.vo.zbpm.ZBPMCouponVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller.zbpm
 * @title ZBPMCouponController
 * @description 向ZBPM项目提供Coupon活动接口
 * @date 2022-04-28 10:03:56
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/zbpm-coupon", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Coupon活动接口")
public class ZBPMCouponController extends BasicController {

    @Resource
    private ZBPMCouponService service;

    /**
     * updateCoupon
     * 更新活动状态
     *
     * @param updateVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新Coupon")
    @PostMapping(value = "/updateCoupon")
    public ResultVo<String> updateCoupon(@RequestBody ZBPMCouponVo updateVo) throws ValidateException {
        service.updateCoupon(updateVo);
        return ResultVo.ofSuccess(null);
    }
}
