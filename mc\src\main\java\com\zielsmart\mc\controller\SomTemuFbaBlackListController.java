package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomTemuFbaBlackListService;
import com.zielsmart.mc.vo.SomTemuFbaBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuFbaBlackListVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuFbaBlackListController
 * @description
 * @date 2024-07-04 11:40:20
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuFbaBlackList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "FBA产品黑名单表管理")
public class SomTemuFbaBlackListController extends BasicController {

    @Resource
    SomTemuFbaBlackListService somTemuFbaBlackListService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuFbaBlackListVo>> queryByPage(@RequestBody SomTemuFbaBlackListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuFbaBlackListService.queryByPage(searchVo));
    }

    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated SomTemuFbaBlackListVo somTemuFbaBlackListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuFbaBlackListService.save(somTemuFbaBlackListVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomTemuFbaBlackListVo somTemuFbaBlackListVo) throws ValidateException {
        somTemuFbaBlackListService.delete(somTemuFbaBlackListVo);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "批量删除")
    @PostMapping(value = "/batch-delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDelete(@RequestBody SomTemuFbaBlackListVo somTemuFbaBlackListVo) throws ValidateException {
        somTemuFbaBlackListService.batchDelete(somTemuFbaBlackListVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomTemuFbaBlackListPageSearchVo searchVo) {
        String data = somTemuFbaBlackListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        String[] importFields = {"店铺ID", "平台", "站点", "展示码","发货方式(使用逗号分隔)","发货仓库&发货库区"};
        importParams.setImportFields(importFields);
        List<SomTemuFbaBlackListVo> list = null;
        try {
           list = ExcelImportUtil.importExcel(file.getInputStream(), SomTemuFbaBlackListVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if(CollectionUtil.isEmpty(list)){
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somTemuFbaBlackListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/SomTemuFbaBlackListTemplate.xlsx";
    }
}
