package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McFeishuMsgConfigService;
import com.zielsmart.mc.vo.McFeishuMsgConfigPageSearchVo;
import com.zielsmart.mc.vo.McFeishuMsgConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McFeishuMsgConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mc-feishu-msgconfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "消息提醒人员配置管理")
public class McFeishuMsgConfigController extends BasicController {

    @Resource
    McFeishuMsgConfigService mcFeishuMsgConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McFeishuMsgConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McFeishuMsgConfigVo>> queryByPage(@RequestBody McFeishuMsgConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcFeishuMsgConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcFeishuMsgConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McFeishuMsgConfigVo mcFeishuMsgConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcFeishuMsgConfigService.save(mcFeishuMsgConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param mcFeishuMsgConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McFeishuMsgConfigVo mcFeishuMsgConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcFeishuMsgConfigService.update(mcFeishuMsgConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcFeishuMsgConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McFeishuMsgConfigVo mcFeishuMsgConfigVo) throws ValidateException {
        mcFeishuMsgConfigService.delete(mcFeishuMsgConfigVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "获取修改详情")
    @PostMapping(value = "/go-edit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<McFeishuMsgConfigVo> goEdit(@RequestBody McFeishuMsgConfigVo mcFeishuMsgConfigVo) throws ValidateException {
        return ResultVo.ofSuccess(mcFeishuMsgConfigService.goEdit(mcFeishuMsgConfigVo));
    }


}
