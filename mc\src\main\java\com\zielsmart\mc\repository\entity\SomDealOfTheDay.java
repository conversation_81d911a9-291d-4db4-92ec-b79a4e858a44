package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * DOTO表
 * gen by 代码生成器 2022-08-09
 */

@Table(name = "mc.som_deal_of_the_day")
public class SomDealOfTheDay implements java.io.Serializable {

    /**
     * 秒杀价毛利率
     */
    @Column("deal_price_gross")
    private BigDecimal dealPriceGross;

    /**
     * 活动预计爆发系数
     */
    @Column("deal_burst_coefficient")
    private BigDecimal dealBurstCoefficient;

    /**
     * 近三十天DMS
     */
    @Column("dms_last_30day")
    private BigDecimal dmsLast30day;

    /**
     * 三级分类近四周毛利率
     */
    @Column("category_gross")
    private BigDecimal categoryGross;

    /**
     * 活动预计销量
     */
    @Column("expected_sales_volume")
    private BigDecimal expectedSalesVolume;

    /**
     * 活动预计销售额
     */
    @Column("estimate_of_sales")
    private BigDecimal estimateOfSales;

    /**
     * 活动预计毛利额
     */
    @Column("expected_gross_profit_margin")
    private BigDecimal expectedGrossProfitMargin;

    /**
     * 审批角色
     */
    @Column("approval_role")
    private String approvalRole;

    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 子ASIN
     */
    @Column("child_asin")
    private String childAsin;
    /**
     * 父ASIN
     */
    @Column("parent_asin")
    private String parentAsin;
    /**
     * 期望运行天数
     */
    @Column("last_days")
    private Integer lastDays;
    /**
     * FBA/FBM
     */
    @Column("fulfillment_channel")
    private String fulfillmentChannel;
    /**
     * 品牌
     */
    @Column("brand_name")
    private String brandName;
    /**
     * 大类目
     */
    @Column("amazon_category")
    private String amazonCategory;
    /**
     * 商品名称
     */
    @Column("product_name")
    private String productName;
    /**
     * 评分
     */
    @Column("score")
    private BigDecimal score;
    /**
     * 评价数
     */
    @Column("comments_number")
    private Integer commentsNumber;
    /**
     * 库存
     */
    @Column("stock")
    private Integer stock;
    /**
     * 当前售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 秒杀价
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * 秒杀折扣
     */
    @Column("deal_discount")
    private BigDecimal dealDiscount;
    /**
     * Coupon折扣
     */
    @Column("coupon_discount")
    private BigDecimal couponDiscount;
    /**
     * Promotion折扣
     */
    @Column("promotion_discount")
    private BigDecimal promotionDiscount;
    /**
     * T28销量
     */
    @Column("sales_month_ago")
    private Integer salesMonthAgo;
    /**
     * T28销售额
     */
    @Column("sales_from_month_ago")
    private BigDecimal salesFromMonthAgo;
    /**
     * 预计TD销量
     */
    @Column("estimated_deal_sales")
    private Integer estimatedDealSales;
    /**
     * 活动命名
     */
    @Column("deal_title")
    private String dealTitle;
    /**
     * 状态 10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始  70.进行中 80.已结束  90.取消中
     */
    @Column("status")
    private Integer status;
    /**
     * 三级分类编码
     */
    @Column("category_inline_code")
    private String categoryInlineCode;
    /**
     * 三级分类名称
     */
    @Column("category_name")
    private String categoryName;
    /**
     * 活动提报开始日期
     */
    @Column("plan_start_date")
    private Date planStartDate;
    /**
     * 活动提报截止日期
     */
    @Column("plan_end_date")
    private Date planEndDate;
    /**
     * 活动实际开始日期
     */
    @Column("real_start_date")
    private Date realStartDate;
    /**
     * 活动实际截止日期
     */
    @Column("real_end_date")
    private Date realEndDate;
    /**
     * 审批失败原因
     */
    @Column("audit_failure_remark")
    private String auditFailureRemark;
    /**
     * 申请原因10.提升排名 20.清货 30.稳排名 99.自定义
     */
    @Column("apply_reason")
    private Integer applyReason;
    /**
     * 自定义原因
     */
    @Column("cutomer_reason")
    private String cutomerReason;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 最后修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 最后修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;
    /**
     * 币种
     */
    @Column("currency_code")
    private String currencyCode;
    /**
     * 总折扣
     */
    @Column("total_discount")
    private BigDecimal totalDiscount;
    /**
     * 总折扣金额
     */
    @Column("total_discount_amount")
    private BigDecimal totalDiscountAmount;
    /**
     * 提报失败原因
     */
    @Column("submission_failure_remark")
    private String submissionFailureRemark;
    /**
     * 预估成交价
     */
    @Column("transaction_price")
    private BigDecimal transactionPrice;

    /**
     * 大促类型
     * Prime Day Window	1
     * Prime Fall Deal Event	2
     * BFCM Window	3
     * Black Friday Window	4
     * Cyber Monday Window	5
     */
    @Column("campaign_type")
    private Integer campaignType;

    /**
     * 取消原因
     */
    @Column("cancel_remark")
    private String cancelRemark;
    /**
     * 取消失败原因
     */
    @Column("cacenl_failure_remark")
    private String cacenlFailureRemark;
    /**
     * 库存可售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;
    /**
     * 提交时间
     */
    @Column("submit_time")
    private Date submitTime;
    /**
     * 审批时间
     */
    @Column("audit_time")
    private Date auditTime;

    @Column("accept_adjustment")
    private String acceptAdjustment;

    //近四周销售预测达成率
    @Column("completion_rate")
    private BigDecimal completionRate;

    /**
     * 错误信息(由RPA提供)
     */
    @Column("error_msg")
    private String errorMsg;

    /**
     * 修改状态10.修改中 20.修改成功 30.修改失败
     */
    @Column("modify_status")
    private Integer modifyStatus;

    /**
     * 修改原因
     */
    @Column("modify_remark")
    private String modifyRemark;
    /**
     * 修改失败原因
     */
    @Column("modify_failure_remark")
    private String modifyFailureRemark;

    public SomDealOfTheDay() {
    }

    public Integer getCampaignType() {
        return campaignType;
    }

    public void setCampaignType(Integer campaignType) {
        this.campaignType = campaignType;
    }

    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    public String getAcceptAdjustment() {
        return acceptAdjustment;
    }

    public void setAcceptAdjustment(String acceptAdjustment) {
        this.acceptAdjustment = acceptAdjustment;
    }

    public BigDecimal getDealPriceGross() {
        return dealPriceGross;
    }

    public void setDealPriceGross(BigDecimal dealPriceGross) {
        this.dealPriceGross = dealPriceGross;
    }

    public BigDecimal getDealBurstCoefficient() {
        return dealBurstCoefficient;
    }

    public void setDealBurstCoefficient(BigDecimal dealBurstCoefficient) {
        this.dealBurstCoefficient = dealBurstCoefficient;
    }

    public BigDecimal getDmsLast30day() {
        return dmsLast30day;
    }

    public void setDmsLast30day(BigDecimal dmsLast30day) {
        this.dmsLast30day = dmsLast30day;
    }

    public BigDecimal getCategoryGross() {
        return categoryGross;
    }

    public void setCategoryGross(BigDecimal categoryGross) {
        this.categoryGross = categoryGross;
    }

    public BigDecimal getExpectedSalesVolume() {
        return expectedSalesVolume;
    }

    public void setExpectedSalesVolume(BigDecimal expectedSalesVolume) {
        this.expectedSalesVolume = expectedSalesVolume;
    }

    public BigDecimal getEstimateOfSales() {
        return estimateOfSales;
    }

    public void setEstimateOfSales(BigDecimal estimateOfSales) {
        this.estimateOfSales = estimateOfSales;
    }

    public BigDecimal getExpectedGrossProfitMargin() {
        return expectedGrossProfitMargin;
    }

    public void setExpectedGrossProfitMargin(BigDecimal expectedGrossProfitMargin) {
        this.expectedGrossProfitMargin = expectedGrossProfitMargin;
    }

    public String getApprovalRole() {
        return approvalRole;
    }

    public void setApprovalRole(String approvalRole) {
        this.approvalRole = approvalRole;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 子ASIN
     *
     * @return
     */
    public String getChildAsin() {
        return childAsin;
    }

    /**
     * 子ASIN
     *
     * @param childAsin
     */
    public void setChildAsin(String childAsin) {
        this.childAsin = childAsin;
    }

    /**
     * 父ASIN
     *
     * @return
     */
    public String getParentAsin() {
        return parentAsin;
    }

    /**
     * 父ASIN
     *
     * @param parentAsin
     */
    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    /**
     * 期望运行天数
     *
     * @return
     */
    public Integer getLastDays() {
        return lastDays;
    }

    /**
     * 期望运行天数
     *
     * @param lastDays
     */
    public void setLastDays(Integer lastDays) {
        this.lastDays = lastDays;
    }

    /**
     * FBA/FBM
     *
     * @return
     */
    public String getFulfillmentChannel() {
        return fulfillmentChannel;
    }

    /**
     * FBA/FBM
     *
     * @param fulfillmentChannel
     */
    public void setFulfillmentChannel(String fulfillmentChannel) {
        this.fulfillmentChannel = fulfillmentChannel;
    }

    /**
     * 品牌
     *
     * @return
     */
    public String getBrandName() {
        return brandName;
    }

    /**
     * 品牌
     *
     * @param brandName
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    /**
     * 大类目
     *
     * @return
     */
    public String getAmazonCategory() {
        return amazonCategory;
    }

    /**
     * 大类目
     *
     * @param amazonCategory
     */
    public void setAmazonCategory(String amazonCategory) {
        this.amazonCategory = amazonCategory;
    }

    /**
     * 商品名称
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 商品名称
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 评分
     *
     * @return
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 评分
     *
     * @param score
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 评价数
     *
     * @return
     */
    public Integer getCommentsNumber() {
        return commentsNumber;
    }

    /**
     * 评价数
     *
     * @param commentsNumber
     */
    public void setCommentsNumber(Integer commentsNumber) {
        this.commentsNumber = commentsNumber;
    }

    /**
     * 库存
     *
     * @return
     */
    public Integer getStock() {
        return stock;
    }

    /**
     * 库存
     *
     * @param stock
     */
    public void setStock(Integer stock) {
        this.stock = stock;
    }

    /**
     * 当前售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 当前售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 秒杀价
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 秒杀价
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * 秒杀折扣
     *
     * @return
     */
    public BigDecimal getDealDiscount() {
        return dealDiscount;
    }

    /**
     * 秒杀折扣
     *
     * @param dealDiscount
     */
    public void setDealDiscount(BigDecimal dealDiscount) {
        this.dealDiscount = dealDiscount;
    }

    /**
     * Coupon折扣
     *
     * @return
     */
    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    /**
     * Coupon折扣
     *
     * @param couponDiscount
     */
    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    /**
     * Promotion折扣
     *
     * @return
     */
    public BigDecimal getPromotionDiscount() {
        return promotionDiscount;
    }

    /**
     * Promotion折扣
     *
     * @param promotionDiscount
     */
    public void setPromotionDiscount(BigDecimal promotionDiscount) {
        this.promotionDiscount = promotionDiscount;
    }

    /**
     * T28销量
     *
     * @return
     */
    public Integer getSalesMonthAgo() {
        return salesMonthAgo;
    }

    /**
     * T28销量
     *
     * @param salesMonthAgo
     */
    public void setSalesMonthAgo(Integer salesMonthAgo) {
        this.salesMonthAgo = salesMonthAgo;
    }

    /**
     * T28销售额
     *
     * @return
     */
    public BigDecimal getSalesFromMonthAgo() {
        return salesFromMonthAgo;
    }

    /**
     * T28销售额
     *
     * @param salesFromMonthAgo
     */
    public void setSalesFromMonthAgo(BigDecimal salesFromMonthAgo) {
        this.salesFromMonthAgo = salesFromMonthAgo;
    }

    /**
     * 预计TD销量
     *
     * @return
     */
    public Integer getEstimatedDealSales() {
        return estimatedDealSales;
    }

    /**
     * 预计TD销量
     *
     * @param estimatedDealSales
     */
    public void setEstimatedDealSales(Integer estimatedDealSales) {
        this.estimatedDealSales = estimatedDealSales;
    }

    /**
     * 活动命名
     *
     * @return
     */
    public String getDealTitle() {
        return dealTitle;
    }

    /**
     * 活动命名
     *
     * @param dealTitle
     */
    public void setDealTitle(String dealTitle) {
        this.dealTitle = dealTitle;
    }

    /**
     * 状态 10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始  70.进行中 80.已结束
     *
     * @return
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态 10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始  70.进行中 80.已结束
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 三级分类编码
     *
     * @return
     */
    public String getCategoryInlineCode() {
        return categoryInlineCode;
    }

    /**
     * 三级分类编码
     *
     * @param categoryInlineCode
     */
    public void setCategoryInlineCode(String categoryInlineCode) {
        this.categoryInlineCode = categoryInlineCode;
    }

    /**
     * 三级分类名称
     *
     * @return
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 三级分类名称
     *
     * @param categoryName
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 活动提报开始日期
     *
     * @return
     */
    public Date getPlanStartDate() {
        return planStartDate;
    }

    /**
     * 活动提报开始日期
     *
     * @param planStartDate
     */
    public void setPlanStartDate(Date planStartDate) {
        this.planStartDate = planStartDate;
    }

    /**
     * 活动提报截止日期
     *
     * @return
     */
    public Date getPlanEndDate() {
        return planEndDate;
    }

    /**
     * 活动提报截止日期
     *
     * @param planEndDate
     */
    public void setPlanEndDate(Date planEndDate) {
        this.planEndDate = planEndDate;
    }

    /**
     * 活动实际开始日期
     *
     * @return
     */
    public Date getRealStartDate() {
        return realStartDate;
    }

    /**
     * 活动实际开始日期
     *
     * @param realStartDate
     */
    public void setRealStartDate(Date realStartDate) {
        this.realStartDate = realStartDate;
    }

    /**
     * 活动实际截止日期
     *
     * @return
     */
    public Date getRealEndDate() {
        return realEndDate;
    }

    /**
     * 活动实际截止日期
     *
     * @param realEndDate
     */
    public void setRealEndDate(Date realEndDate) {
        this.realEndDate = realEndDate;
    }

    /**
     * 审批失败原因
     *
     * @return
     */
    public String getAuditFailureRemark() {
        return auditFailureRemark;
    }

    /**
     * 审批失败原因
     *
     * @param auditFailureRemark
     */
    public void setAuditFailureRemark(String auditFailureRemark) {
        this.auditFailureRemark = auditFailureRemark;
    }

    /**
     * 申请原因10.提升排名 20.清货 30.稳排名 99.自定义
     *
     * @return
     */
    public Integer getApplyReason() {
        return applyReason;
    }

    /**
     * 申请原因10.提升排名 20.清货 30.稳排名 99.自定义
     *
     * @param applyReason
     */
    public void setApplyReason(Integer applyReason) {
        this.applyReason = applyReason;
    }

    /**
     * 自定义原因
     *
     * @return
     */
    public String getCutomerReason() {
        return cutomerReason;
    }

    /**
     * 自定义原因
     *
     * @param cutomerReason
     */
    public void setCutomerReason(String cutomerReason) {
        this.cutomerReason = cutomerReason;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 最后修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 最后修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 最后修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public String getSubmissionFailureRemark() {
        return submissionFailureRemark;
    }

    public void setSubmissionFailureRemark(String submissionFailureRemark) {
        this.submissionFailureRemark = submissionFailureRemark;
    }

    public BigDecimal getTransactionPrice() {
        return transactionPrice;
    }

    public void setTransactionPrice(BigDecimal transactionPrice) {
        this.transactionPrice = transactionPrice;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public String getCacenlFailureRemark() {
        return cacenlFailureRemark;
    }

    public void setCacenlFailureRemark(String cacenlFailureRemark) {
        this.cacenlFailureRemark = cacenlFailureRemark;
    }

    public Integer getStockSaleDays() {
        return stockSaleDays;
    }

    public void setStockSaleDays(Integer stockSaleDays) {
        this.stockSaleDays = stockSaleDays;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getModifyStatus() {
        return modifyStatus;
    }

    public void setModifyStatus(Integer modifyStatus) {
        this.modifyStatus = modifyStatus;
    }

    public String getModifyRemark() {
        return modifyRemark;
    }

    public void setModifyRemark(String modifyRemark) {
        this.modifyRemark = modifyRemark;
    }

    public String getModifyFailureRemark() {
        return modifyFailureRemark;
    }

    public void setModifyFailureRemark(String modifyFailureRemark) {
        this.modifyFailureRemark = modifyFailureRemark;
    }
}
