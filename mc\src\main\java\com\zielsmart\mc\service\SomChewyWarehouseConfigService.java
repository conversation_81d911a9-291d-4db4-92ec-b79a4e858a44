package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.entity.SomChewyWarehouseConfig;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.SomChewyWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SomChewyWarehouseConfigService {
    @Resource
    private SomChewyWarehouseConfigMapper somChewyWarehouseConfigMapper;
    @Resource
    private SomChewyWarehouseInfoMapper somChewyWarehouseInfoMapper;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private McDearanceProductMapper mcDearanceProductMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    public PageVo<SomChewyWarehouseConfigVo> queryByPage(SomChewyWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomChewyWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomChewyWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);

        if (!pageResult.getList().isEmpty()) {
            List<SomChewyWarehouseConfig> all = somChewyWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, List<SomChewyWarehouseConfig>> allMap = all.stream().collect(Collectors.groupingBy(SomChewyWarehouseConfig::getWarehouseCode, Collectors.toList()));
            for (SomChewyWarehouseConfigVo config : pageResult.getList()) {
                if (allMap.containsKey(config.getWarehouseCode())) {
                    List<SomChewyWarehouseConfig> warehouseConfigs = allMap.get(config.getWarehouseCode());
                    List<String> nameList = new ArrayList<>();
                    config.setList(warehouseConfigs.stream().map(x -> {
                        SomChewyWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomChewyWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                        useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                        nameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    config.setWarehouseNameList(nameList);
                    SomChewyWarehouseConfig config1 = warehouseConfigs.get(0);
                    config.setSite(config1.getSite());
                    config.setCreateName(config1.getCreateName());
                    config.setCreateNum(config1.getCreateNum());
                    config.setCreateTime(config1.getCreateTime());
                }
            }
        }

        return ConvertUtils.pageConvert(pageResult, SomChewyWarehouseConfigVo.class, searchVo);
    }

    public void save(SomChewyWarehouseConfigVo somChewyWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somChewyWarehouseConfigVo) || somChewyWarehouseConfigVo.getWarehouseCode() == null || somChewyWarehouseConfigVo.getSite() == null
                || somChewyWarehouseConfigVo.getList() == null || somChewyWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somChewyWarehouseConfigVo.getWarehouseCode();
        String site = somChewyWarehouseConfigVo.getSite();
        if (somChewyWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).andEq("site", site).count() > 0) {
            throw new ValidateException("您输入的平台仓库在系统中已存在，不允许重复添加");
        }
        List<SomChewyWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomChewyWarehouseConfigVo.UseableWarehouse useableWarehouse : somChewyWarehouseConfigVo.getList()) {
            SomChewyWarehouseConfig config = new SomChewyWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somChewyWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setSite(somChewyWarehouseConfigVo.getSite());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setLastModifyNum(tokenUser.getJobNumber());
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            config.setCreateTime(now);
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somChewyWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(SomChewyWarehouseConfigVo somChewyWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somChewyWarehouseConfigVo) || somChewyWarehouseConfigVo.getWarehouseCode() == null || somChewyWarehouseConfigVo.getSite() == null
                || somChewyWarehouseConfigVo.getList() == null || somChewyWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somChewyWarehouseConfigVo.getWarehouseCode();
        String site = somChewyWarehouseConfigVo.getSite();
        somChewyWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).andEq("site", site).delete();
        List<SomChewyWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomChewyWarehouseConfigVo.UseableWarehouse useableWarehouse : somChewyWarehouseConfigVo.getList()) {
            SomChewyWarehouseConfig config = new SomChewyWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somChewyWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setSite(site);
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            config.setLastModifyNum(tokenUser.getJobNumber());
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somChewyWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    public void delete(SomChewyWarehouseConfigVo somChewyWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somChewyWarehouseConfigVo) || StrUtil.isEmpty(somChewyWarehouseConfigVo.getWarehouseCode()) || StrUtil.isEmpty(somChewyWarehouseConfigVo.getSite())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somChewyWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somChewyWarehouseConfigVo.getWarehouseCode()).andEq("site", somChewyWarehouseConfigVo.getSite()).delete();
    }

    public List<SomChewyWarehouseInfoVo> warehouse() {
        List<SomChewyWarehouseInfo> warehouseConfigList = somChewyWarehouseInfoMapper.createLambdaQuery().select("warehouse_code", "warehouse_name");
        List<SomChewyWarehouseInfoVo> warehouseList = ConvertUtils.listConvert(warehouseConfigList, SomChewyWarehouseInfoVo.class);
        for (SomChewyWarehouseInfoVo somChewyWarehouseInfoVo : warehouseList) {
            somChewyWarehouseInfoVo.setWarehouseCodeAndName(somChewyWarehouseInfoVo.getWarehouseCode() + "-" + somChewyWarehouseInfoVo.getWarehouseName());
        }
        return warehouseList;
    }

    public PageVo<SomChewyStockReportVo> stockReport(SomChewyWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomChewyStockReportVo> pageResult = dynamicSqlManager.getMapper(SomChewyWarehouseConfigMapper.class).stockReport(searchVo, pageRequest);

        if (!pageResult.getList().isEmpty()) {
            stockReportInitData(pageResult.getList());
        }

        return ConvertUtils.pageConvert(pageResult, SomChewyStockReportVo.class, searchVo);
    }

    public String exportStockReport(SomChewyWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        List<SomChewyStockReportVo> somChewyStockReportVoList = dynamicSqlManager.getMapper(SomChewyWarehouseConfigMapper.class).exportStockReport(searchVo);

        if (!somChewyStockReportVoList.isEmpty()) {
            stockReportInitData(somChewyStockReportVoList);

            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Chewy可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomChewyStockReportVo.class, somChewyStockReportVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return "";
    }

    private void stockReportInitData(List<SomChewyStockReportVo> somChewyStockReportVoList) {
        if (somChewyStockReportVoList.isEmpty()) {
            return ;
        }

        // 可用库存信息
        List<McStockInfo> stockList = mcStockInfoMapper.createQuery()
                .andIn("product_main_code", somChewyStockReportVoList.stream().map(SomChewyStockReportVo::getProductMainCode).collect(Collectors.toList()))
                .select();
        Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(
                x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(),
                Function.identity(),
                (x1, x2) -> x1
        ));

        // chewy可售仓库配置
        List<SomChewyWarehouseConfig> warehouseList = somChewyWarehouseConfigMapper.all();
        Map<String, String> warehouseMap = warehouseList.stream().collect(Collectors.toMap(
                SomChewyWarehouseConfig::getWarehouseCode,
                SomChewyWarehouseConfig::getWarehouseName,
                (x1, x2) -> x1
        ));
        Map<String, List<SomChewyWarehouseConfig>> warehouseByCode = warehouseList.stream()
                .collect(Collectors.groupingBy(SomChewyWarehouseConfig::getWarehouseCode));

        // 获取清仓产品
        List<McDearanceProduct> mcDearanceProductList = mcDearanceProductMapper.createLambdaQuery().andEq("platform", "Chewy").andEq("status", 1).select();
        Map<String, McDearanceProduct> mcDearanceProductMap = mcDearanceProductList.stream().collect(Collectors.toMap(
                x -> x.getPlatform() + x.getSite() + x.getSellSku(),
                v -> v, (key1, key2) -> key1
        ));

        for (SomChewyStockReportVo report : somChewyStockReportVoList) {
            // 总库存
            BigDecimal totalStock = BigDecimal.ZERO;
            // list
            List<SomChewyStockReportVo.WarehouseInventory> list = new ArrayList<>();

            // warehouseCode=平台仓库编码 warehouseConfigs=平台仓库下的仓库、库区
            for (Map.Entry<String, List<SomChewyWarehouseConfig>> entry : warehouseByCode.entrySet()) {
                String warehouseCode = entry.getKey();
                List<SomChewyWarehouseConfig> warehouseConfigs = entry.getValue();
                SomChewyStockReportVo.WarehouseInventory warehouseInventory = new SomChewyStockReportVo.WarehouseInventory();
                warehouseInventory.setWarehouseCode(warehouseCode);
                warehouseInventory.setWarehouseName(warehouseMap.getOrDefault(warehouseCode, null));
                BigDecimal stock = BigDecimal.ZERO;

                // 匹配库存信息
                List<McStockInfo> stockInfoList = warehouseConfigs.stream()
                        .map(x -> {
                            String key = x.getUseableWarehouseCode() + x.getUseableStorageCode() + report.getProductMainCode();
                            return stockMap.get(key);
                        })
                        .filter(java.util.Objects::nonNull)
                        .collect(Collectors.toList());

                if (!stockInfoList.isEmpty()) {
                    for (McStockInfo stockInfo : stockInfoList) {
                        if (stockInfo.getTotalStock() != null) {
                            stock = stock.add(BigDecimal.valueOf(stockInfo.getTotalStock()));
                        }
                    }

                    // 判断是否是清仓产品 非清仓产品,减去安全库存
                    if (!mcDearanceProductMap.containsKey(report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
                        // 获取设置的安全库存
                        Integer safetyStock = null == report.getSafetyStock() ? 0 : report.getSafetyStock();
                        // 计算安全库存
                        BigDecimal sevenDayNumber = stockInfoList.stream()
                                .filter(x -> null != x.getSevenDayNumber())
                                .map(x -> x.getSevenDayNumber())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 确定安全库存
                        int safeInventory = sevenDayNumber.signum() == 0 ? safetyStock :
                                Integer.max(sevenDayNumber.divide(BigDecimal.valueOf(3),5, RoundingMode.DOWN).intValue(), safetyStock);
                        // 减去安全库存
                        stock = stock.subtract(BigDecimal.valueOf(safeInventory));
                    }
                }

                if (stock.compareTo(BigDecimal.ZERO) < 0) {
                    stock = BigDecimal.ZERO;
                }

                totalStock = totalStock.add(stock);

                warehouseInventory.setQuantity(stock.intValue());
                list.add(warehouseInventory);
            }

            report.setList(list);
            report.setStock(totalStock.intValue());
            if (report.getStock() < 0) {
                report.setStock(0);
            }
        }
    }
}
