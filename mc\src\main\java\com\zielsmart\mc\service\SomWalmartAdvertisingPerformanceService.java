package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomPddAndOd;
import com.zielsmart.mc.repository.entity.SomWalmartAdvertisingPerformance;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomAliExpressWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomWalmartAdvertisingPerformanceMapper;
import com.zielsmart.mc.vo.SomPddAndOdExtVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceExtVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformancePageSearchVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-12-12 12:04:46
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomWalmartAdvertisingPerformanceService {
    
    @Resource
    private SomWalmartAdvertisingPerformanceMapper somWalmartAdvertisingPerformanceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWalmartAdvertisingPerformanceVo> queryByPage(SomWalmartAdvertisingPerformancePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomWalmartAdvertisingPerformanceVo> pageResult = dynamicSqlManager.getMapper(SomWalmartAdvertisingPerformanceMapper.class).queryByPage(searchVo, pageRequest);

        pageResult.getList().stream()
                .filter(d -> d.getCtr() != null && d.getConversionRate() != null)
                .forEach(d -> {
                    d.setCtrShow(toPercentageString(d.getCtr()));
                    d.setConversionRateShow(toPercentageString(d.getConversionRate()));
                });

        return ConvertUtils.pageConvert(pageResult, SomWalmartAdvertisingPerformanceVo.class, searchVo);
    }

    private static String toPercentageString(BigDecimal decimal) {
        if (decimal == null) {
            return "0%";
        }
        // 格式化为百分比，保留两位小数
        BigDecimal percentage = decimal.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        return percentage + "%";
    }

    /**
     * save
     * 添加
     * @param somWalmartAdvertisingPerformanceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWalmartAdvertisingPerformanceVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somWalmartAdvertisingPerformanceVo.setAid(IdUtil.fastSimpleUUID());
        //TODO 根据情况判断是否需要添加创建人信息
        somWalmartAdvertisingPerformanceMapper.insert(ConvertUtils.beanConvert(somWalmartAdvertisingPerformanceVo, SomWalmartAdvertisingPerformance.class));
    }

    /**
     * update
     * 修改
     * @param somWalmartAdvertisingPerformanceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWalmartAdvertisingPerformanceVo) || StrUtil.isEmpty(somWalmartAdvertisingPerformanceVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somWalmartAdvertisingPerformanceMapper.createLambdaQuery()
                .andEq("aid",somWalmartAdvertisingPerformanceVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somWalmartAdvertisingPerformanceVo, SomWalmartAdvertisingPerformance.class));
    }

    /**
     * delete
     * 删除
     * @param somWalmartAdvertisingPerformanceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWalmartAdvertisingPerformanceVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        // somWalmartAdvertisingPerformanceMapper.createLambdaQuery().andIn("aid", somWalmartAdvertisingPerformanceVo.getAidList()).delete();
        somWalmartAdvertisingPerformanceMapper.createLambdaQuery().andEq("aid",somWalmartAdvertisingPerformanceVo.getAid()).delete();
    }

    /**
     * import
     * 导入
     * @param list
     * <AUTHOR>
     */
    public String importExcel(List<SomWalmartAdvertisingPerformanceExtVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder();

        List<SomWalmartAdvertisingPerformance> insertList = new ArrayList<>(); // 需创建的数据
        List<SomWalmartAdvertisingPerformance> updateList = new ArrayList<>(); // 需更新的数据

        // 字典值 sites
        List<McDictionaryInfo> mcDictionaryInfos = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "Walmart").select();
        List<String> sites = mcDictionaryInfos.stream().map(McDictionaryInfo::getItemLable).collect(Collectors.toList());

        for (SomWalmartAdvertisingPerformanceExtVo importVo : list) {
            if (importVo.getSite() != null) { // 剔除空行
                if (sites.contains(importVo.getSite())) {
                    SomWalmartAdvertisingPerformance somWalmartAdvertisingPerformanceResult = somWalmartAdvertisingPerformanceMapper
                            .createLambdaQuery()
                            .andEq("site", importVo.getSite())
                            .andEq("campaign_name", importVo.getCampaignName())
                            .andEq("advertising_date", importVo.getAdvertisingDate())
                            .single();
                    if (ObjectUtil.isEmpty(somWalmartAdvertisingPerformanceResult)) {
                        SomWalmartAdvertisingPerformance somWalmartAdvertisingPerformance = new SomWalmartAdvertisingPerformance();
                        somWalmartAdvertisingPerformance.setAid(IdUtil.fastSimpleUUID());
                        somWalmartAdvertisingPerformance.setPlatform("Walmart");
                        somWalmartAdvertisingPerformance.setSite(importVo.getSite());
                        somWalmartAdvertisingPerformance.setSellerSku(importVo.getSellerSku());
                        somWalmartAdvertisingPerformance.setSku(importVo.getSku());
                        somWalmartAdvertisingPerformance.setAdvertisingType(importVo.getAdvertisingType());
                        somWalmartAdvertisingPerformance.setCurrency(importVo.getCurrency());
                        somWalmartAdvertisingPerformance.setAdvertisingDate(importVo.getAdvertisingDate());
                        somWalmartAdvertisingPerformance.setCampaignName(importVo.getCampaignName());
                        somWalmartAdvertisingPerformance.setItemId(importVo.getItemId());
                        somWalmartAdvertisingPerformance.setItemName(importVo.getItemName());
                        somWalmartAdvertisingPerformance.setImpressions(importVo.getImpressions());
                        somWalmartAdvertisingPerformance.setClicks(importVo.getClicks());
                        somWalmartAdvertisingPerformance.setCtr(importVo.getCtr());
                        somWalmartAdvertisingPerformance.setAdSpend(importVo.getAdSpend());
                        somWalmartAdvertisingPerformance.setOrders(importVo.getOrders());
                        somWalmartAdvertisingPerformance.setConversionRate(importVo.getConversionRate());
                        somWalmartAdvertisingPerformance.setTotalSalesAmount(importVo.getTotalSalesAmount());
                        somWalmartAdvertisingPerformance.setAdvertisedSkuSales(importVo.getAdvertisedSkuSales());
                        somWalmartAdvertisingPerformance.setUnitSold(importVo.getUnitSold());
                        somWalmartAdvertisingPerformance.setAdvertisedSkuUnit(importVo.getAdvertisedSkuUnit());
                        somWalmartAdvertisingPerformance.setCreateNum(tokenUser.getJobNumber());
                        somWalmartAdvertisingPerformance.setCreateName(tokenUser.getUserName());
                        somWalmartAdvertisingPerformance.setCreateTime(DateTime.now().toJdkDate());
                        insertList.add(somWalmartAdvertisingPerformance);
                    } else {
                        somWalmartAdvertisingPerformanceResult.setPlatform("Walmart");
                        somWalmartAdvertisingPerformanceResult.setSite(importVo.getSite());
                        somWalmartAdvertisingPerformanceResult.setSellerSku(importVo.getSellerSku());
                        somWalmartAdvertisingPerformanceResult.setSku(importVo.getSku());
                        somWalmartAdvertisingPerformanceResult.setAdvertisingType(importVo.getAdvertisingType());
                        somWalmartAdvertisingPerformanceResult.setCurrency(importVo.getCurrency());
                        somWalmartAdvertisingPerformanceResult.setAdvertisingDate(importVo.getAdvertisingDate());
                        somWalmartAdvertisingPerformanceResult.setCampaignName(importVo.getCampaignName());
                        somWalmartAdvertisingPerformanceResult.setItemId(importVo.getItemId());
                        somWalmartAdvertisingPerformanceResult.setItemName(importVo.getItemName());
                        somWalmartAdvertisingPerformanceResult.setImpressions(importVo.getImpressions());
                        somWalmartAdvertisingPerformanceResult.setClicks(importVo.getClicks());
                        somWalmartAdvertisingPerformanceResult.setCtr(importVo.getCtr());
                        somWalmartAdvertisingPerformanceResult.setAdSpend(importVo.getAdSpend());
                        somWalmartAdvertisingPerformanceResult.setOrders(importVo.getOrders());
                        somWalmartAdvertisingPerformanceResult.setConversionRate(importVo.getConversionRate());
                        somWalmartAdvertisingPerformanceResult.setTotalSalesAmount(importVo.getTotalSalesAmount());
                        somWalmartAdvertisingPerformanceResult.setAdvertisedSkuSales(importVo.getAdvertisedSkuSales());
                        somWalmartAdvertisingPerformanceResult.setUnitSold(importVo.getUnitSold());
                        somWalmartAdvertisingPerformanceResult.setAdvertisedSkuUnit(importVo.getAdvertisedSkuUnit());
                        updateList.add(somWalmartAdvertisingPerformanceResult);
                    }
                } else {
                    // 输出站点不存在的异常数据
                    resultStr.append("站点:")
                            .append(importVo.getSite())
                            .append(" — 查询数据不存在")
                            .append("\n");
                }
            }
        }

        if (!insertList.isEmpty()) {
            somWalmartAdvertisingPerformanceMapper.insertBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            somWalmartAdvertisingPerformanceMapper.batchUpdate(updateList);
        }

        return resultStr.toString();
    }

    /**
     * export
     * 导出
     * <AUTHOR>
     */
    public String export(SomWalmartAdvertisingPerformancePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomWalmartAdvertisingPerformanceVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Walmart平台广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomWalmartAdvertisingPerformanceVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
