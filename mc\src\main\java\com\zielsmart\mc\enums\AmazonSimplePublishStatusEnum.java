package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

/**
 * 亚马逊简单上货发布状态
 */
@AllArgsConstructor
public enum AmazonSimplePublishStatusEnum {

    NOT_PUBLISH(10, "未发布"),
    PUBLISHED(20, "已发布"),

    ;

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (AmazonSimplePublishStatusEnum value : AmazonSimplePublishStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getDesc();
            }
        }
        return null;
    }

    private final Integer status;
    private final String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
