package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomThreePlatformPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceWhiteVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomThreePlatformPriceWhiteMapper
 * @description 三方平台价格管理-白名单
 * @date 2024-11-13 16:33:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somThreePlatformPriceWhite")
public interface SomThreePlatformPriceWhiteMapper extends BaseMapper<SomThreePlatformPriceWhite> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomThreePlatformPriceWhiteVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomThreePlatformPriceWhiteVo> queryByPage(@Param("searchVo") SomThreePlatformPriceWhitePageSearchVo searchVo, PageRequest pageRequest);
}
