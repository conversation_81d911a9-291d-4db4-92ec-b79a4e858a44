package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomProductStyleConfigurationPageSearchVo;
import com.zielsmart.mc.vo.SomProductStyleConfigurationVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-04-11
 */

@SqlResource("somProductStyleConfiguration")
public interface SomProductStyleConfigurationMapper extends BaseMapper<SomProductStyleConfiguration> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageResult< SomProductStyleConfigurationVo>}
     * <AUTHOR>
     * @history
     */
    List<SomProductStyleConfigurationVo> queryByPage(@Param("searchVo") SomProductStyleConfigurationPageSearchVo searchVo);

    /**
     * queryStyleList
     * 查询风格下拉选集合
     *
     * @return {@link java.util.List<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    List<String> queryStyleList();
}
