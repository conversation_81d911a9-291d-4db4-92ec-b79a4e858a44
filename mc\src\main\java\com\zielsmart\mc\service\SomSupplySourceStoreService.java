package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomSupplySourceStore;
import com.zielsmart.mc.repository.entity.SomSupplySourceStoreConfig;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomSupplySourceStoreConfigMapper;
import com.zielsmart.mc.repository.mapper.SomSupplySourceStoreMapper;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStorePageSearchVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreVo;
import com.zielsmart.mc.vo.supply.*;
import com.zielsmart.mc.vo.supply.ext.CapabilitiesExt;
import com.zielsmart.mc.vo.supply.ext.ConfigurationExt;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
public class SomSupplySourceStoreService {

    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomSupplySourceStoreConfigMapper somSupplySourceStoreConfigMapper;
    @Resource
    private SomSupplySourceStoreMapper somSupplySourceStoreMapper;
    @Resource
    private IMagicService magicService;
    @Value("${x.authorization.token}")
    private String token;

    /**
     * add
     * 新增
     *
     * @param addVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(SomSupplySourceStoreExtVo addVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addVo) || StrUtil.isBlank(addVo.getAlias()) || StrUtil.isBlank(addVo.getAddressStateOrRegion()) || StrUtil.isBlank(addVo.getAddressCity()) || StrUtil.isBlank(addVo.getAddressPostalCode())
                || StrUtil.isBlank(addVo.getAddressLine1()) || StrUtil.isBlank(addVo.getAddressPhone()) || ObjectUtil.isEmpty(addVo.getConfiguration()) || ObjectUtil.isEmpty(addVo.getCapabilities())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        /*if (!(addVo.getAlias().contains("-") && addVo.getAlias().contains("-"))) {
            throw new ValidateException("提货仓名称格式必须为：提货仓名称 - 城市");
        }*/
        // 主数据
        //addVo.setSupplySourceCode(generCode(addVo.getWarehouseCode(), addVo.getSlCode()));
        addVo.setSupplySourceCode(IdUtil.fastSimpleUUID());
        if (CollectionUtil.isNotEmpty(addVo.getList())) {
            addVo.setMfnWarehouseSl(JSONUtil.toJsonStr(addVo.getList()));
        }
        addVo.setStatus(20);
        addVo.setPushStatus(10);
        String aid = IdUtil.fastSimpleUUID();
        addVo.setAid(aid);
        addVo.setCreateNum(tokenUser.getJobNumber());
        addVo.setCreateName(tokenUser.getUserName());
        addVo.setCreateTime(DateTime.now().toJdkDate());
        somSupplySourceStoreMapper.insert(ConvertUtils.beanConvert(addVo, SomSupplySourceStore.class));
        Root root = generRoot(addVo);
        // 配置信息
        SomSupplySourceStoreConfig config = new SomSupplySourceStoreConfig();
        config.setAid(IdUtil.fastSimpleUUID());
        config.setStoreId(aid);
        config.setConfigurationJsonString(JSONUtil.toJsonStr(root.getConfiguration()));
        config.setCapabilitiesJsonString(JSONUtil.toJsonStr(root.getCapabilities()));
        config.setConfigVersion(generVersion());
        config.setPushStatus(10);
        config.setCreateNum(tokenUser.getJobNumber());
        config.setCreateName(tokenUser.getUserName());
        config.setCreateTime(DateTime.now().toJdkDate());
        somSupplySourceStoreConfigMapper.insert(config);
    }

    /**
     * 生成唯一的提货仓标识符
     *
     * @param whCode
     * @param slCode
     * @return
     */
    private String generCode(String whCode, String slCode) {
        // 生成规则：“自发仓编码” + “库区编码” + “三位流水号”,唯一
        String supplySourceCode = whCode + slCode + RandomUtil.randomNumbers(3);
        long count = somSupplySourceStoreMapper.createLambdaQuery().andEq("supply_source_code", supplySourceCode).count();
        if (count != 0) {
            generCode(whCode, slCode);
        }
        return supplySourceCode;
    }

    /**
     * 生成版本号
     *
     * @return
     */
    public String generVersion() {
        String version;
        int year = DateUtil.year(DateTime.now());
        int tempMonth = DateUtil.month(DateTime.now()) + 1;
        String month;
        if (tempMonth < 10) {
            month = "0" + tempMonth;
        } else {
            month = String.valueOf(tempMonth);
        }
        version = String.valueOf(year) + month + "001";
        return version;
    }

    /**
     * 组织数据
     *
     * @param addVo
     * @return
     */
    private Root generRoot(SomSupplySourceStoreExtVo addVo) {
        Root root = new Root();
        root.setAlias(addVo.getAlias());
        // 配置资料
        Configuration configuration = new Configuration();
        configuration.setTimezone(StrUtil.isNotBlank(addVo.getConfiguration().getTimezone()) ? addVo.getConfiguration().getTimezone() : Strings.EMPTY);
        ThroughputConfig throughputConfig = new ThroughputConfig();
        ThroughputCap throughputCap = new ThroughputCap();
        throughputCap.setValue(Integer.valueOf(addVo.getConfiguration().getThroughputCapValue()));
        throughputCap.setTimeUnit(addVo.getConfiguration().getThroughputCapTimeUnit());
        throughputConfig.setThroughputCap(throughputCap);
        throughputConfig.setThroughputUnit(null);
        configuration.setThroughputConfig(throughputConfig);
        OperationalConfiguration operationalConfiguration = new OperationalConfiguration();
        ContactDetails contactDetails = new ContactDetails();
        Primary primary = new Primary();
        primary.setEmail(StrUtil.isNotBlank(addVo.getConfiguration().getEmail()) ? addVo.getConfiguration().getEmail() : Strings.EMPTY);
        primary.setPhone(StrUtil.isNotBlank(addVo.getConfiguration().getPhone()) ? addVo.getConfiguration().getPhone() : Strings.EMPTY);
        contactDetails.setPrimary(primary);
        operationalConfiguration.setContactDetails(contactDetails);
        OperatingHoursByDay operatingHoursByDay = new OperatingHoursByDay();
        operatingHoursByDay.setMonday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getMonday())?addVo.getCapabilities().getMonday():null);
        operatingHoursByDay.setTuesday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getTuesday())?addVo.getCapabilities().getTuesday():null);
        operatingHoursByDay.setWednesday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getWednesday())?addVo.getCapabilities().getWednesday():null);
        operatingHoursByDay.setThursday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getThursday())?addVo.getCapabilities().getThursday():null);
        operatingHoursByDay.setFriday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getFriday())?addVo.getCapabilities().getFriday():null);
        operatingHoursByDay.setSaturday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getSaturday())?addVo.getCapabilities().getSaturday():null);
        operatingHoursByDay.setSunday(CollectionUtil.isNotEmpty(addVo.getCapabilities().getSunday())?addVo.getCapabilities().getSunday():null);
        operationalConfiguration.setOperatingHoursByDay(operatingHoursByDay);
        HandlingTime handlingTime = new HandlingTime();
        handlingTime.setValue(addVo.getConfiguration().getHandlingTimeValue());
        handlingTime.setTimeUnit(addVo.getConfiguration().getHandlingTimeUnit());
        operationalConfiguration.setHandlingTime(handlingTime);
        operationalConfiguration.setThroughputConfig(throughputConfig);
        configuration.setOperationalConfiguration(operationalConfiguration);
        // 工作时间
        Capabilities capabilities = new Capabilities();
        Outbound outbound = new Outbound();
        ReturnLocation returnLocation = new ReturnLocation();
        AddressWithContact addressWithContact = new AddressWithContact();
        Address address = new Address();
        address.setCity(addVo.getAddressCity());
        address.setPostalCode(addVo.getAddressPostalCode());
        address.setStateOrRegion(addVo.getAddressStateOrRegion());
        address.setPhone(addVo.getAddressPhone());
        address.setCountryCode("US");
        address.setName(addVo.getAddressName());
        address.setAddressLine1(addVo.getAddressLine1());
        address.setAddressLine2(addVo.getAddressLine2());
        address.setAddressLine3(addVo.getAddressLine3());
        address.setCounty(addVo.getAddressCountry());
        address.setDistrict(addVo.getAddressDistrict());
        addressWithContact.setAddress(address);
        addressWithContact.setContactDetails(contactDetails);
        returnLocation.setAddressWithContact(addressWithContact);
        outbound.setReturnLocation(returnLocation);
        DeliveryChannel deliveryChannel = new DeliveryChannel();
        deliveryChannel.setSupported(addVo.getConfiguration().isDeliveryIsSupported());
        deliveryChannel.setOperationalConfiguration(operationalConfiguration);
        outbound.setDeliveryChannel(deliveryChannel);
        PickupChannel pickupChannel = new PickupChannel();
        InventoryHoldPeriod inventoryHoldPeriod = new InventoryHoldPeriod();
        inventoryHoldPeriod.setValue(addVo.getConfiguration().getInventoryHoldPeriodValue());
        inventoryHoldPeriod.setTimeUnit(addVo.getConfiguration().getInventoryHoldPeriodUnit());
        pickupChannel.setInventoryHoldPeriod(inventoryHoldPeriod);
        pickupChannel.setSupported(addVo.getConfiguration().isPickupIsSupported());
        pickupChannel.setOperationalConfiguration(operationalConfiguration);
        outbound.setPickupChannel(pickupChannel);
        outbound.setSupported(true);
        outbound.setOperationalConfiguration(operationalConfiguration);
        capabilities.setOutbound(outbound);
        root.setConfiguration(configuration);
        root.setCapabilities(capabilities);
        return root;
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomSupplySourceStoreVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomSupplySourceStoreExtVo> query(SomSupplySourceStorePageSearchVo searchVo) {
        List<SomSupplySourceStoreExtVo> resultList = dynamicSqlManager.getMapper(SomSupplySourceStoreMapper.class).query(searchVo);
        if (CollectionUtil.isNotEmpty(resultList)) {
            List<String> codeList = Arrays.asList("SupplySourceStatus", "SupplySourcePushStatus");
            List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", codeList).select();
            for (SomSupplySourceStoreExtVo vo : resultList) {
                if (StrUtil.isNotBlank(vo.getAddressLine1())) {
                    String address = vo.getAddressLine1();
                    if (StrUtil.isNotBlank(vo.getAddressLine2())) {
                        address += ("\n") + vo.getAddressLine2();
                    }
                    if (StrUtil.isNotBlank(vo.getAddressLine3())) {
                        address += ("\n") + vo.getAddressLine3();
                    }
                    vo.setAddress(address);
                }
                List<String> nameList = new ArrayList<>();
                if (StrUtil.isNotBlank(vo.getMfnWarehouseSl())) {
                    List<SomSupplySourceStoreVo.UseableWarehouse> list = JSONUtil.toList(JSONUtil.parseArray(vo.getMfnWarehouseSl()), SomSupplySourceStoreVo.UseableWarehouse.class);
                    list.forEach(f -> {
                        nameList.add(f.getMfnWarehouseName() + "-" + f.getMfnSlName());
                    });
                    vo.setList(list);
                }
                vo.setMfnWhNameAndSlName(CollectionUtil.isNotEmpty(nameList) ? String.join("\n", nameList) : null);
                //vo.setWhNameAndSlName(StrUtil.isNotBlank(vo.getSlName()) ? vo.getWarehouseName() + ("\n") + vo.getSlName() : vo.getWarehouseName());
                //vo.setMfnWhNameAndSlName(StrUtil.isNotBlank(vo.getMfnSlName()) ? vo.getMfnWarehouseName() + ("\n") + vo.getMfnSlName() : vo.getMfnWarehouseName());
                dictionaryInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemTypeCode(), "SupplySourceStatus") && StrUtil.equals(f.getItemValue(), vo.getStatus().toString())).findFirst().ifPresent(ps -> {
                    vo.setStatusName(ps.getItemLable());
                });
                dictionaryInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemTypeCode(), "SupplySourcePushStatus") && StrUtil.equals(f.getItemValue(), vo.getPushStatus().toString())).findFirst().ifPresent(ps -> {
                    vo.setPushStatusName(ps.getItemLable());
                });
            }
        }
        return resultList;
    }

    /**
     * queryByAid
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomSupplySourceStoreVo}
     * <AUTHOR>
     * @history
     */
    public SomSupplySourceStoreConfigExtVo queryByAid(SomSupplySourceStoreVo searchVo) throws ValidateException {
        if (StrUtil.isBlank(searchVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStoreConfigExtVo resultVo;
        SomSupplySourceStore sourceStoreVo = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", searchVo.getAid()).single();
        resultVo = ConvertUtils.beanConvert(sourceStoreVo, SomSupplySourceStoreConfigExtVo.class);
        if (ObjectUtil.isNotEmpty(resultVo)) {
            List<String> codeList = Arrays.asList("SupplySourceStatus", "SupplySourcePushStatus");
            List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", codeList).select();
            dictionaryInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemTypeCode(), "SupplySourceStatus") && StrUtil.equals(f.getItemValue(), resultVo.getStatus().toString())).findFirst().ifPresent(ps -> {
                resultVo.setStatusName(ps.getItemLable());
            });
            dictionaryInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemTypeCode(), "SupplySourcePushStatus") && StrUtil.equals(f.getItemValue(), resultVo.getPushStatus().toString())).findFirst().ifPresent(ps -> {
                resultVo.setPushStatusName(ps.getItemLable());
            });
            if (StrUtil.isNotBlank(resultVo.getMfnWarehouseSl())) {
                List<SomSupplySourceStoreVo.UseableWarehouse> list = JSONUtil.toList(JSONUtil.parseArray(resultVo.getMfnWarehouseSl()), SomSupplySourceStoreVo.UseableWarehouse.class);
                resultVo.setList(list);
            }
        }
        List<SomSupplySourceStoreConfig> configList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", searchVo.getAid()).desc("config_version").select();
        if (CollectionUtil.isNotEmpty(configList)) {
            SomSupplySourceStoreConfig temp = configList.get(0);
            Configuration configuration = JSONUtil.toBean(temp.getConfigurationJsonString(), Configuration.class);
            Capabilities capabilities = JSONUtil.toBean(temp.getCapabilitiesJsonString(), Capabilities.class);
            ConfigurationExt configExt = new ConfigurationExt();
            configExt.setEmail(configuration.getOperationalConfiguration().getContactDetails().getPrimary().getEmail());
            configExt.setPhone(configuration.getOperationalConfiguration().getContactDetails().getPrimary().getPhone());
            configExt.setThroughputCapValue(configuration.getThroughputConfig().getThroughputCap().getValue().toString());
            configExt.setThroughputCapTimeUnit(configuration.getThroughputConfig().getThroughputCap().getTimeUnit());
            configExt.setHandlingTimeValue(configuration.getOperationalConfiguration().getHandlingTime().getValue());
            configExt.setHandlingTimeUnit(configuration.getOperationalConfiguration().getHandlingTime().getTimeUnit());
            configExt.setTimezone(configuration.getTimezone());
            configExt.setDeliveryIsSupported(capabilities.getOutbound().getDeliveryChannel().isSupported());
            configExt.setPickupIsSupported(capabilities.getOutbound().getPickupChannel().isSupported());
            configExt.setInventoryHoldPeriodValue(capabilities.getOutbound().getPickupChannel().getInventoryHoldPeriod().getValue());
            configExt.setInventoryHoldPeriodUnit(capabilities.getOutbound().getPickupChannel().getInventoryHoldPeriod().getTimeUnit());
            resultVo.setConfiguration(configExt);
            CapabilitiesExt capabilitiesExt = new CapabilitiesExt();
            capabilitiesExt.setMonday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getMonday());
            capabilitiesExt.setTuesday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getTuesday());
            capabilitiesExt.setWednesday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getWednesday());
            capabilitiesExt.setThursday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getThursday());
            capabilitiesExt.setFriday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getFriday());
            capabilitiesExt.setSaturday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getSaturday());
            capabilitiesExt.setSunday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getSunday());
            resultVo.setCapabilities(capabilitiesExt);
        }
        return resultVo;
    }

    /**
     * edit
     * 编辑
     *
     * @param editVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(SomSupplySourceStoreConfigExtVo editVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(editVo) || StrUtil.isBlank(editVo.getAid()) || StrUtil.isBlank(editVo.getAlias()) || StrUtil.isBlank(editVo.getAddressStateOrRegion()) || StrUtil.isBlank(editVo.getAddressCity()) || StrUtil.isBlank(editVo.getAddressPostalCode())
                || StrUtil.isBlank(editVo.getAddressLine1()) || StrUtil.isBlank(editVo.getAddressPhone()) || ObjectUtil.isEmpty(editVo.getConfiguration()) || ObjectUtil.isEmpty(editVo.getCapabilities())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        /*if (!(editVo.getAlias().contains("-") && editVo.getAlias().contains("-"))) {
            throw new ValidateException("提货仓名称格式必须为：提货仓名称-城市");
        }*/
        if (CollectionUtil.isNotEmpty(editVo.getList())) {
            editVo.setMfnWarehouseSl(JSONUtil.toJsonStr(editVo.getList()));
        }
        SomSupplySourceStore obj = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", editVo.getAid()).single();
//        if (ObjectUtil.equal(obj.getPushStatus(), 10)) {
        boolean objFlag = StrUtil.equals(obj.getAlias(), editVo.getAlias()) && StrUtil.equals(obj.getAddressName(), editVo.getAddressName())
                && StrUtil.equals(obj.getAddressLine1(), editVo.getAddressLine1()) && StrUtil.equals(obj.getAddressLine2(), editVo.getAddressLine2()) && StrUtil.equals(obj.getAddressLine3(), editVo.getAddressLine3())
                && StrUtil.equals(obj.getAddressCity(), editVo.getAddressCity()) && StrUtil.equals(obj.getAddressCountry(), editVo.getAddressCountry()) && StrUtil.equals(obj.getAddressDistrict(), editVo.getAddressDistrict())
                && StrUtil.equals(obj.getAddressPhone(), editVo.getAddressPhone()) && StrUtil.equals(obj.getAddressCountryCode(), editVo.getAddressCountryCode())
                && StrUtil.equals(obj.getAddressStateOrRegion(), editVo.getAddressStateOrRegion()) && StrUtil.equals(obj.getAddressPostalCode(), editVo.getAddressPostalCode())
                && StrUtil.equals(obj.getMfnWarehouseSl(), editVo.getMfnWarehouseSl()) && obj.getSpreetailFlag() == editVo.getSpreetailFlag() && StrUtil.equals(obj.getSpreetailWarehouseName(), editVo.getSpreetailWarehouseName());
        log.info("步骤一中的内容{}", objFlag == true ? "未修改" : "已修改");
        List<SomSupplySourceStoreConfig> configList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", editVo.getAid()).desc("config_version").select();
        SomSupplySourceStoreConfig config = null;
        if (CollectionUtil.isNotEmpty(configList)) {
            config = configList.get(0);
        }
        Root root = generRoot(editVo);
        String configuration = JSONUtil.toJsonStr(root.getConfiguration());
        String capabilities = JSONUtil.toJsonStr(root.getCapabilities());
        boolean configFlag = false;
        if (config != null) {
            configFlag = StrUtil.equals(config.getConfigurationJsonString(), configuration) && StrUtil.equals(config.getCapabilitiesJsonString(), capabilities);
        }
        log.info("步骤二中的内容{}", configFlag == true ? "未修改" : "已修改");
        if (objFlag && configFlag) {
            throw new ValidateException("您未改变任何内容，请重新编辑");
        }
        // 更新主表
        somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", editVo.getAid()).updateSelective(ConvertUtils.beanConvert(editVo, SomSupplySourceStore.class));
        // 更新配置表
        SomSupplySourceStoreConfig data = organizeData(config, root, tokenUser, editVo);
        somSupplySourceStoreConfigMapper.insert(data);
//        } else if (ObjectUtil.equal(obj.getPushStatus(), 20)) {
//            if (configFlag) {
//                throw new ValidateException("您未改变任何内容，请重新编辑");
//            }
//            SomSupplySourceStoreConfig data = organizeData(config, root, tokenUser);
//            somSupplySourceStoreConfigMapper.insert(data);
//        }
    }

    /**
     * 组织保存数据
     *
     * @param config
     * @param root
     * @param tokenUser
     * @return
     */
    private SomSupplySourceStoreConfig organizeData(SomSupplySourceStoreConfig config, Root root, TokenUserInfo tokenUser, SomSupplySourceStoreConfigExtVo editVo) {
        if (config != null) {
            if (ObjectUtil.equal(10, config.getPushStatus())) {
                config.setConfigVersion(config.getConfigVersion());
                somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("aid", config.getAid()).delete();
            } else {
                Integer configVersion = Integer.valueOf(config.getConfigVersion()) + 1;
                config.setConfigVersion(String.valueOf(configVersion));
            }
        } else {
            config = new SomSupplySourceStoreConfig();
            config.setStoreId(editVo.getAid());
            config.setConfigVersion(generVersion());
        }
        config.setAid(IdUtil.fastSimpleUUID());
        config.setConfigurationJsonString(JSONUtil.toJsonStr(root.getConfiguration()));
        config.setCapabilitiesJsonString(JSONUtil.toJsonStr(root.getCapabilities()));
        config.setPushStatus(10);
        config.setCreateNum(tokenUser.getJobNumber());
        config.setCreateName(tokenUser.getUserName());
        config.setCreateTime(DateTime.now().toJdkDate());
        return config;
    }

    /**
     * inactive
     * 禁用
     *
     * @param inactiveVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void inactive(SomSupplySourceStoreVo inactiveVo) throws ValidateException {
        if (ObjectUtil.isEmpty(inactiveVo) || StrUtil.isEmpty(inactiveVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStore obj = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", inactiveVo.getAid()).andEq("status", 10).andEq("push_status", 20).single();
        if (ObjectUtil.isNotEmpty(obj)) {
            if(StrUtil.isBlank(obj.getSupplySourceId())){
                throw new ValidateException("当前数据不允许禁用");
            }
            try{
                UpdateSupplySourceStatusRequest request = new UpdateSupplySourceStatusRequest();
                request.setSupplySourceId(obj.getSupplySourceId());
                SupplySourceUpdateStatus status = new SupplySourceUpdateStatus();
                status.setStatus("Inactive");
                request.setSupplySourceUpdateStatus(status);
                ResultVo resultVo = magicService.updateSupplySourceStatus(token,request);
                if(resultVo.isSuccess()){
                    obj.setStatus(20);
                    somSupplySourceStoreMapper.updateById(obj);
                }else {
                    throw new ValidateException("调用平台接口出错,错误信息:"+resultVo.getMessage());
                }
            }catch (Exception e){
                throw new ValidateException(e.getMessage());
            }
        } else {
            throw new ValidateException("查询不到需禁用的数据");
        }
    }

    /**
     * 启用
     *
     * @param activeVo
     * @throws ValidateException
     */
    public void active(SomSupplySourceStoreVo activeVo) throws ValidateException {
        if (ObjectUtil.isEmpty(activeVo) || StrUtil.isEmpty(activeVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStore obj = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", activeVo.getAid()).andEq("status", 20).andEq("push_status", 20).single();
        if (ObjectUtil.isNotEmpty(obj)) {
            if(StrUtil.isBlank(obj.getSupplySourceId())){
                throw new ValidateException("当前数据不允许启用");
            }
            try {
                UpdateSupplySourceStatusRequest request = new UpdateSupplySourceStatusRequest();
                request.setSupplySourceId(obj.getSupplySourceId());
                SupplySourceUpdateStatus status = new SupplySourceUpdateStatus();
                status.setStatus("Active");
                request.setSupplySourceUpdateStatus(status);
                ResultVo resultVo = magicService.updateSupplySourceStatus(token,request);
                if(resultVo.isSuccess()){
                    obj.setStatus(10);
                    somSupplySourceStoreMapper.updateById(obj);
                }else {
                    throw new ValidateException("调用平台接口出错,错误信息:"+resultVo.getMessage());
                }
            } catch (Exception e){
                throw new ValidateException(e.getMessage());
            }
        } else {
            throw new ValidateException("查询不到需启用的数据");
        }
    }

    /**
     * disable
     * 归档
     *
     * @param disableVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void disable(SomSupplySourceStoreVo disableVo) throws ValidateException {
        if (ObjectUtil.isEmpty(disableVo) || StrUtil.isEmpty(disableVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStore obj = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", disableVo.getAid()).single();
        if (ObjectUtil.isNotEmpty(obj)) {
            if(StrUtil.isBlank(obj.getSupplySourceId())){
                throw new ValidateException("当前数据不允许归档");
            }
            try{
                DisableSupplySourceStatusRequest request = new DisableSupplySourceStatusRequest();
                request.setSupplySourceId(obj.getSupplySourceId());
                ResultVo resultVo = magicService.archiveSupplySource(token,request);
                if(resultVo.isSuccess()){
                    obj.setStatus(99);
                    somSupplySourceStoreMapper.updateById(obj);
                }else {
                    throw new ValidateException("调用平台接口出错,错误信息:"+resultVo.getMessage());
                }
            }catch (Exception e){
                throw new ValidateException(e.getMessage());
            }
        } else {
            throw new ValidateException("查询不到需归档的数据");
        }
    }

    /**
     * pushSupplySources
     * 推送提货仓资料
     *
     * @param pushVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void pushSupplySources(SomSupplySourceStoreVo pushVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(pushVo) || StrUtil.isEmpty(pushVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStore obj = somSupplySourceStoreMapper.createLambdaQuery().andEq("aid", pushVo.getAid()).andNotEq("push_status", 20).single();
        if (ObjectUtil.isNotEmpty(obj)) {
            PushInfo pushInfo = new PushInfo();
            Address address = new Address();
            List<SomSupplySourceStoreConfig> configList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", pushVo.getAid()).desc("config_version").select();
            if (CollectionUtil.isNotEmpty(configList)) {
                SomSupplySourceStoreConfig config = configList.get(0);
                Capabilities capabilities = JSONUtil.toBean(config.getCapabilitiesJsonString(), Capabilities.class);
                if (ObjectUtil.isNotEmpty(capabilities) && ObjectUtil.isNotEmpty(capabilities.getOutbound()) && ObjectUtil.isNotEmpty(capabilities.getOutbound().getReturnLocation())
                        && ObjectUtil.isNotEmpty(capabilities.getOutbound().getReturnLocation().getAddressWithContact()) && ObjectUtil.isNotEmpty(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress())) {
                    address.setName(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getName());
                    address.setAddressLine1(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getAddressLine1());
                    address.setCountryCode(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getCountryCode());
                    address.setStateOrRegion(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getStateOrRegion());
                    address.setAddressLine2(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getAddressLine2());
                    address.setAddressLine3(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getAddressLine3());
                    address.setCity(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getCity());
                    address.setCountryCode(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getCountryCode());
                    address.setDistrict(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getDistrict());
                    address.setPostalCode(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getPostalCode());
                    address.setPhone(capabilities.getOutbound().getReturnLocation().getAddressWithContact().getAddress().getPhone());
                }
                obj.setPushNum(tokenUser.getJobNumber());
                obj.setPushName(tokenUser.getUserName());
                obj.setPushTime(DateTime.now().toJdkDate());
                pushInfo.setAddress(address);
                pushInfo.setSupplySourceCode(obj.getSupplySourceCode());
                pushInfo.setAlias(obj.getAlias());
                try {
                    ResultVo<CreateSupplySourceResponse> resultVo = magicService.createSupplySource(token, pushInfo);
                    if (resultVo.isSuccess() && ObjectUtil.isNotEmpty(resultVo.getData())) {
                        obj.setSupplySourceId(resultVo.getData().getSupplySourceId());
                        obj.setPushStatus(20);
                        somSupplySourceStoreMapper.updateById(obj);
                    } else {
                        obj.setPushStatus(99);
                        somSupplySourceStoreMapper.updateById(obj);
                        throw new ValidateException("调用平台接口出错,错误信息:" + resultVo.getMessage());
                    }
                }catch (Exception e){
                    throw new ValidateException(e.getMessage());
                }
            }
        } else {
            throw new ValidateException("查询不到需推送提货仓资料的数据");
        }
    }

    /**
     * queryAllInfos
     * 获取所有的提货仓
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomSupplySourceStoreVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomSupplySourceStoreVo> queryAllInfos() {
        List<SomSupplySourceStore> allInfos = somSupplySourceStoreMapper.createLambdaQuery().andEq("status",10).andEq("push_status",20).select("aid","supply_source_id", "alias");
        return ConvertUtils.listConvert(allInfos, SomSupplySourceStoreVo.class);
    }
}
