package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomSheinWarehouseConfigService;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigVo;
import com.zielsmart.mc.vo.SomSheinWarehouseInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomSheinWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somSheinWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Shein可售仓库配置管理")
public class SomSheinWarehouseConfigController extends BasicController {

    @Resource
    SomSheinWarehouseConfigService somSheinWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomSheinWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomSheinWarehouseConfigVo>> queryByPage(@RequestBody SomSheinWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somSheinWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somSheinWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomSheinWarehouseConfigVo somSheinWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSheinWarehouseConfigService.save(somSheinWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "仓库区域编码名称")
    @GetMapping(value = "/warehouse")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomSheinWarehouseInfoVo>> warehouse() throws ValidateException {
        return ResultVo.ofSuccess(somSheinWarehouseConfigService.warehouse());
    }


    /**
     * update
     *
     * @param somSheinWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomSheinWarehouseConfigVo somSheinWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSheinWarehouseConfigService.update(somSheinWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somSheinWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomSheinWarehouseConfigVo somSheinWarehouseConfigVo) throws ValidateException {
        somSheinWarehouseConfigService.delete(somSheinWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }
}
