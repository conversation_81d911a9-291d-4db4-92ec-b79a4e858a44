package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomTargetWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2023-12-06
 */

@SqlResource("somTargetWarehouseConfig")
public interface SomTargetWarehouseConfigMapper extends BaseMapper<SomTargetWarehouseConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomTargetWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTargetWarehouseConfigVo> queryByPage(@Param("searchVo") SomTargetWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
