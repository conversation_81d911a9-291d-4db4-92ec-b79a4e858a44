package com.zielsmart.mc.controller;

import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.SomProductSeriesConfigurationService;
import com.zielsmart.mc.vo.SomProductSeriesConfigurationPageSearchVo;
import com.zielsmart.mc.vo.SomProductSeriesConfigurationVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomProductSeriesConfigurationController
 * @description
 * @date 2024-04-11 16:32:06
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somProductSeriesConfiguration", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "产品系列配置表管理")
public class SomProductSeriesConfigurationController extends BasicController {

    @Resource
    SomProductSeriesConfigurationService somProductSeriesConfigurationService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomProductSeriesConfigurationVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomProductSeriesConfigurationVo>> queryByPage(@RequestBody SomProductSeriesConfigurationPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somProductSeriesConfigurationService.queryByPage(searchVo));
    }

    /**
     * enableOrDisable
     * 启用/禁用
     *
     * @param somProductSeriesConfigurationVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "启用/禁用")
    @PostMapping(value = "/enableOrDisable")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> enableOrDisable(@RequestBody SomProductSeriesConfigurationVo somProductSeriesConfigurationVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somProductSeriesConfigurationService.enableOrDisable(somProductSeriesConfigurationVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * querySeriesList
     * 查询产品系列下拉选集合
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<java.lang.String>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询产品系列下拉选集合")
    @PostMapping(value = "/querySeriesList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<String>> querySeriesList(@RequestBody SomProductSeriesConfigurationPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somProductSeriesConfigurationService.querySeriesList(searchVo.getStyleName()));
    }

    /**
     * upload
     * 上传图片
     *
     * @param param
     * @param multipartFiles
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<cn.hutool.json.JSONObject>>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "上传图片{aid:'',typeName:'',type:''}")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> upload(@RequestParam Map param, @RequestParam("files") MultipartFile[] multipartFiles, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        return ResultVo.ofSuccess(somProductSeriesConfigurationService.upload(param, multipartFiles, tokenUser));
    }

    /**
     * addSort
     * 增加排序
     *
     * @param somProductSeriesConfigurationVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "增加排序")
    @PostMapping(value = "/addSort")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addSort(@RequestBody SomProductSeriesConfigurationVo somProductSeriesConfigurationVo) throws ValidateException {
        somProductSeriesConfigurationService.addSort(somProductSeriesConfigurationVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * addDefinition
     * 增加系列定义
     *
     * @param somProductSeriesConfigurationVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "增加系列定义")
    @PostMapping(value = "/addDefinition")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addDefinition(@RequestBody SomProductSeriesConfigurationVo somProductSeriesConfigurationVo) throws ValidateException {
        somProductSeriesConfigurationService.addDefinition(somProductSeriesConfigurationVo);
        return ResultVo.ofSuccess(null);
    }
}
