package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IBiService;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.util.MarketActivityUtil;
import com.zielsmart.mc.util.MessageType;
import com.zielsmart.mc.vo.SomWootPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomWootPromotionVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomWootPromotionService {

    @Resource
    private SomDealRuleWhiteListMapper somDealRuleWhiteListMapper;

    @Resource
    private SomKpiGrossProfitMapper kopGrossProfitMapper;

    @Resource
    private SomWootPurchaseOrderItemMapper wootItemMapper;

    @Resource
    private McPlatformPropertiesMapper platformPropertiesMapper;

    @Resource
    private SomWootPromotionMapper somWootPromotionMapper;

    @Resource
    private McListingInfoAmazonMapper infoAmazonMapper;

    @Resource
    private McProductSalesMapper productSalesMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private SomProductHierarchyMapper hierarchyMapper;

    @Resource
    private SomWootPromotionWarehouseConfigMapper wootConfigMapper;

    @Resource
    private IBiService biService;

    @Value("${bi.magic.head.token}")
    private String biToken;

    @Resource
    private McSkuInfoMapper skuInfoMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    private static Map<String, Object> getStringObjectMap(String site, List<SomWootPromotionVo> list, String exportDate, Map<String, Long> skuCountMap) {
        Map<String, Object> data = new HashMap<>();
        data.put("exportDate", exportDate);
        data.put("site", "Woot.com".equals(site) ? "No" : "Yes");
        data.put("shippingMethod", "Woot.com".equals(site) ? "IOG TRANS" : "Will Call");
        String dropshipFrom = "Ameziel Inc.\n8291 Milliken Ave\nRancho Cucamonga, CA 91730\nUS\n9099477676";
        data.put("from", "Woot.com".equals(site) ? "FBA" : dropshipFrom);
        List<Map<String, Object>> itemList = new ArrayList<>();
        for (SomWootPromotionVo woot : list) {
            Map<String, Object> item = new HashMap<>();
            item.put("yes", "Woot.com".equals(woot.getSite()) ? "Yes" : "No");
            item.put("ean", woot.getEan());
            item.put("asin", woot.getAsin());
            item.put("fnSku", woot.getFnSku());
            item.put("win", woot.getWin());
            item.put("brand", woot.getBrand());
            if (woot.getProductName() != null) {
                String[] split = woot.getProductName().split(",");
                item.put("shortProductTitle", split[0]);
            }
            item.put("longProductTitle", woot.getProductName());
            item.put("website", "https://www.amazon.com/dp/" + woot.getAsin());
            item.put("color", null);
            item.put("gender", null);
            item.put("size", null);
            item.put("model", woot.getSellerSku());
            item.put("manuf", woot.getSellerSku());
            item.put("manu", 365);
            item.put("link", null);
            item.put("country", "China");
            item.put("condition", "New");
            item.put("packageType", "Retail Box");
            item.put("msrp", woot.getListPrice());
            item.put("weight", woot.getNetWeight() != null ? woot.getNetWeight().divide(BigDecimal.valueOf(1000)).multiply(BigDecimal.valueOf(2.2046)) : null);
            item.put("length", woot.getPackageingLength1() != null ? woot.getPackageingLength1().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            item.put("width", woot.getPackageingLength2() != null ? woot.getPackageingLength2().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            item.put("height", woot.getPackageingLength3() != null ? woot.getPackageingLength3().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            item.put("unitsPerCasePack", skuCountMap.getOrDefault(woot.getSku(), 1l));
            item.put("casePackWeight", woot.getNetWeight() != null ? woot.getNetWeight().divide(BigDecimal.valueOf(1000)).multiply(BigDecimal.valueOf(2.2046)) : null);
            item.put("casePackLength", woot.getPackageingLength1() != null ? woot.getPackageingLength1().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            item.put("casePackWidth", woot.getPackageingLength2() != null ? woot.getPackageingLength2().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            item.put("casePackHeight", woot.getPackageingLength3() != null ? woot.getPackageingLength3().divide(BigDecimal.valueOf(2.54D), 2, RoundingMode.HALF_UP) : null);
            String promotionQuantity = woot.getPromotionQuantity();
            int purchaseQty = 0;
            if (!StrUtil.isEmpty(promotionQuantity)) {
                JSONArray objects = JSONUtil.parseArray(promotionQuantity);
                purchaseQty = objects.stream().map(x -> {
                    cn.hutool.json.JSONObject json = (cn.hutool.json.JSONObject) x;
                    return json.getInt("quantity");
                }).reduce(Integer::sum).orElse(0);

            }
            item.put("purchaseQty", purchaseQty);
            item.put("unitCost", woot.getPromotionPrice());
            item.put("nego", woot.getWootPrice() != null ? woot.getWootPrice().multiply(new BigDecimal(purchaseQty)) : null);
            item.put("typeName", woot.getCategoryName());
            itemList.add(item);
        }
        data.put("list", itemList);
        return data;
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomWootPromotionVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWootPromotionVo> queryByPage(SomWootPromotionPageSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomWootPromotionVo> pageResult = dynamicSqlManager.getMapper(SomWootPromotionMapper.class).queryByPage(searchVo, pageRequest);
        for (SomWootPromotionVo woot : pageResult.getList()) {
            if (woot.getPromotionQuantity() != null) {
                JSONArray objects = JSONUtil.parseArray(woot.getPromotionQuantity());
                int quantity = objects.stream().mapToInt(node -> {
                    cn.hutool.json.JSONObject jsonNode = (cn.hutool.json.JSONObject) node;
                    return jsonNode.getInt("quantity");
                }).sum();
                woot.setQuantity(quantity);
                if (woot.getSapPrice() != null && woot.getSapPrice().intValue()!=0) {
                    //毛利率  报价/到仓价 - 1.05
                    BigDecimal gross = woot.getPromotionPrice().divide(woot.getSapPrice(),2, RoundingMode.HALF_UP).subtract(BigDecimal.valueOf(1.05D));
                    //利润 = 报价*毛利率
                    BigDecimal multiply = woot.getPromotionPrice().multiply(gross,  new MathContext(2, RoundingMode.HALF_UP));
                    woot.setMaxProfit(multiply);
                }
            }
            if (woot.getSalesDaysInTheLast12Weeks() != null) {
                List<MarketActivityUtil.OverstockReport> overstockReports = objectMapper.readValue(woot.getSalesDaysInTheLast12Weeks(), new TypeReference<List<MarketActivityUtil.OverstockReport>>() {
                });
                overstockReports.stream().min(Comparator.comparingInt(MarketActivityUtil.OverstockReport::getMarketableDays)).ifPresent(x -> woot.setOverstockReportDays(x.getMarketableDays()));
            } else {
                woot.setOverstockReportDays(0);
            }

        }
        return ConvertUtils.pageConvert(pageResult, SomWootPromotionVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somWootPromotionVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(somWootPromotionVo) || StrUtil.isEmpty(somWootPromotionVo.getSite()) || StrUtil.isEmpty(somWootPromotionVo.getSellerSku())
                || ObjectUtil.isNull(somWootPromotionVo.getVcFlag()) || ObjectUtil.isNull(somWootPromotionVo.getFbaStockFlag())
                || somWootPromotionVo.getWarehouseStockList() == null || somWootPromotionVo.getWarehouseStockList().isEmpty() || StrUtil.isEmpty(somWootPromotionVo.getDealType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        int totalQuantity = somWootPromotionVo.getWarehouseStockList().stream().mapToInt(SomWootPromotionVo.PromotionQuantity::getQuantity).sum();
        if (totalQuantity == 0) {
            throw new ValidateException("提报总数量不能为0");
        }
        //去重 根据“站点” + “展示码” + “活动状态 不等于 提报成功 提报失败 审批未通过
        LambdaQuery<SomWootPromotion> lambdaQuery = somWootPromotionMapper.createLambdaQuery();
        lambdaQuery.andEq("site", somWootPromotionVo.getSite())
                .andEq("seller_sku", somWootPromotionVo.getSellerSku())
                .andNotEq("status", 50)
                .andNotEq("status", 99)
                .andNotEq("status", 30);
        if (somWootPromotionVo.getOverstockReport() != null && somWootPromotionVo.getOverstockReport().size() > 4) {
            somWootPromotionVo.setSalesDaysInTheLast12Weeks(objectMapper.writeValueAsString(somWootPromotionVo.getOverstockReport()));
        }
        if (somWootPromotionVo.getWarehouseStockList() != null) {
            somWootPromotionVo.setPromotionQuantity(objectMapper.writeValueAsString(somWootPromotionVo.getWarehouseStockList()));
        }
        if (StrUtil.isBlank(somWootPromotionVo.getAid())) {
            long count = lambdaQuery.count();
            if (count > 0) {
                throw new ValidateException("此产品已经提报了Woot活动，不允许再次提报");
            }
            SomWootPromotion somWootPromotion = ConvertUtils.beanConvert(somWootPromotionVo, SomWootPromotion.class);
            somWootPromotion.setAid(IdUtil.fastSimpleUUID());
            somWootPromotion.setCreateName(tokenUser.getUserName());
            somWootPromotion.setCreateNum(tokenUser.getJobNumber());
            somWootPromotion.setCreateTime(DateTime.now().toJdkDate());
            somWootPromotion.setStatus(10);
            somWootPromotionMapper.insert(somWootPromotion);
        } else {
            long count = lambdaQuery.andNotEq("aid", somWootPromotionVo.getAid()).count();
            if (count > 0) {
                throw new ValidateException("此产品已经提报了Woot活动，不允许再次提报");
            }
            SomWootPromotion somWootPromotion = ConvertUtils.beanConvert(somWootPromotionVo, SomWootPromotion.class);
            somWootPromotionMapper.createLambdaQuery().andEq("aid", somWootPromotionVo.getAid()).updateSelective(somWootPromotion);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somWootPromotionVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWootPromotionVo) || ObjectUtil.isEmpty(somWootPromotionVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        // 检测状态是否是草稿
        long count = somWootPromotionMapper.createLambdaQuery()
                .andIn("aid", somWootPromotionVo.getAidList())
                .andNotEq("status", 10)
                .count();
        if (count > 0) {
            throw new ValidateException("请检查Woot活动状态，只能删除草稿状态的数据");
        }
        log.info("删除Woot活动数据：删除人：{}，删除数据：{}", tokenUser.getJobNumber(), somWootPromotionVo.getAidList());
        somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).delete();
    }

    /**
     * 导出 excel
     *
     * @param searchVo 入参
     * @return String
     * @throws Exception
     */
    public String export(SomWootPromotionPageSearchVo searchVo) throws Exception {
        // 导出站点不能为空
        String site = searchVo.getSite();
        if (StrUtil.isEmpty(site)) {
            throw new ValidateException("站点不能为空！");
        }
        searchVo.setCurrent(1);
        // 只导出状态是提报中 40 的数据
        searchVo.setStatus(40);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomWootPromotionVo> records = queryByPage(searchVo).getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        //查询层级视图
        List<SomProductHierarchy> hierarchyList = hierarchyMapper.createLambdaQuery()
                .andIn("upper_sku_code", records.stream().map(SomWootPromotionVo::getSku).collect(Collectors.toList()))
                .select("aid", "upper_sku_code");
        // 根据 upper_sku_code 分组，汇总每个sku的条数
        Map<String, Long> skuCountMap = hierarchyList.stream().collect(Collectors.groupingBy(SomProductHierarchy::getUpperSkuCode, Collectors.counting()));
        TemplateExportParams params = new TemplateExportParams("static/excel/WootExportTemplate.xlsx", true);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String exportDate = sdf.format(new Date());
        Map<String, Object> data = getStringObjectMap(site, records, exportDate, skuCountMap);
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(params, data);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("WootPromotion导出失败:{}", e.toString(), e);
            throw new RuntimeException("导出失败" + e.toString());
        }
    }

    public Integer permissions(TokenUserInfo tokenUser) {
        McDictionaryInfo single = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "WootPermissions").single();
        int permissions = Integer.parseInt(single.getItemValue());
        single.setItemValue(String.valueOf(permissions == 0 ? 1 : 0));
        single.setItemValue1(tokenUser.getJobNumber());
        single.setItemValue2(new Date().toString());
        single.setItemValue3(tokenUser.getUserName());
        dictionaryInfoMapper.updateById(single);
        return Integer.parseInt(single.getItemValue());
    }

    /**
     * 获取展示码详情
     *
     * @param somWootPromotionVo 入参
     * @param tokenUser 当前登陆用户
     * @return
     * @throws Exception
     */
    public SomWootPromotionVo detail(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws Exception {
        if (somWootPromotionVo == null || StrUtil.isEmpty(somWootPromotionVo.getSite()) || StrUtil.isEmpty(somWootPromotionVo.getSellerSku()) || ObjectUtil.isNull(somWootPromotionVo.getVcFlag()) || ObjectUtil.isNull(somWootPromotionVo.getFbaStockFlag())) {
            throw new ValidateException("站点、展示码、FBA标识、VC标识 不能为空");
        }
        boolean isVc = somWootPromotionVo.getVcFlag() == 1;
        McProductSales sales = productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("display_product_code", somWootPromotionVo.getSellerSku())
                .andEq("sales_flag", 1)
                .andIsNotNull("asin_code")
                .andEq("site", isVc ? "VC-Amazon.com" : "Amazon.com").single();
        if (sales == null || sales.getAsinCode() == null) {
            throw new ValidateException("产品销售视图没有找到相应的ASIN数据，不允许提报Woot活动");
        }
        //设置sku asin 等属性
        List<McDictionaryInfo> dictionaryInfoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "SalesProductStatus").select();
        Map<String, String> dictMap = dictionaryInfoList.stream().collect(Collectors.toMap(x -> x.getItemValue(), v -> v.getItemLable(), (x1, x2) -> x1));
        somWootPromotionVo.setSku(sales.getProductMainCode());
        SomWootPromotionVo result = new SomWootPromotionVo();
        result.setSku(sales.getProductMainCode());
        result.setAsin(sales.getAsinCode());
        result.setFnSku(sales.getFnskuCode());
        result.setSellerSkuStatus(dictMap.getOrDefault(sales.getSellerSkuStatusCode(), null));
        //设置币种
        McPlatformProperties platformProperties = platformPropertiesMapper.createLambdaQuery().andEq("site", somWootPromotionVo.getSite()).single();
        if (platformProperties != null) {
            result.setCurrency(platformProperties.getCurrencyCode());
        }
        if (!isVc) {
            //市场价  Amazon->FBA  Merchant->FBM
            McListingInfoAmazon infoAmazon = infoAmazonMapper.createLambdaQuery()
                    .andEq("asin_code", result.getAsin())
                    .andEq("site", "Amazon.com")
                    .andEq("fulfillment_channel", somWootPromotionVo.getFbaStockFlag() == 1 ? "Amazon" : "Merchant").single();
            if (infoAmazon == null) {
                //vc标识  没有市场价 所以不报错
                throw new ValidateException("展示码：" + somWootPromotionVo.getSellerSku() + " 没有找到 Amazon Listing，不允许提报Woot活动");
            }
            result.setListPrice(infoAmazon.getPrice());
        }

        //到仓价  因为同步sap价格数据的逻辑是每月同步上个月的数据，所以一直都是最新的
        BigDecimal sapPrice = null;
        if (somWootPromotionVo.getSite().equals("Woot.com")) {
            //取最新年度 最新会计区间的 美国Amazon仓 的到仓价
            sapPrice = somWootPromotionMapper.getComSapPrice(result.getSku());
        } else {
            //Woot.dropship站点   取最新年度 最新会计区间的 美西仓和美国东北仓的到仓价的最大值
            sapPrice = somWootPromotionMapper.getDropshipSapPrice(result.getSku());
        }
        result.setSapPrice(sapPrice == null ? BigDecimal.ZERO : sapPrice);
        if (isVc) {
            //vc标识 市场价等于到仓价
            result.setListPrice(result.getSapPrice());
        }
        //查询基础视图
        List<McSkuInfo> skuInfoList = skuInfoMapper.createLambdaQuery().select("sku_code", "product_type_code", "basics_product_main_code");
        Map<String, McSkuInfo> skuMap = skuInfoList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (x1, x2) -> x1));
        //查询层级关系
        List<SomProductHierarchy> hierarchyList = hierarchyMapper.createLambdaQuery().select("aid", "upper_sku_code", "current_sku_code");
        Map<String, List<SomProductHierarchy>> hierarchyMap = hierarchyList.stream().collect(Collectors.groupingBy(e -> e.getUpperSkuCode()));

        //库存可售天数 未来12周的可销售天数
        marketableDays(skuMap, hierarchyMap, somWootPromotionVo);
        result.setOverstockReport(somWootPromotionVo.getOverstockReport());
        //添加校验  未来12周可销售天数中如果有任意两周的可销售天数小于等于21天的情况下，不允许新增/导入此活动。
        //白名单不校验  查询Woot平台白名单
        List<SomDealRuleWhiteList> wootWhiteList = somDealRuleWhiteListMapper.createLambdaQuery().andEq("platform", "Woot").select();
        boolean isWhite = wootWhiteList.stream().anyMatch(x -> x.getSite().equals(somWootPromotionVo.getSite()) && x.getSellerSku().equals(somWootPromotionVo.getSellerSku()));
        long count = somWootPromotionVo.getOverstockReport().stream().filter(x -> x.getMarketableDays() <= 21).count();
        if (count >= 2 && !isVc && !isWhite) {
            //vc标志 不进行可售天数的校验
            throw new ValidateException("未来12周可销售天数中有两周的可销售天数小于等于21天，不允许新增此活动");
        }
        result.setMarketableDays(somWootPromotionVo.getMarketableDays());
        somWootPromotionVo.getOverstockReport().stream().min(Comparator.comparingInt(x -> x.getMarketableDays())).ifPresent(x -> result.setOverstockReportDays(x.getMarketableDays()));
        //近四周毛利率
        SomKpiGrossProfit kpiGrossProfit = kopGrossProfitMapper.createLambdaQuery().andEq("site", "Amazon.com").andEq("seller_sku", somWootPromotionVo.getSellerSku()).single();
        if (kpiGrossProfit != null) {
            result.setGrossProfitRate30Day(kpiGrossProfit.getOneMonthGrossProfitRate());
        }
        //仓库库存
        List<SomWootPromotionVo.PromotionQuantity> promotionQuantityList = createStockList(somWootPromotionVo);
        result.setWarehouseStockList(promotionQuantityList);
        return result;
    }

    private List<SomWootPromotionVo.PromotionQuantity> createStockList(SomWootPromotionVo somWootPromotionVo) throws Exception {
        List<SomWootPromotionVo.PromotionQuantity> promotionQuantityList = new ArrayList<>();
        List<SomWootPromotionWarehouseConfig> configList = wootConfigMapper.createLambdaQuery().andEq("site", somWootPromotionVo.getSite())
                .andEq("vc_flag", somWootPromotionVo.getVcFlag())
                .andEq("fba_stock_flag", somWootPromotionVo.getFbaStockFlag())
                .select();
        if (configList.isEmpty()) {
            return new ArrayList<>();
        }
        //查询Woot已保存的仓库数据
        Map<String, Integer> existQuantityMap = new HashMap<>();
        if (StrUtil.isNotBlank(somWootPromotionVo.getAid())) {
            SomWootPromotion single = somWootPromotionMapper.single(somWootPromotionVo.getAid());
            if (single.getPromotionQuantity() != null) {
                List<SomWootPromotionVo.PromotionQuantity> promotionQuantities = objectMapper.readValue(single.getPromotionQuantity(), new TypeReference<List<SomWootPromotionVo.PromotionQuantity>>() {
                });
                existQuantityMap = promotionQuantities.stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), v -> v.getQuantity(), (x1, x2) -> x1));
            }
        }
        Map<String, List<SomWootPromotionWarehouseConfig>> warehouseCodeMap = configList.stream().collect(Collectors.groupingBy(e -> e.getUseableWarehouseCode()));
        List<McProductSalesStockVo> stockList = somWootPromotionMapper.querySkuStockInfo(Arrays.asList(somWootPromotionVo.getSellerSku()), warehouseCodeMap.keySet());
        //根据仓库分组
        Map<String, List<McProductSalesStockVo>> warehouseCodeStockMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode()));
        //获取编码对应的名字
        List<McWarehouse> warehouseCodeList = mcWarehouseMapper.createLambdaQuery().andIn("warehouse_code", warehouseCodeMap.keySet()).select();
        Map<String, String> warehouseMap = warehouseCodeList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), v -> v.getWarehouseName(), (x1, x2) -> x1));

        //循环统计仓库 总库存
        for (String warehouseCode : warehouseCodeMap.keySet()) {
            SomWootPromotionVo.PromotionQuantity quantity = new SomWootPromotionVo.PromotionQuantity();
            //获取仓库对应的渠道集合
            List<String> slCodeList = warehouseCodeMap.get(warehouseCode).stream().map(x -> x.getUseableStorageCode()).distinct().collect(Collectors.toList());
            List<McProductSalesStockVo> mcProductSalesStockVos = warehouseCodeStockMap.getOrDefault(warehouseCode, new ArrayList<>());
            Integer totalStock = mcProductSalesStockVos.stream().filter(x -> slCodeList.contains(x.getSlCode())).map(x -> x.getTotalStock()).reduce(Integer::sum).orElse(0);
            quantity.setWarehouseCode(warehouseCode);
            quantity.setAvailable(totalStock);
            quantity.setQuantity(existQuantityMap.getOrDefault(warehouseCode, 0));
            quantity.setWarehouseName(warehouseMap.get(warehouseCode));
            promotionQuantityList.add(quantity);
        }
        return promotionQuantityList;
    }

    private void marketableDays(Map<String, McSkuInfo> skuMap, Map<String, List<SomProductHierarchy>> hierarchyMap, SomWootPromotionVo somWootPromotionVo) {
        //下层SKU集合
        List<String> childSkus = new ArrayList<>();
        String tmpSku = somWootPromotionVo.getSku();
        log.info("Woot 请求库存可售天数,sku:{}", tmpSku);
        McSkuInfo skuInfoVo = skuMap.getOrDefault(tmpSku, null);
        if (null != skuInfoVo && ("Z003".equals(skuInfoVo.getProductTypeCode()) || "Z004".equals(skuInfoVo.getProductTypeCode()))) {
            if (hierarchyMap.containsKey(tmpSku)) {
                log.info("SKU:{} 查询层级关系", tmpSku);
                List<String> tmpList = hierarchyMap.get(tmpSku).stream().map(x -> x.getCurrentSkuCode()).collect(Collectors.toList());
                childSkus.addAll(tmpList);
            }
        }
        //查询基础SKU  封装biBody
        List<MarketActivityUtil.BiBodyVo> biBodyList = new ArrayList<>();
        String fc = somWootPromotionVo.getSite().equals("Woot.com") ? "FBA" : "FBM";
        String cg = somWootPromotionVo.getSite().equals("Woot.com") ? "FBA-US" : "FBM-US";
        if (childSkus.isEmpty()) {
            MarketActivityUtil.BiBodyVo biBodyVo = new MarketActivityUtil.BiBodyVo();
            biBodyVo.setProductMainCode(somWootPromotionVo.getSku());
            biBodyVo.setBasicsProducMainCode(findBasicSku(skuMap, somWootPromotionVo.getSku()));
            biBodyVo.setFulfillmentChannel(fc);
            biBodyVo.setCustomerGroup(cg);
            biBodyList.add(biBodyVo);
        } else {
            for (String childSku : childSkus) {
                MarketActivityUtil.BiBodyVo biBodyVo = new MarketActivityUtil.BiBodyVo();
                biBodyVo.setProductMainCode(childSku);
                biBodyVo.setBasicsProducMainCode(findBasicSku(skuMap, childSku));
                biBodyVo.setFulfillmentChannel(fc);
                biBodyVo.setCustomerGroup(cg);
                biBodyList.add(biBodyVo);
            }
        }

        ResultVo<List<MarketActivityUtil.BiReturnVo>> marketableDaysSom = null;
        try {
            log.info("Woot BI请求库存可售天数,biBodyList:{}", JSONUtil.toJsonStr(biBodyList));
            marketableDaysSom = biService.getMarketableDaysSom(biToken, biBodyList);
        } catch (Exception e) {
            log.error("BI请求库存可售天数异常或超时:{}", e.toString());
            throw new RuntimeException("BI请求库存可售天数异常或超时:" + e);
        }
        if (!marketableDaysSom.isSuccess()) {
            throw new RuntimeException("BI返回库存可售天数结果异常");
        }
        List<MarketActivityUtil.BiReturnVo> biData = marketableDaysSom.getData();
        if (biData.isEmpty()) {
            throw new RuntimeException("BI返回库存可售天数结果为空");
        }
        log.info("Woot BI返回库存可售天数,biData:{}", JSONUtil.toJsonStr(biData));

        biData.stream().filter(x -> ObjectUtil.isNotNull(x.getInventorySalesReport()) && ObjectUtil.isNotNull(x.getInventorySalesReport().getMarketableDays()))
                .min(Comparator.comparingInt(x -> x.getInventorySalesReport().getMarketableDays()))
                .ifPresent(x -> {
                    somWootPromotionVo.setMarketableDays(x.getInventorySalesReport().getMarketableDays());
                    somWootPromotionVo.setOverstockReport(x.getOverstockReport());
                });
    }

    private String findBasicSku(Map<String, McSkuInfo> skuMap, String sku) {
        if (skuMap.containsKey(sku)) {
            McSkuInfo skuInfo = skuMap.get(sku);
            if (null != skuInfo && StrUtil.isNotEmpty(skuInfo.getBasicProductMainCode())) {
                return findBasicSku(skuMap, skuInfo.getBasicProductMainCode());
            }
        }
        return sku;
    }

    public void submit(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws ValidateException {
        //主要操作，把草稿状态的数据变成审批中状态
        if (ObjectUtil.isEmpty(somWootPromotionVo) || ObjectUtil.isEmpty(somWootPromotionVo.getAidList())) {
            throw new ValidateException("请选择要提交的数据");
        }
        //检测状态是否是草稿
        long count = somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).andNotEq("status", 10).count();
        if (count > 0) {
            throw new ValidateException("请检查Woot活动状态，只能提交草稿状态的数据");
        }
        log.info("Woot活动提交,提交人：{},somWootPromotionVo:{}", tokenUser.getJobNumber(), JSONUtil.toJsonStr(somWootPromotionVo));

        List<SomWootPromotion> data = somWootPromotionMapper.queryApproval(somWootPromotionVo.getAidList());
        somWootPromotionMapper.batchUpdateStatus(data);
        wootStatusChangeNotice(data, "状态变更：这批产品的状态由‘草稿’变更为 审批中");
    }

    /**
     * 发送状态变更通知
     *
     * @param lists
     * @param tips
     */
    public void wootStatusChangeNotice(List<SomWootPromotion> lists, String tips) {
        try {
            //按照创建人分组
            Map<String, List<SomWootPromotion>> map = lists.stream().collect(Collectors.groupingBy(e -> e.getCreateNum()));
            for (String createNum : map.keySet()) {
                List<SomWootPromotion> somWootPromotions = map.get(createNum);
                String sellerSkus = somWootPromotions.stream().map(e -> e.getSellerSku()).distinct().collect(Collectors.joining(","));
                String msg = "{\n" +
                        "  \"header\": {\n" +
                        "    \"template\": \"green\",\n" +
                        "    \"title\": {\n" +
                        "      \"tag\": \"plain_text\",\n" +
                        "      \"content\": \"Woot营销活动消息通知\"\n" +
                        "    }\n" +
                        "  },\n" +
                        "  \"elements\": [\n" +
                        "    {\n" +
                        "      \"tag\": \"markdown\",\n" +
                        "      \"content\": \"*Seller sku：%s*\\n**%s**\"\n" +
                        "    }\n" +
                        "  ]\n" +
                        "}";
                log.info("发送飞书通知,通知人:{},sellerSkus:{},msg:{}", createNum, sellerSkus, tips);
                msg = String.format(msg, sellerSkus, tips);
                FeiShuUtils.sendNotice(createNum, msg, MessageType.INTERACTIVE);
            }
        } catch (Exception e) {
            log.error("飞书通知异常:{},通知的list json:{}", e.toString(), JSONUtil.toJsonStr(lists), e);
        }
    }

    public void approval(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws ValidateException {
        //主要操作，把审批中状态的数据变成提报中/审批位通过状态
        if (ObjectUtil.isEmpty(somWootPromotionVo) || ObjectUtil.isEmpty(somWootPromotionVo.getAidList())) {
            throw new ValidateException("请选择要审批的数据");
        }
        if (somWootPromotionVo.getStatus() == null) {
            throw new ValidateException("请选择审批结果");
        }
        List<SomWootPromotion> dbList = somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).select();
        long count = dbList.stream().filter(x -> x.getStatus() != 20).count();
        //检测状态是否是审批中
        if (count > 0) {
            throw new ValidateException("请检查Woot活动状态，只能审批 审批中状态的数据");
        }
        List<String> approvalList = dbList.stream().filter(x -> x.getApprovalNum() != null).map(x -> x.getApprovalNum()).distinct().collect(Collectors.toList());
        if (!approvalList.isEmpty() && (approvalList.size() > 1 || !tokenUser.getJobNumber().equals(approvalList.get(0)))) {
            throw new ValidateException("请选择审批人是自己的数据进行审批");
        }

        log.info("woot审批:提交人：{}，json:{}", tokenUser.getJobNumber(), JSONUtil.toJsonStr(somWootPromotionVo));

        List<SomWootPromotion> lists = somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).select("seller_sku", "create_num");

        SomWootPromotion somWootPromotion = new SomWootPromotion();
        somWootPromotion.setStatus(somWootPromotionVo.getStatus());
        somWootPromotion.setApprovalRemark(somWootPromotionVo.getApprovalRemark());
        somWootPromotion.setApprovalName(tokenUser.getUserName());
        somWootPromotion.setApprovalNum(tokenUser.getJobNumber());
        somWootPromotion.setApprovalTime(DateTime.now().toJdkDate());
        somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).updateSelective(somWootPromotion);

        String statusStr = "";
        if (somWootPromotionVo.getStatus() == 30) {
            statusStr = "审批未通过";
            statusStr = statusStr + "  \\n原因：" + somWootPromotionVo.getApprovalRemark();
        } else if (somWootPromotionVo.getStatus() == 40) {
            statusStr = "提报中";
        } else {
            statusStr = "不合法状态:" + somWootPromotionVo.getStatus();
        }
        String tips = String.format("状态变更：这批产品的状态由‘审批中’变更为 %s", statusStr);
        wootStatusChangeNotice(lists, tips);
    }


    private String getWootSite(Integer vcFlag) {
        if (vcFlag == 1) {
            return "VC-Amazon.com";
        }
        return "Amazon.com";
    }

    public void importExcel(List<SomWootPromotionVo> list, TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        log.info("开始导入Woot活动数据");
        List<String> errorList = new ArrayList<>();
        //保存hashcode 校验数据重复
        Set<Integer> repeatSet = new HashSet<>();
        log.info("查询Woot仓库配置");
        List<SomWootPromotionWarehouseConfig> wootConfigList = wootConfigMapper.all();
        Map<String, List<SomWootPromotionWarehouseConfig>> wootConfigMap = wootConfigList.stream().collect(Collectors.groupingBy(e -> e.getSite() + e.getvcFlag() + e.getFbaStockFlag()));

        String sellerSkus = list.stream().filter(x -> x.getSellerSku() != null).map(SomWootPromotionVo::getSellerSku).distinct().collect(Collectors.joining("','", "'", "'"));
        List<String> sellerSkuList = list.stream().filter(x -> x.getSellerSku() != null).map(SomWootPromotionVo::getSellerSku).distinct().collect(Collectors.toList());
        //查询销售视图状态
        List<McProductSales> salesList = productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andIn("display_product_code", sellerSkuList)
                .andEq("sales_flag", 1)
                .andIsNotNull("asin_code")
                .andIn("site", Arrays.asList("VC-Amazon.com", "Amazon.com")).select();
        Map<String, McProductSales> salesMap = salesList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));

        //仓库库存
        List<McProductSalesStockVo> stockList = somWootPromotionMapper.querySkuStockInfo(sellerSkuList, wootConfigList.stream().map(SomWootPromotionWarehouseConfig::getUseableWarehouseCode).collect(Collectors.toSet()));
        Map<String, List<McProductSalesStockVo>> stockMap = stockList.stream().collect(Collectors.groupingBy(McProductSalesStockVo::getWarehouseCode));

        //查询Woot营销活动表
        List<SomWootPromotion> wootList = somWootPromotionMapper.queryImportWootInfo(sellerSkus);
        Map<String, SomWootPromotion> wootMap = wootList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku(), Function.identity(), (x1, x2) -> x1));


        List<SomDealRuleWhiteList> wootWhiteList = somDealRuleWhiteListMapper.createLambdaQuery().andEq("platform", "Woot").select();
        Map<String, Boolean> wootWhiteMap = wootWhiteList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku(), v -> true, (x1, x2) -> x1));


        //这个for循环做数据校验
        for (SomWootPromotionVo somWootPromotionVo : list) {
            log.info("校验数据是否存在空值");
            if (somWootPromotionVo.getSite() == null || somWootPromotionVo.getSellerSku() == null || somWootPromotionVo.getVcFlag() == null ||
                    somWootPromotionVo.getFbaStockFlag() == null || somWootPromotionVo.getProductName() == null || somWootPromotionVo.getPromotionPrice() == null ||
                    StrUtil.isEmpty(somWootPromotionVo.getDealType())) {
                errorList.add("错误0：站点、展示码、VCFlag、FbaStockFlag、DEAL类型不能为空，不允许导入");
                continue;
            }
            //校验Excel中数据重复
            if (repeatSet.contains(somWootPromotionVo.hashCode())) {
                errorList.add("错误1：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，Excel数据重复，不允许导入");
                continue;
            }
            repeatSet.add(somWootPromotionVo.hashCode());
            //校验数据库中是否存在相同数据  根据“站点” + “展示码” + “活动状态 不等于 提报成功或提报失败或审批未通过”
            if (wootMap.containsKey(somWootPromotionVo.getSite() + somWootPromotionVo.getSellerSku())) {
                errorList.add("错误2：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，数据库中存在活动状态不等于提报成功或提报失败或审批未通过的数据，不允许导入");
            }
            //校验销售视图是否存在
            McProductSales mcProductSales = salesMap.getOrDefault(getWootSite(somWootPromotionVo.getVcFlag()) + somWootPromotionVo.getSellerSku(), null);
            if (mcProductSales == null) {
                errorList.add("错误4：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，销售视图或ASIN不存在 或者 没有对应的库存数据，不允许导入");
                continue;
            }

            if (!wootConfigMap.containsKey(somWootPromotionVo.getSite() + somWootPromotionVo.getVcFlag() + somWootPromotionVo.getFbaStockFlag())) {
                errorList.add("错误3：站点：" + somWootPromotionVo.getSite() + "，VCFlag：" + somWootPromotionVo.getVcFlag() + "，FbaStockFlag：" + somWootPromotionVo.getFbaStockFlag() + "，没有配置Woot仓库配置");
                continue;
            }
            //校验数量是否符合规则
            //展示码状态不是“预淘汰 40”或者“淘汰 50”的情况下，用户输入的销售提报数量必须≤库存数量的80%
            String sellerSkuStatusCode = mcProductSales.getSellerSkuStatusCode();
            int checkTotalStock = 0;
            List<SomWootPromotionWarehouseConfig> configList = wootConfigMap.get(somWootPromotionVo.getSite() + somWootPromotionVo.getVcFlag() + somWootPromotionVo.getFbaStockFlag());
            Map<String, List<SomWootPromotionWarehouseConfig>> configTmpMap = configList.stream().collect(Collectors.groupingBy(SomWootPromotionWarehouseConfig::getUseableWarehouseCode));
            for (String warehouseCode : configTmpMap.keySet()) {
                List<McProductSalesStockVo> stockItemList = stockMap.getOrDefault(warehouseCode, new ArrayList<>());
                List<String> warehouseConfigList = configTmpMap.get(warehouseCode).stream().map(SomWootPromotionWarehouseConfig::getUseableStorageCode).collect(Collectors.toList());
                Integer totalStock = stockItemList.stream().filter(x -> warehouseConfigList.contains(x.getSlCode()))
                        .collect(Collectors.toList())
                        .stream()
                        .map(McProductSalesStockVo::getTotalStock)
                        .reduce(Integer::sum).orElse(0);
                Integer wc6201 = somWootPromotionVo.getWc6201();
                if (wc6201 != null && warehouseCode.equals("6201")) {
                    checkTotalStock = checkTotalStock + wc6201;
                    if (wc6201 > totalStock * 0.8) {
                        errorList.add("错误5：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，用户输入的销售提报数量必须≤美国Amazon仓 库存数量的80%%");
                    }
                }
                Integer wc6101 = somWootPromotionVo.getWc6101();
                if (wc6101 != null && warehouseCode.equals("6101")) {
                    checkTotalStock = checkTotalStock + wc6101;
                    if (wc6101 > totalStock * 0.8) {
                        errorList.add("错误5：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，用户输入的销售提报数量必须≤配置的美西仓 库存数量的80%%");
                    }
                }
                Integer wc6102 = somWootPromotionVo.getWc6102();
                if (wc6102 != null && warehouseCode.equals("6102")) {
                    checkTotalStock = checkTotalStock + wc6102;
                    if (wc6101 > totalStock * 0.8) {
                        errorList.add("错误5：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，用户输入的销售提报数量必须≤配置的美国东北仓 库存数量的80%%");
                    }
                }
            }
            if (checkTotalStock <= 0) {
                errorList.add("错误6：站点：" + somWootPromotionVo.getSite() + "，展示码：" + somWootPromotionVo.getSellerSku() + "，当前仓库配置对应的用户输入提报数量为0，不允许导入");
            }
        }
        if (!errorList.isEmpty()) {
            throw new ValidateException(errorList.stream().collect(Collectors.joining("\n")));
        }

        List<McPlatformProperties> platformProperties = platformPropertiesMapper.createLambdaQuery().andEq("platform", "Woot").select();
        Map<String, String> currencyMap = platformProperties.stream().collect(Collectors.toMap(x -> x.getSite(), v -> v.getCurrencyCode(), (x1, x2) -> x1));

        List<String> asinList = salesList.stream().map(x -> x.getAsinCode()).distinct().collect(Collectors.toList());
        Map<String, McListingInfoAmazon> listPriceMap = new HashMap<>();
        if (!asinList.isEmpty()) {
            List<McListingInfoAmazon> amazonList = infoAmazonMapper.createLambdaQuery()
                    .andIn("asin_code", asinList)
                    .andEq("site", "Amazon.com")
                    .select();
            listPriceMap = amazonList.stream().collect(Collectors.toMap(x -> x.getAsinCode() + x.getFulfillmentChannel(), Function.identity(), (x1, x2) -> x1));
        }

        //查询基础视图
        List<McSkuInfo> skuInfoList = skuInfoMapper.createLambdaQuery().select("sku_code", "product_type_code", "basics_product_main_code");
        Map<String, McSkuInfo> skuMap = skuInfoList.stream().collect(Collectors.toMap(x -> x.getSkuCode(), Function.identity(), (x1, x2) -> x1));
        //查询层级关系
        List<SomProductHierarchy> hierarchyList = hierarchyMapper.createLambdaQuery().select("aid", "upper_sku_code", "current_sku_code");
        Map<String, List<SomProductHierarchy>> hierarchyMap = hierarchyList.stream().collect(Collectors.groupingBy(e -> e.getUpperSkuCode()));
        //获取编码对应的名字 防止突然改名
        List<McWarehouse> warehouseCode = mcWarehouseMapper.createLambdaQuery().andIn("warehouse_code", Arrays.asList("6101", "6102", "6201")).select();
        Map<String, String> warehouseMap = warehouseCode.stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), v -> v.getWarehouseName(), (x1, x2) -> x1));

        List<SomWootPromotion> insertList = new ArrayList<>();
        List<String> errorList2 = new ArrayList<>();
        for (SomWootPromotionVo wootVo : list) {
            McProductSales sales = salesMap.get(getWootSite(wootVo.getVcFlag()) + wootVo.getSellerSku());
            SomWootPromotion wootPromotion = new SomWootPromotion();
            wootPromotion.setAid(IdUtil.fastSimpleUUID());
            wootPromotion.setSite(wootVo.getSite());
            wootPromotion.setSellerSku(wootVo.getSellerSku());
            wootPromotion.setSku(sales.getProductMainCode());
            wootPromotion.setProductName(wootVo.getProductName());
            wootPromotion.setvcFlag(wootVo.getVcFlag());
            wootPromotion.setFbaStockFlag(wootVo.getFbaStockFlag());
            //标价/市场价  Amazon->FBA  Merchant->FBM
            McListingInfoAmazon listPrice = listPriceMap.getOrDefault(sales.getAsinCode() + (wootPromotion.getFbaStockFlag() == 1 ? "Amazon" : "Merchant"), new McListingInfoAmazon());
            wootPromotion.setListPrice(listPrice.getPrice() == null ? BigDecimal.ZERO : listPrice.getPrice());
            wootPromotion.setPromotionPrice(wootVo.getPromotionPrice());
            //促销 和 可售库存 数量
            List<SomWootPromotionVo.PromotionQuantity> quantityMap = promotionQuantity(sales.getDisplayProductCode(), wootVo, warehouseMap, stockMap, wootConfigMap);
            wootPromotion.setPromotionQuantity(objectMapper.writeValueAsString(quantityMap));
            //到仓价  因为同步sap价格数据的逻辑是每月同步上个月的数据，所以一直都是最新的
            BigDecimal sapPrice = null;
            if (wootVo.getSite().equals("Woot.com")) {
                //取最新年度 最新会计区间的 美国Amazon仓 的到仓价
                sapPrice = somWootPromotionMapper.getComSapPrice(sales.getProductMainCode());
            } else {
                //Woot.dropship站点   取最新年度 最新会计区间的 美西仓和美国东北仓的到仓价的最大值
                sapPrice = somWootPromotionMapper.getDropshipSapPrice(sales.getProductMainCode());
            }
            wootPromotion.setSapPrice(sapPrice == null ? BigDecimal.ZERO : sapPrice);
            if (wootVo.getVcFlag() == 1) {
                //vc标识 市场价取到仓价
                wootPromotion.setListPrice(wootPromotion.getSapPrice());
            }
            //设置币种
            wootPromotion.setCurrency(currencyMap.get(wootVo.getSite()));
            wootPromotion.setStatus(10);

            if (StrUtil.isBlank(sales.getAsinCode())) {
                errorList2.add("错误8：站点：" + wootVo.getSite() + "，展示码：" + wootVo.getSellerSku() + "，ASIN为空，不允许导入此活动");
            }
            //未来12周的可销售天数
            SomWootPromotionVo somWootPromotionVo = ConvertUtils.beanConvert(wootPromotion, SomWootPromotionVo.class);
            marketableDays(skuMap, hierarchyMap, somWootPromotionVo);
            if (somWootPromotionVo.getOverstockReport() != null) {
                //添加校验  未来12周可销售天数中如果有任意两周的可销售天数小于等于21天的情况下，不允许新增/导入此活动。
                long count = somWootPromotionVo.getOverstockReport().stream().filter(x -> x.getMarketableDays() <= 21).count();
                boolean isWhite = wootWhiteMap.containsKey(wootVo.getSite() + wootVo.getSellerSku());
                if (count >= 2 && !(wootVo.getVcFlag() == 1) && !isWhite) {
                    log.info("站点:{}，展示码:{}，未来12周可销售天数中有两周的可销售天数小于等于21天，不允许导入此活动", wootVo.getSite(), wootVo.getSellerSku());
                    errorList2.add("错误7：站点：" + wootVo.getSite() + "，展示码：" + wootVo.getSellerSku() + "，未来12周可销售天数中有两周的可销售天数小于等于21天，不允许导入此活动");
                    continue;
                }
                wootPromotion.setSalesDaysInTheLast12Weeks(objectMapper.writeValueAsString(somWootPromotionVo.getOverstockReport()));
            }
            //可销售天数
            wootPromotion.setMarketableDays(somWootPromotionVo.getMarketableDays());
            //近四周毛利率
            SomKpiGrossProfit kpiGrossProfit = kopGrossProfitMapper.createLambdaQuery().andEq("site", "Amazon.com").andEq("seller_sku", somWootPromotionVo.getSellerSku()).single();
            if (kpiGrossProfit != null) {
                wootPromotion.setGrossProfitRate30Day(kpiGrossProfit.getOneMonthGrossProfitRate());
            }
            wootPromotion.setCreateName(tokenUser.getUserName());
            wootPromotion.setCreateNum(tokenUser.getJobNumber());
            wootPromotion.setCreateTime(DateTime.now().toJdkDate());
            wootPromotion.setAsin(sales.getAsinCode());
            wootPromotion.setFnSku(sales.getFnskuCode());
            wootPromotion.setDealType(wootVo.getDealType());
            insertList.add(wootPromotion);
        }
        if (!errorList2.isEmpty()) {
            throw new ValidateException(String.join("\n", errorList2));
        }
        if (!insertList.isEmpty()) {
            somWootPromotionMapper.insertBatch(insertList);
        }
    }

    private List<SomWootPromotionVo.PromotionQuantity> promotionQuantity(String sellerSku, SomWootPromotionVo wootVo, Map<String, String> warehouseMap, Map<String, List<McProductSalesStockVo>> stockMap, Map<String, List<SomWootPromotionWarehouseConfig>> wootConfigMap) throws ValidateException {
        List<SomWootPromotionVo.PromotionQuantity> quantityList = new ArrayList<>();
        log.info("获取Woot仓库配置");
        List<SomWootPromotionWarehouseConfig> configList = wootConfigMap.getOrDefault(wootVo.getSite() + wootVo.getVcFlag() + wootVo.getFbaStockFlag(), new ArrayList<>());
        Map<String, List<SomWootPromotionWarehouseConfig>> configTmpMap = configList.stream().collect(Collectors.groupingBy(SomWootPromotionWarehouseConfig::getUseableWarehouseCode));
        for (String warehouseCode : configTmpMap.keySet()) {
            SomWootPromotionVo.PromotionQuantity quantity = new SomWootPromotionVo.PromotionQuantity();
            quantity.setWarehouseName(warehouseMap.get(warehouseCode));
            quantity.setWarehouseCode(warehouseCode);

            List<String> warehouseConfigList = configTmpMap.get(warehouseCode).stream().map(SomWootPromotionWarehouseConfig::getUseableStorageCode).collect(Collectors.toList());
            List<McProductSalesStockVo> stockList = stockMap.getOrDefault(warehouseCode, new ArrayList<>());
            Integer totalStock = stockList.stream().filter(x -> warehouseConfigList.contains(x.getSlCode()))
                    .collect(Collectors.toList())
                    .stream()
                    .map(McProductSalesStockVo::getTotalStock)
                    .reduce(Integer::sum).orElse(0);

            quantity.setQuantity(0);
            quantity.setAvailable(totalStock);
            Integer wc6201 = wootVo.getWc6201();
            if (wc6201 != null && warehouseCode.equals("6201")) {
                quantity.setQuantity(wc6201);
            }
            Integer wc6101 = wootVo.getWc6201();
            if (wc6101 != null && warehouseCode.equals("6101")) {
                quantity.setQuantity(wc6101);
            }
            Integer wc6102 = wootVo.getWc6201();
            if (wc6102 != null && warehouseCode.equals("6102")) {
                quantity.setQuantity(wc6102);
            }
            quantityList.add(quantity);
        }
        return quantityList;
    }

    public List<String> sellerSkus(SomWootPromotionVo somWootPromotionVo) throws ValidateException {
        if (somWootPromotionVo == null || StrUtil.isEmpty(somWootPromotionVo.getSite())) {
            throw new ValidateException("site不能为空");
        }
        return productSalesMapper.createLambdaQuery()
                .andEq("site", somWootPromotionVo.getSite())
                .andEq("is_enabled", 1)
                .select("display_product_code")
                .stream()
                .map(McProductSales::getDisplayProductCode)
                .collect(Collectors.toList());
    }

    public SomWootPromotionVo quantityDetail(SomWootPromotionVo woot) throws Exception {
        if (woot == null || StrUtil.isEmpty(woot.getAid())) {
            throw new ValidateException("数据不能为空");
        }
        SomWootPromotion wootPromotion = somWootPromotionMapper.single(woot.getAid());
        if (wootPromotion == null || wootPromotion.getPromotionQuantity() == null) {
            log.error("aid:{},库存数据PromotionQuantity 不存在", woot.getAid());
            throw new ValidateException("根据Aid查询 库存数据PromotionQuantity 为空");
        }
        List<SomWootPromotionVo.PromotionQuantity> promotionQuantities = objectMapper.readValue(wootPromotion.getPromotionQuantity(), new TypeReference<List<SomWootPromotionVo.PromotionQuantity>>() {
        });
        List<SomWootPromotionVo.PromotionQuantity> sortList = promotionQuantities.stream().sorted(Comparator.comparingInt(SomWootPromotionVo.PromotionQuantity::getQuantity).reversed()).collect(Collectors.toList());
        woot.setWarehouseStockList(sortList);
        woot.setTotalRealQuantity(0);

        if (wootPromotion.getPurchaseOrder() != null) {

            //查询 woot item 表中的实际提报数量
            SomWootPurchaseOrderItem itemOrder = wootItemMapper.createLambdaQuery()
                    .andEq("purchase_order", wootPromotion.getPurchaseOrder())
                    .andEq("seller_sku", wootPromotion.getSellerSku())
                    .single();
            if (itemOrder == null) {
                log.error("woot item 表中没有找到对应的数据:PO:{},SellSku:{}", wootPromotion.getPurchaseOrder(), wootPromotion.getSellerSku());
                return woot;
            }
            woot.setTotalRealQuantity(itemOrder.getSalesVolume() == null ? 0 : itemOrder.getSalesVolume());
            //计算总的用户提报数量
            int totalQuantity = sortList.stream().mapToInt(SomWootPromotionVo.PromotionQuantity::getQuantity).reduce(Integer::sum).orElse(0);
            int lastQuantity = woot.getTotalRealQuantity();
            for (SomWootPromotionVo.PromotionQuantity vo : woot.getWarehouseStockList()) {
                //按照用户提报数量占比计算实际提报数量
                vo.setRealQuantity(getRealQuantity(vo.getQuantity(), totalQuantity, woot.getTotalRealQuantity(), lastQuantity));
            }
        }
        return woot;
    }

    /**
     * @param quantity          用户输入提报的数量
     * @param totalQuantity     用户输入提报数量 汇总
     * @param totalRealQuantity 总的实际提报数量
     * @param lastQuantity      剩余的总的实际提报数量
     * @return
     */
    private int getRealQuantity(int quantity, int totalQuantity, int totalRealQuantity, int lastQuantity) {
        if (totalQuantity == 0 || totalRealQuantity == 0 || quantity == 0 || lastQuantity <= 0) {
            //lastQuantity 应该不会小于0
            if (lastQuantity < 0) {
                log.error("lastQuantity 小于0:{}", lastQuantity);
            }
            return 0;
        }
        //转成BigDecimal 防止精度丢失
        BigDecimal totalQuantityBd = BigDecimal.valueOf(totalQuantity);
        BigDecimal totalRealQuantityBd = BigDecimal.valueOf(totalRealQuantity);
        BigDecimal quantityBd = BigDecimal.valueOf(quantity);

        BigDecimal result = quantityBd.multiply(totalRealQuantityBd).divide(totalQuantityBd, 2, RoundingMode.HALF_UP);
        if (lastQuantity - result.intValue() < 0) {
            result = BigDecimal.valueOf(lastQuantity);
            lastQuantity = 0;
            return result.intValue();
        }
        lastQuantity = lastQuantity - result.intValue();
        return result.intValue();
    }

    public SomWootPromotionVo look(SomWootPromotionVo woot) throws Exception {
        if (woot == null || StrUtil.isEmpty(woot.getAid())) {
            throw new ValidateException("数据不能为空");
        }
        SomWootPromotion wootPromotion = somWootPromotionMapper.single(woot.getAid());
        if (wootPromotion == null || wootPromotion.getPromotionQuantity() == null) {
            log.error("查看接口 aid:{},根据Aid查询数据为空", woot.getAid());
            throw new ValidateException("根据Aid查询数据为空");
        }
        SomWootPromotionVo somWootPromotionVo = ConvertUtils.beanConvert(wootPromotion, SomWootPromotionVo.class);
        //设置展示码状态
        McProductSales sales = productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("display_product_code", somWootPromotionVo.getSellerSku())
                .andEq("site", somWootPromotionVo.getSite()).single();
        //设置sku asin 等属性
        List<McDictionaryInfo> dictionaryInfoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "SalesProductStatus").select();
        Map<String, String> dictMap = dictionaryInfoList.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
        somWootPromotionVo.setSellerSkuStatus(dictMap.getOrDefault(sales.getSellerSkuStatusCode(), null));
        somWootPromotionVo.setWarehouseStockList(objectMapper.readValue(wootPromotion.getPromotionQuantity(), new TypeReference<List<SomWootPromotionVo.PromotionQuantity>>() {
        }));
        if (somWootPromotionVo.getSalesDaysInTheLast12Weeks() != null) {
            List<MarketActivityUtil.OverstockReport> overstockReports = objectMapper.readValue(somWootPromotionVo.getSalesDaysInTheLast12Weeks(), new TypeReference<List<MarketActivityUtil.OverstockReport>>() {
            });
            overstockReports.stream().min(Comparator.comparingInt(MarketActivityUtil.OverstockReport::getMarketableDays)).ifPresent(x -> somWootPromotionVo.setOverstockReportDays(x.getMarketableDays()));
        } else {
            somWootPromotionVo.setOverstockReportDays(0);
        }
        return somWootPromotionVo;
    }

    public void fail(SomWootPromotionVo somWootPromotionVo, TokenUserInfo tokenUser) throws ValidateException {
        //主要操作，把提报中的状态改为提报失败  可选填备注  非必填
        if (ObjectUtil.isEmpty(somWootPromotionVo) || ObjectUtil.isEmpty(somWootPromotionVo.getAidList())) {
            throw new ValidateException("请选择要标记提报失败的数据");
        }
        //检测状态是否是审批中
        long count = somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).andNotEq("status", 40).count();
        if (count > 0) {
            throw new ValidateException("请检查Woot活动状态，只能标记 提报中状态的数据");
        }
        log.info("开始标记提报失败:提交人：{}，json:{}", tokenUser.getJobNumber(), JSONUtil.toJsonStr(somWootPromotionVo));

        SomWootPromotion somWootPromotion = new SomWootPromotion();
        //99 代表提报失败
        somWootPromotion.setStatus(99);
        somWootPromotion.setApprovalRemark(somWootPromotionVo.getApprovalRemark());
        somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).updateSelective(somWootPromotion);

        List<SomWootPromotion> lists = somWootPromotionMapper.createLambdaQuery().andIn("aid", somWootPromotionVo.getAidList()).select("seller_sku", "create_num");
        String tips = "状态变更：这批产品的状态由‘提报中’变更为 提报失败  \\n原因：" + somWootPromotionVo.getApprovalRemark();
        wootStatusChangeNotice(lists, tips);
    }
}
