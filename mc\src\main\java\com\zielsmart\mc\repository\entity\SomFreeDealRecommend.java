package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * LD&7DD推荐数据表
 * gen by 代码生成器 2022-10-25
 */

@Table(name = "mc.som_free_deal_recommend")
public class SomFreeDealRecommend implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 国家
     */
    @Column("country")
    private String country;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 下载时间
     */
    @Column("update_time")
    private Date updateTime;
    /**
     * 品名
     */
    @Column("product_name")
    private String productName;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * Listing链接
     */
    @Column("platform_link")
    private String platformLink;
    /**
     * 秒杀类型
     */
    @Column("deal_type")
    private String dealType;
    /**
     * 秒杀起始日期
     */
    @Column("deal_start")
    private Date dealStart;
    /**
     * 秒杀截止日期
     */
    @Column("deal_end")
    private Date dealEnd;
    /**
     * 秒杀活动节日
     */
    @Column("deal_festival")
    private String dealFestival;
    /**
     * 推荐的秒杀价格
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * 现售价
     */
    @Column("your_price")
    private BigDecimal yourPrice;
    /**
     * 未参加活动前的数量
     */
    @Column("discount_per_unit")
    private Integer discountPerUnit;
    /**
     * 最少秒杀数量
     */
    @Column("min_qty")
    private Integer minQty;
    /**
     * 秒杀费用
     */
    @Column("deal_fee")
    private BigDecimal dealFee;

    public SomFreeDealRecommend() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 国家
     *
     * @return
     */
    public String getCountry() {
        return country;
    }

    /**
     * 国家
     *
     * @param country
     */
    public void setCountry(String country) {
        this.country = country;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 下载时间
     *
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 品名
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 品名
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * Listing链接
     *
     * @return
     */
    public String getPlatformLink() {
        return platformLink;
    }

    /**
     * Listing链接
     *
     * @param platformLink
     */
    public void setPlatformLink(String platformLink) {
        this.platformLink = platformLink;
    }

    /**
     * 秒杀类型
     *
     * @return
     */
    public String getDealType() {
        return dealType;
    }

    /**
     * 秒杀类型
     *
     * @param dealType
     */
    public void setDealType(String dealType) {
        this.dealType = dealType;
    }

    /**
     * 秒杀起始日期
     *
     * @return
     */
    public Date getDealStart() {
        return dealStart;
    }

    /**
     * 秒杀起始日期
     *
     * @param dealStart
     */
    public void setDealStart(Date dealStart) {
        this.dealStart = dealStart;
    }

    /**
     * 秒杀截止日期
     *
     * @return
     */
    public Date getDealEnd() {
        return dealEnd;
    }

    /**
     * 秒杀截止日期
     *
     * @param dealEnd
     */
    public void setDealEnd(Date dealEnd) {
        this.dealEnd = dealEnd;
    }

    /**
     * 秒杀活动节日
     *
     * @return
     */
    public String getDealFestival() {
        return dealFestival;
    }

    /**
     * 秒杀活动节日
     *
     * @param dealFestival
     */
    public void setDealFestival(String dealFestival) {
        this.dealFestival = dealFestival;
    }

    /**
     * 推荐的秒杀价格
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 推荐的秒杀价格
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * 现售价
     *
     * @return
     */
    public BigDecimal getYourPrice() {
        return yourPrice;
    }

    /**
     * 现售价
     *
     * @param yourPrice
     */
    public void setYourPrice(BigDecimal yourPrice) {
        this.yourPrice = yourPrice;
    }

    /**
     * 未参加活动前的数量
     *
     * @return
     */
    public Integer getDiscountPerUnit() {
        return discountPerUnit;
    }

    /**
     * 未参加活动前的数量
     *
     * @param discountPerUnit
     */
    public void setDiscountPerUnit(Integer discountPerUnit) {
        this.discountPerUnit = discountPerUnit;
    }

    /**
     * 最少秒杀数量
     *
     * @return
     */
    public Integer getMinQty() {
        return minQty;
    }

    /**
     * 最少秒杀数量
     *
     * @param minQty
     */
    public void setMinQty(Integer minQty) {
        this.minQty = minQty;
    }

    /**
     * 秒杀费用
     *
     * @return
     */
    public BigDecimal getDealFee() {
        return dealFee;
    }

    /**
     * 秒杀费用
     *
     * @param dealFee
     */
    public void setDealFee(BigDecimal dealFee) {
        this.dealFee = dealFee;
    }
}
