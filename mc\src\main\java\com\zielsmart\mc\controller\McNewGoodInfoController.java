package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McNewGoodInfoService;
import com.zielsmart.mc.vo.McNewGoodInfoExVo;
import com.zielsmart.mc.vo.McNewGoodInfoMarkVo;
import com.zielsmart.mc.vo.McNewGoodInfoPageSearchVo;
import com.zielsmart.mc.vo.McNewGoodInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McNewGoodInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcNewGoodInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "新品上货表管理新品上货表管理")
public class McNewGoodInfoController extends BasicController{

    @Resource
    McNewGoodInfoService mcNewGoodInfoService;

    /**
     * save
     * 新增
     *
     * @param McNewGoodInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增")
    @PostMapping(value = "/add")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody @Validated McNewGoodInfoVo McNewGoodInfoVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcNewGoodInfoService.add(McNewGoodInfoVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "根据SKU查询站点信息")
    @PostMapping(value = "/sku-info")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McNewGoodInfoVo>> skuInfo(@RequestBody @Validated McNewGoodInfoVo McNewGoodInfoVo) throws ValidateException {
        return ResultVo.ofSuccess(mcNewGoodInfoService.skuInfo(McNewGoodInfoVo));
    }

    /**
     * queryByPage
     * 查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McNewGoodInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McNewGoodInfoVo>> queryByPage(@RequestBody McNewGoodInfoPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcNewGoodInfoService.queryByPage(searchVo));
    }

    /**
     * exportNewGoodInfo
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/exportNewGoodInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportNewGoodInfo(@RequestBody McNewGoodInfoPageSearchVo searchVo) {
        String data = mcNewGoodInfoService.exportNewGoodInfo(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * update
     * 简单上货/详细上货
     *
     * @param McNewGoodInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody McNewGoodInfoVo McNewGoodInfoVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcNewGoodInfoService.edit(McNewGoodInfoVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param exVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McNewGoodInfoExVo exVo) throws ValidateException {
        mcNewGoodInfoService.delete(exVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     * 批量修改
     *
     * @param exVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改  上货中  完成上货")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McNewGoodInfoExVo exVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcNewGoodInfoService.update(exVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     * 标记
     *
     * @param markVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记")
    @PostMapping(value = "/mark")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> mark(@RequestBody McNewGoodInfoMarkVo markVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcNewGoodInfoService.mark(markVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "同步产品销售视图")
    @PostMapping(value = "/sync-sale",consumes = "application/json;charset=UTF-8")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json;charset=UTF-8"))
    public ResultVo<Object> syncSale(@RequestBody McNewGoodInfoVo McNewGoodInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        return mcNewGoodInfoService.syncSale(McNewGoodInfoVo,tokenUser);
    }
}
