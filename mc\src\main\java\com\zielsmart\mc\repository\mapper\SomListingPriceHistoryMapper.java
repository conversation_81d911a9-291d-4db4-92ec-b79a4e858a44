package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomListingPrice;
import com.zielsmart.mc.repository.entity.SomListingPriceHistory;
import com.zielsmart.mc.vo.SomListingPriceExtVo;
import com.zielsmart.mc.vo.SomListingPriceHistoryPageSearchVo;
import com.zielsmart.mc.vo.SomListingPriceHistoryVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-05-25
 */

@SqlResource("somListingPriceHistory")
public interface SomListingPriceHistoryMapper extends BaseMapper<SomListingPriceHistory> {

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomListingPriceHistoryVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomListingPriceHistoryVo> queryByPage(@Param("searchVo")SomListingPriceHistoryPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * updateFailStatus
     * 批量更新历史表审核中数据
     * @param updateVoList
     * <AUTHOR>
     * @history
     */
    default void updateStatus(@Param("updateList") List<SomListingPriceExtVo> updateVoList) {
        this.getSQLManager().updateBatch(SqlId.of("somListingPriceHistory.updatStatus"), updateVoList);
    }

    /**
     * updateFailStatus
     * 批量更新历史表审核中数据
     * @param updateVoList
     * <AUTHOR>
     * @history
     */
    default void updateFailStatus(@Param("updateList") List<SomListingPriceHistory> updateVoList) {
        this.getSQLManager().updateBatch(SqlId.of("somListingPriceHistory.updateFailStatus"), updateVoList);
    }
}
