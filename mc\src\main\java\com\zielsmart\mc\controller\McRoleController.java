package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.RoleService;
import com.zielsmart.mc.vo.role.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McRoleController
 * @description
 * @date 2021-07-16 16:11:21
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/role", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台站点角色")
public class McRoleController extends BasicController {
    @Resource
    private RoleService roleService;


    /**
     * queryByPage
     * 角色分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo< McRoleVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "角色分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McRoleVo>> queryByPage(@RequestBody McRolePageSearchVo searchVo) {
        return ResultVo.ofSuccess(roleService.queryByPage(searchVo));
    }


    /**
     * saveOrUpdate
     * 添加角色
     * @param mcRoleVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link RuntimeException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加角色")
    @PostMapping(value = "/save-or-update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveOrUpdate(@RequestBody @Validated McRoleVo mcRoleVo) throws ValidateException {
        roleService.saveOrUpdate(mcRoleVo);
        return ResultVo.ofSuccess(null);
    }


    /**
     * delete
     * 删除角色
     * @param aidList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除角色")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McRoleVo mcRoleVo) throws ValidationException {
        roleService.delete(mcRoleVo);
        return ResultVo.ofSuccess(null);
    }


    /**
     * addUserSite
     * 添加角色的用户和站点
     * @param mcUserRoleVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link RuntimeException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加角色的用户和站点")
    @PostMapping(value = "/add-user-site")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addUserSite(@RequestBody RoleUserSiteAddVo roleUserSiteAddVo) throws RuntimeException {
        roleService.addUserSite(roleUserSiteAddVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * deleteUser
     * 删除角色下的用户
     * @param aidList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除角色下的用户")
    @PostMapping(value = "/delete-user")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> deleteUser(@RequestBody McUserRoleVo mcUserRoleVo) throws ValidationException {
        roleService.deleteUser(mcUserRoleVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * deleteSite
     * 删除角色下的站点
     * @param mcRoleResourceVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除角色下的站点")
    @PostMapping(value = "/delete-site")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> deleteSite(@RequestBody McRoleResourceVo mcRoleResourceVo) throws ValidationException {
        roleService.deleteSite(mcRoleResourceVo);
        return ResultVo.ofSuccess(null);
    }

}
