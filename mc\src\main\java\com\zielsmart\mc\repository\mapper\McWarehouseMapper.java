package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.vo.McWarehousePageSearchVo;
import com.zielsmart.mc.vo.McWarehouseVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2021-08-11
 */

@SqlResource("mcWarehouse")
public interface McWarehouseMapper extends BaseMapper<McWarehouse> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McVcWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McWarehouseVo> queryByPage(@Param("searchVo") McWarehousePageSearchVo searchVo, PageRequest pageRequest);
}
