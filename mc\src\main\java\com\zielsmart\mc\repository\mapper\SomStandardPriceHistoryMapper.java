package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomStandardPriceHistory;
import com.zielsmart.mc.vo.SomStandardPriceHistoryPageSearchVo;
import com.zielsmart.mc.vo.SomStandardPriceHistoryVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2025-05-07
 */

@SqlResource("somStandardPriceHistory")
public interface SomStandardPriceHistoryMapper extends BaseMapper<SomStandardPriceHistory> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomStandardPriceHistoryVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomStandardPriceHistoryVo> queryByPage(@Param("searchVo") SomStandardPriceHistoryPageSearchVo searchVo, PageRequest pageRequest);

    List<SomStandardPriceHistoryVo> exportExcel(@Param("searchVo") SomStandardPriceHistoryPageSearchVo searchVo);
}
