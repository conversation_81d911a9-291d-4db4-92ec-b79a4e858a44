package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPromotion;
import com.zielsmart.mc.vo.*;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-06-21
 */

@SqlResource("somPromotion")
public interface SomPromotionMapper extends BaseMapper<SomPromotion> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPromotionVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPromotionExtVo> queryByPage(@Param("searchVo") SomPromotionPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryPurchasedItems
     * 获取层级促销折扣
     * 查询Promotion折扣时，不查询状态是 提报失败 草稿 审批未通过的数据
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    String queryPurchasedItems(@Param("searchVo")SomPromotionSearchVo searchVo);

    /**
     * queryAdditionalItem
     * 获取组合折扣
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomPromotionAdditionalItemVo>}
     * <AUTHOR>
     * @history
     */
    SomPromotionAdditionalItemVo queryAdditionalItem(@Param("searchVo")SomPromotionSearchVo searchVo);

    /**
     * checkCouponAndDeal
     * 校验营销活动
     * @param validateVo
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @history
     */
    SomPromotionAdditionalItemVo checkPromotion(@Param("validateVo") ValidateDealVo validateVo);
}
