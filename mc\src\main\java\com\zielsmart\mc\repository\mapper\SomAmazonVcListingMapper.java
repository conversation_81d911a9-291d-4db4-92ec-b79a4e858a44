package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.repository.entity.SomAmazonVcListing;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2024-01-23
*/

@SqlResource("somAmazonVcListing")
public interface SomAmazonVcListingMapper extends BaseMapper<SomAmazonVcListing> {

    /**
     * 查询VC可售库存报表
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomVcSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomVcSaleInventoryReportVo> queryVcInventoryReport(@Param("searchVo") SomVcSaleInventoryReportSearchVo searchVo, PageRequest pageRequest);
}
