package com.zielsmart.mc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Temu跨境（香港半托）营销活动明细 sku_items
 */
@Data
@Schema(title = "Temu跨境（香港半托）营销活动明细 skc_items 信息", name = "SomTemuCrossPromotionDetailVo")
public class SomTemuCrossPromotionDetailVo implements java.io.Serializable {

    @Schema(description = "site", name = "site")
    private String site;

    @Schema(description = "skcId", name = "skcId")
    private String skcId;

    @Schema(description = "skuId", name = "skuId")
    private String skuId;

    @Schema(description = "展示码", name = "sellerSku")
    private String sellerSku;

    @Schema(description = "日常供货价", name = "dailyPrice")
    private BigDecimal dailyPrice;

    @Schema(description = "活动供货价", name = "activityPrice")
    private BigDecimal activityPrice;

}
