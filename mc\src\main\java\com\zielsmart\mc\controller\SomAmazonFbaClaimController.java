package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonFbaClaimService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.repository.query.Param;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @version 2.0
 * @author: 王帅杰
 * @package: com.zielsmart.mc.controller.SomAmazonFbaClaimController
 * @title: SomAmazonFbaClaimController
 * @description: 亚马逊FBA索赔管理
 * @date: 2023-03-16 04:16:23
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/amazon-fba", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "亚马逊FBA索赔管理")
public class SomAmazonFbaClaimController extends BasicController {

    @Resource
    private SomAmazonFbaClaimService somAmazonFbaClaimService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<AmazonFbaClaimVo>> queryByPage(@RequestBody SomAmazonFbaSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonFbaClaimService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonFbaSearchVo searchVo) {
        String data = somAmazonFbaClaimService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "获取重测明细")
    @PostMapping(value = "/resurvey-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomAmazonFbaResurveyVo>> resurveyDetail(@RequestBody SomAmazonFbaSearchVo searchVo){
        return ResultVo.ofSuccess(somAmazonFbaClaimService.resurveyDetail(searchVo));
    }

    @Operation(summary = "获取索赔明细")
    @PostMapping(value = "/claim-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomAmazonFbaClaimVo>> claimDetail(@RequestBody SomAmazonFbaSearchVo searchVo){
        return ResultVo.ofSuccess(somAmazonFbaClaimService.claimDetail(searchVo));
    }

    @Operation(summary = "检测重测数据")
    @PostMapping(value = "/check-resurvey")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> checkResurvey(@RequestBody AmazonFbaResurveyVo data){
        return somAmazonFbaClaimService.checkResurvey(data);
    }

    @Operation(summary = "保存重测数据")
    @PostMapping(value = "/save-resurvey")
    @ResponseBody
    public ResultVo<String> saveResurvey(@RequestBody AmazonFbaResurveyVo resurvey,@Parameter(hidden = true) @TokenUser TokenUserInfo user){
        somAmazonFbaClaimService.saveResurvey(resurvey,user);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "检测索赔数据")
    @PostMapping(value = "/check-claim")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> checkClaim(@RequestBody AmazonFbaResurveyVo data){
        return somAmazonFbaClaimService.checkClaim(data);
    }

    @Operation(summary = "保存索赔数据")
    @PostMapping(value = "/save-claim")
    @ResponseBody
    public ResultVo<String> saveClaim(@RequestBody AmazonFbaResurveyVo resurvey,@Parameter(hidden = true) @TokenUser TokenUserInfo user){
        somAmazonFbaClaimService.saveClaim(resurvey,user);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "更新重测数据")
    @PostMapping(value = "/update-resurvey")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> updateResurvey(@RequestBody SomAmazonFbaResurveyVo resurveyVo,@Parameter(hidden = true) @TokenUser TokenUserInfo user){
        somAmazonFbaClaimService.updateResurvey(resurveyVo,user);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "更新索赔数据")
    @PostMapping(value = "/update-claim")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> updateClaim(@RequestBody SomAmazonFbaClaimVo claimVo,@Parameter(hidden = true) @TokenUser TokenUserInfo user){
        somAmazonFbaClaimService.updateClaim(claimVo,user);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导入索赔模板")
    @PostMapping(value = "/import-claim", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importClaim(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码","CASE ID","索赔金额","索赔结果","备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomAmazonFbaClaimVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonFbaClaimVo.class,importParams);
        } catch (Exception e) {
            if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                throw new ValidateException("导入模板有误,请检查模板");
            }
            throw new RuntimeException(e.getMessage());
        }
        List<SomAmazonFbaClaimVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonFbaClaimService.importClaim(list, tokenUser);
        return ResultVo.ofSuccess();
    }


    @Operation(summary = "导入重测模板")
    @PostMapping(value = "/import-resurvey", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importResurvey(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码","CASE ID","重测长边","重测中边","重测短边","重测重量","重测结果","备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomAmazonFbaResurveyVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonFbaResurveyVo.class,importParams);
        } catch (Exception e) {
            if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                throw new ValidateException("导入模板有误,请检查模板");
            }
            throw new RuntimeException(e.getMessage());
        }
        List<SomAmazonFbaResurveyVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonFbaClaimService.importResurvey(list, tokenUser);
        return ResultVo.ofSuccess();
    }



}
