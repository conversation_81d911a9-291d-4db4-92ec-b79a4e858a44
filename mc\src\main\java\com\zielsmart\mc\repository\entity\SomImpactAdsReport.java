package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-05-12
*/

@Table(name="mc.som_impact_ads_report")
public class SomImpactAdsReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 广告日期
	 */
	@Column("day")
	private Date day ;
	/**
	 * 参与的媒体（合伙人）数量
	 */
	@Column("media_count")
	private Integer mediaCount ;
	/**
	 * 点击次数
	 */
	@Column("clicks")
	private Integer clicks ;
	/**
	 * 跟踪的行动总数，不包括撤销
	 */
	@Column("actions")
	private Integer actions ;
	/**
	 * 所有行为的总收入(销售金额)
	 */
	@Column("revenue")
	private BigDecimal revenue ;
	/**
	 * 总诉讼费用和其他(非诉讼)费用
	 */
	@Column("total_cost")
	private BigDecimal totalCost ;
	/**
	 * 原始浏览次数
	 */
	@Column("raw_impressions")
	private Integer rawImpressions ;
	/**
	 * 浏览次数
	 */
	@Column("impressions")
	private Integer impressions ;
	/**
	 * 总的点击次数
	 */
	@Column("total_clicks")
	private Integer totalClicks ;
	/**
	 * 相反的行为次数
	 */
	@Column("reversed_actions")
	private Integer reversedActions ;
	/**
	 * 确认的数量
	 */
	@Column("actions_confirmed")
	private Integer actionsConfirmed ;
	/**
	 * 打开的数量
	 */
	@Column("actions_open")
	private Integer actionsOpen ;
	/**
	 * Grossactions
	 */
	@Column("gross_actions")
	private Integer grossActions ;
	/**
	 * ReversedRevenue
	 */
	@Column("reversed_revenue")
	private Long reversedRevenue ;
	/**
	 * Grossrevenue
	 */
	@Column("gross_revenue")
	private Long grossRevenue ;
	/**
	 * ActionCost
	 */
	@Column("acction_cost")
	private BigDecimal acctionCost ;
	/**
	 * ReversedActionCost
	 */
	@Column("reversed_action_cost")
	private BigDecimal reversedActionCost ;
	/**
	 * Grossactioncost
	 */
	@Column("gross_action_cost")
	private BigDecimal grossActionCost ;
	/**
	 * Margin
	 */
	@Column("margin")
	private BigDecimal margin ;
	/**
	 * GoodsCost
	 */
	@Column("goods_cost")
	private BigDecimal goodsCost ;
	/**
	 * TotalBusinessCost
	 */
	@Column("total_business_cost")
	private BigDecimal totalBusinessCost ;
	@Column("cpa")
	private BigDecimal cpa ;
	/**
	 * 点击率
	 */
	@Column("ctr")
	private BigDecimal ctr ;
	/**
	 * 转化率
	 */
	@Column("cr")
	private BigDecimal cr ;
	/**
	 * 目标广告支出回报率
	 */
	@Column("roas")
	private BigDecimal roas ;
	/**
	 * 投资回报率
	 */
	@Column("roi")
	private BigDecimal roi ;
	/**
	 * 经常性收入
	 */
	@Column("rr")
	private BigDecimal rr ;
	/**
	 * 折扣
	 */
	@Column("discount")
	private BigDecimal discount ;
	/**
	 * 折扣率
	 */
	@Column("discount_rate")
	private BigDecimal discountRate ;
	/**
	 * 调用次数
	 */
	@Column("calls")
	private Integer calls ;
	/**
	 * 点击付费广告的费用
	 */
	@Column("cpc_cost")
	private BigDecimal cpcCost ;
	/**
	 * 客户端广告的费用
	 */
	@Column("client_cost")
	private BigDecimal clientCost ;
	@Column("imported_cost")
	private BigDecimal importedCost ;
	/**
	 * 其他费用
	 */
	@Column("other_cost")
	private BigDecimal otherCost ;
	/**
	 * 数量
	 */
	@Column("quantity")
	private Integer quantity ;
	@Column("zzzcpc_quantity")
	private Integer zzzcpcQuantity ;
	/**
	 * AOV
	 */
	@Column("avo")
	private BigDecimal avo ;
	/**
	 * 用户获取成本
	 */
	@Column("cpc")
	private BigDecimal cpc ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomImpactAdsReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 广告日期
	*@return
	*/
	public Date getDay(){
		return  day;
	}
	/**
	* 广告日期
	*@param  day
	*/
	public void setDay(Date day ){
		this.day = day;
	}
	/**
	* 参与的媒体（合伙人）数量
	*@return
	*/
	public Integer getMediaCount(){
		return  mediaCount;
	}
	/**
	* 参与的媒体（合伙人）数量
	*@param  mediaCount
	*/
	public void setMediaCount(Integer mediaCount ){
		this.mediaCount = mediaCount;
	}
	/**
	* 点击次数
	*@return
	*/
	public Integer getClicks(){
		return  clicks;
	}
	/**
	* 点击次数
	*@param  clicks
	*/
	public void setClicks(Integer clicks ){
		this.clicks = clicks;
	}
	/**
	* 跟踪的行动总数，不包括撤销
	*@return
	*/
	public Integer getActions(){
		return  actions;
	}
	/**
	* 跟踪的行动总数，不包括撤销
	*@param  actions
	*/
	public void setActions(Integer actions ){
		this.actions = actions;
	}
	/**
	* 所有行为的总收入(销售金额)
	*@return
	*/
	public BigDecimal getRevenue(){
		return  revenue;
	}
	/**
	* 所有行为的总收入(销售金额)
	*@param  revenue
	*/
	public void setRevenue(BigDecimal revenue ){
		this.revenue = revenue;
	}
	/**
	* 总诉讼费用和其他(非诉讼)费用
	*@return
	*/
	public BigDecimal getTotalCost(){
		return  totalCost;
	}
	/**
	* 总诉讼费用和其他(非诉讼)费用
	*@param  totalCost
	*/
	public void setTotalCost(BigDecimal totalCost ){
		this.totalCost = totalCost;
	}
	/**
	* 原始浏览次数
	*@return
	*/
	public Integer getRawImpressions(){
		return  rawImpressions;
	}
	/**
	* 原始浏览次数
	*@param  rawImpressions
	*/
	public void setRawImpressions(Integer rawImpressions ){
		this.rawImpressions = rawImpressions;
	}
	/**
	* 浏览次数
	*@return
	*/
	public Integer getImpressions(){
		return  impressions;
	}
	/**
	* 浏览次数
	*@param  impressions
	*/
	public void setImpressions(Integer impressions ){
		this.impressions = impressions;
	}
	/**
	* 总的点击次数
	*@return
	*/
	public Integer getTotalClicks(){
		return  totalClicks;
	}
	/**
	* 总的点击次数
	*@param  totalClicks
	*/
	public void setTotalClicks(Integer totalClicks ){
		this.totalClicks = totalClicks;
	}
	/**
	* 相反的行为次数
	*@return
	*/
	public Integer getReversedActions(){
		return  reversedActions;
	}
	/**
	* 相反的行为次数
	*@param  reversedActions
	*/
	public void setReversedActions(Integer reversedActions ){
		this.reversedActions = reversedActions;
	}
	/**
	* 确认的数量
	*@return
	*/
	public Integer getActionsConfirmed(){
		return  actionsConfirmed;
	}
	/**
	* 确认的数量
	*@param  actionsConfirmed
	*/
	public void setActionsConfirmed(Integer actionsConfirmed ){
		this.actionsConfirmed = actionsConfirmed;
	}
	/**
	* 打开的数量
	*@return
	*/
	public Integer getActionsOpen(){
		return  actionsOpen;
	}
	/**
	* 打开的数量
	*@param  actionsOpen
	*/
	public void setActionsOpen(Integer actionsOpen ){
		this.actionsOpen = actionsOpen;
	}
	/**
	* Grossactions
	*@return
	*/
	public Integer getGrossActions(){
		return  grossActions;
	}
	/**
	* Grossactions
	*@param  grossActions
	*/
	public void setGrossActions(Integer grossActions ){
		this.grossActions = grossActions;
	}
	/**
	* ReversedRevenue
	*@return
	*/
	public Long getReversedRevenue(){
		return  reversedRevenue;
	}
	/**
	* ReversedRevenue
	*@param  reversedRevenue
	*/
	public void setReversedRevenue(Long reversedRevenue ){
		this.reversedRevenue = reversedRevenue;
	}
	/**
	* Grossrevenue
	*@return
	*/
	public Long getGrossRevenue(){
		return  grossRevenue;
	}
	/**
	* Grossrevenue
	*@param  grossRevenue
	*/
	public void setGrossRevenue(Long grossRevenue ){
		this.grossRevenue = grossRevenue;
	}
	/**
	* ActionCost
	*@return
	*/
	public BigDecimal getAcctionCost(){
		return  acctionCost;
	}
	/**
	* ActionCost
	*@param  acctionCost
	*/
	public void setAcctionCost(BigDecimal acctionCost ){
		this.acctionCost = acctionCost;
	}
	/**
	* ReversedActionCost
	*@return
	*/
	public BigDecimal getReversedActionCost(){
		return  reversedActionCost;
	}
	/**
	* ReversedActionCost
	*@param  reversedActionCost
	*/
	public void setReversedActionCost(BigDecimal reversedActionCost ){
		this.reversedActionCost = reversedActionCost;
	}
	/**
	* Grossactioncost
	*@return
	*/
	public BigDecimal getGrossActionCost(){
		return  grossActionCost;
	}
	/**
	* Grossactioncost
	*@param  grossActionCost
	*/
	public void setGrossActionCost(BigDecimal grossActionCost ){
		this.grossActionCost = grossActionCost;
	}
	/**
	* Margin
	*@return
	*/
	public BigDecimal getMargin(){
		return  margin;
	}
	/**
	* Margin
	*@param  margin
	*/
	public void setMargin(BigDecimal margin ){
		this.margin = margin;
	}
	/**
	* GoodsCost
	*@return
	*/
	public BigDecimal getGoodsCost(){
		return  goodsCost;
	}
	/**
	* GoodsCost
	*@param  goodsCost
	*/
	public void setGoodsCost(BigDecimal goodsCost ){
		this.goodsCost = goodsCost;
	}
	/**
	* TotalBusinessCost
	*@return
	*/
	public BigDecimal getTotalBusinessCost(){
		return  totalBusinessCost;
	}
	/**
	* TotalBusinessCost
	*@param  totalBusinessCost
	*/
	public void setTotalBusinessCost(BigDecimal totalBusinessCost ){
		this.totalBusinessCost = totalBusinessCost;
	}
	public BigDecimal getCpa(){
		return  cpa;
	}
	public void setCpa(BigDecimal cpa ){
		this.cpa = cpa;
	}
	/**
	* 点击率
	*@return
	*/
	public BigDecimal getCtr(){
		return  ctr;
	}
	/**
	* 点击率
	*@param  ctr
	*/
	public void setCtr(BigDecimal ctr ){
		this.ctr = ctr;
	}
	/**
	* 转化率
	*@return
	*/
	public BigDecimal getCr(){
		return  cr;
	}
	/**
	* 转化率
	*@param  cr
	*/
	public void setCr(BigDecimal cr ){
		this.cr = cr;
	}
	/**
	* 目标广告支出回报率
	*@return
	*/
	public BigDecimal getRoas(){
		return  roas;
	}
	/**
	* 目标广告支出回报率
	*@param  roas
	*/
	public void setRoas(BigDecimal roas ){
		this.roas = roas;
	}
	/**
	* 投资回报率
	*@return
	*/
	public BigDecimal getRoi(){
		return  roi;
	}
	/**
	* 投资回报率
	*@param  roi
	*/
	public void setRoi(BigDecimal roi ){
		this.roi = roi;
	}
	/**
	* 经常性收入
	*@return
	*/
	public BigDecimal getRr(){
		return  rr;
	}
	/**
	* 经常性收入
	*@param  rr
	*/
	public void setRr(BigDecimal rr ){
		this.rr = rr;
	}
	/**
	* 折扣
	*@return
	*/
	public BigDecimal getDiscount(){
		return  discount;
	}
	/**
	* 折扣
	*@param  discount
	*/
	public void setDiscount(BigDecimal discount ){
		this.discount = discount;
	}
	/**
	* 折扣率
	*@return
	*/
	public BigDecimal getDiscountRate(){
		return  discountRate;
	}
	/**
	* 折扣率
	*@param  discountRate
	*/
	public void setDiscountRate(BigDecimal discountRate ){
		this.discountRate = discountRate;
	}
	/**
	* 调用次数
	*@return
	*/
	public Integer getCalls(){
		return  calls;
	}
	/**
	* 调用次数
	*@param  calls
	*/
	public void setCalls(Integer calls ){
		this.calls = calls;
	}
	/**
	* 点击付费广告的费用
	*@return
	*/
	public BigDecimal getCpcCost(){
		return  cpcCost;
	}
	/**
	* 点击付费广告的费用
	*@param  cpcCost
	*/
	public void setCpcCost(BigDecimal cpcCost ){
		this.cpcCost = cpcCost;
	}
	/**
	* 客户端广告的费用
	*@return
	*/
	public BigDecimal getClientCost(){
		return  clientCost;
	}
	/**
	* 客户端广告的费用
	*@param  clientCost
	*/
	public void setClientCost(BigDecimal clientCost ){
		this.clientCost = clientCost;
	}
	public BigDecimal getImportedCost(){
		return  importedCost;
	}
	public void setImportedCost(BigDecimal importedCost ){
		this.importedCost = importedCost;
	}
	/**
	* 其他费用
	*@return
	*/
	public BigDecimal getOtherCost(){
		return  otherCost;
	}
	/**
	* 其他费用
	*@param  otherCost
	*/
	public void setOtherCost(BigDecimal otherCost ){
		this.otherCost = otherCost;
	}
	/**
	* 数量
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 数量
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	public Integer getZzzcpcQuantity(){
		return  zzzcpcQuantity;
	}
	public void setZzzcpcQuantity(Integer zzzcpcQuantity ){
		this.zzzcpcQuantity = zzzcpcQuantity;
	}
	/**
	* AOV
	*@return
	*/
	public BigDecimal getAvo(){
		return  avo;
	}
	/**
	* AOV
	*@param  avo
	*/
	public void setAvo(BigDecimal avo ){
		this.avo = avo;
	}
	/**
	* 用户获取成本
	*@return
	*/
	public BigDecimal getCpc(){
		return  cpc;
	}
	/**
	* 用户获取成本
	*@param  cpc
	*/
	public void setCpc(BigDecimal cpc ){
		this.cpc = cpc;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
