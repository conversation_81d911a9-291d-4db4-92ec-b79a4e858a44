package com.zielsmart.mc.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.McConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @version V1.0
 * @title: WayfairService
 * @package: com.zielsmart.mc.service
 * @description:
 * @author: lvjishu<PERSON>
 * @date: 2021-04-27 9:26
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class WayfairService {
    @Value("${wayfair.granttype}")
    private String granttype;
    @Value("${wayfair.clientid}")
    private String clientid;
    @Value("${wayfair.clientsecret}")
    private String clientsecret;
    @Value("${wayfair.url}")
    private String url;
    @Value("${wayfair.tokenurl}")
    private String tokenurl;
    @Cacheable(cacheNames = McConstants.CACHE_AREA_WAYFAIR, key = "token")
    public String getToken(){
        String token =null;
        Map<String,String> map=new HashMap<>();
        map.put("grant_type",granttype);
        map.put("client_id",clientid);
        map.put("client_secret",clientsecret);
        map.put("audience",url);
        try {
            HttpResponse response = HttpRequest.post(tokenurl)
                    .header("content-type","application/json")
                    .header("cache-control","no-cache")
                    .body(new ObjectMapper().writeValueAsString(map))
                    .timeout(300000)
                    .execute();
            if (response.isOk() && response.getStatus() == HttpStatus.HTTP_OK) {
                //TODO 解析json
                String jsonStr = response.body();
                token = JSONUtil.parseObj(jsonStr).getStr("access_token");
                //{"access_token":"BStoMToZurch37lbMM5F93tn1iVPO6XmM9ctTzoskVG9oOWvpthRtYPsDXE8F6ruIVDrr.y9NV7tRneZSDVwGAsU1V_lO.yvbWlD3o0DdlGmnHVVlVAg3cB6166ByAeX2FZenKGuHdFduyC3mQb6o84fUdaXyRFeBhw4UNjnPuEosWLH4H_mR3GHHypuQI0JTFCYNx6fiH9.BtiGE4F8cD1KVO.qkXYy5OWk8CLsZlEsZUnLvIjc.6_Qmr4D4syg36li71Up3M5bVKtscaTL0qNibh4zdiMFT5e3bARoDNl8.b.1qQBRvVZmRwV.kE212nXPKWW5cikQrMEE6DmStpcPWiDU6DUzrjbkZLRRi0xpeH1sjCYcIrT8Ng792UmwGvat1CgVwPODdOOLfORFCjC4eFXR938PmkZmYjWdpP8KGhx5PGEW5oHHW6iWyPirNG4YuXTc1FVwIi6W3KOaKFVR6KUVPASvveLKAUaVto1MH1Kh5hPeXIDqbHaEYofrQmV5IwDgZ7cQXc3McgzEmgmfOIXx3zEnQJQN1CSzjb2PWHxErChRPuCdgDNYfiDK2G8rhi1OsFfyQsmkx.Bbuvj3CtZM5Gt.k.k2f5Qs_5hEIl4TSxCgo9Wqp9893L1JAwuUdkhEVIGH3VIPzE3AwPxu6wqBUrtnQ86eW4aGzpKEEfL7BXt0v9mdFCOKjoIakLIqlB74otHGFvucBfwboXXH7jvDf76ATzgvv1omgOj2zxkkWbjahw6mY_5A_yoKnpcaNV3IkmbmGaQXPAT4j9CjxpHBlv4YjPk9Cl_NZhke8NnCA.Vu6omITruH0imVTZ7LLZchtB.G6UDUHXKwIPOv.LRE6.M4wP3s4AU5Pyysq7zl0lhAL0Ff2XEBv_EE0LdN5bK7cn8xmPzHoka3go64s98jCV2kGNHtIgEeLPBma_f9tdRLj3MSyAEXNUwFL2sAdIY3JzcNkTNkLhbSiDFKFEPA0EtniZ4S52DrAhcfNDtNjQEeVapRoCNNCG67_0z6EP7hINib4taZ9bnGZWyB2HNnqP3j35BG_NQw.xmLrU.7IJfZfmErIlYlUnLMbvU2p45gwzDAmOcOgZv0p5GvrZlHmhM.fwsQg3nLSJftrcRr_LCzE.WxPF_w6WT0bQatDGuc0ZPWgkuq_UzXTRdKSNRBxylY2SkGgxCqdEUhZQKemZsHuo8W.53CcMlO_flKgepOzxOj_W6GWDzkRPlxZYb7QHPsAYD.tJoX6pK9HscdEpnrIqgPHEDOniTdjz2pZOv.uQM1_lVEDAWIwj6ud3S2SGg14YHBTGNfaX.hH7.ftC9TY9jijF2jNiWACbJyMsfz5Tjvz4InglX_2IcB4G7S7TbZhjcT5JKgy_Tur3YYY1GSO_I0BANCB7NpTrUbcn2G_qQIoG.aH74ks7eBVC_Dp5f4YPneh2_1ZDCFbXUKTbLi5EqBk3I9Br8.yIG78_MCpHERN6nhKQ.cfLhUNPUsSeSrl3rlfTowTUg_prpMKeIQCG8x5vjEjDT4l1ITrfqD9UcZF4luTaPIMhlKAxoX7SWk.Tuq6Q.h2BWohxJ4L0e.Dr70WRvvtoZD2zE2xZsCdgW_6zsfB9kGG_fhwysjlaUTXRt2ccYt2A18_gy8SfYhOR67RNPKm2fR18Pmibyw8XXkSmok6Qe8xhgalWdi9ZriFf89n6_j8ZzRg1qJKbRsaqb9pPq81qEN1LZ5ZADQZFzZyWzrqHuv6d9orpLzlAsVdDBbn8Y9RsBrqSzK1ggdfgrSSbuVyUlgPtKrE9G2at9Fr9V6_v6k4JC2swuOBeVti1d1gTtNa_MB81t_Vl47x4TyECNz5osd9xKV3Zd2JCWuvScKcWNYxUEvVSFM5pTN4lXRDAJjs3QHXzK33Imf.teGDKNibRyfXvKgrwdWjsBsa6I6Av8I0tnXS1LILchqjoNDiZ9jh7FB_JDQBUfGbZv7Ye7Q61kJnvwo3sBB.DDaFODv059yVy1O1um354Dgdp_PJWtY8llXsIq.dPycylYKm62QGGdA0GeZ_HorAghXy6U7eBMom8XgPYK0XM62NvW_w3TNqDwGP_jcFfbmd6h8zaQxc.H3z2gw.yp.Zb02VfhsoCQf814QdG3MIVETvNe3srGJf56SyL2eQV0-","token_type":"bearer","expires_in":"3600","refresh_token":"AOlBYWC48pAtCCqtVIpyKcs.14X9nUBaDI4P84MtYENsGwnpK15yN18-"}
            } else {
                log.error("获取token失败：{}", response.getStatus());
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return token;
    }
}
