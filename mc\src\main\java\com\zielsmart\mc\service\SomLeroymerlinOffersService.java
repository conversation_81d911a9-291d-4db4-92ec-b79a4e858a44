package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomLeroymerlinOffers;
import com.zielsmart.mc.repository.mapper.SomLeroymerlinOffersMapper;
import com.zielsmart.mc.util.InventoryCalcUtil;
import com.zielsmart.mc.vo.SomLeroymerlinOffersPageSearchVo;
import com.zielsmart.mc.vo.SomLeroymerlinOffersVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-04-08 11:04:41
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomLeroymerlinOffersService {
    
    @Resource
    private SomLeroymerlinOffersMapper somLeroymerlinOffersMapper;


    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomLeroymerlinOffersVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomLeroymerlinOffersVo> queryByPage(SomLeroymerlinOffersPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomLeroymerlinOffersVo> pageResult = somLeroymerlinOffersMapper.queryByPage(searchVo, pageRequest);
        if(!pageResult.getList().isEmpty()){
            List<String> skuList = null;
            if(pageResult.getList().size()<200){
                skuList = pageResult.getList().stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("LeroyMerlin.fr",skuList);
            calcInventory(pageResult.getList(), inventoryMap);
        }
        return ConvertUtils.pageConvert(pageResult, SomLeroymerlinOffersVo.class, searchVo);
    }

    private static void calcInventory(List<SomLeroymerlinOffersVo> list, Map<String, BigDecimal> inventoryMap) {
        for (SomLeroymerlinOffersVo offersVo : list) {
            BigDecimal inventory = inventoryMap.getOrDefault(offersVo.getSku()+"INVENTORY", BigDecimal.ZERO);
            BigDecimal dms = inventoryMap.getOrDefault(offersVo.getSku()+"DMS", BigDecimal.ZERO);
            //减去安全库存
            int stock = inventory.intValue() - Math.max(dms.divide(BigDecimal.valueOf(3),2,  RoundingMode.DOWN).intValue(), offersVo.getSafetyStock());
            offersVo.setStock(Math.max(stock, 0));
        }
    }

    /**
     * save
     * 添加
     * @param somLeroymerlinOffersVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomLeroymerlinOffersVo somLeroymerlinOffersVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somLeroymerlinOffersVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if(StrUtil.isBlank(somLeroymerlinOffersVo.getSellerSku()) || somLeroymerlinOffersVo.getPrice()==null || StrUtil.isBlank(somLeroymerlinOffersVo.getLogistic()) || somLeroymerlinOffersVo.getLeadtime()==null){
            throw new ValidateException("必填项未填写，请检查数据");
        }
        somLeroymerlinOffersVo.setModifyNum(tokenUser.getJobNumber());
        somLeroymerlinOffersVo.setModifyName(tokenUser.getUserName());
        somLeroymerlinOffersVo.setModifyTime(DateTime.now().toJdkDate());

        if (somLeroymerlinOffersVo.getDiscountPrice() != null) {
            if(somLeroymerlinOffersVo.getDiscountStartDate()==null || somLeroymerlinOffersVo.getDiscountEndDate()==null){
                throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
            }

            if(somLeroymerlinOffersVo.getDiscountStartDate().after(somLeroymerlinOffersVo.getDiscountEndDate())){
                throw new ValidateException("展示码"+somLeroymerlinOffersVo.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
            }
        }

        if (StrUtil.isBlank(somLeroymerlinOffersVo.getAid())) {
            //插入
            //判断展示码是否存在
            long count = somLeroymerlinOffersMapper.createLambdaQuery().andEq("seller_sku", somLeroymerlinOffersVo.getSellerSku()).count();
            if (count > 0) {
                throw new ValidateException("展示码已存在，请刷新界面检查数据");
            }
            somLeroymerlinOffersVo.setAid(IdUtil.fastSimpleUUID());
            somLeroymerlinOffersMapper.insert(ConvertUtils.beanConvert(somLeroymerlinOffersVo, SomLeroymerlinOffers.class));
        }else {
            //更新
            somLeroymerlinOffersMapper.updateById(ConvertUtils.beanConvert(somLeroymerlinOffersVo, SomLeroymerlinOffers.class));
        }
    }

    public String export(SomLeroymerlinOffersPageSearchVo searchVo) {
        List<SomLeroymerlinOffersVo> records = somLeroymerlinOffersMapper.exportExcel(searchVo);
        if (!records.isEmpty()) {
            //计算库存
            List<String> skuList = null;
            if(records.size()<200){
                skuList = records.stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("BUT.fr",skuList);
            calcInventory(records, inventoryMap);

            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "But fr Offers管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomLeroymerlinOffersVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomLeroymerlinOffersVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验"展示码","划线价格", "折扣价格","折扣开始时间","折扣结束时间","物流方式", "备货时间","备注" 是否为空
        HashSet<String> sellerSet = new HashSet<>();
        List<SomLeroymerlinOffers> existSellerSku = somLeroymerlinOffersMapper.createLambdaQuery().select("seller_sku","aid");
        Map<String, String> existSellerSkuMap = existSellerSku.stream().collect(Collectors.toMap(SomLeroymerlinOffers::getSellerSku, x -> x.getAid()));
        List<SomLeroymerlinOffers> allLeroymerlinListing = somLeroymerlinOffersMapper.allLeroymerlinListing();
        //转map
        Map<String, SomLeroymerlinOffers> leroymerlinOffersMap = allLeroymerlinListing.stream().collect(Collectors.toMap(SomLeroymerlinOffers::getSellerSku, x -> x));
        //校验展示码是否存在
        String notExistSellerSkus = list.stream().map(SomLeroymerlinOffersVo::getSellerSku).filter(sellerSku -> !leroymerlinOffersMap.containsKey(sellerSku)).collect(Collectors.joining(","));
        if(StrUtil.isNotBlank(notExistSellerSkus)){
            throw new ValidateException("展示码"+notExistSellerSkus+"不存在，批量导入失败!");
        }

        List<SomLeroymerlinOffersVo> insertList = new ArrayList<>();
        List<SomLeroymerlinOffersVo> updateList = new ArrayList<>();

        for (SomLeroymerlinOffersVo item : list) {
            if(!sellerSet.add(item.getSellerSku())){
                throw new ValidateException(" 展示码"+item.getSellerSku()+"重复，批量导入失败");
            }
            if(StrUtil.isBlank(item.getSellerSku()) || item.getPrice()==null || StrUtil.isBlank(item.getLogistic()) || item.getLeadtime()==null){
                throw new ValidateException("必填项未填写，批量导入失败");
            }
            if (item.getDiscountPrice() != null) {
                if(item.getDiscountStartDate()==null || item.getDiscountEndDate()==null){
                    throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
                }
                if(item.getPrice().compareTo(item.getDiscountPrice())<=0){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“划线价格>折扣价格”规则，批量导入失败！");
                }
                if(item.getDiscountStartDate().after(item.getDiscountEndDate())){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
                }
            }
            if(item.getDiscountStartDate()!=null || item.getDiscountEndDate()!=null){
                if (item.getDiscountPrice() == null) {
                    throw new ValidateException("折扣开始时间和结束时间已填写，折扣价格必须填写");
                }
            }

            if (existSellerSkuMap.containsKey(item.getSellerSku())) {
                //执行更新操作
                updateList.add(item);
            }else {
                //执行插入操作
                item.setAid(IdUtil.fastSimpleUUID());
                insertList.add(item);
            }
            item.setModifyNum(tokenUser.getJobNumber());
            item.setModifyName(tokenUser.getUserName());
            item.setModifyTime(DateTime.now().toJdkDate());
        }
        if(!insertList.isEmpty()){
            somLeroymerlinOffersMapper.insertBatch(ConvertUtils.listConvert(insertList, SomLeroymerlinOffers.class));
        }
        if(!updateList.isEmpty()){
            somLeroymerlinOffersMapper.updateBatch(ConvertUtils.listConvert(updateList, SomLeroymerlinOffers.class));
        }
    }
}
