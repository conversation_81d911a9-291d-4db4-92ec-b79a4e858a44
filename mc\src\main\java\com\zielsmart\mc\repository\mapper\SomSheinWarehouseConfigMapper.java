package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-09-20
*/

@SqlResource("somSheinWarehouseConfig")
public interface SomSheinWarehouseConfigMapper extends BaseMapper<SomSheinWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomSheinWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomSheinWarehouseConfigVo> queryByPage(@Param("searchVo")SomSheinWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
