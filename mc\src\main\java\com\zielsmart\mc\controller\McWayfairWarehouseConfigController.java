package com.zielsmart.mc.controller;

import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.McWayfairWarehouseConfigService;
import com.zielsmart.mc.vo.McWayfairWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWayfairWarehouseConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.LoginIgnore;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWayfairWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcWayfairWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "wayfair可售仓库配置管理")
public class McWayfairWarehouseConfigController extends BasicController{

    @Resource
    McWayfairWarehouseConfigService mcWayfairWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McWayfairWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McWayfairWarehouseConfigVo>> queryByPage(@RequestBody McWayfairWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWayfairWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcWayfairWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/更新")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McWayfairWarehouseConfigVo mcWayfairWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWayfairWarehouseConfigService.save(mcWayfairWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcWayfairWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @LoginIgnore
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McWayfairWarehouseConfigVo mcWayfairWarehouseConfigVo) throws ValidateException {
        mcWayfairWarehouseConfigService.delete(mcWayfairWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * getConfigsBySite
     * 根据站点获取可售仓库配置信息
     * @param json
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McWayfairWarehouseConfigVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据站点获取可售仓库配置信息")
    @PostMapping(value = "/getConfigsBySite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWayfairWarehouseConfigVo>> getConfigsBySite(@RequestBody JSONObject json) throws ValidateException {
        return ResultVo.ofSuccess(mcWayfairWarehouseConfigService.getConfigsBySite(json));
    }

    /**
     * getPlatformWareHouseCode
     * 根据平台、站点获取平台仓库编码
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McWayfairWarehouseConfigVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台、站点获取平台仓库编码")
    @PostMapping(value = "/getPlatformWareHouseCode")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWayfairWarehouseConfigVo>> getPlatformWareHouseCode(@RequestBody McWayfairWarehouseConfigPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcWayfairWarehouseConfigService.getPlatformWareHouseCode(searchVo));
    }
}
