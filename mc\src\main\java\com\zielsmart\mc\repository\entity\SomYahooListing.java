package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2023-02-02
 */

@Table(name = "mc.som_yahoo_listing")
public class SomYahooListing implements java.io.Serializable {
    /**
     * 主键id
     */
    @AssignID
    private String aid;
    /**
     * 产品所在路径
     */
    @Column("path")
    private String path;
    /**
     * 产品名称
     */
    @Column("product_name")
    private String productName;
    /**
     * 产品编码
     */
    @Column("code")
    private String code;
    /**
     * 子产品编码
     */
    @Column("sub_code")
    private String subCode;
    /**
     * 原价 日元
     */
    @Column("original_price")
    private Integer originalPrice;
    /**
     * 售价 日元
     */
    @Column("price")
    private Integer price;
    /**
     * 优惠价
     */
    @Column("sale_price")
    private Integer salePrice;
    /**
     * 会员价
     */
    @Column("member_price")
    private Integer memberPrice;
    /**
     * 配置项
     */
    @Column("options")
    private String options;
    /**
     * 标题
     */
    @Column("headline")
    private String headline;
    /**
     * 产品描述
     */
    @Column("caption")
    private String caption;
    /**
     * 产品摘要
     */
    @Column("abstract")
    private String abstracts;
    /**
     * 产品详情
     */
    @Column("explanation")
    private String explanation;
    /**
     * 附加说明1
     */
    @Column("additional1")
    private String additional1;
    /**
     * 附加说明2
     */
    @Column("additional2")
    private String additional2;
    /**
     * 附加说明3
     */
    @Column("additional3")
    private String additional3;
    /**
     * 相关链接
     */
    @Column("relevant_links")
    private String relevantLinks;
    /**
     * 发货重量
     */
    @Column("ship_weight")
    private BigDecimal shipWeight;
    /**
     * 是否征税
     */
    @Column("taxable")
    private Integer taxable;
    /**
     * 发布日期
     */
    @Column("release_date")
    private Date releaseDate;
    @Column("temporary_point_term")
    private String temporaryPointTerm;
    @Column("point_code")
    private Integer pointCode;
    @Column("meta_key")
    private String metaKey;
    @Column("meta_desc")
    private String metaDesc;
    @Column("template")
    private String template;
    @Column("sale_period_start")
    private Date salePeriodStart;
    @Column("sale_period_end")
    private Date salePeriodEnd;
    @Column("sale_limit")
    private Integer saleLimit;
    @Column("sp_code")
    private String spCode;
    @Column("pr_rate")
    private Integer prRate;
    /**
     * 品牌编码
     */
    @Column("brand_code")
    private String brandCode;
    @Column("person_code")
    private String personCode;
    /**
     * 雅虎产品编码
     */
    @Column("yahoo_product_code")
    private String yahooProductCode;
    /**
     * 产品编码
     */
    @Column("product_code")
    private String productCode;
    /**
     * JAN编码
     */
    @Column("jan")
    private String jan;
    /**
     * ISBN编码
     */
    @Column("isbn")
    private String isbn;
    @Column("delivery")
    private Integer delivery;
    @Column("astk_code")
    private Integer astkCode;
    @Column("condition")
    private Integer condition;
    @Column("taojapan")
    private Integer taojapan;
    /**
     * 分类编码
     */
    @Column("product_category")
    private String productCategory;
    /**
     * 规格1
     */
    @Column("spec1")
    private String spec1;
    /**
     * 规格2
     */
    @Column("spec2")
    private String spec2;
    /**
     * 规格3
     */
    @Column("spec3")
    private String spec3;
    /**
     * 规格4
     */
    @Column("spec4")
    private String spec4;
    /**
     * 规格5
     */
    @Column("spec5")
    private String spec5;
    /**
     * 规格6
     */
    @Column("spec6")
    private String spec6;
    /**
     * 规格7
     */
    @Column("spec7")
    private String spec7;
    /**
     * 规格8
     */
    @Column("spec8")
    private String spec8;
    /**
     * 规格9
     */
    @Column("spec9")
    private String spec9;
    /**
     * 规格10
     */
    @Column("spec10")
    private String spec10;
    @Column("display")
    private Integer display;
    @Column("sort")
    private Integer sort;
    @Column("sp_additional")
    private String spAdditional;
    @Column("sort_priority")
    private Integer sortPriority;
    /**
     * 原价证明
     */
    @Column("original_price_evidence")
    private String originalPriceEvidence;
    /**
     * 交货期存货
     */
    @Column("lead_time_instock")
    private Integer leadTimeInstock;
    /**
     * 交货时间
     */
    @Column("lead_time_outstock")
    private Integer leadTimeOutstock;
    @Column("keep_stock")
    private Integer keepStock;
    /**
     * 是否免邮
     */
    @Column("postage_set")
    private Integer postageSet;
    @Column("taxrate_type")
    private BigDecimal taxrateType;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomYahooListing() {
    }

    /**
     * 主键id
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键id
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 产品所在路径
     *
     * @return
     */
    public String getPath() {
        return path;
    }

    /**
     * 产品所在路径
     *
     * @param path
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 产品名称
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 产品名称
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 产品编码
     *
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * 产品编码
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 子产品编码
     *
     * @return
     */
    public String getSubCode() {
        return subCode;
    }

    /**
     * 子产品编码
     *
     * @param subCode
     */
    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    /**
     * 原价 日元
     *
     * @return
     */
    public Integer getOriginalPrice() {
        return originalPrice;
    }

    /**
     * 原价 日元
     *
     * @param originalPrice
     */
    public void setOriginalPrice(Integer originalPrice) {
        this.originalPrice = originalPrice;
    }

    /**
     * 售价 日元
     *
     * @return
     */
    public Integer getPrice() {
        return price;
    }

    /**
     * 售价 日元
     *
     * @param price
     */
    public void setPrice(Integer price) {
        this.price = price;
    }

    /**
     * 优惠价
     *
     * @return
     */
    public Integer getSalePrice() {
        return salePrice;
    }

    /**
     * 优惠价
     *
     * @param salePrice
     */
    public void setSalePrice(Integer salePrice) {
        this.salePrice = salePrice;
    }

    /**
     * 会员价
     *
     * @return
     */
    public Integer getMemberPrice() {
        return memberPrice;
    }

    /**
     * 会员价
     *
     * @param memberPrice
     */
    public void setMemberPrice(Integer memberPrice) {
        this.memberPrice = memberPrice;
    }

    /**
     * 配置项
     *
     * @return
     */
    public String getOptions() {
        return options;
    }

    /**
     * 配置项
     *
     * @param options
     */
    public void setOptions(String options) {
        this.options = options;
    }

    /**
     * 标题
     *
     * @return
     */
    public String getHeadline() {
        return headline;
    }

    /**
     * 标题
     *
     * @param headline
     */
    public void setHeadline(String headline) {
        this.headline = headline;
    }

    /**
     * 产品描述
     *
     * @return
     */
    public String getCaption() {
        return caption;
    }

    /**
     * 产品描述
     *
     * @param caption
     */
    public void setCaption(String caption) {
        this.caption = caption;
    }

    /**
     * 产品摘要
     *
     * @return
     */
    public String getAbstract() {
        return abstracts;
    }

    /**
     * 产品摘要
     *
     * @param abstracts
     */
    public void setAbstract(String abstracts) {
        this.abstracts = abstracts;
    }

    /**
     * 产品详情
     *
     * @return
     */
    public String getExplanation() {
        return explanation;
    }

    /**
     * 产品详情
     *
     * @param explanation
     */
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    /**
     * 附加说明1
     *
     * @return
     */
    public String getAdditional1() {
        return additional1;
    }

    /**
     * 附加说明1
     *
     * @param additional1
     */
    public void setAdditional1(String additional1) {
        this.additional1 = additional1;
    }

    /**
     * 附加说明2
     *
     * @return
     */
    public String getAdditional2() {
        return additional2;
    }

    /**
     * 附加说明2
     *
     * @param additional2
     */
    public void setAdditional2(String additional2) {
        this.additional2 = additional2;
    }

    /**
     * 附加说明3
     *
     * @return
     */
    public String getAdditional3() {
        return additional3;
    }

    /**
     * 附加说明3
     *
     * @param additional3
     */
    public void setAdditional3(String additional3) {
        this.additional3 = additional3;
    }

    /**
     * 相关链接
     *
     * @return
     */
    public String getRelevantLinks() {
        return relevantLinks;
    }

    /**
     * 相关链接
     *
     * @param relevantLinks
     */
    public void setRelevantLinks(String relevantLinks) {
        this.relevantLinks = relevantLinks;
    }

    /**
     * 发货重量
     *
     * @return
     */
    public BigDecimal getShipWeight() {
        return shipWeight;
    }

    /**
     * 发货重量
     *
     * @param shipWeight
     */
    public void setShipWeight(BigDecimal shipWeight) {
        this.shipWeight = shipWeight;
    }

    /**
     * 是否征税
     *
     * @return
     */
    public Integer getTaxable() {
        return taxable;
    }

    /**
     * 是否征税
     *
     * @param taxable
     */
    public void setTaxable(Integer taxable) {
        this.taxable = taxable;
    }

    /**
     * 发布日期
     *
     * @return
     */
    public Date getReleaseDate() {
        return releaseDate;
    }

    /**
     * 发布日期
     *
     * @param releaseDate
     */
    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    public String getTemporaryPointTerm() {
        return temporaryPointTerm;
    }

    public void setTemporaryPointTerm(String temporaryPointTerm) {
        this.temporaryPointTerm = temporaryPointTerm;
    }

    public Integer getPointCode() {
        return pointCode;
    }

    public void setPointCode(Integer pointCode) {
        this.pointCode = pointCode;
    }

    public String getMetaKey() {
        return metaKey;
    }

    public void setMetaKey(String metaKey) {
        this.metaKey = metaKey;
    }

    public String getMetaDesc() {
        return metaDesc;
    }

    public void setMetaDesc(String metaDesc) {
        this.metaDesc = metaDesc;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public Date getSalePeriodStart() {
        return salePeriodStart;
    }

    public void setSalePeriodStart(Date salePeriodStart) {
        this.salePeriodStart = salePeriodStart;
    }

    public Date getSalePeriodEnd() {
        return salePeriodEnd;
    }

    public void setSalePeriodEnd(Date salePeriodEnd) {
        this.salePeriodEnd = salePeriodEnd;
    }

    public Integer getSaleLimit() {
        return saleLimit;
    }

    public void setSaleLimit(Integer saleLimit) {
        this.saleLimit = saleLimit;
    }

    public String getspCode() {
        return spCode;
    }

    public void setspCode(String spCode) {
        this.spCode = spCode;
    }

    public Integer getprRate() {
        return prRate;
    }

    public void setprRate(Integer prRate) {
        this.prRate = prRate;
    }

    /**
     * 品牌编码
     *
     * @return
     */
    public String getBrandCode() {
        return brandCode;
    }

    /**
     * 品牌编码
     *
     * @param brandCode
     */
    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    /**
     * 雅虎产品编码
     *
     * @return
     */
    public String getYahooProductCode() {
        return yahooProductCode;
    }

    /**
     * 雅虎产品编码
     *
     * @param yahooProductCode
     */
    public void setYahooProductCode(String yahooProductCode) {
        this.yahooProductCode = yahooProductCode;
    }

    /**
     * 产品编码
     *
     * @return
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * 产品编码
     *
     * @param productCode
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     * JAN编码
     *
     * @return
     */
    public String getJan() {
        return jan;
    }

    /**
     * JAN编码
     *
     * @param jan
     */
    public void setJan(String jan) {
        this.jan = jan;
    }

    /**
     * ISBN编码
     *
     * @return
     */
    public String getIsbn() {
        return isbn;
    }

    /**
     * ISBN编码
     *
     * @param isbn
     */
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public Integer getDelivery() {
        return delivery;
    }

    public void setDelivery(Integer delivery) {
        this.delivery = delivery;
    }

    public Integer getAstkCode() {
        return astkCode;
    }

    public void setAstkCode(Integer astkCode) {
        this.astkCode = astkCode;
    }

    public Integer getCondition() {
        return condition;
    }

    public void setCondition(Integer condition) {
        this.condition = condition;
    }

    public Integer getTaojapan() {
        return taojapan;
    }

    public void setTaojapan(Integer taojapan) {
        this.taojapan = taojapan;
    }

    /**
     * 分类编码
     *
     * @return
     */
    public String getProductCategory() {
        return productCategory;
    }

    /**
     * 分类编码
     *
     * @param productCategory
     */
    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    /**
     * 规格1
     *
     * @return
     */
    public String getSpec1() {
        return spec1;
    }

    /**
     * 规格1
     *
     * @param spec1
     */
    public void setSpec1(String spec1) {
        this.spec1 = spec1;
    }

    /**
     * 规格2
     *
     * @return
     */
    public String getSpec2() {
        return spec2;
    }

    /**
     * 规格2
     *
     * @param spec2
     */
    public void setSpec2(String spec2) {
        this.spec2 = spec2;
    }

    /**
     * 规格3
     *
     * @return
     */
    public String getSpec3() {
        return spec3;
    }

    /**
     * 规格3
     *
     * @param spec3
     */
    public void setSpec3(String spec3) {
        this.spec3 = spec3;
    }

    /**
     * 规格4
     *
     * @return
     */
    public String getSpec4() {
        return spec4;
    }

    /**
     * 规格4
     *
     * @param spec4
     */
    public void setSpec4(String spec4) {
        this.spec4 = spec4;
    }

    /**
     * 规格5
     *
     * @return
     */
    public String getSpec5() {
        return spec5;
    }

    /**
     * 规格5
     *
     * @param spec5
     */
    public void setSpec5(String spec5) {
        this.spec5 = spec5;
    }

    /**
     * 规格6
     *
     * @return
     */
    public String getSpec6() {
        return spec6;
    }

    /**
     * 规格6
     *
     * @param spec6
     */
    public void setSpec6(String spec6) {
        this.spec6 = spec6;
    }

    /**
     * 规格7
     *
     * @return
     */
    public String getSpec7() {
        return spec7;
    }

    /**
     * 规格7
     *
     * @param spec7
     */
    public void setSpec7(String spec7) {
        this.spec7 = spec7;
    }

    /**
     * 规格8
     *
     * @return
     */
    public String getSpec8() {
        return spec8;
    }

    /**
     * 规格8
     *
     * @param spec8
     */
    public void setSpec8(String spec8) {
        this.spec8 = spec8;
    }

    /**
     * 规格9
     *
     * @return
     */
    public String getSpec9() {
        return spec9;
    }

    /**
     * 规格9
     *
     * @param spec9
     */
    public void setSpec9(String spec9) {
        this.spec9 = spec9;
    }

    /**
     * 规格10
     *
     * @return
     */
    public String getSpec10() {
        return spec10;
    }

    /**
     * 规格10
     *
     * @param spec10
     */
    public void setSpec10(String spec10) {
        this.spec10 = spec10;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getspAdditional() {
        return spAdditional;
    }

    public void setspAdditional(String spAdditional) {
        this.spAdditional = spAdditional;
    }

    public Integer getSortPriority() {
        return sortPriority;
    }

    public void setSortPriority(Integer sortPriority) {
        this.sortPriority = sortPriority;
    }

    /**
     * 原价证明
     *
     * @return
     */
    public String getOriginalPriceEvidence() {
        return originalPriceEvidence;
    }

    /**
     * 原价证明
     *
     * @param originalPriceEvidence
     */
    public void setOriginalPriceEvidence(String originalPriceEvidence) {
        this.originalPriceEvidence = originalPriceEvidence;
    }

    /**
     * 交货期存货
     *
     * @return
     */
    public Integer getLeadTimeInstock() {
        return leadTimeInstock;
    }

    /**
     * 交货期存货
     *
     * @param leadTimeInstock
     */
    public void setLeadTimeInstock(Integer leadTimeInstock) {
        this.leadTimeInstock = leadTimeInstock;
    }

    /**
     * 交货时间
     *
     * @return
     */
    public Integer getLeadTimeOutstock() {
        return leadTimeOutstock;
    }

    /**
     * 交货时间
     *
     * @param leadTimeOutstock
     */
    public void setLeadTimeOutstock(Integer leadTimeOutstock) {
        this.leadTimeOutstock = leadTimeOutstock;
    }

    public Integer getKeepStock() {
        return keepStock;
    }

    public void setKeepStock(Integer keepStock) {
        this.keepStock = keepStock;
    }

    /**
     * 是否免邮
     *
     * @return
     */
    public Integer getPostageSet() {
        return postageSet;
    }

    /**
     * 是否免邮
     *
     * @param postageSet
     */
    public void setPostageSet(Integer postageSet) {
        this.postageSet = postageSet;
    }

    public BigDecimal getTaxrateType() {
        return taxrateType;
    }

    public void setTaxrateType(BigDecimal taxrateType) {
        this.taxrateType = taxrateType;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
