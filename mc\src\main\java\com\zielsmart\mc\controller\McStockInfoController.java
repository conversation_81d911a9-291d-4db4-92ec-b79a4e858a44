package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McStockInfoService;
import com.zielsmart.mc.vo.McStockInfoSearchVo;
import com.zielsmart.mc.vo.McStockInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McStockInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcStockInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "可用库存信息管理")
public class McStockInfoController extends BasicController {

    @Resource
    McStockInfoService mcStockInfoService;

    /**
     * querybyPlatformSiteSellerSku
     * 根据平台站点展示码查询库存信息
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McStockInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点展示码查询库存信息")
    @PostMapping(value = "/querybyPlatformSiteSellerSku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<Integer> querybyPlatformSiteSellerSku(@RequestBody McStockInfoSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcStockInfoService.querybyPlatformSiteSellerSku(searchVo));
    }
}
