package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomCouponItems;
import com.zielsmart.mc.vo.*;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-04-21
 */

@SqlResource("somCouponItems")
public interface SomCouponItemsMapper extends BaseMapper<SomCouponItems> {

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomCouponItemsVo> queryByPage(@Param("searchVo") SomCouponItemsPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * checkUnique
     * 唯一性校验
     *
     * @param checkVo
     * @return int
     * <AUTHOR>
     * @history
     */
    List<SomCouponItemsVo> checkUnique(@Param("checkVo") SomCouponExVo checkVo);

    /**
     * batchUpdate
     * 批量更新
     *
     * @param itemsList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("itemsList") List<SomCouponItems> itemsList) {
        this.getSQLManager().updateBatch(SqlId.of("somCouponItems.batchUpdate"), itemsList);
    }

    /**
     * queryDiscounts
     * 查询产品活动折扣详情
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCouponItemsVo> queryDiscounts(@Param("searchVo")SomCouponSearchVo searchVo);

    /**
     * queryCouponPromotionDiscount
     * 查询coupon和promotion折扣
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomCouponItemsVo}
     * <AUTHOR>
     * @history
     */
    List<SomCouponItemsVo> queryCouponPromotionDiscount(@Param("searchVo")SomCouponSearchVo searchVo);

    /**
     * queryDiscounts
     * 查询产品活动折扣详情
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCouponItemsExtVo> queryDiscountList(@Param("searchVo")SomCouponSearchVo searchVo);
}
