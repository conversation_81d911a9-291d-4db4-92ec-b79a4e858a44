package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTargetListing;
import com.zielsmart.mc.repository.mapper.SomTargetListingMapper;
import com.zielsmart.mc.vo.SomTargetListingPageSearchVo;
import com.zielsmart.mc.vo.SomTargetListingVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.Base64;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTargetListingService
 * @description
 * @date 2023-12-05 15:34:18
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomTargetListingService {

    @Resource
    private SomTargetListingMapper somTargetListingMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomTargetListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTargetListingVo> queryByPage(SomTargetListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTargetListingVo> pageResult = dynamicSqlManager.getMapper(SomTargetListingMapper.class).queryByPage(searchVo, pageRequest);
        if (ObjectUtil.isNotEmpty(pageResult.getList())) {
            //站点暂时先写死
            pageResult.getList().forEach(f -> {
                //f.setSite("Target.us");
                f.setSite(StrUtil.isBlank(f.getSite()) ? "Target.us" : f.getSite());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomTargetListingVo.class, searchVo);
    }

}
