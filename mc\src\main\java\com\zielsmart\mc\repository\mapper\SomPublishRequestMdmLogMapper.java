package com.zielsmart.mc.repository.mapper;

import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomPublishRequestMdmLogMapper
 * @description 请求mdm日志表，后续可能会删除
 * @date 2025-07-28 10:17:48
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somPublishRequestMdmLog")
public interface SomPublishRequestMdmLogMapper extends BaseMapper<SomPublishRequestMdmLog> {

}
