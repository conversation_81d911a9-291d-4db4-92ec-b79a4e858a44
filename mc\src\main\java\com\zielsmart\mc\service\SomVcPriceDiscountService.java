package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.enums.VcDotdPromotionType;
import com.zielsmart.mc.enums.VcPromotionStatusEnum;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomVcPriceDiscount;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.SomVcPriceDiscountMapper;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zielsmart.mc.McConstants.NEED_CHECK;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-05-12 14:01:16
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcPriceDiscountService {

    @Resource
    private SomVcPriceDiscountMapper vcPriceDiscountMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private SomVcPromotionService vcPromotionService;

    @Resource
    private McProductSalesMapper productSalesMapper;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return VC Price Discount 营销活动集合
     */
    public PageVo<SomVcPriceDiscountVo> queryByPage(SomVcPriceDiscountPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcPriceDiscountVo> pageResult = vcPriceDiscountMapper.queryByPage(searchVo, pageRequest);
        List<SomVcPriceDiscountVo> somVcPriceDiscountVos = pageResult.getList();
        if (CollUtil.isNotEmpty(somVcPriceDiscountVos)) {
            Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getDictionaryMap();
            somVcPriceDiscountVos.forEach(data -> {
                // 活动类型/状态/申请原因
                McDictionaryInfo promotionTypeDict = dictionaryMap.get("VcPromotionType").get(String.valueOf(data.getPromotionType()));
                data.setPromotionTypeDesc(promotionTypeDict == null ? null : promotionTypeDict.getItemLable());
                McDictionaryInfo vcPriceDiscountStatusDict = dictionaryMap.get("VcPriceDiscountStatus").get(String.valueOf(data.getStatus()));
                data.setStatusDesc(vcPriceDiscountStatusDict == null ? null : vcPriceDiscountStatusDict.getItemLable());
                McDictionaryInfo vcPromotionApplyReason = dictionaryMap.get("VcPromotionApplyReason").get(String.valueOf(data.getApplyReason()));
                data.setApplyReasonDesc(vcPromotionApplyReason == null ? null : vcPromotionApplyReason.getItemLable());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomVcPriceDiscountVo.class, searchVo);
    }

    /**
     * 添加/编辑
     *
     * @param somVcPriceDiscountVo 入参
     * @param tokenUser            当前登录用户
     */
    public void addOrEdit(SomVcPriceDiscountVo somVcPriceDiscountVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcPriceDiscountVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        checkSaveOrEditParam(somVcPriceDiscountVo);
        if (StrUtil.isEmpty(somVcPriceDiscountVo.getAid())) {
            add(somVcPriceDiscountVo, tokenUser);
        } else {
            edit(somVcPriceDiscountVo, tokenUser);
        }
    }

    /**
     * 新增 VC Price Discount 营销活动
     *
     * @param somVcPriceDiscountVo 入参
     * @param tokenUser            当前登录用户
     */
    public void add(SomVcPriceDiscountVo somVcPriceDiscountVo, TokenUserInfo tokenUser) throws ValidateException {
        // 判端活动是否存在
        checkPromotionExist(somVcPriceDiscountVo);
        SomVcPriceDiscount somVcPriceDiscount = new SomVcPriceDiscount();
        buildSomVcPriceDiscount(somVcPriceDiscountVo, somVcPriceDiscount);
        // 核验销售视图是否存在并填充销售视图相关信息
        checkAndFillProductSalesField(somVcPriceDiscount);
        // 主键ID
        somVcPriceDiscount.setAid(IdUtil.fastSimpleUUID());
        // 新增默认状态：草稿
        somVcPriceDiscount.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
        // 新增默认活动类型：Price Discount
        somVcPriceDiscount.setPromotionType(VcDotdPromotionType.PRICE_DISCOUNT.getType());
        somVcPriceDiscount.setCreateNum(tokenUser.getJobNumber());
        somVcPriceDiscount.setCreateName(tokenUser.getUserName());
        somVcPriceDiscount.setCreateTime(DateTime.now().toJdkDate());
        vcPriceDiscountMapper.insert(somVcPriceDiscount);
    }

    /**
     * 修改 VC Price Discount 营销活动
     *
     * @param somVcPriceDiscountVo 入参
     * @param tokenUser            当前登录用户
     */
    public void edit(SomVcPriceDiscountVo somVcPriceDiscountVo, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据是否存在
        SomVcPriceDiscount somVcPriceDiscount = checkSomVcPriceDiscountExist(somVcPriceDiscountVo);
        // [草稿,需要关注]状态才可以编辑
        List<Integer> allowEditStatus = VcPromotionStatusEnum.getAllowEditStatus();
        if (!allowEditStatus.contains(somVcPriceDiscount.getStatus())) {
            throw new ValidateException("当前数据不允许编辑！");
        }
        // 判端活动是否存在
        checkPromotionExist(somVcPriceDiscountVo);
        // 构建需要更新的字段属性
        buildSomVcPriceDiscount(somVcPriceDiscountVo, somVcPriceDiscount);
        // 核验销售视图是否存在并填充销售视图相关信息
        checkAndFillProductSalesField(somVcPriceDiscount);
        // 编辑后状态 -> 草稿
        somVcPriceDiscount.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
        somVcPriceDiscount.setModifyNum(tokenUser.getJobNumber());
        somVcPriceDiscount.setModifyName(tokenUser.getUserName());
        somVcPriceDiscount.setModifyTime(DateTime.now().toJdkDate());
        vcPriceDiscountMapper.updateById(somVcPriceDiscount);
    }

    /**
     * 标记为已提报
     *
     * @param somVcPriceDiscountVo 入参
     * @param tokenUser            当前登录用户
     */
    public void markSubmit(SomVcPriceDiscountVo somVcPriceDiscountVo, TokenUserInfo tokenUser) throws ValidateException {
        SomVcPriceDiscount somVcPriceDiscount = checkSomVcPriceDiscountExist(somVcPriceDiscountVo);
        // [草稿]状态才可以标记为已提报
        Integer draftStatus = VcPromotionStatusEnum.DRAFT.getStatus();
        if (!draftStatus.equals(somVcPriceDiscount.getStatus())) {
            throw new ValidateException("当前数据不允许标记为已提报！");
        }
        // 标记已提报后 -> 未开始
        somVcPriceDiscount.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
        somVcPriceDiscount.setModifyTime(DateTime.now().toJdkDate());
        somVcPriceDiscount.setModifyNum(tokenUser.getJobNumber());
        somVcPriceDiscount.setModifyName(tokenUser.getUserName());
        vcPriceDiscountMapper.updateById(somVcPriceDiscount);
    }

    /**
     * 取消 VC Price Discount 营销活动
     *
     * @param somVcPriceDiscountVo 入参
     * @param tokenUser            当前登录用户
     */
    public void cancel(SomVcPriceDiscountVo somVcPriceDiscountVo, TokenUserInfo tokenUser) throws ValidateException {
        SomVcPriceDiscount somVcPriceDiscount = checkSomVcPriceDiscountExist(somVcPriceDiscountVo);
        // [进行中,需要关注,未开始]的活动允许取消
        List<Integer> allowCancelStatus = VcPromotionStatusEnum.getAllowCancelStatus();
        if (!allowCancelStatus.contains(somVcPriceDiscount.getStatus())) {
            throw new ValidateException("当前数据不允许取消！");
        }
        // 状态 -> 已取消
        somVcPriceDiscount.setStatus(VcPromotionStatusEnum.CANCELED.getStatus());
        somVcPriceDiscount.setModifyTime(DateTime.now().toJdkDate());
        somVcPriceDiscount.setModifyNum(tokenUser.getJobNumber());
        somVcPriceDiscount.setModifyName(tokenUser.getUserName());
        vcPriceDiscountMapper.updateById(somVcPriceDiscount);
    }

    /**
     * 删除 VC Price Discount 营销活动
     *
     * @param somVcPriceDiscountVo 入参
     */
    public void delete(SomVcPriceDiscountVo somVcPriceDiscountVo) throws ValidateException {
        SomVcPriceDiscount somVcPriceDiscount = checkSomVcPriceDiscountExist(somVcPriceDiscountVo);
        // [草稿]状态才可以删除
        Integer draftStatus = VcPromotionStatusEnum.DRAFT.getStatus();
        if (!draftStatus.equals(somVcPriceDiscount.getStatus())) {
            throw new ValidateException("当前数据不允许删除！");
        }
        vcPriceDiscountMapper.createLambdaQuery().andEq("aid", somVcPriceDiscountVo.getAid()).delete();
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64编码数据
     */
    public String export(SomVcPriceDiscountPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomVcPriceDiscountVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook;
            try {
                ExportParams params = new ExportParams(null, "VC Price Discount营销活动");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomVcPriceDiscountVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * Price Discount 导入
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    public void importExcel(List<SomVcPriceDiscountImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验导入的必须是同一站点
        vcPromotionService.checkImportBasic(importVos);
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：更新数据
        Date now = DateTime.now().toJdkDate();
        List<SomVcPriceDiscount> vcPriceDiscounts = new ArrayList<>();
        for (SomVcPriceDiscountImportVo importVo : importVos) {
            SomVcPriceDiscount somVcPriceDiscount = ConvertUtils.beanConvert(importVo, SomVcPriceDiscount.class);
            somVcPriceDiscount.setAid(IdUtil.fastSimpleUUID());
            // 新增默认活动类型：PRICE DISCOUNT
            somVcPriceDiscount.setPromotionType(VcDotdPromotionType.PRICE_DISCOUNT.getType());
            // 销售视图信息
            McProductSalesVo productSalesVo = importVo.getProductSalesVo();
            somVcPriceDiscount.setSalesGroupEmptCode(productSalesVo.getSalesGroupEmptCode());
            somVcPriceDiscount.setSalesGroupEmptName(productSalesVo.getSalesGroupEmptName());
            somVcPriceDiscount.setSalesGroupCode(productSalesVo.getSalesGroupCode());
            somVcPriceDiscount.setSalesGroupName(productSalesVo.getSalesGroupName());
            somVcPriceDiscount.setOperationEmptCode(productSalesVo.getOperationEmptCode());
            somVcPriceDiscount.setOperationEmptName(productSalesVo.getOperationEmptName());
            // 新增默认状态：草稿
            somVcPriceDiscount.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcPriceDiscount.setCreateNum(tokenUser.getJobNumber());
            somVcPriceDiscount.setCreateName(tokenUser.getUserName());
            somVcPriceDiscount.setCreateTime(now);
            vcPriceDiscounts.add(somVcPriceDiscount);
        }
        vcPriceDiscountMapper.insertBatch(vcPriceDiscounts);
    }


    /**
     * 核验 Price Discount 导入数据
     *
     * @param importVos 导入行数据
     * @return 错误信息集合
     */
    private List<String> checkImportData(List<SomVcPriceDiscountImportVo> importVos) {
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getImportDictionaryMap();
        List<String> errors = new ArrayList<>();
        // 导入公有属性查询
        SomVcPromotionImportInfoVo importInfoVo = vcPromotionService.queryImportSellerSkus(importVos);
        // 核验重复
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomVcPriceDiscountImportVo importVo : importVos) {
            // 基础核验，核验必填项，填写的字段是否正确
            if (!checkImportFields(importVo, dictionaryMap, errors, repeatCheckSet)) {
                continue;
            }
            // 核验活动是否存在
            SomVcPromotionCheckUniqueVo checkUniqueVo = vcPromotionService.buildImportCheckUniqueVo(importVo);
            if (vcPromotionService.checkUnique(checkUniqueVo)) {
                errors.add(StrUtil.concat(true, "错误20：", importVo.getUniqueMsg(), "该产品已存在营销活动，为了防止叠加折扣，不允许重复新增！"));
                continue;
            }
            // 核验导入公有属性
            boolean checkCommonProperty = vcPromotionService.checkImportBasic(importVo, importInfoVo, errors);
            if (checkCommonProperty) {
                // 处理价格：「折扣」或者「活动价格」
                handleImportPrice(importVo);
            }
            // 币种
            importVo.setCurrency(importInfoVo.getCurrency());
        }
        return errors;
    }

    /**
     * 处理导入价格
     *
     * @param importVo 导入行数据
     */
    private void handleImportPrice(SomVcPriceDiscountImportVo importVo) {
        // 「折扣」或者「活动价格」任填其一，需要计算出另一个的值，如果两个都填写了，忽略
        if (importVo.getDiscount() == null && importVo.getLikelyPromotionalPrice() != null) {
            //「折扣」 = 1 - (「活动价格」 / RRP)
            BigDecimal temp = importVo.getLikelyPromotionalPrice().divide(importVo.getRecommendedRetailPrice(), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal discount = BigDecimal.ONE.subtract(temp).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setDiscount(discount);
        } else if (importVo.getDiscount() != null && importVo.getLikelyPromotionalPrice() == null) {
            // 「活动价格」 = RRP - RRP * 「折扣」= RRP (1 - 折扣)
            BigDecimal likelyPromotionalPrice = importVo.getRecommendedRetailPrice().multiply(BigDecimal.ONE.subtract(importVo.getDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setLikelyPromotionalPrice(likelyPromotionalPrice);
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        } else {
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        }
        // Funding = RRP - 活动价格
        BigDecimal funding = importVo.getRecommendedRetailPrice().subtract(importVo.getLikelyPromotionalPrice());
        importVo.setPerUnitFunding(funding.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    /**
     * DOTD 导入行数据基础核验
     *
     * @param importVo       行数据
     * @param dictionaryMap  字典map
     * @param errors         错误集合
     * @param repeatCheckSet 核验重复Set
     * @return boolean
     */
    private boolean checkImportFields(SomVcPriceDiscountImportVo importVo, Map<String, Map<String, McDictionaryInfo>> dictionaryMap,
                                      List<String> errors, Set<String> repeatCheckSet) {
        // 核验必填项
        String site = importVo.getSite();
        String accountName = importVo.getAccountName();
        String vendorCode = importVo.getVendorCode();
        Date startDate = importVo.getStartDate();
        Date endDate = importVo.getEndDate();
        String applyReasonStr = importVo.getApplyReasonStr();
        if ((!StrUtil.isAllNotEmpty(site, accountName, vendorCode, applyReasonStr) || startDate == null || endDate == null)) {
            errors.add("错误0：请输入必填项！");
            return false;
        }
        // 展示码和asin不允许都为空
        String sellerSku = importVo.getSellerSku();
        String asin = importVo.getAsin();
        if (StrUtil.isAllEmpty(sellerSku, asin)) {
            errors.add("错误1：展示码、ASIN不能都为空！");
            return false;
        }
        // 折扣比例、活动价格不能都为空！
        String discountStr = importVo.getDiscountStr();
        String likelyPromotionalPriceStr = importVo.getLikelyPromotionalPriceStr();
        if (StrUtil.isAllEmpty(discountStr, likelyPromotionalPriceStr)) {
            errors.add("错误2：折扣比例、活动价格不能都为空！");
            return false;
        }
        // 核验重复，以[供应商编码+展示码/ASIN]唯一
        String key = importVo.getUniqueId();
        if (repeatCheckSet.contains(key)) {
            errors.add(StrUtil.concat(true, "错误3：", importVo.getUniqueMsg(), "数据重复！"));
            return false;
        }
        repeatCheckSet.add(key);
        // 核验活动日期
        try {
            vcPromotionService.checkPromotionDate(importVo.getStartDate(), importVo.getEndDate(), true);
        } catch (ValidateException ex) {
            errors.add(StrUtil.concat(true, "错误4：", importVo.getUniqueMsg(), ex.getMessage()));
            return false;
        }
        // 核验折扣比例，存在计算，不直接转换
        if (vcPromotionService.isInvalidAmount(discountStr)) {
            errors.add(StrUtil.format("错误5：折扣比例[{}]格式有误！", discountStr));
            return false;
        }
        importVo.setDiscount(StrUtil.isEmpty(discountStr) ? null : new BigDecimal(discountStr));
        // 核验活动价格
        if (vcPromotionService.isInvalidAmount(likelyPromotionalPriceStr)) {
            errors.add(StrUtil.format("错误6：活动价格[{}]格式有误！", likelyPromotionalPriceStr));
            return false;
        }
        importVo.setLikelyPromotionalPrice(StrUtil.isEmpty(likelyPromotionalPriceStr) ? null : new BigDecimal(likelyPromotionalPriceStr));
        // 校验评分
        String scoreStr = importVo.getScoreStr();
        if (vcPromotionService.isInvalidAmount(scoreStr)) {
            errors.add(StrUtil.format("错误7：评分[{}]格式有误！", scoreStr));
            return false;
        }
        importVo.setScore(StrUtil.isEmpty(scoreStr) ? null : new BigDecimal(scoreStr));
        // 核验申请原因
        Map<String, McDictionaryInfo> applyReasonDictMap = dictionaryMap.get("VcPromotionApplyReason");
        McDictionaryInfo applyReasonDict = applyReasonDictMap.get(applyReasonStr);
        if (applyReasonDict == null) {
            errors.add(StrUtil.format("错误8：申请原因[{}]格式有误！", applyReasonStr));
            return false;
        }
        importVo.setApplyReason(Integer.valueOf(applyReasonDict.getItemValue()));
        // 如果申请原因字典 itemValue=1，需要填写自定义申请原因，itemValue!=1，填了等于没填
        String customReason = importVo.getCustomReason();
        if (NEED_CHECK.equals(applyReasonDict.getItemValue1()) && StrUtil.isEmpty(customReason)) {
            errors.add(StrUtil.format("错误9：申请原因为[{}]时，必须填写自定义申请原因！", applyReasonStr));
            return false;
        }
        importVo.setApplyReason(Integer.valueOf(applyReasonDict.getItemValue()));
        importVo.setCustomReason(NEED_CHECK.equals(applyReasonDict.getItemValue1()) ? customReason : null);
        return true;
    }

    /**
     * 获取字典
     * VcPriceDiscountStatus：VC Price Discount 营销活动申请状态
     * VcPromotionApplyReason：VC 营销活动申请原因
     * VcPromotionType：VC 营销活动类型
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getDictionaryMap() {
        List<String> typeCodes = Arrays.asList("VcPriceDiscountStatus", "VcPromotionApplyReason", "VcPromotionType");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1)));
    }

    /**
     * 获取导入字典
     * 导入文件中填充的是字典的 label
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getImportDictionaryMap() {
        List<String> typeCodes = Collections.singletonList("VcPromotionApplyReason");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1)));
    }


    /**
     * 核验并填充销售视图字段
     *
     * @param somVcPriceDiscount Price Discount 营销活动
     */
    private void checkAndFillProductSalesField(SomVcPriceDiscount somVcPriceDiscount) throws ValidateException {
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setSiteList(Collections.singletonList(somVcPriceDiscount.getSite()));
        mcProductSalesSearchVo.setDisplayProductCodeList(Collections.singletonList(somVcPriceDiscount.getSellerSku()));
        mcProductSalesSearchVo.setPlatform("VC");
        List<McProductSalesVo> productSales = productSalesMapper.getProductSales(mcProductSalesSearchVo);
        if (CollUtil.isEmpty(productSales)) {
            throw new ValidateException("该产品销售视图不存在！");
        }
        McProductSalesVo productSalesVo = productSales.get(0);
        somVcPriceDiscount.setSalesGroupCode(productSalesVo.getSalesGroupCode());
        somVcPriceDiscount.setSalesGroupName(productSalesVo.getSalesGroupName());
        somVcPriceDiscount.setOperationEmptCode(productSalesVo.getOperationEmptCode());
        somVcPriceDiscount.setOperationEmptName(productSalesVo.getOperationEmptName());
        somVcPriceDiscount.setSalesGroupEmptCode(productSalesVo.getSalesGroupEmptCode());
        somVcPriceDiscount.setSalesGroupEmptName(productSalesVo.getSalesGroupEmptName());
    }

    /**
     * 核验活动是否存在
     *
     * @param somVcPriceDiscountVo 入参
     */
    private void checkPromotionExist(SomVcPriceDiscountVo somVcPriceDiscountVo) throws ValidateException {
        SomVcPromotionCheckUniqueVo checkUniqueVo = buildCheckUniqueVo(somVcPriceDiscountVo);
        if (vcPromotionService.checkUnique(checkUniqueVo)) {
            throw new ValidateException("该产品已存在营销活动，为了防止叠加折扣，不允许重复新增！");
        }
    }

    /**
     * 核验DOTD营销活动是否存在
     *
     * @param somVcPriceDiscountVo 入参
     * @return SomVcPriceDiscount
     */
    private SomVcPriceDiscount checkSomVcPriceDiscountExist(SomVcPriceDiscountVo somVcPriceDiscountVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcPriceDiscountVo) || StrUtil.isEmpty(somVcPriceDiscountVo.getAid())) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        SomVcPriceDiscount somVcPriceDiscount = vcPriceDiscountMapper.createLambdaQuery().andEq("aid", somVcPriceDiscountVo.getAid()).single();
        if (somVcPriceDiscount == null) {
            throw new ValidateException("数据不存在或已删除！");
        }
        return somVcPriceDiscount;
    }

    /**
     * 构建查询活动是否存在参数
     *
     * @param somVcPriceDiscountVo 活动数据
     * @return SomVcPromotionCheckUniqueVo
     */
    private SomVcPromotionCheckUniqueVo buildCheckUniqueVo(SomVcPriceDiscountVo somVcPriceDiscountVo) {
        SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
        checkUniqueVo.setPriceDiscountAid(somVcPriceDiscountVo.getAid());
        checkUniqueVo.setSite(somVcPriceDiscountVo.getSite());
        checkUniqueVo.setVendorCode(somVcPriceDiscountVo.getVendorCode());
        checkUniqueVo.setSellerSku(somVcPriceDiscountVo.getSellerSku());
        checkUniqueVo.setStartDate(somVcPriceDiscountVo.getStartDate());
        checkUniqueVo.setEndDate(somVcPriceDiscountVo.getEndDate());
        return checkUniqueVo;

    }


    /**
     * 核验新增/修改参数
     *
     * @param somVcPriceDiscountVo 入参
     */
    private void checkSaveOrEditParam(SomVcPriceDiscountVo somVcPriceDiscountVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcPriceDiscountVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 获取字典
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getDictionaryMap();
        // 核验必填项
        String accountName = somVcPriceDiscountVo.getAccountName();
        String site = somVcPriceDiscountVo.getSite();
        String vendorCode = somVcPriceDiscountVo.getVendorCode();
        String sellerSku = somVcPriceDiscountVo.getSellerSku();
        if (!StrUtil.isAllNotEmpty(site, accountName, vendorCode, sellerSku)) {
            throw new ValidateException("站点、账号、Vendor Code、展示码不能为空！");
        }
        // 核验日期
        Date startDate = somVcPriceDiscountVo.getStartDate();
        Date endDate = somVcPriceDiscountVo.getEndDate();
        vcPromotionService.checkPromotionDate(startDate, endDate, true);
        // 核验价格相关
        BigDecimal discount = somVcPriceDiscountVo.getDiscount();
        BigDecimal likelyPromotionalPrice = somVcPriceDiscountVo.getLikelyPromotionalPrice();
        BigDecimal perUnitFunding = somVcPriceDiscountVo.getPerUnitFunding();
        if (!ObjectUtil.isAllNotEmpty(discount, likelyPromotionalPrice, perUnitFunding)) {
            throw new ValidateException("折扣、活动价、Funding不能为空！");
        }
        // 申请原因itemValue1为1时，自定义原因不能为空
        McDictionaryInfo applyReasonDict = dictionaryMap.get("VcPromotionApplyReason").get(String.valueOf(somVcPriceDiscountVo.getApplyReason()));
        if (applyReasonDict == null) {
            throw new ValidateException("申请原因有误！");
        }
        String needCheck = "1";
        if (needCheck.equals(applyReasonDict.getItemValue1()) && StrUtil.isEmpty(somVcPriceDiscountVo.getCustomReason())) {
            throw new ValidateException("申请原因为" + applyReasonDict.getItemValue() + "时，自定义原因不能为空！");
        }
    }

    /**
     * 构建需要更新的SomVcDotd对象属性
     *
     * @param somVcPriceDiscountVo 入参
     * @param somVcPriceDiscount 原有somVcPriceDiscount对象
     */
    private void buildSomVcPriceDiscount(SomVcPriceDiscountVo somVcPriceDiscountVo, SomVcPriceDiscount somVcPriceDiscount) {
        somVcPriceDiscount.setAccountName(somVcPriceDiscountVo.getAccountName());
        somVcPriceDiscount.setSite(somVcPriceDiscountVo.getSite());
        somVcPriceDiscount.setVendorCode(somVcPriceDiscountVo.getVendorCode());
        somVcPriceDiscount.setSellerSku(somVcPriceDiscountVo.getSellerSku());
        somVcPriceDiscount.setAsin(somVcPriceDiscountVo.getAsin());
        somVcPriceDiscount.setSku(somVcPriceDiscountVo.getSku());
        somVcPriceDiscount.setRecommendedRetailPrice(somVcPriceDiscountVo.getRecommendedRetailPrice());
        somVcPriceDiscount.setFrontSellPrice(somVcPriceDiscountVo.getFrontSellPrice());
        somVcPriceDiscount.setDmsLast30day(somVcPriceDiscountVo.getDmsLast30day());
        somVcPriceDiscount.setStockSaleDays(somVcPriceDiscountVo.getStockSaleDays());
        somVcPriceDiscount.setStartDate(somVcPriceDiscountVo.getStartDate());
        somVcPriceDiscount.setEndDate(somVcPriceDiscountVo.getEndDate());
        somVcPriceDiscount.setDiscount(somVcPriceDiscountVo.getDiscount());
        somVcPriceDiscount.setLikelyPromotionalPrice(somVcPriceDiscountVo.getLikelyPromotionalPrice());
        somVcPriceDiscount.setPerUnitFunding(somVcPriceDiscountVo.getPerUnitFunding());
        somVcPriceDiscount.setScore(somVcPriceDiscountVo.getScore());
        somVcPriceDiscount.setApplyReason(somVcPriceDiscountVo.getApplyReason());
        somVcPriceDiscount.setCustomReason(somVcPriceDiscountVo.getCustomReason());
        somVcPriceDiscount.setAsin(somVcPriceDiscountVo.getAsin());
        somVcPriceDiscount.setCurrency(somVcPriceDiscountVo.getCurrency());
    }

}
