package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
import java.math.BigDecimal;
/*
 * GoogleAds购物广告报表
 * gen by 代码生成器 2024-02-26
 */

@Table(name = "mc.som_google_ads_shopping_report")
public class SomGoogleAdsShoppingReport implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * SKU
     */
    @Column("product_item_id")
    private String productItemId;
    /**
     * 广告日期
     */
    @Column("day")
    private Date day;
    /**
     * 曝光量
     */
    @Column("impr")
    private Integer impr;
    /**
     * 点击次数
     */
    @Column("clicks")
    private Integer clicks;
    /**
     * 花费
     */
    @Column("cost")
    private Long cost;
    /**
     * 总加购数
     */
    @Column("all_conversions")
    private BigDecimal allConversions;
    /**
     * 订单转化量
     */
    @Column("conversions")
    private BigDecimal conversions;
    /**
     * 当天销售额
     */
    @Column("conversions_value")
    private BigDecimal conversionsValue;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomGoogleAdsShoppingReport() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getProductItemId() {
        return productItemId;
    }

    /**
     * SKU
     *
     * @param productItemId
     */
    public void setProductItemId(String productItemId) {
        this.productItemId = productItemId;
    }

    /**
     * 广告日期
     *
     * @return
     */
    public Date getDay() {
        return day;
    }

    /**
     * 广告日期
     *
     * @param day
     */
    public void setDay(Date day) {
        this.day = day;
    }

    /**
     * 曝光量
     *
     * @return
     */
    public Integer getImpr() {
        return impr;
    }

    /**
     * 曝光量
     *
     * @param impr
     */
    public void setImpr(Integer impr) {
        this.impr = impr;
    }

    /**
     * 点击次数
     *
     * @return
     */
    public Integer getClicks() {
        return clicks;
    }

    /**
     * 点击次数
     *
     * @param clicks
     */
    public void setClicks(Integer clicks) {
        this.clicks = clicks;
    }

    /**
     * 花费
     *
     * @return
     */
    public Long getCost() {
        return cost;
    }

    /**
     * 花费
     *
     * @param cost
     */
    public void setCost(Long cost) {
        this.cost = cost;
    }

    /**
     * 总加购数
     *
     * @return
     */
    public BigDecimal getAllConversions() {
        return allConversions;
    }

    /**
     * 总加购数
     *
     * @param allConversions
     */
    public void setAllConversions(BigDecimal allConversions) {
        this.allConversions = allConversions;
    }

    /**
     * 订单转化量
     *
     * @return
     */
    public BigDecimal getConversions() {
        return conversions;
    }

    /**
     * 订单转化量
     *
     * @param conversions
     */
    public void setConversions(BigDecimal conversions) {
        this.conversions = conversions;
    }

    /**
     * 当天销售额
     *
     * @return
     */
    public BigDecimal getConversionsValue() {
        return conversionsValue;
    }

    /**
     * 当天销售额
     *
     * @param conversionsValue
     */
    public void setConversionsValue(BigDecimal conversionsValue) {
        this.conversionsValue = conversionsValue;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
