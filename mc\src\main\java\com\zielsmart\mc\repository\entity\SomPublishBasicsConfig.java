package com.zielsmart.mc.repository.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 
* gen by 代码生成器 2025-07-28
*/

@Table(name="mc.som_publish_basics_config")
public class SomPublishBasicsConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 客户编码
	 */
	@Column("customer_code")
	private String customerCode ;
	/**
	 * 客户简称
	 */
	@Column("customer_short_name")
	private String customerShortName ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 营销渠道
	 */
	@Column("marketing_channel")
	private String marketingChannel ;
	/**
	 * 国家
	 */
	@Column("country")
	private String country ;


	@Column("country_code")
	private String countryCode ;
	/**
	 * 销售市场
	 */
	@Column("sales_market")
	private String salesMarket ;
	/**
	 * 0.非寄售  1.寄售
	 */
	@Column("consignment_sales")
	private Integer consignmentSales ;
	/**
	 * EAN公用关系（支持多选，以英文逗号分隔）：
AMZ-EU
AMZ-JP
AMZ-NA
CH-NA
WM-NA
三方-DE
三方-FR
三方-IT
三方-ES
不需要
	 */
	@Column("ean_group")
	private String eanGroup ;
	/**
	 * 0.不需要简单上货 1.需要简单上货
	 */
	@Column("simple_publish_tag")
	private Integer simplePublishTag ;
	/**
	 * 是否需要维护不可售处理  0.否  1.是
	 */
	@Column("consignment_processing_required")
	private Integer consignmentProcessingRequired ;
	/**
	 * 是否需要维护VC合作标识  0.否 1.是
	 */
	@Column("vc_cooperation_required")
	private Integer vcCooperationRequired ;
	/**
	 * 状态。枚举值：
10  启用
20  禁用
99  关站
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 展示码生成方式：
10 前缀
20 后缀
30 中间
	 */
	@Column("seller_sku_generate_type")
	private Integer sellerSkuGenerateType ;
	/**
	 * 当「展示码生成方式」为“30”时，从SKU的第几个字符开始增加特定字母
	 */
	@Column("seller_sku_generate_index")
	private Integer sellerSkuGenerateIndex ;
	/**
	 * 前缀/后缀/中间   需要增加的固定字符。
	 */
	@Column("seller_sku_generate_char")
	private String sellerSkuGenerateChar ;
	/**
	 * 从EYA同步到SOM的系统时间
	 */
	@Column("sync_time")
	private Date syncTime ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 销售市场编码
	 */
	@Column("sales_market_code")
	private String salesMarketCode ;

	public SomPublishBasicsConfig() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 客户编码
	*@return
	*/
	public String getCustomerCode(){
		return  customerCode;
	}
	/**
	* 客户编码
	*@param  customerCode
	*/
	public void setCustomerCode(String customerCode ){
		this.customerCode = customerCode;
	}
	/**
	* 客户简称
	*@return
	*/
	public String getCustomerShortName(){
		return  customerShortName;
	}
	/**
	* 客户简称
	*@param  customerShortName
	*/
	public void setCustomerShortName(String customerShortName ){
		this.customerShortName = customerShortName;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 营销渠道
	*@return
	*/
	public String getMarketingChannel(){
		return  marketingChannel;
	}
	/**
	* 营销渠道
	*@param  marketingChannel
	*/
	public void setMarketingChannel(String marketingChannel ){
		this.marketingChannel = marketingChannel;
	}
	/**
	* 国家
	*@return
	*/
	public String getCountry(){
		return  country;
	}
	/**
	* 国家
	*@param  country
	*/
	public void setCountry(String country ){
		this.country = country;
	}
	/**
	* 销售市场
	*@return
	*/
	public String getSalesMarket(){
		return  salesMarket;
	}
	/**
	* 销售市场
	*@param  salesMarket
	*/
	public void setSalesMarket(String salesMarket ){
		this.salesMarket = salesMarket;
	}
	/**
	* 0.非寄售  1.寄售
	*@return
	*/
	public Integer getConsignmentSales(){
		return  consignmentSales;
	}
	/**
	* 0.非寄售  1.寄售
	*@param  consignmentSales
	*/
	public void setConsignmentSales(Integer consignmentSales ){
		this.consignmentSales = consignmentSales;
	}
	/**
	* EAN公用关系（支持多选，以英文逗号分隔）：
AMZ-EU
AMZ-JP
AMZ-NA
CH-NA
WM-NA
三方-DE
三方-FR
三方-IT
三方-ES
不需要
	*@return
	*/
	public String getEanGroup(){
		return  eanGroup;
	}
	/**
	* EAN公用关系（支持多选，以英文逗号分隔）：
AMZ-EU
AMZ-JP
AMZ-NA
CH-NA
WM-NA
三方-DE
三方-FR
三方-IT
三方-ES
不需要
	*@param  eanGroup
	*/
	public void setEanGroup(String eanGroup ){
		this.eanGroup = eanGroup;
	}
	/**
	* 0.不需要简单上货 1.需要简单上货
	*@return
	*/
	public Integer getSimplePublishTag(){
		return  simplePublishTag;
	}
	/**
	* 0.不需要简单上货 1.需要简单上货
	*@param  simplePublishTag
	*/
	public void setSimplePublishTag(Integer simplePublishTag ){
		this.simplePublishTag = simplePublishTag;
	}
	/**
	* 是否需要维护不可售处理  0.否  1.是
	*@return
	*/
	public Integer getConsignmentProcessingRequired(){
		return  consignmentProcessingRequired;
	}
	/**
	* 是否需要维护不可售处理  0.否  1.是
	*@param  consignmentProcessingRequired
	*/
	public void setConsignmentProcessingRequired(Integer consignmentProcessingRequired ){
		this.consignmentProcessingRequired = consignmentProcessingRequired;
	}
	/**
	* 是否需要维护VC合作标识  0.否 1.是
	*@return
	*/
	public Integer getvcCooperationRequired(){
		return  vcCooperationRequired;
	}
	/**
	* 是否需要维护VC合作标识  0.否 1.是
	*@param  vcCooperationRequired
	*/
	public void setvcCooperationRequired(Integer vcCooperationRequired ){
		this.vcCooperationRequired = vcCooperationRequired;
	}
	/**
	* 状态。枚举值：
10  启用
20  禁用
99  关站
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态。枚举值：
10  启用
20  禁用
99  关站
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 展示码生成方式：
10 前缀
20 后缀
30 中间
	*@return
	*/
	public Integer getSellerSkuGenerateType(){
		return  sellerSkuGenerateType;
	}
	/**
	* 展示码生成方式：
10 前缀
20 后缀
30 中间
	*@param  sellerSkuGenerateType
	*/
	public void setSellerSkuGenerateType(Integer sellerSkuGenerateType ){
		this.sellerSkuGenerateType = sellerSkuGenerateType;
	}
	/**
	* 当「展示码生成方式」为“30”时，从SKU的第几个字符开始增加特定字母
	*@return
	*/
	public Integer getSellerSkuGenerateIndex(){
		return  sellerSkuGenerateIndex;
	}
	/**
	* 当「展示码生成方式」为“30”时，从SKU的第几个字符开始增加特定字母
	*@param  sellerSkuGenerateIndex
	*/
	public void setSellerSkuGenerateIndex(Integer sellerSkuGenerateIndex ){
		this.sellerSkuGenerateIndex = sellerSkuGenerateIndex;
	}
	/**
	* 前缀/后缀/中间   需要增加的固定字符。
	*@return
	*/
	public String getSellerSkuGenerateChar(){
		return  sellerSkuGenerateChar;
	}
	/**
	* 前缀/后缀/中间   需要增加的固定字符。
	*@param  sellerSkuGenerateChar
	*/
	public void setSellerSkuGenerateChar(String sellerSkuGenerateChar ){
		this.sellerSkuGenerateChar = sellerSkuGenerateChar;
	}
	/**
	* 从EYA同步到SOM的系统时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 从EYA同步到SOM的系统时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 最后修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 销售市场编码
	*@return
	*/
	public String getSalesMarketCode(){
		return  salesMarketCode;
	}
	/**
	* 销售市场编码
	*@param  salesMarketCode
	*/
	public void setSalesMarketCode(String salesMarketCode ){
		this.salesMarketCode = salesMarketCode;
	}

}
