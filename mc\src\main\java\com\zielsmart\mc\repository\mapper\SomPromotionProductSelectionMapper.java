package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPromotionProductSelection;
import com.zielsmart.mc.vo.SomPromotionProductSelectionPageSearchVo;
import com.zielsmart.mc.vo.SomPromotionProductSelectionVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-06-20
 */

@SqlResource("somPromotionProductSelection")
public interface SomPromotionProductSelectionMapper extends BaseMapper<SomPromotionProductSelection> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPromotionProductSelectionVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPromotionProductSelectionVo> queryByPage(@Param("searchVo") SomPromotionProductSelectionPageSearchVo searchVo, PageRequest pageRequest);
}
