package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomOfferSummary;
import com.zielsmart.mc.vo.SomOfferSummaryPageSearchVo;
import com.zielsmart.mc.vo.SomOfferSummaryVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-11-30
 */

@SqlResource("somOfferSummary")
public interface SomOfferSummaryMapper extends BaseMapper<SomOfferSummary> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOfferSummaryVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomOfferSummaryVo> queryByPage(@Param("searchVo") SomOfferSummaryPageSearchVo searchVo, PageRequest pageRequest);
}
