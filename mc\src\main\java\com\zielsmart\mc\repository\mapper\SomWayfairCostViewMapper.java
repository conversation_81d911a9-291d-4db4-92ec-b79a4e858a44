package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomWayfairCostViewPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairCostViewVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-02-28
 */

@SqlResource("somWayfairCostView")
public interface SomWayfairCostViewMapper extends BaseMapper<SomWayfairCostView> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult<SomWayfairCostViewVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWayfairCostViewVo> queryByPage(@Param("searchVo") SomWayfairCostViewPageSearchVo searchVo, PageRequest pageRequest);
}
