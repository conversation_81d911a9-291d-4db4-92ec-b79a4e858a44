package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* But fr Offers管理
* gen by 代码生成器 2025-04-01
*/

@Table(name="mc.som_but_offers")
public class SomButOffers implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 划线价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 折扣价格
	 */
	@Column("discount_price")
	private BigDecimal discountPrice ;
	/**
	 * 折扣开始时间
	 */
	@Column("discount_start_date")
	private Date discountStartDate ;
	/**
	 * 折扣结束时间
	 */
	@Column("discount_end_date")
	private Date discountEndDate ;
	/**
	 * 物流方式
	 */
	@Column("logistic")
	private String logistic ;
	/**
	 * 备货时间
	 */
	@Column("leadtime")
	private Integer leadtime ;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomButOffers() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 划线价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 划线价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 折扣价格
	*@return
	*/
	public BigDecimal getDiscountPrice(){
		return  discountPrice;
	}
	/**
	* 折扣价格
	*@param  discountPrice
	*/
	public void setDiscountPrice(BigDecimal discountPrice ){
		this.discountPrice = discountPrice;
	}
	/**
	* 折扣开始时间
	*@return
	*/
	public Date getDiscountStartDate(){
		return  discountStartDate;
	}
	/**
	* 折扣开始时间
	*@param  discountStartDate
	*/
	public void setDiscountStartDate(Date discountStartDate ){
		this.discountStartDate = discountStartDate;
	}
	/**
	* 折扣结束时间
	*@return
	*/
	public Date getDiscountEndDate(){
		return  discountEndDate;
	}
	/**
	* 折扣结束时间
	*@param  discountEndDate
	*/
	public void setDiscountEndDate(Date discountEndDate ){
		this.discountEndDate = discountEndDate;
	}
	/**
	* 物流方式
	*@return
	*/
	public String getLogistic(){
		return  logistic;
	}
	/**
	* 物流方式
	*@param  logistic
	*/
	public void setLogistic(String logistic ){
		this.logistic = logistic;
	}
	/**
	* 备货时间
	*@return
	*/
	public Integer getLeadtime(){
		return  leadtime;
	}
	/**
	* 备货时间
	*@param  leadtime
	*/
	public void setLeadtime(Integer leadtime ){
		this.leadtime = leadtime;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemark(){
		return  remark;
	}
	/**
	* 备注
	*@param  remark
	*/
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
