package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-03-08
*/

@Table(name="mc.som_free_deal_recommend_item")
public class SomFreeDealRecommendItem implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 主表的主键
	 */
	@Column("rid")
	private String rid ;
	/**
	 * asin
	 */
	@Column("asin")
	private String asin ;
	/**
	 * seller_sku
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 发货方式
	 */
	@Column("fulfillment")
	private String fulfillment ;
	/**
	 * 建议秒杀价
	 */
	@Column("deal_recommend_price")
	private BigDecimal dealRecommendPrice ;
	/**
	 * 建议的最高秒杀价
	 */
	@Column("deal_max_price")
	private BigDecimal dealMaxPrice ;
	/**
	 * 建议的最低秒杀价
	 */
	@Column("deal_min_price")
	private BigDecimal dealMinPrice ;
	/**
	 * 秒杀数量
	 */
	@Column("deal_quantity")
	private Integer dealQuantity ;
	/**
	 * 浏览次数
	 */
	@Column("glance_views")
	private Long glanceViews ;
	/**
	 * 产品的首图
	 */
	@Column("image_url")
	private String imageUrl ;
	/**
	 * 产品的评分
	 */
	@Column("item_rating")
	private Integer itemRating ;
	/**
	 * 产品的评价数
	 */
	@Column("item_rating_count")
	private Long itemRatingCount ;
	/**
	 * Listing的状态
	 */
	@Column("listing_status")
	private String listingStatus ;
	/**
	 * 品名
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 现售价
	 */
	@Column("seller_price")
	private BigDecimal sellerPrice ;
	/**
	 * 产品现库存数量
	 */
	@Column("seller_quantity")
	private Long sellerQuantity ;
	/**
	 * 此次推荐的起始时间
	 */
	@Column("recommend_start_date")
	private Date recommendStartDate ;
	/**
	 * 此次推荐的截止时间
	 */
	@Column("recommend_end_date")
	private Date recommendEndDate ;

	public SomFreeDealRecommendItem() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 主表的主键
	*@return
	*/
	public String getRid(){
		return  rid;
	}
	/**
	* 主表的主键
	*@param  rid
	*/
	public void setRid(String rid ){
		this.rid = rid;
	}
	/**
	* asin
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* asin
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* seller_sku
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* seller_sku
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 发货方式
	*@return
	*/
	public String getFulfillment(){
		return  fulfillment;
	}
	/**
	* 发货方式
	*@param  fulfillment
	*/
	public void setFulfillment(String fulfillment ){
		this.fulfillment = fulfillment;
	}
	/**
	* 建议秒杀价
	*@return
	*/
	public BigDecimal getDealRecommendPrice(){
		return  dealRecommendPrice;
	}
	/**
	* 建议秒杀价
	*@param  dealRecommendPrice
	*/
	public void setDealRecommendPrice(BigDecimal dealRecommendPrice ){
		this.dealRecommendPrice = dealRecommendPrice;
	}
	/**
	* 建议的最高秒杀价
	*@return
	*/
	public BigDecimal getDealMaxPrice(){
		return  dealMaxPrice;
	}
	/**
	* 建议的最高秒杀价
	*@param  dealMaxPrice
	*/
	public void setDealMaxPrice(BigDecimal dealMaxPrice ){
		this.dealMaxPrice = dealMaxPrice;
	}
	/**
	* 建议的最低秒杀价
	*@return
	*/
	public BigDecimal getDealMinPrice(){
		return  dealMinPrice;
	}
	/**
	* 建议的最低秒杀价
	*@param  dealMinPrice
	*/
	public void setDealMinPrice(BigDecimal dealMinPrice ){
		this.dealMinPrice = dealMinPrice;
	}
	/**
	* 秒杀数量
	*@return
	*/
	public Integer getDealQuantity(){
		return  dealQuantity;
	}
	/**
	* 秒杀数量
	*@param  dealQuantity
	*/
	public void setDealQuantity(Integer dealQuantity ){
		this.dealQuantity = dealQuantity;
	}
	/**
	* 浏览次数
	*@return
	*/
	public Long getGlanceViews(){
		return  glanceViews;
	}
	/**
	* 浏览次数
	*@param  glanceViews
	*/
	public void setGlanceViews(Long glanceViews ){
		this.glanceViews = glanceViews;
	}
	/**
	* 产品的首图
	*@return
	*/
	public String getImageUrl(){
		return  imageUrl;
	}
	/**
	* 产品的首图
	*@param  imageUrl
	*/
	public void setImageUrl(String imageUrl ){
		this.imageUrl = imageUrl;
	}
	/**
	* 产品的评分
	*@return
	*/
	public Integer getItemRating(){
		return  itemRating;
	}
	/**
	* 产品的评分
	*@param  itemRating
	*/
	public void setItemRating(Integer itemRating ){
		this.itemRating = itemRating;
	}
	/**
	* 产品的评价数
	*@return
	*/
	public Long getItemRatingCount(){
		return  itemRatingCount;
	}
	/**
	* 产品的评价数
	*@param  itemRatingCount
	*/
	public void setItemRatingCount(Long itemRatingCount ){
		this.itemRatingCount = itemRatingCount;
	}
	/**
	* Listing的状态
	*@return
	*/
	public String getListingStatus(){
		return  listingStatus;
	}
	/**
	* Listing的状态
	*@param  listingStatus
	*/
	public void setListingStatus(String listingStatus ){
		this.listingStatus = listingStatus;
	}
	/**
	* 品名
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 品名
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 现售价
	*@return
	*/
	public BigDecimal getSellerPrice(){
		return  sellerPrice;
	}
	/**
	* 现售价
	*@param  sellerPrice
	*/
	public void setSellerPrice(BigDecimal sellerPrice ){
		this.sellerPrice = sellerPrice;
	}
	/**
	* 产品现库存数量
	*@return
	*/
	public Long getSellerQuantity(){
		return  sellerQuantity;
	}
	/**
	* 产品现库存数量
	*@param  sellerQuantity
	*/
	public void setSellerQuantity(Long sellerQuantity ){
		this.sellerQuantity = sellerQuantity;
	}
	/**
	* 此次推荐的起始时间
	*@return
	*/
	public Date getRecommendStartDate(){
		return  recommendStartDate;
	}
	/**
	* 此次推荐的起始时间
	*@param  recommendStartDate
	*/
	public void setRecommendStartDate(Date recommendStartDate ){
		this.recommendStartDate = recommendStartDate;
	}
	/**
	* 此次推荐的截止时间
	*@return
	*/
	public Date getRecommendEndDate(){
		return  recommendEndDate;
	}
	/**
	* 此次推荐的截止时间
	*@param  recommendEndDate
	*/
	public void setRecommendEndDate(Date recommendEndDate ){
		this.recommendEndDate = recommendEndDate;
	}

}
