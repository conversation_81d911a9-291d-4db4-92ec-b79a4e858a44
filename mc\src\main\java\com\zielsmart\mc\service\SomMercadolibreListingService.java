package com.zielsmart.mc.service;

import com.zielsmart.mc.repository.mapper.SomMercadolibreListingMapper;
import com.zielsmart.mc.vo.SomMercadolibreListingVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageSearchVo;
import com.zielsmart.web.basic.vo.PageVo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomMercadolibreListingService {

    @Resource
    private SomMercadolibreListingMapper somMercadolibreListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomMercadolibreListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomMercadolibreListingVo> queryByPage(PageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMercadolibreListingVo> pageResult = dynamicSqlManager.getMapper(SomMercadolibreListingMapper.class).queryByPage(searchVo, pageRequest);

        return ConvertUtils.pageConvert(pageResult, SomMercadolibreListingVo.class, searchVo);
    }
}
