package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.util.AsposeUtils;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.file.FileSearchResponse;
import com.zielsmart.web.basic.file.FileStoreResponse;
import com.zielsmart.web.basic.file.s3.S3FileStoreRequest;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomWootPurchaseOrderService
 * @description
 * @date 2024-06-04 10:41:11
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomWootPurchaseOrderService {
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private SomWootPurchaseOrderMapper somWootPurchaseOrderMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private SomWootPurchaseOrderItemMapper somWootPurchaseOrderItemMapper;

    @Resource
    private SomWootPromotionMapper somWootPromotionMapper;

    @Value("${aws.s3china.bucket}")
    private String chinaBucket;

    @Value("${aws.s3china.domain}")
    private String chinaDomain;

    @Resource
    private S3ChinaFileService s3ChinaFileService;

    @Resource
    private McProductSalesMapper mcProductSalesMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private McSkuInfoMapper mcSkuInfoMapper;

    @Value("${magic.head.token}")
    private String token;
    @Resource
    private IMagicService iMagicService;

    @Resource
    private SomWootPromotionService somWootPromotionService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomWootPurchaseOrderVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWootPurchaseOrderVo> queryByPage(SomWootPurchaseOrderPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomWootPurchaseOrderVo> pageResult = dynamicSqlManager.getMapper(SomWootPurchaseOrderMapper.class).queryByPage(searchVo, pageRequest);
        List<SomWootPurchaseOrderVo> result = pageResult.getList();
        if (CollUtil.isNotEmpty(result)) {
            // 查询状态字典值
            List<McDictionaryInfo> dictList = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "WootPaymentStatus").select();
            Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            List<String> aids = pageResult.getList().stream().map(SomWootPurchaseOrderVo::getAid).collect(Collectors.toList());
            List<SomWootPurchaseOrderItem> items = somWootPurchaseOrderItemMapper.createLambdaQuery().andIn("m_aid", aids).select();
            Map<String, List<SomWootPurchaseOrderItem>> itemsMap = items.stream().collect(Collectors.groupingBy(SomWootPurchaseOrderItem::getMAid));
            result.forEach(data -> {
                List<SomWootPurchaseOrderItemVo> itemVos = transToVo(itemsMap.get(data.getAid()));
                data.setTotalQuantity(itemVos.stream().mapToInt(SomWootPurchaseOrderItemVo::getQuantity).sum());
                data.setTotalSalesVolume(itemVos.stream().mapToInt(f -> f.getSalesVolume() == null ? 0 : f.getSalesVolume()).sum());
                data.setAmount(itemVos.stream().map(f -> BigDecimal.valueOf(Math.max((f.getQuantity() - (f.getReturnPurchaseOrderQuantity() == null ? 0 : f.getReturnPurchaseOrderQuantity())), 0)).multiply(f.getWootPrice() == null ? BigDecimal.ZERO : f.getWootPrice())).reduce(BigDecimal.ZERO, BigDecimal::add));
                data.setPaymentStatusDesc(dictMap.get(data.getPaymentStatus() + ""));
                data.setItemList(itemVos);
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomWootPurchaseOrderVo.class, searchVo);
    }

    /**
     * somWootPurchaseOrderItems -> somWootPurchaseOrderItemVos
     *
     * @param purchaseOrderItems items
     * @return List<SomWootPurchaseOrderItemVo>
     */
    private List<SomWootPurchaseOrderItemVo> transToVo(List<SomWootPurchaseOrderItem> purchaseOrderItems) {
        if (CollUtil.isEmpty(purchaseOrderItems)) {
            return Collections.emptyList();
        }
        return purchaseOrderItems.stream().map(item -> {
            SomWootPurchaseOrderItemVo orderItemVo = new SomWootPurchaseOrderItemVo();
            orderItemVo.setAid(item.getAid());
            orderItemVo.setMAid(item.getMAid());
            orderItemVo.setPurchaseOrder(item.getPurchaseOrder());
            orderItemVo.setWin(item.getWin());
            orderItemVo.setAsin(item.getAsin());
            orderItemVo.setSellerSku(item.getSellerSku());
            orderItemVo.setQuantity(item.getQuantity());
            orderItemVo.setSalesVolume(item.getSalesVolume());
            orderItemVo.setReturnPurchaseOrderQuantity(item.getReturnPurchaseOrderQuantity());
            orderItemVo.setWootPrice(item.getWootPrice());
            return orderItemVo;
        }).collect(Collectors.toList());
    }

    /**
     * save
     * 新增/编辑
     *
     * @param somWootPurchaseOrderVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(SomWootPurchaseOrderVo somWootPurchaseOrderVo, TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(somWootPurchaseOrderVo) || StrUtil.isBlank(somWootPurchaseOrderVo.getVendorId()) ||
                StrUtil.isBlank(somWootPurchaseOrderVo.getPurchaseOrder()) || StrUtil.isBlank(somWootPurchaseOrderVo.getBuyer()) ||
                ObjectUtil.isEmpty(somWootPurchaseOrderVo.getPurchaseOrderDate()) || ObjectUtil.isEmpty(somWootPurchaseOrderVo.getStartDate()) ||
                ObjectUtil.isEmpty(somWootPurchaseOrderVo.getEndDate()) || StrUtil.isBlank(somWootPurchaseOrderVo.getDealType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isBlank(somWootPurchaseOrderVo.getAid())) {
            long count = somWootPurchaseOrderMapper.createLambdaQuery().andEq("purchase_order", somWootPurchaseOrderVo.getPurchaseOrder()).count();
            if (count > 0) {
                throw new ValidateException("Purchase Order Number已存在，不允许重复");
            }
        }
        if (StrUtil.isNotBlank(somWootPurchaseOrderVo.getReturnOrder())) {
            long reCount;
            if (StrUtil.isBlank(somWootPurchaseOrderVo.getAid())) {
                reCount = somWootPurchaseOrderMapper.createLambdaQuery().andEq("return_order", somWootPurchaseOrderVo.getReturnOrder()).count();
            } else {
                reCount = somWootPurchaseOrderMapper.createLambdaQuery().andEq("return_order", somWootPurchaseOrderVo.getReturnOrder()).andNotEq("aid", somWootPurchaseOrderVo.getAid()).count();
            }
            if (reCount > 0) {
                throw new ValidateException("Return Order Number已存在，不允许重复");
            }
            if (ObjectUtil.isEmpty(somWootPurchaseOrderVo.getReturnOrderDate())) {
                throw new ValidateException("填写了Return Order Number，Return Order Date不能为空");
            }
        }
        Date now = DateTime.now().toJdkDate();
        somWootPurchaseOrderVo.setCreateNum(tokenUser.getJobNumber());
        somWootPurchaseOrderVo.setCreateName(tokenUser.getUserName());
        somWootPurchaseOrderVo.setCreateTime(now);
        somWootPurchaseOrderVo.setPaymentStatus(10);
        List<SomWootPurchaseOrderItemVo> itemVoList = new ArrayList<>();
        if (StrUtil.isBlank(somWootPurchaseOrderVo.getAid())) {
            somWootPurchaseOrderVo.setAid(IdUtil.fastSimpleUUID());
            somWootPurchaseOrderMapper.insert(ConvertUtils.beanConvert(somWootPurchaseOrderVo, SomWootPurchaseOrder.class));
        } else {
            somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", somWootPurchaseOrderVo.getAid()).updateSelective(ConvertUtils.beanConvert(somWootPurchaseOrderVo, SomWootPurchaseOrder.class));
            //删除明细表数据
            somWootPurchaseOrderItemMapper.createLambdaQuery().andEq("m_aid", somWootPurchaseOrderVo.getAid()).delete();
        }
        List<String> sellerSkuList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(somWootPurchaseOrderVo.getItemList())) {
            sellerSkuList = somWootPurchaseOrderVo.getItemList().stream().map(SomWootPurchaseOrderItemVo::getSellerSku).distinct().collect(Collectors.toList());
            somWootPurchaseOrderVo.getItemList().forEach(f -> {
                SomWootPurchaseOrderItemVo itemVo = new SomWootPurchaseOrderItemVo();
                itemVo.setAid(IdUtil.fastSimpleUUID());
                itemVo.setMAid(somWootPurchaseOrderVo.getAid());
                itemVo.setWin(f.getWin());
                itemVo.setAsin(f.getAsin());
                itemVo.setSellerSku(f.getSellerSku());
                itemVo.setQuantity(f.getQuantity());
                itemVo.setReturnPurchaseOrderQuantity(f.getReturnPurchaseOrderQuantity());
                itemVo.setSalesVolume(f.getSalesVolume());
                itemVo.setWootPrice(f.getWootPrice());
                itemVo.setPurchaseOrder(somWootPurchaseOrderVo.getPurchaseOrder());
                itemVo.setCreateNum(tokenUser.getJobNumber());
                itemVo.setCreateName(tokenUser.getUserName());
                itemVo.setCreateTime(now);
                itemVoList.add(itemVo);
            });
        }
        if (CollectionUtil.isNotEmpty(itemVoList)) {
            somWootPurchaseOrderItemMapper.insertBatch(ConvertUtils.listConvert(itemVoList, SomWootPurchaseOrderItem.class));
        }
        //更新营销活动状态
        int fbaStockFlag;
        if ("10".equals(somWootPurchaseOrderVo.getShipFrom())) {
            fbaStockFlag = 1;
        } else {
            fbaStockFlag = 0;
        }
        List<SomWootPromotion> promotionList = somWootPromotionMapper.createLambdaQuery()
                .andEq("status", 40)
                .andEq("fba_stock_flag", fbaStockFlag)
                .andEq("deal_type", somWootPurchaseOrderVo.getDealType())
                .andIn("seller_sku", sellerSkuList)
                .select();
        if (CollectionUtil.isNotEmpty(promotionList)) {
            List<String> aidList = promotionList.stream().map(SomWootPromotion::getAid).collect(Collectors.toList());
            SomWootPromotion promotion = new SomWootPromotion();
            promotion.setPurchaseOrder(somWootPurchaseOrderVo.getPurchaseOrder());
            promotion.setStatus(50);
            somWootPromotionMapper.createLambdaQuery().andIn("aid", aidList).updateSelective(promotion);
            //判断编辑的时候是否有删除展示码数据，如果有，需要把营销活动表中改展示码对应的purchaseOrder改为空， 状态改为“提报中”
            List<String> finalSellerSkuList = sellerSkuList;
            List<SomWootPromotion> differenceList = promotionList.stream().filter(f -> !finalSellerSkuList.contains(f.getSellerSku())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(differenceList)) {
                List<String> difAidList = differenceList.stream().map(SomWootPromotion::getAid).collect(Collectors.toList());
                SomWootPromotion difPromotion = new SomWootPromotion();
                difPromotion.setPurchaseOrder("");
                difPromotion.setStatus(40);
                somWootPromotionMapper.createLambdaQuery().andIn("aid", difAidList).updateSelective(difPromotion);
                promotionList.removeAll(differenceList);
            }
            // 发送飞书消息提醒
            SomWootPurchaseOrderNoticeVo noticeVo = buildNoticeVo(somWootPurchaseOrderVo, 50);
            FeiShuUtils.sendWootSubmitSuccessNotice(noticeVo);
            // woot 营销活动状态变更通知
            if (!promotionList.isEmpty()) {
                somWootPromotionService.wootStatusChangeNotice(promotionList, "状态变更：这批产品的状态由‘提报中’变更为‘提报成功’");
            }

        }
    }

    /**
     * editPaymentStatus
     * 编辑付款进度
     *
     * @param somWootPurchaseOrderVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void editPaymentStatus(SomWootPurchaseOrderVo somWootPurchaseOrderVo) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(somWootPurchaseOrderVo) || StrUtil.isEmpty(somWootPurchaseOrderVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        SomWootPurchaseOrder order = new SomWootPurchaseOrder();
        order.setAid(somWootPurchaseOrderVo.getAid());
        order.setPaymentStatus(somWootPurchaseOrderVo.getPaymentStatus());
        if (somWootPurchaseOrderVo.getPaymentStatus() == 20) {
            Date now = DateTime.now().toJdkDate();
            order.setEstimatePaymentDate(DateUtils.addDays(now, 90));
        }
        order.setCashRemark(somWootPurchaseOrderVo.getCashRemark());
        somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", somWootPurchaseOrderVo.getAid()).updateSelective(order);
        //发送飞书消息提醒
        if (somWootPurchaseOrderVo.getPaymentStatus() == 20 && StrUtil.isNotBlank(somWootPurchaseOrderVo.getReturnOrder())) {
            List<SomWootPurchaseOrderItemVo> itemVoList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(somWootPurchaseOrderVo.getItemList())) {
                itemVoList = somWootPurchaseOrderVo.getItemList().stream().filter(f -> null != f.getReturnPurchaseOrderQuantity() && f.getReturnPurchaseOrderQuantity() != 0).collect(Collectors.toList());
            }
            somWootPurchaseOrderVo.setItemList(itemVoList);
            SomWootPurchaseOrderNoticeVo noticeVo = buildNoticeVo(somWootPurchaseOrderVo, 20);
            FeiShuUtils.sendWootStorageNotice(noticeVo);

            // 发送给销售负责人
            List<String> sellerSkuList = itemVoList.stream().map(SomWootPurchaseOrderItemVo::getSellerSku).collect(Collectors.toList());
            List<McProductSales> productSalesList = dynamicSqlManager.getMapper(McProductSalesMapper.class)
                    .createLambdaQuery()
                    .andIn("display_product_code", sellerSkuList)
                    .andEq("site", "Woot.dropship")
                    .andEq("is_enabled", 1)
                    .select("display_product_code", "sales_group_empt_code");
            Map<String, McProductSales> productSalesMap = productSalesList.stream().collect(Collectors.toMap(
                    McProductSales::getDisplayProductCode,
                    Function.identity(),
                    (existing, replacement) -> existing
            ));
            Map<String, List<SomWootPurchaseOrderItemVo>> itemVoMap = itemVoList.stream()
                    .collect(Collectors.groupingBy(SomWootPurchaseOrderItemVo::getSellerSku));

            Map<String, List<SomWootPurchaseOrderItemVo>> noticeToUserDataMap = new HashMap<>();
            productSalesMap.values().forEach(productSales -> {
                String displayProductCode = productSales.getDisplayProductCode();
                List<SomWootPurchaseOrderItemVo> items = itemVoMap.get(displayProductCode);
                if (items != null) {
                    String salesGroupEmptCode = productSales.getSalesGroupEmptCode();
                    noticeToUserDataMap.computeIfAbsent(salesGroupEmptCode, k -> new ArrayList<>()).addAll(items);
                }
            });

            for (Map.Entry<String, List<SomWootPurchaseOrderItemVo>> entry : noticeToUserDataMap.entrySet()) {
                String salesGroupEmptCode = entry.getKey();
                List<SomWootPurchaseOrderItemVo> list = entry.getValue();
                somWootPurchaseOrderVo.setItemList(list);
                SomWootPurchaseOrderNoticeVo noticeToUserVo = buildNoticeVo(somWootPurchaseOrderVo, 20);
                FeiShuUtils.sendWootStorageNoticeToUser(salesGroupEmptCode, noticeToUserVo);
            }
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somWootPurchaseOrderVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(SomWootPurchaseOrderVo somWootPurchaseOrderVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWootPurchaseOrderVo) || StrUtil.isBlank(somWootPurchaseOrderVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        //region 更新Woot营销活动状态为提报中
        SomWootPurchaseOrder order = somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", somWootPurchaseOrderVo.getAid()).single();
        String purchaseOrder = order.getPurchaseOrder();
        List<SomWootPurchaseOrderItem> itemList = somWootPurchaseOrderItemMapper.createLambdaQuery().andEq("m_aid", somWootPurchaseOrderVo.getAid()).select();
        List<String> sellerSkuList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemList)) {
            sellerSkuList = itemList.stream().map(SomWootPurchaseOrderItem::getSellerSku).collect(Collectors.toList());
        }
        int fbaStockFlag = 0;
        if (StrUtil.equals("10", order.getShipFrom())) {
            fbaStockFlag = 1;
        }
        List<SomWootPromotion> promotionList = somWootPromotionMapper.createLambdaQuery().andEq("purchase_order", purchaseOrder).andEq("status", 50)
                .andEq("fba_stock_flag", fbaStockFlag).andIn("seller_sku", sellerSkuList).select();
        if (CollectionUtil.isNotEmpty(promotionList)) {
            List<String> aidList = promotionList.stream().map(SomWootPromotion::getAid).collect(Collectors.toList());
            SomWootPromotion promotion = new SomWootPromotion();
            promotion.setPurchaseOrder("");
            promotion.setStatus(40);
            somWootPromotionMapper.createLambdaQuery().andIn("aid", aidList).updateSelective(promotion);
            //营销活动状态变更通知
            somWootPromotionService.wootStatusChangeNotice(promotionList, "状态变更：这批产品的状态由‘提报成功’变更为‘提报中’");
        }
        //endregion
        //region 删除S3文件
        String purchaseOrderFileUrl = "";
        String returnOrderFileUrl = "";
        String invoiceFileUrl = "";
        if (StrUtil.isNotBlank(order.getPurchaseOrderFileUrl())) {
            purchaseOrderFileUrl = order.getPurchaseOrderFileUrl().substring(order.getPurchaseOrderFileUrl().indexOf("/som") + 1);
        }
        if (StrUtil.isNotBlank(order.getReturnOrderFileUrl())) {
            returnOrderFileUrl = order.getReturnOrderFileUrl().substring(order.getReturnOrderFileUrl().indexOf("/som") + 1);
        }
        if (StrUtil.isNotBlank(order.getInvoiceFileUrl())) {
            invoiceFileUrl = order.getInvoiceFileUrl().substring(order.getInvoiceFileUrl().indexOf("/som") + 1);
        }
        List<String> pathList = new ArrayList<>();
        if (StrUtil.isNotBlank(purchaseOrderFileUrl)) {
            pathList.add(purchaseOrderFileUrl);
        }
        if (StrUtil.isNotBlank(returnOrderFileUrl)) {
            pathList.add(returnOrderFileUrl);
        }
        if (StrUtil.isNotBlank(invoiceFileUrl)) {
            pathList.add(invoiceFileUrl);
        }
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(pathList)) {
            map.put("filePath", pathList);
            s3ChinaFileService.delete(map);
        }
        //endregion
        somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", somWootPurchaseOrderVo.getAid()).delete();
        somWootPurchaseOrderItemMapper.createLambdaQuery().andEq("m_aid", somWootPurchaseOrderVo.getAid()).delete();
    }

    /**
     * queryByAsin
     * 根据ASIN查询营销活动，获取展示码和促销价格
     *
     * @param asinQueryVo 入参
     * @return {@link com.zielsmart.mc.vo.SomWootPromotionVo}
     * <AUTHOR>
     * @history
     */
    public SomWootPromotionVo queryByAsin(SomWootPurchaseOrderAsinQueryVo asinQueryVo) throws ValidateException {
        if (StrUtil.isBlank(asinQueryVo.getAsin()) || StrUtil.isBlank(asinQueryVo.getDealType())) {
            throw new ValidateException("ASIN、Deal类型不能为空");
        }
        SomWootPromotion promotion = somWootPromotionMapper.createLambdaQuery()
                .andEq("asin", asinQueryVo.getAsin())
                .andEq("status", 40)
                .andEq("deal_type", asinQueryVo.getDealType())
                .single();
        SomWootPromotionVo promotionVo = new SomWootPromotionVo();
        if (promotion != null) {
            promotionVo.setSellerSku(promotion.getSellerSku());
            promotionVo.setPromotionPrice(promotion.getPromotionPrice());
        }
        return promotionVo;
    }

    /**
     * uploadFile
     * 上传附件
     *
     * @param param
     * @param multipartFile
     * @return {@link java.lang.String}
     * @throws {@link IOException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public String uploadFile(Map param, MultipartFile multipartFile) throws IOException {
        Map<String, String> metaMap = new HashMap<>();
        String savePath = "som/wootPurchaseOrder";
        if (param.get("purchaseOrder") != null && param.get("typeName") != null) {
            savePath = savePath + "/" + param.get("purchaseOrder") + "/" + param.get("typeName") + "/";
        }
        S3FileStoreRequest fileStoreRequest = S3FileStoreRequest.builder().build();
        if (metaMap.size() > 0) {
            fileStoreRequest.setMetaMap(metaMap);
        }
        String fileContent = Base64.getEncoder().encodeToString(multipartFile.getBytes());
        //设置文件的内容
        fileStoreRequest.setFileBase64Content(fileContent);
        //设置桶名称
        fileStoreRequest.setBucketName(chinaBucket);
        //是否生成随机名称
        fileStoreRequest.setUseRandomName(true);
        //保存的目录路径
        fileStoreRequest.setSavePath(savePath);
        //保存的文件名字
        fileStoreRequest.setFileName(multipartFile.getOriginalFilename());
        FileStoreResponse response = s3ChinaFileService.save(fileStoreRequest);
        String filePath = response.getFilePath();
        String fileName = response.getFileName();
        String url = chinaDomain + filePath;
        JSONObject json = new JSONObject();
        json.putOpt("filePath", filePath);
        json.putOpt("fileName", fileName);
        json.putOpt("url", url);
        json.putOpt("purchaseOrder", param.get("purchaseOrder").toString());
        json.putOpt("type", param.get("type").toString());
        SomWootPurchaseOrder order = somWootPurchaseOrderMapper.createLambdaQuery().andEq("purchase_order", json.getStr("purchaseOrder")).single();
        String purchaseOrderFileUrl = "";
        String returnOrderFileUrl = "";
        String invoiceFileUrl = "";
        if (order != null) {
            if (StrUtil.isNotBlank(order.getPurchaseOrderFileUrl())) {
                purchaseOrderFileUrl = order.getPurchaseOrderFileUrl().substring(order.getPurchaseOrderFileUrl().indexOf("/som") + 1);
            }
            if (StrUtil.isNotBlank(order.getReturnOrderFileUrl())) {
                returnOrderFileUrl = order.getReturnOrderFileUrl().substring(order.getReturnOrderFileUrl().indexOf("/som") + 1);
            }
            if (StrUtil.isNotBlank(order.getInvoiceFileUrl())) {
                invoiceFileUrl = order.getInvoiceFileUrl().substring(order.getInvoiceFileUrl().indexOf("/som") + 1);
            }
        }
        Map map = new HashMap();
        SomWootPurchaseOrderVo orderVo = new SomWootPurchaseOrderVo();
        orderVo.setPurchaseOrder(json.getStr("purchaseOrder"));
        if (StrUtil.equals("1", json.getStr("type"))) {
            if (StrUtil.isNotBlank(purchaseOrderFileUrl)) {
                map.put("filePath", CollectionUtil.toList(purchaseOrderFileUrl));
                s3ChinaFileService.delete(map);
            }
            orderVo.setPurchaseOrderFileUrl(json.getStr("url"));
        }
        if (StrUtil.equals("2", json.getStr("type"))) {
            if (StrUtil.isNotBlank(returnOrderFileUrl)) {
                map.put("filePath", CollectionUtil.toList(returnOrderFileUrl));
                s3ChinaFileService.delete(map);
            }
            orderVo.setReturnOrderFileUrl(json.getStr("url"));
        }
        if (StrUtil.equals("3", json.getStr("type"))) {
            if (StrUtil.isNotBlank(invoiceFileUrl)) {
                map.put("filePath", CollectionUtil.toList(invoiceFileUrl));
                s3ChinaFileService.delete(map);
            }
            orderVo.setInvoiceFileUrl(json.getStr("url"));
        }
        somWootPurchaseOrderMapper.createLambdaQuery().andEq("purchase_order", orderVo.getPurchaseOrder()).updateSelective(ConvertUtils.beanConvert(orderVo, SomWootPurchaseOrder.class));
        return json.getStr("url");
    }

    /**
     * downloadFile
     * 下载文件
     *
     * @param param
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public String downloadFile(Map param) throws Exception {
        String result = null;
        if (param.get("aid") != null && param.get("type") != null) {
            SomWootPurchaseOrder order = somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", param.get("aid").toString()).single();
            String filePath = "";
            if ("1".equals(String.valueOf(param.get("type")))) {
                filePath = order.getPurchaseOrderFileUrl().substring(order.getPurchaseOrderFileUrl().indexOf("/som") + 1);
            }
            if ("2".equals(String.valueOf(param.get("type")))) {
                filePath = order.getReturnOrderFileUrl().substring(order.getReturnOrderFileUrl().indexOf("/som") + 1);
            }
            if ("3".equals(String.valueOf(param.get("type")))) {
                filePath = order.getInvoiceFileUrl().substring(order.getInvoiceFileUrl().indexOf("/som") + 1);
            }
            FileSearchResponse response = s3ChinaFileService.download(filePath);
            result = response.getFileBase64Content();
        }
        return result;
    }

    /**
     * generateInvoice
     * 生成发票
     *
     * @param orderVo
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateInvoice(SomWootPurchaseOrderVo orderVo) throws Exception {
        Date now = DateTime.now().toJdkDate();
        String invoiceDate = DateUtil.format(now, "yyyy-MM-dd");
        String date = DateUtil.format(now, "yyyyMMdd");
        String template;
        Workbook workbook;
        WorkbookDesigner designer = null;
        String prefix = "US$";
        List<SomWootPurchaseOrderInvoiceVo> invoiceVoList = new ArrayList<>();
        List<SomWootPurchaseOrderItem> orderItemList = new ArrayList<>();
        //查询所有的Woot营销活动
        List<SomWootPromotion> promotionList = somWootPromotionMapper.all();
        //查询产品基础视图
        List<McSkuInfo> skuInfoList = mcSkuInfoMapper.all();
        if (StrUtil.equals("10", orderVo.getShipFrom())) {
            template = "static/excel/WootFBAInvoiceTemplate.xlsx";
            AsposeUtils.registerCells();
            try (InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(template)) {
                workbook = new Workbook(templateStream);
                designer = new WorkbookDesigner();
                designer.setWorkbook(workbook);
                designer.setDataSource("Invoice_Num", orderVo.getPurchaseOrder() + "I");
                designer.setDataSource("Purchase_Order", orderVo.getPurchaseOrder());
                designer.setDataSource("Invoice_Date", invoiceDate);
                int totalCount = 0;
                BigDecimal total = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(orderVo.getItemList())) {
                    for (SomWootPurchaseOrderItemVo itemVo : orderVo.getItemList()) {
                        Integer quantity = 0;
                        SomWootPurchaseOrderInvoiceVo invoiceVo = new SomWootPurchaseOrderInvoiceVo();
                        invoiceVo.setItemNo(itemVo.getSellerSku());
                        //根据purchaseOrder+展示码查询promotion表中的提报数量
                        //2024.6.19 发票中每行明细的数量更改为明细表中填写的数量-退回数量
                        quantity = Math.max((itemVo.getQuantity() - (itemVo.getReturnPurchaseOrderQuantity() == null ? 0 : itemVo.getReturnPurchaseOrderQuantity())), 0);
                        invoiceVo.setQuantity(quantity);
                        //BigDecimal price = promotion.getPromotionPrice() == null ? BigDecimal.ZERO : promotion.getPromotionPrice();
                        BigDecimal price = itemVo.getWootPrice() == null ? BigDecimal.ZERO : itemVo.getWootPrice();
                        BigDecimal amount = price.multiply(BigDecimal.valueOf(quantity));
                        invoiceVo.setPrice(price);
                        invoiceVo.setPriceStr(prefix + price);
                        invoiceVo.setAmount(amount);
                        invoiceVo.setAmountStr(prefix + amount);
                        Optional<SomWootPromotion> promotionOptional = promotionList.stream().filter(f -> StrUtil.equals(f.getPurchaseOrder(), orderVo.getPurchaseOrder()) && StrUtil.equals(f.getSellerSku(), itemVo.getSellerSku())).findFirst();
                        if (promotionOptional.isPresent()) {
                            SomWootPromotion promotion = promotionOptional.get();
                            skuInfoList.stream().filter(f -> StrUtil.equals(promotion.getSku(), f.getSkuCode())).findFirst().ifPresent(x -> {
                                invoiceVo.setDescription(x.getProductNameEn());
                            });
                        }
                        invoiceVoList.add(invoiceVo);
                        //设置实际销量
                        SomWootPurchaseOrderItem orderItem = new SomWootPurchaseOrderItem();
                        orderItem.setAid(itemVo.getAid());
                        orderItem.setSalesVolume(quantity);
                        orderItemList.add(orderItem);
                    }
                }
                if (CollectionUtil.isNotEmpty(invoiceVoList)) {
                    invoiceVoList = invoiceVoList.stream().filter(f -> null != f.getQuantity() && f.getQuantity() != 0).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(invoiceVoList)) {
                        totalCount = invoiceVoList.stream().mapToInt(f -> f.getQuantity() == null ? 0 : f.getQuantity()).sum();
                        total = invoiceVoList.stream().map(f -> f.getAmount() == null ? BigDecimal.ZERO : f.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                designer.setDataSource("Invoice_TotalCount", totalCount);
                designer.setDataSource("Invoice_Total", prefix + total);
                designer.setDataSource("Invoice", invoiceVoList);
                designer.process();
            }
        }
        if (StrUtil.equals("20", orderVo.getShipFrom())) {
            template = "static/excel/WootDropshipInvoiceTemplate.xlsx";
            AsposeUtils.registerCells();
            //查询产品销售视图
            List<McProductSales> salesList = mcProductSalesMapper.createLambdaQuery().andEq("site", "Woot.dropship").andEq("is_enabled", 1).select();
            ResultVo<List<SomWootPurchaseOrderInvoiceExtVo>> resultVo = iMagicService.getWootOrder(token, orderVo);
            if (!resultVo.isSuccess()) {
                throw new ValidateException("获取展示码销量接口异常：" + resultVo.getMessage());
            }
            int totalCount = 0;
            BigDecimal total = BigDecimal.ZERO;
            List<SomWootPurchaseOrderInvoiceExtVo> extVoList = resultVo.getData();
            try (InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(template)) {
                workbook = new Workbook(templateStream);
                designer = new WorkbookDesigner();
                designer.setWorkbook(workbook);
                designer.setDataSource("Invoice_Num", orderVo.getPurchaseOrder() + "I");
                designer.setDataSource("Purchase_Order", orderVo.getPurchaseOrder());
                designer.setDataSource("Invoice_Date", invoiceDate);
                if (CollectionUtil.isNotEmpty(orderVo.getItemList())) {
                    for (SomWootPurchaseOrderItemVo itemVo : orderVo.getItemList()) {
                        Integer quantity = 0;
                        SomWootPurchaseOrderInvoiceVo invoiceVo = new SomWootPurchaseOrderInvoiceVo();
                        invoiceVo.setItemNo(itemVo.getSellerSku());
                        if (CollectionUtil.isNotEmpty(extVoList)) {
                            quantity = extVoList.stream().filter(f -> StrUtil.equals(itemVo.getSellerSku(), f.getSellerSkuCode())).findFirst().orElse(new SomWootPurchaseOrderInvoiceExtVo()).getSkuNumber();
                            if (quantity == null) {
                                quantity = 0;
                            }
                        }
                        invoiceVo.setQuantity(quantity);
                        BigDecimal price = itemVo.getWootPrice() == null ? BigDecimal.ZERO : itemVo.getWootPrice();
                        BigDecimal amount = price.multiply(BigDecimal.valueOf(quantity));
                        invoiceVo.setPrice(price);
                        invoiceVo.setPriceStr(prefix + price);
                        invoiceVo.setAmount(amount);
                        invoiceVo.setAmountStr(prefix + amount);
                        String sku = salesList.stream().filter(f -> StrUtil.equals(f.getDisplayProductCode(), itemVo.getSellerSku())).findFirst().orElse(new McProductSales()).getProductMainCode();
                        if (StrUtil.isNotBlank(sku)) {
                            skuInfoList.stream().filter(f -> StrUtil.equals(sku, f.getSkuCode())).findFirst().ifPresent(x -> {
                                invoiceVo.setDescription(x.getProductNameEn());
                            });
                        }
                        invoiceVoList.add(invoiceVo);
                        //设置实际销量
                        SomWootPurchaseOrderItem orderItem = new SomWootPurchaseOrderItem();
                        orderItem.setAid(itemVo.getAid());
                        orderItem.setSalesVolume(quantity);
                        orderItemList.add(orderItem);
                    }
                }
                if (CollectionUtil.isNotEmpty(invoiceVoList)) {
                    invoiceVoList = invoiceVoList.stream().filter(f -> null != f.getQuantity() && f.getQuantity() != 0).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(invoiceVoList)) {
                        totalCount = invoiceVoList.stream().mapToInt(f -> f.getQuantity() == null ? 0 : f.getQuantity()).sum();
                        total = invoiceVoList.stream().map(f -> f.getAmount() == null ? BigDecimal.ZERO : f.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                designer.setDataSource("Invoice_TotalCount", totalCount);
                designer.setDataSource("Invoice_Total", prefix + total);
                designer.setDataSource("Invoice", invoiceVoList);
                designer.process();
            }
        }
        //发票上传到S3
        try (ByteArrayOutputStream saveStream = new ByteArrayOutputStream()) {
            designer.getWorkbook().save(saveStream, com.aspose.cells.SaveFormat.PDF);
            InputStream inputStream = new ByteArrayInputStream(saveStream.toByteArray());
            MultipartFile multipartFile = streamToMultipartFile(inputStream, "invoice.pdf");
            Map<String, String> map = new HashMap();
            map.put("purchaseOrder", orderVo.getPurchaseOrder());
            map.put("typeName", "invoice");
            map.put("type", "3");
            String url = uploadFile(map, multipartFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        //更新som_woot_purchase_order_item表中的“实际销量”字段
        if (CollectionUtil.isNotEmpty(orderItemList)) {
            somWootPurchaseOrderItemMapper.batchUpdate(orderItemList);
        }
        //更新som_woot_purchase_order表状态
        SomWootPurchaseOrder somWootPurchaseOrder = new SomWootPurchaseOrder();
        somWootPurchaseOrder.setAid(orderVo.getAid());
        somWootPurchaseOrder.setPaymentStatus(20);
        somWootPurchaseOrder.setEstimatePaymentDate(DateUtils.addDays(now, 90));
        somWootPurchaseOrderMapper.createLambdaQuery().andEq("aid", orderVo.getAid()).updateSelective(somWootPurchaseOrder);
        //发送飞书消息提醒
        if (StrUtil.isNotBlank(orderVo.getReturnOrder())) {
            List<SomWootPurchaseOrderItemVo> itemVoList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(orderVo.getItemList())) {
                itemVoList = orderVo.getItemList().stream().filter(f -> null != f.getReturnPurchaseOrderQuantity() && f.getReturnPurchaseOrderQuantity() != 0).collect(Collectors.toList());
            }
            orderVo.setItemList(itemVoList);
            FeiShuUtils.sendWootStorageNotice(buildNoticeVo(orderVo, 20));
        }
    }

    /**
     * streamToMultipartFile
     * 文件流转为上传文件
     *
     * @param inputStream
     * @param fileName
     * @return {@link org.springframework.web.multipart.MultipartFile}
     * @throws {@link IOException}
     * <AUTHOR>
     * @history
     */
    private MultipartFile streamToMultipartFile(InputStream inputStream, String fileName) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        // 将 InputStream 中的数据写入 ByteArrayOutputStream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        // 创建 MockMultipartFile
        return new MockMultipartFile(fileName, fileName, "application/pdf", byteArrayOutputStream.toByteArray());
    }

    /**
     * buildNoticeVo
     * 组装消息提醒实体对象
     *
     * @param somWootPurchaseOrderVo
     * @param status
     * @return {@link com.zielsmart.mc.vo.SomWootPurchaseOrderNoticeVo}
     * <AUTHOR>
     * @history
     */
    private SomWootPurchaseOrderNoticeVo buildNoticeVo(SomWootPurchaseOrderVo somWootPurchaseOrderVo, Integer status) {
        SomWootPurchaseOrderNoticeVo noticeVo = new SomWootPurchaseOrderNoticeVo();
        noticeVo.setPurchaseOrder(somWootPurchaseOrderVo.getPurchaseOrder());
        noticeVo.setShipFrom("10".equals(somWootPurchaseOrderVo.getShipFrom()) ? "FBA" : "Dropship");
        noticeVo.setStartDate(somWootPurchaseOrderVo.getStartDate());
        noticeVo.setEndDate(somWootPurchaseOrderVo.getEndDate());
        List<SomWootPromotion> promotionList = somWootPromotionMapper.all();
        List<SomWootPurchaseOrderNoticeVo.SkuInfo> skuInfoList = new ArrayList<>();
        switch (status) {
            case 50:
                noticeVo.setStatus("提报成功");
                if (CollectionUtil.isNotEmpty(somWootPurchaseOrderVo.getItemList())) {
                    for (SomWootPurchaseOrderItemVo itemVo : somWootPurchaseOrderVo.getItemList()) {
                        SomWootPurchaseOrderNoticeVo.SkuInfo skuInfo = new SomWootPurchaseOrderNoticeVo.SkuInfo();
                        List<SomWootPurchaseOrderNoticeVo.WarehouseInfo> warehouseInfoList = new ArrayList<>();
                        SomWootPurchaseOrderNoticeVo.WarehouseInfo warehouseInfo;
                        Integer quantity = itemVo.getQuantity();
                        skuInfo.setSellerSku(itemVo.getSellerSku());
                        SomWootPromotion promotion = promotionList.stream().filter(f -> StrUtil.equals(f.getPurchaseOrder(), somWootPurchaseOrderVo.getPurchaseOrder()) && StrUtil.equals(f.getSellerSku(), itemVo.getSellerSku())).findFirst().orElse(null);
                        if (promotion != null) {
                            skuInfo.setSku(promotion.getSku());
                            //ShipFrom为FBA时，仓库是固定的Amazon仓，出库数量为采购订单明细中填写的数量
                            if ("10".equals(somWootPurchaseOrderVo.getShipFrom())) {
                                warehouseInfo = new SomWootPurchaseOrderNoticeVo.WarehouseInfo();
                                warehouseInfo.setWarehouseName("美国Amazon仓");
                                warehouseInfo.setOutboundQuantity(quantity);
                                warehouseInfoList.add(warehouseInfo);
                            }
                            //ShipFrom为Dropship时，仓库是营销活动promotion_quantity中的仓库，每个仓库的出库数量=采购订单明细中填写的数量/仓库的总数量
                            //2024.6.28修改为：每个仓库的出库数量=采购订单明细中填写的数量，并且排除掉数量为0的仓库
                            if ("20".equals(somWootPurchaseOrderVo.getShipFrom())) {
                                JSONArray jsonArray = JSONUtil.parseArray(promotion.getPromotionQuantity());
                                if (!JSONUtil.isNull(jsonArray)) {
                                    //Integer avgQuantity = quantity / jsonArray.size();
                                    for (int i = 0; i < jsonArray.size(); i++) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                                        if (jsonObject.getInt("quantity") != 0) {
                                            warehouseInfo = new SomWootPurchaseOrderNoticeVo.WarehouseInfo();
                                            warehouseInfo.setWarehouseName(jsonObject.getStr("warehouseName"));
                                            warehouseInfo.setOutboundQuantity(jsonObject.getInt("quantity"));
                                            warehouseInfoList.add(warehouseInfo);
                                        }
                                    }
                                }
                            }
                        }
                        skuInfo.setWarehouseInfoList(warehouseInfoList);
                        skuInfoList.add(skuInfo);
                    }
                }
                noticeVo.setSkuInfoList(skuInfoList);
                break;
            case 20:
                noticeVo.setReturnOrder(somWootPurchaseOrderVo.getReturnOrder());
                if (CollectionUtil.isNotEmpty(somWootPurchaseOrderVo.getItemList())) {
                    for (SomWootPurchaseOrderItemVo itemVo : somWootPurchaseOrderVo.getItemList()) {
                        SomWootPurchaseOrderNoticeVo.SkuInfo skuInfo = new SomWootPurchaseOrderNoticeVo.SkuInfo();
                        skuInfo.setSellerSku(itemVo.getSellerSku());
                        skuInfo.setPoQuantity(itemVo.getQuantity());
                        skuInfo.setRpoQuantity(itemVo.getReturnPurchaseOrderQuantity());
                        promotionList.stream().filter(f -> StrUtil.equals(f.getPurchaseOrder(), somWootPurchaseOrderVo.getPurchaseOrder()) && StrUtil.equals(f.getSellerSku(), itemVo.getSellerSku())).findFirst().ifPresent(promotion -> skuInfo.setSku(promotion.getSku()));
                        skuInfoList.add(skuInfo);
                    }
                }
                noticeVo.setSkuInfoList(skuInfoList);
                break;
        }
        return noticeVo;
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return String
     */
    public String export(SomWootPurchaseOrderPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomWootPurchaseOrderVo> records = queryByPage(searchVo).getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        //获取字典Payment Status
        List<McDictionaryInfo> dictList = mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", Arrays.asList("WootShipFrom", "WootPaymentStatus")).select();
        Map<String, String> paymentStatusMap = dictList.stream().filter(x -> x.getItemTypeCode().equals("WootPaymentStatus")).collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
        Map<String, String> shipFromMap = dictList.stream().filter(x -> x.getItemTypeCode().equals("WootShipFrom")).collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
        //查询所有的营销活动数据
        List<SomWootPromotion> promotionList = somWootPromotionMapper.createLambdaQuery().andIsNotNull("purchase_order").select();
        Map<String, SomWootPromotion> promotionMap = promotionList.stream().collect(Collectors.toMap(x -> x.getPurchaseOrder() + "_" + x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        //根据purchaseOrder+展示码匹配营销活动数据，获取仓库库存数据
        List<SomWootPurchaseOrderVo> resultList = records.stream().flatMap(x -> x.getItemList().stream().map(v -> {
            try {
                SomWootPurchaseOrderVo clone = x.clone();
                SomWootPromotion promotion = promotionMap.get(x.getPurchaseOrder() + "_" + v.getSellerSku());
                if (promotion != null) {
                    clone.setSku(promotion.getSku());
                    List<SomWootPromotionVo.PromotionQuantity> promotionQuantities = objectMapper.readValue(promotion.getPromotionQuantity(), new TypeReference<List<SomWootPromotionVo.PromotionQuantity>>() {});
                    //按照仓库编码分组 sum quantity
                    Map<String, Integer> quantityMap = promotionQuantities.stream().collect(Collectors.groupingBy(SomWootPromotionVo.PromotionQuantity::getWarehouseName, Collectors.summingInt(SomWootPromotionVo.PromotionQuantity::getQuantity)));
                    //整个帅的  如果要有新增的仓库，只需要在SomWootPurchaseOrderVo中添加就行 名字用仓库真正的名字
                    //获取SomWootPurchaseOrderVo 中所有@Excel注解的字段
                    List<Field> fields = Arrays.stream(SomWootPurchaseOrderVo.class.getDeclaredFields()).filter(f -> f.isAnnotationPresent(Excel.class)).collect(Collectors.toList());
                    //根据Excel name属性，获取对应字段的值
                    for (Field field : fields) {
                        Excel excel = field.getAnnotation(Excel.class);
                        String name = excel.name();
                        if (quantityMap.containsKey(name)) {
                            Integer quantity = quantityMap.get(name);
                            field.setAccessible(true);
                            field.set(clone, quantity);
                        }
                    }
                    //设置到仓价
                    clone.setSapPrice(promotion.getSapPrice());
                }
                //Woot单价取item中的活动单价
                clone.setPromotionPrice(v.getWootPrice());
                //退回数量
                clone.setReturnPurchaseOrderQuantity(v.getReturnPurchaseOrderQuantity());
                if (clone.getPromotionPrice() != null && v.getReturnPurchaseOrderQuantity() != null) {
                    //退回金额
                    clone.setReturnPurchaseOrderAmount(clone.getPromotionPrice().multiply(new BigDecimal(v.getReturnPurchaseOrderQuantity())));
                }
                // 销量分为两种情况：
                //  1. 如果 shipFrom 是 FBA 的情况，销量 = 数量 - 退回数量
                //  2. 如果 shipFrom 是 DROPSHIP 的情况，销量 = sales_volume
                if ("10".equals(clone.getShipFrom())) {
                    if (v.getQuantity() != null && v.getReturnPurchaseOrderQuantity() != null) {
                        //销售数量
                        clone.setQuantity(v.getQuantity() - v.getReturnPurchaseOrderQuantity());
                    }
                } else if ("20".equals(clone.getShipFrom())) {
                    clone.setQuantity(v.getSalesVolume());
                }
                if (clone.getPromotionPrice() != null && clone.getQuantity() != null) {
                    //销售额
                    clone.setQuantityAmount(clone.getPromotionPrice().multiply(new BigDecimal(clone.getQuantity())));
                }
                Integer paymentStatus = x.getPaymentStatus();
                clone.setPaymentStatusDesc(paymentStatusMap.get(String.valueOf(paymentStatus)));
                clone.setShipFrom(shipFromMap.get(clone.getShipFrom()));
                clone.setSellerSku(v.getSellerSku());
                clone.setSellerSkuQuantity(v.getQuantity());
                return clone;
            } catch (CloneNotSupportedException | JsonProcessingException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        })).collect(Collectors.toList());
        try {
            ExportParams params = new ExportParams(null, "采购订单");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            org.apache.poi.ss.usermodel.Workbook workbook = ExcelExportUtil.exportExcel(params, SomWootPurchaseOrderVo.class, resultList);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(byteArray);
        } catch (Exception e) {
            log.error("WootPurchaseOrder导出失败:{}", e.toString(), e);
            throw new RuntimeException("导出失败" + e.toString());
        }
    }
}
