package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.mapper.SomOttoPlatformPriceHistoryMapper;
import com.zielsmart.mc.vo.SomOttoPlatformPriceHistoryPageSearchVo;
import com.zielsmart.mc.vo.SomOttoPlatformPriceHistoryVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description 平台价调整历史表
 * @date 2025-04-01 15:50:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomOttoPlatformPriceHistoryService {
    
    @Resource
    private SomOttoPlatformPriceHistoryMapper somOttoPlatformPriceHistoryMapper;

    @Resource
    private SomOttoPlatformPriceService somOttoPlatformPriceService;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOttoPlatformPriceHistoryVo>}
     * <AUTHOR>
     */
    public PageVo<SomOttoPlatformPriceHistoryVo> queryByPage(SomOttoPlatformPriceHistoryPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOttoPlatformPriceHistoryVo> pageResult = somOttoPlatformPriceHistoryMapper.queryByPage(searchVo, pageRequest);
        List<SomOttoPlatformPriceHistoryVo> priceVos = pageResult.getList();
        if (CollUtil.isNotEmpty(priceVos)) {
            Map<String, McDictionaryInfo> mcDictionaryInfoMap = getDictionaryMap().get("OttoPlatformPriceAdjustStatus");
            priceVos.forEach(data -> {
                McDictionaryInfo mcDictionaryInfo = mcDictionaryInfoMap.get(String.valueOf(data.getAdjustStatus()));
                data.setAdjustStatusColor(mcDictionaryInfo == null ? null : mcDictionaryInfo.getItemValue2());
                data.setAdjustStatusDesc(mcDictionaryInfo == null ? null : mcDictionaryInfo.getItemLable());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomOttoPlatformPriceHistoryVo.class, searchVo);
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64
     */
    public String export(SomOttoPlatformPriceHistoryPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomOttoPlatformPriceHistoryVo> records = queryByPage(searchVo).getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        Workbook workbook;
        try {
            ExportParams params = new ExportParams(null, "OTTO平台价调整历史记录");
            params.setAutoSize(true);
            params.setType(ExcelType.XSSF);
            workbook = ExcelExportUtil.exportExcel(params, SomOttoPlatformPriceHistoryVo.class, records);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取字典
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getDictionaryMap() {
        return somOttoPlatformPriceService.getDictionaryMap();
    }



}
