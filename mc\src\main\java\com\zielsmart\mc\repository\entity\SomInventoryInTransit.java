package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 在途SKU库存
* gen by 代码生成器 2023-09-01
*/

@Table(name="mc.som_inventory_in_transit")
public class SomInventoryInTransit implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 基础款SKU
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 新款SKU
	 */
	@Column("new_sku_code")
	private String newSkuCode ;
	/**
	 * 基础款SKU在途数量
	 */
	@Column("transit_quantity")
	private Integer transitQuantity ;
	/**
	 * 0.未到仓 1.已到仓
	 */
	@Column("ready_flag")
	private Integer readyFlag ;
	/**
	 * 新款SKU预计到仓日期
	 */
	@Column("plan_arrive_date")
	private Date planArriveDate ;
	/**
	 * 新款SKU预计到仓数量
	 */
	@Column("plan_arrive_quantity")
	private Integer planArriveQuantity ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	@Column("market")
	private String market;

	public SomInventoryInTransit() {
	}

	public String getMarket() {
		return market;
	}

	public void setMarket(String market) {
		this.market = market;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 基础款SKU
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* 基础款SKU
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 新款SKU
	*@return
	*/
	public String getNewSkuCode(){
		return  newSkuCode;
	}
	/**
	* 新款SKU
	*@param  newSkuCode
	*/
	public void setNewSkuCode(String newSkuCode ){
		this.newSkuCode = newSkuCode;
	}
	/**
	* 基础款SKU在途数量
	*@return
	*/
	public Integer getTransitQuantity(){
		return  transitQuantity;
	}
	/**
	* 基础款SKU在途数量
	*@param  transitQuantity
	*/
	public void setTransitQuantity(Integer transitQuantity ){
		this.transitQuantity = transitQuantity;
	}
	/**
	* 0.未到仓 1.已到仓
	*@return
	*/
	public Integer getReadyFlag(){
		return  readyFlag;
	}
	/**
	* 0.未到仓 1.已到仓
	*@param  readyFlag
	*/
	public void setReadyFlag(Integer readyFlag ){
		this.readyFlag = readyFlag;
	}
	/**
	* 新款SKU预计到仓日期
	*@return
	*/
	public Date getPlanArriveDate(){
		return  planArriveDate;
	}
	/**
	* 新款SKU预计到仓日期
	*@param  planArriveDate
	*/
	public void setPlanArriveDate(Date planArriveDate ){
		this.planArriveDate = planArriveDate;
	}
	/**
	* 新款SKU预计到仓数量
	*@return
	*/
	public Integer getPlanArriveQuantity(){
		return  planArriveQuantity;
	}
	/**
	* 新款SKU预计到仓数量
	*@param  planArriveQuantity
	*/
	public void setPlanArriveQuantity(Integer planArriveQuantity ){
		this.planArriveQuantity = planArriveQuantity;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
