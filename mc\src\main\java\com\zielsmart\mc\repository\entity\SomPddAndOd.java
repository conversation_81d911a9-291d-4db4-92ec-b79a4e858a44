package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * PDD & Outlet Deal表
 * gen by 代码生成器 2022-08-03
 */

@Table(name = "mc.som_pdd_and_od")
public class SomPddAndOd implements java.io.Serializable {

    /**
     * 近四周平均成交价
     */
    @Column("last_month_average_price")
    private BigDecimal lastMonthAveragePrice;

    /**
     * 近四周最高可销售天数
     */
    @Column("last_month_maximum_days_of_stock_to_sell")
    private Integer lastMonthMaximumDaysOfStockToSell;

    /**
     * 近四周最低可销售天数
     */
    @Column("last_month_minimum_days_of_stock_to_sell")
    private Integer lastMonthMinimumDaysOfStockToSell;
    /**
     * 近四周平均毛利率
     */
    @Column("last_month_gross")
    private BigDecimal lastMonthGross;


    @Column("deal_price_gross")
    private BigDecimal dealPriceGross;

    //近四周销售预测达成率
    @Column("completion_rate")
    private BigDecimal completionRate;

    private BigDecimal dealBurstCoefficient; // 活动预计爆发系数
    @Column("dms_last_30day")
    private BigDecimal dmsLast30day; // 近三十天DMS
    private BigDecimal categoryGross; // 三级分类近四周毛利率
    private BigDecimal expectedSalesVolume; // 活动预计销量
    private BigDecimal estimateOfSales; // 活动预计销售额
    private BigDecimal expectedGrossProfitMargin; // 活动预计毛利额
    private String approvalRole; // 审批角色 审批分支

    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 类型10.Prime Day Deal 20.Outlet Deal
     */
    @Column("deal_type")
    private Integer dealType;
    /**
     * 大促类型
     * 1 = Prime Day Window
     * 2 = Prime Fall Deal Event
     * 3 = BFCM Window
     * 4 = Black Friday Window
     * 5 = Cyber Monday Window
     */
    @Column("campaign_type")
    private Integer campaignType ;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 评分
     */
    @Column("score")
    private BigDecimal score;
    /**
     * 当前售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 秒杀价
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * 秒杀折扣
     */
    @Column("deal_discount")
    private BigDecimal dealDiscount;
    /**
     * Coupon折扣
     */
    @Column("coupon_discount")
    private BigDecimal couponDiscount;
    /**
     * Promotion折扣
     */
    @Column("promotion_discount")
    private BigDecimal promotionDiscount;
    /**
     * 总折扣
     */
    @Column("total_discount")
    private BigDecimal totalDiscount;
    /**
     * 总折扣金额
     */
    @Column("total_discount_amount")
    private BigDecimal totalDiscountAmount;
    /**
     * 预估成交价
     */
    @Column("transaction_price")
    private BigDecimal transactionPrice;
    /**
     * 三级分类编码
     */
    @Column("category_inline_code")
    private String categoryInlineCode;
    /**
     * 三级分类名称
     */
    @Column("category_name")
    private String categoryName;
    /**
     * 状态10.草稿20审批中29审批未通过30提报成功40未开始70进行中80已结束
     */
    @Column("status")
    private Integer status;
    /**
     * 活动提报开始日期
     */
    @Column("plan_start_date")
    private Date planStartDate;
    /**
     * 活动提报截止日期
     */
    @Column("plan_end_date")
    private Date planEndDate;
    /**
     * 活动实际开始日期
     */
    @Column("real_start_date")
    private Date realStartDate;
    /**
     * 活动实际截止日期
     */
    @Column("real_end_date")
    private Date realEndDate;
    /**
     * 审批失败原因
     */
    @Column("audit_failure_remark")
    private String auditFailureRemark;
    /**
     * 提报失败原因
     */
    @Column("submission_failure_remark")
    private String submissionFailureRemark;
    /**
     * 备注
     */
    @Column("remark")
    private String remark;
    /**
     * 申请原因10.提升排名 20.清货 30.稳排名 99.自定义
     */
    @Column("apply_reason")
    private Integer applyReason;
    /**
     * 自定义原因
     */
    @Column("cutomer_reason")
    private String cutomerReason;
    /**
     * 币种
     */
    @Column("currency_code")
    private String currencyCode;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;
    /**
     * 取消原因
     */
    @Column("cancel_remark")
    private String cancelRemark;
    /**
     * 取消失败原因
     */
    @Column("cacenl_failure_remark")
    private String cacenlFailureRemark;
    /**
     * 库存可售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;
    /**
     * 提交时间
     */
    @Column("submit_time")
    private Date submitTime;
    /**
     * 审批时间
     */
    @Column("audit_time")
    private Date auditTime;


    @Column("committed_units")
    private Integer committedUnits;

    /**
     * 错误信息(由RPA提供)
     */
    @Column("error_msg")
    private String errorMsg;

    /**
     * 修改状态10.修改中 20.修改成功 30.修改失败
     */
    @Column("modify_status")
    private Integer modifyStatus;

    /**
     * 修改原因
     */
    @Column("modify_remark")
    private String modifyRemark;
    /**
     * 修改失败原因
     */
    @Column("modify_failure_remark")
    private String modifyFailureRemark;

    public SomPddAndOd() {
    }

    public Integer getCommittedUnits() {
        return committedUnits;
    }

    public void setCommittedUnits(Integer committedUnits) {
        this.committedUnits = committedUnits;
    }

    public BigDecimal getLastMonthAveragePrice() {
        return lastMonthAveragePrice;
    }

    public void setLastMonthAveragePrice(BigDecimal lastMonthAveragePrice) {
        this.lastMonthAveragePrice = lastMonthAveragePrice;
    }

    public Integer getLastMonthMaximumDaysOfStockToSell() {
        return lastMonthMaximumDaysOfStockToSell;
    }

    public void setLastMonthMaximumDaysOfStockToSell(Integer lastMonthMaximumDaysOfStockToSell) {
        this.lastMonthMaximumDaysOfStockToSell = lastMonthMaximumDaysOfStockToSell;
    }

    public Integer getLastMonthMinimumDaysOfStockToSell() {
        return lastMonthMinimumDaysOfStockToSell;
    }

    public void setLastMonthMinimumDaysOfStockToSell(Integer lastMonthMinimumDaysOfStockToSell) {
        this.lastMonthMinimumDaysOfStockToSell = lastMonthMinimumDaysOfStockToSell;
    }

    public BigDecimal getLastMonthGross() {
        return lastMonthGross;
    }

    public void setLastMonthGross(BigDecimal lastMonthGross) {
        this.lastMonthGross = lastMonthGross;
    }

    public BigDecimal getDealPriceGross() {
        return dealPriceGross;
    }

    public void setDealPriceGross(BigDecimal dealPriceGross) {
        this.dealPriceGross = dealPriceGross;
    }


    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }


    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 评分
     *
     * @return
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 评分
     *
     * @param score
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 当前售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 当前售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 秒杀价
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 秒杀价
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * 秒杀折扣
     *
     * @return
     */
    public BigDecimal getDealDiscount() {
        return dealDiscount;
    }

    /**
     * 秒杀折扣
     *
     * @param dealDiscount
     */
    public void setDealDiscount(BigDecimal dealDiscount) {
        this.dealDiscount = dealDiscount;
    }

    /**
     * Coupon折扣
     *
     * @return
     */
    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    /**
     * Coupon折扣
     *
     * @param couponDiscount
     */
    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    /**
     * Promotion折扣
     *
     * @return
     */
    public BigDecimal getPromotionDiscount() {
        return promotionDiscount;
    }

    /**
     * Promotion折扣
     *
     * @param promotionDiscount
     */
    public void setPromotionDiscount(BigDecimal promotionDiscount) {
        this.promotionDiscount = promotionDiscount;
    }

    /**
     * 三级分类编码
     *
     * @return
     */
    public String getCategoryInlineCode() {
        return categoryInlineCode;
    }

    /**
     * 三级分类编码
     *
     * @param categoryInlineCode
     */
    public void setCategoryInlineCode(String categoryInlineCode) {
        this.categoryInlineCode = categoryInlineCode;
    }

    /**
     * 三级分类名称
     *
     * @return
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 三级分类名称
     *
     * @param categoryName
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 状态10.草稿20审批中29审批未通过30提报成功40未开始70进行中80已结束
     *
     * @return
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态10.草稿20审批中29审批未通过30提报成功40未开始70进行中80已结束
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 活动提报开始日期
     *
     * @return
     */
    public Date getPlanStartDate() {
        return planStartDate;
    }

    /**
     * 活动提报开始日期
     *
     * @param planStartDate
     */
    public void setPlanStartDate(Date planStartDate) {
        this.planStartDate = planStartDate;
    }

    /**
     * 活动提报截止日期
     *
     * @return
     */
    public Date getPlanEndDate() {
        return planEndDate;
    }

    /**
     * 活动提报截止日期
     *
     * @param planEndDate
     */
    public void setPlanEndDate(Date planEndDate) {
        this.planEndDate = planEndDate;
    }

    /**
     * 活动实际开始日期
     *
     * @return
     */
    public Date getRealStartDate() {
        return realStartDate;
    }

    /**
     * 活动实际开始日期
     *
     * @param realStartDate
     */
    public void setRealStartDate(Date realStartDate) {
        this.realStartDate = realStartDate;
    }

    /**
     * 活动实际截止日期
     *
     * @return
     */
    public Date getRealEndDate() {
        return realEndDate;
    }

    /**
     * 活动实际截止日期
     *
     * @param realEndDate
     */
    public void setRealEndDate(Date realEndDate) {
        this.realEndDate = realEndDate;
    }

    /**
     * 审批失败原因
     *
     * @return
     */
    public String getAuditFailureRemark() {
        return auditFailureRemark;
    }

    /**
     * 审批失败原因
     *
     * @param auditFailureRemark
     */
    public void setAuditFailureRemark(String auditFailureRemark) {
        this.auditFailureRemark = auditFailureRemark;
    }

    /**
     * 提报失败原因
     *
     * @return
     */
    public String getSubmissionFailureRemark() {
        return submissionFailureRemark;
    }

    /**
     * 提报失败原因
     *
     * @param submissionFailureRemark
     */
    public void setSubmissionFailureRemark(String submissionFailureRemark) {
        this.submissionFailureRemark = submissionFailureRemark;
    }

    /**
     * 备注
     *
     * @return
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     *
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDealType() {
        return dealType;
    }

    public void setDealType(Integer dealType) {
        this.dealType = dealType;
    }

    public Integer getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(Integer applyReason) {
        this.applyReason = applyReason;
    }

    public String getCutomerReason() {
        return cutomerReason;
    }

    public void setCutomerReason(String cutomerReason) {
        this.cutomerReason = cutomerReason;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public BigDecimal getTransactionPrice() {
        return transactionPrice;
    }

    public void setTransactionPrice(BigDecimal transactionPrice) {
        this.transactionPrice = transactionPrice;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public String getCacenlFailureRemark() {
        return cacenlFailureRemark;
    }

    public void setCacenlFailureRemark(String cacenlFailureRemark) {
        this.cacenlFailureRemark = cacenlFailureRemark;
    }

    public Integer getStockSaleDays() {
        return stockSaleDays;
    }

    public void setStockSaleDays(Integer stockSaleDays) {
        this.stockSaleDays = stockSaleDays;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getCampaignType() {
        return campaignType;
    }

    public void setCampaignType(Integer campaignType) {
        this.campaignType = campaignType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getModifyStatus() {
        return modifyStatus;
    }

    public void setModifyStatus(Integer modifyStatus) {
        this.modifyStatus = modifyStatus;
    }

    public String getModifyRemark() {
        return modifyRemark;
    }

    public void setModifyRemark(String modifyRemark) {
        this.modifyRemark = modifyRemark;
    }

    public String getModifyFailureRemark() {
        return modifyFailureRemark;
    }

    public void setModifyFailureRemark(String modifyFailureRemark) {
        this.modifyFailureRemark = modifyFailureRemark;
    }

    public BigDecimal getDealBurstCoefficient() {
        return dealBurstCoefficient;
    }

    public void setDealBurstCoefficient(BigDecimal dealBurstCoefficient) {
        this.dealBurstCoefficient = dealBurstCoefficient;
    }

    public BigDecimal getDmsLast30day() {
        return dmsLast30day;
    }

    public void setDmsLast30day(BigDecimal dmsLast30day) {
        this.dmsLast30day = dmsLast30day;
    }

    public BigDecimal getCategoryGross() {
        return categoryGross;
    }

    public void setCategoryGross(BigDecimal categoryGross) {
        this.categoryGross = categoryGross;
    }

    public BigDecimal getExpectedSalesVolume() {
        return expectedSalesVolume;
    }

    public void setExpectedSalesVolume(BigDecimal expectedSalesVolume) {
        this.expectedSalesVolume = expectedSalesVolume;
    }

    public BigDecimal getEstimateOfSales() {
        return estimateOfSales;
    }

    public void setEstimateOfSales(BigDecimal estimateOfSales) {
        this.estimateOfSales = estimateOfSales;
    }

    public BigDecimal getExpectedGrossProfitMargin() {
        return expectedGrossProfitMargin;
    }

    public void setExpectedGrossProfitMargin(BigDecimal expectedGrossProfitMargin) {
        this.expectedGrossProfitMargin = expectedGrossProfitMargin;
    }

    public String getApprovalRole() {
        return approvalRole;
    }

    public void setApprovalRole(String approvalRole) {
        this.approvalRole = approvalRole;
    }
}
