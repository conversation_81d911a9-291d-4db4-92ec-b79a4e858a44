package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomTiktokListingService;
import com.zielsmart.mc.vo.SomTiktokListingPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokListingReport;
import com.zielsmart.mc.vo.SomTiktokListingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTiktokListingController
 * @description
 * @date 2024-12-12 18:00:47
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTiktokListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TikTok listing管理")
public class SomTiktokListingController extends BasicController {

    @Resource
    SomTiktokListingService somTiktokListingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTiktokListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTiktokListingVo>> queryByPage(@RequestBody SomTiktokListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTiktokListingService.queryByPage(searchVo));
    }


    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomTiktokListingPageSearchVo searchVo) {
        String data = somTiktokListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "可售库存报表")
    @PostMapping(value = "/stock-report")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTiktokListingReport>> stockReport(@RequestBody SomTiktokListingPageSearchVo searchVo) throws Exception {
        return ResultVo.ofSuccess(somTiktokListingService.stockReport(searchVo));
    }

    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export-stock-report", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportStockReport(@RequestBody SomTiktokListingPageSearchVo searchVo) throws Exception {
        String data = somTiktokListingService.exportStockReport(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


}
