package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 定价表
 * gen by 代码生成器 2022-05-16
 */

@Table(name = "mc.som_standard_price")
public class SomStandardPrice implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 建议零售价
     */
    @Column("recommended_retail_price")
    private BigDecimal recommendedRetailPrice;
    /**
     * 最低价
     */
    @Column("minimum_price")
    private BigDecimal minimumPrice;
    /**
     * 最低毛利率
     */
    @Column("minimum_gross_profit_margin")
    private BigDecimal minimumGrossProfitMargin;
    /**
     * 最高价
     */
    @Column("highest_price")
    private BigDecimal highestPrice;
    /**
     * 最高毛利率
     */
    @Column("highest_gross_profit_margin")
    private BigDecimal highestGrossProfitMargin;
    /**
     * 货币
     */
    @Column("currency_code")
    private String currencyCode;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;

    /**
     * 标准价
     */
    @Column("standard_price")
    private BigDecimal standardPrice;

    /**
     * 标准毛利率
     */
    @Column("standard_gross_margin")
    private BigDecimal standardGrossMargin;

    /**
     * SKU
     */
    @Column("product_main_code")
    private String productMainCode;

    /**
     * 发货方式 1寄售  0自发
     */
    @Column("is_consignment_sales")
    private Integer isConsignmentSales;

    /**
     * 数据来源
     */
    @Column("data_source")
    private String dataSource;

    public SomStandardPrice() {
    }


    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardGrossMargin() {
        return standardGrossMargin;
    }

    public void setStandardGrossMargin(BigDecimal standardGrossMargin) {
        this.standardGrossMargin = standardGrossMargin;
    }

    /**
     * 商品主编码
     *
     * @return
     */
    public String getProductMainCode() {
        return productMainCode;
    }

    /**
     * 商品主编码
     *
     * @param productMainCode
     */
    public void setProductMainCode(String productMainCode) {
        this.productMainCode = productMainCode;
    }

    /**
     * 是否代销
     *
     * @return
     */
    public Integer getIsConsignmentSales() {
        return isConsignmentSales;
    }

    /**
     * 是否代销
     *
     * @param isConsignmentSales
     */
    public void setIsConsignmentSales(Integer isConsignmentSales) {
        this.isConsignmentSales = isConsignmentSales;
    }

    /**
     * 数据来源
     *
     * @return
     */
    public String getDataSource() {
        return dataSource;
    }

    /**
     * 数据来源
     *
     * @param dataSource
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 建议零售价
     *
     * @return
     */
    public BigDecimal getRecommendedRetailPrice() {
        return recommendedRetailPrice;
    }

    /**
     * 建议零售价
     *
     * @param recommendedRetailPrice
     */
    public void setRecommendedRetailPrice(BigDecimal recommendedRetailPrice) {
        this.recommendedRetailPrice = recommendedRetailPrice;
    }

    /**
     * 最低价
     *
     * @return
     */
    public BigDecimal getMinimumPrice() {
        return minimumPrice;
    }

    /**
     * 最低价
     *
     * @param minimumPrice
     */
    public void setMinimumPrice(BigDecimal minimumPrice) {
        this.minimumPrice = minimumPrice;
    }

    /**
     * 最低毛利率
     *
     * @return
     */
    public BigDecimal getMinimumGrossProfitMargin() {
        return minimumGrossProfitMargin;
    }

    /**
     * 最低毛利率
     *
     * @param minimumGrossProfitMargin
     */
    public void setMinimumGrossProfitMargin(BigDecimal minimumGrossProfitMargin) {
        this.minimumGrossProfitMargin = minimumGrossProfitMargin;
    }

    /**
     * 最高价
     *
     * @return
     */
    public BigDecimal getHighestPrice() {
        return highestPrice;
    }

    /**
     * 最高价
     *
     * @param highestPrice
     */
    public void setHighestPrice(BigDecimal highestPrice) {
        this.highestPrice = highestPrice;
    }

    /**
     * 最高毛利率
     *
     * @return
     */
    public BigDecimal getHighestGrossProfitMargin() {
        return highestGrossProfitMargin;
    }

    /**
     * 最高毛利率
     *
     * @param highestGrossProfitMargin
     */
    public void setHighestGrossProfitMargin(BigDecimal highestGrossProfitMargin) {
        this.highestGrossProfitMargin = highestGrossProfitMargin;
    }

    /**
     * 货币
     *
     * @return
     */
    public String getCurrencyCode() {
        return currencyCode;
    }

    /**
     * 货币
     *
     * @param currencyCode
     */
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

}
