package com.zielsmart.mc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/*
 * Temu本本营销活动的VO实体对象
 */
@Data
@Schema(title = "Temu本本营销活动", name = "SomTemuLocalPromotionVo")
public class SomTemuLocalPromotionVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "营销活动ID", name = "activityId")
    private String activityId;

    @Schema(description = "营销活动名称", name = "activityName")
    private String activityName;

    @Schema(description = "活动起始时间", name = "activityStartTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityStartTime;

    @Schema(description = "活动截止时间", name = "activityEndTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityEndTime;

    @Schema(description = "活动状态：1. 未开始 2. 运行中 3. 已结束", name = "activityStatus")
    private Integer activityStatus;

    @Schema(description = "活动类型 2 Lightning deals 13 Advanced big sale 27 Clearance deals 100 Official big sale", name = "activityType")
    private Integer activityType;

    @Schema(description = "是否加入了此活动，0否 1是", name = "isJoinedActivity")
    private Integer isJoinedActivity;

    @Schema(description = "创建时间", name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "店铺名称", name = "accountName")
    private String accountName;

    @Schema(description = "活动状态描述：1. 未开始 2. 运行中 3. 已结束", name = "activityStatusDesc")
    private String activityStatusDesc;

    @Schema(description = "活动类型描述", name = "activityTypeDesc")
    private String activityTypeDesc;

}
