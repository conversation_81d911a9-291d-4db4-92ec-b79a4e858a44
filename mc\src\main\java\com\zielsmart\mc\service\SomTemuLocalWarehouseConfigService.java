package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.repository.entity.SomTemuLocalWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.repository.mapper.SomTemuLocalWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomTemuLocalWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-06-27 11:35:22
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuLocalWarehouseConfigService {

    @Resource
    private SomTemuLocalWarehouseConfigMapper somTemuLocalWarehouseConfigMapper;

    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return PageVo<SomTemuLocalWarehouseConfigVo>
     */
    public PageVo<SomTemuLocalWarehouseConfigVo> queryByPage(SomTemuLocalWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuLocalWarehouseConfigVo> pageResult = somTemuLocalWarehouseConfigMapper.queryByPage(searchVo, pageRequest);
        List<SomTemuLocalWarehouseConfigVo> localWarehouseConfigVos = pageResult.getList();
        // 处理数据
        handleSomTemuLocalWarehouseConfig(localWarehouseConfigVos);
        return ConvertUtils.pageConvert(pageResult, SomTemuLocalWarehouseConfigVo.class, searchVo);
    }


    /**
     * 新增
     *
     * @param localWarehouseConfigVo 入参
     * @param tokenUser              当前登录用户
     */
    public void save(SomTemuLocalWarehouseConfigVo localWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(localWarehouseConfigVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 核验新增和更新入参
        checkSaveOrUpdateParam(localWarehouseConfigVo);
        // 店铺ID/平台/站点唯一
        String accountId = localWarehouseConfigVo.getAccountId();
        String site = localWarehouseConfigVo.getSite();
        long count = somTemuLocalWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("account_id", accountId)
                .count();
        if (count > 0) {
            throw new ValidateException("此店铺、站点已维护了可售仓库！");
        }
        // 整合数据入库
        List<SomTemuLocalWarehouseConfig> localWarehouseConfigs = buildLocalWarehouseConfigs(localWarehouseConfigVo, tokenUser);
        somTemuLocalWarehouseConfigMapper.insertBatch(localWarehouseConfigs);
    }

    /**
     * 修改
     *
     * @param localWarehouseConfigVo 入参
     * @param tokenUser              当前登录用户
     * @throws ValidateException 核验exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomTemuLocalWarehouseConfigVo localWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        // 核验入参
        checkSaveOrUpdateParam(localWarehouseConfigVo);
        // 处理数据
        List<SomTemuLocalWarehouseConfig> localWarehouseConfigs = buildLocalWarehouseConfigs(localWarehouseConfigVo, tokenUser);
        // 刪除旧数据
        String site = localWarehouseConfigVo.getSite();
        String accountId = localWarehouseConfigVo.getAccountId();
        somTemuLocalWarehouseConfigMapper.createLambdaQuery().andEq("site", site).andEq("account_id", accountId).delete();
        // 新增新数据
        somTemuLocalWarehouseConfigMapper.insertBatch(localWarehouseConfigs);
    }

    /**
     * 删除
     *
     * @param somTemuLocalWarehouseConfigVo 入参
     */
    public void delete(SomTemuLocalWarehouseConfigVo somTemuLocalWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuLocalWarehouseConfigVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        String accountId = somTemuLocalWarehouseConfigVo.getAccountId();
        String site = somTemuLocalWarehouseConfigVo.getSite();
        if (StrUtil.isEmpty(accountId) || StrUtil.isEmpty(site)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTemuLocalWarehouseConfigMapper.createLambdaQuery().andEq("site", site).andEq("account_id", accountId).delete();
    }

    /**
     * 处理仓库配置数据
     *
     * @param localWarehouseConfigVos 仓库配置
     */
    private void handleSomTemuLocalWarehouseConfig(List<SomTemuLocalWarehouseConfigVo> localWarehouseConfigVos) {
        if (CollUtil.isEmpty(localWarehouseConfigVos)) {
            return;
        }
        // 查询所有的数据
        List<String> sites = localWarehouseConfigVos.stream().map(SomTemuLocalWarehouseConfigVo::getSite).distinct().collect(Collectors.toList());
        List<SomTemuLocalWarehouseConfig> warehouseConfigs = somTemuLocalWarehouseConfigMapper.createLambdaQuery().andIn("site", sites).select();
        Map<String, List<SomTemuLocalWarehouseConfig>> warehouseConfigsMap = warehouseConfigs.stream().collect(Collectors.groupingBy(e -> e.getSite() + '_' + e.getAccountId(), Collectors.toList()));
        // 库区名称映射，key:StorageCode,value:StorageName
        Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
        // Temu 店铺映射
        Map<String, String> temuAccountMap = queryLocalAccount().stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
        // 仓库名称映射，key:WarehouseCode,value:WarehouseName
        Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
        localWarehouseConfigVos.forEach(config -> {
            List<SomTemuLocalWarehouseConfig> configs = warehouseConfigsMap.get(config.getSite() + '_' + config.getAccountId());
            if (CollUtil.isNotEmpty(configs)) {
                List<String> warehouseNameList = new ArrayList<>();
                List<SomTemuLocalWarehouseConfigVo.UseableWarehouse> useableWarehouses = configs.stream().map(x -> {
                    SomTemuLocalWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTemuLocalWarehouseConfigVo.UseableWarehouse();
                    useableWarehouse.setAid(x.getAid());
                    useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                    useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                    warehouseNameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                    return useableWarehouse;
                }).collect(Collectors.toList());
                config.setList(useableWarehouses);
                config.setWarehouseNameList(warehouseNameList);
                config.setCreateName(configs.get(0).getCreateName());
                config.setCreateNum(configs.get(0).getCreateNum());
                config.setCreateTime(configs.get(0).getCreateTime());
                config.setAccountName(temuAccountMap.get(config.getAccountId()));
            }
        });
    }

    /**
     * 获取本本的账号信息
     *
     * @return List<McDictionaryInfo>
     */
    private List<McDictionaryInfo> queryLocalAccount() {
        // 获取字典里面的 TemuAccount，有效:item_value4=1，本本:item_value8=本本
        return mcDictionaryInfoMapper.createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .andEq("item_value8", "本本")
                .select();
    }

    /**
     * 构建 SomTemuLocalWarehouseConfig bean集合
     *
     * @param localWarehouseConfigVo 入参
     * @param tokenUser              当前登录
     * @return List<SomTemuLocalWarehouseConfig>
     */
    private List<SomTemuLocalWarehouseConfig> buildLocalWarehouseConfigs(SomTemuLocalWarehouseConfigVo localWarehouseConfigVo, TokenUserInfo tokenUser) {
        List<SomTemuLocalWarehouseConfig> localWarehouseConfigs = new ArrayList<>();
        // 整合数据
        Date now = DateTime.now().toJdkDate();
        List<SomTemuLocalWarehouseConfigVo.UseableWarehouse> useableWarehouses = localWarehouseConfigVo.getList();
        for (SomTemuLocalWarehouseConfigVo.UseableWarehouse useableWarehouse : useableWarehouses) {
            SomTemuLocalWarehouseConfig localWarehouseConfig = new SomTemuLocalWarehouseConfig();
            localWarehouseConfig.setAid(IdUtil.fastSimpleUUID());
            localWarehouseConfig.setSite(localWarehouseConfigVo.getSite());
            localWarehouseConfig.setAccountId(localWarehouseConfigVo.getAccountId());
            localWarehouseConfig.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            localWarehouseConfig.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            localWarehouseConfig.setCreateName(tokenUser.getUserName());
            localWarehouseConfig.setCreateNum(tokenUser.getJobNumber());
            localWarehouseConfig.setCreateTime(now);
            localWarehouseConfig.setLastModifyName(tokenUser.getUserName());
            localWarehouseConfig.setLastModifyNum(tokenUser.getJobNumber());
            localWarehouseConfig.setLastModifyTime(now);
            localWarehouseConfigs.add(localWarehouseConfig);
        }
        return localWarehouseConfigs;
    }

    /**
     * 核验新增和更新入参
     *
     * @param localWarehouseConfigVo 入参
     * @throws ValidateException
     */
    private void checkSaveOrUpdateParam(SomTemuLocalWarehouseConfigVo localWarehouseConfigVo) throws ValidateException {
        List<SomTemuLocalWarehouseConfigVo.UseableWarehouse> useableWarehouses = localWarehouseConfigVo.getList();
        String accountId = localWarehouseConfigVo.getAccountId();
        String site = localWarehouseConfigVo.getSite();
        if (StrUtil.isEmpty(accountId) || StrUtil.isEmpty(site) || CollUtil.isEmpty(useableWarehouses)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 核验店铺ID是否正确,itemValue3中存储的是店铺ID
        List<String> accountIds = queryLocalAccount().stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        if (!accountIds.contains(accountId)) {
            throw new ValidateException("店铺已删除或已被注销！");
        }
    }


}
