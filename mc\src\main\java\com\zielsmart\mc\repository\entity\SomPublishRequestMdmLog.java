package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 刊登请求mdm日志表
 */
@Table(name = "mc.som_publish_request_mdm_log")
public class SomPublishRequestMdmLog implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 客户编码
     */
    @Column("customer_code")
    private String customerCode;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * mdm响应
     */
    @Column("mdm_response")
    private String mdmResponse;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomPublishRequestMdmLog() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 客户编码
     *
     * @return
     */
    public String getCustomerCode() {
        return customerCode;
    }

    /**
     * 客户编码
     *
     * @param customerCode
     */
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * SKU
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * mdm响应
     *
     * @return
     */
    public String getMdmResponse() {
        return mdmResponse;
    }

    /**
     * mdm响应
     *
     * @param mdmResponse
     */
    public void setMdmResponse(String mdmResponse) {
        this.mdmResponse = mdmResponse;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
