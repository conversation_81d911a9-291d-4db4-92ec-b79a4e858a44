package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomTemuFbaBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuFbaBlackListVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-07-04
 */

@SqlResource("somTemuFbaBlackList")
public interface SomTemuFbaBlackListMapper extends BaseMapper<SomTemuFbaBlackList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomTemuFbaBlackListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuFbaBlackListVo> queryByPage(@Param("searchVo") SomTemuFbaBlackListPageSearchVo searchVo, PageRequest pageRequest);


    default void batchUpdateById(@Param("updateList") List<SomTemuFbaBlackList> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somTemuFbaBlackList.batchUpdateById"), updateList);
    }
}
