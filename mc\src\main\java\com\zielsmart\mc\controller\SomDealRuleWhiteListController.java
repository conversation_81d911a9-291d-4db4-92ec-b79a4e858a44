package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import com.zielsmart.mc.service.SomDealRuleWhiteListService;
import com.zielsmart.mc.vo.SomDealRuleWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleWhiteListVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomDealRuleWhiteListController
 * @description
 * @date 2024-03-06 10:03:39
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somDealRuleWhiteList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "校验规则白名单管理")
public class SomDealRuleWhiteListController extends BasicController {

    @Resource
    SomDealRuleWhiteListService somDealRuleWhiteListService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomDealRuleWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomDealRuleWhiteListVo>> queryByPage(@RequestBody SomDealRuleWhiteListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somDealRuleWhiteListService.queryByPage(searchVo));
    }

    /**
     * save
     * 添加
     *
     * @param somDealRuleWhiteListVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated SomDealRuleWhiteListVo somDealRuleWhiteListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somDealRuleWhiteListService.save(somDealRuleWhiteListVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param somDealRuleWhiteListVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomDealRuleWhiteListVo somDealRuleWhiteListVo) throws ValidateException {
        somDealRuleWhiteListService.delete(somDealRuleWhiteListVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomDealRuleWhiteListPageSearchVo searchVo) {
        String data = somDealRuleWhiteListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/SomDealRuleWhiteListTemplate.xlsx";
    }

    /**
     * importExcel
     * 导入数据
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"平台", "站点", "展示码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomDealRuleWhiteListVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomDealRuleWhiteListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somDealRuleWhiteListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }
}
