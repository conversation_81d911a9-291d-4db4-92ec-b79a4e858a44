package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
 * 三级分类信息
 * gen by 代码生成器 2022-02-09
 */

@Table(name="mc.mc_category_info")
public class McCategoryInfo implements java.io.Serializable {
	/**
	 * 序列号ID
	 */
	@AssignID
	private String aid ;
	/**
	 * SAP编号,预留字段，同步成功后SAP返回的唯一编号
	 默认和业务主码一致
	 */
	@Column("sap_num")
	private String sapNum ;
	/**
	 * 上一次同步时间
	 */
	@Column("last_syn_time")
	private Date lastSynTime ;
	/**
	 * 品类内部编码：主要用于品类方案时，品类展示编码会重复，为了区分新旧品类
	 */
	@Column("category_inline_code")
	private String categoryInlineCode ;
	/**
	 * 品类展示编码
	 */
	@Column("category_code")
	private String categoryCode ;
	/**
	 * 品类中文名
	 */
	@Column("category_name_cn")
	private String categoryNameCn ;
	/**
	 * 品类英文名
	 */
	@Column("category_name_en")
	private String categoryNameEn ;
	/**
	 * 品类描述信息
	 */
	@Column("category_info")
	private String categoryInfo ;
	/**
	 * 品类级别，品类分为四级
	 0为预留级别
	 实际业务使用从1开始使用
	 0
	 1
	 2
	 3
	 */
	@Column("category_level")
	private Integer categoryLevel ;
	/**
	 * 父品类内部编码
	 */
	@Column("parent_inline_code")
	private String parentInlineCode ;
	/**
	 * 是否展示：用于界面设置展示，
	 例如角色维护菜单可能都不需要再权限设置上展示，但是这个菜单是有效的
	 */
	@Column("is_display")
	private Integer isDisplay ;
	/**
	 * 展示顺序
	 */
	@Column("display_seq")
	private Integer displaySeq ;
	/**
	 * 是否有效
	 */
	@Column("is_enabled")
	private Integer isEnabled ;
	/**
	 * 生效时间
	 */
	@Column("start_time")
	private Date startTime ;
	/**
	 * 失效时间
	 */
	@Column("end_time")
	private Date endTime ;
	/**
	 * 创建人
	 */
	@Column("create_seqnum")
	private Integer createSeqnum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人
	 */
	@Column("modify_seqnum")
	private Integer modifySeqnum ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 描述信息
	 */
	@Column("remark_desc")
	private String remarkDesc ;
	/**
	 * 品类标识 字典值  外键，77字典表_字典信息值   dict_dictvalue
	 3048 品类标识
	 */
	@Column("category_flag_code")
	private String categoryFlagCode ;
	/**
	 * SAP是否同步成功
	 */
	@Column("is_sync_sap_success")
	private Integer isSyncSapSuccess ;
	/**
	 * SAP同步结果信息
	 */
	@Column("syn_descinfo")
	private String synDescinfo ;

	@Column("sync_time")
	private Date syncTime;
	/**
	 * BUS工号
	 */
	@Column("bus_emp_num")
	private String busEmpNum ;
	/**
	 * BUS姓名
	 */
	@Column("bus_emp_name")
	private String busEmpName ;

	public McCategoryInfo() {
	}

	/**
	 * 序列号ID
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 序列号ID
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * SAP编号,预留字段，同步成功后SAP返回的唯一编号
	 默认和业务主码一致
	 *@return
	 */
	public String getSapNum(){
		return  sapNum;
	}
	/**
	 * SAP编号,预留字段，同步成功后SAP返回的唯一编号
	 默认和业务主码一致
	 *@param  sapNum
	 */
	public void setSapNum(String sapNum ){
		this.sapNum = sapNum;
	}
	/**
	 * 上一次同步时间
	 *@return
	 */
	public Date getLastSynTime(){
		return  lastSynTime;
	}
	/**
	 * 上一次同步时间
	 *@param  lastSynTime
	 */
	public void setLastSynTime(Date lastSynTime ){
		this.lastSynTime = lastSynTime;
	}
	/**
	 * 品类内部编码：主要用于品类方案时，品类展示编码会重复，为了区分新旧品类
	 *@return
	 */
	public String getCategoryInlineCode(){
		return  categoryInlineCode;
	}
	/**
	 * 品类内部编码：主要用于品类方案时，品类展示编码会重复，为了区分新旧品类
	 *@param  categoryInlineCode
	 */
	public void setCategoryInlineCode(String categoryInlineCode ){
		this.categoryInlineCode = categoryInlineCode;
	}
	/**
	 * 品类展示编码
	 *@return
	 */
	public String getCategoryCode(){
		return  categoryCode;
	}
	/**
	 * 品类展示编码
	 *@param  categoryCode
	 */
	public void setCategoryCode(String categoryCode ){
		this.categoryCode = categoryCode;
	}
	/**
	 * 品类中文名
	 *@return
	 */
	public String getCategoryNameCn(){
		return  categoryNameCn;
	}
	/**
	 * 品类中文名
	 *@param  categoryNameCn
	 */
	public void setCategoryNameCn(String categoryNameCn ){
		this.categoryNameCn = categoryNameCn;
	}
	/**
	 * 品类英文名
	 *@return
	 */
	public String getCategoryNameEn(){
		return  categoryNameEn;
	}
	/**
	 * 品类英文名
	 *@param  categoryNameEn
	 */
	public void setCategoryNameEn(String categoryNameEn ){
		this.categoryNameEn = categoryNameEn;
	}
	/**
	 * 品类描述信息
	 *@return
	 */
	public String getCategoryInfo(){
		return  categoryInfo;
	}
	/**
	 * 品类描述信息
	 *@param  categoryInfo
	 */
	public void setCategoryInfo(String categoryInfo ){
		this.categoryInfo = categoryInfo;
	}
	/**
	 * 品类级别，品类分为四级
	 0为预留级别
	 实际业务使用从1开始使用
	 0
	 1
	 2
	 3
	 *@return
	 */
	public Integer getCategoryLevel(){
		return  categoryLevel;
	}
	/**
	 * 品类级别，品类分为四级
	 0为预留级别
	 实际业务使用从1开始使用
	 0
	 1
	 2
	 3
	 *@param  categoryLevel
	 */
	public void setCategoryLevel(Integer categoryLevel ){
		this.categoryLevel = categoryLevel;
	}
	/**
	 * 父品类内部编码
	 *@return
	 */
	public String getParentInlineCode(){
		return  parentInlineCode;
	}
	/**
	 * 父品类内部编码
	 *@param  parentInlineCode
	 */
	public void setParentInlineCode(String parentInlineCode ){
		this.parentInlineCode = parentInlineCode;
	}
	/**
	 * 是否展示：用于界面设置展示，
	 例如角色维护菜单可能都不需要再权限设置上展示，但是这个菜单是有效的
	 *@return
	 */
	public Integer getisDisplay(){
		return  isDisplay;
	}
	/**
	 * 是否展示：用于界面设置展示，
	 例如角色维护菜单可能都不需要再权限设置上展示，但是这个菜单是有效的
	 *@param  isDisplay
	 */
	public void setisDisplay(Integer isDisplay ){
		this.isDisplay = isDisplay;
	}
	/**
	 * 展示顺序
	 *@return
	 */
	public Integer getDisplaySeq(){
		return  displaySeq;
	}
	/**
	 * 展示顺序
	 *@param  displaySeq
	 */
	public void setDisplaySeq(Integer displaySeq ){
		this.displaySeq = displaySeq;
	}
	/**
	 * 是否有效
	 *@return
	 */
	public Integer getisEnabled(){
		return  isEnabled;
	}
	/**
	 * 是否有效
	 *@param  isEnabled
	 */
	public void setisEnabled(Integer isEnabled ){
		this.isEnabled = isEnabled;
	}
	/**
	 * 生效时间
	 *@return
	 */
	public Date getStartTime(){
		return  startTime;
	}
	/**
	 * 生效时间
	 *@param  startTime
	 */
	public void setStartTime(Date startTime ){
		this.startTime = startTime;
	}
	/**
	 * 失效时间
	 *@return
	 */
	public Date getEndTime(){
		return  endTime;
	}
	/**
	 * 失效时间
	 *@param  endTime
	 */
	public void setEndTime(Date endTime ){
		this.endTime = endTime;
	}
	/**
	 * 创建人
	 *@return
	 */
	public Integer getCreateSeqnum(){
		return  createSeqnum;
	}
	/**
	 * 创建人
	 *@param  createSeqnum
	 */
	public void setCreateSeqnum(Integer createSeqnum ){
		this.createSeqnum = createSeqnum;
	}
	/**
	 * 创建人姓名
	 *@return
	 */
	public String getCreateName(){
		return  createName;
	}
	/**
	 * 创建人姓名
	 *@param  createName
	 */
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	 * 创建人工号
	 *@return
	 */
	public String getCreateNum(){
		return  createNum;
	}
	/**
	 * 创建人工号
	 *@param  createNum
	 */
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	 * 创建时间
	 *@return
	 */
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	 * 创建时间
	 *@param  createTime
	 */
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	 * 修改人
	 *@return
	 */
	public Integer getModifySeqnum(){
		return  modifySeqnum;
	}
	/**
	 * 修改人
	 *@param  modifySeqnum
	 */
	public void setModifySeqnum(Integer modifySeqnum ){
		this.modifySeqnum = modifySeqnum;
	}
	/**
	 * 修改人工号
	 *@return
	 */
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	 * 修改人工号
	 *@param  modifyNum
	 */
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	 * 修改人姓名
	 *@return
	 */
	public String getModifyName(){
		return  modifyName;
	}
	/**
	 * 修改人姓名
	 *@param  modifyName
	 */
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	 * 修改时间
	 *@return
	 */
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	 * 修改时间
	 *@param  modifyTime
	 */
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}
	/**
	 * 描述信息
	 *@return
	 */
	public String getRemarkDesc(){
		return  remarkDesc;
	}
	/**
	 * 描述信息
	 *@param  remarkDesc
	 */
	public void setRemarkDesc(String remarkDesc ){
		this.remarkDesc = remarkDesc;
	}
	/**
	 * 品类标识 字典值  外键，77字典表_字典信息值   dict_dictvalue
	 3048 品类标识
	 *@return
	 */
	public String getCategoryFlagCode(){
		return  categoryFlagCode;
	}
	/**
	 * 品类标识 字典值  外键，77字典表_字典信息值   dict_dictvalue
	 3048 品类标识
	 *@param  categoryFlagCode
	 */
	public void setCategoryFlagCode(String categoryFlagCode ){
		this.categoryFlagCode = categoryFlagCode;
	}
	/**
	 * SAP是否同步成功
	 *@return
	 */
	public Integer getisSyncSapSuccess(){
		return  isSyncSapSuccess;
	}
	/**
	 * SAP是否同步成功
	 *@param  isSyncSapSuccess
	 */
	public void setisSyncSapSuccess(Integer isSyncSapSuccess ){
		this.isSyncSapSuccess = isSyncSapSuccess;
	}
	/**
	 * SAP同步结果信息
	 *@return
	 */
	public String getSynDescinfo(){
		return  synDescinfo;
	}
	/**
	 * SAP同步结果信息
	 *@param  synDescinfo
	 */
	public void setSynDescinfo(String synDescinfo ){
		this.synDescinfo = synDescinfo;
	}

	public void setBusEmpNum(String busEmpNum ){
		this.busEmpNum = busEmpNum;
	}

	public void setBusEmpName(String busEmpName ){
		this.busEmpName = busEmpName;
	}

	public String getBusEmpNum() {
		return busEmpNum;
	}

	public String getBusEmpName() {
		return busEmpName;
	}

	public Date getSyncTime() {
		return syncTime;
	}

	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}
}
