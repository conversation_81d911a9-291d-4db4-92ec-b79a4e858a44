package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomWayfairCostView;
import com.zielsmart.mc.repository.entity.SomWayfairInvoice;
import com.zielsmart.mc.repository.entity.SomWayfairRtvDetail;
import com.zielsmart.mc.repository.mapper.SomWayfairCostViewMapper;
import com.zielsmart.mc.repository.mapper.SomWayfairInvoiceMapper;
import com.zielsmart.mc.repository.mapper.SomWayfairRtvDetailMapper;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.excel.SomWayfairInvoiceExcel;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomWayfairInvoiceService {

    @Resource
    private SomWayfairInvoiceMapper somWayfairInvoiceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private SomWayfairCostViewMapper somWayfairCostViewMapper;
    @Resource
    private SomWayfairRtvDetailMapper somWayfairRtvDetailMapper;

    private static SomWayfairRtvDetail getSomWayfairRtvDetail(Map.Entry<String, List<SomWayfairInvoiceExcel>> stringListEntry, SomWayfairInvoice valueAddedServicesInvoice) {
        List<SomWayfairInvoiceExcel> value = stringListEntry.getValue();
        SomWayfairInvoiceExcel sigleValue = value.get(0);
        //计算出每个分组的总的计费数量：SUM(Charge Units)；计算出每个分组的收费总额：SUM（Charge Amount）
        int totalChargeUnits = value.stream().mapToInt(SomWayfairInvoiceExcel::getChargeUnits).sum();
        BigDecimal totalChargeAmount = value.stream().map(SomWayfairInvoiceExcel::getChargeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        SomWayfairRtvDetail rtvDetail = new SomWayfairRtvDetail();
        rtvDetail.setAid(IdUtil.fastSimpleUUID());
        rtvDetail.setMAid(valueAddedServicesInvoice.getAid());
        rtvDetail.setSellerSku(stringListEntry.getKey());
        rtvDetail.setProductBucket(sigleValue.getProductBucket());
        rtvDetail.setWayfairDimensionsL(sigleValue.getDimensionsL());
        rtvDetail.setWayfairDimensionsW(sigleValue.getDimensionsW());
        rtvDetail.setWayfairDimensionsH(sigleValue.getDimensionsH());
        rtvDetail.setWayfairWeight(sigleValue.getWeight());
        rtvDetail.setWayfairGirth(sigleValue.getGirth());
        rtvDetail.setCurrency(sigleValue.getLocalCurrency());
        rtvDetail.setInvoice(sigleValue.getInvoice());
        rtvDetail.setChargeUnit(totalChargeUnits);
        rtvDetail.setChargeAmount(totalChargeAmount);
        return rtvDetail;
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomWayfairInvoiceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWayfairInvoiceExtVo> queryByPage(SomWayfairInvoicePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomWayfairInvoiceExtVo> pageResult = dynamicSqlManager.getMapper(SomWayfairInvoiceMapper.class).queryByPage(searchVo, pageRequest);
        if (ObjectUtil.isNotEmpty(pageResult.getList())) {
            List<String> aidList = pageResult.getList().stream().filter(x->x.getType()!=null && x.getType() == 30).map(x -> x.getAid()).collect(Collectors.toList());
            List<SomWayfairRtvDetail> detailList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(aidList)) {
                detailList = somWayfairRtvDetailMapper.createLambdaQuery().andIn("m_aid", aidList).select();
            }

            for (SomWayfairInvoiceExtVo vo : pageResult.getList()) {
                List<SomWayfairRtvDetailVo> detailVoList = new ArrayList<>();
                if (vo.getType() == 30) {
                    List<SomWayfairRtvDetail> rtvDetailList = detailList.stream().filter(f -> f.getMAid().equals(vo.getAid()) && StrUtil.isNotBlank(f.getSellerSku())).collect(Collectors.toList());
                    SomWayfairRtvDetail detail = detailList.stream().filter(f -> f.getMAid().equals(vo.getAid()) && StrUtil.isBlank(f.getSellerSku())).findFirst().get();
                    if (detail != null) {
                        vo.setChargeAmount(detail.getChargeAmount());
                        vo.setCurrency(detail.getCurrency());
                    }
                    if (CollectionUtil.isNotEmpty(rtvDetailList)) {
                        detailVoList.addAll(rtvDetailList.stream().map(f -> {
                            SomWayfairRtvDetailVo detailVo = new SomWayfairRtvDetailVo();
                            detailVo.setSellerSku(f.getSellerSku());
                            detailVo.setChargeUnit(f.getChargeUnit());
                            detailVo.setSomVolume(f.getSomVolume());
                            detailVo.setSomFee(f.getSomFee());
                            detailVo.setSomStorageFeeAmount(f.getSomStorageFeeAmount());
                            return detailVo;
                        }).collect(Collectors.toList()));
                        vo.setChargeUnit(rtvDetailList.stream().mapToInt(SomWayfairRtvDetail::getChargeUnit).sum());
                        BigDecimal amount = rtvDetailList.stream().map(SomWayfairRtvDetail::getSomStorageFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        vo.setSomStorageFeeAmount(amount);
                        if (vo.getChargeAmount() != null) {
                            vo.setOvercharged(vo.getChargeAmount().subtract(amount));
                        }
                    }
                    vo.setDetailVoList(detailVoList);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomWayfairInvoiceExtVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somWayfairInvoiceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWayfairInvoiceVo somWayfairInvoiceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWayfairInvoiceVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somWayfairInvoiceVo.setAid(IdUtil.fastSimpleUUID());
        somWayfairInvoiceMapper.insert(ConvertUtils.beanConvert(somWayfairInvoiceVo, SomWayfairInvoice.class));
    }

    /**
     * update
     * 修改
     *
     * @param somWayfairInvoiceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomWayfairInvoiceVo somWayfairInvoiceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWayfairInvoiceVo) || StrUtil.isEmpty(somWayfairInvoiceVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        somWayfairInvoiceMapper.createLambdaQuery()
                .andEq("aid", somWayfairInvoiceVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somWayfairInvoiceVo, SomWayfairInvoice.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somWayfairInvoiceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWayfairInvoiceVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somWayfairInvoiceMapper.createLambdaQuery().andEq("aid", somWayfairInvoiceVo.getAid()).delete();
    }

    public String export(SomWayfairInvoicePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        List<SomWayfairInvoiceExtVo> records = somWayfairInvoiceMapper.queryExport(searchVo, pageRequest).getList();
        if (!records.isEmpty()) {
            Map<String, List<SomWayfairInvoiceExtVo>> map = records.stream().collect(Collectors.groupingBy(SomWayfairInvoiceVo::getAid));
            List<SomWayfairInvoiceExtVo> extVoList = new ArrayList<>();
            for (String key : map.keySet()) {
                List<SomWayfairInvoiceExtVo> voList = map.get(key);
                if (voList.size() > 1) {
                    voList = voList.stream().filter(f -> f.getAid().equals(key) && StrUtil.isNotBlank(f.getSellerSku())).collect(Collectors.toList());
                }
                extVoList.addAll(voList);
            }
            extVoList = extVoList.stream().sorted(Comparator.comparing(SomWayfairInvoiceExtVo::getCreateTime).reversed()).collect(Collectors.toList());
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Wayfair账单发票索赔表管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomWayfairInvoiceExtVo.class, extVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public int importExcel(List<SomWayfairInvoiceExcel> list, TokenUserInfo tokenUser, String site, String importDate) throws CloneNotSupportedException, ParseException, ValidateException {
        log.info("导入数据");
        List<SomWayfairInvoice> insertList = new ArrayList<>();
        List<SomWayfairRtvDetail> insertDetailList = new ArrayList<>();

        Date now = DateTime.now().toJdkDate();
        SomWayfairInvoice invoice = new SomWayfairInvoice();
        invoice.setCreateName(tokenUser.getUserName());
        invoice.setCreateNum(tokenUser.getJobNumber());
        invoice.setCreateTime(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        invoice.setImportDate(sdf.parse(importDate));
        invoice.setSite(site);
        //未索赔
        invoice.setStatus(10);

        //根据站点 日期 类型 删除原有数据
        List<Integer> typeList = new ArrayList<>();
        int insertSize = 0;

        //获取“Charge Category”列为“Storage”的数据
        List<SomWayfairInvoiceExcel> storageList = list.stream().filter(e -> e.getChargeCategory() != null && e.getChargeCategory().equals("Storage")).collect(Collectors.toList());
        //根据“Part Number”列进行分组，计算出每个分组的总的计费数量：SUM(Charge Units)；计算出每个分组的收费总额：SUM（Charge Amount）
        boolean partNumberNull = storageList.stream().anyMatch(e -> e.getPartNumber() == null);
        if (partNumberNull) {
            throw new ValidateException("请检查数据，Charge Category列中 Storage 类型的数据，Part Number列存在空值");
        }
        //2024-08-01 新增逻辑 只索赔 storage 金额大于等于4的数据
        storageList = storageList.stream().filter(e -> e.getChargeAmount() != null && e.getChargeAmount().compareTo(BigDecimal.valueOf(4)) >= 0).collect(Collectors.toList());
        Map<String, List<SomWayfairInvoiceExcel>> storageMap = storageList.stream().collect(Collectors.groupingBy(SomWayfairInvoiceExcel::getPartNumber));
        //查询平台费用表
        LocalDate date = LocalDate.parse(importDate);
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        List<SomWayfairCostView> costList = somWayfairCostViewMapper.createLambdaQuery().andEq("site", site).andBetween("create_time", start, end).select();

        Map<String, SomWayfairCostView> costMap = costList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku(), Function.identity(), (x1, x2) -> x1));

        for (Map.Entry<String, List<SomWayfairInvoiceExcel>> entry : storageMap.entrySet()) {
            SomWayfairInvoice storageInvoice = invoice.clone();
            storageInvoice.setType(10);
            storageInvoice.setAid(IdUtil.fastSimpleUUID());

            SomWayfairRtvDetail rtvDetail = getSomWayfairRtvDetail(entry, storageInvoice);

            Optional.ofNullable(costMap.get(site + entry.getKey())).ifPresent(x -> {
                rtvDetail.setSomVolume(x.getVolume());
                rtvDetail.setSomFee(x.getStorageCharge());
                if (rtvDetail.getSomVolume() != null && rtvDetail.getSomFee() != null && rtvDetail.getChargeUnit() != null) {
                    rtvDetail.setSomStorageFeeAmount(rtvDetail.getSomVolume().multiply(rtvDetail.getSomFee()).multiply(BigDecimal.valueOf(rtvDetail.getChargeUnit())));
                }
            });

            if (rtvDetail.getSomStorageFeeAmount() == null) {
                continue;
            }
            BigDecimal overcharged = rtvDetail.getChargeAmount().subtract(rtvDetail.getSomStorageFeeAmount());
            if (overcharged.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            rtvDetail.setOvercharged(overcharged);

            insertList.add(storageInvoice);
            insertDetailList.add(rtvDetail);
        }
        if (insertSize != insertList.size()) {
            typeList.add(10);
            insertSize = insertList.size();
        }
        //“Charge Category”列为“Outbound Orders”的数据

        List<SomWayfairInvoiceExcel> outboundOrdersList = list.stream().filter(e -> e.getChargeCategory() != null && e.getChargeCategory().equals("Outbound Orders")).collect(Collectors.toList());
        //根据“Part Number”列进行分组，计算出每个分组的总的计费数量：SUM(Charge Units)；计算出每个分组的收费总额：SUM（Charge Amount）
        partNumberNull = outboundOrdersList.stream().anyMatch(e -> e.getPartNumber() == null);
        if (partNumberNull) {
            throw new ValidateException("请检查数据，Charge Category列中 Outbound Orders 类型的数据，Part Number列存在空值");
        }
        Map<String, List<SomWayfairInvoiceExcel>> outboundOrdersMap = outboundOrdersList.stream().collect(Collectors.groupingBy(SomWayfairInvoiceExcel::getPartNumber));
        for (Map.Entry<String, List<SomWayfairInvoiceExcel>> stringListEntry : outboundOrdersMap.entrySet()) {
            SomWayfairInvoice outboundOrdersInvoice = invoice.clone();
            outboundOrdersInvoice.setType(20);
            outboundOrdersInvoice.setAid(IdUtil.fastSimpleUUID());

            SomWayfairRtvDetail rtvDetail = getSomWayfairRtvDetail(stringListEntry, outboundOrdersInvoice);
            Optional.ofNullable(costMap.get(site + stringListEntry.getKey())).ifPresent(x -> {
                rtvDetail.setSomFee(x.getDeliveryCost());
                if (rtvDetail.getSomFee() != null && rtvDetail.getChargeUnit() != null) {
                    rtvDetail.setSomStorageFeeAmount(rtvDetail.getSomFee().multiply(BigDecimal.valueOf(rtvDetail.getChargeUnit())));
                }
            });
            if (rtvDetail.getSomStorageFeeAmount() == null) {
                continue;
            }
            BigDecimal overcharged = rtvDetail.getChargeAmount().subtract(rtvDetail.getSomStorageFeeAmount());
            if (overcharged.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            rtvDetail.setOvercharged(overcharged);
            insertList.add(outboundOrdersInvoice);
            insertDetailList.add(rtvDetail);
        }
        if (insertSize != insertList.size()) {
            typeList.add(20);
            insertSize = insertList.size();
        }

        //获取到CSV中“Adjustment Description”列为“Value Added Services”的数据
        List<SomWayfairInvoiceExcel> valueAddedServicesList = list.stream().filter(e -> e.getAdjustmentDescription() != null && e.getAdjustmentDescription().equals("Value Added Services")).collect(Collectors.toList());
        //根据“Part Number”列进行分组，计算出每个分组的总的计费数量：SUM(Charge Units)；计算出每个分组的收费总额：SUM（Charge Amount）
        partNumberNull = valueAddedServicesList.stream().anyMatch(e -> e.getPartNumber() == null);
        if (partNumberNull) {
            throw new ValidateException("请检查数据，Adjustment Description列中 Value Added Services 类型的数据，Part Number列存在空值");
        }
        Map<String, List<SomWayfairInvoiceExcel>> valueAddedServicesMap = valueAddedServicesList.stream().collect(Collectors.groupingBy(SomWayfairInvoiceExcel::getPartNumber));
        for (Map.Entry<String, List<SomWayfairInvoiceExcel>> stringListEntry : valueAddedServicesMap.entrySet()) {
            SomWayfairInvoice valueAddedServicesInvoice = invoice.clone();
            valueAddedServicesInvoice.setType(40);
            valueAddedServicesInvoice.setAid(IdUtil.fastSimpleUUID());

            SomWayfairRtvDetail rtvDetail = getSomWayfairRtvDetail(stringListEntry, valueAddedServicesInvoice);

            Optional.ofNullable(costMap.get(site + stringListEntry.getKey())).ifPresent(x -> {
                rtvDetail.setSomFee(x.getVasCost());
                if (rtvDetail.getSomFee() != null && rtvDetail.getChargeUnit() != null) {
                    rtvDetail.setSomStorageFeeAmount(rtvDetail.getSomFee().multiply(BigDecimal.valueOf(rtvDetail.getChargeUnit())));
                }
            });
            if (rtvDetail.getSomStorageFeeAmount() == null) {
                continue;
            }
            BigDecimal overcharged = rtvDetail.getChargeAmount().subtract(rtvDetail.getSomStorageFeeAmount());
            if (overcharged.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            rtvDetail.setOvercharged(overcharged);

            insertList.add(valueAddedServicesInvoice);
            insertDetailList.add(rtvDetail);
        }
        if (insertSize != insertList.size()) {
            typeList.add(40);
            insertSize = insertList.size();
        }

        //获取到CSV中“Adjustment Description”列为“RTV” 并且 “Admin Description Notes”列包含“FSW”字符的数据
        List<SomWayfairInvoiceExcel> rtvFswList = list.stream().filter(e -> e.getAdjustmentDescription() != null && e.getAdjustmentDescription().equals("RTV") && e.getAdminDescriptionNotes() != null && e.getAdminDescriptionNotes().contains("FSW")).collect(Collectors.toList());
        for (SomWayfairInvoiceExcel invoiceExcel : rtvFswList) {
            SomWayfairInvoice rtvFswInvoice = invoice.clone();
            rtvFswInvoice.setType(30);
            rtvFswInvoice.setAid(IdUtil.fastSimpleUUID());
            SomWayfairRtvDetail rtvDetail = new SomWayfairRtvDetail();
            rtvDetail.setAid(IdUtil.fastSimpleUUID());
            rtvDetail.setMAid(rtvFswInvoice.getAid());
            rtvDetail.setCurrency(invoiceExcel.getLocalCurrency());
            rtvDetail.setInvoice(invoiceExcel.getInvoice());
            rtvDetail.setReturnOrderNumber(invoiceExcel.getAdminDescriptionNotes().substring(invoiceExcel.getAdminDescriptionNotes().indexOf("FSW")));
            rtvDetail.setChargeAmount(invoiceExcel.getChargeAmount());
            insertList.add(rtvFswInvoice);
            insertDetailList.add(rtvDetail);
        }
        if (insertSize != insertList.size()) {
            typeList.add(30);
            insertSize = insertList.size();
        }
        if (typeList.isEmpty() || insertList.isEmpty()) {
            return 0;
        }
        //根据站点 日期 类型 删除原有数据
        List<SomWayfairInvoice> aidInvoiceList = somWayfairInvoiceMapper.createLambdaQuery().andEq("site", site).andEq("import_date", sdf.parse(importDate)).andIn("type", typeList).select("aid");
        if (!aidInvoiceList.isEmpty()) {
            List<String> aidList = aidInvoiceList.stream().map(SomWayfairInvoice::getAid).collect(Collectors.toList());
            somWayfairInvoiceMapper.createLambdaQuery().andIn("aid", aidList).delete();
            somWayfairRtvDetailMapper.createLambdaQuery().andIn("m_aid", aidList).delete();
        }
        //插入数据
        somWayfairInvoiceMapper.insertBatch(insertList);
        somWayfairRtvDetailMapper.insertBatch(insertDetailList);
        log.info("导入数据完成");
        return insertList.size();
    }

    /**
     * feedbackClaimResult
     * 反馈索赔结果
     *
     * @param invoiceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void feedbackClaimResult(SomWayfairInvoiceVo invoiceVo) throws ValidateException {
        if (StrUtil.isBlank(invoiceVo.getAid()) || ObjectUtil.isEmpty(invoiceVo.getStatus())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        invoiceVo.setFeedbackDate(DateTime.now().toJdkDate());
        somWayfairInvoiceMapper.createLambdaQuery()
                .andEq("aid", invoiceVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(invoiceVo, SomWayfairInvoice.class));
    }

    /**
     * claim
     * 索赔
     *
     * @param somWayfairInvoiceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void claim(SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWayfairInvoiceVo) || CollectionUtil.isEmpty(somWayfairInvoiceVo.getAidList()) || StrUtil.isBlank(somWayfairInvoiceVo.getTicketNumber())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        List<SomWayfairInvoice> invoiceList = somWayfairInvoiceMapper.createLambdaQuery().andIn("aid", somWayfairInvoiceVo.getAidList()).select();
        List<SomWayfairRtvDetail> detailList = somWayfairRtvDetailMapper.createLambdaQuery().andIn("m_aid", somWayfairInvoiceVo.getAidList()).andIsNotNull("seller_sku").select();
        if (CollectionUtil.isNotEmpty(invoiceList)) {
            Date now = DateTime.now().toJdkDate();
            List<Integer> typeList = invoiceList.stream().map(SomWayfairInvoice::getType).distinct().collect(Collectors.toList());
            if (typeList.size() > 1) {
                throw new ValidateException("只允许选择同类型的数据进行索赔");
            }
            boolean statusFlag = invoiceList.stream().anyMatch(f -> f.getStatus() != 10);
            if (statusFlag) {
                throw new ValidateException("已索赔的数据不允许二次索赔");
            }
            Integer type = typeList.get(0);
            if (type == 30) {
                for (SomWayfairInvoice item : invoiceList) {
                    if (CollectionUtil.isNotEmpty(detailList)) {
                        if (detailList.stream().noneMatch(f -> f.getMAid().equals(item.getAid()))) {
                            throw new ValidateException("RTV类型的索赔必须维护明细才允许索赔，请您先维护RTV退回单明细");
                        }
                    } else {
                        throw new ValidateException("RTV类型的索赔必须维护明细才允许索赔，请您先维护RTV退回单明细");
                    }
                }
            }
            if (type == 40) {
                for (SomWayfairInvoice item : invoiceList) {
                    if (CollectionUtil.isNotEmpty(detailList)) {
                        Optional<SomWayfairRtvDetail> detailOptional = detailList.stream().filter(f -> f.getMAid().equals(item.getAid())).findFirst();
                        if (detailOptional.isPresent()) {
                            SomWayfairRtvDetail detail = detailOptional.get();
                            if (detail.getVasFlag() != 1) {
                                throw new ValidateException("展示码：" + detail.getSellerSku() + "  Adjustment VAS 没有免安装凭证，因此不需要索赔");
                            }
                        }
                    }
                }
            }
            somWayfairInvoiceVo.setStatus(20);
            somWayfairInvoiceVo.setDisputeDate(now);
            somWayfairInvoiceMapper.createLambdaQuery().andIn("aid", somWayfairInvoiceVo.getAidList())
                    .updateSelective(ConvertUtils.beanConvert(somWayfairInvoiceVo, SomWayfairInvoice.class));
        }
    }

    /**
     * updatePackagingMark
     * 更新免二次包装标识
     *
     * @param somWayfairInvoiceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void updatePackagingMark(SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        if (StrUtil.isBlank(somWayfairInvoiceVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        SomWayfairRtvDetailVo detailVo = new SomWayfairRtvDetailVo();
        detailVo.setVasFlag(somWayfairInvoiceVo.getVasFlag());
        somWayfairRtvDetailMapper.createLambdaQuery().andEq("m_aid", somWayfairInvoiceVo.getAid()).andEq("seller_sku", somWayfairInvoiceVo.getSellerSku())
                .updateSelective(ConvertUtils.beanConvert(detailVo, SomWayfairRtvDetail.class));
    }

    /**
     * editRtvDetails
     * 编辑RTV明细
     *
     * @param somWayfairInvoiceVo
     * <AUTHOR>
     * @history
     */
    public void editRtvDetails(SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        if (StrUtil.isBlank(somWayfairInvoiceVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        if (CollectionUtil.isNotEmpty(somWayfairInvoiceVo.getDetailVoList())) {
            SomWayfairRtvDetail detail = somWayfairRtvDetailMapper.createLambdaQuery().andEq("m_aid", somWayfairInvoiceVo.getAid()).andIsNull("seller_sku").single();
            BigDecimal amount = somWayfairInvoiceVo.getDetailVoList().stream().map(SomWayfairRtvDetailVo::getSomStorageFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal overcharged = null;
            if (detail.getChargeAmount() != null) {
                overcharged =  detail.getChargeAmount().subtract(amount);
                if (overcharged.compareTo(BigDecimal.ZERO) <= 0) {
                    overcharged = null;
                }
            }
            somWayfairRtvDetailMapper.createLambdaQuery().andEq("m_aid", somWayfairInvoiceVo.getAid()).andIsNotNull("seller_sku").delete();
            List<SomWayfairRtvDetailVo> detailVoList = new ArrayList<>();
            BigDecimal finalOvercharged = overcharged;
            somWayfairInvoiceVo.getDetailVoList().forEach(f -> {
                SomWayfairRtvDetailVo vo = new SomWayfairRtvDetailVo();
                vo.setAid(IdUtil.fastSimpleUUID());
                vo.setMAid(somWayfairInvoiceVo.getAid());
                vo.setProductBucket(detail.getProductBucket());
                vo.setReturnOrderNumber(detail.getReturnOrderNumber());
                vo.setWayfairDimensionsL(detail.getWayfairDimensionsL());
                vo.setWayfairDimensionsW(detail.getWayfairDimensionsW());
                vo.setWayfairDimensionsH(detail.getWayfairDimensionsH());
                vo.setWayfairWeight(detail.getWayfairWeight());
                vo.setWayfairGirth(detail.getWayfairGirth());
                vo.setCurrency(detail.getCurrency());
                vo.setChargeAmount(detail.getChargeAmount());
                vo.setOvercharged(finalOvercharged);
                vo.setInvoice(detail.getInvoice());
                vo.setSellerSku(f.getSellerSku());
                vo.setChargeUnit(f.getChargeUnit());
                vo.setSomVolume(f.getSomVolume());
                vo.setSomFee(f.getSomFee());
                vo.setSomStorageFeeAmount(f.getSomStorageFeeAmount());
                detailVoList.add(vo);
            });
            somWayfairRtvDetailMapper.insertBatch(ConvertUtils.listConvert(detailVoList, SomWayfairRtvDetail.class));
        }
    }

    /**
     * queryRtvCost
     * 查询RTV产品费用
     *
     * @param queryVo
     * @return {@link SomWayfairRtvDetailVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomWayfairRtvDetailVo queryRtvCost(SomWayfairRtvDetailQueryVo queryVo) throws ValidateException {
        if (StrUtil.isBlank(queryVo.getSite()) || StrUtil.isBlank(queryVo.getImportDate()) || StrUtil.isBlank(queryVo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        LocalDate date = LocalDate.parse(queryVo.getImportDate());
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        SomWayfairCostView costView = somWayfairCostViewMapper.createLambdaQuery().andEq("site", queryVo.getSite()).andEq("seller_sku", queryVo.getSellerSku()).andBetween("create_time", start, end).single();
        SomWayfairRtvDetailVo detailVo = new SomWayfairRtvDetailVo();
        if (costView != null) {
            detailVo.setSomVolume(costView.getVolume());
            detailVo.setSomFee(costView.getRtvCost());
        }
        return detailVo;
    }
}
