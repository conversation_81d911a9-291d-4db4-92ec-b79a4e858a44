package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomSheinBlackList;
import com.zielsmart.mc.repository.entity.SomSheinBlackList;
import com.zielsmart.mc.repository.entity.SomSheinBlackList;
import com.zielsmart.mc.repository.mapper.SomSheinBlackListMapper;
import com.zielsmart.mc.vo.SomSheinBlackListVo;
import com.zielsmart.mc.vo.SomSheinBlackListVo;
import com.zielsmart.mc.vo.SomSheinBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomSheinBlackListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-06-30 14:30:28
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomSheinBlackListService {
    
    @Resource
    private SomSheinBlackListMapper somSheinBlackListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomSheinBlackListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomSheinBlackListVo> queryByPage(SomSheinBlackListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomSheinBlackListVo> pageResult = dynamicSqlManager.getMapper(SomSheinBlackListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomSheinBlackListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somSheinBlackListVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomSheinBlackListVo somSheinBlackListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somSheinBlackListVo) || StrUtil.isBlank(somSheinBlackListVo.getSite())  || StrUtil.isBlank(somSheinBlackListVo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somSheinBlackListMapper.createLambdaQuery().andEq("site", somSheinBlackListVo.getSite()).andEq("seller_sku", somSheinBlackListVo.getSellerSku()).count();
        if(count>0){
            throw new ValidateException("数据已存在，请检查数据");
        }
        somSheinBlackListVo.setAid(IdUtil.fastSimpleUUID());
        somSheinBlackListVo.setCreateNum(tokenUser.getJobNumber());
        somSheinBlackListVo.setCreateName(tokenUser.getUserName());
        somSheinBlackListVo.setCreateTime(DateTime.now().toJdkDate());
        somSheinBlackListMapper.insert(ConvertUtils.beanConvert(somSheinBlackListVo, SomSheinBlackList.class));
    }


    /**
     * delete
     * 删除
     * @param somSheinBlackListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomSheinBlackListVo somSheinBlackListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somSheinBlackListVo) || StrUtil.isBlank(somSheinBlackListVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somSheinBlackListMapper.createLambdaQuery().andEq("aid",somSheinBlackListVo.getAid()).delete();
    }

    public String export(SomSheinBlackListPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomSheinBlackListVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "Shein黑名单管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomSheinBlackListVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
    }


    public String importExcel(List<SomSheinBlackListVo> list, TokenUserInfo tokenUser) {
        String errorMsg = "";
        List<SomSheinBlackList> insertList = new ArrayList<>();
        List<String> errorList=new ArrayList<>();
        List<SomSheinBlackList> allList = somSheinBlackListMapper.all();
        Map<String, List<SomSheinBlackList>> allMap = allList.stream().collect(Collectors.groupingBy(e -> e.getSite()+e.getSellerSku(), Collectors.toList()));
        for (SomSheinBlackListVo vo : list) {
            if (allMap.containsKey(vo.getSite()+vo.getSellerSku())){
                errorList.add("客户："+vo.getSite()+"，展示码："+vo.getSellerSku()+"已存在!");
            }
            SomSheinBlackList config = new SomSheinBlackList();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setSite(vo.getSite());
            config.setSellerSku(vo.getSellerSku());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateTime(new Date());
            insertList.add(config);
        }
        if (CollectionUtil.isNotEmpty(errorList)){
            errorMsg= StrUtil.join("\n",errorList);
            return errorMsg;
        }
        somSheinBlackListMapper.insertBatch(insertList);
        return errorMsg;
    }

    public void batchDelete(List<SomSheinBlackListVo> list) {
        Set<String> sellerSkuSet = list.stream().map(SomSheinBlackListVo::getSellerSku).collect(Collectors.toSet());
        List<SomSheinBlackList> SomSheinBlackLists = new ArrayList<>();
        CollUtil.split(sellerSkuSet, 500).forEach(batch -> {
            SomSheinBlackLists.addAll(somSheinBlackListMapper.createLambdaQuery()
                    .andIn("seller_sku", batch)
                    .select("aid", "site", "seller_sku"));
        });

        Map<String, List<SomSheinBlackList>> filteredMap = SomSheinBlackLists.stream()
                .collect(Collectors.groupingBy(
                        x -> x.getSite() + x.getSellerSku(),
                        Collectors.toList()
                ));

        List<String> aidList = new ArrayList<>();
        for (SomSheinBlackListVo vo : list) {
            String key = vo.getSite() + vo.getSellerSku();
            if (filteredMap.containsKey(key)) {
                aidList.addAll(filteredMap.get(key).stream()
                        .map(SomSheinBlackList::getAid)
                        .collect(Collectors.toList()));
            }
        }
        CollUtil.split(aidList, 500).forEach(batch -> {
            int deleted = somSheinBlackListMapper.createLambdaQuery()
                    .andIn("aid", batch)
                    .delete();
            log.info("删除了 {} 条记录", deleted);
        });
    }
}
