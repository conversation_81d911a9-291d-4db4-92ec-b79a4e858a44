package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTemuWarehouseService;
import com.zielsmart.mc.vo.SomTemuWarehouseQueryVo;
import com.zielsmart.mc.vo.SomTemuWarehouseVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuLocalWarehouseConfigController
 * @description Temu平台仓库
 * @date 2025-06-30 11:35:22
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuWarehouse", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu平台仓库")
public class SomTemuWarehouseController extends BasicController {

    @Resource
    private SomTemuWarehouseService somTemuWarehouseService;

    @Operation(summary = "Temu仓库列表")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomTemuWarehouseVo>> queryWarehouse(@RequestBody SomTemuWarehouseQueryVo queryVo) throws ValidateException {
        List<SomTemuWarehouseVo> somTemuWarehouseVos = somTemuWarehouseService.queryWarehouse(queryVo);
        return ResultVo.ofSuccess(somTemuWarehouseVos);
    }
}
