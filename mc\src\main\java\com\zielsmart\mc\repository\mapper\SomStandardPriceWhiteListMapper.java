package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomStandardPriceWhiteList;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListVo;
import com.zielsmart.mc.vo.eya.EyaPriceWaringVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-05-17
 */

@SqlResource("somStandardPriceWhiteList")
public interface SomStandardPriceWhiteListMapper extends BaseMapper<SomStandardPriceWhiteList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomStandardPriceWhiteListVo>}
     * <AUTHOR> @history
     */
    PageResult<SomStandardPriceWhiteListVo> queryByPage(@Param("searchVo") SomStandardPriceWhiteListPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryByPlatformAndSite
     * 根据平台站点展示码查询定价白名单
     *
     * @param waringVo
     * @return long
     * <AUTHOR>
     * @history
     */
    long queryByPlatformAndSite(@Param("searchVo") EyaPriceWaringVo waringVo);

    List<SomStandardPriceWhiteListVo> exportExcel(@Param("searchVo")SomStandardPriceWhiteListPageSearchVo searchVo);
}
