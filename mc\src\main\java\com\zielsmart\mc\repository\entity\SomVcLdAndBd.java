package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 
* gen by 代码生成器 2025-05-13
*/

@Table(name="mc.som_vc_ld_and_bd")
public class SomVcLdAndBd implements java.io.Serializable {
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 账号名称/大分类
	 */
	@Column("account_name")
	private String accountName ;
	/**
	 * 供应商编码
	 */
	@Column("vendor_code")
	private String vendorCode ;
	/**
	 * 活动名称
	 */
	@Column("deal_name")
	private String dealName ;
	/**
	 * 营销活动类型
	 */
	@Column("deal_type")
	private Integer dealType ;
	/**
	 * 营销活动注册方式
	 */
	@Column("registration_type")
	private Integer registrationType ;
	/**
	 * 大促类型
	 */
	@Column("campaign_type")
	private Integer campaignType ;
	/**
	 * 起始日期
	 */
	@Column("start_date")
	private Date startDate ;
	/**
	 * 截止日期
	 */
	@Column("end_date")
	private Date endDate ;
	/**
	 * 活动状态
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 提报失败的原因
	 */
	@Column("failure_message")
	private String failureMessage ;
	/**
	 * 需要关注时，错误提示信息
	 */
	@Column("need_attention_reason")
	private String needAttentionReason ;
	/**
	 * 申请原因
	 */
	@Column("apply_reason")
	private Integer applyReason ;
	/**
	 * 自定义申请原因
	 */
	@Column("custom_reason")
	private String customReason ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomVcLdAndBd() {
	}

	public String getAid(){
		return  aid;
	}
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 账号名称/大分类
	*@return
	*/
	public String getAccountName(){
		return  accountName;
	}
	/**
	* 账号名称/大分类
	*@param  accountName
	*/
	public void setAccountName(String accountName ){
		this.accountName = accountName;
	}
	/**
	* 供应商编码
	*@return
	*/
	public String getVendorCode(){
		return  vendorCode;
	}
	/**
	* 供应商编码
	*@param  vendorCode
	*/
	public void setVendorCode(String vendorCode ){
		this.vendorCode = vendorCode;
	}
	/**
	* 活动名称
	*@return
	*/
	public String getDealName(){
		return  dealName;
	}
	/**
	* 活动名称
	*@param  dealName
	*/
	public void setDealName(String dealName ){
		this.dealName = dealName;
	}
	/**
	* 营销活动类型
	*@return
	*/
	public Integer getDealType(){
		return  dealType;
	}
	/**
	* 营销活动类型
	*@param  dealType
	*/
	public void setDealType(Integer dealType ){
		this.dealType = dealType;
	}
	/**
	* 营销活动注册方式
	*@return
	*/
	public Integer getRegistrationType(){
		return  registrationType;
	}
	/**
	* 营销活动注册方式
	*@param  registrationType
	*/
	public void setRegistrationType(Integer registrationType ){
		this.registrationType = registrationType;
	}
	/**
	* 大促类型
	*@return
	*/
	public Integer getCampaignType(){
		return  campaignType;
	}
	/**
	* 大促类型
	*@param  campaignType
	*/
	public void setCampaignType(Integer campaignType ){
		this.campaignType = campaignType;
	}
	/**
	* 起始日期
	*@return
	*/
	public Date getStartDate(){
		return  startDate;
	}
	/**
	* 起始日期
	*@param  startDate
	*/
	public void setStartDate(Date startDate ){
		this.startDate = startDate;
	}
	/**
	* 截止日期
	*@return
	*/
	public Date getEndDate(){
		return  endDate;
	}
	/**
	* 截止日期
	*@param  endDate
	*/
	public void setEndDate(Date endDate ){
		this.endDate = endDate;
	}
	/**
	* 活动状态
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 活动状态
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 提报失败的原因
	*@return
	*/
	public String getFailureMessage(){
		return  failureMessage;
	}
	/**
	* 提报失败的原因
	*@param  failureMessage
	*/
	public void setFailureMessage(String failureMessage ){
		this.failureMessage = failureMessage;
	}
	/**
	* 需要关注时，错误提示信息
	*@return
	*/
	public String getNeedAttentionReason(){
		return  needAttentionReason;
	}
	/**
	* 需要关注时，错误提示信息
	*@param  needAttentionReason
	*/
	public void setNeedAttentionReason(String needAttentionReason ){
		this.needAttentionReason = needAttentionReason;
	}
	/**
	* 申请原因
	*@return
	*/
	public Integer getApplyReason(){
		return  applyReason;
	}
	/**
	* 申请原因
	*@param  applyReason
	*/
	public void setApplyReason(Integer applyReason ){
		this.applyReason = applyReason;
	}
	/**
	* 自定义申请原因
	*@return
	*/
	public String getCustomReason(){
		return  customReason;
	}
	/**
	* 自定义申请原因
	*@param  customReason
	*/
	public void setCustomReason(String customReason ){
		this.customReason = customReason;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
