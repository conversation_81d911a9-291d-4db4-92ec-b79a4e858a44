package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMListingPriceService;
import com.zielsmart.mc.vo.SomListingPriceExtVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller.zbpm
 * @title ZBPMListingPriceController
 * @description
 * @date 2022-07-11 14:03:36
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/zbpm-listingPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台价接口")
public class ZBPMListingPriceController extends BasicController {

    @Resource
    private ZBPMListingPriceService service;

    /**
     * update
     * 更新平台价
     *
     * @param updateVoList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新平台价")
    @PostMapping(value = "/updateListingPrice")
    public ResultVo<String> updateListingPrice(@RequestBody List<SomListingPriceExtVo> updateVoList, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        service.updateListingPrice(updateVoList,tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
