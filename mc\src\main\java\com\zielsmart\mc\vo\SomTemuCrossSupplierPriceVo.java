package com.zielsmart.mc.vo;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

/*
* Temu跨境供货价的VO实体对象
* gen by 代码生成器 2025-06-27
*/

@Data
@Schema(title = "Temu跨境供货价",name = "SomTemuCrossSupplierPriceVo")
public class SomTemuCrossSupplierPriceVo implements java.io.Serializable {
	/**
	 * 主键aid
	 */
    @Schema(description = "主键aid",name="aid")
	private String aid ;
	/**
	 * 账号
	 */
    @Schema(description = "账号",name="accountTag")
	private String accountTag ;
	/**
	 * 店铺ID
	 */
    @Schema(description = "店铺ID",name="accountId")
	private Long accountId ;
	/**
	 * 商品ID
	 */
    @Schema(description = "商品ID",name="productId")
	private Long productId ;
	/**
	 * SKC ID
	 */
    @Schema(description = "SKC ID",name="productSkcId")
	private Long productSkcId ;
	/**
	 * Product sku id
	 */
    @Schema(description = "Product sku id",name="productSkuId")
	private Long productSkuId ;
	/**
	 * 供货价
	 */
    @Schema(description = "供货价",name="supplierPrice")
	private BigDecimal supplierPrice ;
	/**
	 * 币种
	 */
    @Schema(description = "币种",name="currency")
	private String currency ;
	/**
	 * 站点供货价列表，仅半托管有值
	 */
    @Schema(description = "站点供货价列表，仅半托管有值",name="siteSupplierPrices")
	private Object siteSupplierPrices ;
	/**
	 * 调用接口下载的时间
	 */
    @Schema(description = "调用接口下载的时间",name="downloadTime")
	private Date downloadTime ;



}
