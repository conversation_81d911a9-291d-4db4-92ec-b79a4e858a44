package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McProductSalesService;
import com.zielsmart.mc.vo.McProductSalesSearchVo;
import com.zielsmart.mc.vo.McProductSalesVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McProductSalesController
 * @description
 * @date 2021-08-09 11:23:52
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/product-sales", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "产品销售视图")
public class McProductSalesController extends BasicController {

    @Resource
    McProductSalesService productSalesService;

    @Operation(summary = "根据站点sku模糊查询产品销售视图")
    @PostMapping(value = "/query-by-site-sku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<McProductSalesVo>> queryBySiteAndSku(@RequestBody McProductSalesSearchVo searchVo) {
        return ResultVo.ofSuccess(productSalesService.queryBySiteAndSku(searchVo));
    }

    @Operation(summary = "根据平台站点查询展示码")
    @PostMapping(value = "/queryDisplayProductCode")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<String>> queryDisplayProductCode(@RequestBody McProductSalesSearchVo searchVo) throws Exception {
        return ResultVo.ofSuccess(productSalesService.queryDisplayProductCode(searchVo));
    }

    @Operation(summary = "根据平台站点查询非寄售产品销售视图")
    @PostMapping(value = "/queryByPlatformAndSite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<McProductSalesVo>> queryByPlatformAndSite(@RequestBody McProductSalesSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(productSalesService.queryByPlatformAndSite(searchVo));
    }

    @Operation(summary = "根据国家发货方式和sku前三位查询产品销售视图")
    @PostMapping(value = "/queryByCountryChanel")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<McProductSalesVo>> queryByCountryChanel(@RequestBody McProductSalesSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(productSalesService.queryByCountryChanel(searchVo));
    }

    @Operation(summary = "根据平台、站点查询组合组套产品")
    @PostMapping(value = "/querySetProducts")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<McProductSalesVo>> querySetProducts(@RequestBody McProductSalesSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(productSalesService.querySetProducts(searchVo));
    }

    @Operation(summary = "根据参数查询产品销售视图，注：sql中不写死固定值，一切由前端传参决定")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    public ResultVo<List<McProductSalesVo>> query(@RequestBody McProductSalesSearchVo searchVo) {
        return ResultVo.ofSuccess(productSalesService.query(searchVo));
    }
}

