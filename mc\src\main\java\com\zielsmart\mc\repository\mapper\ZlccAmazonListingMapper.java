package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.ZlccAmazonListingPageSearchVo;
import com.zielsmart.mc.vo.ZlccAmazonListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-12-11
*/

@SqlResource("zlccAmazonListing")
public interface ZlccAmazonListingMapper extends BaseMapper<ZlccAmazonListing> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.ZlccAmazonListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<ZlccAmazonListingVo> queryByPage(@Param("searchVo")ZlccAmazonListingPageSearchVo searchVo, PageRequest pageRequest);

    long findAsinCount(@Param("value") String value);
}
