package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* Temu库存推送黑名单
* gen by 代码生成器 2024-11-05
*/

@Table(name="mc.som_temu_black_list")
public class SomTemuBlackList implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 平台SKU唯一编码
	 */
	@Column("product_sku_id")
	private String productSkuId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人工号
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomTemuBlackList() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 平台SKU唯一编码
	*@return
	*/
	public String getProductSkuId(){
		return  productSkuId;
	}
	/**
	* 平台SKU唯一编码
	*@param  productSkuId
	*/
	public void setProductSkuId(String productSkuId ){
		this.productSkuId = productSkuId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人工号
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
