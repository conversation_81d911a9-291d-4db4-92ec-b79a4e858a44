package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonShelvesRecordService;
import com.zielsmart.mc.vo.SomAmazonShelvesRecordPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonShelvesRecordVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonShelvesRecordController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAmazonShelvesRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "PAN-EU 上架下架管理")
public class SomAmazonShelvesRecordController extends BasicController{

    @Resource
    SomAmazonShelvesRecordService somAmazonShelvesRecordService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonShelvesRecordVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonShelvesRecordVo>> queryByPage(@RequestBody SomAmazonShelvesRecordPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonShelvesRecordService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonShelvesRecordPageSearchVo searchVo) {
        String data = somAmazonShelvesRecordService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
