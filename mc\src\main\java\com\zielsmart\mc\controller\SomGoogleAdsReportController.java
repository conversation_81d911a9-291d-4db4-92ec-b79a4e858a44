package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomGoogleAdsReportService;
import com.zielsmart.mc.vo.SomGoogleAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomGoogleAdsReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomGoogleAdsReportController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somGoogleAdsReport", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Google广告报表管理")
public class SomGoogleAdsReportController extends BasicController{

    @Resource
    SomGoogleAdsReportService somGoogleAdsReportService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomGoogleAdsReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomGoogleAdsReportVo>> queryByPage(@RequestBody SomGoogleAdsReportPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somGoogleAdsReportService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomGoogleAdsReportPageSearchVo searchVo) {
        String data = somGoogleAdsReportService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
    @Operation(summary = "获取下拉框数据")
    @PostMapping(value = "/get-list")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomGoogleAdsReportVo>> getList(@RequestBody  Map map) {
        return ResultVo.ofSuccess(somGoogleAdsReportService.getList(map));
    }
}
