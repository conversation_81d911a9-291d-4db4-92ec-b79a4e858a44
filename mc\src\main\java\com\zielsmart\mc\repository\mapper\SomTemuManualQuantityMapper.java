package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomTemuManualQuantityPageSearchVo;
import com.zielsmart.mc.vo.SomTemuManualQuantityVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-07-05
 */

@SqlResource("somTemuManualQuantity")
public interface SomTemuManualQuantityMapper extends BaseMapper<SomTemuManualQuantity> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomTemuManualQuantityVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuManualQuantityVo> queryByPage(@Param("searchVo") SomTemuManualQuantityPageSearchVo searchVo, PageRequest pageRequest);
}
