package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McSkuInfoService;
import com.zielsmart.mc.vo.McSkuInfoExVo;
import com.zielsmart.mc.vo.McSkuInfoPageSearchVo;
import com.zielsmart.mc.vo.McSkuInfoVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McSkuInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcSkuInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "产品基础视图管理")
public class McSkuInfoController extends BasicController{

    @Resource
    McSkuInfoService mcSkuInfoService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McSkuInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McSkuInfoVo>> queryByPage(@RequestBody McSkuInfoPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcSkuInfoService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcSkuInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McSkuInfoVo mcSkuInfoVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcSkuInfoService.save(mcSkuInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param mcSkuInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McSkuInfoVo mcSkuInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcSkuInfoService.update(mcSkuInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcSkuInfoVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McSkuInfoVo mcSkuInfoVo) throws ValidateException {
        mcSkuInfoService.delete(mcSkuInfoVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * querySkuInfo
     * 查询sku基础信息
     * @param skuCode
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.McSkuInfoExVo>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询sku基础信息")
    @PostMapping(value = "/querySkuInfo")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<McSkuInfoExVo> querySkuInfo(@RequestBody String skuCode) {
        return ResultVo.ofSuccess(mcSkuInfoService.querySkuInfo(skuCode));
    }
}
