package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* Shein可售仓库配置
* gen by 代码生成器 2023-09-20
*/

@Table(name="mc.som_shein_warehouse_config")
public class SomSheinWarehouseConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台仓库ID
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 平台仓库名称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 可售仓库
	 */
	@Column("useable_warehouse_code")
	private String useableWarehouseCode ;
	/**
	 * 可售库区
	 */
	@Column("useable_storage_code")
	private String useableStorageCode ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public SomSheinWarehouseConfig() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台仓库ID
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 平台仓库ID
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 平台仓库名称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 平台仓库名称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 可售仓库
	*@return
	*/
	public String getUseableWarehouseCode(){
		return  useableWarehouseCode;
	}
	/**
	* 可售仓库
	*@param  useableWarehouseCode
	*/
	public void setUseableWarehouseCode(String useableWarehouseCode ){
		this.useableWarehouseCode = useableWarehouseCode;
	}
	/**
	* 可售库区
	*@return
	*/
	public String getUseableStorageCode(){
		return  useableStorageCode;
	}
	/**
	* 可售库区
	*@param  useableStorageCode
	*/
	public void setUseableStorageCode(String useableStorageCode ){
		this.useableStorageCode = useableStorageCode;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
