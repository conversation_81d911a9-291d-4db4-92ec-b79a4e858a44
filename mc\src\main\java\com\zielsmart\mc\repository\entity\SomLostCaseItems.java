package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* lost索赔从表
* gen by 代码生成器 2023-04-03
*/

@Table(name="mc.som_lost_case_items")
public class SomLostCaseItems implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 主表ID
	 */
	@Column("lost_case_id")
	private String lostCaseId ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * FNSKU
	 */
	@Column("fnsku")
	private String fnsku ;
	/**
	 * asin
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 索赔数量
	 */
	@Column("lost_quantity")
	private Integer lostQuantity ;

	public SomLostCaseItems() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 主表ID
	*@return
	*/
	public String getLostCaseId(){
		return  lostCaseId;
	}
	/**
	* 主表ID
	*@param  lostCaseId
	*/
	public void setLostCaseId(String lostCaseId ){
		this.lostCaseId = lostCaseId;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* FNSKU
	*@return
	*/
	public String getFnsku(){
		return  fnsku;
	}
	/**
	* FNSKU
	*@param  fnsku
	*/
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	/**
	* asin
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* asin
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 索赔数量
	*@return
	*/
	public Integer getLostQuantity(){
		return  lostQuantity;
	}
	/**
	* 索赔数量
	*@param  lostQuantity
	*/
	public void setLostQuantity(Integer lostQuantity ){
		this.lostQuantity = lostQuantity;
	}

}
