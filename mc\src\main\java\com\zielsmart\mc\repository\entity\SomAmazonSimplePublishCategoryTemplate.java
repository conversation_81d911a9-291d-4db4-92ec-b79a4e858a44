package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Amazon简单上货类目模板配置
 * gen by 代码生成器 2025-07-17
 */

@Table(name = "mc.som_amazon_simple_publish_category_template")
public class SomAmazonSimplePublishCategoryTemplate implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 平台类目ID
     */
    @Column("platform_category_id")
    private String platformCategoryId;
    /**
     * 平台类目名称
     */
    @Column("platform_category_name")
    private String platformCategoryName;
    /**
     * 模版附件URL
     */
    @Column("template_url")
    private String templateUrl;
	/**
	 * 页码
	 */
	@Column("sheet_num")
	private Integer sheetNum;
	/**
     * 公司四级类目编码
     */
    @Column("category_code")
    private String categoryCode;
    /**
     * 公司四级类目名称
     */
    @Column("category_name")
    private String categoryName;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomAmazonSimplePublishCategoryTemplate() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 平台类目ID
     *
     * @return
     */
    public String getPlatformCategoryId() {
        return platformCategoryId;
    }

    /**
     * 平台类目ID
     *
     * @param platformCategoryId
     */
    public void setPlatformCategoryId(String platformCategoryId) {
        this.platformCategoryId = platformCategoryId;
    }

    /**
     * 平台类目名称
     *
     * @return
     */
    public String getPlatformCategoryName() {
        return platformCategoryName;
    }

    /**
     * 平台类目名称
     *
     * @param platformCategoryName
     */
    public void setPlatformCategoryName(String platformCategoryName) {
        this.platformCategoryName = platformCategoryName;
    }

    /**
     * 模版附件URL
     *
     * @return
     */
    public String getTemplateUrl() {
        return templateUrl;
    }

    /**
     * 模版附件URL
     *
     * @param templateUrl
     */
    public void setTemplateUrl(String templateUrl) {
        this.templateUrl = templateUrl;
    }

    /**
     * 公司四级类目编码
     *
     * @return
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 公司四级类目编码
     *
     * @param categoryCode
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    /**
     * 公司四级类目名称
     *
     * @return
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 公司四级类目名称
     *
     * @param categoryName
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public Integer getSheetNum() {
		return sheetNum;
	}

	public void setSheetNum(Integer sheetNum) {
		this.sheetNum = sheetNum;
	}
}
