package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomConforamaLengowPageSearchVo;
import com.zielsmart.mc.vo.SomConforamaLengowVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.HashMap;
import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-09-09
*/

@SqlResource("somConforamaLengow")
public interface SomConforamaLengowMapper extends BaseMapper<SomConforamaLengow> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomConforamaLengowVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<HashMap<String, Object>> queryByPage(@Param("searchVo")SomConforamaLengowPageSearchVo searchVo, PageRequest pageRequest);

    default void batchUpdate(List<SomConforamaLengow> updateList){
        this.getSQLManager().updateBatch(SqlId.of("somConforamaLengow.batchUdate"), updateList);
    }
}
