package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonShelvesRecord;
import com.zielsmart.mc.vo.SomAmazonShelvesRecordPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonShelvesRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-07-20
*/

@SqlResource("somAmazonShelvesRecord")
public interface SomAmazonShelvesRecordMapper extends BaseMapper<SomAmazonShelvesRecord> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonShelvesRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonShelvesRecordVo> queryByPage(@Param("searchVo")SomAmazonShelvesRecordPageSearchVo searchVo, PageRequest pageRequest);
}
