package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McVcWarehouseConfig;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.vo.McVcWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McVcWarehouseConfigVo;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-12-01
 */

@SqlResource("mcVcWarehouseConfig")
public interface McVcWarehouseConfigMapper extends BaseMapper<McVcWarehouseConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McVcWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McVcWarehouseConfigVo> queryByPage(@Param("searchVo") McVcWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * findWarehouseAndSl
     * 查询仓库库区信息
     * @param platformWarehouseCodes
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWareHouseAndSlVo>}
     * <AUTHOR>
     * @history
     */
    List<McWareHouseAndSlVo> findWarehouseAndSl(@Param("platformWarehouseCodes")List<String> platformWarehouseCodes);

    /**
     * queryConfigurableWarehouses
     * 查询可配置仓库
     * @param marketCode
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McWarehouse>}
     * <AUTHOR>
     * @history
     */
    List<McWarehouse> queryConfigurableWarehouses(@Param("marketCode") String marketCode);

    /**
     * queryAllConfigList
     * 查询已配置的仓库信息
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWareHouseAndSlVo>}
     * <AUTHOR>
     * @history
     */
    List<McWareHouseAndSlVo> queryAllConfigList();
}
