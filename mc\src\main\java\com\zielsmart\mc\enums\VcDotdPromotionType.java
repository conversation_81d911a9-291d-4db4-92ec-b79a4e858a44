package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

/**
 * VC营销活动类型枚举
 */
@AllArgsConstructor
public enum VcDotdPromotionType {
    PRICE_DISCOUNT(10, "Price Discount"),
    LIGHTING_DEAL(20, "Lightning Deal"),
    BEST_DEAL(30, "Best Deal"),
    POINTS_PROMOTION(40, "Points promotion"),
    DOTD(50, "DOTD"),


    ;

    private final Integer type;
    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
