package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.McWarehouseConfig;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import com.zielsmart.mc.vo.McWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
*
* gen by 代码生成器 mapper 2021-08-04
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
*
* gen by 代码生成器 mapper 2021-08-10
*/

@SqlResource("mcWarehouseConfig")
public interface McWarehouseConfigMapper extends BaseMapper<McWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McWarehouseConfigVo> queryByPage(@Param("searchVo")McWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<McWareHouseAndSlVo> findWarehouseCodeAndName(@Param("platforms")List<String> platforms,@Param("sites")List<String> sites);

    List<McWarehouseConfigVo> findWarehouseByPlatformSite(@Param("warehouse")McWarehouseConfigVo warehouse);

    /**
     * queryAllConfiguredInfos
     * 获取所有已配置的平台、站点
     *
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McWarehouseConfig>}
     * <AUTHOR>
     * @history
     */
    List<McWarehouseConfig> queryAllConfiguredInfos();

    /**
     * queryConfigurableWarehouses
     * 查询可配置仓库
     * @param marketCode
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McWarehouse>}
     * <AUTHOR>
     * @history
     */
    List<McWarehouse> queryConfigurableWarehouses(@Param("market") String market);
}
