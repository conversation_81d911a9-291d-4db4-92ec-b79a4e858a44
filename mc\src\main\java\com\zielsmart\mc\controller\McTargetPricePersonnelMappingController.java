package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McTargetPricePersonnelMappingService;
import com.zielsmart.mc.vo.McDeptVo;
import com.zielsmart.mc.vo.McTargetPricePersonnelMappingPageSearchVo;
import com.zielsmart.mc.vo.McTargetPricePersonnelMappingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McTargetPricePersonnelMappingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcTargetPricePersonnelMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "目标价审核配置")
public class McTargetPricePersonnelMappingController extends BasicController {

    @Resource
    McTargetPricePersonnelMappingService mcTargetPricePersonnelMappingService;

    /**
     * importExcel
     * 导入数据
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"业务组名称", "运营负责人工号", "运营负责人姓名", "审核负责人工号", "审核负责人姓名"};
        importParams.setImportFields(arr);
        ExcelImportResult<McTargetPricePersonnelMappingVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), McTargetPricePersonnelMappingVo.class, importParams);
        } catch (Exception e) {
                throw new ValidateException("导入模板有误,请检查模板");
        }
        List<McTargetPricePersonnelMappingVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        String str = mcTargetPricePersonnelMappingService.importExcel(list, tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.McTargetPricePersonnelMappingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<McTargetPricePersonnelMappingVo>> queryByPage(@RequestBody McTargetPricePersonnelMappingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcTargetPricePersonnelMappingService.queryByPage(searchVo));
    }

    /**
     * export
     * 导出数据
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出数据")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody McTargetPricePersonnelMappingPageSearchVo searchVo) {
        String data = mcTargetPricePersonnelMappingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "获取业务组列表")
    @PostMapping(value = "/getMcGroupCodeList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<McDeptVo>> getMcGroupCodeList() {
        return ResultVo.ofSuccess(mcTargetPricePersonnelMappingService.getMcGroupCodeList());
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @param
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/TargetPricePersonnelMappingTemplate.xlsx";
    }

    /**
     * delete
     * 删除
     * @param mcWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McTargetPricePersonnelMappingVo mcWarehouseConfigVo) throws ValidateException {
        mcTargetPricePersonnelMappingService.delete(mcWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }
}
