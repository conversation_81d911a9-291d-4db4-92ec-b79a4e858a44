package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomTemuChecklist;
import com.zielsmart.mc.repository.mapper.SomTemuChecklistMapper;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.SomTemuChecklistExtVo;
import com.zielsmart.mc.vo.SomTemuChecklistPageSearchVo;
import com.zielsmart.mc.vo.SomTemuChecklistUpdateVo;
import com.zielsmart.mc.vo.SomTemuChecklistVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuChecklistService {
    
    @Resource
    private SomTemuChecklistMapper somTemuChecklistMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private IMagicService iMagicService;

    @Value("${magic.head.token}")
    private String token;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuChecklistVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuChecklistExtVo> queryByPage(SomTemuChecklistPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuChecklistExtVo> pageResult = dynamicSqlManager.getMapper(SomTemuChecklistMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuChecklistExtVo.class, searchVo);
    }

    public void acceptQuotation(String site) throws ValidateException {
        //DOTO 调用MaigcApi接口
        ResultVo result = null;
        try {
            result = iMagicService.temuBatchAcceptPrice(token,site);
        } catch (Exception e) {
            Throwable cause = e;
            if (e instanceof UndeclaredThrowableException) {
                cause = ((UndeclaredThrowableException) e).getUndeclaredThrowable();
            }
            log.error("MagicApiTemu批量接受核单价服务异常：{}", cause == null ? "cause is null" : cause.toString());
            throw new ValidateException("MagicApiTemu批量接受核单价服务异常");
        }
    }

    public void batchDicker(SomTemuChecklistUpdateVo somTemuChecklistUpdateVo, TokenUserInfo tokenUser) throws ValidateException {
        List<SomTemuChecklist> list = somTemuChecklistMapper.createLambdaQuery().andIn("aid", somTemuChecklistUpdateVo.getAids()).select();
        for (SomTemuChecklist somTemuChecklist : list) {
            somTemuChecklist.setSupplierPrice(somTemuChecklistUpdateVo.getSupplierPrice());
            somTemuChecklist.setSupplierPriceCurrency(somTemuChecklistUpdateVo.getSupplierPriceCurrency());
            somTemuChecklist.setBargainReason(somTemuChecklistUpdateVo.getBargainReason());
            somTemuChecklist.setHandleType(99);
        }
        somTemuChecklistMapper.batchUpdate(list);
        //DOTO 调用MaigcApi接口
        ResultVo result = null;
        try {
            result = iMagicService.temuBatchNegotiatePrice(token, somTemuChecklistUpdateVo);
        } catch (Exception e) {
            Throwable cause = e;
            if (e instanceof UndeclaredThrowableException) {
                cause = ((UndeclaredThrowableException) e).getUndeclaredThrowable();
            }
            log.error("MagicApiTemu批量议价服务异常：{}", cause == null ? "cause is null" : cause.toString());
            throw new ValidateException("MagicApiTemu批量议价服务异常");
        }
    }
}
