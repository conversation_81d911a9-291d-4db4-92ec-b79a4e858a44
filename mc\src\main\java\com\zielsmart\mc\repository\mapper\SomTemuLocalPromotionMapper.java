package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuLocalPromotion;
import com.zielsmart.mc.repository.entity.SomTemuLocalPromotionDetail;
import com.zielsmart.mc.vo.SomTemuLocalPromotionDetailPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalPromotionDetailVo;
import com.zielsmart.mc.vo.SomTemuLocalPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalPromotionVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomTemuLocalPromotionMapper
 * @description Temu本本营销活动Mapper
 * @date 2025-03-12 09:10:57
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuLocalPromotion")
public interface SomTemuLocalPromotionMapper extends BaseMapper<SomTemuLocalPromotion> {

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuLocalPromotionVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuLocalPromotionVo> queryByPage(@Param("searchVo") SomTemuLocalPromotionPageSearchVo searchVo, PageRequest pageRequest);

    List<SomTemuLocalPromotionVo> exportExcel(@Param("searchVo") SomTemuLocalPromotionPageSearchVo searchVo);

    PageResult<SomTemuLocalPromotionDetailVo> detailCandidateQueryByPage(@Param("searchVo")SomTemuLocalPromotionDetailPageSearchVo searchVo, PageRequest pageRequest);

    List<SomTemuLocalPromotionDetailVo> candidateSelect(@Param("accountIds") Set<String> accountIds);
}
