package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMStockWhiteListService;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/zbpm-stockwhitelist", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "库存白名单")
public class ZBPMStockWhiteListController extends BasicController {

    @Resource
    private ZBPMStockWhiteListService service;

    @Operation(summary = "获取市场")
    @PostMapping(value = "/get-market")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZBPMStockWhiteListVo>> getMarket() {
        return ResultVo.ofSuccess(service.getMarket());
    }

    @Operation(summary = "根据市场获取平台")
    @PostMapping(value = "/get-Platforms")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZBPMStockWhiteListVo>> getPlatforms(@RequestBody ZBPMWhiteListSearchVo searchVo) {
        return ResultVo.ofSuccess(service.getPlatforms(searchVo));
    }

    @Operation(summary = "根据市场、平台和SKU获取所有的产品销售视图")
    @PostMapping(value = "/get-ProductSales")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZBPMStockWhiteListVo>> getAllProductSales(@RequestBody ZBPMWhiteListSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(service.getAllProductSales(searchVo));
    }

    @Operation(summary = "保存库存白名单")
    @PostMapping(value = "/saveStockWhiteList")
    public ResultVo<String> saveStockWhiteList(@RequestBody ZBPMStockWhiteListVo vo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) {
        String str = service.saveStockWhiteList(vo, tokenUserInfo);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess(null);
        } else {
            return ResultVo.ofFail(str);
        }
    }
}
