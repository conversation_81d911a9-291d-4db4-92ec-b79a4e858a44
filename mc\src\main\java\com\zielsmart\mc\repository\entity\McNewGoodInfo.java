package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* 新品上货表
* gen by 代码生成器 2022-02-09
*/

@Table(name="mc.mc_new_good_info")
public class McNewGoodInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * sku
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 寄售标识 0非寄售 1寄售
	 */
	@Column("consignment_flag")
	private Integer consignmentFlag ;
	/**
	 * 三级分类编码
	 */
	@Column("category_code")
	private String categoryCode ;
	/**
	 * 品牌
	 */
	@Column("brand")
	private String brand ;
	/**
	 * 上货类型10简单上货 20详细上货
	 */
	@Column("on_type")
	private Integer onType ;
	/**
	 * 标题
	 */
	@Column("title")
	private String title ;
	/**
	 * 首图链接
	 */
	@Column("main_image_url")
	private String mainImageUrl ;
	/**
	 * 大类目编码
	 */
	@Column("main_category")
	private String mainCategory ;
	/**
	 * 小类目编码
	 */
	@Column("sub_category_code")
	private String subCategoryCode ;
	/**
	 * unitCount
	 */
	@Column("unit_count")
	private Integer unitCount ;
	/**
	 * 是否IPQ扩款 0否 1是
	 */
	@Column("ipq_flag")
	private Integer ipqFlag ;
	/**
	 * PQ数量
	 */
	@Column("ipq_unit")
	private Integer ipqUnit ;
	/**
	 * 售价
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 目标价
	 */
	@Column("target_price")
	private BigDecimal targetPrice ;
	/**
	 * 运费模板ID
	 */
	@Column("shipping_group_id")
	private String shippingGroupId ;
	/**
	 * 产品尺寸
	 */
	@Column("product_size")
	private String productSize ;
	/**
	 * 产品净重
	 */
	@Column("product_weight")
	private BigDecimal productWeight ;
	/**
	 * 净重计量单位
	 */
	@Column("product_weight_unit")
	private String productWeightUnit ;
	/**
	 * 不可售处理 字典表，枚举值
	 */
	@Column("unsaleable_treatment")
	private String unsaleableTreatment ;
	/**
	 * 包装尺寸
	 */
	@Column("packaging_size")
	private String packagingSize ;
	/**
	 * 产品毛重
	 */
	@Column("product_gross_weight")
	private BigDecimal productGrossWeight ;
	/**
	 * 毛重计量单位
	 */
	@Column("product_gross_weight_unit")
	private String productGrossWeightUnit ;
	/**
	 * 平台特殊计划
	 */
	@Column("platform_special_plan")
	private String platformSpecialPlan ;
	/**
	 * 预计到港时间
	 */
	@Column("eta_date")
	private Date etaDate ;
	/**
	 * 需求单号
	 */
	@Column("demand_number")
	private String demandNumber ;
	/**
	 * 上货图链接
	 */
	@Column("product_image_url")
	private String productImageUrl ;
	/**
	 * A+类型 0无 1EBC 2.EMC
	 */
	@Column("aplus_type")
	private Integer aplusType ;
	/**
	 * 0否 1是
	 */
	@Column("post_flag")
	private Integer postFlag ;
	@Column("ean_code")
	private String eanCode ;
	@Column("asin")
	private String asin ;
	@Column("fnsku")
	private String fnsku ;
	@Column("batch_id")
	private String batchId ;
	/**
	 * 10.新建 20.上货中 30.上货完成
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 *  同步产品销售视图状态 0表示未同步 1同步成功 99同步失败
	 */
	@Column("sync_sales")
	private Integer syncSales ;
	/**
	 * 同步产品销售视图的错误信息
	 */
	@Column("sync_msg")
	private String syncMsg ;
	/**
	 * 简单上货时间
	 */
	@Column("simple_on_date")
	private Date simpleOnDate ;
	/**
	 * 简单上货操作人工号
	 */
	@Column("simple_on_num")
	private String simpleOnNum ;
	/**
	 * 简单上货操作人姓名
	 */
	@Column("simple_on_name")
	private String simpleOnName ;
	/**
	 * 详细上货时间
	 */
	@Column("detailed_on_date")
	private Date detailedOnDate ;
	/**
	 * 详细上货操作人工号
	 */
	@Column("detailed_on_num")
	private String detailedOnNum ;
	/**
	 * 详细上货操作人姓名
	 */
	@Column("detailed_on_name")
	private String detailedOnName ;
	/**
	 * 销售下的任务单是否不正确 0 错误 1 正常
	 */
	@Column("error_status")
	private Integer errorStatus ;
	/**
	 * 错误内容
	 */
	@Column("error_msg")
	private String errorMsg ;
	/**
	 * 业务组
	 */
	@Column("business_group_code")
	private String businessGroupCode ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;

	@Column("last_modify_time")
	private Date lastModifyTime ;

	@Column("estimated_launch_date")
	private Date estimatedLaunchDate ;

	@Column("business_manager")
	private String businessManager ;
	@Column("business_manager_name")
	private String businessManagerName ;
	@Column("operations_manager")
	private String operationsManager ;
	@Column("operations_manager_name")
	private String operationsManagerName ;


	public McNewGoodInfo() {
	}

	public Date getEstimatedLaunchDate() {
		return estimatedLaunchDate;
	}

	public void setEstimatedLaunchDate(Date estimatedLaunchDate) {
		this.estimatedLaunchDate = estimatedLaunchDate;
	}

	public String getBusinessManager() {
		return businessManager;
	}

	public void setBusinessManager(String businessManager) {
		this.businessManager = businessManager;
	}

	public String getBusinessManagerName() {
		return businessManagerName;
	}

	public void setBusinessManagerName(String businessManagerName) {
		this.businessManagerName = businessManagerName;
	}

	public String getOperationsManager() {
		return operationsManager;
	}

	public void setOperationsManager(String operationsManager) {
		this.operationsManager = operationsManager;
	}

	public String getOperationsManagerName() {
		return operationsManagerName;
	}

	public void setOperationsManagerName(String operationsManagerName) {
		this.operationsManagerName = operationsManagerName;
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * 平台
	 *@return
	 */
	public String getPlatform(){
		return  platform;
	}
	/**
	 * 平台
	 *@param  platform
	 */
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	 * 站点
	 *@return
	 */
	public String getSite(){
		return  site;
	}
	/**
	 * 站点
	 *@param  site
	 */
	public void setSite(String site ){
		this.site = site;
	}
	/**
	 * sku
	 *@return
	 */
	public String getSku(){
		return  sku;
	}
	/**
	 * sku
	 *@param  sku
	 */
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	 * 展示码
	 *@return
	 */
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	 * 展示码
	 *@param  sellerSku
	 */
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	 * 寄售标识 0非寄售 1寄售
	 *@return
	 */
	public Integer getConsignmentFlag(){
		return  consignmentFlag;
	}
	/**
	 * 寄售标识 0非寄售 1寄售
	 *@param  consignmentFlag
	 */
	public void setConsignmentFlag(Integer consignmentFlag ){
		this.consignmentFlag = consignmentFlag;
	}
	/**
	 * 三级分类编码
	 *@return
	 */
	public String getCategoryCode(){
		return  categoryCode;
	}
	/**
	 * 三级分类编码
	 *@param  categoryCode
	 */
	public void setCategoryCode(String categoryCode ){
		this.categoryCode = categoryCode;
	}
	/**
	 * 品牌
	 *@return
	 */
	public String getBrand(){
		return  brand;
	}
	/**
	 * 品牌
	 *@param  brand
	 */
	public void setBrand(String brand ){
		this.brand = brand;
	}
	/**
	 * 标题
	 *@return
	 */
	public String getTitle(){
		return  title;
	}
	/**
	 * 标题
	 *@param  title
	 */
	public void setTitle(String title ){
		this.title = title;
	}
	/**
	 * 首图链接
	 *@return
	 */
	public String getMainImageUrl(){
		return  mainImageUrl;
	}
	/**
	 * 首图链接
	 *@param  mainImageUrl
	 */
	public void setMainImageUrl(String mainImageUrl ){
		this.mainImageUrl = mainImageUrl;
	}
	/**
	 * 大类目编码
	 *@return
	 */
	public String getMainCategory(){
		return  mainCategory;
	}
	/**
	 * 大类目编码
	 *@param  mainCategory
	 */
	public void setMainCategory(String mainCategory ){
		this.mainCategory = mainCategory;
	}
	/**
	 * 小类目编码
	 *@return
	 */
	public String getSubCategoryCode(){
		return  subCategoryCode;
	}
	/**
	 * 小类目编码
	 *@param  subCategoryCode
	 */
	public void setSubCategoryCode(String subCategoryCode ){
		this.subCategoryCode = subCategoryCode;
	}
	/**
	 * unitCount
	 *@return
	 */
	public Integer getUnitCount(){
		return  unitCount;
	}
	/**
	 * unitCount
	 *@param  unitCount
	 */
	public void setUnitCount(Integer unitCount ){
		this.unitCount = unitCount;
	}
	/**
	 * 是否IPQ扩款 0否 1是
	 *@return
	 */
	public Integer getIpqFlag(){
		return  ipqFlag;
	}
	/**
	 * 是否IPQ扩款 0否 1是
	 *@param  ipqFlag
	 */
	public void setIpqFlag(Integer ipqFlag ){
		this.ipqFlag = ipqFlag;
	}
	/**
	 * PQ数量
	 *@return
	 */
	public Integer getIpqUnit(){
		return  ipqUnit;
	}
	/**
	 * PQ数量
	 *@param  ipqUnit
	 */
	public void setIpqUnit(Integer ipqUnit ){
		this.ipqUnit = ipqUnit;
	}
	/**
	 * 售价
	 *@return
	 */
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	 * 售价
	 *@param  price
	 */
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	 * 币种
	 *@return
	 */
	public String getCurrency(){
		return  currency;
	}
	/**
	 * 币种
	 *@param  currency
	 */
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	 * 目标价
	 *@return
	 */
	public BigDecimal getTargetPrice(){
		return  targetPrice;
	}
	/**
	 * 目标价
	 *@param  targetPrice
	 */
	public void setTargetPrice(BigDecimal targetPrice ){
		this.targetPrice = targetPrice;
	}
	/**
	 * 运费模板ID
	 *@return
	 */
	public String getShippingGroupId(){
		return  shippingGroupId;
	}
	/**
	 * 运费模板ID
	 *@param  shippingGroupId
	 */
	public void setShippingGroupId(String shippingGroupId ){
		this.shippingGroupId = shippingGroupId;
	}
	/**
	 * 产品尺寸
	 *@return
	 */
	public String getProductSize(){
		return  productSize;
	}
	/**
	 * 产品尺寸
	 *@param  productSize
	 */
	public void setProductSize(String productSize ){
		this.productSize = productSize;
	}
	/**
	 * 产品净重
	 *@return
	 */
	public BigDecimal getProductWeight(){
		return  productWeight;
	}
	/**
	 * 产品净重
	 *@param  productWeight
	 */
	public void setProductWeight(BigDecimal productWeight ){
		this.productWeight = productWeight;
	}
	/**
	 * 净重计量单位
	 *@return
	 */
	public String getProductWeightUnit(){
		return  productWeightUnit;
	}
	/**
	 * 净重计量单位
	 *@param  productWeightUnit
	 */
	public void setProductWeightUnit(String productWeightUnit ){
		this.productWeightUnit = productWeightUnit;
	}
	/**
	 * 不可售处理 字典表，枚举值
	 *@return
	 */
	public String getUnsaleableTreatment(){
		return  unsaleableTreatment;
	}
	/**
	 * 不可售处理 字典表，枚举值
	 *@param  unsaleableTreatment
	 */
	public void setUnsaleableTreatment(String unsaleableTreatment ){
		this.unsaleableTreatment = unsaleableTreatment;
	}
	/**
	 * 包装尺寸
	 *@return
	 */
	public String getPackagingSize(){
		return  packagingSize;
	}
	/**
	 * 包装尺寸
	 *@param  packagingSize
	 */
	public void setPackagingSize(String packagingSize ){
		this.packagingSize = packagingSize;
	}
	/**
	 * 产品毛重
	 *@return
	 */
	public BigDecimal getProductGrossWeight(){
		return  productGrossWeight;
	}
	/**
	 * 产品毛重
	 *@param  productGrossWeight
	 */
	public void setProductGrossWeight(BigDecimal productGrossWeight ){
		this.productGrossWeight = productGrossWeight;
	}
	/**
	 * 毛重计量单位
	 *@return
	 */
	public String getProductGrossWeightUnit(){
		return  productGrossWeightUnit;
	}
	/**
	 * 毛重计量单位
	 *@param  productGrossWeightUnit
	 */
	public void setProductGrossWeightUnit(String productGrossWeightUnit ){
		this.productGrossWeightUnit = productGrossWeightUnit;
	}
	/**
	 * 平台特殊计划
	 *@return
	 */
	public String getPlatformSpecialPlan(){
		return  platformSpecialPlan;
	}
	/**
	 * 平台特殊计划
	 *@param  platformSpecialPlan
	 */
	public void setPlatformSpecialPlan(String platformSpecialPlan ){
		this.platformSpecialPlan = platformSpecialPlan;
	}
	/**
	 * 预计到港时间
	 *@return
	 */
	public Date getEtaDate(){
		return  etaDate;
	}
	/**
	 * 预计到港时间
	 *@param  etaDate
	 */
	public void setEtaDate(Date etaDate ){
		this.etaDate = etaDate;
	}
	/**
	 * 需求单号
	 *@return
	 */
	public String getDemandNumber(){
		return  demandNumber;
	}
	/**
	 * 需求单号
	 *@param  demandNumber
	 */
	public void setDemandNumber(String demandNumber ){
		this.demandNumber = demandNumber;
	}
	/**
	 * 上货图链接
	 *@return
	 */
	public String getProductImageUrl(){
		return  productImageUrl;
	}
	/**
	 * 上货图链接
	 *@param  productImageUrl
	 */
	public void setProductImageUrl(String productImageUrl ){
		this.productImageUrl = productImageUrl;
	}
	/**
	 * A+类型 0无 1EBC 2.EMC
	 *@return
	 */
	public Integer getAplusType(){
		return  aplusType;
	}
	/**
	 * A+类型 0无 1EBC 2.EMC
	 *@param  aplusType
	 */
	public void setAplusType(Integer aplusType ){
		this.aplusType = aplusType;
	}
	/**
	 * 0否 1是
	 *@return
	 */
	public Integer getPostFlag(){
		return  postFlag;
	}
	/**
	 * 0否 1是
	 *@param  postFlag
	 */
	public void setPostFlag(Integer postFlag ){
		this.postFlag = postFlag;
	}
	public String getEanCode(){
		return  eanCode;
	}
	public void setEanCode(String eanCode ){
		this.eanCode = eanCode;
	}
	public String getAsin(){
		return  asin;
	}
	public void setAsin(String asin ){
		this.asin = asin;
	}
	public String getFnsku(){
		return  fnsku;
	}
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	public String getBatchId(){
		return  batchId;
	}
	public void setBatchId(String batchId ){
		this.batchId = batchId;
	}
	/**
	 * 10.新建 20.上货中 30.上货完成
	 *@return
	 */
	public Integer getStatus(){
		return  status;
	}
	/**
	 * 10.新建 20.上货中 30.上货完成
	 *@param  status
	 */
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	 * 备注
	 *@return
	 */
	public String getRemark(){
		return  remark;
	}
	/**
	 * 备注
	 *@param  remark
	 */
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	 *  同步产品销售视图状态 0表示未同步 1同步成功 99同步失败
	 *@return
	 */
	public Integer getSyncSales(){
		return  syncSales;
	}
	/**
	 *  同步产品销售视图状态 0表示未同步 1同步成功 99同步失败
	 *@param  syncSales
	 */
	public void setSyncSales(Integer syncSales ){
		this.syncSales = syncSales;
	}
	/**
	 * 同步产品销售视图的错误信息
	 *@return
	 */
	public String getSyncMsg(){
		return  syncMsg;
	}
	/**
	 * 同步产品销售视图的错误信息
	 *@param  syncMsg
	 */
	public void setSyncMsg(String syncMsg ){
		this.syncMsg = syncMsg;
	}
	/**
	 * 简单上货时间
	 *@return
	 */
	public Date getSimpleOnDate(){
		return  simpleOnDate;
	}
	/**
	 * 简单上货时间
	 *@param  simpleOnDate
	 */
	public void setSimpleOnDate(Date simpleOnDate ){
		this.simpleOnDate = simpleOnDate;
	}
	/**
	 * 简单上货操作人工号
	 *@return
	 */
	public String getSimpleOnNum(){
		return  simpleOnNum;
	}
	/**
	 * 简单上货操作人工号
	 *@param  simpleOnNum
	 */
	public void setSimpleOnNum(String simpleOnNum ){
		this.simpleOnNum = simpleOnNum;
	}
	/**
	 * 简单上货操作人姓名
	 *@return
	 */
	public String getSimpleOnName(){
		return  simpleOnName;
	}
	/**
	 * 简单上货操作人姓名
	 *@param  simpleOnName
	 */
	public void setSimpleOnName(String simpleOnName ){
		this.simpleOnName = simpleOnName;
	}
	/**
	 * 详细上货时间
	 *@return
	 */
	public Date getDetailedOnDate(){
		return  detailedOnDate;
	}
	/**
	 * 详细上货时间
	 *@param  detailedOnDate
	 */
	public void setDetailedOnDate(Date detailedOnDate ){
		this.detailedOnDate = detailedOnDate;
	}
	/**
	 * 详细上货操作人工号
	 *@return
	 */
	public String getDetailedOnNum(){
		return  detailedOnNum;
	}
	/**
	 * 详细上货操作人工号
	 *@param  detailedOnNum
	 */
	public void setDetailedOnNum(String detailedOnNum ){
		this.detailedOnNum = detailedOnNum;
	}
	/**
	 * 详细上货操作人姓名
	 *@return
	 */
	public String getDetailedOnName(){
		return  detailedOnName;
	}
	/**
	 * 详细上货操作人姓名
	 *@param  detailedOnName
	 */
	public void setDetailedOnName(String detailedOnName ){
		this.detailedOnName = detailedOnName;
	}
	/**
	 * 销售下的任务单是否不正确 0 错误 1 正常
	 *@return
	 */
	public Integer getErrorStatus(){
		return  errorStatus;
	}
	/**
	 * 销售下的任务单是否不正确 0 错误 1 正常
	 *@param  errorStatus
	 */
	public void setErrorStatus(Integer errorStatus ){
		this.errorStatus = errorStatus;
	}
	/**
	 * 错误内容
	 *@return
	 */
	public String getErrorMsg(){
		return  errorMsg;
	}
	/**
	 * 错误内容
	 *@param  errorMsg
	 */
	public void setErrorMsg(String errorMsg ){
		this.errorMsg = errorMsg;
	}
	/**
	 * 业务组
	 *@return
	 */
	public String getBusinessGroupCode(){
		return  businessGroupCode;
	}
	/**
	 * 业务组
	 *@param  businessGroupCode
	 */
	public void setBusinessGroupCode(String businessGroupCode ){
		this.businessGroupCode = businessGroupCode;
	}
	/**
	 * 创建人工号
	 *@return
	 */
	public String getCreateNum(){
		return  createNum;
	}
	/**
	 * 创建人工号
	 *@param  createNum
	 */
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	 * 创建人姓名
	 *@return
	 */
	public String getCreateName(){
		return  createName;
	}
	/**
	 * 创建人姓名
	 *@param  createName
	 */
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	 * 创建时间
	 *@return
	 */
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	 * 创建时间
	 *@param  createTime
	 */
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	 * 最后修改人工号
	 *@return
	 */
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	 * 最后修改人工号
	 *@param  lastModifyNum
	 */
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	 * 最后修改人姓名
	 *@return
	 */
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	 * 最后修改人姓名
	 *@param  lastModifyName
	 */
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	 * 最后修改时间
	 *@return
	 */
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	 * 最后修改时间
	 *@param  lastModifyTime
	 */
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

	public Integer getOnType() {
		return onType;
	}

	public void setOnType(Integer onType) {
		this.onType = onType;
	}
}
