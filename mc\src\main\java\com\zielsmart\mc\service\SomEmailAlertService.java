package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomEmailAlert;
import com.zielsmart.mc.repository.mapper.SomEmailAlertMapper;
import com.zielsmart.mc.vo.SomBatchDeleteVo;
import com.zielsmart.mc.vo.SomEmailAlertPageSearchVo;
import com.zielsmart.mc.vo.SomEmailAlertVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomEmailAlertService {
    
    @Resource
    private SomEmailAlertMapper somEmailAlertMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomEmailAlertVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomEmailAlertVo> queryByPage(SomEmailAlertPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomEmailAlertVo> pageResult = dynamicSqlManager.getMapper(SomEmailAlertMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomEmailAlertVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somEmailAlertVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomEmailAlertVo somEmailAlertVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somEmailAlertVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isEmpty(somEmailAlertVo.getEmailAlertRuleName())){
            throw new ValidateException("邮件预警规则名称不能为空！");
        }
        if (StrUtil.isEmpty(somEmailAlertVo.getMarket())){
            throw new ValidateException("市场不能为空！");
        }
        if (StrUtil.isEmpty(somEmailAlertVo.getAlertNotificationRuleName())){
            throw new ValidateException("预警通知规则名称不能为空！");
        }
        if (StrUtil.isEmpty(somEmailAlertVo.getEmailAddress())
                &&StrUtil.isEmpty(somEmailAlertVo.getEmailHeader())
                &&StrUtil.isEmpty(somEmailAlertVo.getIdentifyingPhrases())){
            throw new ValidateException("发件邮箱地址/邮件标题/识别词组均未填写，请填写任意一字段！");
        }
        long count = somEmailAlertMapper.createLambdaQuery().andEq("email_alert_rule_name", somEmailAlertVo.getEmailAlertRuleName())
                .andEq("market",somEmailAlertVo.getMarket())
                .andEq("email_address",somEmailAlertVo.getEmailAddress())
                .andEq("email_header",somEmailAlertVo.getEmailHeader())
                .andEq("identifying_phrases",somEmailAlertVo.getIdentifyingPhrases())
                .count();
        if (count > 0) {
            throw new ValidateException("当前“%s”规则已存在，请勿重复添加！",somEmailAlertVo.getEmailAlertRuleName());
        }
        somEmailAlertVo.setAid(IdUtil.fastSimpleUUID());
        somEmailAlertVo.setCreateName(tokenUser.getUserName());
        somEmailAlertVo.setCreateNum(tokenUser.getJobNumber());
        somEmailAlertVo.setCreateTime(DateTime.now().toJdkDate());
        somEmailAlertMapper.insert(ConvertUtils.beanConvert(somEmailAlertVo, SomEmailAlert.class));
    }

    /**
     * update
     * 修改
     * @param somEmailAlertVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomEmailAlertVo somEmailAlertVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somEmailAlertVo) || StrUtil.isEmpty(somEmailAlertVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somEmailAlertMapper.createLambdaQuery()
                .andEq("aid",somEmailAlertVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somEmailAlertVo, SomEmailAlert.class));
    }

    /**
     * delete
     * 删除
     * @param somBatchDeleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomBatchDeleteVo somBatchDeleteVo) throws ValidateException {
        if (CollectionUtil.isEmpty(somBatchDeleteVo.getAids())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somEmailAlertMapper.createLambdaQuery().andIn("aid", somBatchDeleteVo.getAids()).delete();
    }
}
