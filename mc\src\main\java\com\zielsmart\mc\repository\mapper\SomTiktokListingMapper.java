package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTiktokListingPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokListingReport;
import com.zielsmart.mc.vo.SomTiktokListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-12-12
*/

@SqlResource("somTiktokListing")
public interface SomTiktokListingMapper extends BaseMapper<SomTiktokListing> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTiktokListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTiktokListingVo> queryByPage(@Param("searchVo")SomTiktokListingPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomTiktokListingReport> stockReport(SomTiktokListingPageSearchVo searchVo, PageRequest pageRequest);
}
