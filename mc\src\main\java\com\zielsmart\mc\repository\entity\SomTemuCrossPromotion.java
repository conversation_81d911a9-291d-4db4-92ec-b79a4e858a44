package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Temu跨境（香港半托）营销活动明细
 * gen by 代码生成器 2025-03-12
 */

@Table(name = "mc.som_temu_cross_promotion")
public class SomTemuCrossPromotion implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;

    /**
     * 站点集合
     */
    @Column("sites")
    private String sites;

    /**
     * 店铺ID
     */
    @Column("account_id")
    private String accountId;
    /**
     * 产品ID
     */
    @Column("product_id")
    private String productId;
    /**
     * 商品ID
     */
    @Column("goods_id")
    private String goodsId;
    /**
     * 活动类型
     */
    @Column("activity_type")
    private Integer activityType;
    /**
     * 活动类型名称
     */
    @Column("activity_type_name")
    private String activityTypeName;
    /**
     * 活动起始时间
     */
    @Column("session_start_time")
    private Date sessionStartTime;
    /**
     * 活动截止时间
     */
    @Column("session_end_time")
    private Date sessionEndTime;
    /**
     * 是否服饰类的活动：1. 是 0.否。服饰类需要关心SKC维度，否则，其他类需要关心SKU维度
     */
    @Column("is_apparel")
    private Integer isApparel;
    /**
     * 是否售罄 0. 正常  1. 即将售罄 2. 已售罄
     */
    @Column("sold_status")
    private Integer soldStatus;
    /**
     * 活动剩余库存
     */
    @Column("remaining_activity_quantity")
    private Integer remainingActivityQuantity;
    /**
     * 活动主题ID
     */
    @Column("activity_thematic_id")
    private String activityThematicId;
    /**
     * 活动主题名称
     */
    @Column("activity_thematic_name")
    private String activityThematicName;
    /**
     * 报名记录ID
     */
    @Column("enroll_id")
    private String enrollId;
    /**
     * 此产品报名此活动的时间
     */
    @Column("enroll_time")
    private Date enrollTime;
    /**
     * 报名状态：1.报名中 2.报名失败 3.报名成功待分配场次 4.报名成功已分配场次 5.报名活动已结束 6.报名活动已下线
     */
    @Column("enroll_status")
    private Integer enrollStatus;
    /**
     * 提报的库存数量
     */
    @Column("activity_stock")
    private Integer activityStock;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 活动详情明细
     */
    @Column("skc_items")
    private Object skcItems;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomTemuCrossPromotion() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点集合
     *
     * @return
     */
    public String getSites() {
        return sites;
    }

    /**
     * 站点集合
     *
     * @param sites
     */
    public void setSites(String sites) {
        this.sites = sites;
    }

    /**
     * 店铺ID
     *
     * @return
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 店铺ID
     *
     * @param accountId
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 产品ID
     *
     * @return
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 产品ID
     *
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * 商品ID
     *
     * @return
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 商品ID
     *
     * @param goodsId
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * 活动类型
     *
     * @return
     */
    public Integer getActivityType() {
        return activityType;
    }

    /**
     * 活动类型
     *
     * @param activityType
     */
    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    /**
     * 活动类型名称
     *
     * @return
     */
    public String getActivityTypeName() {
        return activityTypeName;
    }

    /**
     * 活动类型名称
     *
     * @param activityTypeName
     */
    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }

    /**
     * 活动起始时间
     *
     * @return
     */
    public Date getSessionStartTime() {
        return sessionStartTime;
    }

    /**
     * 活动起始时间
     *
     * @param sessionStartTime
     */
    public void setSessionStartTime(Date sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }

    /**
     * 活动截止时间
     *
     * @return
     */
    public Date getSessionEndTime() {
        return sessionEndTime;
    }

    /**
     * 活动截止时间
     *
     * @param sessionEndTime
     */
    public void setSessionEndTime(Date sessionEndTime) {
        this.sessionEndTime = sessionEndTime;
    }

    /**
     * 是否服饰类的活动：1. 是 0.否。服饰类需要关心SKC维度，否则，其他类需要关心SKU维度
     *
     * @return
     */
    public Integer getisApparel() {
        return isApparel;
    }

    /**
     * 是否服饰类的活动：1. 是 0.否。服饰类需要关心SKC维度，否则，其他类需要关心SKU维度
     *
     * @param isApparel
     */
    public void setisApparel(Integer isApparel) {
        this.isApparel = isApparel;
    }

    /**
     * 是否售罄 0. 正常  1. 即将售罄 2. 已售罄
     *
     * @return
     */
    public Integer getSoldStatus() {
        return soldStatus;
    }

    /**
     * 是否售罄 0. 正常  1. 即将售罄 2. 已售罄
     *
     * @param soldStatus
     */
    public void setSoldStatus(Integer soldStatus) {
        this.soldStatus = soldStatus;
    }

    /**
     * 活动剩余库存
     *
     * @return
     */
    public Integer getRemainingActivityQuantity() {
        return remainingActivityQuantity;
    }

    /**
     * 活动剩余库存
     *
     * @param remainingActivityQuantity
     */
    public void setRemainingActivityQuantity(Integer remainingActivityQuantity) {
        this.remainingActivityQuantity = remainingActivityQuantity;
    }

    /**
     * 活动主题ID
     *
     * @return
     */
    public String getActivityThematicId() {
        return activityThematicId;
    }

    /**
     * 活动主题ID
     *
     * @param activityThematicId
     */
    public void setActivityThematicId(String activityThematicId) {
        this.activityThematicId = activityThematicId;
    }

    /**
     * 活动主题名称
     *
     * @return
     */
    public String getActivityThematicName() {
        return activityThematicName;
    }

    /**
     * 活动主题名称
     *
     * @param activityThematicName
     */
    public void setActivityThematicName(String activityThematicName) {
        this.activityThematicName = activityThematicName;
    }

    /**
     * 报名记录ID
     *
     * @return
     */
    public String getEnrollId() {
        return enrollId;
    }

    /**
     * 报名记录ID
     *
     * @param enrollId
     */
    public void setEnrollId(String enrollId) {
        this.enrollId = enrollId;
    }

    /**
     * 此产品报名此活动的时间
     *
     * @return
     */
    public Date getEnrollTime() {
        return enrollTime;
    }

    /**
     * 此产品报名此活动的时间
     *
     * @param enrollTime
     */
    public void setEnrollTime(Date enrollTime) {
        this.enrollTime = enrollTime;
    }

    /**
     * 报名状态：1.报名中 2.报名失败 3.报名成功待分配场次 4.报名成功已分配场次 5.报名活动已结束 6.报名活动已下线
     *
     * @return
     */
    public Integer getEnrollStatus() {
        return enrollStatus;
    }

    /**
     * 报名状态：1.报名中 2.报名失败 3.报名成功待分配场次 4.报名成功已分配场次 5.报名活动已结束 6.报名活动已下线
     *
     * @param enrollStatus
     */
    public void setEnrollStatus(Integer enrollStatus) {
        this.enrollStatus = enrollStatus;
    }

    /**
     * 提报的库存数量
     *
     * @return
     */
    public Integer getActivityStock() {
        return activityStock;
    }

    /**
     * 提报的库存数量
     *
     * @param activityStock
     */
    public void setActivityStock(Integer activityStock) {
        this.activityStock = activityStock;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * （服饰类）活动详情明细
     *
     * @return
     */
    public Object getSkcItems() {
        return skcItems;
    }

    /**
     * （服饰类）活动详情明细
     *
     * @param skcItems
     */
    public void setSkcItems(Object skcItems) {
        this.skcItems = skcItems;
    }
    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
