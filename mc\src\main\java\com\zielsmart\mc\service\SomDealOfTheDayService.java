package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.zielsmart.mc.event.AmazonActivityReminderEvent;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IBiService;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.util.MarketActivityUtil;
import com.zielsmart.mc.util.MessageType;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.zbpm.ZBPMDealDetailVo;
import com.zielsmart.mc.vo.zbpm.ZBPMDealVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.listener.EventBusTemplate;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
public class SomDealOfTheDayService {

    @Resource
    private SomDealOfTheDayMapper somDealOfTheDayMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private AmazonService amazonService;
    @Resource
    private EventBusTemplate eventBusTemplate;
    @Resource
    private SomDealRuleConfigMapper configMapper;
    @Resource
    private SomDealService dealService;
    @Resource
    private McSellerskuMappingMapper skuMappingMapper;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;
    @Value("${remote.oa.remote.url}")
    private String oaUrl;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    PlatformPropertiesService platformPropertiesService;

    @Resource
    private IBiService biService;
    @Value("${bi.magic.head.token}")
    private String biToken;

    /**
     * addOrEdit
     * 新增或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void addOrEdit(SomDealOfTheDayVo addOrEditVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addOrEditVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.equalsIgnoreCase("Amazon.co.jp", addOrEditVo.getSite()) && ObjectUtil.isEmpty(addOrEditVo.getCommentsNumber())) {
            throw new ValidateException("Amazon.co.jp站点的评价数不能为空");
        }
        if (StrUtil.isBlank(addOrEditVo.getAcceptAdjustment()) || (!addOrEditVo.getAcceptAdjustment().equalsIgnoreCase("no") && !addOrEditVo.getAcceptAdjustment().equalsIgnoreCase("yes"))) {
            throw new ValidateException("是否接受调整开跑日期不能为空 并且只能是 Yes 或者 No");
        }
        // 数值过大 强制转换
        if (addOrEditVo.getExpectedGrossProfitMargin() != null
                && addOrEditVo.getExpectedGrossProfitMargin().toPlainString().replace("-", "").replace(".", "").length() > 8) {
            addOrEditVo.setExpectedGrossProfitMargin(BigDecimal.valueOf(-9999));
        }
        //查询数据库发现品牌数据是展示码，输出日志排查问题
        log.info("DOTD新增或编辑数据 品牌名称：{}", addOrEditVo.getBrandName());

        if (!(StrUtil.equals("Amazon.nl", addOrEditVo.getSite()) && !StrUtil.equals("Amazon.se", addOrEditVo.getSite())) && !StrUtil.equals("Amazon.pl", addOrEditVo.getSite())
                && !StrUtil.equals("Amazon.tr", addOrEditVo.getSite()) && !StrUtil.equals("Amazon.com.be", addOrEditVo.getSite())) {
//            if (ObjectUtil.isEmpty(addOrEditVo.getSalesMonthAgo()) || Integer.valueOf(0).compareTo(addOrEditVo.getSalesMonthAgo()) == 1) {
//                throw new ValidateException("T28销量不能为空,且必须是整数，必须≥0");
//            }
//            if (ObjectUtil.isEmpty(addOrEditVo.getSalesFromMonthAgo()) || BigDecimal.valueOf(0).compareTo(addOrEditVo.getSalesFromMonthAgo()) == 1) {
//                throw new ValidateException("T28销售额不能为空,且必须≥0");
//            }
//            if (ObjectUtil.isEmpty(addOrEditVo.getEstimatedDealSales()) || Integer.valueOf(0).compareTo(addOrEditVo.getEstimatedDealSales()) == 1) {
//                throw new ValidateException("预计T28销量不能为空,且必须是整数，必须≥0");
//            }
        }
        // 唯一性校验
        List<Integer> countList = somDealOfTheDayMapper.checkUnique(addOrEditVo);
        int sum = countList.stream().mapToInt(m -> m).sum();
        if (0 < sum) {
            throw new ValidateException("当前数据存在其他类型的Deal，请检查数据");
        }
        String aid = Strings.EMPTY;
        if (StrUtil.isNotBlank(addOrEditVo.getAid())) {
            aid = addOrEditVo.getAid();
        }
        SomDealRuleConfigVo config = configMapper.queryBySiteDealType(addOrEditVo.getSite(), "DOTD");
        if (ObjectUtil.isEmpty(config) || StrUtil.isEmpty(config.getRules())) {
            throw new ValidateException("当前站点下该Deal类型未配置秒杀规则,请前往秒杀规则配置功能进行配置");
        }
        dealService.checkDeal(aid, addOrEditVo.getSite(), Collections.singletonList(addOrEditVo.getSellerSku()), Collections.singletonList(addOrEditVo.getChildAsin()), addOrEditVo.getPlanStartDate(), config);

        //计算库存可售天数
        MarketActivityUtil.MarketMsgData daysMsgData = calcDays(addOrEditVo);
        if (!daysMsgData.getErrorMap().isEmpty()) {
            String errorMsg = daysMsgData.getErrorMap().values().stream().collect(Collectors.joining(","));
            throw new ValidateException(errorMsg);
        } else {
            addOrEditVo.setStockSaleDays((Integer)daysMsgData.getDaysMap().get(addOrEditVo.getSellerSku()));
            addOrEditVo.setCompletionRate((BigDecimal)daysMsgData.getDaysMap().get(addOrEditVo.getSellerSku()+"_completionRate"));
        }

        // 确定审批角色
        addOrEditVo.setApprovalRole(initApprovalRole(addOrEditVo));

        if (StrUtil.isBlank(addOrEditVo.getAid())) {
            addOrEditVo.setAid(IdUtil.fastSimpleUUID());
            addOrEditVo.setPlatform("Amazon");
            addOrEditVo.setStatus(10);
            addOrEditVo.setCreateNum(tokenUser.getJobNumber());
            addOrEditVo.setCreateName(tokenUser.getUserName());
            addOrEditVo.setCreateTime(DateTime.now().toJdkDate());
            somDealOfTheDayMapper.insert(ConvertUtils.beanConvert(addOrEditVo, SomDealOfTheDay.class));
        } else {
            SomDealOfTheDay obj = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", addOrEditVo.getAid()).single();
            // pdd 草稿 || 需要关注状态可以编辑
            if (ObjectUtil.isEmpty(obj) || !(ObjectUtil.equal(10, obj.getStatus()) || ObjectUtil.equal(41, obj.getStatus()))) {
                throw new ValidateException("当前数据不允许编辑");
            }
            SomDealOfTheDay deal = ConvertUtils.beanConvert(addOrEditVo, SomDealOfTheDay.class);
            if (ObjectUtil.equal(41, obj.getStatus())) {
                deal.setModifyStatus(10);
            } else {
                deal.setModifyStatus(obj.getModifyStatus());
            }
            deal.setPlatform(obj.getPlatform());
            deal.setStatus(obj.getStatus());
            deal.setCreateNum(obj.getCreateNum());
            deal.setCreateName(obj.getCreateName());
            deal.setCreateTime(obj.getCreateTime());
            deal.setModifyNum(tokenUser.getJobNumber());
            deal.setModifyName(tokenUser.getUserName());
            deal.setModifyTime(DateTime.now().toJdkDate());
            // 原有字段保留
            deal.setRealEndDate(obj.getRealEndDate());
            deal.setRealStartDate(obj.getRealStartDate());
            deal.setModifyRemark(obj.getModifyRemark());
            deal.setModifyFailureRemark(obj.getModifyFailureRemark());
            deal.setCancelRemark(obj.getCancelRemark());
            deal.setCacenlFailureRemark(obj.getCacenlFailureRemark());
            deal.setAuditFailureRemark(obj.getAuditFailureRemark());
            deal.setSubmitTime(obj.getSubmitTime());
            deal.setAuditTime(obj.getAuditTime());
            deal.setErrorMsg(obj.getErrorMsg());
            somDealOfTheDayMapper.updateById(deal);
        }
    }

    /**
     * 确定审批角色
     * @param vo
     * @return String
     */
    private String initApprovalRole(SomDealOfTheDayVo vo) {
        if (vo == null || vo.getSite() == null || vo.getDealPriceGross() == null || vo.getCategoryGross() == null) {
            return "无需审批";
            //throw new IllegalArgumentException("确定审批角色：所需参数不能为null");
        }

        String approvalRole = null;
        String site = vo.getSite();
        // 秒杀价毛利率
        BigDecimal dealPriceGross = vo.getDealPriceGross().setScale(2, RoundingMode.HALF_UP);
        // 三级分类近四周毛利率
        BigDecimal categoryGross = vo.getCategoryGross().setScale(2, RoundingMode.HALF_UP);

        if (Arrays.asList("Amazon.com").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-10)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-20)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.de", "Amazon.fr", "Amazon.co.uk", "Amazon.it", "Amazon.es", "Amazon.pl", "Amazon.ie", "Amazon.nl", "Amazon.se", "Amazon.com.be", "Amazon.tr").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-15)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-30)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.ca", "Amazon.com.mx", "Amazon.com.mx.new").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-15)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-40)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-10)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.co.jp", "Amazon.com.au").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-5)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-30)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-20)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }

        if (approvalRole == null) {
            approvalRole = "无需审批";
        }

        return approvalRole;
    }

    /**
     * 确定审批角色 多列 多组
     * @param list
     * @return String
     */
    private String initApprovalRoleGroup(List<SomDealOfTheDay> list) {
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("区域负责人层级", 1);
        priorityMap.put("CS层级", 2);
        priorityMap.put("组长层级", 3);

        String highestPriorityRole = "无需审批";
        int highestPriority = Integer.MAX_VALUE;

        for (SomDealOfTheDay item : list) {
            String approvalRole = item.getApprovalRole();
            if (approvalRole != null && priorityMap.containsKey(approvalRole)) {
                int priority = priorityMap.get(approvalRole);
                if (priority < highestPriority) {
                    highestPriority = priority;
                    highestPriorityRole = approvalRole;
                }
            }
        }

        return highestPriorityRole;
    }

    private MarketActivityUtil.MarketMsgData calcDays(SomDealOfTheDayVo vo){
        List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
        activity.setSite(vo.getSite());
        activity.setSellerSku(vo.getSellerSku());
        activity.setStartDate(sdf.format(vo.getPlanStartDate()));
        activity.setEndDate(sdf.format(vo.getPlanEndDate()));
        activity.setType("DOTD");
        body.add(activity);

        MarketActivityUtil activityUtil = new MarketActivityUtil();
        MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, false);
        return daysMsgData;
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomDealOfTheDayVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomDealOfTheDayExtVo> queryByPage(SomDealOfTheDayPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomDealOfTheDayExtVo> pageResult = dynamicSqlManager.getMapper(SomDealOfTheDayMapper.class).queryByPage(searchVo, pageRequest);
        List<SomDealOfTheDayExtVo> somDealOfTheDayExtVos = pageResult.getList();
        if(CollUtil.isNotEmpty(somDealOfTheDayExtVos)) {
            // 查询字典
            List<String> list = Arrays.asList("PDDStatus", "CampaignType", "PDDModifyStatus");
            List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();
            Map<String, Map<String, String>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                    .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode,
                            Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (v1, v2) -> v1)));
            for (SomDealOfTheDayExtVo somDealOfTheDayExtVo : somDealOfTheDayExtVos) {
                // 状态
                Map<String, String> statusMap = mcDictionaryInfoMap.get("PDDStatus");
                somDealOfTheDayExtVo.setStatusName(CollUtil.isEmpty(statusMap) ? null : statusMap.get(String.valueOf(somDealOfTheDayExtVo.getStatus())));
                // 大促类型
                Map<String, String> campaignTypeMap = mcDictionaryInfoMap.get("CampaignType");
                somDealOfTheDayExtVo.setCampaignTypeName(CollUtil.isEmpty(campaignTypeMap) ? null : campaignTypeMap.get(String.valueOf(somDealOfTheDayExtVo.getCampaignType())));
                // 修改状态
                Map<String, String> modifyStatusMap = mcDictionaryInfoMap.get("PDDModifyStatus");
                somDealOfTheDayExtVo.setModifyStatusName(CollUtil.isEmpty(modifyStatusMap) ? null : modifyStatusMap.get(String.valueOf(somDealOfTheDayExtVo.getModifyStatus())));
                // 填充错误信息，如果状态非需要关注，需要把字段值手动置为 null
                Integer needAttentionStatus = 41;
                somDealOfTheDayExtVo.setErrorMsg(!needAttentionStatus.equals(somDealOfTheDayExtVo.getStatus()) ? null : somDealOfTheDayExtVo.getErrorMsg());
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomDealOfTheDayExtVo.class, searchVo);
    }

    /**
     * queryByAid
     * 查看
     *
     * @param searchVo
     * @return {@link java.lang.Object}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomDealOfTheDayVo queryByAid(SomDealOfTheDayVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomDealOfTheDay deal = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", searchVo.getAid()).single();
        SomDealOfTheDayExtVo pddAndOdExtVo = ConvertUtils.beanConvert(deal, SomDealOfTheDayExtVo.class);
        if (ObjectUtil.isNotEmpty(pddAndOdExtVo)) {
            List<McDictionaryInfo> campaignTypeList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "CampaignType").select();
            List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "PDDApplyReason").select();
            if (ObjectUtil.isNotEmpty(pddAndOdExtVo.getApplyReason())) {
                dictionaryInfoList.stream().filter(f -> StrUtil.equals("PDDApplyReason", f.getItemTypeCode()) && StrUtil.equals(f.getItemValue(), String.valueOf(pddAndOdExtVo.getApplyReason()))).findFirst().ifPresent(ps -> {
                    pddAndOdExtVo.setApplyReasonName(ps.getItemLable());
                });
            }
            if (ObjectUtil.isNotEmpty(pddAndOdExtVo.getCampaignType())) {
                campaignTypeList.stream().filter(t -> StrUtil.equals(pddAndOdExtVo.getCampaignType().toString(), t.getItemValue())).findFirst().ifPresent(ps -> {
                    pddAndOdExtVo.setCampaignTypeName(ps.getItemLable());
                });
            }

        }
        nullToZero(pddAndOdExtVo);
        return pddAndOdExtVo;
    }

    /**
     * 兼容历史数据
     * null转0
     */
    private void nullToZero(SomDealOfTheDayVo vo) {
        vo.setDealPriceGross(Optional.ofNullable(vo.getDealPriceGross()).orElse(BigDecimal.ZERO));
        vo.setDealBurstCoefficient(Optional.ofNullable(vo.getDealBurstCoefficient()).orElse(BigDecimal.ZERO));
        vo.setDmsLast30day(Optional.ofNullable(vo.getDmsLast30day()).orElse(BigDecimal.ZERO));
        vo.setCategoryGross(Optional.ofNullable(vo.getCategoryGross()).orElse(BigDecimal.ZERO));
        vo.setExpectedSalesVolume(Optional.ofNullable(vo.getExpectedSalesVolume()).orElse(BigDecimal.ZERO));
        vo.setEstimateOfSales(Optional.ofNullable(vo.getEstimateOfSales()).orElse(BigDecimal.ZERO));
        vo.setExpectedGrossProfitMargin(Optional.ofNullable(vo.getExpectedGrossProfitMargin()).orElse(BigDecimal.ZERO));
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomDealOfTheDayVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || CollUtil.isEmpty(deleteVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somDealOfTheDayMapper.createLambdaQuery().andIn("aid", deleteVo.getAidList()).delete();
    }

    /**
     * submit
     * 提交
     *
     * @param submitVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void submit(SomDealOfTheDayVo submitVo, TokenUserInfo tokenUserInfo) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(submitVo) || StrUtil.isBlank(submitVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomDealOfTheDay deal = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", submitVo.getAid()).andEq("status", 10).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许提交");
        }
        // 是否发起审批
        if (StrUtil.isNotEmpty(deal.getApprovalRole()) && !deal.getApprovalRole().equals("无需审批")) {
            // 获取流程
            ZBPMDealVo zbpmSubmitVo = new ZBPMDealVo();
            zbpmSubmitVo.setDealTypeName("DOTD"); // 兼容旧版本
            zbpmSubmitVo.setDealType("DOTD"); // 活动类型
            // 提报国家
            McPlatformPropertiesVo mcPlatformPropertiesVo = new McPlatformPropertiesVo();
            mcPlatformPropertiesVo.setPlatform(deal.getPlatform());
            mcPlatformPropertiesVo.setSite(deal.getSite());
            McPlatformPropertiesVo mcPlatformPropertiesVoRes = platformPropertiesService.getPlatformProperties(mcPlatformPropertiesVo);
            zbpmSubmitVo.setCountry(mcPlatformPropertiesVoRes.getCountryName());
            // 确定抬头审批角色
            zbpmSubmitVo.setApprovalRole(deal.getApprovalRole());
            zbpmSubmitVo.setAidList(Collections.singletonList(submitVo.getAid()));
            ZBPMDealDetailVo dealVo = ConvertUtils.beanConvert(deal, ZBPMDealDetailVo.class);
            if (ObjectUtil.isNotEmpty(dealVo)) {
                // 申请原因转义
                List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "PDDApplyReason").select();
                if (ObjectUtil.isNotEmpty(dictionaryInfoList)) {
                    dictionaryInfoList.stream().filter(f -> StrUtil.equals("PDDApplyReason", f.getItemTypeCode()) && StrUtil.equals(deal.getApplyReason().toString(), f.getItemValue())).findFirst().ifPresent(ps -> {
                        dealVo.setApplyReasonName(ps.getItemLable());
                    });
                    if (ObjectUtil.equal(99, deal.getApplyReason())) {
                        dealVo.setApplyReasonName(dealVo.getApplyReasonName() + ";" + deal.getCutomerReason());
                    }
                }
                // 货币符号
                McListingInfoAmazon amazoninfo = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andEq("site", deal.getSite()).andEq("seller_sku", deal.getSellerSku()).single();
                if (ObjectUtil.isNotEmpty(amazoninfo) && StrUtil.isNotBlank(amazoninfo.getCurrencyCode())) {
                    dealVo.setCurrencySymbol(amazoninfo.getCurrencyCode());
                }
                // 业务组 业务组负责人 业务助理
                List<McProductSalesExtVo> productSales = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryProductSales(Collections.singletonList(deal.getSite()), Collections.singletonList(deal.getSellerSku()));
                if (CollectionUtil.isNotEmpty(productSales)) {
                    productSales.stream().filter(f -> StrUtil.equals(f.getSite(), dealVo.getSite()) && StrUtil.equals(f.getDisplayProductCode(), dealVo.getSellerSku())).findFirst().ifPresent(ps -> {
                        dealVo.setBusinessGroupCode(ps.getBusinessGroupCode());
                        dealVo.setSalesGroupName(ps.getSalesGroupName());
                        dealVo.setBusinessLeaderCode(ps.getBusinessLeaderCode());
                        dealVo.setSalesGroupEmptName(ps.getSalesGroupEmptName());
                        dealVo.setBusinessOperationCode(ps.getBusinessOperationCode());
                        dealVo.setOperationEmptName(ps.getOperationEmptName());
                    });
                }
                // 查询毛利率
                SomKpiGrossProfit profit = dynamicSqlManager.getMapper(SomKpiGrossProfitMapper.class).createLambdaQuery().andEq("platform", deal.getPlatform())
                        .andEq("site", deal.getSite()).andEq("seller_sku", deal.getSellerSku()).single();
                if (ObjectUtil.isNotEmpty(profit) && ObjectUtil.isNotEmpty(profit.getOneMonthGrossProfitRate())) {
                    dealVo.setOneMonthGrossProfitRate(profit.getOneMonthGrossProfitRate());
                }
                zbpmSubmitVo.setDealDetailList(Collections.singletonList(dealVo));
                // 发起OA流程
                submitProcess(zbpmSubmitVo, tokenUserInfo);
                deal.setStatus(20);
                deal.setSubmitTime(DateTime.now().toJdkDate());
                somDealOfTheDayMapper.updateById(deal);
            } else {
                throw new ValidateException("Deal活动明细信息为空");
            }
        } else {
            deal.setStatus(21);
            deal.setSubmitTime(DateTime.now().toJdkDate());
            somDealOfTheDayMapper.updateById(deal);
        }
    }

    /**
     * feedbackResult
     * 反馈提报结果
     *
     * @param feedbackVo
     * <AUTHOR>
     * @history
     */
    public void feedbackResult(SomDealOfTheDayVo feedbackVo, TokenUserInfo tokenUserInfo) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomDealOfTheDay obj = somDealOfTheDayMapper.single(feedbackVo.getAid());
        if (ObjectUtil.isEmpty(obj) || !(21 == obj.getStatus() || 110 == obj.getStatus())) {
            throw new ValidateException("当前状态不允许反馈提报结果");
        }

        if (ObjectUtil.equal(30, feedbackVo.getStatus())) {
            if ((ObjectUtil.isEmpty(feedbackVo.getRealStartDate()) || ObjectUtil.isEmpty(feedbackVo.getRealEndDate()))) {
                throw new ValidateException("提报成功时,活动实际起止时间不允许为空");
            } else {
                obj.setStatus(40);
                obj.setRealStartDate(feedbackVo.getRealStartDate());
                obj.setRealEndDate(feedbackVo.getRealEndDate());
            }
        } else if (ObjectUtil.equal(39, feedbackVo.getStatus())) {
            if (StrUtil.isBlank(feedbackVo.getSubmissionFailureRemark())) {
                throw new ValidateException("提报失败时,失败原因不允许为空");
            } else {
                obj.setStatus(feedbackVo.getStatus());
                obj.setSubmissionFailureRemark(feedbackVo.getSubmissionFailureRemark());
            }
        }
        //反馈实际活动起止时间时，提报活动起止时间同步调整
        obj.setRealStartDate(feedbackVo.getRealStartDate());
        obj.setRealEndDate(feedbackVo.getRealEndDate());
        obj.setPlanStartDate(feedbackVo.getRealStartDate());
        obj.setPlanEndDate(feedbackVo.getRealEndDate());

        obj.setModifyNum(tokenUserInfo.getJobNumber());
        obj.setModifyName(tokenUserInfo.getUserName());
        obj.setModifyTime(DateTime.now().toJdkDate());
        somDealOfTheDayMapper.updateById(obj);
        if (ObjectUtil.equal(39, feedbackVo.getStatus())) {
            try {
                AmazonActivityReminderEvent.Reminder reminder = new AmazonActivityReminderEvent.Reminder(obj.getSite(), obj.getSellerSku(), Strings.EMPTY,
                        Strings.EMPTY, obj.getPlanStartDate(), obj.getPlanEndDate(), "DOTD", feedbackVo.getSubmissionFailureRemark(), obj.getCreateNum());
                AmazonActivityReminderEvent event = new AmazonActivityReminderEvent(Arrays.asList(reminder));
                eventBusTemplate.publish(event);
            } catch (Exception e) {
                throw new ValidateException("放入消息队列出错" + e.getMessage());
            }
        }
    }

    /**
     * modifyPlanTime
     * 修改活动时间
     *
     * @param modifyVo
     * <AUTHOR>
     * @history
     */
    public void modifyPlanTime(SomDealOfTheDayVo modifyVo, TokenUserInfo tokenUserInfo) throws ValidateException {
        if (ObjectUtil.isEmpty(modifyVo) || StrUtil.isBlank(modifyVo.getAid()) || ObjectUtil.isEmpty(modifyVo.getPlanStartDate()) || ObjectUtil.isEmpty(modifyVo.getPlanEndDate())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        /**
         * 大促类型
         * 1 = Prime Day Window
         * 2 = Prime Fall Deal Event
         * 3 = BFCM Window
         * 4 = Black Friday Window
         * 5 = Cyber Monday Window
         */
        SomDealOfTheDay obj = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", modifyVo.getAid()).andEq("campaign_type", 1).single();
        if (ObjectUtil.isEmpty(obj)) {
            throw new ValidateException("当前数据不允许修改活动时间");
        }
        obj.setPlanStartDate(modifyVo.getPlanStartDate());
        obj.setPlanEndDate(modifyVo.getPlanEndDate());
        obj.setModifyNum(tokenUserInfo.getJobNumber());
        obj.setModifyName(tokenUserInfo.getUserName());
        obj.setModifyTime(DateTime.now().toJdkDate());
        somDealOfTheDayMapper.updateById(obj);
    }

    /**
     * batchSubmit
     * 批量提交
     *
     * @param batchSubmitVo
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSubmit(SomDealOfTheDayExtVo batchSubmitVo, TokenUserInfo tokenUserInfo) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(batchSubmitVo) || CollectionUtil.isEmpty(batchSubmitVo.getAidList())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<SomDealOfTheDay> dealList = somDealOfTheDayMapper.createLambdaQuery().andIn("aid", batchSubmitVo.getAidList()).select();
        if (CollectionUtil.isNotEmpty(dealList)) {
            long num = dealList.stream().filter(f -> !ObjectUtil.equal(10, f.getStatus())).count();
            if (0 < num) {
                throw new ValidateException("只允许批量提报状态为'草稿'的活动");
            }
            for (SomDealOfTheDay deal : dealList) {
                deal.setSubmitTime(DateTime.now().toJdkDate());
                // 是否发起审批
                if (StrUtil.isNotEmpty(deal.getApprovalRole()) && !deal.getApprovalRole().equals("无需审批")) {
                    deal.setStatus(20);
                } else {
                    deal.setStatus(21);
                }
            }
            somDealOfTheDayMapper.batchSubmit(dealList.stream().filter(f -> ObjectUtil.notEqual(20, f.getStatus())).collect(Collectors.toList()));
            if (dealList.stream().filter(f -> ObjectUtil.equal(20, f.getStatus())).count() > 0) {
                dealList = dealList.stream().filter(f -> ObjectUtil.equal(20, f.getStatus())).collect(Collectors.toList());
                // 获取流程
                ZBPMDealVo zbpmSubmitVo = new ZBPMDealVo();
                zbpmSubmitVo.setDealTypeName("DOTD"); // 兼容旧版本
                zbpmSubmitVo.setDealType("DOTD"); // 活动类型
                // 提报国家 逗号分割
                StringJoiner country = new StringJoiner(",");
                Set<String> uniqueSites = new HashSet<>();
                for (SomDealOfTheDay deal : dealList) {
                    String site = deal.getSite();
                    if (!uniqueSites.contains(site)) {
                        uniqueSites.add(site);
                        McPlatformPropertiesVo mcPlatformPropertiesVo = new McPlatformPropertiesVo();
                        mcPlatformPropertiesVo.setPlatform(deal.getPlatform());
                        mcPlatformPropertiesVo.setSite(deal.getSite());
                        McPlatformPropertiesVo mcPlatformPropertiesVoRes = platformPropertiesService.getPlatformProperties(mcPlatformPropertiesVo);
                        country.add(mcPlatformPropertiesVoRes.getCountryName());
                    }
                }
                zbpmSubmitVo.setCountry(country.toString());
                // 确定审批角色
                zbpmSubmitVo.setApprovalRole(initApprovalRoleGroup(dealList));

                List<String> aidList = dealList.stream().map(m -> m.getAid()).collect(Collectors.toList());
                zbpmSubmitVo.setAidList(aidList);
                List<ZBPMDealDetailVo> dealDetailList = new ArrayList<>();
                List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "PDDApplyReason").select();
                // 查询毛利率
                List<String> platforms = dealList.stream().map(m -> m.getPlatform()).distinct().collect(Collectors.toList());
                List<String> sites = dealList.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
                List<String> sellerSkus = dealList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
                List<SomKpiGrossProfit> profitList = dynamicSqlManager.getMapper(SomKpiGrossProfitMapper.class).createLambdaQuery().andIn("platform", platforms)
                        .andIn("site", sites).andIn("seller_sku", sellerSkus).select();
                for (SomDealOfTheDay dealVo : dealList) {
                    ZBPMDealDetailVo detailVo = ConvertUtils.beanConvert(dealVo, ZBPMDealDetailVo.class);
                    dictionaryInfoList.stream().filter(f -> StrUtil.equals("PDDApplyReason", f.getItemTypeCode()) && ObjectUtil.isNotEmpty(dealVo.getApplyReason()) && StrUtil.equals(dealVo.getApplyReason().toString(), f.getItemValue())).findFirst().ifPresent(ps -> {
                        detailVo.setApplyReasonName(ps.getItemLable());
                    });
                    if (ObjectUtil.equal(99, dealVo.getApplyReason())) {
                        detailVo.setApplyReasonName(detailVo.getApplyReasonName() + ";" + dealVo.getCutomerReason());
                    }
                    // 货币符号
                    McListingInfoAmazon amazoninfo = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andEq("site", dealVo.getSite()).andEq("seller_sku", dealVo.getSellerSku()).single();
                    if (ObjectUtil.isNotEmpty(amazoninfo) && StrUtil.isNotBlank(amazoninfo.getCurrencyCode())) {
                        detailVo.setCurrencySymbol(amazoninfo.getCurrencyCode());
                    }
                    // 业务组 业务组负责人 业务助理
                    List<McProductSalesExtVo> productSales = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryProductSales(Collections.singletonList(dealVo.getSite()), Collections.singletonList(dealVo.getSellerSku()));
                    if (CollectionUtil.isNotEmpty(productSales)) {
                        productSales.stream().filter(f -> StrUtil.equals(f.getSite(), dealVo.getSite()) && StrUtil.equals(f.getDisplayProductCode(), dealVo.getSellerSku())).findFirst().ifPresent(ps -> {
                            detailVo.setBusinessGroupCode(ps.getBusinessGroupCode());
                            detailVo.setSalesGroupName(ps.getSalesGroupName());
                            detailVo.setBusinessLeaderCode(ps.getBusinessLeaderCode());
                            detailVo.setSalesGroupEmptName(ps.getSalesGroupEmptName());
                            detailVo.setBusinessOperationCode(ps.getBusinessOperationCode());
                            detailVo.setOperationEmptName(ps.getOperationEmptName());
                        });
                    }
                    SomKpiGrossProfit profit = profitList.stream().filter(f -> StrUtil.equalsIgnoreCase(dealVo.getPlatform(), f.getPlatform()) && StrUtil.equalsIgnoreCase(dealVo.getSite(), f.getSite())
                            && StrUtil.equalsIgnoreCase(dealVo.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(profit) && ObjectUtil.isNotEmpty(profit.getOneMonthGrossProfitRate())) {
                        detailVo.setOneMonthGrossProfitRate(profit.getOneMonthGrossProfitRate());
                    }
                    dealDetailList.add(detailVo);
                    zbpmSubmitVo.setDealDetailList(dealDetailList);
                }
                // 发起OA流程
                submitProcess(zbpmSubmitVo, tokenUserInfo);
                somDealOfTheDayMapper.batchSubmit(dealList);
            }
        } else {
            throw new ValidateException("Deal活动信息为空");
        }
    }

    private String nullToString(Object obj) {
        if (ObjectUtil.isNull(obj)) {
            return "";
        }
        return obj.toString();
    }

    private String beanToOaJson(ZBPMDealVo dealVo) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建要生成的JSON对象
        ObjectNode fdListObject = objectMapper.createObjectNode();
        ArrayNode idArray = objectMapper.createArrayNode();
        ArrayNode sellerSkuArray = objectMapper.createArrayNode();
        ArrayNode siteArray = objectMapper.createArrayNode();
        ArrayNode startArray = objectMapper.createArrayNode();
        ArrayNode endArray = objectMapper.createArrayNode();
        ArrayNode primeDayFlagArray = objectMapper.createArrayNode();
        ArrayNode lastDaysArray = objectMapper.createArrayNode();
        ArrayNode priceArray = objectMapper.createArrayNode();
        ArrayNode grossMarginArray = objectMapper.createArrayNode();
        ArrayNode activityGrossMargin = objectMapper.createArrayNode();
        ArrayNode stockSaleDaysArray = objectMapper.createArrayNode();
        ArrayNode saleYieldRateArray = objectMapper.createArrayNode();
        ArrayNode dealPriceArray = objectMapper.createArrayNode();
        ArrayNode dealDiscountArray = objectMapper.createArrayNode();
        ArrayNode couponDiscountArray = objectMapper.createArrayNode();
        ArrayNode promotionDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalAmountArray = objectMapper.createArrayNode();
        ArrayNode transactionPriceArray = objectMapper.createArrayNode();
        ArrayNode scoreArray = objectMapper.createArrayNode();
        ArrayNode estimatedDealSales = objectMapper.createArrayNode();
        ArrayNode salesFromMonthAgo = objectMapper.createArrayNode();
        ArrayNode salesMonthAgo = objectMapper.createArrayNode();
        ArrayNode salesGroupNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptCodeArray = objectMapper.createArrayNode();
        ArrayNode operationEmptNameArray = objectMapper.createArrayNode();
        ArrayNode operationEmpCodeArray = objectMapper.createArrayNode();
        ArrayNode applyReasonNameArray = objectMapper.createArrayNode();
        ArrayNode dealBurstCoefficientArray = objectMapper.createArrayNode();
        ArrayNode estimateOfSalesArray = objectMapper.createArrayNode();
        ArrayNode expectedGrossProfitMarginArray = objectMapper.createArrayNode();
        ArrayNode categoryGrossArray = objectMapper.createArrayNode();
        ArrayNode approvalRoleArray = objectMapper.createArrayNode();
        for (String id : dealVo.getAidList()) {
            idArray.add(id);
        }
        JsonNode type = new TextNode(dealVo.getDealTypeName());
        fdListObject.set("fd_dealTypeName", type);
        JsonNode country = new TextNode(dealVo.getCountry());
        fdListObject.set("fd_country", country);
        JsonNode dealType = new TextNode(dealVo.getDealType());
        fdListObject.set("fd_deal_type", dealType);
        JsonNode approvalRole = new TextNode(dealVo.getApprovalRole());
        fdListObject.set("fd_approvalRole", approvalRole);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ZBPMDealDetailVo> itemList = dealVo.getDealDetailList().stream().sorted(Comparator.comparing(ZBPMDealDetailVo::getTotalDiscount).reversed()).collect(Collectors.toList());
        for (ZBPMDealDetailVo detail : itemList) {
            sellerSkuArray.add(nullToString(detail.getSellerSku()));
            siteArray.add(nullToString(detail.getSite()));
            startArray.add(sf.format(detail.getPlanStartDate()));
            endArray.add(sf.format(detail.getPlanEndDate()));

            primeDayFlagArray.add((detail.getCampaignType()!=null && detail.getCampaignType() == 1) ? "是" : "否");

            lastDaysArray.add(nullToString(detail.getLastDays()));
            priceArray.add(detail.getPrice() == null ? "" : detail.getCurrencySymbol() + detail.getPrice().toString());
            grossMarginArray.add(nullToString(detail.getOneMonthGrossProfitRate()));
            activityGrossMargin.add(nullToString(detail.getDealPriceGross()));
            stockSaleDaysArray.add(nullToString(detail.getStockSaleDays()));
            saleYieldRateArray.add(nullToString(detail.getCompletionRate()));
            dealPriceArray.add(null == detail.getDealPrice() ? "" : detail.getCurrencySymbol() + detail.getDealPrice());
            dealDiscountArray.add(nullToString(detail.getDealDiscount()));
            couponDiscountArray.add(nullToString(detail.getCouponDiscount()));
            promotionDiscountArray.add(nullToString(detail.getPromotionDiscount()));
            totalDiscountArray.add(nullToString(detail.getTotalDiscount()));
            totalAmountArray.add(null == detail.getTotalDiscountAmount() ? "" : detail.getCurrencySymbol() + detail.getTotalDiscountAmount());
            transactionPriceArray.add(null == detail.getTransactionPrice() ? "" : detail.getCurrencySymbol() + detail.getTransactionPrice());
            scoreArray.add(nullToString(detail.getScore()));
            salesMonthAgo.add(nullToString(detail.getSalesMonthAgo()));
            salesFromMonthAgo.add(nullToString(detail.getSalesFromMonthAgo()));
            estimatedDealSales.add(nullToString(detail.getEstimatedDealSales()));
            salesGroupNameArray.add(nullToString(detail.getSalesGroupName()));
            salesGroupEmptNameArray.add(nullToString(detail.getSalesGroupEmptName()));
            salesGroupEmptCodeArray.add(nullToString(detail.getBusinessLeaderCode()));
            operationEmptNameArray.add(nullToString(detail.getOperationEmptName()));
            operationEmpCodeArray.add(nullToString(detail.getBusinessOperationCode()));
            applyReasonNameArray.add(nullToString(detail.getApplyReasonName()));
            dealBurstCoefficientArray.add(nullToString(detail.getDealBurstCoefficient()));
            estimateOfSalesArray.add(nullToString(detail.getEstimateOfSales()));
            expectedGrossProfitMarginArray.add(nullToString(detail.getExpectedGrossProfitMargin()));
            categoryGrossArray.add(nullToString(detail.getCategoryGross()));
            approvalRoleArray.add(nullToString(detail.getApprovalRole()));
        }
        fdListObject.set("fd_dotd_list.fd_dotd_id", idArray);
        fdListObject.set("fd_dotd_list.fd_dotd_sellerSku", sellerSkuArray);
        fdListObject.set("fd_dotd_list.fd_dotd_site", siteArray);
        fdListObject.set("fd_dotd_list.fd_dotd_startTime", startArray);
        fdListObject.set("fd_dotd_list.fd_dotd_endTime", endArray);

        fdListObject.set("fd_dotd_list.fd_dotd_primeDayFlag", primeDayFlagArray);
        fdListObject.set("fd_dotd_list.fd_dotd_lastDays", lastDaysArray);
        fdListObject.set("fd_dotd_list.fd_dotd_price", priceArray);
        fdListObject.set("fd_dotd_list.fd_dotd_grossMargin", grossMarginArray);
        fdListObject.set("fd_dotd_list.fd_dotd_activity_grossMargin", activityGrossMargin);
        fdListObject.set("fd_dotd_list.fd_dotd_stockSaleDays", stockSaleDaysArray);
        //添加 近四周预测达成率
        fdListObject.set("fd_dotd_list.fd_dotd_saleYieldRate", saleYieldRateArray);

        fdListObject.set("fd_dotd_list.fd_dotd_dealPrice", dealPriceArray);
        fdListObject.set("fd_dotd_list.fd_dotd_dealDiscount", dealDiscountArray);
        fdListObject.set("fd_dotd_list.fd_dotd_couponDiscount", couponDiscountArray);
        fdListObject.set("fd_dotd_list.fd_dotd_promotionDiscount", promotionDiscountArray);
        fdListObject.set("fd_dotd_list.fd_dotd_totalDiscount", totalDiscountArray);
        fdListObject.set("fd_dotd_list.fd_dotd_totalAmount", totalAmountArray);
        fdListObject.set("fd_dotd_list.fd_dotd_transactionPrice", transactionPriceArray);
        fdListObject.set("fd_dotd_list.fd_dotd_score", scoreArray);
        fdListObject.set("fd_dotd_list.fd_dotd_salesMonthAgo", salesMonthAgo);
        fdListObject.set("fd_dotd_list.fd_dotd_salesFromMonthAgo", salesFromMonthAgo);
        fdListObject.set("fd_dotd_list.fd_dotd_estimatedDealSales", estimatedDealSales);
        fdListObject.set("fd_dotd_list.fd_dotd_salesGroupName", salesGroupNameArray);
        fdListObject.set("fd_dotd_list.fd_dotd_salesGroupEmptName", salesGroupEmptNameArray);
        fdListObject.set("fd_dotd_list.fd_dotd_salesGroupEmptCode", salesGroupEmptCodeArray);
        fdListObject.set("fd_dotd_list.fd_dotd_operationEmptName", operationEmptNameArray);
        fdListObject.set("fd_dotd_list.fd_dotd_operationEmpCode", operationEmpCodeArray);
        fdListObject.set("fd_dotd_list.fd_dotd_applyReasonName", applyReasonNameArray);

        fdListObject.set("fd_dotd_list.fd_dotd_eventsEruptionSeveral", dealBurstCoefficientArray);
        fdListObject.set("fd_dotd_list.fd_dotd_eventsSellGross", estimateOfSalesArray);
        fdListObject.set("fd_dotd_list.fd_dotd_eventsGross", expectedGrossProfitMarginArray);
        fdListObject.set("fd_dotd_list.fd_dotd_categoryGross", categoryGrossArray);
        fdListObject.set("fd_dotd_list.fd_dotd_approvalRole", approvalRoleArray);
        // 将生成的JSON对象转换为字符串
        String json = objectMapper.writeValueAsString(fdListObject);
        return json;
    }

    /**
     * submitProcess
     * 提交流程
     *
     * @param json
     * @param process
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    private void submitProcess(ZBPMDealVo dealVo, TokenUserInfo tokenUserInfo) throws ValidateException, JsonProcessingException {
        McDictionaryInfo couponInfo = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "OATemplateId")
                .andEq("item_lable", "Deal")
                .single();
        String formValue = beanToOaJson(dealVo);
        MultiValueMap<String, Object> wholeForm = new LinkedMultiValueMap<>();
        //文档标题
        wholeForm.add("docSubject", "亚马逊Deals活动申请");
        //流程发起人
        wholeForm.add("docCreator", String.format("{\"LoginName\": \"%s\"}", tokenUserInfo.getJobNumber()));
        //文档状态，可以为草稿（"10"）或者待审（"20"）两种状态，默认为待审
        wholeForm.add("docStatus", "20");
        //文档模板id，不允许为空
        wholeForm.add("fdTemplateId", couponInfo.getItemValue());
        String formValues = null;
        //流程表单数据，允许为空
        wholeForm.add("formValues", formValue);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(wholeForm, headers);
        //有返回值的情况 VO可以替换成具体的JavaBean
        ResponseEntity<String> obj = null;
        try {
            log.info("OA新增流程传参,url:{},param:{}", oaUrl, JSONUtil.toJsonStr(entity));
            obj = restTemplate.exchange(oaUrl + "/api/km-review/kmReviewRestService/addReview", HttpMethod.POST, entity, String.class);
        } catch (RestClientException e) {
            log.error("调用OA失败：" + e.getMessage());
            if (StrUtil.isBlank(e.getMessage())) {
                throw new ValidateException(e.getCause().getMessage());
            } else {
                throw new ValidateException(e.getMessage());
            }
        }
        String body = obj.getBody();
        log.info("返回信息：", body);
        if (obj.getStatusCode() != HttpStatus.OK) {
            throw new ValidateException(body);
        }
    }

    /**
     * exportExcel
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String exportExcel(SomDealOfTheDayPageSearchVo searchVo) {
        List<SomDealOfTheDayExportVo> dealOfTheDayExportVos = somDealOfTheDayMapper.exportExcel(searchVo);
        if (CollUtil.isEmpty(dealOfTheDayExportVos)) {
            return null;
        }
        List<String> dictCodes = Arrays.asList("PDDApplyReason", "PDDStatus", "CampaignType", "PDDModifyStatus");
        List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", dictCodes).select();
        Map<String, Map<String, String>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode,
                        Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (v1, v2) -> v1)));
        for (SomDealOfTheDayExportVo dealOfTheDayExportVo : dealOfTheDayExportVos) {
            // 申请原因
            Map<String, String> applyReasonMap = mcDictionaryInfoMap.get("PDDApplyReason");
            dealOfTheDayExportVo.setApplyReasonName(CollUtil.isEmpty(applyReasonMap) ? null : applyReasonMap.get(String.valueOf(dealOfTheDayExportVo.getApplyReason())));
            // 活动状态
            Map<String, String> statusMap = mcDictionaryInfoMap.get("PDDStatus");
            dealOfTheDayExportVo.setStatusName(CollUtil.isEmpty(statusMap) ? null : statusMap.get(String.valueOf(dealOfTheDayExportVo.getStatus())));
            // 大促类型
            Map<String, String> campaignTypeMap = mcDictionaryInfoMap.get("CampaignType");
            dealOfTheDayExportVo.setCampaignTypeName(CollUtil.isEmpty(campaignTypeMap) ? null : campaignTypeMap.get(String.valueOf(dealOfTheDayExportVo.getCampaignType())));
            // 修改状态
            Map<String, String> modifyStatusMap = mcDictionaryInfoMap.get("PDDModifyStatus");
            dealOfTheDayExportVo.setModifyStatusName(CollUtil.isEmpty(modifyStatusMap) ? null : modifyStatusMap.get(String.valueOf(dealOfTheDayExportVo.getModifyStatus())));
            // 填充错误信息，如果状态非需要关注，需要把字段值手动置为 null
            Integer needAttentionStatus = 41;
            dealOfTheDayExportVo.setErrorMsg(!needAttentionStatus.equals(dealOfTheDayExportVo.getStatus()) ? null : dealOfTheDayExportVo.getErrorMsg());
        }
        try {
            Workbook workbook;
            ExportParams params = new ExportParams(null, "DOTD");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomDealOfTheDayExportVo.class, dealOfTheDayExportVos);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(byteArray);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * importExcel
     * 导入
     *
     * @param list
     * @param tokenUser
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String importExcel(List<SomDealOfTheDayExtVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder result = new StringBuilder();
        long count = list.stream().map(m -> m.getSite()).distinct().count();
        if (1 < count) {
            result.append("EXCEL中必须是相同的站点");
            return result.toString();
        }
        Map<String, List<SomDealOfTheDayExtVo>> map = list.stream().collect(Collectors.groupingBy(p -> String.format("%s,%s", p.getSite(), p.getSellerSku())));
        for (String key : map.keySet()) {
            if (map.get(key).size() > 1) {
                List<String> strings = Arrays.asList(key.split(","));
                result.append(strings.get(0)).append("\n").append(strings.get(1)).append("\n");
            }
        }
        if (StrUtil.isNotBlank(result)) {
            result.append("不允许出现重复数据");
            return result.toString();
        }
        MarketActivityUtil activityUtil = new MarketActivityUtil();

        // 根据站点展示码查询币种/asin/现售价/三级分类
        List<String> sellerSkus = list.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());

        //查询数据库发现品牌数据是展示码，输出日志排查问题
        String brandNames = list.stream().filter(x->StrUtil.isNotBlank(x.getSellerSku())).map(m -> m.getBrandName()).distinct().collect(Collectors.joining(","));
        log.info("DOTD导入Excel品牌名称:{}", brandNames);

        List<String> platformSkuList = list.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McSellerskuMapping> mappingList = skuMappingMapper.createLambdaQuery().andEq("site", list.get(0).getSite()).andIn("product_display_code", sellerSkus).select();
        List<String> skuMappingList = mappingList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        sellerSkus.addAll(skuMappingList);
        List<String> platformSkuMappingList = mappingList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        platformSkuList.addAll(platformSkuMappingList);
        List<McListingInfoAmazonExVo> amazonExVoList = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).searchBySiteSellerSkus(list.get(0).getSite(), sellerSkus.stream().distinct().collect(Collectors.toList()));
        // 获取listing信息
        List<McListingInfoAmazon> amazonList = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andEq("site", list.get(0).getSite()).andIn("seller_sku", platformSkuList).select();
        String errorStr = validate(list, amazonExVoList, mappingList, amazonList);
        if (StrUtil.isNotBlank(errorStr)) {
            return errorStr;
        }
        List<SomDealOfTheDay> dealList = new ArrayList<>();
        // 查询coupon折扣
        SomCouponSearchVo couponSearchVo = new SomCouponSearchVo();
        couponSearchVo.setPlatform(list.get(0).getPlatform());
        couponSearchVo.setSite(list.get(0).getSite());
        couponSearchVo.setSellerSkuList(sellerSkus);
        List<SomCouponItemsExtVo> couponItemsList = dynamicSqlManager.getMapper(SomCouponItemsMapper.class).queryDiscountList(couponSearchVo);
        List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "PDDApplyReason").select();
        String aid = Strings.EMPTY;
        // 查询可售库存
        List<McStockInfoExtVo> stockInfoList = dynamicSqlManager.getMapper(McStockInfoMapper.class).queryBySitesAndSkus(Collections.singletonList(list.get(0).getSite()), amazonExVoList.stream().map(m -> m.getSku()).collect(Collectors.toList()));

        // 封装BI接口参数
        List<String> displayProductCodes = list.stream().map(SomDealOfTheDayVo::getSellerSku).collect(Collectors.toList());
        List<String> sites = list.stream().map(SomDealOfTheDayVo::getSite).collect(Collectors.toList());
        List<McProductSales> mcProductSalesList = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery()
                .andIn("display_product_code", displayProductCodes)
                .andIn("site", sites)
                .andEq("is_enabled", 1)
                .select();
        Map<String, McProductSales> mcProductSalesMap = mcProductSalesList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        List<BiAmazonProfitCalculationVo> biBody = list.stream().map(x -> {
            BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
            bi.setSite(x.getSite());
            // 美国站点 需传开始结束时间
            if ("Amazon.com".equals(x.getSite())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String date = sdf.format(x.getPlanStartDate());
                bi.setDate(date);
            }
            bi.setPrice(x.getDealPrice());//秒杀价
            bi.setDisplayProductCode(x.getSellerSku());
            bi.setProductMainCode(mcProductSalesMap.get(x.getSite() + x.getSellerSku()).getProductMainCode());
            bi.setPromotionType("DOTD");
            return bi;
        }).collect(Collectors.toList());

        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        if (biList == null || biList.isEmpty()) {
            throw new IllegalArgumentException("Bi接口获取数据为空");
        }
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        StringBuilder errorStrBi = new StringBuilder();
        for (SomDealOfTheDayExtVo importVo : list) {
            String key = importVo.getSite() + importVo.getSellerSku();
            BiAmazonProfitCalculationVo BiData = biMap.get(key);
            if (BiData != null && BiData.getGross() != null) {
                BigDecimal gross = BiData.getGross();
                if (gross.compareTo(BigDecimal.valueOf(-9999)) != 0) {
                    gross = gross.multiply(BigDecimal.valueOf(100));
                }
                importVo.setDealPriceGross(gross.setScale(2, RoundingMode.HALF_UP));
            } else {
                if (importVo.getDealPriceGross() == null) {
                    errorStrBi.append("站点:" + importVo.getSite() + " 展示码:" + importVo.getSellerSku() + " 未获取到'秒杀价毛利率'\n");
                } else {
                    importVo.setDealPriceGross(importVo.getDealPriceGross().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if (BiData != null && BiData.getDealBurstCoefficient() != null) {
                importVo.setDealBurstCoefficient(BiData.getDealBurstCoefficient().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
            } else {
                if (importVo.getDealBurstCoefficient() == null) {
                    errorStrBi.append("站点:" + importVo.getSite() + " 展示码:" + importVo.getSellerSku() + " 未获取到'活动预计爆发系数'\n");
                } else {
                    importVo.setDealBurstCoefficient(importVo.getDealBurstCoefficient().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if (BiData != null && BiData.getDmsLast30day() != null) {
                importVo.setDmsLast30day(BiData.getDmsLast30day().setScale(2, RoundingMode.HALF_UP));
            } else {
                if (importVo.getDmsLast30day() == null) {
                    errorStrBi.append("站点:" + importVo.getSite() + " 展示码:" + importVo.getSellerSku() + " 未获取到'近三十天DMS'\n");
                }
            }
            if (BiData != null && BiData.getCategoryGross() != null) {
                importVo.setCategoryGross(BiData.getCategoryGross().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
            } else {
                if (importVo.getCategoryGross() == null) {
                    errorStrBi.append("站点:" + importVo.getSite() + " 展示码:" + importVo.getSellerSku() + " 未获取到'三级分类近四周毛利率'\n");
                } else {
                    importVo.setCategoryGross(importVo.getCategoryGross().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
        }
        if (errorStrBi.length() > 0) {
            return errorStrBi.toString() + "请您手工输入。";
        }
        for (SomDealOfTheDayExtVo importVo : list) {
            BigDecimal dealPriceGross = importVo.getDealPriceGross().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 秒杀价毛利率
            BigDecimal dmsLast30day = importVo.getDmsLast30day(); // 近三十天DMS
            BigDecimal dealBurstCoefficient = importVo.getDealBurstCoefficient().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 活动预计爆发系数
            BigDecimal dealPrice = importVo.getDealPrice(); // 秒杀价
            Date planStartDate = importVo.getPlanStartDate(); // 活动开始时间
            Date planEndDate = importVo.getPlanEndDate(); // 活动结束时间
            // 活动天数
            long daysBetween = DateUtil.betweenDay(planStartDate, planEndDate, true);
            daysBetween = daysBetween == 0 ? 1 : daysBetween + 1;

            // 活动预计销量 = 活动预计爆发系数 * 近三十天DMS * 活动天数
            BigDecimal expectedSalesVolume = dealBurstCoefficient.multiply(dmsLast30day).multiply(new BigDecimal(daysBetween)).setScale(2, RoundingMode.HALF_UP);
            importVo.setExpectedSalesVolume(expectedSalesVolume);

            // 活动预计销售额 = 活动预计销量 * 秒杀价
            BigDecimal estimateOfSales = expectedSalesVolume.multiply(dealPrice).setScale(2, RoundingMode.HALF_UP);
            importVo.setEstimateOfSales(estimateOfSales);

            // 活动预计毛利额 = 活动预计销售额 * 秒杀价毛利率
            BigDecimal expectedGrossProfitMargin = estimateOfSales.multiply(dealPriceGross).setScale(2, RoundingMode.HALF_UP);
            // 数值过大 强制转换
            if (expectedGrossProfitMargin.toPlainString().replace("-", "").replace(".", "").length() > 8) {
                expectedGrossProfitMargin = BigDecimal.valueOf(-9999);
            }
            importVo.setExpectedGrossProfitMargin(expectedGrossProfitMargin);

            // 确定审批角色
            importVo.setApprovalRole(initApprovalRole(importVo));
        }

        //计算库存可售天数
        List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (SomDealOfTheDayExtVo tmp : list) {
            MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
            activity.setSite(tmp.getSite());
            activity.setSellerSku(tmp.getSellerSku());
            activity.setStartDate(sdf.format(tmp.getPlanStartDate()));
            activity.setEndDate(sdf.format(tmp.getPlanEndDate()));
            activity.setType("DOTD");
            body.add(activity);
        }
        MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, false);
        Map<String, Object> daysMap = daysMsgData.getDaysMap();
        Map<String, String> errorMap = daysMsgData.getErrorMap();
        List<Map<String, String>> msgList = new ArrayList<>();

        for (SomDealOfTheDayExtVo importVo : list) {
            SomDealOfTheDay deal = ConvertUtils.beanConvert(importVo, SomDealOfTheDay.class);
            McDictionaryInfo applyReason = infoList.stream().filter(s -> StrUtil.equalsIgnoreCase(importVo.getApplyReasonName().trim(), s.getItemLable().trim()))
                    .findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(applyReason) && StrUtil.isNotEmpty(applyReason.getItemValue())) {
                deal.setApplyReason(Integer.valueOf(applyReason.getItemValue()));
            }
            McSellerskuMapping mapping = mappingList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), importVo.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(mapping)) {
                deal.setSellerSku(mapping.getSellerSku());
            }
            McListingInfoAmazon amazon = amazonList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), deal.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(amazon)) {
                deal.setChildAsin(amazon.getAsinCode());
                deal.setFulfillmentChannel(amazon.getFulfillmentChannel());
                deal.setPrice(amazon.getPrice());
                deal.setProductName(amazon.getProductName());
            }
            if (ObjectUtil.isNotEmpty(mapping)) {
                deal.setSellerSku(mapping.getProductDisplayCode());
            }
            //设置库存可售天数
            if (daysMap.containsKey(importVo.getSellerSku())) {
                deal.setStockSaleDays((Integer) daysMap.get(importVo.getSellerSku()));
                deal.setCompletionRate((BigDecimal)daysMap.get(importVo.getSellerSku()+"_completionRate"));
            }else{
                String tmpMsg = "*站点：%s*\n*展示码：%s*\n**失败原因**：%s";
                String errorMsg = errorMap.get(importVo.getSellerSku());
                errorMsg = errorMsg.replace("%%", "%");
                tmpMsg = String.format(tmpMsg, deal.getSite(), deal.getSellerSku(), errorMsg);
                Map<String, String> msg = new HashMap<>();
                msg.put("tag", "markdown");
                msg.put("content",tmpMsg);
                msgList.add(msg);
                continue;
            }

            McListingInfoAmazonExVo listingInfo = amazonExVoList.stream().filter(f -> StrUtil.equals(deal.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(listingInfo)) {
                deal.setSellerSku(listingInfo.getSellerSku());
                deal.setCategoryName(listingInfo.getCategoryName());
                deal.setCurrencyCode(listingInfo.getCurrencyCode());
                // 计算库存可售天数
                /*if (StrUtil.isNotEmpty(listingInfo.getSku())) {
                    List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(importVo.getSite(), f.getSite()) &&
                            StrUtil.equalsIgnoreCase(listingInfo.getSku(), f.getProductMainCode())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(stockInfos)) {
                        long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                        BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                            deal.setStockSaleDays(0);
                        } else {
                            // 库存可销售天数 库存/7天发货平均值 只取整数
                            int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                            deal.setStockSaleDays(stockSaleDays);
                        }
                    } else {
                        deal.setStockSaleDays(0);
                    }
                } else {
                    deal.setStockSaleDays(0);
                }*/
            }
            // 唯一性校验
            SomDealRuleConfigVo config = configMapper.queryBySiteDealType(importVo.getSite(), "DOTD");
            if (ObjectUtil.isEmpty(config) || StrUtil.isEmpty(config.getRules())) {
                throw new ValidateException("当前站点下该Deal类型未配置秒杀规则,请前往秒杀规则配置功能进行配置");
            }
            dealService.checkDeal(aid, importVo.getSite(), Collections.singletonList(importVo.getSellerSku()), Collections.singletonList(deal.getChildAsin()), importVo.getPlanStartDate(), config);
            deal.setAid(IdUtil.fastSimpleUUID());
            deal.setPlatform("Amazon");
            BigDecimal discount = deal.getDealPrice().divide(deal.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
            deal.setDealDiscount(BigDecimal.ONE.subtract(discount).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
            SomCouponItemsVo couponItemsExtVo = couponItemsList.stream().filter(f -> StrUtil.equals(importVo.getSellerSku(), f.getSellerSku())
                    && importVo.getPlanStartDate().after(f.getBeginDate()) && importVo.getPlanEndDate().before(f.getEndDate())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(couponItemsExtVo) && ObjectUtil.isNotEmpty(couponItemsExtVo.getCouponDiscount())) {
                deal.setCouponDiscount(couponItemsExtVo.getCouponDiscount());
            } else {
                deal.setCouponDiscount(BigDecimal.ZERO);
            }
            McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
            BigDecimal promotionDiscount = BigDecimal.ZERO;
            try {
                searchExVo.setSite(importVo.getSite());
                searchExVo.setKeyWord(importVo.getSellerSku());
                searchExVo.setBeginDate(importVo.getPlanStartDate());
                searchExVo.setEndDate(importVo.getPlanEndDate());
                promotionDiscount = amazonService.queryPromotionDiscount(searchExVo);
            } catch (ValidateException e) {
                e.printStackTrace();
            }
            deal.setPromotionDiscount(promotionDiscount);
            deal.setTotalDiscount(deal.getDealDiscount().add(deal.getCouponDiscount()).add(deal.getPromotionDiscount()));
            deal.setTotalDiscountAmount(deal.getPrice().multiply(deal.getTotalDiscount()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            deal.setTransactionPrice(deal.getPrice().subtract(deal.getTotalDiscountAmount()));
            deal.setPlanStartDate(DateUtil.offsetMillisecond(importVo.getPlanStartDate(), 0).toJdkDate());
            deal.setPlanEndDate(DateUtil.offsetMinute(importVo.getPlanEndDate(), 1439).toJdkDate());
            deal.setScore(ObjectUtil.isEmpty(importVo.getScore()) ? BigDecimal.ZERO : importVo.getScore());
            deal.setStatus(10);
            deal.setCreateNum(tokenUser.getJobNumber());
            deal.setCreateName(tokenUser.getUserName());
            deal.setCreateTime(DateTime.now().toJdkDate());
//            BigDecimal dealGross = biMap.getOrDefault(importVo.getSite() + importVo.getSellerSku(), null);
//            if (dealGross != null) {
//                dealGross = dealGross.multiply(BigDecimal.valueOf(100));
//            }
//            deal.setDealPriceGross(dealGross);
            dealList.add(deal);
        }
        if (!msgList.isEmpty()) {
            String msgBody = "{\"header\":{\"template\":\"red\",\"title\":{\"tag\":\"plain_text\",\"content\":\"%s\"}},\"elements\":[%s]}";
            String msgs = msgList.stream().map(x -> JSONUtil.toJsonStr(x)).collect(Collectors.joining(","));
            msgBody = String.format(msgBody, "DOTD库存可售天数异常提醒", msgs);
            try {
                FeiShuUtils.sendNotice(tokenUser.getJobNumber(), msgBody, MessageType.INTERACTIVE);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (!dealList.isEmpty()) {
            somDealOfTheDayMapper.insertBatch(dealList);
        }
        return result.toString();
    }

    /**
     * validate
     * 导入校验
     *
     * @param list
     * @return {@link java.lang.StringBuilder}
     * <AUTHOR>
     * @history
     */
    private String validate(List<SomDealOfTheDayExtVo> list, List<McListingInfoAmazonExVo> amazonExVoList, List<McSellerskuMapping> mappingList, List<McListingInfoAmazon> amazonList) {
        String errorStr = Strings.EMPTY;
        List<String> errorList = new ArrayList<>();
        for (SomDealOfTheDayExtVo vo : list) {
            if (StrUtil.isBlank(vo.getAcceptAdjustment()) || (!vo.getAcceptAdjustment().equalsIgnoreCase("no") && !vo.getAcceptAdjustment().equalsIgnoreCase("yes"))) {
                errorList.add("是否接受调整开跑日期不能为空");
            }
            if (StrUtil.isBlank(vo.getSite())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "站点不能为空");
                errorList.add(error);
            }
            if (StrUtil.isBlank(vo.getSellerSku())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "展示码不能为空");
                errorList.add(error);
            }
            if (ObjectUtil.isEmpty(vo.getPlanStartDate())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "活动开始时间不能为空");
                errorList.add(error);
            }
            if (ObjectUtil.isEmpty(vo.getPlanEndDate())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "活动截止时间不能为空");
                errorList.add(error);
            }
            if (ObjectUtil.isEmpty(vo.getLastDays()) || Integer.valueOf(0).compareTo(vo.getLastDays()) == 1) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "期望运行天数不能为空,且必须>0");
                errorList.add(error);
            }
            if (ObjectUtil.isEmpty(vo.getDealPrice())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "秒杀价不能为空");
                errorList.add(error);
            }
            if (StrUtil.isBlank(vo.getApplyReasonName())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "申请原因不能为空");
                errorList.add(error);
            }
            if (StrUtil.equals("自定义", vo.getApplyReasonName()) && StrUtil.isBlank(vo.getCutomerReason())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "申请原因为自定义时,自定义申请原因字段不能为空");
                errorList.add(error);
            }
            if (StrUtil.equalsIgnoreCase("Amazon.co.jp", vo.getSite()) && ObjectUtil.isEmpty(vo.getCommentsNumber())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "评价数不能为空");
                errorList.add(error);
            }
            if ((StrUtil.equalsIgnoreCase("Amazon.co.jp", vo.getSite()) || StrUtil.equalsIgnoreCase("Amaozn.ca", vo.getSite())) && StrUtil.isBlank(vo.getAmazonCategory())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "大目录不能为空");
                errorList.add(error);
            }
            if (!(StrUtil.equals("Amazon.nl", vo.getSite()) && !StrUtil.equals("Amazon.tr", vo.getSite())) && !StrUtil.equals("Amazon.se", vo.getSite())
                    && !StrUtil.equals("Amazon.pl", vo.getSite()) && !StrUtil.equals("Amazon.com.be", vo.getSite())) {
//                if (StrUtil.isBlank(vo.getParentAsin())) {
//                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
//                            "父ASIN不能为空");
//                    errorList.add(error);
//                }
//                if (StrUtil.isBlank(vo.getBrandName())) {
//                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
//                            "品牌不能为空");
//                    errorList.add(error);
//                }
//                if (ObjectUtil.isEmpty(vo.getSalesMonthAgo()) || Integer.valueOf(0).compareTo(vo.getSalesMonthAgo()) == 1) {
//                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
//                            "T28销量不能为空,且必须是整数，必须≥0");
//                    errorList.add(error);
//                }
//                if (ObjectUtil.isEmpty(vo.getSalesFromMonthAgo()) || BigDecimal.valueOf(0).compareTo(vo.getSalesFromMonthAgo()) == 1) {
//                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
//                            "T28销售额不能为空,且必须≥0");
//                    errorList.add(error);
//                }
//                if (ObjectUtil.isEmpty(vo.getEstimatedDealSales()) || Integer.valueOf(0).compareTo(vo.getEstimatedDealSales()) == 1) {
//                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
//                            "预计TD销量不能为空,且必须是整数，必须≥0");
//                    errorList.add(error);
//                }
            }
            String oldSellerSku = vo.getSellerSku();
            McSellerskuMapping mapping = mappingList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getProductDisplayCode(), vo.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(mapping)) {
                vo.setSellerSku(mapping.getSellerSku());
            }
            McListingInfoAmazon amazon = amazonList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), vo.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(amazon)) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "查询不到现售价");
                errorList.add(error);
            }
            if (ObjectUtil.isNotEmpty(mapping)) {
                vo.setSellerSku(mapping.getProductDisplayCode());
            }
            McListingInfoAmazonExVo listingInfo = amazonExVoList.stream().filter(f -> StrUtil.equals(vo.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(listingInfo)) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}的秒杀活动{}", vo.getSite(), vo.getSellerSku(), DateUtil.formatDate(vo.getPlanStartDate()), DateUtil.formatDate(vo.getPlanEndDate()),
                        "查询不到现售价");
                errorList.add(error);
            }
            vo.setSellerSku(oldSellerSku);
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            errorStr = errorList.stream().collect(Collectors.joining("\n"));
        }
        return errorStr;
    }

    /**
     * getWednesday
     * 获取本周四
     *
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @history
     */
    private Date getWednesday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.WEDNESDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param list
     * @param tokenUser
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public String batchFeedbackResult(List<SomDealOfTheDayExtVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder();
        for (SomDealOfTheDayExtVo importVo : list) {
            if (StrUtil.isBlank(importVo.getSite())) {
                throw new ValidateException("站点不允许为空");
            }
            if (StrUtil.isBlank(importVo.getDealTypeName())) {
                throw new ValidateException("秒杀类型不允许为空");
            }
            if (ObjectUtil.isEmpty(importVo.getPlanStartDate())) {
                throw new ValidateException("活动开始时间不允许为空");
            }
            if (ObjectUtil.isEmpty(importVo.getPlanEndDate())) {
                throw new ValidateException("活动截止时间不允许为空");
            }
            if (StrUtil.isBlank(importVo.getSellerSku())) {
                throw new ValidateException("展示码不允许为空");
            }
            if (StrUtil.isBlank(importVo.getSubmitResult())) {
                throw new ValidateException("提报结果不允许为空");
            }
            if (StrUtil.equalsIgnoreCase("提报失败", importVo.getSubmitResult()) && StrUtil.isBlank(importVo.getSubmissionFailureRemark())) {
                throw new ValidateException("提报失败时,失败原因不允许为空");
            }
            // 秒杀类型
            if (!StrUtil.equals("DOTD", importVo.getDealTypeName())) {
                throw new ValidateException("存在不为DOTD类型的数据");
            }
            // 提报结果
            if (StrUtil.equals("提报成功", importVo.getSubmitResult())) {
                importVo.setStatus(40);
            } else if (StrUtil.equals("提报失败", importVo.getSubmitResult())) {
                importVo.setStatus(39);
            }
        }
        List<String> sites = list.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
        List<String> sellerSkus = list.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<Integer> statusList = Arrays.asList(110, 21);
        List<SomDealOfTheDay> dealList = somDealOfTheDayMapper.createLambdaQuery().andIn("site", sites).andIn("seller_sku", sellerSkus).andIn("status", statusList).select();
        List<SomDealOfTheDay> updateList = new ArrayList<>();
        for (SomDealOfTheDayExtVo importVo : list) {
            Date startDate = DateUtil.offsetMillisecond(importVo.getPlanStartDate(), 0).toJdkDate();
            Date endDate = DateUtil.offsetMinute(importVo.getPlanEndDate(), 1439).toJdkDate();
            SomDealOfTheDay deal = dealList.stream().filter(f -> StrUtil.equals(importVo.getSite(), f.getSite()) && StrUtil.equals(importVo.getSellerSku(), f.getSellerSku())
                    && ((f.getPlanStartDate().compareTo(startDate) == -1) || (f.getPlanStartDate().compareTo(startDate) == 0)) && (f.getPlanEndDate().after(endDate) || (f.getPlanEndDate().compareTo(endDate) == 0))).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(deal)) {
                deal.setStatus(importVo.getStatus());
                if (ObjectUtil.equal(39, importVo.getStatus())) {
                    deal.setSubmissionFailureRemark(importVo.getSubmissionFailureRemark());
                }
                deal.setRealStartDate(deal.getPlanStartDate());
                deal.setRealEndDate(deal.getPlanEndDate());
                deal.setModifyNum(tokenUser.getJobNumber());
                deal.setModifyName(tokenUser.getUserName());
                deal.setModifyTime(DateTime.now().toJdkDate());
                updateList.add(deal);
            } else {
                resultStr.append("站点:").append(importVo.getSite()).append("展示码:").append(importVo.getSellerSku()).append("秒杀类型:").append(importVo.getDealTypeName())
                        .append("起止时间:").append(DateUtil.format(importVo.getPlanStartDate(), "yyyy-MM-dd")).append("~").append(DateUtil.format(importVo.getPlanEndDate(), "yyyy-MM-dd")).append("的秒杀活动不存在").append("\n");
            }
        }
        if (StrUtil.isNotBlank(resultStr)) {
            return resultStr.toString();
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            somDealOfTheDayMapper.batchFeedbackResult(updateList);
        }
        List<SomDealOfTheDay> eventList = dealList.stream().filter(f -> ObjectUtil.equal(39, f.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(eventList)) {
            try {
                List<AmazonActivityReminderEvent.Reminder> reminderList = new ArrayList<>();
                for (SomDealOfTheDay deal : eventList) {
                    SomDealOfTheDayExtVo feedbackVo = list.stream().filter(f -> StrUtil.equalsIgnoreCase(deal.getSite(), f.getSite()) && StrUtil.equalsIgnoreCase(deal.getSellerSku(), f.getSellerSku()))
                            .findFirst().orElse(null);
                    AmazonActivityReminderEvent.Reminder reminder = new AmazonActivityReminderEvent.Reminder(deal.getSite(), deal.getSellerSku(), Strings.EMPTY,
                            Strings.EMPTY, deal.getPlanStartDate(), deal.getPlanEndDate(), "DOTD", feedbackVo.getSubmissionFailureRemark(), deal.getCreateNum());
                    reminderList.add(reminder);
                }
                AmazonActivityReminderEvent event = new AmazonActivityReminderEvent(reminderList);
                eventBusTemplate.publish(event);
            } catch (Exception e) {
                throw new ValidateException("放入消息队列出错" + e.getMessage());
            }
        }
        return Strings.EMPTY;
    }

    /**
     * cancel
     * 取消活动
     *
     * @param cancelVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void cancel(SomDealOfTheDayVo cancelVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(cancelVo) || StrUtil.isBlank(cancelVo.getAid()) || StrUtil.isBlank(cancelVo.getCancelRemark())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<Integer> statuses = Arrays.asList(40, 41);
        SomDealOfTheDay deal = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", cancelVo.getAid()).andIn("status", statuses).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许取消");
        }
        //90.取消中
        deal.setStatus(90);
        deal.setCancelRemark(cancelVo.getCancelRemark());
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somDealOfTheDayMapper.updateById(deal);
    }

    /**
     * feedbackCancelResult
     * 反馈取消结果
     *
     * @param feedbackVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void feedbackCancelResult(SomDealOfTheDayVo feedbackVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomDealOfTheDay deal = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", feedbackVo.getAid()).andEq("status", 90).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许反馈取消结果");
        }
        if (ObjectUtil.equal(91, feedbackVo.getStatus()) && StrUtil.isBlank(feedbackVo.getCacenlFailureRemark())) {
            throw new ValidateException("取消失败时,失败原因不能为空");
        } else {
            deal.setCacenlFailureRemark(feedbackVo.getCacenlFailureRemark());
        }
        deal.setStatus(feedbackVo.getStatus());
        deal.setRealStartDate(feedbackVo.getRealStartDate());
        deal.setRealEndDate(feedbackVo.getRealEndDate());
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somDealOfTheDayMapper.updateById(deal);
    }

    /**
     * cloneById
     * 克隆
     *
     * @param cloneVo
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void cloneById(SomDealOfTheDayVo cloneVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(cloneVo) || StrUtil.isEmpty(cloneVo.getAid()) || ObjectUtil.isEmpty(cloneVo.getPlanStartDate()) || ObjectUtil.isEmpty(cloneVo.getPlanEndDate())|| ObjectUtil.isEmpty(cloneVo.getLastDays())) {
            throw new ValidateException("数据存在空值，请检查");
        }
        SomDealOfTheDay obj = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", cloneVo.getAid()).single();
        if (ObjectUtil.isEmpty(obj)) {
            throw new ValidateException("当前数据不允许克隆");
        }
        // 唯一性校验
        cloneVo.setChildAsin(obj.getChildAsin());
        cloneVo.setSite(obj.getSite());
        List<Integer> countList = somDealOfTheDayMapper.checkUnique(cloneVo);
        int sum = countList.stream().mapToInt(m -> m).sum();
        if (0 < sum) {
            throw new ValidateException("当前数据存在其他类型的Deal，请检查数据");
        }
        SomDealRuleConfigVo config = configMapper.queryBySiteDealType(cloneVo.getSite(), "DOTD");
        if (ObjectUtil.isEmpty(config) || StrUtil.isEmpty(config.getRules())) {
            throw new ValidateException("当前站点下该Deal类型未配置秒杀规则,请前往秒杀规则配置功能进行配置");
        }
        dealService.checkDeal(Strings.EMPTY, cloneVo.getSite(), Collections.singletonList(cloneVo.getSellerSku()), Collections.singletonList(cloneVo.getChildAsin()), cloneVo.getPlanStartDate(), config);
        SomCouponSearchVo couponSearchVo = new SomCouponSearchVo();
        couponSearchVo.setPlatform(obj.getPlatform());
        couponSearchVo.setSite(obj.getSite());
        couponSearchVo.setSellerSkuList(Collections.singletonList(obj.getSellerSku()));
        List<SomCouponItemsExtVo> couponItemsList = dynamicSqlManager.getMapper(SomCouponItemsMapper.class).queryDiscountList(couponSearchVo);
        SomCouponItemsVo couponItemsExtVo = couponItemsList.stream().filter(f -> StrUtil.equals(obj.getSellerSku(), f.getSellerSku())
                && cloneVo.getPlanStartDate().after(f.getBeginDate()) && cloneVo.getPlanEndDate().before(f.getEndDate())).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(couponItemsExtVo) && ObjectUtil.isNotEmpty(couponItemsExtVo.getCouponDiscount())) {
            obj.setCouponDiscount(couponItemsExtVo.getCouponDiscount());
        } else {
            obj.setCouponDiscount(BigDecimal.ZERO);
        }
        BigDecimal promotionDiscount = BigDecimal.ZERO;
        try {
            McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
            searchExVo.setSite(obj.getSite());
            searchExVo.setKeyWord(obj.getSellerSku());
            searchExVo.setBeginDate(obj.getPlanStartDate());
            searchExVo.setEndDate(obj.getPlanEndDate());
            promotionDiscount = amazonService.queryPromotionDiscount(searchExVo);
        } catch (Exception e) {
            throw new ValidateException("查询promotion折扣出错" + e.getMessage());
        }
        obj.setPromotionDiscount(promotionDiscount);
        obj.setTotalDiscount(obj.getDealDiscount().add(obj.getCouponDiscount()).add(obj.getPromotionDiscount()));
        obj.setTotalDiscountAmount(obj.getPrice().multiply(obj.getTotalDiscount()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        obj.setTransactionPrice(obj.getPrice().subtract(obj.getTotalDiscountAmount()));
        obj.setAid(IdUtil.fastSimpleUUID());
        obj.setStatus(10);
        obj.setPlanStartDate(cloneVo.getPlanStartDate());
        obj.setPlanEndDate(cloneVo.getPlanEndDate());
        //设置 期望运行天数
        obj.setLastDays(cloneVo.getLastDays());
        obj.setCreateNum(tokenUser.getJobNumber());
        obj.setCreateName(tokenUser.getUserName());
        obj.setCreateTime(DateTime.now().toJdkDate());
        obj.setRealStartDate(null);
        obj.setRealEndDate(null);
        obj.setAuditFailureRemark(null);
        obj.setModifyName(null);
        obj.setModifyNum(null);
        obj.setModifyTime(null);
        obj.setSubmissionFailureRemark(null);
        obj.setCancelRemark(null);
        obj.setCacenlFailureRemark(null);
        McProductSales productSale = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery().andEq("site", obj.getSite())
                .andEq("display_product_code", obj.getSellerSku()).single();
        if (ObjectUtil.isNotEmpty(productSale) && StrUtil.isNotEmpty(productSale.getProductMainCode())) {
            List<McStockInfoExtVo> stockInfoList = dynamicSqlManager.getMapper(McStockInfoMapper.class).queryBySitesAndSkus(Collections.singletonList(obj.getSite()), Collections.singletonList(productSale.getProductMainCode()));
            if (CollectionUtil.isNotEmpty(stockInfoList)) {
                long totalStock = stockInfoList.stream().mapToInt(m -> m.getTotalStock()).sum();
                BigDecimal sevenDayNumber = stockInfoList.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                    obj.setStockSaleDays(0);
                } else {
                    // 库存可销售天数 库存/7天发货平均值 只取整数
                    int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                    obj.setStockSaleDays(stockSaleDays);
                }
            } else {
                obj.setStockSaleDays(0);
            }
        } else {
            obj.setStockSaleDays(0);
        }

        // 获取利润计算值Bi 补充所需字段 复用代码
        McProductSales mcProductSales = productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("display_product_code", obj.getSellerSku())
                .andEq("site", obj.getSite())
                .single();
        List<BiAmazonProfitCalculationVo> biBody = new ArrayList<>();
        BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
        bi.setSite(obj.getSite());
        // 美国站点 需传开始结束时间
        if ("Amazon.com".equals(obj.getSite())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String date = sdf.format(cloneVo.getPlanStartDate());
            bi.setDate(date);
        }
        bi.setPrice(obj.getDealPrice());
        bi.setDisplayProductCode(obj.getSellerSku());
        bi.setProductMainCode(mcProductSales.getProductMainCode());
        bi.setPromotionType("DOTD");
        biBody.add(bi);
        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x2)
        );
        BigDecimal dealPriceGross = BigDecimal.ZERO; // 秒杀价毛利率
        BigDecimal dealBurstCoefficient = BigDecimal.ZERO; // 活动预计爆发系数
        BigDecimal dmsLast30day = BigDecimal.ZERO; // 近三十天DMS
        BigDecimal categoryGross = BigDecimal.ZERO; // 三级分类近四周毛利率
        String key = obj.getSite() + obj.getSellerSku();
        if (biMap.containsKey(key)) {
            dealPriceGross = biMap.get(key).getGross();
            if (dealPriceGross != null && dealPriceGross.compareTo(BigDecimal.valueOf(-9999)) != 0) {
                dealPriceGross = dealPriceGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            }
            dealBurstCoefficient = biMap.get(key).getDealBurstCoefficient();
            dealBurstCoefficient = dealBurstCoefficient != null ? dealBurstCoefficient.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            dmsLast30day = biMap.get(key).getDmsLast30day();
            dmsLast30day = dmsLast30day != null ? dmsLast30day.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            categoryGross = biMap.get(key).getCategoryGross();
            categoryGross = categoryGross != null ? categoryGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        }
        obj.setDealPriceGross(dealPriceGross);
        obj.setDealBurstCoefficient(dealBurstCoefficient);
        obj.setDmsLast30day(dmsLast30day);
        obj.setCategoryGross(categoryGross);
        // 三值计算 初始化原始值
        dealPriceGross = obj.getDealPriceGross().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 秒杀价毛利率
        dmsLast30day = obj.getDmsLast30day(); // 近三十天DMS
        dealBurstCoefficient = obj.getDealBurstCoefficient().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 活动预计爆发系数
        BigDecimal dealPrice = obj.getDealPrice(); // 秒杀价
        // 活动天数
        Date planStartDate = obj.getPlanStartDate(); // 活动开始时间
        Date planEndDate = obj.getPlanEndDate(); // 活动结束时间
        long daysBetween = DateUtil.betweenDay(planStartDate, planEndDate, true);
        daysBetween = daysBetween == 0 ? 1 : daysBetween + 1;
        // 活动预计销量 = 活动预计爆发系数 * 近三十天DMS * 活动天数
        BigDecimal expectedSalesVolume = dealBurstCoefficient.multiply(dmsLast30day).multiply(new BigDecimal(daysBetween)).setScale(2, RoundingMode.HALF_UP);
        obj.setExpectedSalesVolume(expectedSalesVolume);
        // 活动预计销售额 = 活动预计销量 * 秒杀价
        BigDecimal estimateOfSales = expectedSalesVolume.multiply(dealPrice).setScale(2, RoundingMode.HALF_UP);
        obj.setEstimateOfSales(estimateOfSales);
        // 活动预计毛利额 = 活动预计销售额 * 秒杀价毛利率
        BigDecimal expectedGrossProfitMargin = estimateOfSales.multiply(dealPriceGross).setScale(2, RoundingMode.HALF_UP);
        // 数值过大 强制转换
        if (expectedGrossProfitMargin.toPlainString().replace("-", "").replace(".", "").length() > 8) {
            expectedGrossProfitMargin = BigDecimal.valueOf(-9999);
        }
        obj.setExpectedGrossProfitMargin(expectedGrossProfitMargin);
        // 确定审批角色
        SomDealOfTheDayVo somDealOfTheDayVo = new SomDealOfTheDayVo();
        BeanUtils.copyProperties(obj, somDealOfTheDayVo);
        obj.setApprovalRole(initApprovalRole(somDealOfTheDayVo));

        somDealOfTheDayMapper.insert(obj);
    }

    /**
     * summited
     * 标记为已提报
     *
     * @param summitedVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void summited(SomDealOfTheDayExtVo summitedVo) throws ValidateException {
        if (ObjectUtil.isEmpty(summitedVo) || CollectionUtil.isEmpty(summitedVo.getAidList())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<SomDealOfTheDay> dealList = somDealOfTheDayMapper.createLambdaQuery().andIn("aid", summitedVo.getAidList()).andEq("status", 21).select();
        if (summitedVo.getAidList().size() != dealList.size()) {
            throw new ValidateException("存在不同状态的数据,请检查");
        }
        for (SomDealOfTheDay deal : dealList) {
            deal.setStatus(110);
        }
        somDealOfTheDayMapper.batchUpdate(dealList);
    }

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    public String batchCancel(List<SomDealOfTheDayImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder(); // 返回的错误信息
        List<SomDealOfTheDay> updateList = new ArrayList<>(); // 需要更新的数据列
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (SomDealOfTheDayImportVo importVo : list) {
            if (ObjectUtil.isEmpty(importVo) || ObjectUtil.isEmpty(importVo.getSite()) || ObjectUtil.isEmpty(importVo.getSellerSku())) {
                throw new ValidateException("参数存在空值,请检查");
            }

            List<SomDealOfTheDay> deals = somDealOfTheDayMapper
                    .createLambdaQuery()
                    .andEq("site", importVo.getSite())
                    .andEq("seller_sku", importVo.getSellerSku())
                    .andGreatEq("plan_start_date", LocalDateTime.parse(importVo.getPlanStartDate() + " 00:00:00", formatter))
                    .andLessEq("plan_end_date", LocalDateTime.parse(importVo.getPlanEndDate() + " 23:59:59", formatter))
                    .andEq("status", 40)
                    .select();

            if (ObjectUtil.isEmpty(deals)) {
                resultStr
                        .append("站点:")
                        .append(importVo.getSite())
                        .append(" — 展示码:")
                        .append(importVo.getSellerSku())
                        .append(" — 查询数据不存在")
                        .append("\n");
            } else {
                for (SomDealOfTheDay deal : deals) {
                    // 状态
                    deal.setStatus(90);
                    updateList.add(deal);
                }
            }
        }

        if (StrUtil.isNotBlank(resultStr)) {
            return resultStr.toString();
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            somDealOfTheDayMapper.batchCancel(updateList);
        }

        return Strings.EMPTY;
    }

    /**
     * batchFeedbackResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    public String batchFeedbackCancelResult(List<SomDealOfTheDayImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder(); // 返回的错误信息
        List<SomDealOfTheDay> updateList = new ArrayList<>(); // 需要更新的数据列
        List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "PDDStatus").select(); // 字典值
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (SomDealOfTheDayImportVo importVo : list) {
            if (ObjectUtil.isEmpty(importVo) || ObjectUtil.isEmpty(importVo.getSite()) || ObjectUtil.isEmpty(importVo.getSellerSku())) {
                throw new ValidateException("参数存在空值,请检查");
            }

            List<SomDealOfTheDay> deals = somDealOfTheDayMapper
                    .createLambdaQuery()
                    .andEq("site", importVo.getSite())
                    .andEq("seller_sku", importVo.getSellerSku())
                    .andGreatEq("plan_start_date", LocalDateTime.parse(importVo.getPlanStartDate() + " 00:00:00", formatter))
                    .andLessEq("plan_end_date", LocalDateTime.parse(importVo.getPlanEndDate() + " 23:59:59", formatter))
                    .andEq("status", 90)
                    .select();

            if (ObjectUtil.isEmpty(deals)) {
                resultStr
                        .append("站点:")
                        .append(importVo.getSite())
                        .append(" — 展示码:")
                        .append(importVo.getSellerSku())
                        .append(" — 查询数据不存在")
                        .append("\n");
            } else {
                for (SomDealOfTheDay deal : deals) {
                    // 状态
                    mcDictionaryInfos.stream()
                            .filter(e -> e.getItemLable().equals(importVo.getStatusName()))
                            .findFirst().ifPresent(ps -> {
                                deal.setStatus(Integer.parseInt(ps.getItemValue()));
                            });
                    deal.setCacenlFailureRemark(importVo.getRemark()); // 取消失败原因
                    updateList.add(deal);
                }
            }
        }

        if (StrUtil.isNotBlank(resultStr)) {
            return resultStr.toString();
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            somDealOfTheDayMapper.batchFeedbackCancelResult(updateList);
        }

        return Strings.EMPTY;
    }

    /**
     * 批量编辑
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登陆用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importBatchEdit(List<SomDealOfTheDayBatchEditImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkBatchEditImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        List<SomDealOfTheDay> updateSomDealOfTheDays = new ArrayList<>();
        for (SomDealOfTheDayBatchEditImportVo importVo : importVos) {
            SomDealOfTheDay somDealOfTheDay = new SomDealOfTheDay();
            somDealOfTheDay.setAid(importVo.getAid());
            // 修改状态：修改中
            somDealOfTheDay.setModifyStatus(10);
            somDealOfTheDay.setDealPrice(importVo.getDealPrice());
            somDealOfTheDay.setModifyName(tokenUser.getUserName());
            somDealOfTheDay.setModifyNum(tokenUser.getJobNumber());
            somDealOfTheDay.setModifyTime(DateTime.now().toJdkDate());
            updateSomDealOfTheDays.add(somDealOfTheDay);
        }
        somDealOfTheDayMapper.batchImportEdit(updateSomDealOfTheDays);
    }

    /**
     * 反馈修改结果
     *
     * @param somDealOfTheDayVo 营销活动
     * @param tokenUser 当前登陆用户
     */
    public void feedbackModifyResult(SomDealOfTheDayVo somDealOfTheDayVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealOfTheDayVo)) {
            throw new ValidateException("参数存在空值,请检查！");
        }
        String aid = somDealOfTheDayVo.getAid();
        Integer modifyStatus = somDealOfTheDayVo.getModifyStatus();
        if (StrUtil.isEmpty(aid) || modifyStatus == null) {
            throw new ValidateException("参数存在空值,请检查！");
        }
        // 查询字典获取传参是否正确
        List<McDictionaryInfo> pddModifyStatusDictList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "PDDModifyStatus").select();
        Map<String, McDictionaryInfo> pddModifyStatusDictMap = pddModifyStatusDictList.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (v1, v2) -> v1));
        McDictionaryInfo pddModifyStatusDict = pddModifyStatusDictMap.get(String.valueOf(modifyStatus));
        // 核验修改状态，itemValue1=1 => 正确，itemValue2=1 => 需要填写修改原因
        String dictNeedCheck = "1";
        if (pddModifyStatusDict == null || !dictNeedCheck.equals(pddModifyStatusDict.getItemValue1())) {
            throw new ValidateException("修改状态有误！");
        }
        if (dictNeedCheck.equals(pddModifyStatusDict.getItemValue2()) && StrUtil.isEmpty(somDealOfTheDayVo.getModifyFailureRemark())) {
            throw new ValidateException("失败原因不能为空！");
        }
        SomDealOfTheDay somDealOfTheDay = somDealOfTheDayMapper.createLambdaQuery().andEq("aid", aid).andEq("modify_status", 10).single();
        if (somDealOfTheDay == null) {
            throw new ValidateException("当前状态不允许反馈修改结果！");
        }
        somDealOfTheDay.setModifyStatus(modifyStatus);
        handleStatusByModifyStatus(somDealOfTheDay);
        // itemValue2 != 1 => 修改原因置为空
        somDealOfTheDay.setModifyFailureRemark(dictNeedCheck.equals(pddModifyStatusDict.getItemValue2()) ? somDealOfTheDayVo.getModifyFailureRemark() : null);
        somDealOfTheDay.setModifyName(tokenUser.getUserName());
        somDealOfTheDay.setModifyNum(tokenUser.getJobNumber());
        somDealOfTheDay.setModifyTime(DateTime.now().toJdkDate());
        somDealOfTheDayMapper.batchImportFeedbackEditResult(Collections.singletonList(somDealOfTheDay));
    }

    /**
     * 导入批量反馈修改结果
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importBatchFeedbackEditResult(List<SomDealOfTheDayBatchFeedbackEditResultImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkBatchFeedbackEditResultData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：更新数据
        List<SomDealOfTheDay> updateSomDealOfTheDays = new ArrayList<>();
        for (SomDealOfTheDayBatchFeedbackEditResultImportVo importVo : importVos) {
            SomDealOfTheDay somDealOfTheDay = new SomDealOfTheDay();
            somDealOfTheDay.setAid(importVo.getAid());
            somDealOfTheDay.setStatus(importVo.getStatus());
            somDealOfTheDay.setModifyStatus(importVo.getModifyStatus());
            somDealOfTheDay.setModifyFailureRemark(importVo.getModifyFailureRemark());
            somDealOfTheDay.setModifyName(tokenUser.getUserName());
            somDealOfTheDay.setModifyNum(tokenUser.getJobNumber());
            somDealOfTheDay.setModifyTime(DateTime.now().toJdkDate());
            updateSomDealOfTheDays.add(somDealOfTheDay);
        }
        somDealOfTheDayMapper.batchImportFeedbackEditResult(updateSomDealOfTheDays);
    }

    /**
     * 核验批量反馈修改结果行数据
     *
     * @param importVos 导入行数据
     * @return 错误信息列表
     */
    private List<String> checkBatchFeedbackEditResultData(List<SomDealOfTheDayBatchFeedbackEditResultImportVo> importVos) {
        // 查询 状态=41(需要关注) 修改状态=10(修改中)的数据
        List<SomDealOfTheDay> somDealOfTheDays = somDealOfTheDayMapper.createLambdaQuery().andEq("status", 41).andEq("modify_status", 10).select();
        Map<String, List<SomDealOfTheDay>> somDealOfTheDayMap = somDealOfTheDays.stream().collect(Collectors.groupingBy(x -> StrUtil.concat(true, x.getSite(), "_", x.getSellerSku())));
        // 查询字典
        List<McDictionaryInfo> pddModifyStatusDictList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "PDDModifyStatus").select();
        Map<String, McDictionaryInfo> pddModifyStatusMap = pddModifyStatusDictList.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (v1, v2) -> v1));
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomDealOfTheDayBatchFeedbackEditResultImportVo importVo : importVos) {
            // 校验必填字段
            String site = importVo.getSite();
            String sellerSku = importVo.getSellerSku();
            Date planStartDate = importVo.getPlanStartDate();
            Date planEndDate = importVo.getPlanEndDate();
            String modifyStatusStr = importVo.getModifyStatusStr();
            if ((!StrUtil.isAllNotEmpty(site, sellerSku, modifyStatusStr)) || planStartDate == null || planEndDate == null) {
                errors.add("错误0：站点、展示码、活动起始时间、活动截止时间、修改状态不能为空！");
                continue;
            }
            String planStartDateStr = DateUtil.format(planStartDate, "yyyy-MM-dd");
            String planEndDateStr = DateUtil.format(planEndDate, "yyyy-MM-dd");
            String allErrorFieldMsg = StrUtil.concat(true, "站点[", site, "]展示码[", sellerSku, "]活动起始时间[", planStartDateStr, "]活动截止时间[", planEndDateStr, "]");
            // 核验数据重复
            String key = StrUtil.concat(true, site, sellerSku, planStartDateStr, planEndDateStr);
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, "错误1：", allErrorFieldMsg, "数据重复！"));
                continue;
            }
            repeatCheckSet.add(key);
            // 核验修改状态，itemValue1=1 => 正确，itemValue2=1 => 需要填写修改原因
            String dictNeedCheck = "1";
            McDictionaryInfo pddModifyStatusDict = pddModifyStatusMap.get(modifyStatusStr);
            if (pddModifyStatusDict == null || !dictNeedCheck.equals(pddModifyStatusDict.getItemValue1())) {
                errors.add(StrUtil.concat(true, "错误2：修改状态[", modifyStatusStr, "]有误！"));
                continue;
            }
            importVo.setModifyStatus(Integer.valueOf(pddModifyStatusDict.getItemValue()));
            if (dictNeedCheck.equals(pddModifyStatusDict.getItemValue2()) && StrUtil.isEmpty(importVo.getModifyFailureRemark())) {
                errors.add(StrUtil.concat(true, "错误3：修改状态为[", modifyStatusStr, "]时，修改原因不能为空！"));
                continue;
            }
            // 如果不需要填写失败原因，填了那么就置为空
            importVo.setModifyFailureRemark(dictNeedCheck.equals(pddModifyStatusDict.getItemValue2()) ? importVo.getModifyFailureRemark() : null);
            // 核验数据是否存在
            String siteSellerSkuKey = StrUtil.concat(true, site, "_", sellerSku);
            if (!somDealOfTheDayMap.containsKey(siteSellerSkuKey)) {
                errors.add(StrUtil.concat(true, "错误4：", allErrorFieldMsg, "当前状态不允许反馈修改结果！"));
                continue;
            }
            // 活动起始时间（转变为一天的开始时间）、活动截止时间格式（转变为一天的结束时间，注意数据库存储的是 23:59:00）
            importVo.setPlanStartDate(DateUtil.beginOfDay(planStartDate));
            importVo.setPlanEndDate(DateUtil.offsetSecond(DateUtil.endOfDay(planEndDate), -59));
            // 这里以秒的形式进行比较
            List<SomDealOfTheDay> somPddAndOdsList = somDealOfTheDayMap.get(siteSellerSkuKey);
            SomDealOfTheDay somDealOfTheDay = somPddAndOdsList.stream()
                    .filter(x -> x.getPlanStartDate() != null && x.getPlanEndDate() != null)
                    .filter(x -> DateUtil.beginOfSecond(x.getPlanStartDate()).compareTo(DateUtil.beginOfSecond(importVo.getPlanStartDate())) == 0 &&
                            DateUtil.endOfSecond(x.getPlanEndDate()).compareTo(DateUtil.endOfSecond(importVo.getPlanEndDate())) == 0)
                    .findFirst()
                    .orElse(null);
            if (somDealOfTheDay == null) {
                errors.add(StrUtil.concat(true, "错误4：", allErrorFieldMsg, "当前状态不允许反馈修改结果！"));
                continue;
            }
            importVo.setAid(somDealOfTheDay.getAid());
            // 变更活动状态
            somDealOfTheDay.setModifyStatus(importVo.getModifyStatus());
            handleStatusByModifyStatus(somDealOfTheDay);
            importVo.setStatus(somDealOfTheDay.getStatus());
        }
        return errors;
    }

    /**
     * 通过【修改状态】，计算【活动状态】的变更
     *      修改成功时，需要变更【活动状态】
     *      非修改成功时，不需要变更【活动状态】
     *
     * @param somDealOfTheDay 营销活动
     */
    private void handleStatusByModifyStatus(SomDealOfTheDay somDealOfTheDay) {
        Integer successModifyStatus = 20;
        if (!successModifyStatus.equals(somDealOfTheDay.getModifyStatus())) {
            return;
        }
        DateTime now = DateTime.now();
        if (ObjectUtil.isAllNotEmpty(somDealOfTheDay.getRealStartDate(), somDealOfTheDay.getRealEndDate())) {
            //  实际截止时间 > 当前时间 > 实际开始时间，活动状态 => 70(进行中)，否则 => 110(运营已提报)
            Integer status = now.after(somDealOfTheDay.getRealStartDate()) && now.before(somDealOfTheDay.getRealEndDate()) ? 70 : 110;
            somDealOfTheDay.setStatus(status);
        } else {
            // 计划截止时间 > 当前时间 > 计划开始时间，活动状态 => 70(进行中)，否则 => 110(运营已提报)
            if (ObjectUtil.isAllNotEmpty(somDealOfTheDay.getPlanStartDate(), somDealOfTheDay.getPlanEndDate())) {
                Integer status = now.after(somDealOfTheDay.getPlanStartDate()) && now.before(somDealOfTheDay.getPlanEndDate()) ? 70 : 110;
                somDealOfTheDay.setStatus(status);
            }
        }
    }

    /**
     * 核验批量编辑行数据
     *
     * @param importVos 导入行记录
     * @return 错误信息列表
     */
    private List<String> checkBatchEditImportData(List<SomDealOfTheDayBatchEditImportVo> importVos) {
        // 查询 活动状态=41（需要关注）的数据
        List<SomDealOfTheDay> somDealOfTheDays = somDealOfTheDayMapper.createLambdaQuery().andEq("status", 41).select();
        Map<String, List<SomDealOfTheDay>> somDealOfTheDayMap = somDealOfTheDays.stream()
                .collect(Collectors.groupingBy(x -> StrUtil.concat(true, x.getSite(), "_", x.getSellerSku())));
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomDealOfTheDayBatchEditImportVo importVo : importVos) {
            // 校验必填字段
            String site = importVo.getSite();
            String sellerSku = importVo.getSellerSku();
            Date planStartDate = importVo.getPlanStartDate();
            Date planEndDate = importVo.getPlanEndDate();
            String dealPriceStr = importVo.getDealPriceStr();
            if ((!StrUtil.isAllNotEmpty(site, sellerSku, dealPriceStr)) || planStartDate == null || planEndDate == null) {
                errors.add("错误0：站点、展示码、活动起始时间、活动截止时间、秒杀价不能为空！");
                continue;
            }
            String planStartDateStr = DateUtil.format(planStartDate, "yyyy-MM-dd");
            String planEndDateStr = DateUtil.format(planEndDate, "yyyy-MM-dd");
            String allErrorFieldMsg = StrUtil.concat(true, "站点[", site, "]展示码[", sellerSku, "]活动起始时间[", planStartDateStr, "]活动截止时间[", planEndDateStr, "]");
            // 核验数据重复
            String key = StrUtil.concat(true, site, sellerSku, planStartDateStr, planEndDateStr);
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, "错误1：", allErrorFieldMsg, "数据重复！"));
                continue;
            }
            repeatCheckSet.add(key);
            // 核验秒杀价
            try {
                importVo.setDealPrice(new BigDecimal(importVo.getDealPriceStr()));
            } catch (Exception e) {
                errors.add(StrUtil.concat(true, "错误2：秒杀价[", dealPriceStr, "]格式有误！"));
                continue;
            }
            // 核验数据是否存在
            String siteSellerSkuKey = StrUtil.concat(true, site, "_", sellerSku);
            if (!somDealOfTheDayMap.containsKey(siteSellerSkuKey)) {
                errors.add(StrUtil.concat(true, "错误3：", allErrorFieldMsg, "非需要关注状态，不允许编辑！"));
                continue;
            }
            // 活动起始时间（转变为一天的开始时间）、活动截止时间格式（转变为一天的结束时间，注意数据库存储的是 23:59:00）
            importVo.setPlanStartDate(DateUtil.beginOfDay(planStartDate));
            importVo.setPlanEndDate(DateUtil.offsetSecond(DateUtil.endOfDay(planEndDate), -59));
            // 这里以秒的形式进行比较
            List<SomDealOfTheDay> somDealOfTheDaysList = somDealOfTheDayMap.get(siteSellerSkuKey);
            SomDealOfTheDay somDealOfTheDay = somDealOfTheDaysList.stream()
                    .filter(x -> x.getPlanStartDate() != null && x.getPlanEndDate() != null)
                    .filter(x -> DateUtil.beginOfSecond(x.getPlanStartDate()).compareTo(DateUtil.beginOfSecond(importVo.getPlanStartDate())) == 0 &&
                            DateUtil.endOfSecond(x.getPlanEndDate()).compareTo(DateUtil.endOfSecond(importVo.getPlanEndDate())) == 0)
                    .findFirst()
                    .orElse(null);
            if (somDealOfTheDay == null) {
                errors.add(StrUtil.concat(true, "错误3：", allErrorFieldMsg, "非需要关注状态，不允许编辑！"));
                continue;
            }
            importVo.setAid(somDealOfTheDay.getAid());
        }
        return errors;
    }
}