package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 目标价历史表
* gen by 代码生成器 2021-11-03
*/

@Table(name="mc.mc_target_price_history")
public class McTargetPriceHistory implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 目标价
	 */
	@Column("target_price")
	private BigDecimal targetPrice ;
	/**
	 * 币种
	 */
	@Column("currency_code")
	private String currencyCode ;
	/**
	 * 预估毛利率百分比
	 */
	@Column("estimated_gross_margin")
	private BigDecimal estimatedGrossMargin ;
	/**
	 * 审核负责人工号
	 */
	@Column("audit_director_code")
	private String auditDirectorCode ;
	/**
	 * 审核负责人姓名
	 */
	@Column("audit_director_name")
	private String auditDirectorName ;
	/**
	 * 审核时间
	 */
	@Column("audit_time")
	private Date auditTime ;

	public McTargetPriceHistory() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}

	public BigDecimal getTargetPrice() {
		return targetPrice;
	}

	public void setTargetPrice(BigDecimal targetPrice) {
		this.targetPrice = targetPrice;
	}

	/**
	* 币种
	*@return
	*/
	public String getCurrencyCode(){
		return  currencyCode;
	}
	/**
	* 币种
	*@param  currencyCode
	*/
	public void setCurrencyCode(String currencyCode ){
		this.currencyCode = currencyCode;
	}
	/**
	* 预估毛利率百分比
	*@return
	*/
	public BigDecimal getEstimatedGrossMargin(){
		return  estimatedGrossMargin;
	}
	/**
	* 预估毛利率百分比
	*@param  estimatedGrossMargin
	*/
	public void setEstimatedGrossMargin(BigDecimal estimatedGrossMargin ){
		this.estimatedGrossMargin = estimatedGrossMargin;
	}
	/**
	* 审核负责人工号
	*@return
	*/
	public String getAuditDirectorCode(){
		return  auditDirectorCode;
	}
	/**
	* 审核负责人工号
	*@param  auditDirectorCode
	*/
	public void setAuditDirectorCode(String auditDirectorCode ){
		this.auditDirectorCode = auditDirectorCode;
	}
	/**
	* 审核负责人姓名
	*@return
	*/
	public String getAuditDirectorName(){
		return  auditDirectorName;
	}
	/**
	* 审核负责人姓名
	*@param  auditDirectorName
	*/
	public void setAuditDirectorName(String auditDirectorName ){
		this.auditDirectorName = auditDirectorName;
	}
	/**
	* 审核时间
	*@return
	*/
	public Date getAuditTime(){
		return  auditTime;
	}
	/**
	* 审核时间
	*@param  auditTime
	*/
	public void setAuditTime(Date auditTime ){
		this.auditTime = auditTime;
	}

}
