package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Temu跨境sku
 * gen by 代码生成器 2025-06-26
 */

@Table(name = "mc.som_temu_cross_sku")
public class SomTemuCrossSku implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 账号(冗余)
     */
    @Column("account_tag")
    private String accountTag;
    /**
     * 店铺ID(冗余)
     */
    @Column("account_id")
    private String accountId;
    /**
     * 商品ID
     */
    @Column("product_id")
    private Long productId;
    /**
     * 商品名称(冗余)
     */
    @Column("product_name")
    private String productName;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * Temu站点ID
     */
    @Column("site_id")
    private String siteId;
    /**
     * Temu站点名称
     */
    @Column("site_name")
    private String siteName;
    /**
     * 货品skuId
     */
    @Column("product_sku_id")
    private Long productSkuId;
    /**
     * 展示码(sku货号)
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 货品sku仓配侧扩展属性
     */
    @Column("product_sku_wh_ext_attr")
    private Object productSkuWhExtAttr;
    /**
     * 虚拟库存
     */
    @Column("virtual_stock")
    private Integer virtualStock;
    /**
     * 规格列表
     */
    @Column("product_sku_spec_list")
    private Object productSkuSpecList;
    /**
     * 货品sku销售域扩展属性
     */
    @Column("product_sku_sale_ext_attr")
    private Object productSkuSaleExtAttr;
    /**
     * 调用接口下载的时间
     */
    @Column("download_time")
    private Date downloadTime;
    /**
     * 主表ID
     */
    @Column("goods_id")
    private String goodsId;

    /**
     * SKC ID
     */
    @Column("product_skc_id")
    private Long productSkcId;

    public SomTemuCrossSku() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 账号(冗余)
     *
     * @return
     */
    public String getAccountTag() {
        return accountTag;
    }

    /**
     * 账号(冗余)
     *
     * @param accountTag
     */
    public void setAccountTag(String accountTag) {
        this.accountTag = accountTag;
    }

    /**
     * 店铺ID(冗余)
     *
     * @return
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 店铺ID(冗余)
     *
     * @param accountId
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 商品ID
     *
     * @return
     */
    public Long getProductId() {
        return productId;
    }

    /**
     * 商品ID
     *
     * @param productId
     */
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    /**
     * 商品名称(冗余)
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 商品名称(冗余)
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * Temu站点ID
     *
     * @return
     */
    public String getSiteId() {
        return siteId;
    }

    /**
     * Temu站点ID
     *
     * @param siteId
     */
    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    /**
     * Temu站点名称
     *
     * @return
     */
    public String getSiteName() {
        return siteName;
    }

    /**
     * Temu站点名称
     *
     * @param siteName
     */
    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    /**
     * 货品skuId
     *
     * @return
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 货品skuId
     *
     * @param productSkuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 展示码(sku货号)
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码(sku货号)
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 货品sku仓配侧扩展属性
     *
     * @return
     */
    public Object getProductSkuWhExtAttr() {
        return productSkuWhExtAttr;
    }

    /**
     * 货品sku仓配侧扩展属性
     *
     * @param productSkuWhExtAttr
     */
    public void setProductSkuWhExtAttr(Object productSkuWhExtAttr) {
        this.productSkuWhExtAttr = productSkuWhExtAttr;
    }

    /**
     * 虚拟库存
     *
     * @return
     */
    public Integer getVirtualStock() {
        return virtualStock;
    }

    /**
     * 虚拟库存
     *
     * @param virtualStock
     */
    public void setVirtualStock(Integer virtualStock) {
        this.virtualStock = virtualStock;
    }

    /**
     * 规格列表
     *
     * @return
     */
    public Object getProductSkuSpecList() {
        return productSkuSpecList;
    }

    /**
     * 规格列表
     *
     * @param productSkuSpecList
     */
    public void setProductSkuSpecList(Object productSkuSpecList) {
        this.productSkuSpecList = productSkuSpecList;
    }

    /**
     * 货品sku销售域扩展属性
     *
     * @return
     */
    public Object getProductSkuSaleExtAttr() {
        return productSkuSaleExtAttr;
    }

    /**
     * 货品sku销售域扩展属性
     *
     * @param productSkuSaleExtAttr
     */
    public void setProductSkuSaleExtAttr(Object productSkuSaleExtAttr) {
        this.productSkuSaleExtAttr = productSkuSaleExtAttr;
    }

    /**
     * 调用接口下载的时间
     *
     * @return
     */
    public Date getDownloadTime() {
        return downloadTime;
    }

    /**
     * 调用接口下载的时间
     *
     * @param downloadTime
     */
    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public Long getProductSkcId() {
        return productSkcId;
    }

    public void setProductSkcId(Long productSkcId) {
        this.productSkcId = productSkcId;
    }
}
