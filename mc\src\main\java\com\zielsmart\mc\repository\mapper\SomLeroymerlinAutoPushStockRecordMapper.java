package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomLeroymerlinAutoPushStockRecord;
import com.zielsmart.mc.vo.SomLeroymerlinAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomLeroymerlinAutoPushStockRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-09
*/

@SqlResource("somLeroymerlinAutoPushStockRecord")
public interface SomLeroymerlinAutoPushStockRecordMapper extends BaseMapper<SomLeroymerlinAutoPushStockRecord> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomLeroymerlinAutoPushStockRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomLeroymerlinAutoPushStockRecordVo> queryByPage(@Param("searchVo") SomLeroymerlinAutoPushStockRecordPageSearchVo searchVo, PageRequest pageRequest);

    List<SomLeroymerlinAutoPushStockRecordVo> exportExcel(@Param("searchVo")SomLeroymerlinAutoPushStockRecordPageSearchVo searchVo);
}
