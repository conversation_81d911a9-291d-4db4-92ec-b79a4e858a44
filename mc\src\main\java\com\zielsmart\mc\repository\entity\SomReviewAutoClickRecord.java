package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 邀评记录表
* gen by 代码生成器 2022-04-18
*/

@Table(name="mc.som_review_auto_click_record")
public class SomReviewAutoClickRecord implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 订单编号
	 */
	@Column("orderid")
	private String orderid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 10.AFN,20.MFN
	 */
	@Column("fulfillment_type")
	private String fulfillmentType ;
	/**
	 * 展示码/Amazon平台sku
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 发票创建时间
	 */
	@Column("invoice_create_date")
	private String invoiceCreateDate ;
	/**
	 * 该展示码当天的订单数量
	 */
	@Column("daily_order_quantity")
	private Integer dailyOrderQuantity ;
	/**
	 * 当天需要review的次数
	 */
	@Column("daily_click_count")
	private Integer dailyClickCount ;
	/**
	 * 当期设置的提升留评率
	 */
	@Column("now_retentionl_rate")
	private BigDecimal nowRetentionlRate ;
	/**
	 * 当期设置的邮件来评率
	 */
	@Column("now_email_to_rate")
	private BigDecimal nowEmailToRate ;
	/**
	 * 10.未推送,20.推送成功,99.推送失败
	 */
	@Column("push_status")
	private Integer pushStatus ;
	/**
	 * 推送日期
	 */
	@Column("push_date")
	private Date pushDate ;
	/**
	 * 推送失败原因
	 */
	@Column("error_msg")
	private String errorMsg ;

	public SomReviewAutoClickRecord() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 订单编号
	*@return
	*/
	public String getOrderid(){
		return  orderid;
	}
	/**
	* 订单编号
	*@param  orderid
	*/
	public void setOrderid(String orderid ){
		this.orderid = orderid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 10.AFN,20.MFN
	*@return
	*/
	public String getFulfillmentType(){
		return  fulfillmentType;
	}
	/**
	* 10.AFN,20.MFN
	*@param  fulfillmentType
	*/
	public void setFulfillmentType(String fulfillmentType ){
		this.fulfillmentType = fulfillmentType;
	}
	/**
	* 展示码/Amazon平台sku
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码/Amazon平台sku
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 发票创建时间
	*@return
	*/
	public String getInvoiceCreateDate(){
		return  invoiceCreateDate;
	}
	/**
	* 发票创建时间
	*@param  invoiceCreateDate
	*/
	public void setInvoiceCreateDate(String invoiceCreateDate ){
		this.invoiceCreateDate = invoiceCreateDate;
	}
	/**
	* 该展示码当天的订单数量
	*@return
	*/
	public Integer getDailyOrderQuantity(){
		return  dailyOrderQuantity;
	}
	/**
	* 该展示码当天的订单数量
	*@param  dailyOrderQuantity
	*/
	public void setDailyOrderQuantity(Integer dailyOrderQuantity ){
		this.dailyOrderQuantity = dailyOrderQuantity;
	}
	/**
	* 当天需要review的次数
	*@return
	*/
	public Integer getDailyClickCount(){
		return  dailyClickCount;
	}
	/**
	* 当天需要review的次数
	*@param  dailyClickCount
	*/
	public void setDailyClickCount(Integer dailyClickCount ){
		this.dailyClickCount = dailyClickCount;
	}
	/**
	* 当期设置的提升留评率
	*@return
	*/
	public BigDecimal getNowRetentionlRate(){
		return  nowRetentionlRate;
	}
	/**
	* 当期设置的提升留评率
	*@param  nowRetentionlRate
	*/
	public void setNowRetentionlRate(BigDecimal nowRetentionlRate ){
		this.nowRetentionlRate = nowRetentionlRate;
	}
	/**
	* 当期设置的邮件来评率
	*@return
	*/
	public BigDecimal getNowEmailToRate(){
		return  nowEmailToRate;
	}
	/**
	* 当期设置的邮件来评率
	*@param  nowEmailToRate
	*/
	public void setNowEmailToRate(BigDecimal nowEmailToRate ){
		this.nowEmailToRate = nowEmailToRate;
	}
	/**
	* 10.未推送,20.推送成功,99.推送失败
	*@return
	*/
	public Integer getPushStatus(){
		return  pushStatus;
	}
	/**
	* 10.未推送,20.推送成功,99.推送失败
	*@param  pushStatus
	*/
	public void setPushStatus(Integer pushStatus ){
		this.pushStatus = pushStatus;
	}
	/**
	* 推送日期
	*@return
	*/
	public Date getPushDate(){
		return  pushDate;
	}
	/**
	* 推送日期
	*@param  pushDate
	*/
	public void setPushDate(Date pushDate ){
		this.pushDate = pushDate;
	}
	/**
	* 推送失败原因
	*@return
	*/
	public String getErrorMsg(){
		return  errorMsg;
	}
	/**
	* 推送失败原因
	*@param  errorMsg
	*/
	public void setErrorMsg(String errorMsg ){
		this.errorMsg = errorMsg;
	}

}
