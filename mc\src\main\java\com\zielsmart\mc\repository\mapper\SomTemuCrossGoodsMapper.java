package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuCrossGoods;
import com.zielsmart.mc.vo.SomTemuCrossGoodsPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossGoodsVo;
import com.zielsmart.mc.vo.SomTemuListingReport;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description SomTemuCrossGoodsMapper
 * @date 2025-06-26 15:52:12
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuCrossGoods")
public interface SomTemuCrossGoodsMapper extends BaseMapper<SomTemuCrossGoods> {

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return PageResult<SomTemuCrossGoodsVo>
     */
    PageResult<SomTemuCrossGoodsVo> queryByPage(@Param("searchVo") SomTemuCrossGoodsPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 获取导出URL链接的主表ID
     *
     * @param searchVo 入参
     * @return List<String>
     */
    List<String> getUrlExportGoodsIds(@Param("searchVo") SomTemuCrossGoodsPageSearchVo searchVo);

    PageResult<SomTemuListingReport> stockReport(@Param("searchVo")SomTemuCrossGoodsPageSearchVo searchVo, PageRequest pageRequest);
}
