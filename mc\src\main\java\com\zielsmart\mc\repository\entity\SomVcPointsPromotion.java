package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* VC Points promotion
* gen by 代码生成器 2025-05-12
*/

@Table(name="mc.som_vc_points_promotion")
public class SomVcPointsPromotion implements java.io.Serializable {
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 账号名称/大分类
	 */
	@Column("account_name")
	private String accountName ;
	/**
	 * 供应商编码
	 */
	@Column("vendor_code")
	private String vendorCode ;
	/**
	 * 营销活动类型
	 */
	@Column("promotion_type")
	private Integer promotionType ;
	/**
	 * 促销活动ID
	 */
	@Column("promotion_id")
	private Long promotionId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 产品SKU编码
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 建议零售价，RRP
	 */
	@Column("recommended_retail_price")
	private BigDecimal recommendedRetailPrice ;
	/**
	 * 供货价
	 */
	@Column("cost_price")
	private BigDecimal costPrice ;
	/**
	 * 积分百分比
	 */
	@Column("discount")
	private BigDecimal discount ;
	/**
	 * 单个产品预估积分
	 */
	@Column("per_unit_funding")
	private BigDecimal perUnitFunding ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 评分
	 */
	@Column("rating")
	private BigDecimal rating ;
	/**
	 * 库存可销售天数
	 */
	@Column("stock_sale_days")
	private Integer stockSaleDays ;
	/**
	 * 30天dms
	 */
	@Column("month_dms")
	private BigDecimal monthDms ;
	/**
	 * 起始日期，格式：yyyy-MM-dd
	 */
	@Column("start_date")
	private Date startDate ;
	/**
	 * 截止日期，格式：yyyy-MM-dd
	 */
	@Column("end_date")
	private Date endDate ;
	/**
	 * 活动状态
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 需要关注时，错误提示信息
	 */
	@Column("need_attention_reason")
	private String needAttentionReason ;
	/**
	 * 申请原因
	 */
	@Column("apply_reason")
	private Integer applyReason ;
	/**
	 * 自定义申请原因
	 */
	@Column("custom_reason")
	private String customReason ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 业务组
	 */
	@Column("sales_group_code")
	private String salesGroupCode ;
	/**
	 * 业务组名称
	 */
	@Column("sales_group_name")
	private String salesGroupName ;
	/**
	 * 销售负责人工号
	 */
	@Column("sales_group_empt_code")
	private String salesGroupEmptCode ;
	/**
	 * 销售负责人姓名
	 */
	@Column("sales_group_empt_name")
	private String salesGroupEmptName ;
	/**
	 * 业务助理工号
	 */
	@Column("operation_empt_code")
	private String operationEmptCode ;
	/**
	 * 业务助理姓名
	 */
	@Column("operation_empt_name")
	private String operationEmptName ;

	// 前台售价
	@Column("price")
	private BigDecimal price;

	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public SomVcPointsPromotion() {
	}

	public String getAid(){
		return  aid;
	}
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 账号名称/大分类
	*@return
	*/
	public String getAccountName(){
		return  accountName;
	}
	/**
	* 账号名称/大分类
	*@param  accountName
	*/
	public void setAccountName(String accountName ){
		this.accountName = accountName;
	}
	/**
	* 供应商编码
	*@return
	*/
	public String getVendorCode(){
		return  vendorCode;
	}
	/**
	* 供应商编码
	*@param  vendorCode
	*/
	public void setVendorCode(String vendorCode ){
		this.vendorCode = vendorCode;
	}
	/**
	* 营销活动类型
	*@return
	*/
	public Integer getPromotionType(){
		return  promotionType;
	}
	/**
	* 营销活动类型
	*@param  promotionType
	*/
	public void setPromotionType(Integer promotionType ){
		this.promotionType = promotionType;
	}
	/**
	* 促销活动ID
	*@return
	*/
	public Long getPromotionId(){
		return  promotionId;
	}
	/**
	* 促销活动ID
	*@param  promotionId
	*/
	public void setPromotionId(Long promotionId ){
		this.promotionId = promotionId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 产品SKU编码
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* 产品SKU编码
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 建议零售价，RRP
	*@return
	*/
	public BigDecimal getRecommendedRetailPrice(){
		return  recommendedRetailPrice;
	}
	/**
	* 建议零售价，RRP
	*@param  recommendedRetailPrice
	*/
	public void setRecommendedRetailPrice(BigDecimal recommendedRetailPrice ){
		this.recommendedRetailPrice = recommendedRetailPrice;
	}
	/**
	* 供货价
	*@return
	*/
	public BigDecimal getCostPrice(){
		return  costPrice;
	}
	/**
	* 供货价
	*@param  costPrice
	*/
	public void setCostPrice(BigDecimal costPrice ){
		this.costPrice = costPrice;
	}
	/**
	* 积分百分比
	*@return
	*/
	public BigDecimal getDiscount(){
		return  discount;
	}
	/**
	* 积分百分比
	*@param  discount
	*/
	public void setDiscount(BigDecimal discount ){
		this.discount = discount;
	}
	/**
	* 单个产品预估积分
	*@return
	*/
	public BigDecimal getPerUnitFunding(){
		return  perUnitFunding;
	}
	/**
	* 单个产品预估积分
	*@param  perUnitFunding
	*/
	public void setPerUnitFunding(BigDecimal perUnitFunding ){
		this.perUnitFunding = perUnitFunding;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 评分
	*@return
	*/
	public BigDecimal getRating(){
		return  rating;
	}
	/**
	* 评分
	*@param  rating
	*/
	public void setRating(BigDecimal rating ){
		this.rating = rating;
	}
	/**
	* 库存可销售天数
	*@return
	*/
	public Integer getStockSaleDays(){
		return  stockSaleDays;
	}
	/**
	* 库存可销售天数
	*@param  stockSaleDays
	*/
	public void setStockSaleDays(Integer stockSaleDays ){
		this.stockSaleDays = stockSaleDays;
	}
	/**
	* 30天dms
	*@return
	*/
	public BigDecimal getMonthDms(){
		return  monthDms;
	}
	/**
	* 30天dms
	*@param  monthDms
	*/
	public void setMonthDms(BigDecimal monthDms ){
		this.monthDms = monthDms;
	}
	/**
	* 起始日期，格式：yyyy-MM-dd
	*@return
	*/
	public Date getStartDate(){
		return  startDate;
	}
	/**
	* 起始日期，格式：yyyy-MM-dd
	*@param  startDate
	*/
	public void setStartDate(Date startDate ){
		this.startDate = startDate;
	}
	/**
	* 截止日期，格式：yyyy-MM-dd
	*@return
	*/
	public Date getEndDate(){
		return  endDate;
	}
	/**
	* 截止日期，格式：yyyy-MM-dd
	*@param  endDate
	*/
	public void setEndDate(Date endDate ){
		this.endDate = endDate;
	}
	/**
	* 活动状态
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 活动状态
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 需要关注时，错误提示信息
	*@return
	*/
	public String getNeedAttentionReason(){
		return  needAttentionReason;
	}
	/**
	* 需要关注时，错误提示信息
	*@param  needAttentionReason
	*/
	public void setNeedAttentionReason(String needAttentionReason ){
		this.needAttentionReason = needAttentionReason;
	}
	/**
	* 申请原因
	*@return
	*/
	public Integer getApplyReason(){
		return  applyReason;
	}
	/**
	* 申请原因
	*@param  applyReason
	*/
	public void setApplyReason(Integer applyReason ){
		this.applyReason = applyReason;
	}
	/**
	* 自定义申请原因
	*@return
	*/
	public String getCustomReason(){
		return  customReason;
	}
	/**
	* 自定义申请原因
	*@param  customReason
	*/
	public void setCustomReason(String customReason ){
		this.customReason = customReason;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 业务组
	*@return
	*/
	public String getSalesGroupCode(){
		return  salesGroupCode;
	}
	/**
	* 业务组
	*@param  salesGroupCode
	*/
	public void setSalesGroupCode(String salesGroupCode ){
		this.salesGroupCode = salesGroupCode;
	}
	/**
	* 业务组名称
	*@return
	*/
	public String getSalesGroupName(){
		return  salesGroupName;
	}
	/**
	* 业务组名称
	*@param  salesGroupName
	*/
	public void setSalesGroupName(String salesGroupName ){
		this.salesGroupName = salesGroupName;
	}
	/**
	* 销售负责人工号
	*@return
	*/
	public String getSalesGroupEmptCode(){
		return  salesGroupEmptCode;
	}
	/**
	* 销售负责人工号
	*@param  salesGroupEmptCode
	*/
	public void setSalesGroupEmptCode(String salesGroupEmptCode ){
		this.salesGroupEmptCode = salesGroupEmptCode;
	}
	/**
	* 销售负责人姓名
	*@return
	*/
	public String getSalesGroupEmptName(){
		return  salesGroupEmptName;
	}
	/**
	* 销售负责人姓名
	*@param  salesGroupEmptName
	*/
	public void setSalesGroupEmptName(String salesGroupEmptName ){
		this.salesGroupEmptName = salesGroupEmptName;
	}
	/**
	* 业务助理工号
	*@return
	*/
	public String getOperationEmptCode(){
		return  operationEmptCode;
	}
	/**
	* 业务助理工号
	*@param  operationEmptCode
	*/
	public void setOperationEmptCode(String operationEmptCode ){
		this.operationEmptCode = operationEmptCode;
	}
	/**
	* 业务助理姓名
	*@return
	*/
	public String getOperationEmptName(){
		return  operationEmptName;
	}
	/**
	* 业务助理姓名
	*@param  operationEmptName
	*/
	public void setOperationEmptName(String operationEmptName ){
		this.operationEmptName = operationEmptName;
	}

	/**
	 * 修改人工号
	 *@return
	 */
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	 * 修改人工号
	 *@param  modifyNum
	 */
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	 * 修改人姓名
	 *@return
	 */
	public String getModifyName(){
		return  modifyName;
	}
	/**
	 * 修改人姓名
	 *@param  modifyName
	 */
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	 * 修改时间
	 *@return
	 */
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	 * 修改时间
	 *@param  modifyTime
	 */
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
