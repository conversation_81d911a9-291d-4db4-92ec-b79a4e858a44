package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.repository.entity.SomWootPromotionWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.repository.mapper.SomWootPromotionWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomWootPromotionWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWootPromotionWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomWootPromotionWarehouseConfigService
 * @description
 * @date 2024-06-03 11:58:13
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomWootPromotionWarehouseConfigService {

    @Resource
    private SomWootPromotionWarehouseConfigMapper somWootPromotionWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomWootPromotionWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWootPromotionWarehouseConfigVo> queryByPage(SomWootPromotionWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomWootPromotionWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomWootPromotionWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<SomWootPromotionWarehouseConfig> configList = somWootPromotionWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, List<SomWootPromotionWarehouseConfig>> configMap = configList.stream().collect(Collectors.groupingBy(f -> f.getSite() + f.getvcFlag() + f.getFbaStockFlag(), Collectors.toList()));
            for (SomWootPromotionWarehouseConfigVo vo : pageResult.getList()) {
                if (configMap.containsKey(vo.getSite() + vo.getVcFlag() + vo.getFbaStockFlag())) {
                    List<SomWootPromotionWarehouseConfig> configs = configMap.get(vo.getSite() + vo.getVcFlag() + vo.getFbaStockFlag());
                    List<String> nameList = new ArrayList<>();
                    vo.setList(configs.stream().map(e -> {
                        SomWootPromotionWarehouseConfigVo.UseableWarehouse warehouse = new SomWootPromotionWarehouseConfigVo.UseableWarehouse();
                        warehouse.setUseableWarehouseCode(e.getUseableWarehouseCode());
                        warehouse.setUseableStorageCode(e.getUseableStorageCode());
                        nameList.add(warehouseMap.get(e.getUseableWarehouseCode()) + "-" + storageMap.get(e.getUseableStorageCode()));
                        return warehouse;
                    }).collect(Collectors.toList()));
                    vo.setWarehouseNameList(nameList);
                    SomWootPromotionWarehouseConfig warehouseConfig = configs.get(0);
                    vo.setAid(warehouseConfig.getAid());
                    vo.setCreateNum(warehouseConfig.getCreateNum());
                    vo.setCreateName(warehouseConfig.getCreateName());
                    vo.setCreateTime(warehouseConfig.getCreateTime());
                    vo.setLastModifyNum(warehouseConfig.getLastModifyNum());
                    vo.setLastModifyName(warehouseConfig.getLastModifyName());
                    vo.setLastModifyTime(warehouseConfig.getLastModifyTime());
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomWootPromotionWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somWootPromotionWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo) || StrUtil.isBlank(somWootPromotionWarehouseConfigVo.getSite()) || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getVcFlag())
                || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getFbaStockFlag()) || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somWootPromotionWarehouseConfigMapper.createLambdaQuery().andEq("site", somWootPromotionWarehouseConfigVo.getSite()).andEq("vc_flag", somWootPromotionWarehouseConfigVo.getVcFlag())
                .andEq("fba_stock_flag", somWootPromotionWarehouseConfigVo.getFbaStockFlag()).count();
        if (count > 0) {
            throw new ValidateException("数据已存在，不允许重复维护");
        }
        List<SomWootPromotionWarehouseConfig> insertList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomWootPromotionWarehouseConfigVo.UseableWarehouse item : somWootPromotionWarehouseConfigVo.getList()) {
            SomWootPromotionWarehouseConfig config = new SomWootPromotionWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setvcFlag(somWootPromotionWarehouseConfigVo.getVcFlag());
            config.setFbaStockFlag(somWootPromotionWarehouseConfigVo.getFbaStockFlag());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setSite(somWootPromotionWarehouseConfigVo.getSite());
            insertList.add(config);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            somWootPromotionWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * update
     * 修改
     *
     * @param somWootPromotionWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo) || StrUtil.isBlank(somWootPromotionWarehouseConfigVo.getSite()) || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getVcFlag())
                || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getFbaStockFlag()) || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somWootPromotionWarehouseConfigMapper.createLambdaQuery().andEq("site", somWootPromotionWarehouseConfigVo.getSite()).andEq("vc_flag", somWootPromotionWarehouseConfigVo.getVcFlag())
                .andEq("fba_stock_flag", somWootPromotionWarehouseConfigVo.getFbaStockFlag()).delete();
        List<SomWootPromotionWarehouseConfig> insertList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomWootPromotionWarehouseConfigVo.UseableWarehouse item : somWootPromotionWarehouseConfigVo.getList()) {
            SomWootPromotionWarehouseConfig config = new SomWootPromotionWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setvcFlag(somWootPromotionWarehouseConfigVo.getVcFlag());
            config.setFbaStockFlag(somWootPromotionWarehouseConfigVo.getFbaStockFlag());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setLastModifyNum(tokenUser.getJobNumber());
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            config.setSite(somWootPromotionWarehouseConfigVo.getSite());
            insertList.add(config);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            somWootPromotionWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * delete
     *
     * @param somWootPromotionWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo) || StrUtil.isBlank(somWootPromotionWarehouseConfigVo.getSite()) || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getVcFlag())
                || ObjectUtil.isEmpty(somWootPromotionWarehouseConfigVo.getFbaStockFlag())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somWootPromotionWarehouseConfigMapper.createLambdaQuery().andEq("site", somWootPromotionWarehouseConfigVo.getSite()).andEq("vc_flag", somWootPromotionWarehouseConfigVo.getVcFlag())
                .andEq("fba_stock_flag", somWootPromotionWarehouseConfigVo.getFbaStockFlag()).delete();
    }
}
