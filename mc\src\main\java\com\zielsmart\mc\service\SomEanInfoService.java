package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.McConstants;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomEanInfo;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomEanInfoMapper;
import com.zielsmart.mc.vo.SomEanBatchVo;
import com.zielsmart.mc.vo.SomEanInfoPageSearchVo;
import com.zielsmart.mc.vo.SomEanInfoVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomEanInfoService {
    
    @Resource
    private SomEanInfoMapper somEanInfoMapper;
    @Resource
    private McDictionaryInfoMapper infoMapper;


    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomEanInfoVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomEanInfoVo> queryByPage(SomEanInfoPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomEanInfoVo> pageResult = somEanInfoMapper.queryByPage(searchVo, pageRequest);
        pageResult.getList().forEach(obj->{
            if (ObjectUtil.isNotNull(obj.getStatus())) {
                obj.setStatusText(obj.getStatus()==0?"未占用":obj.getStatus()==1?"已占用":"已使用");
            }
        });
        return ConvertUtils.pageConvert(pageResult, SomEanInfoVo.class, searchVo);
    }

    /**
     * assign
     * 分配
     * @param somEanInfoVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void assign(SomEanInfoVo somEanInfoVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somEanInfoVo) || StrUtil.isBlank(somEanInfoVo.getSequenceNumBegin())
                || StrUtil.isBlank(somEanInfoVo.getSequenceNumEmd())|| StrUtil.isBlank(somEanInfoVo.getBelong())) {
            throw new ValidateException("顺序号和归属方不能为空值，请检查数据");
        }
        long begin = Long.parseLong(somEanInfoVo.getSequenceNumBegin());
        long end = Long.parseLong(somEanInfoVo.getSequenceNumEmd());
        if (begin > end) {
            throw new ValidateException("开始顺序号不能大于结束顺序号");
        }
        List<String> sequenceList = new ArrayList<>();
        for (long i = begin; i <= end; i++) {
            String str = String.format("%0"+somEanInfoVo.getSequenceNumBegin().length()+"d", i);
            sequenceList.add(str);
        }
        long count = somEanInfoMapper.createLambdaQuery().andIn("sequence_num", sequenceList).andIsNotNull("belong").count();
        if (count > 0) {
            throw new ValidateException("顺序号段中存在已分配的顺序号，请勿重复分配");
        }
        somEanInfoMapper.updateBelong(sequenceList,somEanInfoVo.getBelong());
    }

    /**
     * occupy
     * 占用
     * @param somEanInfoVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public synchronized void occupy(SomEanInfoVo somEanInfoVo, TokenUserInfo tokenUser) throws ValidateException {
        SomEanInfo single = somEanInfoMapper.single(somEanInfoVo.getId());
        if (StrUtil.isBlank(single.getBelong())) {
            throw new ValidateException("此顺序号未分配归属方");
        }
        if (single.getStatus() != 0) {
            throw new ValidateException("顺序号已被占用，请勿重复占用");
        }
        single.setStatus(1);
        single.setCreateNum(tokenUser.getJobNumber());
        single.setCreateName(tokenUser.getUserName());
        single.setCreateTime(DateTime.now().toJdkDate());
        somEanInfoMapper.updateById(single);
    }

    public synchronized String batchOccupy(SomEanBatchVo eanBatchVo, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder sb = new StringBuilder();
        List<String> listOne = new ArrayList<>();
        List<String> listTwo = new ArrayList<>();
        List<SomEanInfo> updateList = new ArrayList<>();
        for (SomEanInfoVo somEanInfoVo : eanBatchVo.getList()) {
            SomEanInfo single = somEanInfoMapper.single(somEanInfoVo.getId());
            if (null == single) {
                throw new ValidateException("没有查询到此主键");
            }
            if (StrUtil.isBlank(single.getBelong())) {
                listOne.add(single.getSequenceNum());
                continue;
            }
            if (single.getStatus() != 0) {
                listTwo.add(single.getSequenceNum());
                continue;
            }
            single.setStatus(1);
            single.setCreateNum(tokenUser.getJobNumber());
            single.setCreateName(tokenUser.getUserName());
            single.setCreateTime(DateTime.now().toJdkDate());
            updateList.add(single);
        }
        if (updateList.size() > 0) {
            somEanInfoMapper.batchOccupy(updateList);
        }
        if (listOne.size() > 0) {
            sb.append("顺序号" + StringUtils.join(listOne, ",") + "未分配归属方\n");
        }
        if (listTwo.size() > 0) {
            sb.append("顺序号" + StringUtils.join(listTwo, ",") + "已被占用，请勿重复占用\n");
        }
        return sb.length()==0?null:sb.toString();
    }


    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomEanInfoVo> list, TokenUserInfo tokenUser) throws ValidateException {
        String belong = list.get(0).getBelong();
        if (StrUtil.isBlank(belong)) {
            throw new ValidateException("归属方不能为空");
        }
        //根据归属方查询表中所有数据
        List<SomEanInfo> dbList = somEanInfoMapper.createLambdaQuery().andEq("belong", belong).select();
        Map<String, SomEanInfo> skuPlatformMap = dbList.stream().filter(x -> StrUtil.isNotBlank(x.getSellerSku()) && StrUtil.isNotBlank(x.getPlatform())).collect(Collectors.toMap(x -> x.getSellerSku() + x.getPlatform(), Function.identity(),(x1,x2)->x1));
        Map<String, SomEanInfo> numMap = dbList.stream().collect(Collectors.toMap(x -> x.getSequenceNum()+x.getBelong(), Function.identity()));
        List<McDictionaryInfo> dbInfoList = infoMapper.createLambdaQuery().andEq("item_type_code", "Platform").select();
        Map<String, McDictionaryInfo> infoMap = dbInfoList.stream().collect(Collectors.toMap(x -> x.getItemValue(), Function.identity(),(x1,x2)->x1));
        StringBuffer sb = new StringBuffer();
        List<SomEanInfo> updateList = new ArrayList<>();
        Set<String> numList = new HashSet<>();
        Set<String> platformList = new HashSet<>();
        Set<String> codeList = new HashSet<>();
        Set<String> jobnumberList = new HashSet<>();
        Set<String> statusList = new HashSet<>();
        Set<String> repeatList = new HashSet<>();
        Set<String> repeatNumList = new HashSet<>();
        List<String> dbRepeatList = new ArrayList<>();
        Set<String> repeatSet = new HashSet<>();
        Set<String> repeatNumSet = new HashSet<>();
        for (SomEanInfoVo ean : list) {
            //校验excel是否有空值
            if (existEmpty(ean)) {
                throw new ValidateException("表中存在必填项未填写的数据，导入失败！");
            }
            if (!belong.equals(ean.getBelong())) {
                throw new ValidateException("表中存在多个归属方的数据，导入失败！");
            }
            if (ean.getCheckCode() != calcCode(ean)) {
                codeList.add(ean.getSequenceNum());
            }
            //校验excel中顺序号是否唯一
            if (repeatNumSet.contains(ean.getSequenceNum())) {
                repeatNumList.add(ean.getSequenceNum());
            }else {
                repeatNumSet.add(ean.getSequenceNum());
            }

            //校验excel中是否存在重复数据  不校验线下2B
            if (!McConstants.TWO_B.equals(ean.getBelong()) && repeatSet.contains(ean.getSellerSku()+ean.getPlatform())) {
                //校验重复
                repeatList.add(ean.getSequenceNum());
            }
            repeatSet.add(ean.getSellerSku() + ean.getPlatform());
            if (!numMap.containsKey(ean.getSequenceNum()+ean.getBelong())) {
                numList.add(ean.getSequenceNum());
            }else{
                //数据库表中存在对应数据进行校验
                SomEanInfo somEanInfo = numMap.get(ean.getSequenceNum()+ean.getBelong());
                if (!StrUtil.equals(somEanInfo.getCreateNum(), tokenUser.getJobNumber())) {
                    jobnumberList.add(ean.getSequenceNum());
                }
                if (ObjectUtil.isNull(somEanInfo.getStatus()) || 1 != somEanInfo.getStatus()) {
                    statusList.add(ean.getSequenceNum());
                }
                if (!McConstants.TWO_B.equals(ean.getBelong()) && skuPlatformMap.containsKey(ean.getSellerSku() + ean.getPlatform())) {
                    //校验重复
                    dbRepeatList.add(ean.getSequenceNum());
                }

                somEanInfo.setCheckCode(ean.getCheckCode());
                somEanInfo.setStatus(2);
                somEanInfo.setEan(ean.getSequenceNum() + ean.getCheckCode());
                somEanInfo.setRemarks(ean.getRemarks());
                somEanInfo.setSellerSku(ean.getSellerSku());
                somEanInfo.setPlatform(ean.getPlatform());
                updateList.add(somEanInfo);
            }
            if (!infoMap.containsKey(ean.getPlatform())) {
                platformList.add(ean.getSequenceNum());
            }
        }

        if (repeatList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", repeatList) + "平台+自发展示码重复，导入失败！\n\n");
        }
        if (dbRepeatList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", dbRepeatList) + "当前平台自发展示码已维护EAN，请勿重复维护。\n\n");
        }
        if (numList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", numList) + "不存在,导入失败！\n\n");
        }
        if (platformList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", platformList) + "平台不存在,导入失败！\n\n");
        }
        if (codeList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", codeList) + "校验码与系统规则计算的不一致，导入失败！\n\n");
        }
        if (jobnumberList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", jobnumberList) + "已被他人占用，导入失败！\n\n");
        }
        if (repeatNumList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", repeatNumList) + "重复，导入失败！\n\n");
        }
        if (statusList.size() > 0) {
            sb.append("表中顺序号" + String.join(",", statusList) + "状态为“非已占用”，导入失败！");
        }

        if (sb.length() > 0) {
            throw new ValidateException(sb.toString());
        }
        somEanInfoMapper.batchUseSequenceNum(updateList);
    }

    private boolean existEmpty(SomEanInfoVo ean) {
        if (StrUtil.isBlank(ean.getSequenceNum()) ||
            ObjectUtil.isNull(ean.getCheckCode())||
            StrUtil.isBlank(ean.getSellerSku())||
            StrUtil.isBlank(ean.getPlatform())||
            StrUtil.isBlank(ean.getBelong())) {
            return true;
        }
        return false;
    }


    /**
     * useSequenceNum
     * 使用
     * @param somEanInfoVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public synchronized void useSequenceNum(SomEanInfoVo somEanInfoVo,TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isNull(somEanInfoVo)) {
            throw new ValidateException("数据不能为空");
        }
        if (ObjectUtil.isNull(somEanInfoVo.getCheckCode())) {
            throw new ValidateException("校验码不能为空");
        }
        if (StrUtil.isBlank(somEanInfoVo.getSellerSku())) {
            throw new ValidateException("自发展示码不能为空");
        }
        if (StrUtil.isBlank(somEanInfoVo.getPlatform())) {
            throw new ValidateException("平台不能为空");
        }
        SomEanInfo single = somEanInfoMapper.single(somEanInfoVo.getId());
        if (!(single.getStatus() == 1 && StrUtil.equals(single.getCreateNum(), tokenUser.getJobNumber()))) {
            throw new ValidateException("状态不是已占用或当前操作人和创建人不符");
        }
        long count = somEanInfoMapper.createLambdaQuery()
                .andEq("seller_sku", somEanInfoVo.getSellerSku())
                .andEq("platform", somEanInfoVo.getPlatform())
                .andNotEq("id", single.getId())
                .andIsNotNull("ean")
                .count();
        if (count > 0 && !StrUtil.equals(single.getBelong(), McConstants.TWO_B)) {
            throw new ValidateException("当前平台自发展示码已维护EAN，请勿重复维护。");
        }
        single.setCheckCode(somEanInfoVo.getCheckCode());
        single.setSellerSku(somEanInfoVo.getSellerSku());
        single.setPlatform(somEanInfoVo.getPlatform());
        single.setStatus(2);
        single.setEan(somEanInfoVo.getSequenceNum() + somEanInfoVo.getCheckCode());
        somEanInfoMapper.updateById(single);
    }

    public synchronized void edit(SomEanInfoVo somEanInfoVo,TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isNull(somEanInfoVo)) {
            throw new ValidateException("数据不能为空");
        }
        if (ObjectUtil.isNull(somEanInfoVo.getCheckCode())) {
            throw new ValidateException("校验码不能为空");
        }
        if (StrUtil.isBlank(somEanInfoVo.getSellerSku())) {
            throw new ValidateException("自发展示码不能为空");
        }
        if (StrUtil.isBlank(somEanInfoVo.getPlatform())) {
            throw new ValidateException("平台不能为空");
        }
        SomEanInfo single = somEanInfoMapper.single(somEanInfoVo.getId());
        if (single.getStatus() != 2) {
            throw new ValidateException("状态不是已使用，请刷新界面重试");
        }
        long count = somEanInfoMapper.createLambdaQuery()
                .andEq("seller_sku", somEanInfoVo.getSellerSku())
                .andEq("platform", somEanInfoVo.getPlatform())
                .andNotEq("id", single.getId())
                .andIsNotNull("ean")
                .count();
        if (count > 0) {
            throw new ValidateException("当前平台自发展示码已维护EAN，请勿重复维护。");
        }
        single.setCheckCode(somEanInfoVo.getCheckCode());
        single.setSellerSku(somEanInfoVo.getSellerSku());
        single.setPlatform(somEanInfoVo.getPlatform());
        single.setEan(somEanInfoVo.getSequenceNum() + somEanInfoVo.getCheckCode());
        single.setModifyName(tokenUser.getUserName());
        single.setModifyNum(tokenUser.getJobNumber());
        single.setModifyTime(DateTime.now().toJdkDate());
        somEanInfoMapper.updateById(single);
    }

    public void remark(SomEanInfoVo somEanInfoVo,TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isNull(somEanInfoVo) || StrUtil.isBlank(somEanInfoVo.getId())) {
            throw new ValidateException("主键不能为空");
        }
        SomEanInfo single = somEanInfoMapper.single(somEanInfoVo.getId());
        if (0 == single.getStatus() || !StrUtil.equals(single.getCreateNum(), tokenUser.getJobNumber())) {
            throw new ValidateException("状态不是已占用/已使用或当前操作人和创建人不符");
        }
        single.setRemarks(somEanInfoVo.getRemarks());
        somEanInfoMapper.updateById(single);
    }

    public String export(SomEanInfoPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomEanInfoVo> records = queryByPage(searchVo).getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "sheet1");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomEanInfoVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public int calcCode(SomEanInfoVo somEanInfoVo) throws ValidateException {
        if (ObjectUtil.isNull(somEanInfoVo) || StrUtil.isBlank(somEanInfoVo.getSequenceNum())) {
            throw new ValidateException("顺序号不能为空");
        }
        String orderNumber = somEanInfoVo.getSequenceNum();
        int oddSum = 0;
        int evenSum = 0;
        for (int i = 1; i < orderNumber.length(); i += 2) {
            oddSum += Integer.parseInt(orderNumber.charAt(i) + "");
        }
        for (int i = 2; i < orderNumber.length(); i += 2) {
            evenSum += Integer.parseInt(orderNumber.charAt(i) + "");
        }
        int result = oddSum * 3 + evenSum;
        int digit = result % 10;
        return digit == 0 ? 0 : 10 - digit;
    }


    public List<SomEanInfoVo> getEan(List<SomEanInfoPageSearchVo.EyaParam> params) throws ValidateException {
        if (params.isEmpty()) {
            throw new ValidateException("传入参数不能为空");
        }
        String sql = "select platform,seller_sku,sequence_num,check_code,ean from som_ean_info where belong!='线下2B' and (";
        for (int i = 0; i < params.size(); i++) {
            SomEanInfoPageSearchVo.EyaParam param = params.get(i);
            sql = sql + "(platform='" + param.getPlatform() + "' and seller_sku='" + param.getSellerSKU() + "')";
            if (i < params.size() - 1) {
                sql = sql + " or ";
            }
        }
        sql = sql + ")";
        return ConvertUtils.listConvert(somEanInfoMapper.execute(sql),SomEanInfoVo.class);
    }
}
