package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTemuManualQuantity;
import com.zielsmart.mc.repository.mapper.SomTemuManualQuantityMapper;
import com.zielsmart.mc.vo.SomTemuManualQuantityPageSearchVo;
import com.zielsmart.mc.vo.SomTemuManualQuantityVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTemuManualQuantityService
 * @description
 * @date 2024-07-05 09:27:31
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuManualQuantityService {

    @Resource
    private SomTemuManualQuantityMapper somTemuManualQuantityMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link PageVo< SomTemuManualQuantityVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuManualQuantityVo> queryByPage(SomTemuManualQuantityPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuManualQuantityVo> pageResult = dynamicSqlManager.getMapper(SomTemuManualQuantityMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuManualQuantityVo.class, searchVo);
    }

    /**
     * save
     *
     * @param somTemuManualQuantityVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuManualQuantityVo somTemuManualQuantityVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuManualQuantityVo) || StrUtil.isBlank(somTemuManualQuantityVo.getPlatform()) || StrUtil.isBlank(somTemuManualQuantityVo.getSite()) || StrUtil.isBlank(somTemuManualQuantityVo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (somTemuManualQuantityVo.getFbtQuantity() == null || somTemuManualQuantityVo.getFbtQuantity() < 0) {
            throw new ValidateException("FBT代发库存必须≥0");
        }
        if (somTemuManualQuantityVo.getVcQuantity() == null || somTemuManualQuantityVo.getVcQuantity() < 0) {
            throw new ValidateException("VC可供应库存必须≥0");
        }
        if (somTemuManualQuantityVo.getScQuantity() == null || somTemuManualQuantityVo.getScQuantity() < 0) {
            throw new ValidateException("SC可供应库存必须≥0");
        }
        if (StrUtil.isBlank(somTemuManualQuantityVo.getAid())) {
            long count = somTemuManualQuantityMapper.createLambdaQuery().andEq("platform", somTemuManualQuantityVo.getPlatform()).andEq("site", somTemuManualQuantityVo.getSite()).andEq("seller_sku", somTemuManualQuantityVo.getSellerSku()).count();
            if (count > 0) {
                throw new ValidateException("该平台、站点、展示码的数据已存在，不允许重复");
            }
            somTemuManualQuantityVo.setAid(IdUtil.fastSimpleUUID());
            somTemuManualQuantityVo.setCreateNum(tokenUser.getJobNumber());
            somTemuManualQuantityVo.setCreateName(tokenUser.getUserName());
            somTemuManualQuantityVo.setCreateTime(DateTime.now().toJdkDate());
            somTemuManualQuantityMapper.insert(ConvertUtils.beanConvert(somTemuManualQuantityVo, SomTemuManualQuantity.class));
        } else {
            SomTemuManualQuantity vo = new SomTemuManualQuantity();
            vo.setFbtQuantity(somTemuManualQuantityVo.getFbtQuantity());
            vo.setvcQuantity(somTemuManualQuantityVo.getVcQuantity());
            vo.setscQuantity(somTemuManualQuantityVo.getScQuantity());
            vo.setModifyNum(tokenUser.getJobNumber());
            vo.setModifyName(tokenUser.getUserName());
            vo.setModifyTime(DateTime.now().toJdkDate());
            somTemuManualQuantityMapper.createLambdaQuery().andEq("aid", somTemuManualQuantityVo.getAid()).updateSelective(vo);
        }
    }

    /**
     * delete
     *
     * @param somTemuManualQuantityVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuManualQuantityVo somTemuManualQuantityVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuManualQuantityVo) || StrUtil.isBlank(somTemuManualQuantityVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTemuManualQuantityMapper.createLambdaQuery().andEq("aid", somTemuManualQuantityVo.getAid()).delete();
    }

    /**
     * export
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String export(SomTemuManualQuantityPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomTemuManualQuantityVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Temu人工指定库存表管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuManualQuantityVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * importExcel
     * 导入
     *
     * @param list
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomTemuManualQuantityVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验Excel中的平台、站点、展示码不允许为空，「FBT代发库存」、「VC可供应」、「SC可供应」必须≥0
        for (SomTemuManualQuantityVo vo : list) {
            if (StrUtil.isBlank(vo.getPlatform()) || StrUtil.isBlank(vo.getSite()) || StrUtil.isBlank(vo.getSellerSku())) {
                throw new ValidateException("平台、站点、展示码不允许为空");
            }
            if ((vo.getFbtQuantity() == null || vo.getFbtQuantity() < 0) || (vo.getVcQuantity() == null || vo.getVcQuantity() < 0) || (vo.getScQuantity() == null || vo.getScQuantity() < 0)) {
                throw new ValidateException("FBT代发库存、VC可供应库存、SC可供应库存必须≥0");
            }
        }
        //校验Excel中是否有重复数据
        List<String> repeatList = new ArrayList<>();
        Map<String, List<SomTemuManualQuantityVo>> importMap = list.stream().collect(Collectors.groupingBy(x -> x.getPlatform() + x.getSite() + x.getSellerSku()));
        for (String key : importMap.keySet()) {
            List<SomTemuManualQuantityVo> listVo = importMap.get(key);
            if (listVo.size() > 1) {
                SomTemuManualQuantityVo vo = listVo.get(0);
                repeatList.add("平台：" + vo.getPlatform() + "，站点：" + vo.getSite() + "，展示码：" + vo.getSellerSku() + "重复，不允许导入");
            }
        }
        if (CollectionUtil.isNotEmpty(repeatList)) {
            throw new ValidateException(String.join("\n", repeatList));
        }
        //校验Excel中的平台+站点+展示码是否在数据库中已存在，已存在不允许导入
        //2024.7.11修改为已存在的执行覆盖逻辑（先删除表中的数据再重新导入）
        List<SomTemuManualQuantity> allList = somTemuManualQuantityMapper.all();
        Map<String, SomTemuManualQuantity> allMap = allList.stream().collect(Collectors.toMap(f -> f.getPlatform() + f.getSite() + f.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        List<SomTemuManualQuantity> quantityList = new ArrayList<>();
        list.forEach(f -> {
            if (allMap.containsKey(f.getPlatform() + f.getSite() + f.getSellerSku())) {
                quantityList.add(allMap.get(f.getPlatform() + f.getSite() + f.getSellerSku()));
            }
        });
        if (CollectionUtil.isNotEmpty(quantityList)) {
            List<String> aidList = quantityList.stream().map(SomTemuManualQuantity::getAid).collect(Collectors.toList());
            somTemuManualQuantityMapper.createLambdaQuery().andIn("aid", aidList).delete();
        }
        Date now = DateTime.now().toJdkDate();
        list.forEach(f -> {
            f.setAid(IdUtil.fastSimpleUUID());
            f.setCreateNum(tokenUser.getJobNumber());
            f.setCreateName(tokenUser.getUserName());
            f.setCreateTime(now);
        });
        somTemuManualQuantityMapper.insertBatch(ConvertUtils.listConvert(list, SomTemuManualQuantity.class));
    }
}
