package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2022-12-26
 */

@Table(name = "mc.sys_user_neweya")
public class SysUserNeweya implements java.io.Serializable {
    @AssignID
    private String aid;
    @Column("user_sap_num")
    private String userSapNum;
    @Column("resource_flag")
    private Integer resourceFlag;
    @Column("syn_record_id")
    private Integer synRecordId;
    @Column("login_number")
    private String loginNumber;
    @Column("employee_number")
    private String employeeNumber;
    @Column("em_name_cn")
    private String emNameCn;
    @Column("em_name_en")
    private String emNameEn;
    @Column("login_pwd")
    private String loginPwd;
    @Column("initialize_pwd")
    private String initializePwd;
    @Column("email_info")
    private String emailInfo;
    @Column("phone_info")
    private String phoneInfo;
    @Column("dingtalk_num")
    private String dingtalkNum;
    @Column("wechat_num")
    private String wechatNum;
    @Column("default_dept_id")
    private Integer defaultDeptId;
    @Column("default_dept_code")
    private String defaultDeptCode;
    @Column("company_id")
    private Integer companyId;
    @Column("company_code")
    private String companyCode;
    @Column("is_freezed")
    private Integer isFreezed;
    @Column("freezed_time")
    private Date freezedTime;
    @Column("freezed_reason")
    private String freezedReason;
    @Column("is_enabled")
    private Integer isEnabled;
    @Column("start_time")
    private Date startTime;
    @Column("end_time")
    private Date endTime;
    @Column("create_num")
    private String createNum;
    @Column("create_time")
    private Date createTime;
    @Column("modify_num")
    private String modifyNum;
    @Column("modify_time")
    private Date modifyTime;
    @Column("remark_desc")
    private String remarkDesc;
    @Column("oa_dept_code")
    private String oaDeptCode;
    @Column("oa_dept_name")
    private String oaDeptName;
    @Column("create_name")
    private String createName;
    @Column("modify_name")
    private String modifyName;
    @Column("last_syn_time")
    private Date lastSynTime;
    @Column("is_sync_sap_success")
    private Integer isSyncSapSuccess;
    @Column("syn_descinfo")
    private String synDescinfo;

    public SysUserNeweya() {
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getUserSapNum() {
        return userSapNum;
    }

    public void setUserSapNum(String userSapNum) {
        this.userSapNum = userSapNum;
    }

    public Integer getResourceFlag() {
        return resourceFlag;
    }

    public void setResourceFlag(Integer resourceFlag) {
        this.resourceFlag = resourceFlag;
    }

    public Integer getSynRecordId() {
        return synRecordId;
    }

    public void setSynRecordId(Integer synRecordId) {
        this.synRecordId = synRecordId;
    }

    public String getLoginNumber() {
        return loginNumber;
    }

    public void setLoginNumber(String loginNumber) {
        this.loginNumber = loginNumber;
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getemNameCn() {
        return emNameCn;
    }

    public void setemNameCn(String emNameCn) {
        this.emNameCn = emNameCn;
    }

    public String getemNameEn() {
        return emNameEn;
    }

    public void setemNameEn(String emNameEn) {
        this.emNameEn = emNameEn;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }

    public String getInitializePwd() {
        return initializePwd;
    }

    public void setInitializePwd(String initializePwd) {
        this.initializePwd = initializePwd;
    }

    public String getEmailInfo() {
        return emailInfo;
    }

    public void setEmailInfo(String emailInfo) {
        this.emailInfo = emailInfo;
    }

    public String getPhoneInfo() {
        return phoneInfo;
    }

    public void setPhoneInfo(String phoneInfo) {
        this.phoneInfo = phoneInfo;
    }

    public String getDingtalkNum() {
        return dingtalkNum;
    }

    public void setDingtalkNum(String dingtalkNum) {
        this.dingtalkNum = dingtalkNum;
    }

    public String getWechatNum() {
        return wechatNum;
    }

    public void setWechatNum(String wechatNum) {
        this.wechatNum = wechatNum;
    }

    public Integer getDefaultDeptId() {
        return defaultDeptId;
    }

    public void setDefaultDeptId(Integer defaultDeptId) {
        this.defaultDeptId = defaultDeptId;
    }

    public String getDefaultDeptCode() {
        return defaultDeptCode;
    }

    public void setDefaultDeptCode(String defaultDeptCode) {
        this.defaultDeptCode = defaultDeptCode;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getisFreezed() {
        return isFreezed;
    }

    public void setisFreezed(Integer isFreezed) {
        this.isFreezed = isFreezed;
    }

    public Date getFreezedTime() {
        return freezedTime;
    }

    public void setFreezedTime(Date freezedTime) {
        this.freezedTime = freezedTime;
    }

    public String getFreezedReason() {
        return freezedReason;
    }

    public void setFreezedReason(String freezedReason) {
        this.freezedReason = freezedReason;
    }

    public Integer getisEnabled() {
        return isEnabled;
    }

    public void setisEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCreateNum() {
        return createNum;
    }

    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyNum() {
        return modifyNum;
    }

    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemarkDesc() {
        return remarkDesc;
    }

    public void setRemarkDesc(String remarkDesc) {
        this.remarkDesc = remarkDesc;
    }

    public String getoaDeptCode() {
        return oaDeptCode;
    }

    public void setoaDeptCode(String oaDeptCode) {
        this.oaDeptCode = oaDeptCode;
    }

    public String getoaDeptName() {
        return oaDeptName;
    }

    public void setoaDeptName(String oaDeptName) {
        this.oaDeptName = oaDeptName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getLastSynTime() {
        return lastSynTime;
    }

    public void setLastSynTime(Date lastSynTime) {
        this.lastSynTime = lastSynTime;
    }

    public Integer getisSyncSapSuccess() {
        return isSyncSapSuccess;
    }

    public void setisSyncSapSuccess(Integer isSyncSapSuccess) {
        this.isSyncSapSuccess = isSyncSapSuccess;
    }

    public String getSynDescinfo() {
        return synDescinfo;
    }

    public void setSynDescinfo(String synDescinfo) {
        this.synDescinfo = synDescinfo;
    }
}
