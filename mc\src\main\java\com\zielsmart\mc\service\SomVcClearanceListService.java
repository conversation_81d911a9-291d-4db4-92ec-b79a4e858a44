package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McVcWarehouseConfig;
import com.zielsmart.mc.repository.entity.SomAmazonVcListing;
import com.zielsmart.mc.repository.entity.SomVcClearanceList;
import com.zielsmart.mc.repository.mapper.McVcWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomAmazonVcListingMapper;
import com.zielsmart.mc.repository.mapper.SomVcClearanceListMapper;
import com.zielsmart.mc.vo.SomVcClearanceListPageSearchVo;
import com.zielsmart.mc.vo.SomVcClearanceListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-01-07 09:14:40
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcClearanceListService {

    @Resource
    private SomVcClearanceListMapper vcClearanceListMapper;

    @Resource
    private SomAmazonVcListingMapper amazonVcListingMapper;

    @Resource
    private McVcWarehouseConfigMapper vcWarehouseConfigMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomVcClearanceListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomVcClearanceListVo> queryByPage(SomVcClearanceListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcClearanceListVo> pageResult = vcClearanceListMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomVcClearanceListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param clearanceListVo 入参
     * @param tokenUser       当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomVcClearanceListVo clearanceListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(clearanceListVo) ||
                StrUtil.isBlank(clearanceListVo.getSite()) ||
                StrUtil.isBlank(clearanceListVo.getVendorCode()) ||
                StrUtil.isBlank(clearanceListVo.getSellerSku()) ||
                StrUtil.isBlank(clearanceListVo.getPlatformWarehouseCode())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String site = clearanceListVo.getSite();
        String vendorCode = clearanceListVo.getVendorCode();
        String sellerSku = clearanceListVo.getSellerSku();
        String platformWarehouseCode = clearanceListVo.getPlatformWarehouseCode();
        if (!StrUtil.isAllNotBlank(site, vendorCode, sellerSku, platformWarehouseCode)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 核验平台仓库编码是否存在
        List<String> platformWarehouseCodes = vcWarehouseConfigMapper.all().stream().map(McVcWarehouseConfig::getPlatformWarehouseCode).collect(Collectors.toList());
        if (!platformWarehouseCodes.contains(platformWarehouseCode)) {
            throw new ValidateException("您输入的平台仓库编码在系统中不存在，不支持清仓");
        }
        // 根据【站点 + 展示码 + 供应商编码】在 listing 中是否存在
        SomAmazonVcListing listing = amazonVcListingMapper.createLambdaQuery().andEq("site", site).andEq("seller_sku", sellerSku).andEq("vendor_code", vendorCode).single();
        if (ObjectUtil.isEmpty(listing)) {
            throw new ValidateException("您输入的产品不存在，请检查");
        }
        LambdaQuery<SomVcClearanceList> vcClearanceListLambdaQuery = vcClearanceListMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("vendor_code", vendorCode)
                .andEq("seller_sku", sellerSku)
                .andEq("platform_warehouse_code", platformWarehouseCode);
        if (!StrUtil.isBlank(clearanceListVo.getAid())) {
            vcClearanceListLambdaQuery.andNotEq("aid", clearanceListVo.getAid());
        }
        long count = vcClearanceListLambdaQuery.count();
        if (count > 0) {
            throw new ValidateException("您输入的产品在清仓列表中已存在，不允许重复维护");
        }
        clearanceListVo.setCreateName(tokenUser.getUserName());
        clearanceListVo.setCreateTime(DateTime.now().toJdkDate());
        clearanceListVo.setCreateNum(tokenUser.getJobNumber());
        if (StrUtil.isBlank(clearanceListVo.getAid())) {
            clearanceListVo.setAid(IdUtil.fastSimpleUUID());
            vcClearanceListMapper.insert(ConvertUtils.beanConvert(clearanceListVo, SomVcClearanceList.class));
        } else {
            vcClearanceListMapper.createLambdaQuery()
                    .andEq("aid", clearanceListVo.getAid())
                    .updateSelective(ConvertUtils.beanConvert(clearanceListVo, SomVcClearanceList.class));
        }
    }

    /**
     * delete
     * 删除
     *
     * @param vcClearanceListVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomVcClearanceListVo vcClearanceListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(vcClearanceListVo) || StrUtil.isEmpty(vcClearanceListVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        vcClearanceListMapper.createLambdaQuery().andEq("aid", vcClearanceListVo.getAid()).delete();
    }

    /**
     * importExcel
     * 导入数据
     *
     * @param clearanceListVos 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomVcClearanceListVo> clearanceListVos, TokenUserInfo tokenUser) throws ValidateException {
        // 去重
        clearanceListVos = clearanceListVos.stream().distinct().collect(Collectors.toList());
        if (clearanceListVos.isEmpty()) {
            return;
        }
        // 查询全部 listing，不确定上传的数量，使用【站点】进行查询
        List<String> sites = clearanceListVos.stream().map(SomVcClearanceListVo::getSite).distinct().collect(Collectors.toList());
        Map <String, SomAmazonVcListing> listingMap = queryListingMap(sites);
        String errorListingMsg = clearanceListVos.stream()
                .filter(x -> ! listingMap.containsKey(x.getSite() + x.getSellerSku() + x.getVendorCode()))
                .map(x -> StrUtil.concat(false, "站点：", x.getSite() + " 展示码：" + x.getSellerSku()))
                .collect(Collectors.joining("\n"));
        String errorMsg = null;
        if (StrUtil.isNotBlank(errorListingMsg)) {
            errorMsg = StrUtil.concat(true, errorListingMsg, "\n在系统中不存在,请检查数据！");
        }
        // 查询平台仓库编码
        List<String> platformWarehouseCodes = vcWarehouseConfigMapper.all().stream().map(McVcWarehouseConfig::getPlatformWarehouseCode).collect(Collectors.toList());
        String errorWarehouseMsg = clearanceListVos.stream()
                .filter(x -> ! platformWarehouseCodes.contains(x.getPlatformWarehouseCode()))
                .map(x -> StrUtil.concat(false, "平台仓库编码：", x.getPlatformWarehouseCode()))
                .collect(Collectors.joining("\n"));
        if (StrUtil.isNotBlank(errorWarehouseMsg)) {
            errorMsg = StrUtil.concat(true, errorMsg, "\n", errorWarehouseMsg, "\n在系统中不存在，不支持清仓！");
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            throw new ValidateException(errorMsg);
        }
        // 判断在清仓列表中是否已经存在
        List<SomVcClearanceList> clearanceLists = vcClearanceListMapper.all();
        List<String> existVcClearanceKeys = clearanceLists.stream().map(data -> {
            String site = data.getSite();
            String sellerSku = data.getSellerSku();
            String vendorCode = data.getVendorCode();
            String platformWarehouseCode = data.getPlatformWarehouseCode();
            return StrUtil.concat(true, site, sellerSku, vendorCode, platformWarehouseCode);
        }).collect(Collectors.toList());
        // 整合数据
        List<SomVcClearanceListVo> listVos = new ArrayList<>();
        for (SomVcClearanceListVo clearanceListVo : clearanceListVos) {
            String site = clearanceListVo.getSite();
            String sellerSku = clearanceListVo.getSellerSku();
            String vendorCode = clearanceListVo.getVendorCode();
            String platformWarehouseCode = clearanceListVo.getPlatformWarehouseCode();
            String key = StrUtil.concat(true, site, sellerSku, vendorCode, platformWarehouseCode);
            if (existVcClearanceKeys.contains(key)) {
                continue;
            }
            clearanceListVo.setCreateNum(tokenUser.getJobNumber());
            clearanceListVo.setCreateName(tokenUser.getUserName());
            clearanceListVo.setCreateTime(DateTime.now().toJdkDate());
            clearanceListVo.setAid(IdUtil.fastSimpleUUID());
            listVos.add(clearanceListVo);
        }
        if (CollUtil.isNotEmpty(listVos)) {
            vcClearanceListMapper.insertBatch(ConvertUtils.listConvert(listVos, SomVcClearanceList.class));
        }
    }

    /**
     * export
     * 导出
     *
     * @param searchVo 入参
     * @return String
     * <AUTHOR>
     */
    public String export(SomVcClearanceListPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomVcClearanceListVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook;
            try {
                ExportParams params = new ExportParams(null, "VC清仓列表管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomVcClearanceListVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 批量导入删除
     *
     * @param clearanceListVos 导入数据
     */
    public void batchDelete(List<SomVcClearanceListVo> clearanceListVos) {
        List<SomVcClearanceList> clearanceLists = vcClearanceListMapper.createLambdaQuery()
                .select("aid","site","vendor_code", "seller_sku", "platform_warehouse_code");
        Map<String, String> clearanceListMap = clearanceLists.stream()
                .collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku() + x.getVendorCode() + x.getPlatformWarehouseCode(),
                        SomVcClearanceList::getAid, (x1, x2) -> x1));
        Set<String> aids = clearanceListVos.stream().map(data -> {
            String site = data.getSite();
            String sellerSku = data.getSellerSku();
            String vendorCode = data.getVendorCode();
            String platformWarehouseCode = data.getPlatformWarehouseCode();
            String key = StrUtil.concat(true, site, sellerSku, vendorCode, platformWarehouseCode);
            return clearanceListMap.get(key);
        }).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(aids)) {
            vcClearanceListMapper.createLambdaQuery().andIn("aid", aids).delete();
        }
    }

    /**
     * 获取导入涉及的 listing map
     *
     * @param sites 站点
     * @return Map<String, SomAmazonVcListing>
     */
    private Map<String, SomAmazonVcListing> queryListingMap(List<String> sites) {
        if (CollUtil.isEmpty(sites)) {
            return Collections.emptyMap();
        }
        List<SomAmazonVcListing> listings = amazonVcListingMapper.createLambdaQuery()
                .andIn("site", sites)
                .select("site", "seller_sku", "vendor_code");
        return listings.stream().collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku() + x.getVendorCode(), Function.identity(), (x1, x2) -> x1));
    }


}
