package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomTargetInventoryReportService;
import com.zielsmart.mc.vo.SomTargetInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomTargetInventoryReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/target-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Target可售库存报表")
public class SomTargetInventoryReportController extends BasicController {

    @Resource
    private SomTargetInventoryReportService service;

    /**
     * 可售库存报表分页查询
     *
     * @param searchVo
     * @return
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTargetInventoryReportVo>> queryByPage(@RequestBody SomTargetInventoryReportSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(service.queryTargetInventoryReport(searchVo));
    }

    /**
     * 导出
     *
     * @param searchVo
     * @return
     * @throws ValidateException
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomTargetInventoryReportSearchVo searchVo) throws ValidateException, JsonProcessingException {
        String data = service.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
