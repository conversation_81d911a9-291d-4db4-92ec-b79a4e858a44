package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuChecklistExtVo;
import com.zielsmart.mc.vo.SomTemuChecklistPageSearchVo;
import com.zielsmart.mc.vo.SomTemuChecklistVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-09-05
*/

@SqlResource("somTemuChecklist")
public interface SomTemuChecklistMapper extends BaseMapper<SomTemuChecklist> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuChecklistVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuChecklistExtVo> queryByPage(@Param("searchVo")SomTemuChecklistPageSearchVo searchVo, PageRequest pageRequest);
    default void batchUpdate(@Param("somTemuChecklists") List<SomTemuChecklist> somTemuChecklists) {
        this.getSQLManager().updateBatch(SqlId.of("somTemuChecklist.batchUpdate"), somTemuChecklists);
    }
}
