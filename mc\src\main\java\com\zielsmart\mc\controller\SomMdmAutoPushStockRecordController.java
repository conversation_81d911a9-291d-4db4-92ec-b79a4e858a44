package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomMdmAutoPushStockRecordService;
import com.zielsmart.mc.vo.SomMdmAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomMdmAutoPushStockRecordVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomMdmAutoPushStockRecordController
 * @description
 * @date 2025-04-09 16:47:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somMdmAutoPushStockRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "mdm offers历史记录管理")
public class SomMdmAutoPushStockRecordController extends BasicController{

    @Resource
    SomMdmAutoPushStockRecordService somMdmAutoPushStockRecordService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomMdmAutoPushStockRecordVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomMdmAutoPushStockRecordVo>> queryByPage(@RequestBody SomMdmAutoPushStockRecordPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somMdmAutoPushStockRecordService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomMdmAutoPushStockRecordPageSearchVo searchVo){
        String data = somMdmAutoPushStockRecordService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
