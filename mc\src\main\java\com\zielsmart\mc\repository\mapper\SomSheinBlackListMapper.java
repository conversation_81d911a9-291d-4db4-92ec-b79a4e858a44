package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomSheinBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomSheinBlackListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2025-06-30
*/

@SqlResource("somSheinBlackList")
public interface SomSheinBlackListMapper extends BaseMapper<SomSheinBlackList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomSheinBlackListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomSheinBlackListVo> queryByPage(@Param("searchVo")SomSheinBlackListPageSearchVo searchVo, PageRequest pageRequest);
}
