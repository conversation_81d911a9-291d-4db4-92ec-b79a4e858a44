package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
 * Amazon简单上货模版映射关系
 * gen by 代码生成器 2025-07-28
 */

@Table(name = "mc.som_amazon_simple_publish_template_mapping")
public class SomAmazonSimplePublishTemplateMapping implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 模版字段
     */
    @Column("template_field")
    private String templateField;
    /**
     * 映射字段
     */
    @Column("mapping_field")
    private String mappingField;
    /**
     * 字段描述
     */
    @Column("description")
    private String description;

	/**
	 * 字段类型
	 */
	@Column("type")
	public String type;

	/**
	 * 时间转换格式
	 */
	@Column("date_format")
	private String dateFormat;



	public SomAmazonSimplePublishTemplateMapping() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 模版字段
     *
     * @return
     */
    public String getTemplateField() {
        return templateField;
    }

    /**
     * 模版字段
     *
     * @param templateField
     */
    public void setTemplateField(String templateField) {
        this.templateField = templateField;
    }

    /**
     * 映射字段
     *
     * @return
     */
    public String getMappingField() {
        return mappingField;
    }

    /**
     * 映射字段
     *
     * @param mappingField
     */
    public void setMappingField(String mappingField) {
        this.mappingField = mappingField;
    }

    /**
     * 字段描述
     *
     * @return
     */
    public String getDescription() {
        return description;
    }

    /**
     * 字段描述
     *
     * @param description
     */
    public void setDescription(String description) {
        this.description = description;
    }

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDateFormat() {
		return dateFormat;
	}

	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
}
