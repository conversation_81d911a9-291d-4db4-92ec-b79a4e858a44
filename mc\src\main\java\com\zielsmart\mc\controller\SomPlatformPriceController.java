package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.amazon.spapi.merchant.fulfillment.model.FileType;
import com.zielsmart.mc.service.SomPlatformPriceService;
import com.zielsmart.mc.vo.SomPlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomPlatformPriceVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPlatformPriceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somPlatformPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台价格维护管理")
public class SomPlatformPriceController extends BasicController {

    @Resource
    SomPlatformPriceService service;

    /**
     * 下载导入模板
     *
     * @param
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/PlatformPriceTemplate.xlsx";
    }

    /**
     * importExcel
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("platform") String platform, @RequestParam("site") String site,@RequestParam("importDate") String importDate, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        if(!file.isEmpty()){
            String originalFilename = file.getOriginalFilename();
            String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
            if(!".xls".equalsIgnoreCase(fileType) && !".xlsx".equalsIgnoreCase(fileType)){
                throw new ValidateException("导入模板格式有误,请检查模板");
            }
            ImportParams importParams = new ImportParams();
            //头占用的行数
            importParams.setHeadRows(1);
            //标题占用的行数，没有写0et
            importParams.setTitleRows(0);
            // 导入字段
            String[] arr = {"国家", "平台", "站点", "展示码", "发货方式", "售价", "运费","币种"};
            importParams.setImportFields(arr);
            ExcelImportResult<SomPlatformPriceVo> result = null;
            try {
                result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPlatformPriceVo.class, importParams);
            } catch (Exception e) {
                if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                    throw new ValidateException("导入模板有误,请检查模板");
                }else {
                    throw new ValidateException("导入失败,错误信息为{}"+e.getLocalizedMessage());
                }
            }
            List<SomPlatformPriceVo> list = result.getList();
            if (list.isEmpty()) {
                throw new ValidateException("导入数据为空,请检查数据");
            }
            service.importExcel(platform,site,importDate,list, tokenUser);
            return ResultVo.ofSuccess();
        }else {
            return ResultVo.ofFail("导入失败");
        }
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomPlatformPriceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomPlatformPriceVo>> queryByPage(@RequestBody SomPlatformPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(service.queryByPage(searchVo));
    }

    /**
     * delete
     *
     * @param vo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomPlatformPriceVo vo) throws ValidateException {
        service.delete(vo);
        return ResultVo.ofSuccess(null);
    }
}
