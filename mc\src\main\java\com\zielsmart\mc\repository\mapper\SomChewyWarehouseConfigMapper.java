package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomChewyWarehouseConfig;
import com.zielsmart.mc.vo.SomChewyStockReportVo;
import com.zielsmart.mc.vo.SomChewyWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomChewyWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

@SqlResource("somChewyWarehouseConfig")
public interface SomChewyWarehouseConfigMapper extends BaseMapper<SomChewyWarehouseConfig> {

    PageResult<SomChewyWarehouseConfigVo> queryByPage(@Param("searchVo") SomChewyWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomChewyStockReportVo> stockReport(@Param("searchVo") SomChewyWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<SomChewyStockReportVo> exportStockReport(@Param("searchVo") SomChewyWarehouseConfigPageSearchVo searchVo);

}
