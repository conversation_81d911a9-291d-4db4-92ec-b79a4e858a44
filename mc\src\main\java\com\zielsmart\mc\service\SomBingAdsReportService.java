package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.zielsmart.mc.repository.mapper.SomBingAdsReportMapper;
import com.zielsmart.mc.vo.SomBingAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomBingAdsReportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomBingAdsReportService {
    
    @Resource
    private SomBingAdsReportMapper somBingAdsReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomBingAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomBingAdsReportVo> queryByPage(SomBingAdsReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomBingAdsReportVo> pageResult = dynamicSqlManager.getMapper(SomBingAdsReportMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomBingAdsReportVo.class, searchVo);
    }

    public String export(SomBingAdsReportPageSearchVo searchVo) throws ValidateException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomBingAdsReportVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            try {
                Workbook workbook = null;
                ExportParams params = new ExportParams(null, "Bing广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomBingAdsReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
