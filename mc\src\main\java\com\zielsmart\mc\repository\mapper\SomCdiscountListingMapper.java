package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomCdiscountListing;
import com.zielsmart.mc.vo.SomCdiscountListingPageSearchVo;
import com.zielsmart.mc.vo.SomCdiscountListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-01-11
 */

@SqlResource("somCdiscountListing")
public interface SomCdiscountListingMapper extends BaseMapper<SomCdiscountListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomCdiscountListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomCdiscountListingVo> queryByPage(@Param("searchVo") SomCdiscountListingPageSearchVo searchVo, PageRequest pageRequest);
}
