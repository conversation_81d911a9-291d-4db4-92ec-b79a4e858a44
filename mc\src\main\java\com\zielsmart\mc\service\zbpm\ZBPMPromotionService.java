package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomPromotion;
import com.zielsmart.mc.repository.mapper.SomPromotionMapper;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.vo.zbpm.ZBPMPromotionSubmitVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ZBPMPromotionService {

    @Resource
    private SomPromotionMapper promotionMapper;

    /**
     * updatePromotion
     * 更新状态
     *
     * @param updateVo
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void updatePromotion(ZBPMPromotionSubmitVo updateVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(updateVo) || StrUtil.isBlank(updateVo.getAid()) || StrUtil.isBlank(updateVo.getResultMsg())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        if (StrUtil.equals("未通过", updateVo.getResultMsg()) && StrUtil.isBlank(updateVo.getJobNumber())) {
            throw new ValidateException("审核未通过时,发起人不能为空");
        }
        SomPromotion promotion = promotionMapper.createLambdaQuery().andEq("aid", updateVo.getAid()).single();
        if (ObjectUtil.isNotEmpty(promotion)) {
            switch (updateVo.getResultMsg()) {
                case ("审核通过"):
                    promotion.setStatus(21);
                    break;
                case ("未通过"):
                    promotion.setStatus(29);
                    promotion.setAuditErrorMsg(updateVo.getResultResason());
                    break;
            }
            promotion.setAuditTime(DateTime.now().toJdkDate());
            promotionMapper.updateById(promotion);
            if (StrUtil.equalsIgnoreCase("未通过", updateVo.getResultMsg())) {
                ZBPMPromotionSubmitVo submitVo = ConvertUtils.beanConvert(updateVo, ZBPMPromotionSubmitVo.class);
                submitVo.setSite(promotion.getSite());
                submitVo.setInternalDescription(promotion.getInternalDescription());
                submitVo.setPromotionStartDate(promotion.getPromotionStartDate());
                submitVo.setPromotionEndDate(promotion.getPromotionEndDate());
                log.info("Promotion活动审批未通过,发送飞书提醒消息.PromotionId:{},发起人:{}", updateVo.getAid(), updateVo.getJobNumber());
                try {
                    FeiShuUtils.sendPromotionNotice(submitVo);
                } catch (Exception e) {
                    log.error("Promotion活动审批未通过,发送飞书提醒消息异常{}", e.getMessage());
                }
            }
        } else {
            throw new ValidateException("查询不到数据");
        }
    }
}