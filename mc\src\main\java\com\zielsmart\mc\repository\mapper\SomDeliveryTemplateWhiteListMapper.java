package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2022-02-16
*/

@SqlResource("somDeliveryTemplateWhiteList")
public interface SomDeliveryTemplateWhiteListMapper extends BaseMapper<SomDeliveryTemplateWhiteList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomDeliveryTemplateWhiteListVo> queryByPage(@Param("searchVo")SomDeliveryTemplateWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
