package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
/*
* 上架下架表
* gen by 代码生成器 2023-07-20
*/

@Table(name="mc.som_amazon_shelves_record")
public class SomAmazonShelvesRecord implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 批次
	 */
	@Column("batch")
	private String batch ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * Merchant/Amazon
	 */
	@Column("fulfillment_by")
	private String fulfillmentBy ;
	/**
	 * Listing状态
	 */
	@Column("listing_status")
	private String listingStatus ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * B2B价格
	 */
	@Column("business_price")
	private BigDecimal businessPrice ;
	/**
	 * 可售库存
	 */
	@Column("quantity")
	private Integer quantity ;
	/**
	 * 站点可售库存
	 */
	@Column("site_quantity")
	private Integer siteQuantity ;
	/**
	 * 安全库存
	 */
	@Column("safety_stock")
	private Integer safetyStock ;
	/**
	 * 7天DMS
	 */
	@Column("seven_day_dms")
	private BigDecimal sevenDayDms ;
	/**
	 * 上下架理由   10.库存原因 20.PAN-EU物流资格激活
	 */
	@Column("shelves_reason")
	private Integer shelvesReason ;
	/**
	 * 10.上架 20.下架
	 */
	@Column("put_on_or_off")
	private Integer putOnOrOff ;
	/**
	 * 生成AWS S3文件时间
	 */
	@Column("generated_time")
	private Date generatedTime ;
	/**
	 * 生成文件的URL
	 */
	@Column("file_url")
	private String fileUrl ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	@Column("rpa_operation_status")
	private Integer rpaOperationStatus;

	@Column("fail_message")
	private String failMessage ;

	public SomAmazonShelvesRecord() {
	}

	public Integer getRpaOperationStatus() {
		return rpaOperationStatus;
	}

	public void setRpaOperationStatus(Integer rpaOperationStatus) {
		this.rpaOperationStatus = rpaOperationStatus;
	}

	public String getFailMessage() {
		return failMessage;
	}

	public void setFailMessage(String failMessage) {
		this.failMessage = failMessage;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 批次
	*@return
	*/
	public String getBatch(){
		return  batch;
	}
	/**
	* 批次
	*@param  batch
	*/
	public void setBatch(String batch ){
		this.batch = batch;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* Merchant/Amazon
	*@return
	*/
	public String getFulfillmentBy(){
		return  fulfillmentBy;
	}
	/**
	* Merchant/Amazon
	*@param  fulfillmentBy
	*/
	public void setFulfillmentBy(String fulfillmentBy ){
		this.fulfillmentBy = fulfillmentBy;
	}
	/**
	* Listing状态
	*@return
	*/
	public String getListingStatus(){
		return  listingStatus;
	}
	/**
	* Listing状态
	*@param  listingStatus
	*/
	public void setListingStatus(String listingStatus ){
		this.listingStatus = listingStatus;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* B2B价格
	*@return
	*/
	public BigDecimal getBusinessPrice(){
		return  businessPrice;
	}
	/**
	* B2B价格
	*@param  businessPrice
	*/
	public void setBusinessPrice(BigDecimal businessPrice ){
		this.businessPrice = businessPrice;
	}
	/**
	* 可售库存
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 可售库存
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	/**
	* 站点可售库存
	*@return
	*/
	public Integer getSiteQuantity(){
		return  siteQuantity;
	}
	/**
	* 站点可售库存
	*@param  siteQuantity
	*/
	public void setSiteQuantity(Integer siteQuantity ){
		this.siteQuantity = siteQuantity;
	}
	/**
	* 安全库存
	*@return
	*/
	public Integer getSafetyStock(){
		return  safetyStock;
	}
	/**
	* 安全库存
	*@param  safetyStock
	*/
	public void setSafetyStock(Integer safetyStock ){
		this.safetyStock = safetyStock;
	}

	public BigDecimal getSevenDayDms() {
		return sevenDayDms;
	}

	public void setSevenDayDms(BigDecimal sevenDayDms) {
		this.sevenDayDms = sevenDayDms;
	}

	public Integer getShelvesReason() {
		return shelvesReason;
	}

	public void setShelvesReason(Integer shelvesReason) {
		this.shelvesReason = shelvesReason;
	}

	/**
	* 10.上架 20.下架
	*@return
	*/
	public Integer getPutOnOrOff(){
		return  putOnOrOff;
	}
	/**
	* 10.上架 20.下架
	*@param  putOnOrOff
	*/
	public void setPutOnOrOff(Integer putOnOrOff ){
		this.putOnOrOff = putOnOrOff;
	}
	/**
	* 生成AWS S3文件时间
	*@return
	*/
	public Date getGeneratedTime(){
		return  generatedTime;
	}
	/**
	* 生成AWS S3文件时间
	*@param  generatedTime
	*/
	public void setGeneratedTime(Date generatedTime ){
		this.generatedTime = generatedTime;
	}
	/**
	* 生成文件的URL
	*@return
	*/
	public String getFileUrl(){
		return  fileUrl;
	}
	/**
	* 生成文件的URL
	*@param  fileUrl
	*/
	public void setFileUrl(String fileUrl ){
		this.fileUrl = fileUrl;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
