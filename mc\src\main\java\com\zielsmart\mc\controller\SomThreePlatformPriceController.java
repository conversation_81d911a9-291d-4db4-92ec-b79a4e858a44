package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomThreePlatformPriceService;
import com.zielsmart.mc.vo.SomThreePlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomThreePlatformPriceController
 * @description
 * @date 2024-11-13 13:58:16
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somThreePlatformPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "三方平台价格推送表管理")
public class SomThreePlatformPriceController extends BasicController{

    @Resource
    SomThreePlatformPriceService somThreePlatformPriceService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomThreePlatformPriceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomThreePlatformPriceVo>> queryByPage(@RequestBody SomThreePlatformPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somThreePlatformPriceService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somThreePlatformPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomThreePlatformPriceVo somThreePlatformPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somThreePlatformPriceService.save(somThreePlatformPriceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somThreePlatformPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomThreePlatformPriceVo somThreePlatformPriceVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somThreePlatformPriceService.update(somThreePlatformPriceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somThreePlatformPriceVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomThreePlatformPriceVo somThreePlatformPriceVo) throws ValidateException {
        somThreePlatformPriceService.delete(somThreePlatformPriceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomThreePlatformPricePageSearchVo searchVo){
        String data = somThreePlatformPriceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
