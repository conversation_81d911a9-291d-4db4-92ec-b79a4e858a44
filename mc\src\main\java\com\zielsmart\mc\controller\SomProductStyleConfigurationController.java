package com.zielsmart.mc.controller;

import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.SomProductStyleConfigurationService;
import com.zielsmart.mc.vo.SomProductStyleConfigurationPageSearchVo;
import com.zielsmart.mc.vo.SomProductStyleConfigurationVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomProductStyleConfigurationController
 * @description
 * @date 2024-04-11 15:01:10
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somProductStyleConfiguration", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "产品风格配置表管理")
public class SomProductStyleConfigurationController extends BasicController {

    @Resource
    SomProductStyleConfigurationService somProductStyleConfigurationService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomProductStyleConfigurationVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomProductStyleConfigurationVo>> queryByPage(@RequestBody SomProductStyleConfigurationPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somProductStyleConfigurationService.queryByPage(searchVo));
    }

    /**
     * enableOrDisable
     * 启用/禁用
     *
     * @param somProductStyleConfigurationVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "启用/禁用")
    @PostMapping(value = "/enableOrDisable")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> enableOrDisable(@RequestBody @Validated SomProductStyleConfigurationVo somProductStyleConfigurationVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somProductStyleConfigurationService.enableOrDisable(somProductStyleConfigurationVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryStyleList
     * 查询风格下拉选集合
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<java.lang.String>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询风格下拉选集合")
    @PostMapping(value = "/queryStyleList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<String>> queryStyleList() {
        return ResultVo.ofSuccess(somProductStyleConfigurationService.queryStyleList());
    }

    /**
     * upload
     * 上传图片
     *
     * @param param
     * @param multipartFiles
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<cn.hutool.json.JSONObject>>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "上传图片{aid:'',typeName:'',type:''}")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> upload(@RequestParam Map param, @RequestParam("files") MultipartFile[] multipartFiles, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        return ResultVo.ofSuccess(somProductStyleConfigurationService.upload(param, multipartFiles, tokenUser));
    }

    /**
     * addSort
     * 增加排序
     *
     * @param somProductStyleConfigurationVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "增加排序")
    @PostMapping(value = "/addSort")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addSort(@RequestBody SomProductStyleConfigurationVo somProductStyleConfigurationVo) throws ValidateException {
        somProductStyleConfigurationService.addSort(somProductStyleConfigurationVo);
        return ResultVo.ofSuccess(null);
    }
}
