package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
/*
 * coupon活动从表
 * gen by 代码生成器 2022-04-21
 */

@Table(name = "mc.som_coupon_items")
public class SomCouponItems implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 主表主键
     */
    @Column("coupon_id")
    private String couponId;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * SKU等级
     */
    @Column("sku_grade")
    private String skuGrade;
    /**
     * 三级分类编码
     */
    @Column("category_code")
    private String categoryCode;
    /**
     * 售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 目标价
     */
    @Column("target_price")
    private BigDecimal targetPrice;
    /**
     * 价格折扣
     */
    @Column("price_discount")
    private BigDecimal priceDiscount;
    /**
     * Coupon折扣
     */
    @Column("coupon_discount")
    private BigDecimal couponDiscount;
    /**
     * Promotion折扣
     */
    @Column("promotion_discount")
    private BigDecimal promotionDiscount;
    /**
     * Deal折扣
     */
    @Column("deal_discount")
    private BigDecimal dealDiscount;
    /**
     * 总折扣
     */
    @Column("total_discount")
    private BigDecimal totalDiscount;
    /**
     * 总折扣金额
     */
    @Column("total_discount_amount")
    private BigDecimal totalDiscountAmount;
    /**
     * 预估成交价
     */
    @Column("transaction_price")
    private BigDecimal transactionPrice;
    /**
     * 业务组
     */
    @Column("business_group_code")
    private String businessGroupCode;
    /**
     * 业务负责人
     */
    @Column("business_leader_code")
    private String businessLeaderCode;
    /**
     * 业务助理
     */
    @Column("business_operation_code")
    private String businessOperationCode;
    /**
     * 货币符号
     */
    @Column("currency_symbol")
    private String currencySymbol;
    /**
     * 库存可售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;

    public SomCouponItems() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 主表主键
     *
     * @return
     */
    public String getCouponId() {
        return couponId;
    }

    /**
     * 主表主键
     *
     * @param couponId
     */
    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * SKU
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * SKU等级
     *
     * @return
     */
    public String getSkuGrade() {
        return skuGrade;
    }

    /**
     * SKU等级
     *
     * @param skuGrade
     */
    public void setSkuGrade(String skuGrade) {
        this.skuGrade = skuGrade;
    }

    /**
     * 三级分类编码
     *
     * @return
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 三级分类编码
     *
     * @param categoryCode
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 目标价
     *
     * @return
     */
    public BigDecimal getTargetPrice() {
        return targetPrice;
    }

    /**
     * 目标价
     *
     * @param targetPrice
     */
    public void setTargetPrice(BigDecimal targetPrice) {
        this.targetPrice = targetPrice;
    }

    /**
     * 价格折扣
     *
     * @return
     */
    public BigDecimal getPriceDiscount() {
        return priceDiscount;
    }

    /**
     * 价格折扣
     *
     * @param priceDiscount
     */
    public void setPriceDiscount(BigDecimal priceDiscount) {
        this.priceDiscount = priceDiscount;
    }

    /**
     * Coupon折扣
     *
     * @return
     */
    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    /**
     * Coupon折扣
     *
     * @param couponDiscount
     */
    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    /**
     * Promotion折扣
     *
     * @return
     */
    public BigDecimal getPromotionDiscount() {
        return promotionDiscount;
    }

    /**
     * Promotion折扣
     *
     * @param promotionDiscount
     */
    public void setPromotionDiscount(BigDecimal promotionDiscount) {
        this.promotionDiscount = promotionDiscount;
    }

    /**
     * Deal折扣
     *
     * @return
     */
    public BigDecimal getDealDiscount() {
        return dealDiscount;
    }

    /**
     * Deal折扣
     *
     * @param dealDiscount
     */
    public void setDealDiscount(BigDecimal dealDiscount) {
        this.dealDiscount = dealDiscount;
    }

    /**
     * 总折扣
     *
     * @return
     */
    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    /**
     * 总折扣
     *
     * @param totalDiscount
     */
    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    /**
     * 总折扣金额
     *
     * @return
     */
    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    /**
     * 总折扣金额
     *
     * @param totalDiscountAmount
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    /**
     * 预估成交价
     *
     * @return
     */
    public BigDecimal getTransactionPrice() {
        return transactionPrice;
    }

    /**
     * 预估成交价
     *
     * @param transactionPrice
     */
    public void setTransactionPrice(BigDecimal transactionPrice) {
        this.transactionPrice = transactionPrice;
    }

    /**
     * 业务组
     *
     * @return
     */
    public String getBusinessGroupCode() {
        return businessGroupCode;
    }

    /**
     * 业务组
     *
     * @param businessGroupCode
     */
    public void setBusinessGroupCode(String businessGroupCode) {
        this.businessGroupCode = businessGroupCode;
    }

    /**
     * 业务负责人
     *
     * @return
     */
    public String getBusinessLeaderCode() {
        return businessLeaderCode;
    }

    /**
     * 业务负责人
     *
     * @param businessLeaderCode
     */
    public void setBusinessLeaderCode(String businessLeaderCode) {
        this.businessLeaderCode = businessLeaderCode;
    }

    /**
     * 业务助理
     *
     * @return
     */
    public String getBusinessOperationCode() {
        return businessOperationCode;
    }

    /**
     * 业务助理
     *
     * @param businessOperationCode
     */
    public void setBusinessOperationCode(String businessOperationCode) {
        this.businessOperationCode = businessOperationCode;
    }

    /**
     * 货币符号
     *
     * @return
     */
    public String getCurrencySymbol() {
        return currencySymbol;
    }

    /**
     * 货币符号
     *
     * @param currencySymbol
     */
    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public Integer getStockSaleDays() {
        return stockSaleDays;
    }

    public void setStockSaleDays(Integer stockSaleDays) {
        this.stockSaleDays = stockSaleDays;
    }
}
