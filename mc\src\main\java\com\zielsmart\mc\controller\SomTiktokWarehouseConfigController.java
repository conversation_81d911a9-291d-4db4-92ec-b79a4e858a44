package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTiktokWarehouseConfigService;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTiktokWarehouseConfigController
 * @description
 * @date 2024-12-13 12:21:48
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTiktokWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Tiktok可售仓库配置管理")
public class SomTiktokWarehouseConfigController extends BasicController {

    @Resource
    SomTiktokWarehouseConfigService somTiktokWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTiktokWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTiktokWarehouseConfigVo>> queryByPage(@RequestBody SomTiktokWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTiktokWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somTiktokWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTiktokWarehouseConfigService.save(somTiktokWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somTiktokWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTiktokWarehouseConfigService.update(somTiktokWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somTiktokWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo) throws ValidateException {
        somTiktokWarehouseConfigService.delete(somTiktokWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "店铺列表")
    @PostMapping(value = "/shop-cipher")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<Map>> shopCipher() {
        return ResultVo.ofSuccess(somTiktokWarehouseConfigService.shopCipher());
    }


    @Operation(summary = "仓库列表")
    @PostMapping(value = "/warehouse-by-shop-cipher")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<Map>> warehouseList(@RequestBody SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo) throws ValidateException {
        return ResultVo.ofSuccess(somTiktokWarehouseConfigService.warehouseList(somTiktokWarehouseConfigVo));
    }


}
