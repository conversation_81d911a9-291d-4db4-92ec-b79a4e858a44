package com.zielsmart.mc.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class SomTemuImageUrlReport {

    @Excel(name = "平台")
    private String platform;

    @Excel(name = "站点")
    private String site;

    @Excel(name = "产品编码")
    private String productMainCode;

    @Excel(name = "展示码")
    private String sellerSku;

    @Excel(name = "图片名称")
    private String imgName;

    @Excel(name = "图片URL")
    private String imgUrl;

    private String displayProductCode;
}
