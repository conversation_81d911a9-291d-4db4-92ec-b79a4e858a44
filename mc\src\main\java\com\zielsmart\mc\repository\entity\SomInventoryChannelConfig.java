package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 库存渠道配置
* gen by 代码生成器 2023-09-01
*/

@Table(name="mc.som_inventory_channel_config")
public class SomInventoryChannelConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 发货方式 0.非寄售 1.寄售
	 */
	@Column("delivery_type")
	private Integer deliveryType ;
	/**
	 * 库存市场
	 */
	@Column("storage_market")
	private String storageMarket ;
	/**
	 * 仓库编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 库区编码
	 */
	@Column("storage_location")
	private String storageLocation ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public SomInventoryChannelConfig() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 发货方式 0.非寄售 1.寄售
	*@return
	*/
	public Integer getDeliveryType(){
		return  deliveryType;
	}
	/**
	* 发货方式 0.非寄售 1.寄售
	*@param  deliveryType
	*/
	public void setDeliveryType(Integer deliveryType ){
		this.deliveryType = deliveryType;
	}
	/**
	* 库存市场
	*@return
	*/
	public String getStorageMarket(){
		return  storageMarket;
	}
	/**
	* 库存市场
	*@param  storageMarket
	*/
	public void setStorageMarket(String storageMarket ){
		this.storageMarket = storageMarket;
	}
	/**
	* 仓库编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 仓库编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 库区编码
	*@return
	*/
	public String getStorageLocation(){
		return  storageLocation;
	}
	/**
	* 库区编码
	*@param  storageLocation
	*/
	public void setStorageLocation(String storageLocation ){
		this.storageLocation = storageLocation;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 最后修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
