package com.zielsmart.mc.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.zielsmart.eya.amazons3.starter.PathMatchingSimpleStorageResourcePatternResolver;
import com.zielsmart.web.basic.file.IFileHandler;
import com.zielsmart.web.basic.file.s3.S3FileHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @version V2.0
 * @title: FileConfig
 * @package: com.zielsmart.eya.basic.config
 * @description:
 * @author: 李耀华
 * @date: 2020-01-0316:45
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
public class FileConfig {

    @Value("${aws.s3.domain}")
    private String domain;
    @Value("${aws.s3.bucket}")
    private String bucket;

    @Value("${aws.s3china.domain}")
    private String chinaDomain;
    @Value("${aws.s3china.bucket}")
    private String chinaBucket;

    @Bean
    @Primary
    public IFileHandler s3FileHandler(@Qualifier("amazonS3")AmazonS3 s3, PathMatchingSimpleStorageResourcePatternResolver patternResolver) {
        return new S3FileHandler(s3, patternResolver, bucket, domain);
    }

    @Bean
    @Qualifier("amazonS3China")
    public AmazonS3 amazonS3China() {
        AmazonS3 amazonS3 = (AmazonS3)((AmazonS3ClientBuilder)((AmazonS3ClientBuilder) AmazonS3Client.builder()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials("********************", "G/kwcHvq1ZiM+rPKQa5xQe4wUJTORhwIyMMNeUYw")))).withRegion("cn-northwest-1"))
                .build();
        return amazonS3;
    }

    @Bean
    @Qualifier("s3FileHandlerChina")
    public IFileHandler s3FileHandlerChina(@Qualifier("amazonS3China")AmazonS3 amazonS3China, PathMatchingSimpleStorageResourcePatternResolver patternResolver) {
        return new S3FileHandler(amazonS3China, patternResolver, chinaBucket, chinaDomain);
    }

}
