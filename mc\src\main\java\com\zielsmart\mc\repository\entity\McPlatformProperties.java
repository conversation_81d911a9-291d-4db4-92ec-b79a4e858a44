package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 平台站点属性配置
* gen by 代码生成器 2021-08-04
*/

@Table(name="mc.mc_platform_properties")
public class McPlatformProperties implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 市场
	 */
	@Column("market")
	private String market ;
	/**
	 * 0：不自动推送 1：自动推送
	 */
	@Column("auto_flag")
	private Integer autoFlag ;
	/**
	 * 站点链接
	 */
	@Column("link_url")
	private String linkUrl ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	/**
	 * 安全库存
	 */
	@Column("safety_stock")
	private Integer safetyStock ;

	/**
	 * 库存推送频次
	 */
	@Column("sync_frequency")
	private Integer syncFrequency ;

	/**
	 * 币种
	 */
	@Column("currency_code")
	private String currencyCode ;

	/**
	 * handling_time
	 */
	@Column("handling_time")
	private Integer handlingTime ;

	/**
	 * 国家
	 */
	@Column("country")
	private String country ;

	/**
	 * 渠道
	 */
	@Column("sale_channel")
	private String saleChannel ;

	/**
	 * AFN邀评
	 */
	@Column("review_afn_flag")
	private Integer reviewAfnFlag;

	/**
	 * MFN邀评
	 */
	@Column("review_mfn_flag")
	private Integer reviewMfnFlag;

	/**
	 * 跟卖提醒系数
	 */
	@Column("sell_remind_ratio")
	private Short sellRemindRatio;

	public McPlatformProperties() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 市场
	*@return
	*/
	public String getMarket(){
		return  market;
	}
	/**
	* 市场
	*@param  market
	*/
	public void setMarket(String market ){
		this.market = market;
	}
	/**
	* 0：不自动推送 1：自动推送
	*@return
	*/
	public Integer getAutoFlag(){
		return  autoFlag;
	}
	/**
	* 0：不自动推送 1：自动推送
	*@param  autoFlag
	*/
	public void setAutoFlag(Integer autoFlag ){
		this.autoFlag = autoFlag;
	}
	/**
	* 站点链接
	*@return
	*/
	public String getLinkUrl(){
		return  linkUrl;
	}
	/**
	* 站点链接
	*@param  linkUrl
	*/
	public void setLinkUrl(String linkUrl ){
		this.linkUrl = linkUrl;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Integer getHandlingTime() {
		return handlingTime;
	}

	public void setHandlingTime(Integer handlingTime) {
		this.handlingTime = handlingTime;
	}

	public Integer getSafetyStock() {
		return safetyStock;
	}

	public void setSafetyStock(Integer safetyStock) {
		this.safetyStock = safetyStock;
	}

	public Integer getSyncFrequency() {
		return syncFrequency;
	}

	public void setSyncFrequency(Integer syncFrequency) {
		this.syncFrequency = syncFrequency;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getSaleChannel() {
		return saleChannel;
	}

	public void setSaleChannel(String saleChannel) {
		this.saleChannel = saleChannel;
	}

	public Integer getReviewAfnFlag() {
		return reviewAfnFlag;
	}

	public void setReviewAfnFlag(Integer reviewAfnFlag) {
		this.reviewAfnFlag = reviewAfnFlag;
	}

	public Integer getReviewMfnFlag() {
		return reviewMfnFlag;
	}

	public void setReviewMfnFlag(Integer reviewMfnFlag) {
		this.reviewMfnFlag = reviewMfnFlag;
	}

	public Short getSellRemindRatio() {
		return sellRemindRatio;
	}

	public void setSellRemindRatio(Short sellRemindRatio) {
		this.sellRemindRatio = sellRemindRatio;
	}

}
