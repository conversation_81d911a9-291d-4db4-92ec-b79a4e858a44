package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 
* gen by 代码生成器 2025-04-03
*/

@Table(name="mc.som_but_auto_push_stock_record")
public class SomButAutoPushStockRecord implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private String aid ;
	/**
	 * 批次
	 */
	@Column("batch_id")
	private String batchId ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码id__offer
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * EAN编码
	 */
	@Column("product_id")
	private String productId ;
	/**
	 * 库存数量
	 */
	@Column("stock")
	private Integer stock ;
	/**
	 * 安全库存
	 */
	@Column("safe_stock")
	private Integer safeStock ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 推送状态10.未推送20.推送成功99.推送失败
	 */
	@Column("sync_status")
	private Integer syncStatus ;
	/**
	 * 推送失败原因
	 */
	@Column("sync_error_msg")
	private String syncErrorMsg ;
	/**
	 * 推送时间
	 */
	@Column("sync_time")
	private Date syncTime ;
	/**
	 * 全部数据json str
	 */
	@Column("json_data")
	private Object jsonData ;

	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;


	public SomButAutoPushStockRecord() {
	}

	public String getModifyNum() {
		return modifyNum;
	}

	public void setModifyNum(String modifyNum) {
		this.modifyNum = modifyNum;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	/**
	* 主键id
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键id
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 批次
	*@return
	*/
	public String getBatchId(){
		return  batchId;
	}
	/**
	* 批次
	*@param  batchId
	*/
	public void setBatchId(String batchId ){
		this.batchId = batchId;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码id__offer
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码id__offer
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* EAN编码
	*@return
	*/
	public String getProductId(){
		return  productId;
	}
	/**
	* EAN编码
	*@param  productId
	*/
	public void setProductId(String productId ){
		this.productId = productId;
	}
	/**
	* 库存数量
	*@return
	*/
	public Integer getStock(){
		return  stock;
	}
	/**
	* 库存数量
	*@param  stock
	*/
	public void setStock(Integer stock ){
		this.stock = stock;
	}
	/**
	* 安全库存
	*@return
	*/
	public Integer getSafeStock(){
		return  safeStock;
	}
	/**
	* 安全库存
	*@param  safeStock
	*/
	public void setSafeStock(Integer safeStock ){
		this.safeStock = safeStock;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 推送状态10.未推送20.推送成功99.推送失败
	*@return
	*/
	public Integer getSyncStatus(){
		return  syncStatus;
	}
	/**
	* 推送状态10.未推送20.推送成功99.推送失败
	*@param  syncStatus
	*/
	public void setSyncStatus(Integer syncStatus ){
		this.syncStatus = syncStatus;
	}
	/**
	* 推送失败原因
	*@return
	*/
	public String getSyncErrorMsg(){
		return  syncErrorMsg;
	}
	/**
	* 推送失败原因
	*@param  syncErrorMsg
	*/
	public void setSyncErrorMsg(String syncErrorMsg ){
		this.syncErrorMsg = syncErrorMsg;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 推送时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}
	/**
	* 全部数据json str
	*@return
	*/
	public Object getJsonData(){
		return  jsonData;
	}
	/**
	* 全部数据json str
	*@param  jsonData
	*/
	public void setJsonData(Object jsonData ){
		this.jsonData = jsonData;
	}

}
