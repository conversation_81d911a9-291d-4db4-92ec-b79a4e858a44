package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomFreeAddDealItems;
import com.zielsmart.mc.vo.SomFreeAddDealItemsExtVo;
import com.zielsmart.mc.vo.SomFreeAddDealItemsPageSearchVo;
import com.zielsmart.mc.vo.SomFreeAddDealItemsVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2022-08-11
*/

@SqlResource("somFreeAddDealItems")
public interface SomFreeAddDealItemsMapper extends BaseMapper<SomFreeAddDealItems> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomFreeAddDealItemsVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomFreeAddDealItemsVo> queryByPage(@Param("searchVo")SomFreeAddDealItemsPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryItemsVoList
     * 根据主表主键查询从表明细
     * @param aidList
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomFreeAddDealItemsExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomFreeAddDealItemsExtVo> queryItemsVoList(@Param("aidList")List<String> aidList);

    default void updateAsinStatus(@Param("itemList") List<SomFreeAddDealItemsExtVo> itemList){
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDealItems.updateAsinStatus"), itemList);
    }

    /**
     * 批量导入编辑
     *
     * @param updateSomFreeAddDealItems 需要更新的数据
     */
    default void batchImportEdit(@Param("updateSomFreeAddDealItems") List<SomFreeAddDealItems> updateSomFreeAddDealItems) {
        if (CollUtil.isEmpty(updateSomFreeAddDealItems)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDealItems.batchImportEdit"), updateSomFreeAddDealItems);
    }
}
