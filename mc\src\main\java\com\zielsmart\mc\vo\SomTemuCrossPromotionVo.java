package com.zielsmart.mc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/*
 * Temu跨境（香港半托）营销活动明细的VO实体对象
 */
@Data
@Schema(title = "Temu跨境（香港半托）营销活动明细", name = "SomTemuCrossPromotionVo")
public class SomTemuCrossPromotionVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "站点集合", name = "sites")
    private String sites;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "产品ID", name = "productId")
    private String productId;

    @Schema(description = "商品ID", name = "goodsId")
    private String goodsId;

    @Schema(description = "活动类型", name = "activityType")
    private Integer activityType;

    @Schema(description = "活动类型名称", name = "activityTypeName")
    private String activityTypeName;

    @Schema(description = "活动起始时间", name = "sessionStartTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sessionStartTime;

    @Schema(description = "活动截止时间", name = "sessionEndTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sessionEndTime;

    @Schema(description = "活动状态：1. 未开始 2. 运行中 3. 已结束", name = "activityStatus")
    private Integer activityStatus;

    @Schema(description = "是否服饰类的活动：1. 是 0.否。服饰类需要关心SKC维度，否则，其他类需要关心SKU维度", name = "isApparel")
    private Integer isApparel;

    @Schema(description = "是否售罄 0. 正常  1. 即将售罄 2. 已售罄", name = "soldStatus")
    private Integer soldStatus;

    @Schema(description = "活动剩余库存", name = "remainingActivityQuantity")
    private Integer remainingActivityQuantity;

    @Schema(description = "活动主题ID", name = "activityThematicId")
    private String activityThematicId;

    @Schema(description = "活动主题名称", name = "activityThematicName")
    private String activityThematicName;

    @Schema(description = "报名记录ID", name = "enrollId")
    private String enrollId;

    @Schema(description = "此产品报名此活动的时间", name = "enrollTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enrollTime;

    @Schema(description = "报名状态：1.报名中 2.报名失败 3.报名成功待分配场次 4.报名成功已分配场次 5.报名活动已结束 6.报名活动已下线", name = "enrollStatus")
    private Integer enrollStatus;

    @Schema(description = "提报的库存数量", name = "activityStock")
    private Integer activityStock;

    @Schema(description = "币种", name = "currency")
    private String currency;

    @Schema(description = "创建时间", name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "店铺名称", name = "accountName")
    private String accountName;

    @Schema(description = "活动状态描述：1. 未开始 2. 运行中 3. 已结束", name = "activityStatusDesc")
    private String activityStatusDesc;

    @Schema(description = "活动类型描述", name = "activityTypeDesc")
    private String activityTypeDesc;

    @Schema(description = "报名状态描述", name = "enrollStatusDesc")
    private String enrollStatusDesc;
}
