package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonSimplePublishCategoryTemplatePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishCategoryTemplateVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Amazon简单上货类目模板配置管理
 * @date 2025-07-17 10:24:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somAmazonSimplePublishCategoryTemplate")
public interface SomAmazonSimplePublishCategoryTemplateMapper extends BaseMapper<SomAmazonSimplePublishCategoryTemplate> {

    /**
     * 分页查询
     *
     * @param searchVo 查询参数
     * @param pageRequest 入参
     * @return PageResult<SomAmazonSimplePublishCategoryTemplateVo>
     */
    PageResult<SomAmazonSimplePublishCategoryTemplateVo> queryByPage(@Param("searchVo")SomAmazonSimplePublishCategoryTemplatePageSearchVo searchVo, PageRequest pageRequest);
}
