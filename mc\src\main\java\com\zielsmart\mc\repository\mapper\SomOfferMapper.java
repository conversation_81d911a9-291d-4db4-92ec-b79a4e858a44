package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomOffer;
import com.zielsmart.mc.vo.SomOfferExportVo;
import com.zielsmart.mc.vo.SomOfferExtVo;
import com.zielsmart.mc.vo.SomOfferHistoryVo;
import com.zielsmart.mc.vo.SomOfferPageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-11-30
 */

@SqlResource("somOffer")
public interface SomOfferMapper extends BaseMapper<SomOffer> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOfferExtVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomOfferExtVo> queryByPage(@Param("searchVo") SomOfferPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryHistory
     * 查询历史记录
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOfferHistoryVo>}
     * <AUTHOR>
     * @history
     */
    List<SomOfferHistoryVo> queryHistory(@Param("searchVo") SomOfferPageSearchVo searchVo);

    /**
     * queryExportData
     * 导出
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOfferExportVo>}
     * <AUTHOR>
     * @history
     */
    List<SomOfferExportVo> queryExportData(@Param("searchVo")SomOfferPageSearchVo searchVo);
}
