package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* OTTO运费模版管理
* gen by 代码生成器 2024-09-20
*/

@Table(name="mc.som_otto_delivery_template")
public class SomOttoDeliveryTemplate implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 运费模版ID 平台返回
	 */
	@Column("shipping_profile_id")
	private String shippingProfileId ;
	/**
	 * 运费模版名称
	 */
	@Column("shipping_profile_name")
	private String shippingProfileName ;
	/**
	 * 工作时间
	 */
	@Column("working_days")
	private String workingDays ;
	/**
	 * 截单时间 24小时制 最小半小时
	 */
	@Column("order_cut_off")
	private String orderCutoff ;
	/**
	 * 投递类型 字典OttoDeliveryType
	 */
	@Column("delivery_type")
	private String deliveryType ;
	/**
	 * 默认发货时效 天
	 */
	@Column("default_processing_time")
	private Integer defaultProcessingTime ;
	/**
	 * 运输周期 天
	 */
	@Column("transport_time")
	private Integer transportTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomOttoDeliveryTemplate() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 运费模版ID 平台返回
	*@return
	*/
	public String getShippingProfileId(){
		return  shippingProfileId;
	}
	/**
	* 运费模版ID 平台返回
	*@param  shippingProfileId
	*/
	public void setShippingProfileId(String shippingProfileId ){
		this.shippingProfileId = shippingProfileId;
	}
	/**
	* 运费模版名称
	*@return
	*/
	public String getShippingProfileName(){
		return  shippingProfileName;
	}
	/**
	* 运费模版名称
	*@param  shippingProfileName
	*/
	public void setShippingProfileName(String shippingProfileName ){
		this.shippingProfileName = shippingProfileName;
	}
	/**
	* 工作时间
	*@return
	*/
	public String getWorkingDays(){
		return  workingDays;
	}
	/**
	* 工作时间
	*@param  workingDays
	*/
	public void setWorkingDays(String workingDays ){
		this.workingDays = workingDays;
	}
	/**
	* 截单时间 24小时制 最小半小时
	*@return
	*/
	public String getOrderCutoff(){
		return  orderCutoff;
	}
	/**
	* 截单时间 24小时制 最小半小时
	*@param  orderCutOff
	*/
	public void setOrderCutoff(String orderCutoff ){
		this.orderCutoff = orderCutoff;
	}
	/**
	* 投递类型 字典OttoDeliveryType
	*@return
	*/
	public String getDeliveryType(){
		return  deliveryType;
	}
	/**
	* 投递类型 字典OttoDeliveryType
	*@param  deliveryType
	*/
	public void setDeliveryType(String deliveryType ){
		this.deliveryType = deliveryType;
	}
	/**
	* 默认发货时效 天
	*@return
	*/
	public Integer getDefaultProcessingTime(){
		return  defaultProcessingTime;
	}
	/**
	* 默认发货时效 天
	*@param  defaultProcessingTime
	*/
	public void setDefaultProcessingTime(Integer defaultProcessingTime ){
		this.defaultProcessingTime = defaultProcessingTime;
	}
	/**
	* 运输时效 天
	*@return
	*/
	public Integer getTransportTime(){
		return  transportTime;
	}
	/**
	* 运输时效 天
	*@param  transportTime
	*/
	public void setTransportTime(Integer transportTime ){
		this.transportTime = transportTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
