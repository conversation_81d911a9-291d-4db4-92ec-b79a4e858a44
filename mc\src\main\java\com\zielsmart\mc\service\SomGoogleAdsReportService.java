package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.ObjectUtil;
import com.zielsmart.mc.repository.entity.SomGoogleAdsReport;
import com.zielsmart.mc.repository.mapper.SomGoogleAdsReportMapper;
import com.zielsmart.mc.vo.SomGoogleAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomGoogleAdsReportVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomGoogleAdsReportService {
    
    @Resource
    private SomGoogleAdsReportMapper somGoogleAdsReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomGoogleAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomGoogleAdsReportVo> queryByPage(SomGoogleAdsReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomGoogleAdsReportVo> pageResult = dynamicSqlManager.getMapper(SomGoogleAdsReportMapper.class).queryByPage(searchVo, pageRequest);
        for (SomGoogleAdsReportVo vo : pageResult.getList()) {
            if (ObjectUtil.isNotNull(vo.getCost())) {
                Long cost = vo.getCost();
                BigDecimal result = BigDecimal.valueOf(cost).divide(BigDecimal.valueOf(1000000), 2, BigDecimal.ROUND_HALF_UP);
                vo.setCostRound(result);
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomGoogleAdsReportVo.class, searchVo);
    }

    public String export(SomGoogleAdsReportPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomGoogleAdsReportVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Google广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomGoogleAdsReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public List<SomGoogleAdsReportVo> getList(Map map) {
        if (!map.containsKey("type")) {
            return null;
        }
        String type = map.get("type").toString();
        List<SomGoogleAdsReport> list;
        if ("site".equals(type)) {
            list = somGoogleAdsReportMapper.createLambdaQuery().distinct()
                    .andIsNotNull("labels_on_campaign")
                    .select("labels_on_campaign");
        } else if ("account".equals(type)) {
            list = somGoogleAdsReportMapper.createLambdaQuery().distinct()
                    .andIsNotNull("account_name")
                    .select("account_name");
        }else{
            return null;
        }
        return ConvertUtils.listConvert(list,SomGoogleAdsReportVo.class);
    }
}
