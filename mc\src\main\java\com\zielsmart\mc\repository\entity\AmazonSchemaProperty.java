package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
 * 亚马逊字段映射
 * gen by 代码生成器 2025-07-28
 */

@Table(name = "mc.amazon_schema_property")
public class AmazonSchemaProperty implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 亚马逊最小字段
     */
    @Column("amazon")
    private String amazon;
    /**
     * 基本字段类型
     */
    @Column("type")
    private String type;
    /**
     * 标题
     */
    @Column("title")
    private String title;
    /**
     * 类目
     */
    @Column("category_id")
    private String categoryId;
    /**
     * mdm字段
     */
    @Column("mdm")
    private String mdm;
    /**
     * 默认单位
     */
    @Column("default_unit")
    private String defaultUnit;
    /**
     * 是否必填
     */
    @Column("required")
    private Boolean required;
    /**
     * 枚举值
     */
    @Column("enum_value")
    private Object enumValue;
    /**
     * 枚举名称
     */
    @Column("enum_names")
    private Object enumNames;
    /**
     * 描述
     */
    @Column("description")
    private String description;
    /**
     * 在json中的类型
     */
    @Column("json_type")
    private String jsonType;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 示例数据
     */
    @Column("examples")
    private Object examples;

    public AmazonSchemaProperty() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 亚马逊最小字段
     *
     * @return
     */
    public String getAmazon() {
        return amazon;
    }

    /**
     * 亚马逊最小字段
     *
     * @param amazon
     */
    public void setAmazon(String amazon) {
        this.amazon = amazon;
    }

    /**
     * 基本字段类型
     *
     * @return
     */
    public String getType() {
        return type;
    }

    /**
     * 基本字段类型
     *
     * @param type
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 标题
     *
     * @return
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 类目
     *
     * @return
     */
    public String getCategoryId() {
        return categoryId;
    }

    /**
     * 类目
     *
     * @param categoryId
     */
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * mdm字段
     *
     * @return
     */
    public String getMdm() {
        return mdm;
    }

    /**
     * mdm字段
     *
     * @param mdm
     */
    public void setMdm(String mdm) {
        this.mdm = mdm;
    }

    /**
     * 默认单位
     *
     * @return
     */
    public String getDefaultUnit() {
        return defaultUnit;
    }

    /**
     * 默认单位
     *
     * @param defaultUnit
     */
    public void setDefaultUnit(String defaultUnit) {
        this.defaultUnit = defaultUnit;
    }

    /**
     * 是否必填
     *
     * @return
     */
    public Boolean getRequired() {
        return required;
    }

    /**
     * 是否必填
     *
     * @param required
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    /**
     * 枚举值
     *
     * @return
     */
    public Object getEnumValue() {
        return enumValue;
    }

    /**
     * 枚举值
     *
     * @param enumValue
     */
    public void setEnumValue(Object enumValue) {
        this.enumValue = enumValue;
    }

    /**
     * 枚举名称
     *
     * @return
     */
    public Object getEnumNames() {
        return enumNames;
    }

    /**
     * 枚举名称
     *
     * @param enumNames
     */
    public void setEnumNames(Object enumNames) {
        this.enumNames = enumNames;
    }

    /**
     * 描述
     *
     * @return
     */
    public String getDescription() {
        return description;
    }

    /**
     * 描述
     *
     * @param description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 在json中的类型
     *
     * @return
     */
    public String getJsonType() {
        return jsonType;
    }

    /**
     * 在json中的类型
     *
     * @param jsonType
     */
    public void setJsonType(String jsonType) {
        this.jsonType = jsonType;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 示例数据
     *
     * @return
     */
    public Object getExamples() {
        return examples;
    }

    /**
     * 示例数据
     *
     * @param examples
     */
    public void setExamples(Object examples) {
        this.examples = examples;
    }

}
