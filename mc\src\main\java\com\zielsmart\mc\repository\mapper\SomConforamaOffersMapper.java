package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomConforamaOffers;
import com.zielsmart.mc.vo.SomConforamaOffersPageSearchVo;
import com.zielsmart.mc.vo.SomConforamaOffersVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-08
*/

@SqlResource("somConforamaOffers")
public interface SomConforamaOffersMapper extends BaseMapper<SomConforamaOffers> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomConforamaOffersVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomConforamaOffersVo> queryByPage(@Param("searchVo")SomConforamaOffersPageSearchVo searchVo, PageRequest pageRequest);

    List<SomConforamaOffersVo> exportExcel(@Param("searchVo") SomConforamaOffersPageSearchVo searchVo);

    List<SomConforamaOffers> allConforamaListing();

    default void updateBatch(@Param("updateList") List<SomConforamaOffers> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somConforamaOffers.updateBatch"), updateList);
    }
}
