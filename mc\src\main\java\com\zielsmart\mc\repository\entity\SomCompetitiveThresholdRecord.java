package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 三方平台比价表
 * gen by 代码生成器 2024-08-12
 */

@Table(name = "mc.som_competitive_threshold_record")
public class SomCompetitiveThresholdRecord implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 主表ID
     */
    @Column("health_id")
    private String healthId;
    /**
     * 三方平台
     */
    @Column("platform")
    private String platform;
    /**
     * 三方站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 产品前台页面链接
     */
    @Column("item_url")
    private String itemUrl;
    /**
     * 三方平台售价
     */
    @Column("landed_price")
    private BigDecimal landedPrice;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomCompetitiveThresholdRecord() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 主表ID
     *
     * @return
     */
    public String getHealthId() {
        return healthId;
    }

    /**
     * 主表ID
     *
     * @param healthId
     */
    public void setHealthId(String healthId) {
        this.healthId = healthId;
    }

    /**
     * 三方平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 三方平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 三方站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 三方站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 产品前台页面链接
     *
     * @return
     */
    public String getItemUrl() {
        return itemUrl;
    }

    /**
     * 产品前台页面链接
     *
     * @param itemUrl
     */
    public void setItemUrl(String itemUrl) {
        this.itemUrl = itemUrl;
    }

    /**
     * 三方平台售价
     *
     * @return
     */
    public BigDecimal getLandedPrice() {
        return landedPrice;
    }

    /**
     * 三方平台售价
     *
     * @param landedPrice
     */
    public void setLandedPrice(BigDecimal landedPrice) {
        this.landedPrice = landedPrice;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
