package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* Manage Product Selection
* gen by 代码生成器 2022-06-20
*/

@Table(name="mc.som_promotion_product_selection")
public class SomPromotionProductSelection implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 类型10.SKU List 20.ASIN List 30.Browse Node ID List 40.Brand Name List 50.Advanced Product Selection
	 */
	@Column("selection_type")
	private Integer selectionType ;
	/**
	 * 唯一名称
	 */
	@Column("selection_name")
	private String selectionName ;
	/**
	 * 描述
	 */
	@Column("internal_description")
	private String internalDescription ;
	/**
	 * 列表
	 */
	@Column("selection_list")
	private String selectionList ;
	/**
	 * 0.禁用 1.启用
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;

	public SomPromotionProductSelection() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 类型10.SKU List 20.ASIN List 30.Browse Node ID List 40.Brand Name List 50.Advanced Product Selection
	*@return
	*/
	public Integer getSelectionType(){
		return  selectionType;
	}
	/**
	* 类型10.SKU List 20.ASIN List 30.Browse Node ID List 40.Brand Name List 50.Advanced Product Selection
	*@param  selectionType
	*/
	public void setSelectionType(Integer selectionType ){
		this.selectionType = selectionType;
	}
	/**
	* 唯一名称
	*@return
	*/
	public String getSelectionName(){
		return  selectionName;
	}
	/**
	* 唯一名称
	*@param  selectionName
	*/
	public void setSelectionName(String selectionName ){
		this.selectionName = selectionName;
	}
	/**
	* 描述
	*@return
	*/
	public String getInternalDescription(){
		return  internalDescription;
	}
	/**
	* 描述
	*@param  internalDescription
	*/
	public void setInternalDescription(String internalDescription ){
		this.internalDescription = internalDescription;
	}
	/**
	* 列表
	*@return
	*/
	public String getSelectionList(){
		return  selectionList;
	}
	/**
	* 列表
	*@param  selectionList
	*/
	public void setSelectionList(String selectionList ){
		this.selectionList = selectionList;
	}
	/**
	* 0.禁用 1.启用
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 0.禁用 1.启用
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 最后修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 最后修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}
}
