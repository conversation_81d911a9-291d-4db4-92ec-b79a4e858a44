package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-06-09
*/

@Table(name="mc.som_pinterest_ads_report")
public class SomPinterestAdsReport implements java.io.Serializable {
	@AssignID
	private String id ;
	@Column("spend_in_micro_dollar")
	private Integer spendInMicroDollar ;
	@Column("paid_impression")
	private Integer paidImpression ;
	@Column("spend_in_dollar")
	private BigDecimal spendInDollar ;
	@Column("cpc_in_micro_dollar")
	private BigDecimal cpcInMicroDollar ;
	@Column("ecpc_in_micro_dollar")
	private BigDecimal ecpcInMicroDollar ;
	@Column("ecpc_in_dollar")
	private BigDecimal ecpcInDollar ;
	@Column("ctr")
	private BigDecimal ctr ;
	@Column("ectr")
	private BigDecimal ectr ;
	@Column("campaign_name")
	private String campaignName ;
	@Column("pin_id")
	private Integer pinId ;
	@Column("total_engagement")
	private Integer totalEngagement ;
	@Column("engagement_1")
	private Integer engagement1 ;
	@Column("engagement_2")
	private Integer engagement2 ;
	@Column("ecpe_in_dollar")
	private BigDecimal ecpeInDollar ;
	@Column("engagement_rate")
	private BigDecimal engagementRate ;
	@Column("eengagement_rate")
	private BigDecimal eengagementRate ;
	@Column("ecpm_in_micro_dollar")
	private BigDecimal ecpmInMicroDollar ;
	@Column("repin_rate")
	private BigDecimal repinRate ;
	@Column("ctr_2")
	private BigDecimal ctr2 ;
	@Column("campaign_id")
	private Long campaignId ;
	@Column("advertiser_id")
	private Long advertiserId ;
	@Column("ad_account_id")
	private Long adAccountId ;
	@Column("pin_promotion_id")
	private Long pinPromotionId ;
	@Column("ad_id")
	private Long adId ;
	@Column("ad_group_id")
	private Long adGroupId ;
	@Column("campaign_entity_status")
	private Integer campaignEntityStatus ;
	@Column("campaign_objective_type")
	private Integer campaignObjectiveType ;
	@Column("cpm_in_micro_dollar")
	private BigDecimal cpmInMicroDollar ;
	@Column("cpm_in_dollar")
	private BigDecimal cpmInDollar ;
	@Column("ad_group_entity_status")
	private Integer adGroupEntityStatus ;
	@Column("clickthrough_1")
	private Integer clickthrough1 ;
	@Column("repin_1")
	private Integer repin1 ;
	@Column("impression_1")
	private Integer impression1 ;
	@Column("impression_1_gross")
	private Integer impression1Gross ;
	@Column("clickthrough_1_gross")
	private Integer clickthrough1Gross ;
	@Column("outbound_click_1")
	private Integer outboundClick1 ;
	@Column("clickthrough_2")
	private Integer clickthrough2 ;
	@Column("repin_2")
	private Integer repin2 ;
	@Column("impression_2")
	private Integer impression2 ;
	@Column("outbound_click_2")
	private Integer outboundClick2 ;
	@Column("total_clickthrough")
	private Integer totalClickthrough ;
	@Column("total_impression")
	private Integer totalImpression ;
	@Column("total_impression_user")
	private Integer totalImpressionUser ;
	@Column("total_impression_frequency")
	private Integer totalImpressionFrequency ;
	@Column("total_engagement_signup")
	private Integer totalEngagementSignup ;
	@Column("total_engagement_checkout")
	private Integer totalEngagementCheckout ;
	@Column("total_engagement_lead")
	private Integer totalEngagementLead ;
	@Column("total_click_signup")
	private Integer totalClickSignup ;
	@Column("total_click_checkout")
	private Integer totalClickCheckout ;
	@Column("total_click_add_to_cart")
	private Integer totalClickAddToCart ;
	@Column("total_click_lead")
	private Integer totalClickLead ;
	@Column("total_view_signup")
	private Integer totalViewSignup ;
	@Column("total_view_checkout")
	private Integer totalViewCheckout ;
	@Column("total_view_add_to_cart")
	private Integer totalViewAddToCart ;
	@Column("total_view_lead")
	private Integer totalViewLead ;
	@Column("total_conversions")
	private Integer totalConversions ;
	@Column("total_engagement_signup_value_in_micro_dollar")
	private BigDecimal totalEngagementSignupValueInMicroDollar ;
	@Column("total_engagement_checkout_value_in_micro_dollar")
	private BigDecimal totalEngagementCheckoutValueInMicroDollar ;
	@Column("total_click_signup_value_in_micro_dollar")
	private BigDecimal totalClickSignupValueInMicroDollar ;
	@Column("total_click_checkout_value_in_micro_dollar")
	private BigDecimal totalClickCheckoutValueInMicroDollar ;
	@Column("total_view_signup_value_in_micro_dollar")
	private BigDecimal totalViewSignupValueInMicroDollar ;
	@Column("total_view_checkout_value_in_micro_dollar")
	private BigDecimal totalViewCheckoutValueInMicroDollar ;
	@Column("total_web_sessions")
	private Integer totalWebSessions ;
	@Column("web_sessions_1")
	private Integer webSessions1 ;
	@Column("web_sessions_2")
	private Integer webSessions2 ;
	@Column("campaign_lifetime_spend_cap")
	private Integer campaignLifetimeSpendCap ;
	@Column("campaign_daily_spend_cap")
	private Integer campaignDailySpendCap ;
	@Column("total_page_visit")
	private Integer totalPageVisit ;
	@Column("total_signup")
	private Integer totalSignup ;
	@Column("total_checkout")
	private Integer totalCheckout ;
	@Column("total_custom")
	private Integer totalCustom ;
	@Column("total_lead")
	private Integer totalLead ;
	@Column("total_signup_value_in_micro_dollar")
	private BigDecimal totalSignupValueInMicroDollar ;
	@Column("total_checkout_value_in_micro_dollar")
	private BigDecimal totalCheckoutValueInMicroDollar ;
	@Column("total_custom_value_in_micro_dollar")
	private BigDecimal totalCustomValueInMicroDollar ;
	@Column("page_visit_cost_per_action")
	private BigDecimal pageVisitCostPerAction ;
	@Column("page_visit_roas")
	private BigDecimal pageVisitRoas ;
	@Column("checkout_roas")
	private BigDecimal checkoutRoas ;
	@Column("custom_roas")
	private BigDecimal customRoas ;
	@Column("video_3sec_views_2")
	private Integer video3secViews2 ;
	@Column("video_p100_complete_2")
	private Integer videoP100Complete2 ;
	@Column("video_p0_combined_2")
	private Integer videoP0Combined2 ;
	@Column("video_p25_combined_2")
	private Integer videoP25Combined2 ;
	@Column("video_p50_combined_2")
	private Integer videoP50Combined2 ;
	@Column("video_p75_combined_2")
	private Integer videoP75Combined2 ;
	@Column("video_p95_combined_2")
	private Integer videoP95Combined2 ;
	@Column("video_mrc_views_2")
	private Integer videoMrcViews2 ;
	@Column("video_length")
	private Integer videoLength ;
	@Column("ecpv_in_dollar")
	private BigDecimal ecpvInDollar ;
	@Column("ecpcv_in_dollar")
	private BigDecimal ecpcvInDollar ;
	@Column("ecpcv_p95_in_dollar")
	private BigDecimal ecpcvP95InDollar ;
	@Column("total_video_3sec_views")
	private Integer totalVideo3secViews ;
	@Column("total_video_p100_complete")
	private Integer totalVideoP100Complete ;
	@Column("total_video_p0_combined")
	private Integer totalVideoP0Combined ;
	@Column("total_video_p25_combined")
	private Integer totalVideoP25Combined ;
	@Column("total_video_p50_combined")
	private Integer totalVideoP50Combined ;
	@Column("total_video_p75_combined")
	private Integer totalVideoP75Combined ;
	@Column("total_video_p95_combined")
	private Integer totalVideoP95Combined ;
	@Column("total_video_mrc_views")
	private Integer totalVideoMrcViews ;
	@Column("total_video_avg_watchtime_in_second")
	private BigDecimal totalVideoAvgWatchtimeInSecond ;
	@Column("total_repin_rate")
	private BigDecimal totalRepinRate ;
	@Column("web_checkout_cost_per_action")
	private BigDecimal webCheckoutCostPerAction ;
	@Column("web_checkout_roas")
	private BigDecimal webCheckoutRoas ;
	@Column("total_web_checkout")
	private Integer totalWebCheckout ;
	@Column("total_web_checkout_value_in_micro_dollar")
	private Integer totalWebCheckoutValueInMicroDollar ;
	@Column("total_web_click_checkout")
	private Integer totalWebClickCheckout ;
	@Column("total_web_click_checkout_value_in_micro_dollar")
	private BigDecimal totalWebClickCheckoutValueInMicroDollar ;
	@Column("total_web_engagement_checkout")
	private Integer totalWebEngagementCheckout ;
	@Column("total_web_engagement_checkout_value_in_micro_dollar")
	private BigDecimal totalWebEngagementCheckoutValueInMicroDollar ;
	@Column("total_web_view_checkout")
	private Integer totalWebViewCheckout ;
	@Column("total_web_view_checkout_value_in_micro_dollar")
	private Integer totalWebViewCheckoutValueInMicroDollar ;
	@Column("inapp_checkout_cost_per_action")
	private BigDecimal inappCheckoutCostPerAction ;
	@Column("total_offline_checkout")
	private Integer totalOfflineCheckout ;
	@Column("idea_pin_product_tag_visit_1")
	private String ideaPinProductTagVisit1 ;
	@Column("idea_pin_product_tag_visit_2")
	private String ideaPinProductTagVisit2 ;
	@Column("total_idea_pin_product_tag_visit")
	private String totalIdeaPinProductTagVisit ;
	@Column("leads")
	private Integer leads ;
	@Column("cost_per_lead")
	private Integer costPerLead ;
	@Column("quiz_completed")
	private Integer quizCompleted ;
	@Column("quiz_completion_rate")
	private BigDecimal quizCompletionRate ;
	@Column("showcase_pin_clickthrough")
	private String showcasePinClickthrough ;
	@Column("showcase_subpage_clickthrough")
	private String showcaseSubpageClickthrough ;
	@Column("showcase_subpin_clickthrough")
	private String showcaseSubpinClickthrough ;
	@Column("showcase_subpage_impression")
	private String showcaseSubpageImpression ;
	@Column("showcase_subpin_impression")
	private String showcaseSubpinImpression ;
	@Column("showcase_subpage_swipe_left")
	private String showcaseSubpageSwipeLeft ;
	@Column("showcase_subpage_swipe_right")
	private String showcaseSubpageSwipeRight ;
	@Column("showcase_subpin_swipe_left")
	private String showcaseSubpinSwipeLeft ;
	@Column("showcase_subpin_swipe_right")
	private String showcaseSubpinSwipeRight ;
	@Column("showcase_subpage_repin")
	private String showcaseSubpageRepin ;
	@Column("showcase_subpin_repin")
	private String showcaseSubpinRepin ;
	@Column("showcase_subpage_closeup")
	private String showcaseSubpageCloseup ;
	@Column("showcase_card_thumbnail_swipe_forward")
	private String showcaseCardThumbnailSwipeForward ;
	@Column("showcase_card_thumbnail_swipe_backward")
	private String showcaseCardThumbnailSwipeBackward ;
	@Column("analytics_date")
	private Date analyticsDate ;

	public SomPinterestAdsReport() {
	}

	public String getId(){
		return  id;
	}
	public void setId(String id ){
		this.id = id;
	}
	public Integer getSpendInMicroDollar(){
		return  spendInMicroDollar;
	}
	public void setSpendInMicroDollar(Integer spendInMicroDollar ){
		this.spendInMicroDollar = spendInMicroDollar;
	}
	public Integer getPaidImpression(){
		return  paidImpression;
	}
	public void setPaidImpression(Integer paidImpression ){
		this.paidImpression = paidImpression;
	}
	public BigDecimal getSpendInDollar(){
		return  spendInDollar;
	}
	public void setSpendInDollar(BigDecimal spendInDollar ){
		this.spendInDollar = spendInDollar;
	}
	public BigDecimal getCpcInMicroDollar(){
		return  cpcInMicroDollar;
	}
	public void setCpcInMicroDollar(BigDecimal cpcInMicroDollar ){
		this.cpcInMicroDollar = cpcInMicroDollar;
	}
	public BigDecimal getEcpcInMicroDollar(){
		return  ecpcInMicroDollar;
	}
	public void setEcpcInMicroDollar(BigDecimal ecpcInMicroDollar ){
		this.ecpcInMicroDollar = ecpcInMicroDollar;
	}
	public BigDecimal getEcpcInDollar(){
		return  ecpcInDollar;
	}
	public void setEcpcInDollar(BigDecimal ecpcInDollar ){
		this.ecpcInDollar = ecpcInDollar;
	}
	public BigDecimal getCtr(){
		return  ctr;
	}
	public void setCtr(BigDecimal ctr ){
		this.ctr = ctr;
	}
	public BigDecimal getEctr(){
		return  ectr;
	}
	public void setEctr(BigDecimal ectr ){
		this.ectr = ectr;
	}
	public String getCampaignName(){
		return  campaignName;
	}
	public void setCampaignName(String campaignName ){
		this.campaignName = campaignName;
	}
	public Integer getPinId(){
		return  pinId;
	}
	public void setPinId(Integer pinId ){
		this.pinId = pinId;
	}
	public Integer getTotalEngagement(){
		return  totalEngagement;
	}
	public void setTotalEngagement(Integer totalEngagement ){
		this.totalEngagement = totalEngagement;
	}
	public Integer getEngagement1(){
		return  engagement1;
	}
	public void setEngagement1(Integer engagement1 ){
		this.engagement1 = engagement1;
	}
	public Integer getEngagement2(){
		return  engagement2;
	}
	public void setEngagement2(Integer engagement2 ){
		this.engagement2 = engagement2;
	}
	public BigDecimal getEcpeInDollar(){
		return  ecpeInDollar;
	}
	public void setEcpeInDollar(BigDecimal ecpeInDollar ){
		this.ecpeInDollar = ecpeInDollar;
	}
	public BigDecimal getEngagementRate(){
		return  engagementRate;
	}
	public void setEngagementRate(BigDecimal engagementRate ){
		this.engagementRate = engagementRate;
	}
	public BigDecimal getEengagementRate(){
		return  eengagementRate;
	}
	public void setEengagementRate(BigDecimal eengagementRate ){
		this.eengagementRate = eengagementRate;
	}
	public BigDecimal getEcpmInMicroDollar(){
		return  ecpmInMicroDollar;
	}
	public void setEcpmInMicroDollar(BigDecimal ecpmInMicroDollar ){
		this.ecpmInMicroDollar = ecpmInMicroDollar;
	}
	public BigDecimal getRepinRate(){
		return  repinRate;
	}
	public void setRepinRate(BigDecimal repinRate ){
		this.repinRate = repinRate;
	}
	public BigDecimal getCtr2(){
		return  ctr2;
	}

	public void setCtr2(BigDecimal ctr2) {
		this.ctr2 = ctr2;
	}

	public Long getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Long campaignId) {
		this.campaignId = campaignId;
	}

	public Long getAdvertiserId() {
		return advertiserId;
	}

	public void setAdvertiserId(Long advertiserId) {
		this.advertiserId = advertiserId;
	}

	public Long getAdAccountId() {
		return adAccountId;
	}

	public void setAdAccountId(Long adAccountId) {
		this.adAccountId = adAccountId;
	}

	public Long getPinPromotionId() {
		return pinPromotionId;
	}

	public void setPinPromotionId(Long pinPromotionId) {
		this.pinPromotionId = pinPromotionId;
	}

	public Long getAdId() {
		return adId;
	}

	public void setAdId(Long adId) {
		this.adId = adId;
	}

	public Long getAdGroupId() {
		return adGroupId;
	}

	public void setAdGroupId(Long adGroupId) {
		this.adGroupId = adGroupId;
	}

	public Integer getAdGroupEntityStatus() {
		return adGroupEntityStatus;
	}

	public void setAdGroupEntityStatus(Integer adGroupEntityStatus) {
		this.adGroupEntityStatus = adGroupEntityStatus;
	}

	public Integer getCampaignEntityStatus(){
		return  campaignEntityStatus;
	}
	public void setCampaignEntityStatus(Integer campaignEntityStatus ){
		this.campaignEntityStatus = campaignEntityStatus;
	}
	public Integer getCampaignObjectiveType(){
		return  campaignObjectiveType;
	}
	public void setCampaignObjectiveType(Integer campaignObjectiveType ){
		this.campaignObjectiveType = campaignObjectiveType;
	}
	public BigDecimal getCpmInMicroDollar(){
		return  cpmInMicroDollar;
	}
	public void setCpmInMicroDollar(BigDecimal cpmInMicroDollar ){
		this.cpmInMicroDollar = cpmInMicroDollar;
	}
	public BigDecimal getCpmInDollar(){
		return  cpmInDollar;
	}
	public void setCpmInDollar(BigDecimal cpmInDollar ){
		this.cpmInDollar = cpmInDollar;
	}
	public Integer getadGroupEntityStatus(){
		return  adGroupEntityStatus;
	}
	public void setadGroupEntityStatus(Integer adGroupEntityStatus ){
		this.adGroupEntityStatus = adGroupEntityStatus;
	}
	public Integer getClickthrough1(){
		return  clickthrough1;
	}
	public void setClickthrough1(Integer clickthrough1 ){
		this.clickthrough1 = clickthrough1;
	}
	public Integer getRepin1(){
		return  repin1;
	}
	public void setRepin1(Integer repin1 ){
		this.repin1 = repin1;
	}
	public Integer getImpression1(){
		return  impression1;
	}
	public void setImpression1(Integer impression1 ){
		this.impression1 = impression1;
	}
	public Integer getImpression1Gross(){
		return  impression1Gross;
	}
	public void setImpression1Gross(Integer impression1Gross ){
		this.impression1Gross = impression1Gross;
	}
	public Integer getClickthrough1Gross(){
		return  clickthrough1Gross;
	}
	public void setClickthrough1Gross(Integer clickthrough1Gross ){
		this.clickthrough1Gross = clickthrough1Gross;
	}
	public Integer getOutboundClick1(){
		return  outboundClick1;
	}
	public void setOutboundClick1(Integer outboundClick1 ){
		this.outboundClick1 = outboundClick1;
	}
	public Integer getClickthrough2(){
		return  clickthrough2;
	}
	public void setClickthrough2(Integer clickthrough2 ){
		this.clickthrough2 = clickthrough2;
	}
	public Integer getRepin2(){
		return  repin2;
	}
	public void setRepin2(Integer repin2 ){
		this.repin2 = repin2;
	}
	public Integer getImpression2(){
		return  impression2;
	}
	public void setImpression2(Integer impression2 ){
		this.impression2 = impression2;
	}
	public Integer getOutboundClick2(){
		return  outboundClick2;
	}
	public void setOutboundClick2(Integer outboundClick2 ){
		this.outboundClick2 = outboundClick2;
	}
	public Integer getTotalClickthrough(){
		return  totalClickthrough;
	}
	public void setTotalClickthrough(Integer totalClickthrough ){
		this.totalClickthrough = totalClickthrough;
	}
	public Integer getTotalImpression(){
		return  totalImpression;
	}
	public void setTotalImpression(Integer totalImpression ){
		this.totalImpression = totalImpression;
	}
	public Integer getTotalImpressionUser(){
		return  totalImpressionUser;
	}
	public void setTotalImpressionUser(Integer totalImpressionUser ){
		this.totalImpressionUser = totalImpressionUser;
	}
	public Integer getTotalImpressionFrequency(){
		return  totalImpressionFrequency;
	}
	public void setTotalImpressionFrequency(Integer totalImpressionFrequency ){
		this.totalImpressionFrequency = totalImpressionFrequency;
	}
	public Integer getTotalEngagementSignup(){
		return  totalEngagementSignup;
	}
	public void setTotalEngagementSignup(Integer totalEngagementSignup ){
		this.totalEngagementSignup = totalEngagementSignup;
	}
	public Integer getTotalEngagementCheckout(){
		return  totalEngagementCheckout;
	}
	public void setTotalEngagementCheckout(Integer totalEngagementCheckout ){
		this.totalEngagementCheckout = totalEngagementCheckout;
	}
	public Integer getTotalEngagementLead(){
		return  totalEngagementLead;
	}
	public void setTotalEngagementLead(Integer totalEngagementLead ){
		this.totalEngagementLead = totalEngagementLead;
	}
	public Integer getTotalClickSignup(){
		return  totalClickSignup;
	}
	public void setTotalClickSignup(Integer totalClickSignup ){
		this.totalClickSignup = totalClickSignup;
	}
	public Integer getTotalClickCheckout(){
		return  totalClickCheckout;
	}
	public void setTotalClickCheckout(Integer totalClickCheckout ){
		this.totalClickCheckout = totalClickCheckout;
	}
	public Integer getTotalClickAddToCart(){
		return  totalClickAddToCart;
	}
	public void setTotalClickAddToCart(Integer totalClickAddToCart ){
		this.totalClickAddToCart = totalClickAddToCart;
	}
	public Integer getTotalClickLead(){
		return  totalClickLead;
	}
	public void setTotalClickLead(Integer totalClickLead ){
		this.totalClickLead = totalClickLead;
	}
	public Integer getTotalViewSignup(){
		return  totalViewSignup;
	}
	public void setTotalViewSignup(Integer totalViewSignup ){
		this.totalViewSignup = totalViewSignup;
	}
	public Integer getTotalViewCheckout(){
		return  totalViewCheckout;
	}
	public void setTotalViewCheckout(Integer totalViewCheckout ){
		this.totalViewCheckout = totalViewCheckout;
	}
	public Integer getTotalViewAddToCart(){
		return  totalViewAddToCart;
	}
	public void setTotalViewAddToCart(Integer totalViewAddToCart ){
		this.totalViewAddToCart = totalViewAddToCart;
	}
	public Integer getTotalViewLead(){
		return  totalViewLead;
	}
	public void setTotalViewLead(Integer totalViewLead ){
		this.totalViewLead = totalViewLead;
	}
	public Integer getTotalConversions(){
		return  totalConversions;
	}
	public void setTotalConversions(Integer totalConversions ){
		this.totalConversions = totalConversions;
	}
	public BigDecimal getTotalEngagementSignupValueInMicroDollar(){
		return  totalEngagementSignupValueInMicroDollar;
	}
	public void setTotalEngagementSignupValueInMicroDollar(BigDecimal totalEngagementSignupValueInMicroDollar ){
		this.totalEngagementSignupValueInMicroDollar = totalEngagementSignupValueInMicroDollar;
	}
	public BigDecimal getTotalEngagementCheckoutValueInMicroDollar(){
		return  totalEngagementCheckoutValueInMicroDollar;
	}
	public void setTotalEngagementCheckoutValueInMicroDollar(BigDecimal totalEngagementCheckoutValueInMicroDollar ){
		this.totalEngagementCheckoutValueInMicroDollar = totalEngagementCheckoutValueInMicroDollar;
	}
	public BigDecimal getTotalClickSignupValueInMicroDollar(){
		return  totalClickSignupValueInMicroDollar;
	}
	public void setTotalClickSignupValueInMicroDollar(BigDecimal totalClickSignupValueInMicroDollar ){
		this.totalClickSignupValueInMicroDollar = totalClickSignupValueInMicroDollar;
	}
	public BigDecimal getTotalClickCheckoutValueInMicroDollar(){
		return  totalClickCheckoutValueInMicroDollar;
	}
	public void setTotalClickCheckoutValueInMicroDollar(BigDecimal totalClickCheckoutValueInMicroDollar ){
		this.totalClickCheckoutValueInMicroDollar = totalClickCheckoutValueInMicroDollar;
	}
	public BigDecimal getTotalViewSignupValueInMicroDollar(){
		return  totalViewSignupValueInMicroDollar;
	}
	public void setTotalViewSignupValueInMicroDollar(BigDecimal totalViewSignupValueInMicroDollar ){
		this.totalViewSignupValueInMicroDollar = totalViewSignupValueInMicroDollar;
	}
	public BigDecimal getTotalViewCheckoutValueInMicroDollar(){
		return  totalViewCheckoutValueInMicroDollar;
	}
	public void setTotalViewCheckoutValueInMicroDollar(BigDecimal totalViewCheckoutValueInMicroDollar ){
		this.totalViewCheckoutValueInMicroDollar = totalViewCheckoutValueInMicroDollar;
	}
	public Integer getTotalWebSessions(){
		return  totalWebSessions;
	}
	public void setTotalWebSessions(Integer totalWebSessions ){
		this.totalWebSessions = totalWebSessions;
	}
	public Integer getWebSessions1(){
		return  webSessions1;
	}
	public void setWebSessions1(Integer webSessions1 ){
		this.webSessions1 = webSessions1;
	}
	public Integer getWebSessions2(){
		return  webSessions2;
	}
	public void setWebSessions2(Integer webSessions2 ){
		this.webSessions2 = webSessions2;
	}
	public Integer getCampaignLifetimeSpendCap(){
		return  campaignLifetimeSpendCap;
	}
	public void setCampaignLifetimeSpendCap(Integer campaignLifetimeSpendCap ){
		this.campaignLifetimeSpendCap = campaignLifetimeSpendCap;
	}
	public Integer getCampaignDailySpendCap(){
		return  campaignDailySpendCap;
	}
	public void setCampaignDailySpendCap(Integer campaignDailySpendCap ){
		this.campaignDailySpendCap = campaignDailySpendCap;
	}
	public Integer getTotalPageVisit(){
		return  totalPageVisit;
	}
	public void setTotalPageVisit(Integer totalPageVisit ){
		this.totalPageVisit = totalPageVisit;
	}
	public Integer getTotalSignup(){
		return  totalSignup;
	}
	public void setTotalSignup(Integer totalSignup ){
		this.totalSignup = totalSignup;
	}
	public Integer getTotalCheckout(){
		return  totalCheckout;
	}
	public void setTotalCheckout(Integer totalCheckout ){
		this.totalCheckout = totalCheckout;
	}
	public Integer getTotalCustom(){
		return  totalCustom;
	}
	public void setTotalCustom(Integer totalCustom ){
		this.totalCustom = totalCustom;
	}
	public Integer getTotalLead(){
		return  totalLead;
	}
	public void setTotalLead(Integer totalLead ){
		this.totalLead = totalLead;
	}
	public BigDecimal getTotalSignupValueInMicroDollar(){
		return  totalSignupValueInMicroDollar;
	}
	public void setTotalSignupValueInMicroDollar(BigDecimal totalSignupValueInMicroDollar ){
		this.totalSignupValueInMicroDollar = totalSignupValueInMicroDollar;
	}
	public BigDecimal getTotalCheckoutValueInMicroDollar(){
		return  totalCheckoutValueInMicroDollar;
	}
	public void setTotalCheckoutValueInMicroDollar(BigDecimal totalCheckoutValueInMicroDollar ){
		this.totalCheckoutValueInMicroDollar = totalCheckoutValueInMicroDollar;
	}
	public BigDecimal getTotalCustomValueInMicroDollar(){
		return  totalCustomValueInMicroDollar;
	}
	public void setTotalCustomValueInMicroDollar(BigDecimal totalCustomValueInMicroDollar ){
		this.totalCustomValueInMicroDollar = totalCustomValueInMicroDollar;
	}
	public BigDecimal getPageVisitCostPerAction(){
		return  pageVisitCostPerAction;
	}
	public void setPageVisitCostPerAction(BigDecimal pageVisitCostPerAction ){
		this.pageVisitCostPerAction = pageVisitCostPerAction;
	}
	public BigDecimal getPageVisitRoas(){
		return  pageVisitRoas;
	}
	public void setPageVisitRoas(BigDecimal pageVisitRoas ){
		this.pageVisitRoas = pageVisitRoas;
	}
	public BigDecimal getCheckoutRoas(){
		return  checkoutRoas;
	}
	public void setCheckoutRoas(BigDecimal checkoutRoas ){
		this.checkoutRoas = checkoutRoas;
	}
	public BigDecimal getCustomRoas(){
		return  customRoas;
	}
	public void setCustomRoas(BigDecimal customRoas ){
		this.customRoas = customRoas;
	}
	public Integer getVideo3secViews2(){
		return  video3secViews2;
	}
	public void setVideo3secViews2(Integer video3secViews2 ){
		this.video3secViews2 = video3secViews2;
	}
	public Integer getVideoP100Complete2(){
		return  videoP100Complete2;
	}
	public void setVideoP100Complete2(Integer videoP100Complete2 ){
		this.videoP100Complete2 = videoP100Complete2;
	}
	public Integer getVideoP0Combined2(){
		return  videoP0Combined2;
	}
	public void setVideoP0Combined2(Integer videoP0Combined2 ){
		this.videoP0Combined2 = videoP0Combined2;
	}
	public Integer getVideoP25Combined2(){
		return  videoP25Combined2;
	}
	public void setVideoP25Combined2(Integer videoP25Combined2 ){
		this.videoP25Combined2 = videoP25Combined2;
	}
	public Integer getVideoP50Combined2(){
		return  videoP50Combined2;
	}
	public void setVideoP50Combined2(Integer videoP50Combined2 ){
		this.videoP50Combined2 = videoP50Combined2;
	}
	public Integer getVideoP75Combined2(){
		return  videoP75Combined2;
	}
	public void setVideoP75Combined2(Integer videoP75Combined2 ){
		this.videoP75Combined2 = videoP75Combined2;
	}
	public Integer getVideoP95Combined2(){
		return  videoP95Combined2;
	}
	public void setVideoP95Combined2(Integer videoP95Combined2 ){
		this.videoP95Combined2 = videoP95Combined2;
	}
	public Integer getVideoMrcViews2(){
		return  videoMrcViews2;
	}
	public void setVideoMrcViews2(Integer videoMrcViews2 ){
		this.videoMrcViews2 = videoMrcViews2;
	}
	public Integer getVideoLength(){
		return  videoLength;
	}
	public void setVideoLength(Integer videoLength ){
		this.videoLength = videoLength;
	}
	public BigDecimal getEcpvInDollar(){
		return  ecpvInDollar;
	}
	public void setEcpvInDollar(BigDecimal ecpvInDollar ){
		this.ecpvInDollar = ecpvInDollar;
	}
	public BigDecimal getEcpcvInDollar(){
		return  ecpcvInDollar;
	}
	public void setEcpcvInDollar(BigDecimal ecpcvInDollar ){
		this.ecpcvInDollar = ecpcvInDollar;
	}
	public BigDecimal getEcpcvP95InDollar(){
		return  ecpcvP95InDollar;
	}
	public void setEcpcvP95InDollar(BigDecimal ecpcvP95InDollar ){
		this.ecpcvP95InDollar = ecpcvP95InDollar;
	}
	public Integer getTotalVideo3secViews(){
		return  totalVideo3secViews;
	}
	public void setTotalVideo3secViews(Integer totalVideo3secViews ){
		this.totalVideo3secViews = totalVideo3secViews;
	}
	public Integer getTotalVideoP100Complete(){
		return  totalVideoP100Complete;
	}
	public void setTotalVideoP100Complete(Integer totalVideoP100Complete ){
		this.totalVideoP100Complete = totalVideoP100Complete;
	}
	public Integer getTotalVideoP0Combined(){
		return  totalVideoP0Combined;
	}
	public void setTotalVideoP0Combined(Integer totalVideoP0Combined ){
		this.totalVideoP0Combined = totalVideoP0Combined;
	}
	public Integer getTotalVideoP25Combined(){
		return  totalVideoP25Combined;
	}
	public void setTotalVideoP25Combined(Integer totalVideoP25Combined ){
		this.totalVideoP25Combined = totalVideoP25Combined;
	}
	public Integer getTotalVideoP50Combined(){
		return  totalVideoP50Combined;
	}
	public void setTotalVideoP50Combined(Integer totalVideoP50Combined ){
		this.totalVideoP50Combined = totalVideoP50Combined;
	}
	public Integer getTotalVideoP75Combined(){
		return  totalVideoP75Combined;
	}
	public void setTotalVideoP75Combined(Integer totalVideoP75Combined ){
		this.totalVideoP75Combined = totalVideoP75Combined;
	}
	public Integer getTotalVideoP95Combined(){
		return  totalVideoP95Combined;
	}
	public void setTotalVideoP95Combined(Integer totalVideoP95Combined ){
		this.totalVideoP95Combined = totalVideoP95Combined;
	}
	public Integer getTotalVideoMrcViews(){
		return  totalVideoMrcViews;
	}
	public void setTotalVideoMrcViews(Integer totalVideoMrcViews ){
		this.totalVideoMrcViews = totalVideoMrcViews;
	}
	public BigDecimal getTotalVideoAvgWatchtimeInSecond(){
		return  totalVideoAvgWatchtimeInSecond;
	}
	public void setTotalVideoAvgWatchtimeInSecond(BigDecimal totalVideoAvgWatchtimeInSecond ){
		this.totalVideoAvgWatchtimeInSecond = totalVideoAvgWatchtimeInSecond;
	}
	public BigDecimal getTotalRepinRate(){
		return  totalRepinRate;
	}
	public void setTotalRepinRate(BigDecimal totalRepinRate ){
		this.totalRepinRate = totalRepinRate;
	}
	public BigDecimal getWebCheckoutCostPerAction(){
		return  webCheckoutCostPerAction;
	}
	public void setWebCheckoutCostPerAction(BigDecimal webCheckoutCostPerAction ){
		this.webCheckoutCostPerAction = webCheckoutCostPerAction;
	}
	public BigDecimal getWebCheckoutRoas(){
		return  webCheckoutRoas;
	}
	public void setWebCheckoutRoas(BigDecimal webCheckoutRoas ){
		this.webCheckoutRoas = webCheckoutRoas;
	}
	public Integer getTotalWebCheckout(){
		return  totalWebCheckout;
	}
	public void setTotalWebCheckout(Integer totalWebCheckout ){
		this.totalWebCheckout = totalWebCheckout;
	}
	public Integer getTotalWebCheckoutValueInMicroDollar(){
		return  totalWebCheckoutValueInMicroDollar;
	}
	public void setTotalWebCheckoutValueInMicroDollar(Integer totalWebCheckoutValueInMicroDollar ){
		this.totalWebCheckoutValueInMicroDollar = totalWebCheckoutValueInMicroDollar;
	}
	public Integer getTotalWebClickCheckout(){
		return  totalWebClickCheckout;
	}
	public void setTotalWebClickCheckout(Integer totalWebClickCheckout ){
		this.totalWebClickCheckout = totalWebClickCheckout;
	}
	public BigDecimal getTotalWebClickCheckoutValueInMicroDollar(){
		return  totalWebClickCheckoutValueInMicroDollar;
	}
	public void setTotalWebClickCheckoutValueInMicroDollar(BigDecimal totalWebClickCheckoutValueInMicroDollar ){
		this.totalWebClickCheckoutValueInMicroDollar = totalWebClickCheckoutValueInMicroDollar;
	}
	public Integer getTotalWebEngagementCheckout(){
		return  totalWebEngagementCheckout;
	}
	public void setTotalWebEngagementCheckout(Integer totalWebEngagementCheckout ){
		this.totalWebEngagementCheckout = totalWebEngagementCheckout;
	}
	public BigDecimal getTotalWebEngagementCheckoutValueInMicroDollar(){
		return  totalWebEngagementCheckoutValueInMicroDollar;
	}
	public void setTotalWebEngagementCheckoutValueInMicroDollar(BigDecimal totalWebEngagementCheckoutValueInMicroDollar ){
		this.totalWebEngagementCheckoutValueInMicroDollar = totalWebEngagementCheckoutValueInMicroDollar;
	}
	public Integer getTotalWebViewCheckout(){
		return  totalWebViewCheckout;
	}
	public void setTotalWebViewCheckout(Integer totalWebViewCheckout ){
		this.totalWebViewCheckout = totalWebViewCheckout;
	}
	public Integer getTotalWebViewCheckoutValueInMicroDollar(){
		return  totalWebViewCheckoutValueInMicroDollar;
	}
	public void setTotalWebViewCheckoutValueInMicroDollar(Integer totalWebViewCheckoutValueInMicroDollar ){
		this.totalWebViewCheckoutValueInMicroDollar = totalWebViewCheckoutValueInMicroDollar;
	}
	public BigDecimal getInappCheckoutCostPerAction(){
		return  inappCheckoutCostPerAction;
	}
	public void setInappCheckoutCostPerAction(BigDecimal inappCheckoutCostPerAction ){
		this.inappCheckoutCostPerAction = inappCheckoutCostPerAction;
	}
	public Integer getTotalOfflineCheckout(){
		return  totalOfflineCheckout;
	}
	public void setTotalOfflineCheckout(Integer totalOfflineCheckout ){
		this.totalOfflineCheckout = totalOfflineCheckout;
	}
	public String getIdeaPinProductTagVisit1(){
		return  ideaPinProductTagVisit1;
	}
	public void setIdeaPinProductTagVisit1(String ideaPinProductTagVisit1 ){
		this.ideaPinProductTagVisit1 = ideaPinProductTagVisit1;
	}
	public String getIdeaPinProductTagVisit2(){
		return  ideaPinProductTagVisit2;
	}
	public void setIdeaPinProductTagVisit2(String ideaPinProductTagVisit2 ){
		this.ideaPinProductTagVisit2 = ideaPinProductTagVisit2;
	}
	public String getTotalIdeaPinProductTagVisit(){
		return  totalIdeaPinProductTagVisit;
	}
	public void setTotalIdeaPinProductTagVisit(String totalIdeaPinProductTagVisit ){
		this.totalIdeaPinProductTagVisit = totalIdeaPinProductTagVisit;
	}
	public Integer getLeads(){
		return  leads;
	}
	public void setLeads(Integer leads ){
		this.leads = leads;
	}
	public Integer getCostPerLead(){
		return  costPerLead;
	}
	public void setCostPerLead(Integer costPerLead ){
		this.costPerLead = costPerLead;
	}
	public Integer getQuizCompleted(){
		return  quizCompleted;
	}
	public void setQuizCompleted(Integer quizCompleted ){
		this.quizCompleted = quizCompleted;
	}
	public BigDecimal getQuizCompletionRate(){
		return  quizCompletionRate;
	}
	public void setQuizCompletionRate(BigDecimal quizCompletionRate ){
		this.quizCompletionRate = quizCompletionRate;
	}
	public String getShowcasePinClickthrough(){
		return  showcasePinClickthrough;
	}
	public void setShowcasePinClickthrough(String showcasePinClickthrough ){
		this.showcasePinClickthrough = showcasePinClickthrough;
	}
	public String getShowcaseSubpageClickthrough(){
		return  showcaseSubpageClickthrough;
	}
	public void setShowcaseSubpageClickthrough(String showcaseSubpageClickthrough ){
		this.showcaseSubpageClickthrough = showcaseSubpageClickthrough;
	}
	public String getShowcaseSubpinClickthrough(){
		return  showcaseSubpinClickthrough;
	}
	public void setShowcaseSubpinClickthrough(String showcaseSubpinClickthrough ){
		this.showcaseSubpinClickthrough = showcaseSubpinClickthrough;
	}
	public String getShowcaseSubpageImpression(){
		return  showcaseSubpageImpression;
	}
	public void setShowcaseSubpageImpression(String showcaseSubpageImpression ){
		this.showcaseSubpageImpression = showcaseSubpageImpression;
	}
	public String getShowcaseSubpinImpression(){
		return  showcaseSubpinImpression;
	}
	public void setShowcaseSubpinImpression(String showcaseSubpinImpression ){
		this.showcaseSubpinImpression = showcaseSubpinImpression;
	}
	public String getShowcaseSubpageSwipeLeft(){
		return  showcaseSubpageSwipeLeft;
	}
	public void setShowcaseSubpageSwipeLeft(String showcaseSubpageSwipeLeft ){
		this.showcaseSubpageSwipeLeft = showcaseSubpageSwipeLeft;
	}
	public String getShowcaseSubpageSwipeRight(){
		return  showcaseSubpageSwipeRight;
	}
	public void setShowcaseSubpageSwipeRight(String showcaseSubpageSwipeRight ){
		this.showcaseSubpageSwipeRight = showcaseSubpageSwipeRight;
	}
	public String getShowcaseSubpinSwipeLeft(){
		return  showcaseSubpinSwipeLeft;
	}
	public void setShowcaseSubpinSwipeLeft(String showcaseSubpinSwipeLeft ){
		this.showcaseSubpinSwipeLeft = showcaseSubpinSwipeLeft;
	}
	public String getShowcaseSubpinSwipeRight(){
		return  showcaseSubpinSwipeRight;
	}
	public void setShowcaseSubpinSwipeRight(String showcaseSubpinSwipeRight ){
		this.showcaseSubpinSwipeRight = showcaseSubpinSwipeRight;
	}
	public String getShowcaseSubpageRepin(){
		return  showcaseSubpageRepin;
	}
	public void setShowcaseSubpageRepin(String showcaseSubpageRepin ){
		this.showcaseSubpageRepin = showcaseSubpageRepin;
	}
	public String getShowcaseSubpinRepin(){
		return  showcaseSubpinRepin;
	}
	public void setShowcaseSubpinRepin(String showcaseSubpinRepin ){
		this.showcaseSubpinRepin = showcaseSubpinRepin;
	}
	public String getShowcaseSubpageCloseup(){
		return  showcaseSubpageCloseup;
	}
	public void setShowcaseSubpageCloseup(String showcaseSubpageCloseup ){
		this.showcaseSubpageCloseup = showcaseSubpageCloseup;
	}
	public String getShowcaseCardThumbnailSwipeForward(){
		return  showcaseCardThumbnailSwipeForward;
	}
	public void setShowcaseCardThumbnailSwipeForward(String showcaseCardThumbnailSwipeForward ){
		this.showcaseCardThumbnailSwipeForward = showcaseCardThumbnailSwipeForward;
	}
	public String getShowcaseCardThumbnailSwipeBackward(){
		return  showcaseCardThumbnailSwipeBackward;
	}
	public void setShowcaseCardThumbnailSwipeBackward(String showcaseCardThumbnailSwipeBackward ){
		this.showcaseCardThumbnailSwipeBackward = showcaseCardThumbnailSwipeBackward;
	}
	public Date getAnalyticsDate(){
		return  analyticsDate;
	}
	public void setAnalyticsDate(Date analyticsDate ){
		this.analyticsDate = analyticsDate;
	}

}
