package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.mapper.SomAwinAdsReportMapper;
import com.zielsmart.mc.vo.SomAwinAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomAwinAdsReportVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomAwinAdsReportService {
    
    @Resource
    private SomAwinAdsReportMapper somAwinAdsReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomAwinAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomAwinAdsReportVo> queryByPage(SomAwinAdsReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomAwinAdsReportVo> pageResult = dynamicSqlManager.getMapper(SomAwinAdsReportMapper.class).queryByPage(searchVo, pageRequest);
        for (SomAwinAdsReportVo vo : pageResult.getList()) {
            SomAwinAdsReportVo.Amount commissionAmount = JSONUtil.toBean(vo.getCommissionAmount(), SomAwinAdsReportVo.Amount.class);
            SomAwinAdsReportVo.Amount quantityAmount = JSONUtil.toBean(vo.getQuantity(), SomAwinAdsReportVo.Amount.class);
            SomAwinAdsReportVo.Amount saleAmount = JSONUtil.toBean(vo.getSaleAmount(), SomAwinAdsReportVo.Amount.class);
            vo.setCommissionAmountBean(commissionAmount);
            vo.setQuantityBean(quantityAmount);
            vo.setSaleAmountBean(saleAmount);
            vo.setCommissionAmount(null);
            vo.setQuantity(null);
            vo.setSaleAmount(null);
        }
        return ConvertUtils.pageConvert(pageResult, SomAwinAdsReportVo.class, searchVo);
    }


    public String export(SomAwinAdsReportPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomAwinAdsReportVo> exportVoList = queryByPage(searchVo).getRecords();
        if (!exportVoList.isEmpty()) {
            for (SomAwinAdsReportVo vo : exportVoList) {
                SomAwinAdsReportVo.Amount commission = vo.getCommissionAmountBean();
                SomAwinAdsReportVo.Amount quantity = vo.getQuantityBean();
                SomAwinAdsReportVo.Amount sale = vo.getSaleAmountBean();
                vo.setCommissionAmount(formatAmount(commission));
                vo.setQuantity(formatAmount(quantity));
                vo.setSaleAmount(formatAmount(sale));
            }
            try {
                Workbook workbook;
                ExportParams params = new ExportParams(null, "Awin广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomAwinAdsReportVo.class, exportVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private static String formatAmount(SomAwinAdsReportVo.Amount amount) {
        StringBuilder  sb = new StringBuilder();
        sb.append("approved:" + amount.getApproved() == null ? 0 : amount.getApproved().doubleValue() + "\n");
        sb.append("bonus:" + amount.getBonus() == null ? 0 : amount.getBonus().doubleValue() + "\n");
        sb.append("declined:" + amount.getDeclined() == null ? 0 : amount.getDeclined().doubleValue() + "\n");
        sb.append("pending:" + amount.getPending() == null ? 0 : amount.getPending().doubleValue() + "\n");
        sb.append("total:" + amount.getTotal() == null ? 0 : amount.getTotal().doubleValue());
        return sb.toString();
    }
}
