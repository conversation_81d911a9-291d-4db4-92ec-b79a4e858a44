package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomButAutoPushStockRecord;
import com.zielsmart.mc.vo.SomButAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomButAutoPushStockRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-03
*/

@SqlResource("somButAutoPushStockRecord")
public interface SomButAutoPushStockRecordMapper extends BaseMapper<SomButAutoPushStockRecord> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomButAutoPushStockRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomButAutoPushStockRecordVo> queryByPage(@Param("searchVo")SomButAutoPushStockRecordPageSearchVo searchVo, PageRequest pageRequest);

    List<SomButAutoPushStockRecordVo> exportExcel(@Param("searchVo")SomButAutoPushStockRecordPageSearchVo searchVo);
}
