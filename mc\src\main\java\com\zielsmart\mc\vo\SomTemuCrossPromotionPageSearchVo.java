package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
 * Temu跨境（香港半托）营销活动明细的VO分页查询实体
 */
@Data
@Schema(title = "Temu跨境（香港半托）营销活动明细分页查询实体", name = "SomTemuCrossPromotionDetailPageSearchVo")
public class SomTemuCrossPromotionPageSearchVo extends PageSearchVo {

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "活动状态：1. 未开始 2. 运行中 3. 已结束", name = "activityStatus")
    private Integer activityStatus;

    @Schema(description = "活动类型", name = "activityType")
    private Integer activityType;

    @Schema(description = "报名状态：1.报名中 2.报名失败 3.报名成功待分配场次 4.报名成功已分配场次 5.报名活动已结束 6.报名活动已下线", name = "enrollStatus")
    private Integer enrollStatus;

}
