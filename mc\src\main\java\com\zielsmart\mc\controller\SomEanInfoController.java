package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomEanInfoService;
import com.zielsmart.mc.vo.SomEanBatchVo;
import com.zielsmart.mc.vo.SomEanInfoPageSearchVo;
import com.zielsmart.mc.vo.SomEanInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomEanInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/som-ean", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "ean管理管理")
public class SomEanInfoController extends BasicController{

    @Resource
    SomEanInfoService somEanInfoService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomEanInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomEanInfoVo>> queryByPage(@RequestBody SomEanInfoPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somEanInfoService.queryByPage(searchVo));
    }

    @Operation(summary = "获取ean eya调用")
    @PostMapping(value = "/get-ean")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomEanInfoVo>> getEan(@RequestBody List<SomEanInfoPageSearchVo.EyaParam> params) throws ValidateException {
        return ResultVo.ofSuccess(somEanInfoService.getEan(params));
    }

    /**
     * assign
     *
     * @param somEanInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分配")
    @PostMapping(value = "/assign")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> assign(@RequestBody SomEanInfoVo somEanInfoVo) throws ValidateException {
        somEanInfoService.assign(somEanInfoVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * occupy
     *
     * @param somEanInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "占用")
    @PostMapping(value = "/occupy")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> occupy(@RequestBody SomEanInfoVo somEanInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somEanInfoService.occupy(somEanInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "批量占用")
    @PostMapping(value = "/batch-occupy")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchOccupy(@RequestBody SomEanBatchVo eanBatchVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        String msg = somEanInfoService.batchOccupy(eanBatchVo, tokenUser);
        return ResultVo.ofSuccess(msg);
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/eanTemplate.xlsx";
    }

    /**
     * import
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"顺序号", "校验码", "自发展示码","平台","归属方","备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomEanInfoVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomEanInfoVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somEanInfoService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }


    /**
     * use-sequenceNum
     *
     * @param somEanInfoVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "使用")
    @PostMapping(value = "/use-sequenceNum")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> useSequenceNum(@RequestBody SomEanInfoVo somEanInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somEanInfoService.useSequenceNum(somEanInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * edit
     *
     * @param somEanInfoVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody SomEanInfoVo somEanInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somEanInfoService.edit(somEanInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "计算校验码")
    @PostMapping(value = "/calc-code")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<Integer> calcCode(@RequestBody SomEanInfoVo somEanInfoVo) throws ValidateException {
        return ResultVo.ofSuccess(somEanInfoService.calcCode(somEanInfoVo));
    }


    /**
     * remark
     *
     * @param somEanInfoVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "备注")
    @PostMapping(value = "/remark")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> remark(@RequestBody SomEanInfoVo somEanInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somEanInfoService.remark(somEanInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomEanInfoPageSearchVo searchVo) throws ValidateException{
        String data = somEanInfoService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
