package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomAmazonVcComparativePriceRecord;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-01-21
*/

@SqlResource("somAmazonVcComparativePriceRecord")
public interface SomAmazonVcComparativePriceRecordMapper extends BaseMapper<SomAmazonVcComparativePriceRecord> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonVcComparativePriceRecordVo> queryByPage(@Param("searchVo")SomAmazonVcComparativePriceRecordPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchHandleComparativePriceRecord
     * 批量处理比价
     *
     * @param comparativePriceRecord entity
     */
    default void batchHandleComparativePriceRecord(@Param("records") List<SomAmazonVcComparativePriceRecord> comparativePriceRecord) {
        if (CollUtil.isEmpty(comparativePriceRecord)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somAmazonVcComparativePriceRecord.handleComparativePriceRecord"), comparativePriceRecord);
    }
}
