package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.HouzzService;
import com.zielsmart.web.basic.BasicController;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version V1.0
 * @title: HouzzController
 * @package: com.zielsmart.mc.controller
 * @description:
 * @author: lv<PERSON>shu<PERSON>
 * @date: 2021-04-27 9:17
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/houzz", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Houzz平台")
public class HouzzController extends BasicController {
    @Resource
    private HouzzService houzzService;

}
