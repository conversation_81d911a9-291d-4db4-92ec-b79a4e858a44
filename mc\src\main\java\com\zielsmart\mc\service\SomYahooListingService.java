package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomYahooInventory;
import com.zielsmart.mc.repository.mapper.SomYahooInventoryMapper;
import com.zielsmart.mc.repository.mapper.SomYahooListingMapper;
import com.zielsmart.mc.vo.SomYahooListingExtVo;
import com.zielsmart.mc.vo.SomYahooListingPageSearchVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomYahooListingService {

    @Resource
    private SomYahooListingMapper listingMapper;
    @Resource
    private SomYahooInventoryMapper inventoryMapper;
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomYahooListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomYahooListingExtVo> queryByPage(SomYahooListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomYahooListingExtVo> pageResult = listingMapper.queryByPage(searchVo, pageRequest);
        if(CollectionUtil.isNotEmpty(pageResult.getList())){
            List<SomYahooInventory> stockList = inventoryMapper.createLambdaQuery().andIn("code", pageResult.getList().stream().map(m -> m.getCode()).collect(Collectors.toList())).select();
            for (SomYahooListingExtVo vo : pageResult.getList()) {
                if(StrUtil.isNotEmpty(vo.getSubCode())){
                    stockList.stream().filter(f-> StrUtil.equalsIgnoreCase(vo.getCode(),f.getCode()) && StrUtil.equalsIgnoreCase(vo.getSubCode(),f.getSubCode())).findFirst().ifPresent(ps->{
                        vo.setStock(ps.getQuantity());
                    });
                }else {
                    stockList.stream().filter(f-> StrUtil.equalsIgnoreCase(vo.getCode(),f.getCode())).findFirst().ifPresent(ps->{
                        vo.setStock(ps.getQuantity());
                    });
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomYahooListingExtVo.class, searchVo);
    }
}
