package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.McStockInfo;
import com.zielsmart.mc.repository.entity.SomMiraviaWarehouse;
import com.zielsmart.mc.repository.entity.SomMiraviaWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McStockInfoMapper;
import com.zielsmart.mc.repository.mapper.SomMiraviaListingMapper;
import com.zielsmart.mc.repository.mapper.SomMiraviaWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomMiraviaWarehouseMapper;
import com.zielsmart.mc.vo.SomMiraviaListingPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaListingReport;
import com.zielsmart.mc.vo.SomMiraviaListingVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomMiraviaListingService
 * @description
 * @date 2024-04-22 14:47:00
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomMiraviaListingService {
    @Resource
    private SomMiraviaListingMapper somMiraviaListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private SomMiraviaWarehouseMapper somMiraviaWarehouseMapper;
    @Resource
    private SomMiraviaWarehouseConfigMapper somMiraviaWarehouseConfigMapper;

    @Resource
    private ObjectMapper objectMapper;


    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomMiraviaListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomMiraviaListingVo> queryByPage(SomMiraviaListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMiraviaListingVo> pageResult = dynamicSqlManager.getMapper(SomMiraviaListingMapper.class).queryByPage(searchVo, pageRequest);
        List<SomMiraviaWarehouse> warehouseList = somMiraviaWarehouseMapper.all();
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            for (SomMiraviaListingVo vo : pageResult.getList()) {
                List<SomMiraviaListingVo.warehouseInfo> infoList = new ArrayList<>();
                JSONArray array = JSONUtil.parseArray(vo.getWarehouseQuantities());
                if (array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {
                        SomMiraviaListingVo.warehouseInfo warehouseInfo = new SomMiraviaListingVo.warehouseInfo();
                        JSONObject jsonObject = array.getJSONObject(i);
                        if (CollectionUtil.isNotEmpty(warehouseList)) {
                            warehouseList.stream().filter(f -> StrUtil.equals(f.getWarehouseCode(), jsonObject.getStr("warehouse_code"))).findFirst().ifPresent(x -> {
                                warehouseInfo.setWarehouseName(x.getWarehouseName());
                            });
                        }
                        warehouseInfo.setWarehouseCode(jsonObject.getStr("warehouse_code"));
                        warehouseInfo.setStock(jsonObject.getInt("quantity"));
                        infoList.add(warehouseInfo);
                    }
                }
                vo.setWarehouseInfoList(infoList);
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomMiraviaListingVo.class, searchVo);
    }

    public PageVo<SomMiraviaListingReport> stockReport(SomMiraviaListingPageSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMiraviaListingReport> pageResult = somMiraviaListingMapper.stockReport(searchVo, pageRequest);

        if (!pageResult.getList().isEmpty()) {
            List<SomMiraviaWarehouse> warehouseList = somMiraviaWarehouseMapper.all();
            Map<String, String> warehouseMap = warehouseList.stream().collect(Collectors.toMap(k -> k.getWarehouseCode(), v -> v.getWarehouseName(), (x1, x2) -> x1));
            List<McStockInfo> stockList = mcStockInfoMapper.createQuery().andIn("product_main_code", pageResult.getList().stream().map(x -> x.getProductMainCode()).collect(Collectors.toList())).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
            List<SomMiraviaWarehouseConfig> warehouseConfigList = somMiraviaWarehouseConfigMapper.all();
            Map<String, List<SomMiraviaWarehouseConfig>> warehouseMapByCode = warehouseConfigList.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode()));

            for (SomMiraviaListingReport report : pageResult.getList()) {
                // 获取设置的安全库存
                Integer safetyInventory = (null == report.getSafetyStock() ? 0 : report.getSafetyStock());

                String warehouseQuantities = report.getWarehouseQuantities();
                JsonNode jsonNode = objectMapper.readTree(warehouseQuantities);
                Map<String, Integer> jsonMap = StreamSupport.stream(jsonNode.spliterator(), false)
                        .collect(Collectors.toMap(n -> n.get("warehouse_code").asText(), n -> n.get("quantity").asInt()));

                List<SomMiraviaListingReport.WarehouseInventory> list = new ArrayList<>();
                // 总库存
                BigDecimal totalStock = BigDecimal.ZERO;
                // 展示所有的已配置的平台仓库
                for (String warehouseCode : warehouseMapByCode.keySet()) {
                    SomMiraviaListingReport.WarehouseInventory warehouseInventory = new SomMiraviaListingReport.WarehouseInventory();
                    warehouseInventory.setWarehouseCode(warehouseCode);
                    warehouseInventory.setWarehouseName(warehouseMap.getOrDefault(warehouseCode, null));
                    warehouseInventory.setExistStock(jsonMap.getOrDefault(warehouseCode, 0));
                    // 获取仓库库区  通过Map获取
                    List<SomMiraviaWarehouseConfig> warehouseAreaList = warehouseMapByCode.get(warehouseCode);
                    BigDecimal stock = BigDecimal.ZERO;
                    for (SomMiraviaWarehouseConfig warehouse : warehouseAreaList) {
                        McStockInfo orDefault = stockMap.getOrDefault(warehouse.getUseableWarehouseCode() + warehouse.getUseableStorageCode() + report.getProductMainCode(), null);
                        if (ObjectUtil.isNotEmpty(orDefault)) {
                            stock = stock.add(BigDecimal.valueOf(orDefault.getTotalStock()));
                        }
                    }
                    //减安全库存
                    stock = stock.subtract(BigDecimal.valueOf(safetyInventory));
                    if (stock.intValue() < 0) {
                        stock = BigDecimal.ZERO;
                    }
                    totalStock = totalStock.add(stock);
                    warehouseInventory.setQuantity(stock.intValue());
                    list.add(warehouseInventory);
                }
                report.setStock(totalStock.intValue());
                if (report.getStock() < 0) {
                    report.setStock(0);
                }
                report.setList(list);
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomMiraviaListingReport.class, searchVo);
    }

    public String export(SomMiraviaListingPageSearchVo searchVo) throws JsonProcessingException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomMiraviaListingReport> records = stockReport(searchVo).getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Miravia可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomMiraviaListingReport.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
