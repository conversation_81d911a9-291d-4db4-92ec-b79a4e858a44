package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomDealRuleConfig;
import com.zielsmart.mc.vo.SomDealRuleConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-01-06
 */

@SqlResource("somDealRuleConfig")
public interface SomDealRuleConfigMapper extends BaseMapper<SomDealRuleConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomDealRuleConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomDealRuleConfigVo> queryByPage(@Param("searchVo") SomDealRuleConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryBySiteDealType
     * 根据站点和类型查询配置信息
     * @param site
     * @param dealType
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomDealRuleConfigVo>}
     * <AUTHOR>
     * @history
     */
    SomDealRuleConfigVo queryBySiteDealType(@Param("site") String site, @Param("dealType") String dealType);
}
