package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* lost索赔case表
* gen by 代码生成器 2023-04-03
*/

@Table(name="mc.som_lost_case")
public class SomLostCase implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * CASE ID
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * 10.索赔中 20.已赔付 99.已关闭
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomLostCase() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* CASE ID
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* CASE ID
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* 10.索赔中 20.已赔付 99.已关闭
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 10.索赔中 20.已赔付 99.已关闭
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
