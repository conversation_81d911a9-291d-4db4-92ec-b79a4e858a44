package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.McCustomerGroupConfigVo;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-01-16
 */

@SqlResource("somDealRuleValidationConfig")
public interface SomDealRuleValidationConfigMapper extends BaseMapper<SomDealRuleValidationConfig> {
    /**
     * queryByPage
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomDealRuleValidationConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomDealRuleValidationConfigVo> queryByPage(@Param("searchVo") SomDealRuleValidationConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<McCustomerGroupConfigVo> queryCustomerConfig();
}
