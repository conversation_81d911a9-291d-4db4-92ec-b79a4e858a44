package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Amazon类目偏好设置表
 * gen by 代码生成器 2023-12-01
 */

@Table(name = "mc.zlcc_catalog_amazon_preference_setting")
public class ZlccCatalogAmazonPreferenceSetting implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 我司销售的大类目ID
     */
    @Column("browse_node_id")
    private Long browseNodeId;
    /**
     * 我司销售的大类目名称
     */
    @Column("browse_node_name")
    private String browseNodeName;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_date")
    private Date createDate;

    @Column("browse_parent_id")
    private Long browseParentId;

    public ZlccCatalogAmazonPreferenceSetting() {
    }


    public Long getBrowseParentId() {
        return browseParentId;
    }

    public void setBrowseParentId(Long browseParentId) {
        this.browseParentId = browseParentId;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 我司销售的大类目ID
     *
     * @return
     */
    public Long getBrowseNodeId() {
        return browseNodeId;
    }

    /**
     * 我司销售的大类目ID
     *
     * @param browseNodeId
     */
    public void setBrowseNodeId(Long browseNodeId) {
        this.browseNodeId = browseNodeId;
    }

    /**
     * 我司销售的大类目名称
     *
     * @return
     */
    public String getBrowseNodeName() {
        return browseNodeName;
    }

    /**
     * 我司销售的大类目名称
     *
     * @param browseNodeName
     */
    public void setBrowseNodeName(String browseNodeName) {
        this.browseNodeName = browseNodeName;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     *
     * @param createDate
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

}
