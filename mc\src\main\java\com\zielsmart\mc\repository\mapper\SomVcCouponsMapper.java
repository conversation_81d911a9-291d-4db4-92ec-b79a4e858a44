package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomVcCouponsPageSearchVo;
import com.zielsmart.mc.vo.SomVcCouponsVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;

@SqlResource("somVcCoupons")
public interface SomVcCouponsMapper extends BaseMapper<SomVcCoupons> {

    PageResult<SomVcCouponsVo> queryByPage(@Param("searchVo")SomVcCouponsPageSearchVo searchVo, PageRequest pageRequest);

    List<SomVcCouponsVo> queryList(@Param("searchVo")SomVcCouponsPageSearchVo searchVo);

    /**
     * 唯一性验证
     * @param searchVo SomVcCouponsVo
     * @return Integer 查询到的已存在数量
     */
    Integer checkUnique(@Param("searchVo") SomVcCouponsVo searchVo);

}
