package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomVcPriceDiscountPageSearchVo;
import com.zielsmart.mc.vo.SomVcPriceDiscountVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.mapper
 * @title SomVcDotdMapper
 * @description Vc Price Discount表管理
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somVcPriceDiscount")
public interface SomVcPriceDiscountMapper extends BaseMapper<SomVcPriceDiscount> {

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    查询参数
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomVcPriceDiscountVo>}
     * <AUTHOR>
     */
    PageResult<SomVcPriceDiscountVo> queryByPage(SomVcPriceDiscountPageSearchVo searchVo, PageRequest pageRequest);
}
