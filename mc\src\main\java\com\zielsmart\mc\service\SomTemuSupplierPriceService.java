package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuSupplierPriceService {

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    
    @Resource
    private SomTemuSupplierPriceMapper somTemuSupplierPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomTemuCrossSupplierPriceMapper somTemuCrossSupplierPriceMapper;
    @Resource
    private SomTemuCrossSkuMapper somTemuCrossSkuMapper;
    @Resource
    private SomTemuSkuService somTemuSkuService;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuSupplierPriceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuSupplierPriceVo> queryByPage(SomTemuSupplierPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuSupplierPriceVo> pageResult = dynamicSqlManager.getMapper(SomTemuSupplierPriceMapper.class).queryByPage(searchVo, pageRequest);

        // 关联Temu供货价
        if (!pageResult.getList().isEmpty()) {
            // 本本、跨境主体类型
            List<String> localType = new ArrayList<>(Arrays.asList("美国主体", "欧洲主体"));
            List<String> crossType = new ArrayList<>(Collections.singletonList("香港主体"));

            // 查询字典信息
            List<McDictionaryInfo> mcDictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery()
                    .andEq("item_type_code", "TemuAccount")
                    .andEq("item_value4", "1")
                    .select();

            // 本本、跨境主体账号
            List<String> localAccount = new ArrayList<>();
            List<String> crossAccount = new ArrayList<>();
            mcDictionaryInfoList.forEach(item -> {
                if (localType.contains(item.getItemValue1())) {
                    localAccount.add(item.getItemValue3());
                }
                if (crossType.contains(item.getItemValue1())) {
                    crossAccount.add(item.getItemValue3());
                }
            });

            // 本本、跨境sku_id
            List<Long> localSkuIdList = new ArrayList<>();
            List<Long> crossSkuIdList = new ArrayList<>();
            pageResult.getList().forEach(item -> {
                if (localAccount.contains(item.getAccountId()) && item.getProductSkuId() != null) {
                    localSkuIdList.add(Long.parseLong(item.getProductSkuId()));
                }
                if (crossAccount.contains(item.getAccountId()) && item.getProductSkuId() != null) {
                    crossSkuIdList.add(Long.parseLong(item.getProductSkuId()));
                }
            });

            // 本本、跨境供货价
            List<SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceList = Collections.emptyList();
            if (!localSkuIdList.isEmpty()) {
                somTemuLocalSupplierPriceList = dynamicSqlManager.getMapper(SomTemuLocalSupplierPriceMapper.class).createLambdaQuery()
                        .andIn("sku_id", localSkuIdList)
                        .select();
            }
            Map<String, SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceMap = somTemuLocalSupplierPriceList.stream().collect(Collectors.toMap(
                    x -> x.getSkuId().toString() + x.getAccountId(),
                    Function.identity(),
                    (x1, x2) -> x1
            ));
            List<SomTemuCrossSupplierPrice> somTemuCrossSupplierPriceList = Collections.emptyList();
            if (!crossSkuIdList.isEmpty()) {
                somTemuCrossSupplierPriceList = dynamicSqlManager.getMapper(SomTemuCrossSupplierPriceMapper.class).createLambdaQuery()
                        .andIn("product_sku_id", crossSkuIdList)
                        .select();
                // 也可以使用公共方法querySupplierPrice jsonb_array_elements函数 查询多列supplierPrice数据
                // somTemuCrossSupplierPriceList = somTemuCrossSupplierPriceMapper.querySupplierPrice(new ArrayList<>(), new ArrayList<>(), crossSkuIdList);
            }
            Map<String, SomTemuCrossSupplierPrice> somTemuCrossSupplierPriceMap = somTemuCrossSupplierPriceList.stream().collect(Collectors.toMap(
                    x -> x.getProductSkuId().toString() + x.getAccountId(),
                    Function.identity(),
                    (x1, x2) -> x1
            ));

            // 关联供货价
            pageResult.getList().forEach(item -> {
                // 本本
                if (localAccount.contains(item.getAccountId())) {
                    String key = item.getProductSkuId() + item.getAccountId();
                    if (somTemuLocalSupplierPriceMap.containsKey(key)) {
                        SomTemuLocalSupplierPrice somTemuLocalSupplierPrice = somTemuLocalSupplierPriceMap.get(key);
                        if (ObjectUtil.isNotEmpty(somTemuLocalSupplierPrice)) {
                            item.setSupplierPrice(somTemuLocalSupplierPrice.getSupplierPrice());
                            item.setSupplierPriceCurrency(somTemuLocalSupplierPrice.getCurrency());
                        }
                    }
                }
                // 跨境
                if (crossAccount.contains(item.getAccountId())) {
                    String key = item.getProductSkuId() + item.getAccountId();
                    if (somTemuCrossSupplierPriceMap.containsKey(key)) {
                        SomTemuCrossSupplierPrice somTemuCrossSupplierPrice = somTemuCrossSupplierPriceMap.get(key);
                        // 安全解析JSON
                        try {
                            Object siteSupplierPrices = somTemuCrossSupplierPrice.getSiteSupplierPrices();
                            if (ObjectUtil.isEmpty(siteSupplierPrices)) {
                                return;
                            }
                            String siteSupplierPricesJson = siteSupplierPrices.toString();
                            JSONArray siteSupplierPricesArray  = JSONUtil.parseArray(siteSupplierPricesJson);
                            // 三方api返回的价格列表异常为空，则不处理
                            if (ObjectUtil.isEmpty(siteSupplierPricesArray)) {
                                return;
                            }
                            Map<String, BigDecimal> siteMap = new HashMap<>();
                            for (Object obj : siteSupplierPricesArray) {
                                JSONObject jsonObject = (JSONObject) obj;
                                String site = jsonObject.getStr("site");
                                siteMap.put(site, jsonObject.getBigDecimal("supplierPrice"));
                            }
                            if (siteMap.containsKey(item.getSite())) {
                                BigDecimal supplierPrice = siteMap.get(item.getSite());
                                if (supplierPrice != null) {
                                    item.setSupplierPrice(supplierPrice.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_UP));
                                }
                                item.setSupplierPriceCurrency(somTemuCrossSupplierPrice.getCurrency());
                            }
                        } catch (Exception e) {
                            log.error("JSON解析异常", e);
                        }
                    }
                }
            });
        }

        return ConvertUtils.pageConvert(pageResult, SomTemuSupplierPriceVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somTemuSupplierPriceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuSupplierPriceVo somTemuSupplierPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuSupplierPriceVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if(StrUtil.isEmpty(somTemuSupplierPriceVo.getAccountId())){
            throw new ValidateException("账户ID不能为空");
        }
        validateSupplierPrice(somTemuSupplierPriceVo);

        long exist = somTemuSupplierPriceMapper.createLambdaQuery()
                .andEq("account_id",somTemuSupplierPriceVo.getAccountId())
                .andEq("site",somTemuSupplierPriceVo.getSite())
                .andEq("seller_sku",somTemuSupplierPriceVo.getSellerSku())
                .count();

        if (exist>0) {
            throw new ValidateException("您输入的产品在系统中已经维护供货价，不允许重复维护");
        }

        //List<SomTemuSupplierPrice> insertList = generateInsertList(Arrays.asList(somTemuSupplierPriceVo),tokenUser,somTemuSupplierPriceVo.getAccountId());

        SomTemuSupplierPrice somTemuSupplierPrice =  ConvertUtils.beanConvert(somTemuSupplierPriceVo, SomTemuSupplierPrice.class);
        Date createTime = DateTime.now().toJdkDate();

        somTemuSupplierPrice.setAid(IdUtil.fastSimpleUUID());
        somTemuSupplierPrice.setCreateName(tokenUser.getUserName());
        somTemuSupplierPrice.setCreateNum(tokenUser.getJobNumber());
        somTemuSupplierPrice.setCreateTime(createTime);

        // 本本、跨境主体
        List<String> localType = new ArrayList<>(Arrays.asList("美国主体", "欧洲主体"));
        List<String> crossType = new ArrayList<>(Collections.singletonList("香港主体"));

        // 查询字典信息
        McDictionaryInfo mcDictionaryInfo = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .andEq("item_value3", somTemuSupplierPriceVo.getAccountId())
                .single();

        // 本本listing
        if (localType.contains(mcDictionaryInfo.getItemValue1())) {
            SomTemuLocalSku temuListing = dynamicSqlManager.getMapper(SomTemuLocalSkuMapper.class).createLambdaQuery()
                    .andEq("site", somTemuSupplierPriceVo.getSite())
                    .andEq("account_id", somTemuSupplierPriceVo.getAccountId())
                    .andEq("seller_sku", somTemuSupplierPriceVo.getSellerSku())
                    .single();
            somTemuSupplierPrice.setProductSkcId(temuListing.getGoodsId().toString());
            somTemuSupplierPrice.setProductSkuId(temuListing.getSkuId().toString());
            somTemuSupplierPrice.setProductId(temuListing.getGoodsId().toString());
        }

        // 跨境listing
        if (crossType.contains(mcDictionaryInfo.getItemValue1())) {
            SomTemuCrossSku temuListing = dynamicSqlManager.getMapper(SomTemuCrossSkuMapper.class).createLambdaQuery()
                    .andEq("site", somTemuSupplierPriceVo.getSite())
                    .andEq("account_id", somTemuSupplierPriceVo.getAccountId())
                    .andEq("seller_sku", somTemuSupplierPriceVo.getSellerSku())
                    .single();
            SomTemuCrossGoods somTemuCrossGoods = dynamicSqlManager.getMapper(SomTemuCrossGoodsMapper.class).createLambdaQuery()
                    .andEq("aid", temuListing.getGoodsId())
                    .single();
            somTemuSupplierPrice.setProductSkcId(somTemuCrossGoods.getProductSkcId().toString());
            somTemuSupplierPrice.setProductSkuId(temuListing.getProductSkuId().toString());
            somTemuSupplierPrice.setProductId(temuListing.getProductId().toString());
        }

        if (StrUtil.isBlank(somTemuSupplierPrice.getProductSkuId())) {
            throw new ValidateException("未找到有效的listing");
        }

        somTemuSupplierPriceMapper.insert(somTemuSupplierPrice);
    }

    private List<SomTemuSupplierPrice> generateInsertList(List<SomTemuSupplierPriceVo> list,TokenUserInfo tokenUser,String targetAccountId) {
        //根据配置的站点 店铺ID对应关系，获取要生成的店铺ID
//        List<McDictionaryInfo> dictList = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "TemuSiteAccountId").select();
//        Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(x -> x.getItemLable(), v -> v.getItemValue(), (x1, x2) -> x1));
        List<String> accountIdList = list.stream().map(x -> x.getAccountId()).collect(Collectors.toList());
        List<SomTemuSkuBasicVo> skuBasicVos = somTemuSkuService.getTemuSkuByAccountIds(accountIdList);
        Map<String, List<SomTemuSkuBasicVo>> temuGroupMap = skuBasicVos.stream().collect(Collectors.groupingBy(e -> e.getSite() + e.getAccountId() + e.getSellerSku()));

        List<SomTemuSupplierPrice> insertList = new ArrayList<>();
        Date createTime = DateTime.now().toJdkDate();

        for (SomTemuSupplierPriceVo priceVo : list) {
//            String accountIdStrs = dictMap.get(priceVo.getSite());
//            String[] accountIdArr = accountIdStrs.split(",");
//            Set<String> accountIdSet = new HashSet<>(Arrays.asList(accountIdArr));
//            if (targetAccountId != null) {
//                accountIdSet.add(targetAccountId);
//            }
            //感觉账号站点展示码 查询 skc  sku id
//            for (String accountId : accountIdSet) {
                List<SomTemuSkuBasicVo> somTemuSkuBasicVos = temuGroupMap.get(priceVo.getSite() + priceVo.getAccountId() + priceVo.getSellerSku());
                if (ObjectUtil.isNotEmpty(somTemuSkuBasicVos)) {
                    for (SomTemuSkuBasicVo somTemuSkuBasicVo : somTemuSkuBasicVos) {
                        if (!ObjectUtil.isEmpty(somTemuSkuBasicVo)) {
                            SomTemuSupplierPrice somTemuSupplierPrice = ConvertUtils.beanConvert(priceVo, SomTemuSupplierPrice.class);
                            somTemuSupplierPrice.setAid(IdUtil.fastSimpleUUID());
                            somTemuSupplierPrice.setAccountId(priceVo.getAccountId());
                            somTemuSupplierPrice.setProductSkcId(somTemuSkuBasicVo.getProductSkcId());
                            somTemuSupplierPrice.setProductSkuId(somTemuSkuBasicVo.getProductSkuId());
                            somTemuSupplierPrice.setProductId(somTemuSkuBasicVo.getProductId());
                            somTemuSupplierPrice.setCreateName(tokenUser.getUserName());
                            somTemuSupplierPrice.setCreateNum(tokenUser.getJobNumber());
                            somTemuSupplierPrice.setCreateTime(createTime);
                            insertList.add(somTemuSupplierPrice);
                        }
                    }
                }
//            }
        }
        return insertList;
    }


    private void validateSupplierPrice(SomTemuSupplierPriceVo somTemuSupplierPriceVo) throws ValidateException {
        if (StrUtil.isEmpty(somTemuSupplierPriceVo.getAccountId()) ||StrUtil.isEmpty(somTemuSupplierPriceVo.getSite()) || StrUtil.isEmpty(somTemuSupplierPriceVo.getSellerSku())) {
            throw new ValidateException("店铺ID、站点、展示码不能为空");
        }
        if(somTemuSupplierPriceVo.getCost() == null || somTemuSupplierPriceVo.getCost().compareTo(BigDecimal.ZERO) <= 0){
            throw new ValidateException("成本价不能为空且必须大于0");
        }
        if(somTemuSupplierPriceVo.getEstimateSupplierPrice() == null || somTemuSupplierPriceVo.getEstimateSupplierPrice().compareTo(BigDecimal.ZERO) <= 0){
            throw new ValidateException("预期供货价不能为空且必须大于0");
        }
        if(somTemuSupplierPriceVo.getMinimumSupplierPrice() == null || somTemuSupplierPriceVo.getMinimumSupplierPrice().compareTo(BigDecimal.ZERO) <= 0){
            throw new ValidateException("最低供货价不能为空且必须大于0");
        }
        if(somTemuSupplierPriceVo.getEstimateGrossMargin() == null){
            throw new ValidateException("预期毛利率不能为空");
        }
        if (StrUtil.isBlank(somTemuSupplierPriceVo.getCostCurrency())) {
            throw new ValidateException("成本价币种不能为空");
        }
//        if (StrUtil.isBlank(somTemuSupplierPriceVo.getSupplierPriceCurrency())) {
//            throw new ValidateException("供货价币种不能为空");
//        }
    }

    /**
     * update
     * 修改
     * @param somTemuSupplierPriceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomTemuSupplierPriceVo somTemuSupplierPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuSupplierPriceVo) || StrUtil.isEmpty(somTemuSupplierPriceVo.getSite())|| StrUtil.isEmpty(somTemuSupplierPriceVo.getSellerSku())) {
            throw new ValidateException("站点、展示码不能为空");
        }
        List<SomTemuSupplierPrice> updateList = somTemuSupplierPriceMapper.createLambdaQuery()
                .andEq("site", somTemuSupplierPriceVo.getSite())
                .andEq("seller_sku", somTemuSupplierPriceVo.getSellerSku())
                .select();

        for (SomTemuSupplierPrice price : updateList) {
            price.setCost(somTemuSupplierPriceVo.getCost());
            price.setEstimateSupplierPrice(somTemuSupplierPriceVo.getEstimateSupplierPrice());
            price.setMinimumSupplierPrice(somTemuSupplierPriceVo.getMinimumSupplierPrice());
            price.setEstimateGrossMargin(somTemuSupplierPriceVo.getEstimateGrossMargin());
            price.setCostCurrency(somTemuSupplierPriceVo.getCostCurrency());
            somTemuSupplierPriceMapper.updateById(price);
        }
    }

    /**
     * delete
     * 删除
     * @param somTemuSupplierPriceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuSupplierPriceVo somTemuSupplierPriceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuSupplierPriceVo) || ObjectUtil.isEmpty(somTemuSupplierPriceVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
         somTemuSupplierPriceMapper.createLambdaQuery().andIn("aid", somTemuSupplierPriceVo.getAidList()).delete();
    }

    public String export(SomTemuSupplierPricePageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomTemuSupplierPriceVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "Temu供货价管理管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomTemuSupplierPriceVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }

    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomTemuSupplierPriceVo> list, TokenUserInfo tokenUser) throws ValidateException {
        List<SomTemuSupplierPrice> all = somTemuSupplierPriceMapper.all();
        Map<String, List<String>> dbAidMap = all.stream().collect(Collectors.groupingBy(e -> e.getAccountId() + e.getSite() + e.getSellerSku(), Collectors.mapping(y -> y.getAid(), Collectors.toList())));

        //校验数据
        List<String> deleteAid = new ArrayList<>();
        for (SomTemuSupplierPriceVo priceVo : list) {
            //循环的时候顺便把毛利率乘以100
            if (dbAidMap.containsKey(priceVo.getAccountId() + priceVo.getSite() + priceVo.getSellerSku())) {
                deleteAid.addAll(dbAidMap.get(priceVo.getAccountId() + priceVo.getSite() + priceVo.getSellerSku()));
            }
            validateSupplierPrice(priceVo);
            priceVo.setEstimateGrossMargin(priceVo.getEstimateGrossMargin().multiply(BigDecimal.valueOf(100)));
        }

        //校验 店铺ID 站点 展示码是否重复。 根据店铺ID+站点+展示码分组，获取数据数量，如果数量大于1，说明重复，不允许导入。
        Map<String, Long> groupMap = list.stream().collect(Collectors.groupingBy(e ->e.getAccountId()+":"+ e.getSite() +":"+ e.getSellerSku(), Collectors.counting()));
        String repeatSiteSeller = groupMap.entrySet().stream().filter(e -> e.getValue() > 1).map(k -> k.getKey()).collect(Collectors.joining("\n"));
        if (StrUtil.isNotBlank(repeatSiteSeller)) {
            throw new ValidateException("Excel中 店铺ID、站点、展示码重复：\n" + repeatSiteSeller);
        }

        List<SomTemuSupplierPrice> insertList = generateInsertListV2(list,tokenUser,null);
        if (!insertList.isEmpty()) {
            if (!deleteAid.isEmpty()) {
                somTemuSupplierPriceMapper.createLambdaQuery().andIn("aid", deleteAid).delete();
            }
            somTemuSupplierPriceMapper.insertBatch(insertList);
        }
    }

    private List<SomTemuSupplierPrice> generateInsertListV2(List<SomTemuSupplierPriceVo> list,TokenUserInfo tokenUser,String targetAccountId) {
        // 本本、跨境主体类型
        List<String> localType = new ArrayList<>(Arrays.asList("美国主体", "欧洲主体"));
        List<String> crossType = new ArrayList<>(Collections.singletonList("香港主体"));

        // 查询字典信息
        List<McDictionaryInfo> mcDictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .select();

        // 本本、跨境主体账号
        List<String> localAccount = new ArrayList<>();
        List<String> crossAccount = new ArrayList<>();
        mcDictionaryInfoList.forEach(item -> {
            if (localType.contains(item.getItemValue1())) {
                localAccount.add(item.getItemValue3());
            }
            if (crossType.contains(item.getItemValue1())) {
                crossAccount.add(item.getItemValue3());
            }
        });

        // 本本、跨境 accountId sku
        List<String> localAccountIdList = new ArrayList<>();
        List<String> localSkuList = new ArrayList<>();
        List<String> crossAccountIdList = new ArrayList<>();
        List<String> crossSkuList = new ArrayList<>();
        for (SomTemuSupplierPriceVo item : list) {
            if (localAccount.contains(item.getAccountId())) {
                localAccountIdList.add(item.getAccountId());
                localSkuList.add(item.getSellerSku());
            }
            if (crossAccount.contains(item.getAccountId())) {
                crossAccountIdList.add(item.getAccountId());
                crossSkuList.add(item.getSellerSku());
            }
        }

        // 本本listing
        List<SomTemuLocalSku> somTemuLocalSkuList = Collections.emptyList();
        if (!localAccountIdList.isEmpty()) {
            somTemuLocalSkuList = dynamicSqlManager.getMapper(SomTemuLocalSkuMapper.class).createLambdaQuery()
                    .andIn("account_id", localAccountIdList)
                    .andIn("seller_sku", localSkuList)
                    .select();
        }
        Map<String, SomTemuLocalSku> somTemuLocalSkuMap = somTemuLocalSkuList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getAccountId() + x.getSellerSku(),
                Function.identity(),
                (x1, x2) -> x1
        ));

        // 跨境listing
        List<SomTemuCrossSkuVo> somTemuCrossSkuList = Collections.emptyList();
        if (!crossAccountIdList.isEmpty()) {
            somTemuCrossSkuList = somTemuCrossSkuMapper.queryList(crossAccountIdList, crossSkuList);
        }
        Map<String, SomTemuCrossSkuVo> somTemuCrossSkuMap = somTemuCrossSkuList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getAccountId() + x.getSellerSku(),
                Function.identity(),
                (x1, x2) -> x1
        ));

        List<SomTemuSupplierPrice> insertList = new ArrayList<>();

        for (SomTemuSupplierPriceVo priceVo : list) {
            Date createTime = DateTime.now().toJdkDate();
            SomTemuSupplierPrice somTemuSupplierPrice = ConvertUtils.beanConvert(priceVo, SomTemuSupplierPrice.class);
            somTemuSupplierPrice.setAid(IdUtil.fastSimpleUUID());
            somTemuSupplierPrice.setAccountId(priceVo.getAccountId());
            somTemuSupplierPrice.setCreateName(tokenUser.getUserName());
            somTemuSupplierPrice.setCreateNum(tokenUser.getJobNumber());
            somTemuSupplierPrice.setCreateTime(createTime);

            String key = priceVo.getSite() + priceVo.getAccountId() + priceVo.getSellerSku();
            if (localAccount.contains(priceVo.getAccountId())) {
                if (somTemuLocalSkuMap.containsKey(key)) {
                    SomTemuLocalSku tempListing = somTemuLocalSkuMap.get(key);
                    somTemuSupplierPrice.setProductSkcId(tempListing.getGoodsId().toString());
                    somTemuSupplierPrice.setProductSkuId(tempListing.getSkuId().toString());
                    somTemuSupplierPrice.setProductId(tempListing.getGoodsId().toString());
                } else {
                    continue;
                }
            }
            if (crossAccount.contains(priceVo.getAccountId())) {
                if (somTemuCrossSkuMap.containsKey(key)) {
                    SomTemuCrossSkuVo tempListing = somTemuCrossSkuMap.get(key);
                    somTemuSupplierPrice.setProductSkcId(tempListing.getProductSkcId().toString());
                    somTemuSupplierPrice.setProductSkuId(tempListing.getProductSkuId().toString());
                    somTemuSupplierPrice.setProductId(tempListing.getProductId().toString());
                } else {
                    continue;
                }
            }

            insertList.add(somTemuSupplierPrice);
        }

        return insertList;
    }

    public List<String> sellerSku(SomTemuSupplierPriceVo priceVo) throws ValidateException {
        if (priceVo == null || priceVo.getSite() == null || priceVo.getAccountId() == null) {
            throw new ValidateException("站点、账户ID不能为空");
        }

        // 本本、跨境主体
        List<String> localType = new ArrayList<>(Arrays.asList("美国主体", "欧洲主体"));
        List<String> crossType = new ArrayList<>(Collections.singletonList("香港主体"));

        // 查询字典信息
        McDictionaryInfo mcDictionaryInfo = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .andEq("item_value3", priceVo.getAccountId())
                .single();

        if (mcDictionaryInfo == null) {
            return new ArrayList<>();
        }

        // 本本listing
        if (localType.contains(mcDictionaryInfo.getItemValue1())) {
            List<SomTemuLocalSku> temuListing = dynamicSqlManager.getMapper(SomTemuLocalSkuMapper.class).createLambdaQuery()
                    .andEq("site", priceVo.getSite())
                    .andEq("account_id", priceVo.getAccountId())
                    .select();
            if (temuListing.isEmpty()) {
                return new ArrayList<>();
            }
            return temuListing.stream().map(SomTemuLocalSku::getSellerSku).distinct().collect(Collectors.toList());
        }

        // 跨境listing
        if (crossType.contains(mcDictionaryInfo.getItemValue1())) {
            List<SomTemuCrossSku> temuListing = dynamicSqlManager.getMapper(SomTemuCrossSkuMapper.class).createLambdaQuery()
                    .andEq("site", priceVo.getSite())
                    .andEq("account_id", priceVo.getAccountId())
                    .select();
            if (temuListing.isEmpty()) {
                return new ArrayList<>();
            }
            return temuListing.stream().map(SomTemuCrossSku::getSellerSku).distinct().collect(Collectors.toList());
        }

        return new ArrayList<>();
    }
}
