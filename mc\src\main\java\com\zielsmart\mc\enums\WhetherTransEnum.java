package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

/**
 * 翻译枚举
 */
@AllArgsConstructor
public enum WhetherTransEnum {

    YES(1, "是"),
    NO(0, "否"),

    ;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static WhetherTransEnum of(Integer type) {
        if (type == null) {
            return null;
        }
        for (WhetherTransEnum value : WhetherTransEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据类型获取描述
     *
     * @param type 状态
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ConsignmentSalesTypeEnum value : ConsignmentSalesTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型，通常用于导入
     *
     * @param desc 描述
     * @return 类型
     */
    public static Integer getTypeByDesc(String desc) {
        for (ConsignmentSalesTypeEnum value : ConsignmentSalesTypeEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getType();
            }
        }
        return null;
    }

    private final Integer type;
    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
