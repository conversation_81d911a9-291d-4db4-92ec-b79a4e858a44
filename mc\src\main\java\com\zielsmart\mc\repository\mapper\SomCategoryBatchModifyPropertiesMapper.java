package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomCategoryBatchModifyProperties;
import com.zielsmart.mc.vo.SomCategoryBatchModifyPropertiesPageSearchVo;
import com.zielsmart.mc.vo.SomCategoryBatchModifyPropertiesVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2025-07-28
 */

@SqlResource("somCategoryBatchModifyProperties")
public interface SomCategoryBatchModifyPropertiesMapper extends BaseMapper<SomCategoryBatchModifyProperties> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomCategoryBatchModifyPropertiesVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCategoryBatchModifyPropertiesVo> queryByPage(@Param("searchVo") SomCategoryBatchModifyPropertiesPageSearchVo searchVo);
}
