package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2025-04-14
 */

@Table(name = "mc.som_amazon_reimbursements")
public class SomAmazonReimbursements implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 赔付日期
     */
    @Column("approval_date")
    private Date approvalDate;
    /**
     * CASE ID
     */
    @Column("case_id")
    private String caseId;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * FNSKU
     */
    @Column("fnsku")
    private String fnsku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 赔付币种
     */
    @Column("foreign_currency")
    private String foreignCurrency;
    /**
     * 单个赔付金额（外币）
     */
    @Column("amount_per_unit")
    private BigDecimal amountPerUnit;
    /**
     * 赔付现金的数量
     */
    @Column("quantity_reimbursed_cash")
    private Integer quantityReimbursedCash;
    /**
     * 赔付库存的数量
     */
    @Column("quantity_reimbursed_inventory")
    private Integer quantityReimbursedInventory;
    /**
     * 总计赔付数量
     */
    @Column("quantity_reimbursed_total")
    private Integer quantityReimbursedTotal;
    /**
     * 赔付金额（外币）
     */
    @Column("amount_total")
    private BigDecimal amountTotal;
    /**
     * 本币币种
     */
    @Column("domestic_currency")
    private String domesticCurrency;
    /**
     * 赔付金额（本币）
     */
    @Column("amount_total_cny")
    private BigDecimal amountTotalCny;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 亚马逊订单。空值表示此赔付记录不是针对订单做的赔付
     */
    @Column("amazon_order_id")
    private String amazonOrderId;
    /**
     * reason
     */
    @Column("reason")
    private String reason;
    /**
     * 市场
     */
    @Column("market")
    private String market;
    /**
     * 赔偿报表ID
     */
    @Column("reimbursement_id")
    private String reimbursementId;

    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;

    public SomAmazonReimbursements() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 赔付日期
     *
     * @return
     */
    public Date getApprovalDate() {
        return approvalDate;
    }

    /**
     * 赔付日期
     *
     * @param approvalDate
     */
    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    /**
     * CASE ID
     *
     * @return
     */
    public String getCaseId() {
        return caseId;
    }

    /**
     * CASE ID
     *
     * @param caseId
     */
    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * FNSKU
     *
     * @return
     */
    public String getFnsku() {
        return fnsku;
    }

    /**
     * FNSKU
     *
     * @param fnsku
     */
    public void setFnsku(String fnsku) {
        this.fnsku = fnsku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 赔付币种
     *
     * @return
     */
    public String getForeignCurrency() {
        return foreignCurrency;
    }

    /**
     * 赔付币种
     *
     * @param foreignCurrency
     */
    public void setForeignCurrency(String foreignCurrency) {
        this.foreignCurrency = foreignCurrency;
    }

    /**
     * 单个赔付金额（外币）
     *
     * @return
     */
    public BigDecimal getAmountPerUnit() {
        return amountPerUnit;
    }

    /**
     * 单个赔付金额（外币）
     *
     * @param amountPerUnit
     */
    public void setAmountPerUnit(BigDecimal amountPerUnit) {
        this.amountPerUnit = amountPerUnit;
    }

    /**
     * 赔付现金的数量
     *
     * @return
     */
    public Integer getQuantityReimbursedCash() {
        return quantityReimbursedCash;
    }

    /**
     * 赔付现金的数量
     *
     * @param quantityReimbursedCash
     */
    public void setQuantityReimbursedCash(Integer quantityReimbursedCash) {
        this.quantityReimbursedCash = quantityReimbursedCash;
    }

    /**
     * 赔付库存的数量
     *
     * @return
     */
    public Integer getQuantityReimbursedInventory() {
        return quantityReimbursedInventory;
    }

    /**
     * 赔付库存的数量
     *
     * @param quantityReimbursedInventory
     */
    public void setQuantityReimbursedInventory(Integer quantityReimbursedInventory) {
        this.quantityReimbursedInventory = quantityReimbursedInventory;
    }

    /**
     * 总计赔付数量
     *
     * @return
     */
    public Integer getQuantityReimbursedTotal() {
        return quantityReimbursedTotal;
    }

    /**
     * 总计赔付数量
     *
     * @param quantityReimbursedTotal
     */
    public void setQuantityReimbursedTotal(Integer quantityReimbursedTotal) {
        this.quantityReimbursedTotal = quantityReimbursedTotal;
    }

    /**
     * 赔付金额（外币）
     *
     * @return
     */
    public BigDecimal getAmountTotal() {
        return amountTotal;
    }

    /**
     * 赔付金额（外币）
     *
     * @param amountTotal
     */
    public void setAmountTotal(BigDecimal amountTotal) {
        this.amountTotal = amountTotal;
    }

    /**
     * 本币币种
     *
     * @return
     */
    public String getDomesticCurrency() {
        return domesticCurrency;
    }

    /**
     * 本币币种
     *
     * @param domesticCurrency
     */
    public void setDomesticCurrency(String domesticCurrency) {
        this.domesticCurrency = domesticCurrency;
    }

    /**
     * 赔付金额（本币）
     *
     * @return
     */
    public BigDecimal getAmountTotalCny() {
        return amountTotalCny;
    }

    /**
     * 赔付金额（本币）
     *
     * @param amountTotalCny
     */
    public void setAmountTotalCny(BigDecimal amountTotalCny) {
        this.amountTotalCny = amountTotalCny;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 亚马逊订单。空值表示此赔付记录不是针对订单做的赔付
     *
     * @return
     */
    public String getAmazonOrderId() {
        return amazonOrderId;
    }

    /**
     * 亚马逊订单。空值表示此赔付记录不是针对订单做的赔付
     *
     * @param amazonOrderId
     */
    public void setAmazonOrderId(String amazonOrderId) {
        this.amazonOrderId = amazonOrderId;
    }

    /**
     * reason
     *
     * @return
     */
    public String getReason() {
        return reason;
    }

    /**
     * reason
     *
     * @param reason
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * 市场
     *
     * @return
     */
    public String getMarket() {
        return market;
    }

    /**
     * 市场
     *
     * @param market
     */
    public void setMarket(String market) {
        this.market = market;
    }

    /**
     * 赔偿报表ID
     *
     * @return
     */
    public String getReimbursementId() {
        return reimbursementId;
    }

    /**
     * 赔偿报表ID
     *
     * @param reimbursementId
     */
    public void setReimbursementId(String reimbursementId) {
        this.reimbursementId = reimbursementId;
    }

    public String getCreateNum() {
        return createNum;
    }

    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}
