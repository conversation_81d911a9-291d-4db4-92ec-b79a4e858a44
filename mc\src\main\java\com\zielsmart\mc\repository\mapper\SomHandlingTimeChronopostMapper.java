package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomHandlingTimeChronopostPageSearchVo;
import com.zielsmart.mc.vo.SomHandlingTimeChronopostVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-03-11
*/

@SqlResource("somHandlingTimeChronopost")
public interface SomHandlingTimeChronopostMapper extends BaseMapper<SomHandlingTimeChronopost> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomHandlingTimeChronopostVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomHandlingTimeChronopostVo> queryByPage(@Param("searchVo")SomHandlingTimeChronopostPageSearchVo searchVo, PageRequest pageRequest);

    @Update
    void deleteBySellerSkus(@Param("sellerSkus") List<String> sellerSkus);
}
