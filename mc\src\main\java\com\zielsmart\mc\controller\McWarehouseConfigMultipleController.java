package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McWarehouseConfigMultipleService;
import com.zielsmart.mc.vo.McWarehouseConfigMultiplePageSearchVo;
import com.zielsmart.mc.vo.McWarehouseConfigMultipleVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWarehouseConfigMultipleController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcWarehouseConfigMultiple", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "wayfair walmart多仓配置管理")
public class McWarehouseConfigMultipleController extends BasicController {

    @Resource
    McWarehouseConfigMultipleService mcWarehouseConfigMultipleService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McWarehouseConfigMultipleVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McWarehouseConfigMultipleVo>> queryByPage(@RequestBody McWarehouseConfigMultiplePageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWarehouseConfigMultipleService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcWarehouseConfigMultipleVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/更新")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McWarehouseConfigMultipleVo mcWarehouseConfigMultipleVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWarehouseConfigMultipleService.save(mcWarehouseConfigMultipleVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }


    /**
     * delete
     *
     * @param mcWarehouseConfigMultipleVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McWarehouseConfigMultipleVo mcWarehouseConfigMultipleVo) throws ValidateException {
        mcWarehouseConfigMultipleService.delete(mcWarehouseConfigMultipleVo);
        return ResultVo.ofSuccess(null);
    }
}
