package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 运费模板推送报表
 * gen by 代码生成器 2022-02-16
 */

@Table(name = "mc.som_push_delivery_template_record")
public class SomPushDeliveryTemplateRecord implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 批次
     */
    @Column("batch")
    private Date batch;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * seller_sku
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 展示码
     */
    @Column("display_product_code")
    private String displayProductCode;
    /**
     * SKU
     */
    @Column("product_main_code")
    private String productMainCode;
    /**
     * Listing标题
     */
    @Column("product_name")
    private String productName;
    /**
     * 推送前的运费模板名称
     */
    @Column("old_merchant_shipping_group_name")
    private String oldMerchantShippingGroupName;
    /**
     * 要推送的运费模板名称
     */
    @Column("new_merchant_shipping_group_name")
    private String newMerchantShippingGroupName;
    /**
     * json格式的库存数据
     */
    @Column("stock_info")
    private String stockInfo;
    /**
     * 推送状态;10未推送,20推送成功,99推送失败
     */
    @Column("sync_status")
    private Integer syncStatus;
    /**
     * 推送时间
     */
    @Column("sync_time")
    private Date syncTime;
    /**
     * 推送失败原因
     */
    @Column("sync_error_msg")
    private String syncErrorMsg;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomPushDeliveryTemplateRecord() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 批次
     *
     * @return
     */
    public Date getBatch() {
        return batch;
    }

    /**
     * 批次
     *
     * @param batch
     */
    public void setBatch(Date batch) {
        this.batch = batch;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * seller_sku
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * seller_sku
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getDisplayProductCode() {
        return displayProductCode;
    }

    /**
     * 展示码
     *
     * @param displayProductCode
     */
    public void setDisplayProductCode(String displayProductCode) {
        this.displayProductCode = displayProductCode;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getProductMainCode() {
        return productMainCode;
    }

    /**
     * SKU
     *
     * @param productMainCode
     */
    public void setProductMainCode(String productMainCode) {
        this.productMainCode = productMainCode;
    }

    /**
     * Listing标题
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Listing标题
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 推送前的运费模板名称
     *
     * @return
     */
    public String getOldMerchantShippingGroupName() {
        return oldMerchantShippingGroupName;
    }

    /**
     * 推送前的运费模板名称
     *
     * @param oldMerchantShippingGroupName
     */
    public void setOldMerchantShippingGroupName(String oldMerchantShippingGroupName) {
        this.oldMerchantShippingGroupName = oldMerchantShippingGroupName;
    }

    /**
     * 要推送的运费模板名称
     *
     * @return
     */
    public String getNewMerchantShippingGroupName() {
        return newMerchantShippingGroupName;
    }

    /**
     * 要推送的运费模板名称
     *
     * @param newMerchantShippingGroupName
     */
    public void setNewMerchantShippingGroupName(String newMerchantShippingGroupName) {
        this.newMerchantShippingGroupName = newMerchantShippingGroupName;
    }

    /**
     * json格式的库存数据
     *
     * @return
     */
    public String getStockInfo() {
        return stockInfo;
    }

    /**
     * json格式的库存数据
     *
     * @param stockInfo
     */
    public void setStockInfo(String stockInfo) {
        this.stockInfo = stockInfo;
    }

    /**
     * 推送状态;10未推送,20推送成功,99推送失败
     *
     * @return
     */
    public Integer getSyncStatus() {
        return syncStatus;
    }

    /**
     * 推送状态;10未推送,20推送成功,99推送失败
     *
     * @param syncStatus
     */
    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    /**
     * 推送时间
     *
     * @return
     */
    public Date getSyncTime() {
        return syncTime;
    }

    /**
     * 推送时间
     *
     * @param syncTime
     */
    public void setSyncTime(Date syncTime) {
        this.syncTime = syncTime;
    }

    /**
     * 推送失败原因
     *
     * @return
     */
    public String getSyncErrorMsg() {
        return syncErrorMsg;
    }

    /**
     * 推送失败原因
     *
     * @param syncErrorMsg
     */
    public void setSyncErrorMsg(String syncErrorMsg) {
        this.syncErrorMsg = syncErrorMsg;
    }
    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
