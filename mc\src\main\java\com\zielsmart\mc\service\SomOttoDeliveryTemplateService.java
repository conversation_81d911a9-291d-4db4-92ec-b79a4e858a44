package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomOttoDeliveryTemplate;
import com.zielsmart.mc.repository.entity.SomOttoDeliveryTemplateProduct;
import com.zielsmart.mc.repository.entity.SomOttoListing;
import com.zielsmart.mc.repository.mapper.SomOttoDeliveryTemplateMapper;
import com.zielsmart.mc.repository.mapper.SomOttoDeliveryTemplateProductMapper;
import com.zielsmart.mc.repository.mapper.SomOttoListingMapper;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.OttoTemplateVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplatePageSearchVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplateProductVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplateVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomOttoDeliveryTemplateService {
    @Resource
    private SomOttoDeliveryTemplateProductMapper somOttoDeliveryTemplateProductMapper;

    @Value("${spring.profiles.active}")
    public String activeProfile;

    @Value("${magic.head.token}")
    public String token;
    @Resource
    private SomOttoListingMapper somOttoListingMapper;

    @Resource
    private SomOttoDeliveryTemplateMapper somOttoDeliveryTemplateMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private IMagicService magicService;

    private boolean isTest() {
        return !"prod".equals(activeProfile);
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOttoDeliveryTemplateVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomOttoDeliveryTemplateVo> queryByPage(SomOttoDeliveryTemplatePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOttoDeliveryTemplateVo> pageResult = dynamicSqlManager.getMapper(SomOttoDeliveryTemplateMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomOttoDeliveryTemplateVo.class, searchVo);
    }

    /**
     * save
     * 添加/编辑
     *
     * @param ottoVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomOttoDeliveryTemplateVo ottoVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(ottoVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isBlank(ottoVo.getShippingProfileName())) {
            throw new ValidateException("运费模版名称不能为空");
        }
        if (StrUtil.isBlank(ottoVo.getWorkingDays())) {
            throw new ValidateException("工作时间不能为空");
        }
        if (StrUtil.isBlank(ottoVo.getOrderCutoff())) {
            throw new ValidateException("截单时间不能为空");
        }
        if (StrUtil.isBlank(ottoVo.getDeliveryType())) {
            throw new ValidateException("投递类型不能为空");
        }
        if (ottoVo.getDefaultProcessingTime() == null) {
            throw new ValidateException("默认发货时效不能为空");
        }
        if (ottoVo.getTransportTime() == null) {
            throw new ValidateException("运输周期不能为空");
        }


        SomOttoDeliveryTemplate somOttoDeliveryTemplate = ConvertUtils.beanConvert(ottoVo, SomOttoDeliveryTemplate.class);
        if (ottoVo.getAid() == null) {
            //校验运费模版名称是唯一的
            long count = somOttoDeliveryTemplateMapper.createLambdaQuery().andEq("shipping_profile_name", ottoVo.getShippingProfileName()).count();
            if (count > 0) {
                throw new ValidateException("运费模版名称已存在，请重新输入");
            }
            //新增操作
            somOttoDeliveryTemplate.setAid(IdUtil.fastSimpleUUID());
            somOttoDeliveryTemplate.setCreateName(tokenUser.getUserName());
            somOttoDeliveryTemplate.setCreateNum(tokenUser.getJobNumber());
            somOttoDeliveryTemplate.setCreateTime(DateTime.now().toJdkDate());
            if (isTest() || createOttoTemplate(somOttoDeliveryTemplate)) {
                if (somOttoDeliveryTemplate.getShippingProfileId() == null) {
                    //方便测试环境测试，随机设置运费模版ID
                    somOttoDeliveryTemplate.setShippingProfileId(IdUtil.fastSimpleUUID());
                }
                somOttoDeliveryTemplateMapper.insert(somOttoDeliveryTemplate);
            }
        } else {
            //校验运费模版名称是唯一的
            long count = somOttoDeliveryTemplateMapper.createLambdaQuery().andEq("shipping_profile_name", ottoVo.getShippingProfileName()).andNotEq("aid", ottoVo.getAid()).count();
            if (count > 0) {
                throw new ValidateException("运费模版名称已存在，请重新输入");
            }
            if (isTest() || updateOttoTemplate(somOttoDeliveryTemplate)) {
                somOttoDeliveryTemplateMapper.updateTemplateById(somOttoDeliveryTemplate);
            }
        }
    }

    public boolean createOttoTemplate(SomOttoDeliveryTemplate somOttoDeliveryTemplate) throws ValidateException {
        try {
            OttoTemplateVo build = OttoTemplateVo.builder()
                    .shippingProfileName(somOttoDeliveryTemplate.getShippingProfileName())
                    .workingDays(somOttoDeliveryTemplate.getWorkingDays().split(","))
                    .orderCutoff(somOttoDeliveryTemplate.getOrderCutoff())
                    .deliveryType(somOttoDeliveryTemplate.getDeliveryType())
                    .defaultProcessingTime(somOttoDeliveryTemplate.getDefaultProcessingTime())
                    .transportTime(somOttoDeliveryTemplate.getTransportTime())
                    .build();

            ResultVo ottoTemplate = magicService.createOttoTemplate(token, build);
            if (ottoTemplate.isSuccess()) {
                somOttoDeliveryTemplate.setShippingProfileId(ottoTemplate.getData().toString());
            } else {
                log.error("调用OTTO接口创建运费模版失败{}", ottoTemplate.getMessage());
                throw new ValidateException(ottoTemplate.getMessage());
            }
            return true;
        } catch (Exception e) {
            log.error("调用OTTO接口创建运费模版失败{}", e.toString(), e);
            throw new ValidateException("调用OTTO接口创建运费模版失败" + e.toString());
        }
    }

    public boolean updateOttoTemplate(SomOttoDeliveryTemplate somOttoDeliveryTemplate) throws ValidateException {
        try {
            OttoTemplateVo build = OttoTemplateVo.builder()
                    .shippingProfileId(somOttoDeliveryTemplate.getShippingProfileId())
                    .shippingProfileName(somOttoDeliveryTemplate.getShippingProfileName())
                    .workingDays(somOttoDeliveryTemplate.getWorkingDays().split(","))
                    .orderCutoff(somOttoDeliveryTemplate.getOrderCutoff())
                    .deliveryType(somOttoDeliveryTemplate.getDeliveryType())
                    .defaultProcessingTime(somOttoDeliveryTemplate.getDefaultProcessingTime())
                    .transportTime(somOttoDeliveryTemplate.getTransportTime())
                    .build();

            ResultVo resultVo = magicService.updateOttoTemplate(token, build);
            if (resultVo.isSuccess()) {
                return true;
            }
            throw new ValidateException(resultVo.getMessage());
        } catch (Exception e) {
            log.error("调用OTTO接口更新运费模版失败{}", e.toString(), e);
            throw new ValidateException("调用OTTO接口更新运费模版失败" + e.toString());
        }
    }


    /**
     * delete
     * 删除
     *
     * @param somOttoDeliveryTemplateVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somOttoDeliveryTemplateVo) || StrUtil.isBlank(somOttoDeliveryTemplateVo.getShippingProfileId())) {
            throw new ValidateException("请选择要删除的数据,根据运费模版ID删除");
        }

        if (isTest() || deleteOttoTemplate(somOttoDeliveryTemplateVo)) {
            somOttoDeliveryTemplateMapper.createLambdaQuery().andEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).delete();
            somOttoDeliveryTemplateProductMapper.createLambdaQuery().andEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).delete();
        }
    }

    public boolean deleteOttoTemplate(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        try {
            ResultVo resultVo = magicService.deleteOttoTemplate(token, OttoTemplateVo.builder().shippingProfileId(somOttoDeliveryTemplateVo.getShippingProfileId()).build());
            if (resultVo.isSuccess()) {
                return true;
            }
            throw new ValidateException(resultVo.getMessage());
        } catch (Exception e) {
            log.error("调用OTTO接口删除运费模版失败{}", e.toString(), e);
            throw new ValidateException("调用OTTO接口删除运费模版失败" + e.toString());
        }
    }

    public String export(SomOttoDeliveryTemplatePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomOttoDeliveryTemplateVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "OTTO运费模版管理管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomOttoDeliveryTemplateVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public void update(SomOttoDeliveryTemplateVo ottoVo, TokenUserInfo tokenUser) throws ValidateException {
        if (isTest()) {
            return;
        }
        try {

            ResultVo<SomOttoDeliveryTemplate> resultVo = magicService.getOttoTemplate(token, OttoTemplateVo.builder().shippingProfileId(ottoVo.getShippingProfileId()).build());
            if (resultVo.isSuccess()) {
                SomOttoDeliveryTemplate template = somOttoDeliveryTemplateMapper.createLambdaQuery().andEq("shipping_profile_id", ottoVo.getShippingProfileId()).single();
                SomOttoDeliveryTemplate data = resultVo.getData();
                data.setAid(template.getAid());
                data.setCreateName(tokenUser.getUserName());
                data.setCreateNum(tokenUser.getJobNumber());
                data.setCreateTime(DateTime.now().toJdkDate());
                somOttoDeliveryTemplateMapper.updateTemplateById(data);
            } else {
                log.error("调用OTTO接口获取运费模版失败{}", resultVo.getMessage());
                throw new ValidateException(resultVo.getMessage());
            }
        } catch (Exception e) {
            log.error("调用OTTO接口更新运费模版失败{}", e.toString(), e);
            throw new ValidateException("调用OTTO接口更新运费模版失败" + e.toString());
        }
    }

    public PageVo<SomOttoListing> listing(SomOttoDeliveryTemplatePageSearchVo searchVo) {
        somOttoListingMapper.createLambdaQuery().select("sku", "ean");
        LambdaQuery<SomOttoListing> lambdaQuery = somOttoListingMapper.createLambdaQuery();
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            lambdaQuery.orLike("sku", searchVo.getKeyWord()).orLike("ean", searchVo.getKeyWord());
        }
        PageResult<SomOttoListing> page = lambdaQuery.page(searchVo.getCurrent(), searchVo.getPageSize(), "sku", "ean");
        return ConvertUtils.pageConvert(page, SomOttoListing.class, searchVo);
    }

    public void bindSellerSku(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somOttoDeliveryTemplateVo) || StrUtil.isBlank(somOttoDeliveryTemplateVo.getShippingProfileId())) {
            throw new ValidateException("请选择要绑定的商品,根据运费模版ID绑定");
        }
        //查询是否存在已经绑定的展示码
        bindCheck(somOttoDeliveryTemplateVo);
        List<SomOttoDeliveryTemplateProduct> insertList = new ArrayList<>();
        for (String sellerSku : somOttoDeliveryTemplateVo.getBindSellerSkuList()) {
            SomOttoDeliveryTemplateProduct product = new SomOttoDeliveryTemplateProduct();
            product.setAid(IdUtil.fastSimpleUUID());
            product.setShippingProfileId(somOttoDeliveryTemplateVo.getShippingProfileId());
            product.setSellerSku(sellerSku);
            product.setCreateName(tokenUser.getUserName());
            product.setCreateNum(tokenUser.getJobNumber());
            product.setCreateTime(DateTime.now().toJdkDate());
            insertList.add(product);
        }
        if (!insertList.isEmpty()) {
            somOttoDeliveryTemplateProductMapper.insertBatch(insertList);
        }
    }

    /**
     * 检验待绑定的数据是否合法
     *
     * @param somOttoDeliveryTemplateVo
     */
    private void bindCheck(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        List<SomOttoDeliveryTemplateVo> allData = somOttoDeliveryTemplateMapper.queryAllDataWithName();
        Map<String, String> allMap = allData.stream().collect(Collectors.toMap(x -> x.getSellerSku(), k -> k.getShippingProfileName(), (x1, x2) -> x1));

        StringBuilder sb = new StringBuilder();
        for (String sellerSku : somOttoDeliveryTemplateVo.getBindSellerSkuList()) {
            if (allMap.containsKey(sellerSku)) {
                sb.append(sellerSku).append("已被").append(allMap.get(sellerSku)).append("绑定，请重新选择").append("\n");
            }
        }
        if (sb.length() > 0) {
            throw new ValidateException(sb.toString());
        }
    }

    public List<SomOttoDeliveryTemplateProductVo> queryBindSellerSku(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somOttoDeliveryTemplateVo) || StrUtil.isBlank(somOttoDeliveryTemplateVo.getShippingProfileId())) {
            throw new ValidateException("请选择要查询的商品,根据运费模版ID查询");
        }
        List<SomOttoDeliveryTemplateProduct> select = somOttoDeliveryTemplateProductMapper.createLambdaQuery().andEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).select("seller_sku", "create_time");
        return ConvertUtils.listConvert(select, SomOttoDeliveryTemplateProductVo.class);
    }

    public void delBindSellerSku(SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somOttoDeliveryTemplateVo) || StrUtil.isBlank(somOttoDeliveryTemplateVo.getShippingProfileId()) || StrUtil.isBlank(somOttoDeliveryTemplateVo.getSellerSku())) {
            throw new ValidateException("请选择要删除的商品,根据运费模版ID删除");
        }
        somOttoDeliveryTemplateProductMapper.createLambdaQuery().andEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).andEq("seller_sku", somOttoDeliveryTemplateVo.getSellerSku()).delete();
    }

    public void importBindData(List<SomOttoDeliveryTemplateVo> list, SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, TokenUserInfo tokenUser) throws ValidateException {
        //校验Excel数据是否重复
        boolean repeat = list.stream().map(x -> x.getSellerSku()).distinct().count() == list.size();
        if (!repeat) {
            throw new ValidateException("Excel展示码存在重复数据");
        }
        //校验Excel数据是否在otto listing中存在
        List<SomOttoListing> listingList = somOttoListingMapper.createLambdaQuery().select("sku", "ean");
        Map<String, SomOttoListing> listingMap = listingList.stream().collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (x1, x2) -> x1));
        //提示不存在的sellerSku
        List<String> notExistOttoList = list.stream().filter(x -> !listingMap.containsKey(x.getSellerSku())).map(x -> x.getSellerSku()).collect(Collectors.toList());
        if (!notExistOttoList.isEmpty()) {
            String msg = notExistOttoList.stream().collect(Collectors.joining("\n"));
            throw new ValidateException("以下展示码在Otto listing中不存在：\n" + msg);
        }

        //跳过已经和此运费模版ID绑定的数据
        List<SomOttoDeliveryTemplateProduct> bindList = somOttoDeliveryTemplateProductMapper.createLambdaQuery().andEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).select("seller_sku");
        Map<String, SomOttoDeliveryTemplateProduct> existSellerMap = bindList.stream().collect(Collectors.toMap(x -> x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        list.removeIf(x -> existSellerMap.containsKey(x.getSellerSku()));
        List<SomOttoDeliveryTemplateProduct> allList = somOttoDeliveryTemplateProductMapper.createLambdaQuery().andNotEq("shipping_profile_id", somOttoDeliveryTemplateVo.getShippingProfileId()).select("seller_sku", "shipping_profile_id");
        Map<String, SomOttoDeliveryTemplateProduct> allSellerMap = allList.stream().collect(Collectors.toMap(x -> x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        //校验Excel数据是否存在
        List<SomOttoDeliveryTemplateProduct> notExistList = list.stream().filter(x -> allSellerMap.containsKey(x.getSellerSku())).map(x -> allSellerMap.get(x.getSellerSku())).collect(Collectors.toList());
        if (!notExistList.isEmpty()) {
            String msg = notExistList.stream().map(x -> "模版ID：" + x.getShippingProfileId() + ",展示码：" + x.getSellerSku()).collect(Collectors.joining("\n"));
            throw new ValidateException("以下展示码已绑定到别的模版：\n" + msg);
        }
        //插入数据
        Date now = DateTime.now().toJdkDate();
        List<SomOttoDeliveryTemplateProduct> insertList = list.stream().map(x -> {
            SomOttoDeliveryTemplateProduct product = new SomOttoDeliveryTemplateProduct();
            product.setAid(IdUtil.fastSimpleUUID());
            product.setShippingProfileId(somOttoDeliveryTemplateVo.getShippingProfileId());
            product.setSellerSku(x.getSellerSku());
            product.setCreateName(tokenUser.getUserName());
            product.setCreateNum(tokenUser.getJobNumber());
            product.setCreateTime(now);
            return product;
        }).collect(Collectors.toList());
        if (!insertList.isEmpty()) {
            somOttoDeliveryTemplateProductMapper.insertBatch(insertList);
        }
    }
}
