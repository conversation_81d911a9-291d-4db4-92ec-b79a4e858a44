package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomFreeAddDealService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomFreeAddDealController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somFreeAddDeal", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "LD&7DD主表管理")
public class SomFreeAddDealController extends BasicController {

    @Resource
    SomFreeAddDealService somFreeAddDealService;

    /**
     * addOrEdit
     *
     * @param addOrEditVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增或编辑")
    @PostMapping(value = "/addOrEdit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomFreeAddDealExtVo addOrEditVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.addOrEdit(addOrEditVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "取消子ASIN中")
    @PostMapping(value = "/cancelAsin")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> cancelAsin(@RequestBody @Validated SomFreeAddDealExtVo dealVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.cancelAsin(dealVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomFreeAddDealVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomFreeAddDealExtVo>> queryByPage(@RequestBody SomFreeAddDealPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somFreeAddDealService.queryByPage(searchVo));
    }

    /**
     * query-by-aid
     *
     * @param somFreeAddDealVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomFreeAddDealExtVo> querybyAid(@RequestBody SomFreeAddDealVo somFreeAddDealVo) throws ValidateException {
        return ResultVo.ofSuccess(somFreeAddDealService.querybyAid(somFreeAddDealVo));
    }

    /**
     * delete
     *
     * @param somFreeAddDealVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomFreeAddDealVo somFreeAddDealVo) throws ValidateException {
        somFreeAddDealService.delete(somFreeAddDealVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * submit
     *
     * @param submitVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "提交")
    @PostMapping(value = "/submit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> submit(@RequestBody SomFreeAddDealVo submitVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        somFreeAddDealService.submit(submitVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * cancel
     *
     * @param cancelVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "时间校验")
    @PostMapping(value = "/validateTime")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> validateTime(@RequestBody SomFreeAddDealVo cancelVo) throws ValidateException {
        Boolean flag = somFreeAddDealService.validateTime(cancelVo);
        if(flag){
            return ResultVo.ofSuccess();
        }else {
            return ResultVo.ofFail("error");
        }
    }
    /**
     * cancel
     *
     * @param cancelVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> cancel(@RequestBody SomFreeAddDealVo cancelVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.cancel(cancelVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackSubmitResult
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈提报结果")
    @PostMapping(value = "/feedbackSubmitResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackSubmitResult(@RequestBody SomFreeAddDealVo feedbackVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.feedbackSubmitResult(feedbackVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackModifyResult
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈修改结果")
    @PostMapping(value = "/feedbackModifyResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackModifyResult(@RequestBody SomFreeAddDealVo feedbackVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.feedbackModifyResult(feedbackVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackCancelResult
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈取消结果")
    @PostMapping(value = "/feedbackCancelResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackCancelResult(@RequestBody SomFreeAddDealVo feedbackVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.feedbackCancelResult(feedbackVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * modifyPlanTime
     *
     * @param modifyVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改活动时间")
    @PostMapping(value = "/modifyPlanTime")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> modifyPlanTime(@RequestBody SomFreeAddDealVo modifyVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.modifyPlanTime(modifyVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * clone
     * 克隆
     * @param cloneVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "克隆")
    @PostMapping(value = "/clone")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> clone(@RequestBody SomFreeAddDealExtVo cloneVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeAddDealService.cloneById(cloneVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * summited
     * 标记为已提报
     * @param summitedVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记为已提报")
    @PostMapping(value = "/submited")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> summited(@RequestBody SomFreeAddDealExtVo summitedVo) throws ValidateException {
        somFreeAddDealService.summited(summitedVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> exportExcel(@RequestBody SomFreeAddDealPageSearchVo exportVo) {
        String data = somFreeAddDealService.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * downloadBatchCancelTemplate
     * 下载批量取消模板
     * <AUTHOR>
     */
    @Operation(summary = "下载批量取消模板")
    @GetMapping(value = "/downloadBatchCancelTemplate")
    public String downloadBatchCancelTemplate() { return "forward:/static/excel/LdBatchCancelTemplate.xlsx"; }

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    @Operation(summary = "批量取消")
    @PostMapping(value = "/batchCancel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchCancel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "InternalDescription", "提报活动开始时间", "提报活动结束时间"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomFreeAddDealImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomFreeAddDealImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result != null && result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        String str = somFreeAddDealService.batchCancel(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * downloadBatchFeedbackCancelTemplate
     * 下载批量反馈取消结果模板
     * <AUTHOR>
     */
    @Operation(summary = "下载批量反馈取消结果模板")
    @GetMapping(value = "/downloadBatchFeedbackCancelTemplate")
    public String downloadBatchFeedbackCancelTemplate() { return "forward:/static/excel/LdBatchFeedbackCancelTemplate.xlsx"; }

    /**
     * batchFeedbackCancelResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    @Operation(summary = "批量反馈取消结果")
    @PostMapping(value = "/batchFeedbackCancelResult", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchFeedbackCancelResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "InternalDescription", "状态", "备注", "提报活动开始时间", "提报活动结束时间"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomFreeAddDealImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomFreeAddDealImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result != null && result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        String str = somFreeAddDealService.batchFeedbackCancelResult(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    @Operation(summary = "下载批量编辑模板")
    @GetMapping(value = "/batchEditTemplate/download")
    public String downloadBatchEditTemplate() {
        return "forward:/static/excel/LDBatchEditTemplate.xlsx";
    }

    @Operation(summary = "导入批量编辑")
    @PostMapping(value = "/batchEdit/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importBatchEdit(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "Internal Description", "展示码", "秒杀价", "秒杀数量"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomFreeAndDealBatchEditImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomFreeAndDealBatchEditImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somFreeAddDealService.importBatchEdit(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载批量反馈修改结果模板")
    @GetMapping(value = "/batchFeedbackEditResultTemplate/download")
    public String downloadBatchFeedbackEditResultTemplate() {
        return "forward:/static/excel/LDBatchFeedbackEditResultTemplate.xlsx";
    }

    @Operation(summary = "导入批量反馈修改结果")
    @PostMapping(value = "/batchFeedbackEditResult/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importBatchFeedbackEditResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "Internal Description", "修改状态", "失败原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomFreeAddDealBatchFeedbackEditResultImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomFreeAddDealBatchFeedbackEditResultImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somFreeAddDealService.importBatchFeedbackEditResult(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }
}
