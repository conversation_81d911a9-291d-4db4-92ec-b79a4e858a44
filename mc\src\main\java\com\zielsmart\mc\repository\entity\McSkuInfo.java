package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 产品基础视图
* gen by 代码生成器 2022-02-09
*/

@Table(name="mc.mc_sku_info")
public class McSkuInfo implements java.io.Serializable {

	@Column("basics_product_main_code")
	private String basicProductMainCode ;

	@Column("product_group_name")
	private String productGroupName ;

	@Column("product_group_code")
	private String productGroupCode ;

	@Column("create_time")
	private String createTime ;

	@Column("modify_time")
	private Date modifyTime ;

	@Column("product_name_en")
	private String productNameEn;


	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * SKU
	 */
	@Column("sku_code")
	private String skuCode ;
	/**
	 * 产品类型   默认“10”,“20","30","40","50"；即“展示码状态”为”新品“，“试销”，“正常”,"预淘汰","淘汰"
	 */
	@Column("product_type_code")
	private String productTypeCode ;
	/**
	 * 三级分类编码
	 */
	@Column("category_inline_code")
	private String categoryInlineCode ;
	/**
	 * 三级分类名称
	 */
	@Column("category_name_cn")
	private String categoryNameCn ;

	/**
	 * 四级分类编码
	 */
	@Column("fourth_category_inline_code")
	private String fourthCategoryInlineCode;
	/**
	 * 四级分类名称
	 */
	@Column("fourth_category_inline_name")
	private String fourthCategoryInlineName;

	/**
	 * 产品尺寸
	 */
	@Column("product_size")
	private String productSize ;
	/**
	 * 产品名称
	 */
	@Column("product_name_cn")
	private String productNameCn ;
	/**
	 * SKU等级
	 */
	@Column("sku_grade_code")
	private String skuGradeCode ;
	/**
	 * 品牌
	 */
	@Column("product_brand_code")
	private String productBrandCode ;
	/**
	 * 品牌状态
	 */
	@Column("product_status_code")
	private String productStatusCode ;
	/**
	 * 产品净重
	 */
	@Column("parts_net_weight")
	private BigDecimal partsNetWeight ;
	/**
	 * 涉电类型
	 */
	@Column("electricity_code")
	private String electricityCode ;
	/**
	 * 内箱长cm
	 */
	@Column("packageing_length1")
	private BigDecimal packageingLength1 ;
	/**
	 * 内箱宽cm
	 */
	@Column("packageing_length2")
	private BigDecimal packageingLength2 ;
	/**
	 * 内箱高cm
	 */
	@Column("packageing_length3")
	private BigDecimal packageingLength3 ;
	/**
	 * 内箱净重g
	 */
	@Column("net_weight")
	private BigDecimal netWeight ;
	/**
	 * 内箱毛重g
	 */
	@Column("rough_weight")
	private Long roughWeight ;
	/**
	 * 箱容
	 */
	@Column("box_volume")
	private Integer boxVolume ;
	/**
	 * 外箱长cm
	 */
	@Column("box_packageing_length1")
	private BigDecimal boxPackageingLength1 ;
	/**
	 * 外箱宽cm
	 */
	@Column("box_packageing_length2")
	private BigDecimal boxPackageingLength2 ;
	/**
	 * 外箱高cm
	 */
	@Column("box_packageing_length3")
	private BigDecimal boxPackageingLength3 ;
	/**
	 * 外箱净重g
	 */
	@Column("box_net_weight")
	private BigDecimal boxNetWeight ;
	/**
	 * 外箱毛重g
	 */
	@Column("box_rough_weight")
	private BigDecimal boxRoughWeight ;


	public String getProductNameEn() {
		return productNameEn;
	}

	public void setProductNameEn(String productNameEn) {
		this.productNameEn = productNameEn;
	}

	public String getBasicProductMainCode() {
		return basicProductMainCode;
	}

	public void setBasicProductMainCode(String basicProductMainCode) {
		this.basicProductMainCode = basicProductMainCode;
	}

	public String getProductGroupName() {
		return productGroupName;
	}

	public void setProductGroupName(String productGroupName) {
		this.productGroupName = productGroupName;
	}

	public String getProductGroupCode() {
		return productGroupCode;
	}

	public void setProductGroupCode(String productGroupCode) {
		this.productGroupCode = productGroupCode;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getFourthCategoryInlineCode() {
		return fourthCategoryInlineCode;
	}

	public void setFourthCategoryInlineCode(String fourthCategoryInlineCode) {
		this.fourthCategoryInlineCode = fourthCategoryInlineCode;
	}

	public String getFourthCategoryInlineName() {
		return fourthCategoryInlineName;
	}

	public void setFourthCategoryInlineName(String fourthCategoryInlineName) {
		this.fourthCategoryInlineName = fourthCategoryInlineName;
	}

	public McSkuInfo() {
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * SKU
	 *@return
	 */
	public String getSkuCode(){
		return  skuCode;
	}
	/**
	 * SKU
	 *@param  skuCode
	 */
	public void setSkuCode(String skuCode ){
		this.skuCode = skuCode;
	}
	/**
	 * 产品类型
	 *@return
	 */
	public String getProductTypeCode(){
		return  productTypeCode;
	}
	/**
	 * 产品类型
	 *@param  productTypeCode
	 */
	public void setProductTypeCode(String productTypeCode ){
		this.productTypeCode = productTypeCode;
	}
	/**
	 * 三级分类编码
	 *@return
	 */
	public String getCategoryInlineCode(){
		return  categoryInlineCode;
	}
	/**
	 * 三级分类编码
	 *@param  categoryInlineCode
	 */
	public void setCategoryInlineCode(String categoryInlineCode ){
		this.categoryInlineCode = categoryInlineCode;
	}
	/**
	 * 三级分类名称
	 *@return
	 */
	public String getCategoryNameCn(){
		return  categoryNameCn;
	}
	/**
	 * 三级分类名称
	 *@param  categoryNameCn
	 */
	public void setCategoryNameCn(String categoryNameCn ){
		this.categoryNameCn = categoryNameCn;
	}
	/**
	 * 产品尺寸
	 *@return
	 */
	public String getProductSize(){
		return  productSize;
	}
	/**
	 * 产品尺寸
	 *@param  productSize
	 */
	public void setProductSize(String productSize ){
		this.productSize = productSize;
	}
	/**
	 * 产品名称
	 *@return
	 */
	public String getProductNameCn(){
		return  productNameCn;
	}
	/**
	 * 产品名称
	 *@param  productNameCn
	 */
	public void setProductNameCn(String productNameCn ){
		this.productNameCn = productNameCn;
	}
	/**
	 * SKU等级
	 *@return
	 */
	public String getSkuGradeCode(){
		return  skuGradeCode;
	}
	/**
	 * SKU等级
	 *@param  skuGradeCode
	 */
	public void setSkuGradeCode(String skuGradeCode ){
		this.skuGradeCode = skuGradeCode;
	}
	/**
	 * 品牌
	 *@return
	 */
	public String getProductBrandCode(){
		return  productBrandCode;
	}
	/**
	 * 品牌
	 *@param  productBrandCode
	 */
	public void setProductBrandCode(String productBrandCode ){
		this.productBrandCode = productBrandCode;
	}
	/**
	 * 品牌状态
	 *@return
	 */
	public String getProductStatusCode(){
		return  productStatusCode;
	}
	/**
	 * 品牌状态
	 *@param  productStatusCode
	 */
	public void setProductStatusCode(String productStatusCode ){
		this.productStatusCode = productStatusCode;
	}
	/**
	 * 产品净重
	 *@return
	 */
	public BigDecimal getPartsNetWeight(){
		return  partsNetWeight;
	}
	/**
	 * 产品净重
	 *@param  partsNetWeight
	 */
	public void setPartsNetWeight(BigDecimal partsNetWeight ){
		this.partsNetWeight = partsNetWeight;
	}
	/**
	 * 涉电类型
	 *@return
	 */
	public String getElectricityCode(){
		return  electricityCode;
	}
	/**
	 * 涉电类型
	 *@param  electricityCode
	 */
	public void setElectricityCode(String electricityCode ){
		this.electricityCode = electricityCode;
	}
	/**
	 * 内箱长cm
	 *@return
	 */
	public BigDecimal getPackageingLength1(){
		return  packageingLength1;
	}
	/**
	 * 内箱长cm
	 *@param  packageingLength1
	 */
	public void setPackageingLength1(BigDecimal packageingLength1 ){
		this.packageingLength1 = packageingLength1;
	}
	/**
	 * 内箱宽cm
	 *@return
	 */
	public BigDecimal getPackageingLength2(){
		return  packageingLength2;
	}
	/**
	 * 内箱宽cm
	 *@param  packageingLength2
	 */
	public void setPackageingLength2(BigDecimal packageingLength2 ){
		this.packageingLength2 = packageingLength2;
	}
	/**
	 * 内箱高cm
	 *@return
	 */
	public BigDecimal getPackageingLength3(){
		return  packageingLength3;
	}
	/**
	 * 内箱高cm
	 *@param  packageingLength3
	 */
	public void setPackageingLength3(BigDecimal packageingLength3 ){
		this.packageingLength3 = packageingLength3;
	}
	/**
	 * 内箱净重g
	 *@return
	 */
	public BigDecimal getNetWeight(){
		return  netWeight;
	}
	/**
	 * 内箱净重g
	 *@param  netWeight
	 */
	public void setNetWeight(BigDecimal netWeight ){
		this.netWeight = netWeight;
	}
	/**
	 * 内箱毛重g
	 *@return
	 */
	public Long getRoughWeight(){
		return  roughWeight;
	}
	/**
	 * 内箱毛重g
	 *@param  roughWeight
	 */
	public void setRoughWeight(Long roughWeight ){
		this.roughWeight = roughWeight;
	}
	/**
	 * 箱容
	 *@return
	 */
	public Integer getBoxVolume(){
		return  boxVolume;
	}
	/**
	 * 箱容
	 *@param  boxVolume
	 */
	public void setBoxVolume(Integer boxVolume ){
		this.boxVolume = boxVolume;
	}
	/**
	 * 外箱长cm
	 *@return
	 */
	public BigDecimal getBoxPackageingLength1(){
		return  boxPackageingLength1;
	}
	/**
	 * 外箱长cm
	 *@param  boxPackageingLength1
	 */
	public void setBoxPackageingLength1(BigDecimal boxPackageingLength1 ){
		this.boxPackageingLength1 = boxPackageingLength1;
	}
	/**
	 * 外箱宽cm
	 *@return
	 */
	public BigDecimal getBoxPackageingLength2(){
		return  boxPackageingLength2;
	}
	/**
	 * 外箱宽cm
	 *@param  boxPackageingLength2
	 */
	public void setBoxPackageingLength2(BigDecimal boxPackageingLength2 ){
		this.boxPackageingLength2 = boxPackageingLength2;
	}
	/**
	 * 外箱高cm
	 *@return
	 */
	public BigDecimal getBoxPackageingLength3(){
		return  boxPackageingLength3;
	}
	/**
	 * 外箱高cm
	 *@param  boxPackageingLength3
	 */
	public void setBoxPackageingLength3(BigDecimal boxPackageingLength3 ){
		this.boxPackageingLength3 = boxPackageingLength3;
	}
	/**
	 * 外箱净重g
	 *@return
	 */
	public BigDecimal getBoxNetWeight(){
		return  boxNetWeight;
	}
	/**
	 * 外箱净重g
	 *@param  boxNetWeight
	 */
	public void setBoxNetWeight(BigDecimal boxNetWeight ){
		this.boxNetWeight = boxNetWeight;
	}
	/**
	 * 外箱毛重g
	 *@return
	 */
	public BigDecimal getBoxRoughWeight(){
		return  boxRoughWeight;
	}
	/**
	 * 外箱毛重g
	 *@param  boxRoughWeight
	 */
	public void setBoxRoughWeight(BigDecimal boxRoughWeight ){
		this.boxRoughWeight = boxRoughWeight;
	}

}
