package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* 用户表
* gen by 代码生成器 2021-03-04
*/

@Table(name="mc.mc_user")
public class McUser implements java.io.Serializable {
	/**
	 * 用户主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 工号
	 */
	@Column("job_number")
	private String jobNumber ;
	/**
	 * 姓名
	 */
	@Column("user_name")
	private String userName ;
	/**
	 * 邮箱
	 */
	@Column("email")
	private String email ;
	/**
	 * 姓名拼音或者英文
	 */
	@Column("user_name_pinyin")
	private String userNamePinyin;
	/**
	 * 手机号
	 */
	@Column("phone")
	private String phone ;
	/**
	 * 应用加密后密码
	 */
	@Column("secret_password")
	private String secretPassword ;
	/**
	 * 授权密码
	 */
	@Column("author_password")
	private String authorPassword ;
	/**
	 * 同步日期
	 */
	@Column("last_sync_time")
	private Date lastSyncTime ;
	/**
	 * 最后登录日期
	 */
	@Column("last_login_time")
	private Date lastLoginTime ;
	/**
	 * 授权因子
	 */
	@Column("app_key")
	private String appKey ;
	/**
	 * 用户状态（0未激活 1 正常）
	 */
	@Column("user_status")
	private Integer userStatus ;

	/**
	 * avatar 头像
	 */
	@Column("avatar")
	private String avatar;


	/**
	 * 用户的钉钉Id dingId
	 */
	@Column("ding_id")
	private String dingId;
	/**
	 * 默认当前部门 defaultDeptId
	 */
	@Column("default_dept_id")
	private String defaultDeptId;

	/**
	 * 默认当前领导 defaultLeaderId
	 */
	@Column("default_leader_id")
	private String defaultLeaderId;

	/**
	 * 员工性别sex
	 */
	@Column("sex")
	private String sex;

	/**
	 * 用户类型 -1 访客  0 非正式员工   1 正式员工 userType
	 */
	@Column("user_type")
	private Integer userType;

	@Column("feishu_user_id")
	private String feiShuUserId;

	public McUser() {
	}

	/**
	* 用户主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 用户主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 工号
	*@return
	*/
	public String getJobNumber(){
		return  jobNumber;
	}
	/**
	* 工号
	*@param  jobNumber
	*/
	public void setJobNumber(String jobNumber ){
		this.jobNumber = jobNumber;
	}
	/**
	* 姓名
	*@return
	*/
	public String getUserName(){
		return  userName;
	}
	/**
	* 姓名
	*@param  userName
	*/
	public void setUserName(String userName ){
		this.userName = userName;
	}
	/**
	* 邮箱
	*@return
	*/
	public String getEmail(){
		return  email;
	}
	/**
	* 邮箱
	*@param  email
	*/
	public void setEmail(String email ){
		this.email = email;
	}
	/**
	* 姓名拼音或者英文
	*@return
	*/
	public String getUserNamePinyin(){
		return  userNamePinyin;
	}
	/**
	* 姓名拼音或者英文
	*@param  userNamePinyin
	*/
	public void setUserNamePinyin(String userNamePinyin ){
		this.userNamePinyin = userNamePinyin;
	}
	/**
	* 手机号
	*@return
	*/
	public String getPhone(){
		return  phone;
	}
	/**
	* 手机号
	*@param  phone
	*/
	public void setPhone(String phone ){
		this.phone = phone;
	}
	/**
	* 应用加密后密码
	*@return
	*/
	public String getSecretPassword(){
		return  secretPassword;
	}
	/**
	* 应用加密后密码
	*@param  secretPassword
	*/
	public void setSecretPassword(String secretPassword ){
		this.secretPassword = secretPassword;
	}
	/**
	* 授权密码
	*@return
	*/
	public String getAuthorPassword(){
		return  authorPassword;
	}
	/**
	* 授权密码
	*@param  authorPassword
	*/
	public void setAuthorPassword(String authorPassword ){
		this.authorPassword = authorPassword;
	}
	/**
	* 同步日期
	*@return
	*/
	public Date getLastSyncTime(){
		return  lastSyncTime;
	}
	/**
	* 同步日期
	*@param  lastSyncTime
	*/
	public void setLastSyncTime(Date lastSyncTime ){
		this.lastSyncTime = lastSyncTime;
	}
	/**
	* 最后登录日期
	*@return
	*/
	public Date getLastLoginTime(){
		return  lastLoginTime;
	}
	/**
	* 最后登录日期
	*@param  lastLoginTime
	*/
	public void setLastLoginTime(Date lastLoginTime ){
		this.lastLoginTime = lastLoginTime;
	}
	/**
	* 授权因子
	*@return
	*/
	public String getAppKey(){
		return  appKey;
	}
	/**
	* 授权因子
	*@param  appKey
	*/
	public void setAppKey(String appKey ){
		this.appKey = appKey;
	}
	/**
	* 用户状态（0未激活 1 正常）
	*@return
	*/
	public Integer getUserStatus(){
		return  userStatus;
	}
	/**
	* 用户状态（0未激活 1 正常）
	*@param  userStatus
	*/
	public void setUserStatus(Integer userStatus ){
		this.userStatus = userStatus;
	}

	public String getAvatar() {
		return avatar;
	}

	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}

	public String getDingId() {
		return dingId;
	}

	public void setDingId(String dingId) {
		this.dingId = dingId;
	}

	public String getDefaultDeptId() {
		return defaultDeptId;
	}

	public void setDefaultDeptId(String defaultDeptId) {
		this.defaultDeptId = defaultDeptId;
	}

	public String getDefaultLeaderId() {
		return defaultLeaderId;
	}

	public void setDefaultLeaderId(String defaultLeaderId) {
		this.defaultLeaderId = defaultLeaderId;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getFeiShuUserId() {
		return feiShuUserId;
	}

	public void setFeiShuUserId(String feiShuUserId) {
		this.feiShuUserId = feiShuUserId;
	}
}
