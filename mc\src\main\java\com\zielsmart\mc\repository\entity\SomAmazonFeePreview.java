package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-03-16
*/

@Table(name="mc.som_amazon_fee_preview")
public class SomAmazonFeePreview implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * FNSKU
	 */
	@Column("fnsku")
	private String fnsku ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 长边长度 
	 */
	@Column("longest_side")
	private BigDecimal longestSide ;
	/**
	 * 中边长度 
	 */
	@Column("median_side")
	private BigDecimal medianSide ;
	/**
	 * 短边长度 
	 */
	@Column("shortest_side")
	private BigDecimal shortestSide ;
	/**
	 * 商品最长边的长度（含包装）加上围长
	 */
	@Column("length_and_girth")
	private BigDecimal lengthAndGirth ;
	/**
	 * 长度单位
	 */
	@Column("unit_of_dimension")
	private String unitOfDimension ;
	/**
	 * 单个带包装的重量
	 */
	@Column("item_package_weight")
	private BigDecimal itemPackageWeight ;
	/**
	 * 重量单位
	 */
	@Column("unit_of_weight")
	private String unitOfWeight ;
	/**
	 * 产品尺寸标准
	 */
	@Column("product_size_weight_band")
	private String productSizeWeightBand ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * FBA发货费用
	 */
	@Column("expected_domestic_fufillment_fee_per_unit")
	private BigDecimal expectedDomesticFufillmentFeePerUnit ;
	/**
	 * 预估发货费用
	 */
	@Column("standard_freught_amount")
	private BigDecimal standardFreughtAmount ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAmazonFeePreview() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* FNSKU
	*@return
	*/
	public String getFnsku(){
		return  fnsku;
	}
	/**
	* FNSKU
	*@param  fnsku
	*/
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 长边长度 
	*@return
	*/
	public BigDecimal getLongestSide(){
		return  longestSide;
	}
	/**
	* 长边长度 
	*@param  longestSide
	*/
	public void setLongestSide(BigDecimal longestSide ){
		this.longestSide = longestSide;
	}
	/**
	* 中边长度 
	*@return
	*/
	public BigDecimal getMedianSide(){
		return  medianSide;
	}
	/**
	* 中边长度 
	*@param  medianSide
	*/
	public void setMedianSide(BigDecimal medianSide ){
		this.medianSide = medianSide;
	}
	/**
	* 短边长度 
	*@return
	*/
	public BigDecimal getShortestSide(){
		return  shortestSide;
	}
	/**
	* 短边长度 
	*@param  shortestSide
	*/
	public void setShortestSide(BigDecimal shortestSide ){
		this.shortestSide = shortestSide;
	}
	/**
	* 商品最长边的长度（含包装）加上围长
	*@return
	*/
	public BigDecimal getLengthAndGirth(){
		return  lengthAndGirth;
	}
	/**
	* 商品最长边的长度（含包装）加上围长
	*@param  lengthAndGirth
	*/
	public void setLengthAndGirth(BigDecimal lengthAndGirth ){
		this.lengthAndGirth = lengthAndGirth;
	}
	/**
	* 长度单位
	*@return
	*/
	public String getUnitOfDimension(){
		return  unitOfDimension;
	}
	/**
	* 长度单位
	*@param  unitOfDimension
	*/
	public void setUnitOfDimension(String unitOfDimension ){
		this.unitOfDimension = unitOfDimension;
	}
	/**
	* 单个带包装的重量
	*@return
	*/
	public BigDecimal getItemPackageWeight(){
		return  itemPackageWeight;
	}
	/**
	* 单个带包装的重量
	*@param  itemPackageWeight
	*/
	public void setItemPackageWeight(BigDecimal itemPackageWeight ){
		this.itemPackageWeight = itemPackageWeight;
	}
	/**
	* 重量单位
	*@return
	*/
	public String getUnitOfWeight(){
		return  unitOfWeight;
	}
	/**
	* 重量单位
	*@param  unitOfWeight
	*/
	public void setUnitOfWeight(String unitOfWeight ){
		this.unitOfWeight = unitOfWeight;
	}
	/**
	* 产品尺寸标准
	*@return
	*/
	public String getProductSizeWeightBand(){
		return  productSizeWeightBand;
	}
	/**
	* 产品尺寸标准
	*@param  productSizeWeightBand
	*/
	public void setProductSizeWeightBand(String productSizeWeightBand ){
		this.productSizeWeightBand = productSizeWeightBand;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* FBA发货费用
	*@return
	*/
	public BigDecimal getExpectedDomesticFufillmentFeePerUnit(){
		return  expectedDomesticFufillmentFeePerUnit;
	}
	/**
	* FBA发货费用
	*@param  expectedDomesticFufillmentFeePerUnit
	*/
	public void setExpectedDomesticFufillmentFeePerUnit(BigDecimal expectedDomesticFufillmentFeePerUnit ){
		this.expectedDomesticFufillmentFeePerUnit = expectedDomesticFufillmentFeePerUnit;
	}
	/**
	* 预估发货费用
	*@return
	*/
	public BigDecimal getStandardFreughtAmount(){
		return  standardFreughtAmount;
	}
	/**
	* 预估发货费用
	*@param  standardFreughtAmount
	*/
	public void setStandardFreughtAmount(BigDecimal standardFreughtAmount ){
		this.standardFreughtAmount = standardFreughtAmount;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
