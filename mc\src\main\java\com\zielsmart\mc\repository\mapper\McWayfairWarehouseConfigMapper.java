package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import com.zielsmart.mc.vo.McWayfairWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWayfairWarehouseConfigVo;
import com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2021-11-24
*/

@SqlResource("mcWayfairWarehouseConfig")
public interface McWayfairWarehouseConfigMapper extends BaseMapper<McWayfairWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McWayfairWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McWayfairWarehouseConfigVo> queryByPage(@Param("searchVo")McWayfairWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * findWarehouseAndSl
     * 查询仓库库区信息
     * @param platformWarehouseCodes
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWareHouseAndSlVo>}
     * <AUTHOR>
     * @history
     */
    List<McWareHouseAndSlVo> findWarehouseAndSl(@Param("platformWarehouseCodes")List<String> platformWarehouseCodes);

    /**
     * queryConfigurableWarehouses
     * 查询可配置仓库
     * @param marketCode
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McWarehouse>}
     * <AUTHOR>
     * @history
     */
    List<McWarehouse> queryConfigurableWarehouses(@Param("marketCode") String marketCode);

    /**
     * queryAllConfigList
     * 查询已配置的仓库信息
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWareHouseAndSlVo>}
     * <AUTHOR>
     * @history
     */
    List<McWareHouseAndSlVo> queryAllConfigList();
}
