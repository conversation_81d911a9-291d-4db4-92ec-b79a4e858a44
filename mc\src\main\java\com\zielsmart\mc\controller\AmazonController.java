package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.AmazonService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version V1.0
 * @title: AmazonController
 * @package: com.zielsmart.mc.controller
 * @description:
 * @author: lvjishuai
 * @date: 2021-04-25 9:22
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/amazon", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon平台")
public class AmazonController extends BasicController {
    @Resource
    private AmazonService amazonService;


    /**
     * queryByPageListing
     * 分页获取List报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.McListingInfoAmazonVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页获取List报表")
    @PostMapping(value = "/query-by-page-listing")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McListingInfoAmazonVo>> queryByPageListing(@RequestBody @Validated McListingInfoAmazonPageSearchVo searchVo) {
        return ResultVo.ofSuccess(amazonService.queryByPageListing(searchVo));
    }

    /**
     * updatePrice
     * 更新Listing价格
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新Listing价格")
    @PostMapping(value = "/update-price")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> updatePrice(@RequestBody AwsUpdatePriceOrInventoryVo awsUpdatePriceOrInventoryVo) {
        amazonService.updatePrice(awsUpdatePriceOrInventoryVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPlatformSiteSellerSku
     * 根据平台站点展示码查询
     *
     * @param searchExVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点展示码查询")
    @PostMapping(value = "/queryByPlatformSiteSellerSku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McListingInfoAmazonExVo>> queryByPlatformSiteSellerSku(@RequestBody McListingInfoAmazonSearchExVo searchExVo) throws ValidateException {
        return ResultVo.ofSuccess(amazonService.queryByPlatformSiteSellerSku(searchExVo));
    }

    /**
     * queryPromotionDiscount
     * 根据站点展示码/Asin活动开始时间截止时间获取Promotion折扣
     *
     * @param searchExVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.math.BigDecimal>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据站点展示码/Asin活动开始时间截止时间获取Promotion折扣")
    @PostMapping(value = "/queryPromotionDiscount")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<BigDecimal> queryPromotionDiscount(@RequestBody McListingInfoAmazonSearchExVo searchExVo) throws ValidateException {
        return ResultVo.ofSuccess(amazonService.queryPromotionDiscount(searchExVo));
    }

    /**
     * queryByPlatformSiteSellerSku
     * 根据平台站点展示码查询(DOTO)
     *
     * @param searchExVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点展示码查询(DOTO)")
    @PostMapping(value = "/queryForDOTO")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McListingInfoAmazonVo>> queryForDOTO(@RequestBody McListingInfoAmazonSearchExVo searchExVo) throws ValidateException {
        return ResultVo.ofSuccess(amazonService.queryForDOTO(searchExVo));
    }

    /**
     * queryByPlatformSiteSellerSku
     * 根据站点展示码或asin查询
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据站点展示码或asin分页查询")
    @PostMapping(value = "/queryForLD")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McListingInfoAmazonExVo>> queryForLD(@RequestBody McListingInfoAmazonPageSearchVo pageSearchVo) throws ValidateException {
        return ResultVo.ofSuccess(amazonService.queryForLD(pageSearchVo));
    }
}
