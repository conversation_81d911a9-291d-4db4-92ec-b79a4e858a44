package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 目标价审核配置表
* gen by 代码生成器 2021-11-03
*/

@Table(name="mc.mc_target_price_personnel_mapping")
public class McTargetPricePersonnelMapping implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 业务组编码
	 */
	@Column("business_group_code")
	private String businessGroupCode ;
	/**
	 * 业务组名称
	 */
	@Column("business_group_name")
	private String businessGroupName ;
	/**
	 * 运营负责人工号
	 */
	@Column("sale_person_code")
	private String salePersonCode ;
	/**
	 * 运营负责人姓名
	 */
	@Column("sale_person_name")
	private String salePersonName ;
	/**
	 * 审核负责人工号
	 */
	@Column("audit_director_code")
	private String auditDirectorCode ;
	/**
	 * 审核负责人姓名
	 */
	@Column("audit_director_name")
	private String auditDirectorName ;

	public McTargetPricePersonnelMapping() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 业务组编码
	*@return
	*/
	public String getBusinessGroupCode(){
		return  businessGroupCode;
	}
	/**
	* 业务组编码
	*@param  businessGroupCode
	*/
	public void setBusinessGroupCode(String businessGroupCode ){
		this.businessGroupCode = businessGroupCode;
	}
	/**
	* 业务组名称
	*@return
	*/
	public String getBusinessGroupName(){
		return  businessGroupName;
	}
	/**
	* 业务组名称
	*@param  businessGroupName
	*/
	public void setBusinessGroupName(String businessGroupName ){
		this.businessGroupName = businessGroupName;
	}
	/**
	* 运营负责人工号
	*@return
	*/
	public String getSalePersonCode(){
		return  salePersonCode;
	}
	/**
	* 运营负责人工号
	*@param  salePersonCode
	*/
	public void setSalePersonCode(String salePersonCode ){
		this.salePersonCode = salePersonCode;
	}
	/**
	* 运营负责人姓名
	*@return
	*/
	public String getSalePersonName(){
		return  salePersonName;
	}
	/**
	* 运营负责人姓名
	*@param  salePersonName
	*/
	public void setSalePersonName(String salePersonName ){
		this.salePersonName = salePersonName;
	}
	/**
	* 审核负责人工号
	*@return
	*/
	public String getAuditDirectorCode(){
		return  auditDirectorCode;
	}
	/**
	* 审核负责人工号
	*@param  auditDirectorCode
	*/
	public void setAuditDirectorCode(String auditDirectorCode ){
		this.auditDirectorCode = auditDirectorCode;
	}
	/**
	* 审核负责人姓名
	*@return
	*/
	public String getAuditDirectorName(){
		return  auditDirectorName;
	}
	/**
	* 审核负责人姓名
	*@param  auditDirectorName
	*/
	public void setAuditDirectorName(String auditDirectorName ){
		this.auditDirectorName = auditDirectorName;
	}

}
