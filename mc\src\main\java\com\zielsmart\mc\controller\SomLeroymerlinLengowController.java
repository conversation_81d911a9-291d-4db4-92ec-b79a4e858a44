package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.zielsmart.mc.service.SomLeroymerlinLengowService;
import com.zielsmart.mc.vo.SomLeroymerlinLengowPageSearchVo;
import com.zielsmart.mc.vo.SomLeroymerlinLengowVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomLeroymerlinLengowController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somLeroymerlinLengow", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "乐售LeroyMerlin.fr站点listing管理管理")
public class SomLeroymerlinLengowController extends BasicController{

    @Resource
    SomLeroymerlinLengowService somLeroymerlinLengowService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomLeroymerlinLengowVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<HashMap> queryByPage(@RequestBody SomLeroymerlinLengowPageSearchVo searchVo) throws Exception {
        return ResultVo.ofSuccess(somLeroymerlinLengowService.queryByPage(searchVo));
    }

    /**
     * delete
     *
     * @param somLeroymerlinLengowVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomLeroymerlinLengowVo somLeroymerlinLengowVo) throws ValidateException {
        somLeroymerlinLengowService.delete(somLeroymerlinLengowVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomLeroymerlinLengowPageSearchVo searchVo) throws Exception {
        String data = somLeroymerlinLengowService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "下载导入模版")
    @PostMapping(value = "/download", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> download() throws Exception {
        String data = somLeroymerlinLengowService.download();
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("下载模版数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "上传导入模版到S3国外指定位置")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> uploadS3(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somLeroymerlinLengowService.uploadS3(file, tokenUser);
        return ResultVo.ofSuccess();
    }


    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        ExcelImportResult<Map> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), Map.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        List<Map> list = result.getList();
        if (list.isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        list.stream().forEach(m -> m.remove("excelRowNum"));
        somLeroymerlinLengowService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }
}
