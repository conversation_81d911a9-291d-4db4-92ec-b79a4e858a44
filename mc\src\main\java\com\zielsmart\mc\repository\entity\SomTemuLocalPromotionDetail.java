package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
/*
* Temu本本营销活动明细
* gen by 代码生成器 2025-03-12
*/

@Table(name="mc.som_temu_local_promotion_detail")
public class SomTemuLocalPromotionDetail implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 营销活动ID
	 */
	@Column("activity_id")
	private String activityId ;
	/**
	 * 产品ID
	 */
	@Column("goods_id")
	private String goodsId ;
	/**
	 * 活动数量
	 */
	@Column("activity_quantity")
	private Integer activityQuantity ;
	/**
	 * 活动剩余库存
	 */
	@Column("remaining_activity_quantity")
	private Integer remainingActivityQuantity ;
	/**
	 * SKU ID
	 */
	@Column("sku_id")
	private String skuId ;
	/**
	 * 活动供货价
	 */
	@Column("activity_supplier_price")
	private BigDecimal activitySupplierPrice ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomTemuLocalPromotionDetail() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 营销活动ID
	*@return
	*/
	public String getActivityId(){
		return  activityId;
	}
	/**
	* 营销活动ID
	*@param  activityId
	*/
	public void setActivityId(String activityId ){
		this.activityId = activityId;
	}
	/**
	* 产品ID
	*@return
	*/
	public String getGoodsId(){
		return  goodsId;
	}
	/**
	* 产品ID
	*@param  goodsId
	*/
	public void setGoodsId(String goodsId ){
		this.goodsId = goodsId;
	}
	/**
	* 活动数量
	*@return
	*/
	public Integer getActivityQuantity(){
		return  activityQuantity;
	}
	/**
	* 活动数量
	*@param  activityQuantity
	*/
	public void setActivityQuantity(Integer activityQuantity ){
		this.activityQuantity = activityQuantity;
	}
	/**
	* 活动剩余库存
	*@return
	*/
	public Integer getRemainingActivityQuantity(){
		return  remainingActivityQuantity;
	}
	/**
	* 活动剩余库存
	*@param  remainingActivityQuantity
	*/
	public void setRemainingActivityQuantity(Integer remainingActivityQuantity ){
		this.remainingActivityQuantity = remainingActivityQuantity;
	}
	/**
	* SKU ID
	*@return
	*/
	public String getSkuId(){
		return  skuId;
	}
	/**
	* SKU ID
	*@param  skuId
	*/
	public void setSkuId(String skuId ){
		this.skuId = skuId;
	}
	/**
	* 活动供货价
	*@return
	*/
	public BigDecimal getActivitySupplierPrice(){
		return  activitySupplierPrice;
	}
	/**
	* 活动供货价
	*@param  activitySupplierPrice
	*/
	public void setActivitySupplierPrice(BigDecimal activitySupplierPrice ){
		this.activitySupplierPrice = activitySupplierPrice;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
