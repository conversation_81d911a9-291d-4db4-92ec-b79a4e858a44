package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomVcPromotionService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcDotdController
 * @description VC Promotion公共接口管理
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somVcPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC Promotion公共接口管理")
public class SomVcPromotionController extends BasicController {

    @Resource
    SomVcPromotionService somVcPromotionService;

    @Operation(summary = "根据站点+展示码集合查询相关信息")
    @PostMapping(value = "/sellerSkuInfo/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomVcPromotionSellerSkuInfoVo>> querySellerSkuInfo(@RequestBody SomVcPromotionSellerSkuQueryVo queryVo) {
        return ResultVo.ofSuccess(somVcPromotionService.querySellerSkuInfo(queryVo));
    }

    @Operation(summary = "查询供应商编码")
    @PostMapping(value = "/vendorCode/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<String>> queryVendorCode(@RequestBody SomVcPromotionVendorCodeQueryVo queryVo) throws ValidateException {
        return ResultVo.ofSuccess(somVcPromotionService.queryVendorCode(queryVo));
    }

    @Operation(summary = "获取展示码列表")
    @PostMapping(value = "/sellerSkuCode/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<String>> querySellerSkuCode(@RequestBody McProductSalesSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcPromotionService.querySellerSkuCode(searchVo));
    }

    @Operation(summary = "获取展示码列表 分页")
    @PostMapping(value = "/sellerSkuCode/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcPromotionSellerSkuInfoVo>> queryByPageSellerSkuCode(@RequestBody SomVcPromotionSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcPromotionService.queryByPageSellerSkuCode(searchVo));
    }
}
