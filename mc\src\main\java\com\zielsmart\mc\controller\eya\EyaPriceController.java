package com.zielsmart.mc.controller.eya;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.eya.EyaPriceService;
import com.zielsmart.mc.vo.eya.EyaPriceWaringResponseVo;
import com.zielsmart.mc.vo.eya.EyaPriceWaringVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller.eya
 * @title EyaPriceEarlyWaring
 * @description 价格相关接口
 * @date 2022-05-18 10:34:38
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@RequestMapping(value = "/eya-price", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "价格相关接口")
public class EyaPriceController extends BasicController {

    @Resource
    private EyaPriceService service;

    /**
     * warnPrice
     * 价格预警
     *
     * @param waringVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "价格预警")
    @PostMapping(value = "/warnPrice-deprecated")
    @Deprecated
    public ResultVo warnPrice(@RequestBody EyaPriceWaringVo waringVo) throws ValidateException {
        EyaPriceWaringResponseVo responseVo = service.warnPrice(waringVo);
        if (StrUtil.isBlank(responseVo.getMsg()) && ObjectUtil.isEmpty(responseVo.getDiscount())) {
            return ResultVo.ofSuccess();
        } else if(StrUtil.isNotBlank(responseVo.getMsg()) && ObjectUtil.isEmpty(responseVo.getDiscount())){
            return ResultVo.ofFail(responseVo.getMsg());
        }else {
            return ResultVo.ofFail(responseVo,responseVo.getMsg());
        }
    }

    @Operation(summary = "价格预警")
    @PostMapping(value = "/warnPrice")
    public ResultVo warnPriceNew(@RequestBody EyaPriceWaringVo waringVo) throws ValidateException {
        return service.warnPriceNew(waringVo);
    }
}
