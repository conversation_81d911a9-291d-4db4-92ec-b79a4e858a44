package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomVcInventoryReportService
 * @description
 * @date 2024-01-23 16:32:23
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcInventoryReportService {
    @Resource
    private McDearanceProductMapper mcDearanceProductMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private McStockInfoMapper mcStockInfoMapper;

    @Resource
    private SomAmazonVcListingMapper somAmazonVcListingMapper;

    @Resource
    private McVcWarehouseConfigMapper mcVcWarehouseConfigMapper;

    @Resource
    private SomVcClearanceListMapper somVcClearanceListMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;

    /**
     * 分页查询VC可售库存报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomVcSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomVcSaleInventoryReportVo> queryVcInventoryReport(SomVcSaleInventoryReportSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        // 查询字典
        List<McDictionaryInfo> dictionaryInfos = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "VcCooperationPattern").andEq("item_value1", "1").select();
        List<String> cooperationPatternCodes = dictionaryInfos.stream().map(McDictionaryInfo::getItemValue).collect(Collectors.toList());
        //查询VCListing信息
        PageResult<SomVcSaleInventoryReportVo> pageResult = somAmazonVcListingMapper.queryVcInventoryReport(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomVcSaleInventoryReportVo> reportVoList = pageResult.getList();
            //查询所有的可售仓库配置信息
            List<McVcWarehouseConfig> configVoList = mcVcWarehouseConfigMapper.all();
            Map<String, List<McVcWarehouseConfig>> map = configVoList.stream().collect(Collectors.groupingBy(e -> e.getSite() + e.getVendorCode()));
            Map<String, List<McVcWarehouseConfig>> configMap = configVoList.stream().collect(Collectors.groupingBy(e -> e.getSite() + e.getPlatformWarehouseCode() + e.getVendorCode()));
            //查询库存信息
            List<McStockInfo> stockInfoList = mcStockInfoMapper.createQuery().andIn("product_main_code", reportVoList.stream().map(SomVcSaleInventoryReportVo::getProductMainCode).distinct().collect(Collectors.toList())).select();
            Map<String, McStockInfo> stockInfoMap = stockInfoList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
            // 查询已配置的仓库-库区信息
            List<McWareHouseAndSlVo> allConfigList = dynamicSqlManager.getMapper(McVcWarehouseConfigMapper.class).queryAllConfigList().stream().distinct().collect(Collectors.toList());
            // 查询所有配置的仓库-库区信息
            List<SomStorageLocation> locations = somStorageLocationMapper.queryAllInfos();
            Map<String, SomStorageLocation> locationsMap = locations.stream().collect(Collectors.toMap(x -> x.getwhCode() + x.getslCode(), Function.identity(), (x1, x2) -> x1));
            // 查询VC的清仓产品
            Set<String> sites = reportVoList.stream().map(SomVcSaleInventoryReportVo::getSite).collect(Collectors.toSet());
            List<SomVcClearanceList> clearanceLists = somVcClearanceListMapper.createLambdaQuery()
                    .andIn("site", sites)
                    .select();
            List<String> existKeys = clearanceLists.stream().map(data -> StrUtil.concat(true, data.getSite(), data.getVendorCode(), data.getSellerSku(), data.getPlatformWarehouseCode())).collect(Collectors.toList());
            for (SomVcSaleInventoryReportVo report : reportVoList) {
                String siteVendorCode = report.getSite() + report.getVendorCode();
                List<McVcWarehouseConfig> configList = map.getOrDefault(siteVendorCode, new ArrayList<>());
                // 计算总库存
                configList.stream()
                        .map(f -> formatInteger(stockInfoMap.getOrDefault(f.getWarehouseCode() + f.getUsableStorageLocation() + report.getProductMainCode(), new McStockInfo()).getTotalStock()))
                        .reduce(Integer::sum)
                        .ifPresent(report::setSaleInventory);
                if (null == report.getSaleInventory() || report.getSaleInventory() < 0) {
                    report.setSaleInventory(0);
                }
                if (null == report.getSafetyStock() || report.getSafetyStock() < 0) {
                    report.setSafetyStock(0);
                }
                List<SomVcSaleInventoryReportVo.inventoryDetails> detailsList = new ArrayList<>();
                // 初始此处为非清仓产品，如果有可售仓库配置，再根据可售仓库仓库配置重置
                report.setClearanceFlag(false);
                for (McVcWarehouseConfig config : configList) {
                    SomVcSaleInventoryReportVo.inventoryDetails details = new SomVcSaleInventoryReportVo.inventoryDetails();
                    List<McVcWarehouseConfig> itemList = configMap.getOrDefault(config.getSite() + config.getPlatformWarehouseCode() + config.getVendorCode(), new ArrayList<>());
                    // 计算平台仓库编码对应的可售库存
                    itemList.stream()
                            .map(y -> formatInteger(stockInfoMap.getOrDefault(y.getWarehouseCode() + y.getUsableStorageLocation() + report.getProductMainCode(), new McStockInfo()).getTotalStock()))
                            .reduce(Integer::sum)
                            .ifPresent(details::setSaleInventory);
                    List<String> warehouseNameList = new ArrayList<>();
                    itemList.forEach(item -> allConfigList.stream().filter(x -> StrUtil.equals(x.getPlatform(), item.getPlatform()) && StrUtil.equals(x.getSite(), item.getSite()) && StrUtil.equals(x.getUsableWarehouse(), item.getWarehouseCode()) && StrUtil.equals(x.getUsableStorageLocation(), item.getUsableStorageLocation()))
                            .findFirst().ifPresent(f -> warehouseNameList.add(f.getWarehouseName() + "-" + f.getSLName())));
                    // 新增逻辑，如果销售视图VC合作模式=SC-VOC或VC-VOC，则需要推送[正品库-通用渠道]的库存
                    Integer generalInventory = 0;
                    if (cooperationPatternCodes.contains(report.getVcCooperationPatternCode())) {
                        // 获取平台仓库编码对应的仓库
                        List<String> warehouseCodes = itemList.stream().map(McVcWarehouseConfig::getWarehouseCode).distinct().collect(Collectors.toList());
                        // 获取平台仓库编码对应的仓库中的[正品库-通用库存]的库存，库区固定为 '1000'
                        generalInventory = warehouseCodes.stream().map(y -> {
                            McStockInfo mcStockInfo = stockInfoMap.get(y + "1000" + report.getProductMainCode());
                            return mcStockInfo == null ? 0 : mcStockInfo.getTotalStock();
                        }).reduce(Integer::sum).orElse(0);
                        // 查询[正品库-通用库存]的仓库名称，放置于集合 warehouseNameList 内
                        warehouseCodes.forEach(warehouseCode -> {
                            SomStorageLocation somStorageLocation = locationsMap.get(warehouseCode + "1000");
                            if (somStorageLocation != null) {
                                warehouseNameList.add(somStorageLocation.getwhName() + "-" + somStorageLocation.getslName());
                            }
                        });
                    }
                    details.setGeneralInventory(generalInventory);
                    // 可售库存+通用库存
                    details.setSaleInventory(details.getSaleInventory() + generalInventory);
                    details.setWarehouseNameList(warehouseNameList);
                    details.setPlatformWarehouseCode(config.getPlatformWarehouseCode());
                    // 判断该明细是否是清仓产品，如果不是清仓产品，则可售库存=库存-安全库存
                    String key = StrUtil.concat(true, report.getSite(), report.getVendorCode(), report.getSellerSku(), config.getPlatformWarehouseCode());
                    details.setClearanceFlag(existKeys.contains(key));
                    Integer saleInventory = details.getSaleInventory() == null ? 0 : details.getSaleInventory();
                    if (existKeys.contains(key)) {
                        details.setSaleInventory(saleInventory);
                        report.setClearanceFlag(true);
                    } else {
                        details.setSaleInventory(saleInventory - report.getSafetyStock());
                    }
                    if (details.getSaleInventory() < 0) {
                        details.setSaleInventory(0);
                    }
                    detailsList.add(details);
                }
                report.setDetailsList(detailsList.stream().distinct().collect(Collectors.toList()));
                // 库存=details里面的可售库存的和
                Integer detailTotalSale = report.getDetailsList().stream().map(SomVcSaleInventoryReportVo.inventoryDetails::getSaleInventory).reduce(Integer::sum).orElse(0);
                report.setSaleInventory(detailTotalSale < 0 ? 0 : detailTotalSale);
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomVcSaleInventoryReportVo.class, searchVo);
    }

    /**
     * 导出VC可售库存报表
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public String export(SomVcSaleInventoryReportSearchVo searchVo) throws ValidateException {
        searchVo.setPageSize(Integer.MAX_VALUE);
        searchVo.setCurrent(1);
        List<SomVcSaleInventoryReportVo> records = queryVcInventoryReport(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomVcSaleInventoryReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (Exception e) {
                throw new ValidateException("导出VC可售库存报表失败：" + e.getMessage());
            }
        }
        return null;
    }

    private int formatInteger(Integer integer) {
        if (integer == null) {
            return 0;
        }
        return integer.intValue();
    }

}
