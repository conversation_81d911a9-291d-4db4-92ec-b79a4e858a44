package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* Facebook meta站外广告明细
* gen by 代码生成器 2023-05-19
*/

@Table(name="mc.som_facebook_meta_ads_report")
public class SomFacebookMetaAdsReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 账号ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 广告系列ID
	 */
	@Column("campaigns_id")
	private String campaignsId ;
	/**
	 * 广告系列名称
	 */
	@Column("campaign_name")
	private String campaignName ;
	/**
	 * 成效类型
	 */
	@Column("objective")
	private String objective ;
	/**
	 * 购物（订购数量）
	 */
	@Column("purchase_value")
	private Integer purchaseValue ;
	/**
	 * 覆盖人数
	 */
	@Column("reach")
	private Integer reach ;
	/**
	 * spend / purchase_value 四舍五入，保留两位小数
	 */
	@Column("single_effective")
	private BigDecimal singleEffective ;
	/**
	 * 总花费
	 */
	@Column("spend")
	private BigDecimal spend ;
	/**
	 * 展示次数
	 */
	@Column("impressions")
	private Integer impressions ;
	/**
	 * 千次展示费用
	 */
	@Column("cpm")
	private BigDecimal cpm ;
	/**
	 * 单次点击链接费用
	 */
	@Column("cost_per_unique_click")
	private BigDecimal costPerUniqueClick ;
	/**
	 * 点击次数
	 */
	@Column("clicks")
	private Integer clicks ;
	/**
	 * 总点击率
	 */
	@Column("ctr")
	private BigDecimal ctr ;
	/**
	 * 平均单次点击费用
	 */
	@Column("cpc")
	private BigDecimal cpc ;
	/**
	 * 加入购物车数量
	 */
	@Column("add_to_cart_value")
	private Integer addToCartValue ;
	/**
	 * purchase_value / clicks 保留两位小数，转化率
	 */
	@Column("conversion_rate")
	private BigDecimal conversionRate ;
	/**
	 * 网站购物转化价值
	 */
	@Column("action_offsite_conversion_fb_pixel_purchase_value")
	private BigDecimal actionOffsiteConversionFbPixelPurchaseValue ;
	/**
	 * 广告花费回报 (ROAS) - 网站购物
	 */
	@Column("website_purchase_roas_offsite_conversion_fb_pixel_purchase_valu")
	private BigDecimal websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu ;
	/**
	 * spend/action_offsite_conversion_fb_pixel_purchase_value保留两位小数  广告投入产出比
	 */
	@Column("acos")
	private BigDecimal acos ;
	/**
	 * 浏览量
	 */
	@Column("view_content_value")
	private Integer viewContentValue ;
	/**
	 * 起始时间
	 */
	@Column("date_start")
	private Date dateStart ;
	/**
	 * 截止时间
	 */
	@Column("date_stop")
	private Date dateStop ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomFacebookMetaAdsReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 账号ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 账号ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 广告系列ID
	*@return
	*/
	public String getCampaignsId(){
		return  campaignsId;
	}
	/**
	* 广告系列ID
	*@param  campaignsId
	*/
	public void setCampaignsId(String campaignsId ){
		this.campaignsId = campaignsId;
	}
	/**
	* 广告系列名称
	*@return
	*/
	public String getCampaignName(){
		return  campaignName;
	}
	/**
	* 广告系列名称
	*@param  campaignName
	*/
	public void setCampaignName(String campaignName ){
		this.campaignName = campaignName;
	}
	/**
	* 成效类型
	*@return
	*/
	public String getObjective(){
		return  objective;
	}
	/**
	* 成效类型
	*@param  objective
	*/
	public void setObjective(String objective ){
		this.objective = objective;
	}
	/**
	* 购物（订购数量）
	*@return
	*/
	public Integer getPurchaseValue(){
		return  purchaseValue;
	}
	/**
	* 购物（订购数量）
	*@param  purchaseValue
	*/
	public void setPurchaseValue(Integer purchaseValue ){
		this.purchaseValue = purchaseValue;
	}
	/**
	* 覆盖人数
	*@return
	*/
	public Integer getReach(){
		return  reach;
	}
	/**
	* 覆盖人数
	*@param  reach
	*/
	public void setReach(Integer reach ){
		this.reach = reach;
	}
	/**
	* spend / purchase_value 四舍五入，保留两位小数
	*@return
	*/
	public BigDecimal getSingleEffective(){
		return  singleEffective;
	}
	/**
	* spend / purchase_value 四舍五入，保留两位小数
	*@param  singleEffective
	*/
	public void setSingleEffective(BigDecimal singleEffective ){
		this.singleEffective = singleEffective;
	}
	/**
	* 总花费
	*@return
	*/
	public BigDecimal getSpend(){
		return  spend;
	}
	/**
	* 总花费
	*@param  spend
	*/
	public void setSpend(BigDecimal spend ){
		this.spend = spend;
	}
	/**
	* 展示次数
	*@return
	*/
	public Integer getImpressions(){
		return  impressions;
	}
	/**
	* 展示次数
	*@param  impressions
	*/
	public void setImpressions(Integer impressions ){
		this.impressions = impressions;
	}
	/**
	* 千次展示费用
	*@return
	*/
	public BigDecimal getCpm(){
		return  cpm;
	}
	/**
	* 千次展示费用
	*@param  cpm
	*/
	public void setCpm(BigDecimal cpm ){
		this.cpm = cpm;
	}
	/**
	* 单次点击链接费用
	*@return
	*/
	public BigDecimal getCostPerUniqueClick(){
		return  costPerUniqueClick;
	}
	/**
	* 单次点击链接费用
	*@param  costPerUniqueClick
	*/
	public void setCostPerUniqueClick(BigDecimal costPerUniqueClick ){
		this.costPerUniqueClick = costPerUniqueClick;
	}
	/**
	* 点击次数
	*@return
	*/
	public Integer getClicks(){
		return  clicks;
	}
	/**
	* 点击次数
	*@param  clicks
	*/
	public void setClicks(Integer clicks ){
		this.clicks = clicks;
	}
	/**
	* 总点击率
	*@return
	*/
	public BigDecimal getCtr(){
		return  ctr;
	}
	/**
	* 总点击率
	*@param  ctr
	*/
	public void setCtr(BigDecimal ctr ){
		this.ctr = ctr;
	}
	/**
	* 平均单次点击费用
	*@return
	*/
	public BigDecimal getCpc(){
		return  cpc;
	}
	/**
	* 平均单次点击费用
	*@param  cpc
	*/
	public void setCpc(BigDecimal cpc ){
		this.cpc = cpc;
	}
	/**
	* 加入购物车数量
	*@return
	*/
	public Integer getAddToCartValue(){
		return  addToCartValue;
	}
	/**
	* 加入购物车数量
	*@param  addToCartValue
	*/
	public void setAddToCartValue(Integer addToCartValue ){
		this.addToCartValue = addToCartValue;
	}
	/**
	* purchase_value / clicks 保留两位小数，转化率
	*@return
	*/
	public BigDecimal getConversionRate(){
		return  conversionRate;
	}
	/**
	* purchase_value / clicks 保留两位小数，转化率
	*@param  conversionRate
	*/
	public void setConversionRate(BigDecimal conversionRate ){
		this.conversionRate = conversionRate;
	}
	/**
	* 网站购物转化价值
	*@return
	*/
	public BigDecimal getActionOffsiteConversionFbPixelPurchaseValue(){
		return  actionOffsiteConversionFbPixelPurchaseValue;
	}
	/**
	* 网站购物转化价值
	*@param  actionOffsiteConversionFbPixelPurchaseValue
	*/
	public void setActionOffsiteConversionFbPixelPurchaseValue(BigDecimal actionOffsiteConversionFbPixelPurchaseValue ){
		this.actionOffsiteConversionFbPixelPurchaseValue = actionOffsiteConversionFbPixelPurchaseValue;
	}
	/**
	* 广告花费回报 (ROAS) - 网站购物
	*@return
	*/
	public BigDecimal getWebsitePurchaseRoasOffsiteConversionFbPixelPurchaseValu(){
		return  websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu;
	}
	/**
	* 广告花费回报 (ROAS) - 网站购物
	*@param  websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu
	*/
	public void setWebsitePurchaseRoasOffsiteConversionFbPixelPurchaseValu(BigDecimal websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu ){
		this.websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu = websitePurchaseRoasOffsiteConversionFbPixelPurchaseValu;
	}
	/**
	* spend/action_offsite_conversion_fb_pixel_purchase_value保留两位小数  广告投入产出比
	*@return
	*/
	public BigDecimal getAcos(){
		return  acos;
	}
	/**
	* spend/action_offsite_conversion_fb_pixel_purchase_value保留两位小数  广告投入产出比
	*@param  acos
	*/
	public void setAcos(BigDecimal acos ){
		this.acos = acos;
	}
	/**
	* 浏览量
	*@return
	*/
	public Integer getViewContentValue(){
		return  viewContentValue;
	}
	/**
	* 浏览量
	*@param  viewContentValue
	*/
	public void setViewContentValue(Integer viewContentValue ){
		this.viewContentValue = viewContentValue;
	}
	/**
	* 起始时间
	*@return
	*/
	public Date getDateStart(){
		return  dateStart;
	}
	/**
	* 起始时间
	*@param  dateStart
	*/
	public void setDateStart(Date dateStart ){
		this.dateStart = dateStart;
	}
	/**
	* 截止时间
	*@return
	*/
	public Date getDateStop(){
		return  dateStop;
	}
	/**
	* 截止时间
	*@param  dateStop
	*/
	public void setDateStop(Date dateStop ){
		this.dateStop = dateStop;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
