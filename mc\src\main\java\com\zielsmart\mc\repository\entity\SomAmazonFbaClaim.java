package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* FBA索赔记录表
* gen by 代码生成器 2023-03-16
*/

@Table(name="mc.som_amazon_fba_claim")
public class SomAmazonFbaClaim implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * FNSKU
	 */
	@Column("fnsku")
	private String fnsku ;
	/**
	 * asin
	 */
	@Column("asin")
	private String asin ;
	/**
	 * case_id
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 索赔金额
	 */
	@Column("claim_amount")
	private BigDecimal claimAmount ;
	/**
	 * 索赔完成时间
	 */
	@Column("claim_date")
	private Date claimDate ;
	/**
	 * 索赔状态：10未索赔 20已索赔 99不索赔
	 */
	@Column("claim_status")
	private Integer claimStatus ;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public SomAmazonFbaClaim() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* FNSKU
	*@return
	*/
	public String getFnsku(){
		return  fnsku;
	}
	/**
	* FNSKU
	*@param  fnsku
	*/
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	/**
	* asin
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* asin
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* case_id
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* case_id
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 索赔金额
	*@return
	*/
	public BigDecimal getClaimAmount(){
		return  claimAmount;
	}
	/**
	* 索赔金额
	*@param  claimAmount
	*/
	public void setClaimAmount(BigDecimal claimAmount ){
		this.claimAmount = claimAmount;
	}
	/**
	* 索赔完成时间
	*@return
	*/
	public Date getClaimDate(){
		return  claimDate;
	}
	/**
	* 索赔完成时间
	*@param  claimDate
	*/
	public void setClaimDate(Date claimDate ){
		this.claimDate = claimDate;
	}
	/**
	* 索赔状态：10未索赔 20已索赔 99不索赔
	*@return
	*/
	public Integer getClaimStatus(){
		return  claimStatus;
	}
	/**
	* 索赔状态：10未索赔 20已索赔 99不索赔
	*@param  claimStatus
	*/
	public void setClaimStatus(Integer claimStatus ){
		this.claimStatus = claimStatus;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemark(){
		return  remark;
	}
	/**
	* 备注
	*@param  remark
	*/
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  ceateNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 最后修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
