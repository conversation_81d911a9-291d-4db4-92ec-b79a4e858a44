package com.zielsmart.mc.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.event
 * @title AmazonActivityReminderEvent
 * @description Amazon平台营销活动提报失败消息提醒
 * @date 2022-11-02 10:40:59
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Builder
@Data
public class AmazonActivityReminderEvent extends ApplicationEvent {
    public AmazonActivityReminderEvent(Object source) {
        super(source);
    }

    private List<Reminder> list;


    public AmazonActivityReminderEvent(List<Reminder> list) {
        super("");
        this.list = list;
    }

    @Data
    public static class Reminder {
        private String site;
        private String sellerSku;
        private String internalDescription;
        private String sortName;
        private Date startTime;
        private Date endTime;
        private String typeName;
        private String failMsg;
        private String creatNum;
        //销售负责人工号
        private String salesGroupEmptCode;

        public Reminder(String site, String sellerSku, String internalDescription, String sortName, Date startTime, Date endTime, String typeName, String failMsg, String creatNum) {
            this.site = site;
            this.sellerSku = sellerSku;
            this.internalDescription = internalDescription;
            this.sortName = sortName;
            this.startTime = startTime;
            this.endTime = endTime;
            this.typeName = typeName;
            this.failMsg = failMsg;
            this.creatNum = creatNum;
        }
    }
}
