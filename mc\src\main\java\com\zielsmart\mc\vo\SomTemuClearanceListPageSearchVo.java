package com.zielsmart.mc.vo;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.zielsmart.web.basic.vo.PageSearchVo;

/*
* Temu库存推送黑名单的VO分页查询实体
* gen by 代码生成器 2024-12-26
*/

@Data
@Schema(title = "Temu库存推送黑名单分页查询实体",name = "SomTemuClearanceListPageSearchVo")
public class SomTemuClearanceListPageSearchVo extends PageSearchVo {
	/**
	 * 主键
	 */
    @Schema(description = "主键",name="aid")
	private String aid ;
	/**
	 * 站点
	 */
    @Schema(description = "站点",name="site")
	private String site ;
	/**
	 * 平台SKU唯一编码
	 */
    @Schema(description = "平台SKU唯一编码",name="productSkuId")
	private String productSkuId ;
	/**
	 * 展示码
	 */
    @Schema(description = "展示码",name="sellerSku")
	private String sellerSku ;
	/**
	 * 店铺ID
	 */
    @Schema(description = "店铺ID",name="accountId")
	private String accountId ;
	/**
	 * 创建人工号
	 */
    @Schema(description = "创建人工号",name="createNum")
	private String createNum ;
	/**
	 * 创建人工号
	 */
    @Schema(description = "创建人工号",name="createName")
	private String createName ;
	/**
	 * 创建时间
	 */
    @Schema(description = "创建时间",name="createTime")
	private Date createTime ;



}
