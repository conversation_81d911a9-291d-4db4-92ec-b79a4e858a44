package com.zielsmart.mc.controller;

import com.zielsmart.mc.repository.entity.McPlatformProperties;
import com.zielsmart.mc.service.PlatformPropertiesService;
import com.zielsmart.mc.vo.McPlatformPropertiesPageSearchVo;
import com.zielsmart.mc.vo.McPlatformPropertiesVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title PlatformPropertiesController
 * @description
 * @date 2021-08-04 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/platform_properties", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台站点属性配置管理")
public class PlatformPropertiesController extends BasicController {

    @Resource
    PlatformPropertiesService platformPropertiesService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McPlatformPropertiesVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McPlatformPropertiesVo>> queryByPage(@RequestBody McPlatformPropertiesPageSearchVo searchVo) {
        return ResultVo.ofSuccess(platformPropertiesService.queryByPage(searchVo));
    }

    /**
     * save
     * 添加修改
     * @param mcPlatformPropertiesVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加修改")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McPlatformPropertiesVo mcPlatformPropertiesVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        platformPropertiesService.save(mcPlatformPropertiesVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcPlatformPropertiesVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McPlatformPropertiesVo mcPlatformPropertiesVo) throws ValidateException {
        platformPropertiesService.delete(mcPlatformPropertiesVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * searchPlatformSites
     * 查询B2C渠道平台站点
     * @param
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询B2C渠道平台站点")
    @PostMapping(value = "/searchB2CPlatformSites")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<Map<String, List<McPlatformProperties>>> searchB2CPlatformSites() {
        return ResultVo.ofSuccess(platformPropertiesService.searchB2CPlatformSites());
    }

    /**
     * searchByPlatformAndSite
     * 根据平台站点查询配置信息
     * @param mcPlatformPropertiesVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.McPlatformPropertiesVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点查询配置信息")
    @PostMapping(value = "/searchByPlatformAndSite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<McPlatformPropertiesVo> searchByPlatformAndSite(@RequestBody McPlatformPropertiesVo mcPlatformPropertiesVo) throws ValidateException {
        return ResultVo.ofSuccess(platformPropertiesService.searchByPlatformAndSite(mcPlatformPropertiesVo));
    }
}
