package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2023-12-05
 */

@Table(name = "mc.som_target_listing")
public class SomTargetListing implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 状态
     */
    @Column("status")
    private String status;
    /**
     * 产品链接地址
     */
    @Column("store_page_url")
    private String storePageUrl;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 标题
     */
    @Column("title")
    private String title;
    /**
     * 产品描述
     */
    @Column("description")
    private String description;
    /**
     * 价格
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 产品ID
     */
    @Column("product_id")
    private Integer productId;
    /**
     * 产品编码
     */
    @Column("item_number")
    private String itemNumber;
    /**
     * 发货方式
     */
    @Column("fulfilled_by")
    private String fulfilledBy;
    /**
     * 下载时间
     */
    @Column("create_time")
    private Date createTime;

    /**
     * 站点
     */
    @Column("site")
    private String site;

    public SomTargetListing() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 状态
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 产品链接地址
     *
     * @return
     */
    public String getStorePageUrl() {
        return storePageUrl;
    }

    /**
     * 产品链接地址
     *
     * @param storePageUrl
     */
    public void setStorePageUrl(String storePageUrl) {
        this.storePageUrl = storePageUrl;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 标题
     *
     * @return
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 产品描述
     *
     * @return
     */
    public String getDescription() {
        return description;
    }

    /**
     * 产品描述
     *
     * @param description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 价格
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 价格
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 产品ID
     *
     * @return
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 产品ID
     *
     * @param productId
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 产品编码
     *
     * @return
     */
    public String getItemNumber() {
        return itemNumber;
    }

    /**
     * 产品编码
     *
     * @param itemNumber
     */
    public void setItemNumber(String itemNumber) {
        this.itemNumber = itemNumber;
    }

    /**
     * 发货方式
     *
     * @return
     */
    public String getFulfilledBy() {
        return fulfilledBy;
    }

    /**
     * 发货方式
     *
     * @param fulfilledBy
     */
    public void setFulfilledBy(String fulfilledBy) {
        this.fulfilledBy = fulfilledBy;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 下载时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }
}
