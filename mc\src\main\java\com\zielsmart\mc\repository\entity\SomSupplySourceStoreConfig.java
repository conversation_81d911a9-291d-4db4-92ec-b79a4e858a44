package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 自提仓配置表
* gen by 代码生成器 2022-09-19
*/

@Table(name="mc.som_supply_source_store_config")
public class SomSupplySourceStoreConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 主表ID
	 */
	@Column("store_id")
	private String storeId ;
	/**
	 * 配置JSON字符串
	 */
	@Column("configuration_json_string")
	private String configurationJsonString ;
	/**
	 * 自提点功能JSON字符串
	 */
	@Column("capabilities_json_string")
	private String capabilitiesJsonString ;
	/**
	 * 10.未推送  20.推送成功 99.推送失败
	 */
	@Column("push_status")
	private Integer pushStatus ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 推送人工号
	 */
	@Column("push_num")
	private String pushNum ;
	/**
	 * 推送人姓名
	 */
	@Column("push_name")
	private String pushName ;
	/**
	 * 推送时间
	 */
	@Column("push_time")
	private Date pushTime ;
	/**
	 * 版本
	 */
	@Column("config_version")
	private String configVersion ;

	public SomSupplySourceStoreConfig() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 主表ID
	*@return
	*/
	public String getStoreId(){
		return  storeId;
	}
	/**
	* 主表ID
	*@param  storeId
	*/
	public void setStoreId(String storeId ){
		this.storeId = storeId;
	}
	/**
	* 配置JSON字符串
	*@return
	*/
	public String getConfigurationJsonString(){
		return  configurationJsonString;
	}
	/**
	* 配置JSON字符串
	*@param  configurationJsonString
	*/
	public void setConfigurationJsonString(String configurationJsonString ){
		this.configurationJsonString = configurationJsonString;
	}
	/**
	* 自提点功能JSON字符串
	*@return
	*/
	public String getCapabilitiesJsonString(){
		return  capabilitiesJsonString;
	}
	/**
	* 自提点功能JSON字符串
	*@param  capabilitiesJsonString
	*/
	public void setCapabilitiesJsonString(String capabilitiesJsonString ){
		this.capabilitiesJsonString = capabilitiesJsonString;
	}
	/**
	* 10.未推送  20.推送成功 99.推送失败
	*@return
	*/
	public Integer getPushStatus(){
		return  pushStatus;
	}
	/**
	* 10.未推送  20.推送成功 99.推送失败
	*@param  pushStatus
	*/
	public void setPushStatus(Integer pushStatus ){
		this.pushStatus = pushStatus;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 推送人工号
	*@return
	*/
	public String getPushNum(){
		return  pushNum;
	}
	/**
	* 推送人工号
	*@param  pushNum
	*/
	public void setPushNum(String pushNum ){
		this.pushNum = pushNum;
	}
	/**
	* 推送人姓名
	*@return
	*/
	public String getPushName(){
		return  pushName;
	}
	/**
	* 推送人姓名
	*@param  pushName
	*/
	public void setPushName(String pushName ){
		this.pushName = pushName;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getPushTime(){
		return  pushTime;
	}
	/**
	* 推送时间
	*@param  pushTime
	*/
	public void setPushTime(Date pushTime ){
		this.pushTime = pushTime;
	}
	/**
	* 版本
	*@return
	*/
	public String getConfigVersion(){
		return  configVersion;
	}
	/**
	* 版本
	*@param  configVersion
	*/
	public void setConfigVersion(String configVersion ){
		this.configVersion = configVersion;
	}

}
