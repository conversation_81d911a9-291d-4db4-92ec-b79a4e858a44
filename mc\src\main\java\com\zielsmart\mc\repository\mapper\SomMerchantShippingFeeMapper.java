package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomMerchantShippingFee;
import com.zielsmart.mc.vo.SomMerchantShippingFeePageSearchVo;
import com.zielsmart.mc.vo.SomMerchantShippingFeeVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2024-11-13
 */

@SqlResource("somMerchantShippingFee")
public interface SomMerchantShippingFeeMapper extends BaseMapper<SomMerchantShippingFee> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomMerchantShippingFeeVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMerchantShippingFeeVo> queryByPage(@Param("searchVo") SomMerchantShippingFeePageSearchVo searchVo, PageRequest pageRequest);
}
