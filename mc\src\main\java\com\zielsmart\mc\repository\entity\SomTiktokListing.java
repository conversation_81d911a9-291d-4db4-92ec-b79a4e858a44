package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* TikTok listing
* gen by 代码生成器 2024-12-12
*/

@Table(name="mc.som_tiktok_listing")
public class SomTiktokListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 店铺ID
	 */
	@Column("shop_cipher")
	private String shopCipher ;
	/**
	 * 产品ID
	 */
	@Column("product_id")
	private String productId ;
	/**
	 * 产品标题
	 */
	@Column("title")
	private String title ;
	/**
	 * 产品状态
	 */
	@Column("status")
	private String status ;
	/**
	 * SKU信息
	 */
	@Column("skus")
	private Object skus ;
	/**
	 * 销售市场
	 */
	@Column("sales_regions")
	private String salesRegions ;
	/**
	 * 站点，由sales_regions映
	 */
	@Column("site")
	private String site ;
	/**
	 * 产品创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 产品最后修改时间
	 */
	@Column("update_time")
	private Date updateTime ;
	/**
	 * 全局产品信息同步到本地产品失败的原因
	 */
	@Column("product_sync_fail_reasons")
	private String productSyncFailReasons ;
	/**
	 * 产品是否可售，0.不可售 1.可售
	 */
	@Column("is_not_for_sale")
	private Integer isNotForSale ;
	/**
	 * 根据产品标题、描述和图片推荐产品类别
	 */
	@Column("recommended_categories")
	private String recommendedCategories ;
	/**
	 * 产品质量等级
	 */
	@Column("listing_quality_tier")
	private String listingQualityTier ;
	/**
	 * 产品在与TikTok Shop原生集成的平台上的当前状态
	 */
	@Column("integrated_platform_statuses")
	private String integratedPlatformStatuses ;
	/**
	 * TikTok商店产品审核信息
	 */
	@Column("audit")
	private String audit ;
	/**
	 * 下载时间
	 */
	@Column("download_time")
	private Date downloadTime ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;

	public SomTiktokListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getShopCipher(){
		return  shopCipher;
	}
	/**
	* 店铺ID
	*@param  shopCipher
	*/
	public void setShopCipher(String shopCipher ){
		this.shopCipher = shopCipher;
	}
	/**
	* 产品ID
	*@return
	*/
	public String getProductId(){
		return  productId;
	}
	/**
	* 产品ID
	*@param  productId
	*/
	public void setProductId(String productId ){
		this.productId = productId;
	}
	/**
	* 产品标题
	*@return
	*/
	public String getTitle(){
		return  title;
	}
	/**
	* 产品标题
	*@param  title
	*/
	public void setTitle(String title ){
		this.title = title;
	}
	/**
	* 产品状态
	*@return
	*/
	public String getStatus(){
		return  status;
	}
	/**
	* 产品状态
	*@param  status
	*/
	public void setStatus(String status ){
		this.status = status;
	}
	/**
	* SKU信息
	*@return
	*/
	public Object getSkus(){
		return  skus;
	}
	/**
	* SKU信息
	*@param  skus
	*/
	public void setSkus(Object skus ){
		this.skus = skus;
	}
	/**
	* 销售市场
	*@return
	*/
	public String getSalesRegions(){
		return  salesRegions;
	}
	/**
	* 销售市场
	*@param  salesRegions
	*/
	public void setSalesRegions(String salesRegions ){
		this.salesRegions = salesRegions;
	}
	/**
	* 站点，由sales_regions映
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点，由sales_regions映
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 产品创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 产品创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 产品最后修改时间
	*@return
	*/
	public Date getUpdateTime(){
		return  updateTime;
	}
	/**
	* 产品最后修改时间
	*@param  updateTime
	*/
	public void setUpdateTime(Date updateTime ){
		this.updateTime = updateTime;
	}
	/**
	* 全局产品信息同步到本地产品失败的原因
	*@return
	*/
	public String getProductSyncFailReasons(){
		return  productSyncFailReasons;
	}
	/**
	* 全局产品信息同步到本地产品失败的原因
	*@param  productSyncFailReasons
	*/
	public void setProductSyncFailReasons(String productSyncFailReasons ){
		this.productSyncFailReasons = productSyncFailReasons;
	}
	/**
	* 产品是否可售，0.不可售 1.可售
	*@return
	*/
	public Integer getisNotForSale(){
		return  isNotForSale;
	}
	/**
	* 产品是否可售，0.不可售 1.可售
	*@param  isNotForSale
	*/
	public void setisNotForSale(Integer isNotForSale ){
		this.isNotForSale = isNotForSale;
	}
	/**
	* 根据产品标题、描述和图片推荐产品类别
	*@return
	*/
	public String getRecommendedCategories(){
		return  recommendedCategories;
	}
	/**
	* 根据产品标题、描述和图片推荐产品类别
	*@param  recommendedCategories
	*/
	public void setRecommendedCategories(String recommendedCategories ){
		this.recommendedCategories = recommendedCategories;
	}
	/**
	* 产品质量等级
	*@return
	*/
	public String getListingQualityTier(){
		return  listingQualityTier;
	}
	/**
	* 产品质量等级
	*@param  listingQualityTier
	*/
	public void setListingQualityTier(String listingQualityTier ){
		this.listingQualityTier = listingQualityTier;
	}
	/**
	* 产品在与TikTok Shop原生集成的平台上的当前状态
	*@return
	*/
	public String getIntegratedPlatformStatuses(){
		return  integratedPlatformStatuses;
	}
	/**
	* 产品在与TikTok Shop原生集成的平台上的当前状态
	*@param  integratedPlatformStatuses
	*/
	public void setIntegratedPlatformStatuses(String integratedPlatformStatuses ){
		this.integratedPlatformStatuses = integratedPlatformStatuses;
	}
	/**
	* TikTok商店产品审核信息
	*@return
	*/
	public String getAudit(){
		return  audit;
	}
	/**
	* TikTok商店产品审核信息
	*@param  audit
	*/
	public void setAudit(String audit ){
		this.audit = audit;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 下载时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}

}
