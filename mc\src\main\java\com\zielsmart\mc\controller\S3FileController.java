package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.S3FileService;
import com.zielsmart.mc.vo.S3Vo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.file.FileSearchResponse;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @version V2.0
 * @title: S3FileController
 * @package: com.zielsmart.zbpm.controller.S3FileController
 * @description: S3文件存储管理
 * @author: 王帅杰
 * @date: 2023-12-21 02:10:35
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Tag(name = "S3文件存储管理")
@Controller
@RequestMapping(value = "/s3", consumes = MediaType.APPLICATION_JSON_VALUE)
public class S3FileController {

    @Resource
    private S3FileService s3FileService;

    @Operation(summary = "文件上传{site:'',sku:'',dirPath:'',type:''}")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<JSONObject>> upload(@RequestParam Map param,
                                             @RequestParam("files") MultipartFile[] multipartFiles, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) throws Exception {
        s3FileService.upload(param, multipartFiles, tokenUserInfo);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载文件")
    @PostMapping(value = "/download", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<FileSearchResponse> download(@RequestParam(value = "filePath") String filePath) throws Exception {
        return ResultVo.ofSuccess(s3FileService.download(filePath));
    }

    @Operation(summary = "文件删除 {filePath:xx}")
    @PostMapping(value = "/delete", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> delete(@RequestBody Map param) {
        s3FileService.delete(param);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "文件列表")
    @PostMapping(value = "/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<List<S3FileService.S3FileNode>> list(@RequestBody Map param) throws ValidateException {
        return ResultVo.ofSuccess(s3FileService.list(param));
    }

    @Operation(summary = "图片URL下载")
    @PostMapping(value = "/export-image-url", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> exportImageUrl(@RequestBody S3Vo s3Vo) throws ValidateException {
        String data = s3FileService.exportImageUrl(s3Vo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}

