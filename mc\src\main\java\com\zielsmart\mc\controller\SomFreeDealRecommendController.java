package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomFreeDealRecommendService;
import com.zielsmart.mc.vo.SomFreeDealRecommendItemVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendPageSearchVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomFreeDealRecommendController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somFreeDealRecommend", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "LD&7DD推荐数据管理")
public class SomFreeDealRecommendController extends BasicController {

    @Resource
    SomFreeDealRecommendService somFreeDealRecommendService;

    /**
     * queryByPage
     *
     * @param pageSearchVo
     * @return {@link ResultVo< PageVo< SomFreeDealRecommendVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomFreeDealRecommendVo>> queryByPage(@RequestBody SomFreeDealRecommendPageSearchVo pageSearchVo) {
        return ResultVo.ofSuccess(somFreeDealRecommendService.queryByPage(pageSearchVo));
    }

    /**
     * save
     *
     * @param somFreeDealRecommendVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomFreeDealRecommendVo somFreeDealRecommendVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeDealRecommendService.save(somFreeDealRecommendVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somFreeDealRecommendVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomFreeDealRecommendVo somFreeDealRecommendVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somFreeDealRecommendService.update(somFreeDealRecommendVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somFreeDealRecommendVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomFreeDealRecommendVo somFreeDealRecommendVo) throws ValidateException {
        somFreeDealRecommendService.delete(somFreeDealRecommendVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryBySiteAndDealType
     * 通过站点和类型查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomFreeDealRecommendVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "通过站点和类型查询")
    @PostMapping(value = "/queryBySiteAndDealType")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomFreeDealRecommendItemVo>> queryBySiteAndDealType(@RequestBody SomFreeDealRecommendPageSearchVo searchVo) throws ValidateException{
        return ResultVo.ofSuccess(somFreeDealRecommendService.queryBySiteAndDealType(searchVo));
    }
}
