package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.zielsmart.mc.event.AmazonActivityReminderEvent;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IBiService;
import com.zielsmart.mc.util.MarketActivityUtil;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.zbpm.ZBPMDealDetailVo;
import com.zielsmart.mc.vo.zbpm.ZBPMDealVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.listener.EventBusTemplate;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomFreeAddDealService {

    @Resource
    private SomFreeAddDealMapper somFreeAddDealMapper;
    @Resource
    private SomFreeAddDealItemsMapper somFreeAddDealItemsMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private EventBusTemplate eventBusTemplate;
    @Resource
    private AmazonService amazonService;
    @Resource
    private McStockInfoMapper stockInfoMapper;
    @Resource
    private SomDealRuleConfigMapper configMapper;
    @Resource
    private SomDealService dealService;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private SysDeptNeweyaMapper deptMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;
    @Resource
    PlatformPropertiesService platformPropertiesService;
    @Value("${remote.oa.remote.url}")
    private String oaUrl;
    @Resource
    private IBiService biService;
    @Resource
    private RestTemplate restTemplate;
    @Value("${bi.magic.head.token}")
    private String biToken;

    /**
     * save
     * 添加或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(SomFreeAddDealExtVo addOrEditVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addOrEditVo) || CollectionUtil.isEmpty(addOrEditVo.getItemsVoList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long featuredCount = addOrEditVo.getItemsVoList().stream().filter(x -> ObjectUtil.isNotNull(x.getIsFeatured()) && 1 == x.getIsFeatured()).count();
        if (featuredCount != 1) {
            throw new ValidateException("请设置您的精选ASIN");
        }

        Integer descriptionCount = somFreeAddDealMapper.checkInternalDescription(addOrEditVo);
        if (0 < descriptionCount) {
            throw new ValidateException("「Internal Description」不允许出现重复");
        }
        String id = StrUtil.isBlank(addOrEditVo.getAid()) ? Strings.EMPTY : addOrEditVo.getAid();
        List<String> asinList = addOrEditVo.getItemsVoList().stream().map(m -> m.getAsin()).collect(Collectors.toList());
        List<String> sellerSkuList = addOrEditVo.getItemsVoList().stream().map(m -> m.getSellerSku()).collect(Collectors.toList());
        Integer countDeal = somFreeAddDealMapper.checkRepeatDeal(id, addOrEditVo.getSite(), asinList, addOrEditVo.getPlanStartDate(), addOrEditVo.getPlanEndDate());
        if (0 < countDeal) {
            throw new ValidateException("存在其他类型的Deal,请检查");
        }
        String dealType = "Lighting Deal";
        if (ObjectUtil.equal(20, addOrEditVo.getDealType())) {
            dealType = "7 Day Deal";
        }
        SomDealRuleConfigVo config = configMapper.queryBySiteDealType(addOrEditVo.getSite(), dealType);
        if (ObjectUtil.isEmpty(config) || StrUtil.isEmpty(config.getRules())) {
            throw new ValidateException("当前站点下该Deal类型未配置秒杀规则,请前往秒杀规则配置功能进行配置");
        }
        dealService.checkDeal(id, addOrEditVo.getSite(), sellerSkuList, asinList, addOrEditVo.getPlanStartDate(), config);

        if (StrUtil.isBlank(addOrEditVo.getAid())) {
            String aid = IdUtil.fastSimpleUUID();
            addOrEditVo.setAid(aid);
            addOrEditVo.setPlatform("Amazon");
            addOrEditVo.setStatus(10);
            addOrEditVo.setCreateNum(tokenUser.getJobNumber());
            addOrEditVo.setCreateName(tokenUser.getUserName());
            addOrEditVo.setCreateTime(DateTime.now().toJdkDate());
            SomFreeAddDeal deal = ConvertUtils.beanConvert(addOrEditVo, SomFreeAddDeal.class);

            if (CollectionUtil.isNotEmpty(addOrEditVo.getItemsVoList())) {
                MarketActivityUtil.MarketMsgData msgData = calcDays(addOrEditVo, dealType);
                if (!msgData.isSuccess()) {
                    String errorMsg = msgData.getErrorMap().values().stream().collect(Collectors.joining(","));
                    throw new ValidateException(errorMsg);
                } else {
                    for (SomFreeAddDealItemsVo itemsVo : addOrEditVo.getItemsVoList()) {
                        // 数值过大 强制转换
                        if (itemsVo.getExpectedGrossProfitMargin() != null
                                && itemsVo.getExpectedGrossProfitMargin().toPlainString().replace("-", "").replace(".", "").length() > 8) {
                            itemsVo.setExpectedGrossProfitMargin(BigDecimal.valueOf(-9999));
                        }
                        itemsVo.setAid(IdUtil.fastSimpleUUID());
                        itemsVo.setDealId(aid);
                        itemsVo.setStockSaleDays((Integer) msgData.getDaysMap().get(itemsVo.getSellerSku()));
                        itemsVo.setCompletionRate((BigDecimal) msgData.getDaysMap().get(itemsVo.getSellerSku() + "_completionRate"));
//                        McProductSales productSale = productSales.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getSite(), addOrEditVo.getSite()) &&
//                                StrUtil.equalsIgnoreCase(f.getDisplayProductCode(), itemsVo.getSellerSku())).findFirst().orElse(null);
                        //计算库存可售天数
                    /*if (ObjectUtil.isNotEmpty(productSale) && StrUtil.isNotEmpty(productSale.getProductMainCode())) {
                        List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(addOrEditVo.getSite(), f.getSite()) &&
                                StrUtil.equalsIgnoreCase(productSale.getProductMainCode(), f.getProductMainCode())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(stockInfos)) {
                            long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                            BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                                itemsVo.setStockSaleDays(0);
                            } else {
                                // 库存可销售天数 库存/7天发货平均值 只取整数
                                int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                                itemsVo.setStockSaleDays(stockSaleDays);
                            }
                        } else {
                            itemsVo.setStockSaleDays(0);
                        }
                    } else {
                        itemsVo.setStockSaleDays(0);
                    }*/
                        // 确定审批角色
                        itemsVo.setSite(addOrEditVo.getSite());
                        itemsVo.setApprovalRole(initApprovalRole(itemsVo));
                    }
                }
                List<SomFreeAddDealItems> itemsList = ConvertUtils.listConvert(addOrEditVo.getItemsVoList(), SomFreeAddDealItems.class);
                somFreeAddDealItemsMapper.insertBatch(itemsList);

                deal.setApprovalRole(initApprovalRoleGroup(itemsList));
                somFreeAddDealMapper.insert(deal);
            }
        } else {
            SomFreeAddDeal obj = somFreeAddDealMapper.createLambdaQuery().andEq("aid", addOrEditVo.getAid()).single();
//            if (ObjectUtil.isEmpty(obj) || ((ObjectUtil.notEqual(10, obj.getStatus()) && ObjectUtil.notEqual(40, obj.getStatus()) && ObjectUtil.notEqual(70, obj.getStatus())))) {
            if (ObjectUtil.isEmpty(obj) || !(ObjectUtil.equal(10, obj.getStatus()) || ObjectUtil.equal(41, obj.getStatus()))) {
                throw new ValidateException("当前数据不允许编辑");
            }
            SomFreeAddDeal deal = ConvertUtils.beanConvert(addOrEditVo, SomFreeAddDeal.class);
//            if (ObjectUtil.equal(40, obj.getStatus()) || ObjectUtil.equal(70, obj.getStatus())) {
            if (ObjectUtil.equal(41, obj.getStatus())) {
                deal.setModifyStatus(10);
            } else {
                deal.setModifyStatus(obj.getModifyStatus());
            }
            deal.setPlatform(obj.getPlatform());
            deal.setStatus(obj.getStatus());
            deal.setCreateNum(obj.getCreateNum());
            deal.setCreateName(obj.getCreateName());
            deal.setCreateTime(obj.getCreateTime());
            deal.setModifyNum(tokenUser.getJobNumber());
            deal.setModifyName(tokenUser.getUserName());
            deal.setModifyTime(DateTime.now().toJdkDate());

            //region 2023-10-24  修复修改清除submit time,audit time wsj
            deal.setRealEndDate(obj.getRealEndDate());
            deal.setRealStartDate(obj.getRealStartDate());
            deal.setModifyRemark(obj.getModifyRemark());
            deal.setModifyFailureRemark(obj.getModifyFailureRemark());
            deal.setCancelRemark(obj.getCancelRemark());
            deal.setCacenlFailureRemark(obj.getCacenlFailureRemark());
            deal.setAuditFailureRemark(obj.getAuditFailureRemark());
            deal.setSubmitTime(obj.getSubmitTime());
            deal.setAuditTime(obj.getAuditTime());
            //endregion

            somFreeAddDealItemsMapper.createLambdaQuery().andEq("deal_id", addOrEditVo.getAid()).delete();
            if (CollectionUtil.isNotEmpty(addOrEditVo.getItemsVoList())) {
//                List<McProductSales> productSales = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery().andEq("site", addOrEditVo.getSite())
//                        .andIn("display_product_code", addOrEditVo.getItemsVoList().stream().map(m -> m.getSellerSku()).collect(Collectors.toList())).select();
//                List<McStockInfoExtVo> stockInfoList = stockInfoMapper.queryBySitesAndSkus(Collections.singletonList(addOrEditVo.getSite()), productSales.stream().map(m -> m.getProductMainCode()).collect(Collectors.toList()));
                //新计算库存可售天数
                MarketActivityUtil.MarketMsgData msgData = calcDays(addOrEditVo, dealType);
                if (!msgData.isSuccess()) {
                    String errorMsg = msgData.getErrorMap().values().stream().collect(Collectors.joining(","));
                    throw new ValidateException(errorMsg);
                } else {
                    for (SomFreeAddDealItemsVo itemsVo : addOrEditVo.getItemsVoList()) {
                        // 数值过大 强制转换
                        if (itemsVo.getExpectedGrossProfitMargin() != null
                                && itemsVo.getExpectedGrossProfitMargin().toPlainString().replace("-", "").replace(".", "").length() > 8) {
                            itemsVo.setExpectedGrossProfitMargin(BigDecimal.valueOf(-9999));
                        }
                        itemsVo.setAid(IdUtil.fastSimpleUUID());
                        itemsVo.setDealId(addOrEditVo.getAid());
                        itemsVo.setStockSaleDays((Integer) msgData.getDaysMap().get(itemsVo.getSellerSku()));
                        itemsVo.setCompletionRate((BigDecimal) msgData.getDaysMap().get(itemsVo.getSellerSku() + "_completionRate"));
                        /*
                        //计算库存可销售天数
                        McProductSales productSale = productSales.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getSite(), addOrEditVo.getSite()) &&
                                StrUtil.equalsIgnoreCase(f.getDisplayProductCode(), itemsVo.getSellerSku())).findFirst().orElse(null);

                        if (ObjectUtil.isNotEmpty(productSale) && StrUtil.isNotEmpty(productSale.getProductMainCode())) {
                        List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(addOrEditVo.getSite(), f.getSite()) &&
                                StrUtil.equalsIgnoreCase(productSale.getProductMainCode(), f.getProductMainCode())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(stockInfos)) {
                            long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                            BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                                itemsVo.setStockSaleDays(0);
                            } else {
                                // 库存可销售天数 库存/7天发货平均值 只取整数
                                int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                                itemsVo.setStockSaleDays(stockSaleDays);
                            }
                        } else {
                            itemsVo.setStockSaleDays(0);
                        }
                    } else {
                        itemsVo.setStockSaleDays(0);
                    }*/
                        // 确定审批角色
                        itemsVo.setSite(addOrEditVo.getSite());
                        itemsVo.setApprovalRole(initApprovalRole(itemsVo));
                    }
                }
                List<SomFreeAddDealItems> itemsList = ConvertUtils.listConvert(addOrEditVo.getItemsVoList(), SomFreeAddDealItems.class);
                somFreeAddDealItemsMapper.insertBatch(itemsList);

                deal.setApprovalRole(initApprovalRoleGroup(itemsList));
                somFreeAddDealMapper.updateById(deal);
            }
        }
    }

    /**
     * 确定审批角色
     * @param vo
     * @return String
     */
    private String initApprovalRole(SomFreeAddDealItemsVo vo) {
        if (vo == null || vo.getSite() == null || vo.getDealPriceGross() == null || vo.getCategoryGross() == null) {
            return "无需审批";
            //throw new IllegalArgumentException("确定审批角色：所需参数不能为null");
        }

        String approvalRole = null;
        String site = vo.getSite();
        // 秒杀价毛利率
        BigDecimal dealPriceGross = vo.getDealPriceGross().setScale(2, RoundingMode.HALF_UP);
        // 三级分类近四周毛利率
        BigDecimal categoryGross = vo.getCategoryGross().setScale(2, RoundingMode.HALF_UP);

        if (Arrays.asList("Amazon.com").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-10)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-20)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.de", "Amazon.fr", "Amazon.co.uk", "Amazon.it", "Amazon.es", "Amazon.pl", "Amazon.ie", "Amazon.nl", "Amazon.se", "Amazon.com.be", "Amazon.tr").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-15)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-30)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.ca", "Amazon.com.mx", "Amazon.com.mx.new").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.ZERO) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(5)) <= 0) {
                approvalRole = "组长层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-15)) <= 0 && categoryGross.compareTo(BigDecimal.ZERO) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-40)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-10)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }
        if (Arrays.asList("Amazon.co.jp", "Amazon.com.au").contains(site)) {
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-5)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-5)) <= 0) {
                approvalRole = "CS层级";
            }
            if (dealPriceGross.compareTo(BigDecimal.valueOf(-30)) <= 0 && categoryGross.compareTo(BigDecimal.valueOf(-20)) <= 0) {
                approvalRole = "区域负责人层级";
            }
        }

        if (approvalRole == null) {
            approvalRole = "无需审批";
        }

        return approvalRole;
    }

    /**
     * 确定审批角色 多列 多组
     * @param list
     * @return String
     */
    private String initApprovalRoleGroup(List<SomFreeAddDealItems> list) {
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("区域负责人层级", 1);
        priorityMap.put("CS层级", 2);
        priorityMap.put("组长层级", 3);

        String highestPriorityRole = "无需审批";
        int highestPriority = Integer.MAX_VALUE;

        for (SomFreeAddDealItems item : list) {
            String approvalRole = item.getApprovalRole();
            if (approvalRole != null && priorityMap.containsKey(approvalRole)) {
                int priority = priorityMap.get(approvalRole);
                if (priority < highestPriority) {
                    highestPriority = priority;
                    highestPriorityRole = approvalRole;
                }
            }
        }

        return highestPriorityRole;
    }

    private MarketActivityUtil.MarketMsgData calcDays(SomFreeAddDealExtVo deal, String type) {
        List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (SomFreeAddDealItemsExtVo item : deal.getItemsVoList()) {
            MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
            activity.setSite(deal.getSite());
            activity.setSellerSku(item.getSellerSku());
            activity.setStartDate(sdf.format(deal.getPlanStartDate()));
            activity.setEndDate(sdf.format(deal.getPlanEndDate()));
            activity.setType(type);
            body.add(activity);
        }
        MarketActivityUtil activityUtil = new MarketActivityUtil();
        MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, true);
        return daysMsgData;
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomFreeAddDealVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomFreeAddDealExtVo> queryByPage(SomFreeAddDealPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        List<SomFreeAddDealExtVo> list1 = dynamicSqlManager.getMapper(SomFreeAddDealMapper.class).querytotalCount(searchVo);

        DefaultPageResult<SomFreeAddDealExtVo> pageResult = dynamicSqlManager.getMapper(SomFreeAddDealMapper.class).queryByPage(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<String> list = Arrays.asList("LDDealType", "LDModifySatus", "LDStatus");
            List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();

            List<McDictionaryInfo> campaignTypeInfos = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "CampaignType").select();

            List<String> aidList = pageResult.getList().stream().map(m -> m.getAid()).collect(Collectors.toList());
            List<SomFreeAddDealItemsExtVo> itemsList = dynamicSqlManager.getMapper(SomFreeAddDealItemsMapper.class).queryItemsVoList(aidList);
            pageResult.getList().forEach(t -> {
                mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase("LDDealType", f.getItemTypeCode()) && StrUtil.equalsIgnoreCase(String.valueOf(t.getDealType()), f.getItemValue())).findFirst().ifPresent(ps -> {
                    t.setDealTypeName(ps.getItemLable());
                });
                mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase("LDModifySatus", f.getItemTypeCode()) && StrUtil.equalsIgnoreCase(String.valueOf(t.getModifyStatus()), f.getItemValue())).findFirst().ifPresent(ps -> {
                    t.setModifyStatusName(ps.getItemLable());
                });
                mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase("LDStatus", f.getItemTypeCode()) && StrUtil.equalsIgnoreCase(String.valueOf(t.getStatus()), f.getItemValue())).findFirst().ifPresent(ps -> {
                    t.setStatusName(ps.getItemLable());
                });
                if (ObjectUtil.isNotEmpty(t.getCampaignType())) {
                    campaignTypeInfos.stream().filter(f -> StrUtil.equalsIgnoreCase(String.valueOf(t.getCampaignType()), f.getItemValue())).findFirst().ifPresent(ps -> {
                        t.setCampaignTypeName(ps.getItemLable());
                    });
                }
                List<SomFreeAddDealItemsExtVo> itemsVoList = itemsList.stream().filter(f -> StrUtil.equals(t.getAid(), f.getDealId())).collect(Collectors.toList());
                t.setItemsVoList(ConvertUtils.listConvert(itemsVoList, SomFreeAddDealItemsExtVo.class));
                // 填充错误信息，如果状态是需要关注，需要填充子项的错误信息
                Integer needAttentionStatus = 41;
                if (needAttentionStatus.equals(t.getStatus())) {
                    List<String> errorMsg = t.getItemsVoList().stream()
                            .filter(data -> StrUtil.isNotBlank(data.getErrorMsg()))
                            .map(data -> StrUtil.concat(true, data.getSellerSku(), ":", data.getErrorMsg()))
                            .collect(Collectors.toList());
                    t.setErrorMsg(String.join("\n", errorMsg));
                }
            });
            pageResult.setTotalRow(list1.size());
            if (list1.size() == 0L) {
                pageResult.setTotalPage(1L);
            } else if (list1.size() % (long) pageRequest.getPageSize() == 0L) {
                pageResult.setTotalPage(list1.size() / (long) pageRequest.getPageSize());
            } else {
                pageResult.setTotalPage(list1.size() / (long) pageRequest.getPageSize() + 1L);
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomFreeAddDealExtVo.class, searchVo);
    }

    /**
     * querybyAid
     * 查看
     *
     * @param somFreeAddDealVo
     * @return {@link com.zielsmart.mc.vo.SomFreeAddDealExtVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomFreeAddDealExtVo querybyAid(SomFreeAddDealVo somFreeAddDealVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeAddDealVo) || StrUtil.isBlank(somFreeAddDealVo.getAid())) {
            throw new ValidateException("请选择要查看的数据");
        }
        SomFreeAddDeal deal = dynamicSqlManager.getMapper(SomFreeAddDealMapper.class).createLambdaQuery().andEq("aid", somFreeAddDealVo.getAid()).single();
        SomFreeAddDealExtVo vo = ConvertUtils.beanConvert(deal, SomFreeAddDealExtVo.class);
        if (ObjectUtil.isNotEmpty(vo)) {
            List<String> list = Arrays.asList("LDDealType", "PDDApplyReason","CampaignType");
            List<McDictionaryInfo> mcDictionaryInfos = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", list).select();
            mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase("LDDealType", f.getItemTypeCode()) && StrUtil.equalsIgnoreCase(String.valueOf(vo.getDealType()), f.getItemValue())).findFirst().ifPresent(ps -> {
                vo.setDealTypeName(ps.getItemLable());
            });
            mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase("PDDApplyReason", f.getItemTypeCode()) && StrUtil.equalsIgnoreCase(String.valueOf(vo.getApplyReason()), f.getItemValue())).findFirst().ifPresent(ps -> {
                vo.setApplyReasonName(ps.getItemLable());
            });
            if (ObjectUtil.isNotEmpty(vo.getCampaignType())) {
                mcDictionaryInfos.stream().filter(f -> StrUtil.equalsIgnoreCase(String.valueOf(vo.getCampaignType()), f.getItemValue())).findFirst().ifPresent(ps -> {
                    vo.setCampaignTypeName(ps.getItemLable());
                });
            }
            List<SomFreeAddDealItemsExtVo> itemsList = dynamicSqlManager.getMapper(SomFreeAddDealItemsMapper.class).queryItemsVoList(Collections.singletonList(vo.getAid()));
            List<SomFreeAddDealItemsExtVo> itemsVoList = itemsList.stream().filter(f -> StrUtil.equals(vo.getAid(), f.getDealId())).collect(Collectors.toList());
            for (SomFreeAddDealItemsExtVo item : itemsVoList) {
                nullToZero(item);
            }
            vo.setItemsVoList(ConvertUtils.listConvert(itemsVoList, SomFreeAddDealItemsExtVo.class));
        }
        return vo;
    }

    /**
     * 兼容历史数据
     * null转0
     */
    private void nullToZero(SomFreeAddDealItemsExtVo vo) {
        vo.setDealPriceGross(Optional.ofNullable(vo.getDealPriceGross()).orElse(BigDecimal.ZERO));
        vo.setDealBurstCoefficient(Optional.ofNullable(vo.getDealBurstCoefficient()).orElse(BigDecimal.ZERO));
        vo.setDmsLast30day(Optional.ofNullable(vo.getDmsLast30day()).orElse(BigDecimal.ZERO));
        vo.setCategoryGross(Optional.ofNullable(vo.getCategoryGross()).orElse(BigDecimal.ZERO));
        vo.setExpectedSalesVolume(Optional.ofNullable(vo.getExpectedSalesVolume()).orElse(BigDecimal.ZERO));
        vo.setEstimateOfSales(Optional.ofNullable(vo.getEstimateOfSales()).orElse(BigDecimal.ZERO));
        vo.setExpectedGrossProfitMargin(Optional.ofNullable(vo.getExpectedGrossProfitMargin()).orElse(BigDecimal.ZERO));
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomFreeAddDealVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || CollUtil.isEmpty(deleteVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }

        long count = somFreeAddDealMapper.createLambdaQuery().andIn("aid", deleteVo.getAidList()).andNotEq("status", 10).count();
        if (count > 0) {
            throw new ValidateException("当前数据不允许删除");
        }

        somFreeAddDealMapper.createLambdaQuery().andIn("aid", deleteVo.getAidList()).delete();
    }

    /**
     * submit
     * 提交
     *
     * @param submitVo
     * @param tokenUserInfo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void submit(SomFreeAddDealVo submitVo, TokenUserInfo tokenUserInfo) throws ValidateException, JsonProcessingException {
        if (ObjectUtil.isEmpty(submitVo) || StrUtil.isBlank(submitVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", submitVo.getAid()).andEq("status", 10).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许提交");
        }
        List<SomFreeAddDealItems> dealItemList = somFreeAddDealItemsMapper.createLambdaQuery().andEq("deal_id", submitVo.getAid()).select();

        // 是否发起审批
        if (StrUtil.isNotEmpty(deal.getApprovalRole()) && !deal.getApprovalRole().equals("无需审批")) {
            // 组织流程信息
            ZBPMDealVo zbpmSubmitVo = ConvertUtils.beanConvert(deal, ZBPMDealVo.class);
            if (ObjectUtil.isNotEmpty(zbpmSubmitVo)) {
                // 提报国家
                McPlatformPropertiesVo mcPlatformPropertiesVo = new McPlatformPropertiesVo();
                mcPlatformPropertiesVo.setPlatform(deal.getPlatform());
                mcPlatformPropertiesVo.setSite(deal.getSite());
                McPlatformPropertiesVo mcPlatformPropertiesVoRes = platformPropertiesService.getPlatformProperties(mcPlatformPropertiesVo);
                zbpmSubmitVo.setCountry(mcPlatformPropertiesVoRes.getCountryName());
                // 申请原因转义
                List<String> list = Arrays.asList("LDDealType", "PDDApplyReason");
                List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", list).select();
                if (ObjectUtil.isNotEmpty(dictionaryInfoList)) {
                    dictionaryInfoList.stream().filter(f -> StrUtil.equals("LDDealType", f.getItemTypeCode()) && StrUtil.equals(deal.getDealType().toString(), f.getItemValue())).findFirst().ifPresent(ps -> {
                        zbpmSubmitVo.setDealTypeName(ps.getItemLable()); // 兼容旧版本
                        zbpmSubmitVo.setDealType(ps.getItemLable()); // 活动类型
                    });
                    dictionaryInfoList.stream().filter(f -> StrUtil.equals("PDDApplyReason", f.getItemTypeCode()) && StrUtil.equals(deal.getApplyReason().toString(), f.getItemValue())).findFirst().ifPresent(ps -> {
                        zbpmSubmitVo.setApplyReasonName(ps.getItemLable()); // 兼容旧版本
                        zbpmSubmitVo.setDealType(ps.getItemLable()); // 活动类型
                    });
                    if (ObjectUtil.equal(99, deal.getApplyReason())) {
                        zbpmSubmitVo.setApplyReasonName(zbpmSubmitVo.getApplyReasonName() + ";" + deal.getCutomerReason());
                    }
                    zbpmSubmitVo.setAidList(Collections.singletonList(deal.getAid()));
                }
                if (CollectionUtil.isNotEmpty(dealItemList)) {
                    List<ZBPMDealDetailVo> dealDetailList = ConvertUtils.listConvert(dealItemList, ZBPMDealDetailVo.class);
                    // 部门
                    List<SysDeptNeweya> deptList = deptMapper.createLambdaQuery().andEq("is_enabled", 1).select();
                    // 员工
                    List<McUser> userList = dynamicSqlManager.getMapper(McUserMapper.class).all();
                    // 查询币种
                    List<String> sellerSkus = dealItemList.stream().map(m -> m.getSellerSku()).collect(Collectors.toList());
                    List<McListingInfoAmazon> amazonList = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andEq("site", deal.getSite()).andIn("seller_sku", sellerSkus).select();
                    // 查询毛利率
                    List<SomKpiGrossProfit> profitList = dynamicSqlManager.getMapper(SomKpiGrossProfitMapper.class).createLambdaQuery().andEq("site", deal.getSite()).andIn("seller_sku", sellerSkus).select();
                    // 查询产品销售视图
                    List<McProductSales> productSalesList = productSalesMapper.createLambdaQuery().andEq("site", deal.getSite()).andIn("display_product_code", sellerSkus).select();
                    dealDetailList.forEach(f -> {
                        McProductSales productSales = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(deal.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
                        if (ObjectUtil.isNotEmpty(productSales)) {
                            deptList.stream().filter(d -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupCode(), d.getDeptCode())).findFirst().ifPresent(ps -> {
                                f.setSalesGroupName(ps.getDeptNameCn());
                            });
                            userList.stream().filter(u -> StrUtil.equals(u.getJobNumber(), productSales.getSalesGroupEmptCode())).findFirst().ifPresent(ps -> {
                                f.setSalesGroupEmptName(ps.getUserName());
                            });
                            userList.stream().filter(u -> StrUtil.equals(u.getJobNumber(), productSales.getOperationEmptCode())).findFirst().ifPresent(ps -> {
                                f.setOperationEmptName(ps.getUserName());
                            });
                        }
                        amazonList.stream().filter(s -> StrUtil.equals(s.getSellerSku(), f.getSellerSku())).findFirst().ifPresent(ps -> {
                            f.setCurrencySymbol(ps.getCurrencyCode());
                        });
                        profitList.stream().filter(p -> StrUtil.equalsIgnoreCase(deal.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().ifPresent(ps -> {
                            f.setOneMonthGrossProfitRate(ps.getOneMonthGrossProfitRate());
                        });
                    });
                    zbpmSubmitVo.setDealDetailList(dealDetailList);
                    // 发起流程
                    if (StrUtil.isNotEmpty(zbpmSubmitVo.getApprovalRole()) && !zbpmSubmitVo.getApprovalRole().equals("无需审批")) {
                        submitProcess(zbpmSubmitVo, tokenUserInfo);
                    }
                    deal.setStatus(20);
                    deal.setSubmitTime(DateTime.now().toJdkDate());
                    somFreeAddDealMapper.updateById(deal);
                } else {
                    throw new ValidateException("Deal活动明细信息为空");
                }
            } else {
                throw new ValidateException("Deal活动信息为空");
            }
        } else {
            deal.setStatus(21);
            deal.setSubmitTime(DateTime.now().toJdkDate());
            somFreeAddDealMapper.updateById(deal);
        }
    }

    private String nullToString(Object obj) {
        if (ObjectUtil.isNull(obj)) {
            return "";
        }
        return obj.toString();
    }

    private String beanToOaJson(ZBPMDealVo dealVo) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建要生成的JSON对象
        ObjectNode fdListObject = objectMapper.createObjectNode();
        ArrayNode idArray = objectMapper.createArrayNode();
        ArrayNode sellerSkuArray = objectMapper.createArrayNode();
        ArrayNode priceArray = objectMapper.createArrayNode();
        ArrayNode grossMarginArray = objectMapper.createArrayNode();
        ArrayNode activityGrossMarginArray = objectMapper.createArrayNode();
        ArrayNode stockSaleDaysArray = objectMapper.createArrayNode();
        ArrayNode saleYieldRateArray = objectMapper.createArrayNode();
        ArrayNode dealPriceArray = objectMapper.createArrayNode();
        ArrayNode dealDiscountArray = objectMapper.createArrayNode();
        ArrayNode couponDiscountArray = objectMapper.createArrayNode();
        ArrayNode promotionDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalAmountArray = objectMapper.createArrayNode();
        ArrayNode transactionPriceArray = objectMapper.createArrayNode();
        ArrayNode scoreArray = objectMapper.createArrayNode();
        ArrayNode dealQuantity = objectMapper.createArrayNode();
        ArrayNode salesGroupNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptCodeArray = objectMapper.createArrayNode();
        ArrayNode operationEmptNameArray = objectMapper.createArrayNode();
        ArrayNode operationEmpCodeArray = objectMapper.createArrayNode();
        ArrayNode dealBurstCoefficientArray = objectMapper.createArrayNode();
        ArrayNode estimateOfSalesArray = objectMapper.createArrayNode();
        ArrayNode expectedGrossProfitMarginArray = objectMapper.createArrayNode();
        ArrayNode categoryGrossArray = objectMapper.createArrayNode();
        ArrayNode approvalRoleArray = objectMapper.createArrayNode();
        for (String id : dealVo.getAidList()) {
            idArray.add(id);
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        fdListObject.set("fd_dealTypeName", new TextNode(nullToString(dealVo.getDealTypeName())));
        fdListObject.set("fd_country", new TextNode(nullToString(dealVo.getCountry())));
        fdListObject.set("fd_deal_type", new TextNode(nullToString(dealVo.getDealType())));
        fdListObject.set("fd_approvalRole", new TextNode(nullToString(dealVo.getApprovalRole())));

        fdListObject.set("fd_ld7dd_site", new TextNode(nullToString(dealVo.getSite())));
        fdListObject.set("fd_ld7dd_currencyCode", new TextNode(nullToString(dealVo.getCurrencyCode())));
        fdListObject.set("fd_ld7dd_internalDescription", new TextNode(nullToString(dealVo.getInternalDescription())));
        fdListObject.set("fd_ld7dd_variationsFlag", new TextNode((dealVo.getVariationsFlag() == null || dealVo.getVariationsFlag() == 0) ? "否" : "是"));
        fdListObject.set("fd_ld7dd_startTime", new TextNode(sf.format(dealVo.getPlanStartDate())));
        fdListObject.set("fd_ld7dd_endTime", new TextNode(sf.format(dealVo.getPlanEndDate())));
        fdListObject.set("fd_ld7dd_primeDayFlag", new TextNode((dealVo.getCampaignType() == null || dealVo.getCampaignType() != 1)? "否" : "是"));
        fdListObject.set("fd_ld7dd_reason", new TextNode(nullToString(dealVo.getApplyReasonName())));
        List<ZBPMDealDetailVo> itemList = dealVo.getDealDetailList().stream().sorted(Comparator.comparing(ZBPMDealDetailVo::getTotalDiscount).reversed()).collect(Collectors.toList());
        for (ZBPMDealDetailVo detail : itemList) {
            sellerSkuArray.add(nullToString(detail.getSellerSku()));
            priceArray.add(detail.getPrice() == null ? "" : detail.getCurrencySymbol() + detail.getPrice().toString());
            grossMarginArray.add(nullToString(detail.getOneMonthGrossProfitRate()));
            activityGrossMarginArray.add(nullToString(detail.getDealPriceGross()));
            stockSaleDaysArray.add(nullToString(detail.getStockSaleDays()));
            saleYieldRateArray.add(nullToString(detail.getCompletionRate()));

            dealPriceArray.add(null == detail.getDealPrice() ? "" : detail.getCurrencySymbol() + detail.getDealPrice());
            dealDiscountArray.add(nullToString(detail.getDealDiscount()));
            couponDiscountArray.add(nullToString(detail.getCouponDiscount()));
            promotionDiscountArray.add(nullToString(detail.getPromotionDiscount()));
            totalDiscountArray.add(nullToString(detail.getTotalDiscount()));
            totalAmountArray.add(null == detail.getTotalDiscountAmount() ? "" : detail.getCurrencySymbol() + detail.getTotalDiscountAmount());
            transactionPriceArray.add(null == detail.getTransactionPrice() ? "" : detail.getCurrencySymbol() + detail.getTransactionPrice());
            scoreArray.add(nullToString(detail.getScore()));
            dealQuantity.add(nullToString(detail.getDealQuantity()));
            salesGroupNameArray.add(nullToString(detail.getSalesGroupName()));
            salesGroupEmptNameArray.add(nullToString(detail.getSalesGroupEmptName()));
            salesGroupEmptCodeArray.add(nullToString(detail.getBusinessLeaderCode()));
            operationEmptNameArray.add(nullToString(detail.getOperationEmptName()));
            operationEmpCodeArray.add(nullToString(detail.getBusinessOperationCode()));
            dealBurstCoefficientArray.add(nullToString(detail.getDealBurstCoefficient()));
            estimateOfSalesArray.add(nullToString(detail.getEstimateOfSales()));
            expectedGrossProfitMarginArray.add(nullToString(detail.getExpectedGrossProfitMargin()));
            categoryGrossArray.add(nullToString(detail.getCategoryGross()));
            approvalRoleArray.add(nullToString(detail.getApprovalRole()));
        }
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_id", idArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_sellerSku", sellerSkuArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_price", priceArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_grossMargin", grossMarginArray);
        //添加秒杀价毛利率
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_activity_grossMargin", activityGrossMarginArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_stockSaleDays", stockSaleDaysArray);
        //添加 近四周预测达成率
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_saleYieldRate", saleYieldRateArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_dealPrice", dealPriceArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_dealDiscount", dealDiscountArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_couponDiscount", couponDiscountArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_promotionDiscount", promotionDiscountArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_totalDiscount", totalDiscountArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_totalAmount", totalAmountArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_transactionPrice", transactionPriceArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_score", scoreArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_dealQuantity", dealQuantity);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_salesGroupName", salesGroupNameArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_salesGroupEmptName", salesGroupEmptNameArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_salesGroupEmptCode", salesGroupEmptCodeArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_operationEmptName", operationEmptNameArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_operationEmpCode", operationEmpCodeArray);

        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_eventsEruptionSeveral", dealBurstCoefficientArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_eventsSellGross", estimateOfSalesArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_eventsGross", expectedGrossProfitMarginArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_categoryGross", categoryGrossArray);
        fdListObject.set("fd_ld_7dd_list.fd_ld7dd_approvalRole", approvalRoleArray);
        // 将生成的JSON对象转换为字符串
        String json = objectMapper.writeValueAsString(fdListObject);
        return json;
    }

    private void submitProcess(ZBPMDealVo dealVo, TokenUserInfo tokenUserInfo) throws ValidateException, JsonProcessingException {
        McDictionaryInfo couponInfo = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "OATemplateId")
                .andEq("item_lable", "Deal")
                .single();
        String formValue = beanToOaJson(dealVo);
        MultiValueMap<String, Object> wholeForm = new LinkedMultiValueMap<>();
        //文档标题
        wholeForm.add("docSubject", "亚马逊Deals活动申请");
        //流程发起人
        wholeForm.add("docCreator", String.format("{\"LoginName\": \"%s\"}", tokenUserInfo.getJobNumber()));
        //文档状态，可以为草稿（"10"）或者待审（"20"）两种状态，默认为待审
        wholeForm.add("docStatus", "20");
        //文档模板id，不允许为空
        wholeForm.add("fdTemplateId", couponInfo.getItemValue());
        String formValues = null;
        //流程表单数据，允许为空
        wholeForm.add("formValues", formValue);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(wholeForm, headers);
        //有返回值的情况 VO可以替换成具体的JavaBean
        ResponseEntity<String> obj = null;
        try {
            log.info("OA新增流程传参,url:{},param:{}", oaUrl, JSONUtil.toJsonStr(entity));
            obj = restTemplate.exchange(oaUrl + "/api/km-review/kmReviewRestService/addReview", HttpMethod.POST, entity, String.class);
        } catch (RestClientException e) {
            log.error("调用OA失败：" + e.getMessage());
            if (StrUtil.isBlank(e.getMessage())) {
                throw new ValidateException(e.getCause().getMessage());
            } else {
                throw new ValidateException(e.getMessage());
            }
        }
        String body = obj.getBody();
        log.info("返回信息：", body);
        if (obj.getStatusCode() != HttpStatus.OK) {
            throw new ValidateException(body);
        }
    }

    /**
     * validateTime
     * 校验时间
     *
     * @param cancelVo
     * @return {@link java.lang.Boolean}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public Boolean validateTime(SomFreeAddDealVo cancelVo) throws ValidateException {
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", cancelVo.getAid()).andEq("status", 40).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许取消");
        }
        Date beginDate = DateUtil.offsetHour(deal.getPlanStartDate(), 25).toJdkDate();
        Date nowDate = DateTime.now().toJdkDate();
        if (nowDate.after(beginDate)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * cancel
     *
     * @param cancelVo
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void cancel(SomFreeAddDealVo cancelVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(cancelVo) || StrUtil.isBlank(cancelVo.getAid()) || StrUtil.isBlank(cancelVo.getCancelRemark())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.single(cancelVo.getAid());
        //未开始  需要关注  进行中  运营已提报
        if (ObjectUtil.isEmpty(deal.getStatus()) || !(40 == deal.getStatus() || 41 == deal.getStatus() || 70 == deal.getStatus() || 110 == deal.getStatus())) {
            throw new ValidateException("当前状态不允许取消");
        }
        deal.setStatus(50);
        deal.setCancelRemark(cancelVo.getCancelRemark());
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.updateById(deal);
    }

    /**
     * feedbackSubmitResult
     * 反馈提报结果
     *
     * @param feedbackVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void feedbackSubmitResult(SomFreeAddDealVo feedbackVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.single(feedbackVo.getAid());
        if (ObjectUtil.isEmpty(deal) || !(21 == deal.getStatus() || 110 == deal.getStatus() || 41 == deal.getStatus())) {
            throw new ValidateException("当前状态不允许反馈提报结果");
        }
        if (ObjectUtil.equal(30, feedbackVo.getStatus())) {
            if ((ObjectUtil.isEmpty(feedbackVo.getRealStartDate()) || ObjectUtil.isEmpty(feedbackVo.getRealEndDate()))) {
                throw new ValidateException("提报成功时,活动实际起止时间不允许为空");
            } else {
                deal.setStatus(40);
                deal.setRealStartDate(feedbackVo.getRealStartDate());
                deal.setRealEndDate(feedbackVo.getRealEndDate());
                if (feedbackVo.isBothModify()) {
                    deal.setPlanStartDate(feedbackVo.getRealStartDate());
                    deal.setPlanEndDate(feedbackVo.getRealEndDate());
                }
            }
        } else if (ObjectUtil.equal(39, feedbackVo.getStatus())) {
            if (StrUtil.isBlank(feedbackVo.getCancelRemark())) {
                throw new ValidateException("提报失败时,失败原因不允许为空");
            } else {
                deal.setStatus(feedbackVo.getStatus());
                deal.setCancelRemark(feedbackVo.getCancelRemark());
            }
        }

        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.updateById(deal);
        if (ObjectUtil.equal(39, feedbackVo.getStatus())) {
            try {
                List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "LDDealType").select();
                McDictionaryInfo info = infoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemValue(), deal.getDealType().toString())).findFirst().orElse(null);

                List<AmazonActivityReminderEvent.Reminder> list = new ArrayList<>();

                List<SomFreeAddDealItems> itemList = somFreeAddDealItemsMapper.createLambdaQuery().andEq("deal_id", deal.getAid()).select();
                for (SomFreeAddDealItems item : itemList) {
                    AmazonActivityReminderEvent.Reminder reminder = new AmazonActivityReminderEvent.Reminder(deal.getSite(), item.getSellerSku(), deal.getInternalDescription(),
                            info.getItemLable(), deal.getPlanStartDate(), deal.getPlanEndDate(), "LD&7DD", feedbackVo.getCancelRemark(), deal.getCreateNum());
                    list.add(reminder);
                }
                AmazonActivityReminderEvent event = new AmazonActivityReminderEvent(list);
                eventBusTemplate.publish(event);
            } catch (Exception e) {
                throw new ValidateException("放入消息队列出错" + e.getMessage());
            }
        }
    }

    /**
     * feedbackModifyResult
     * 反馈修改结果
     *
     * @param feedbackVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void feedbackModifyResult(SomFreeAddDealVo feedbackVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getModifyStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", feedbackVo.getAid()).andEq("modify_status", 10).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许反馈修改结果");
        }
        if (ObjectUtil.equal(30, feedbackVo.getModifyStatus()) && StrUtil.isBlank(feedbackVo.getModifyFailureRemark())) {
            throw new ValidateException("修改失败时,失败原因不能为空");
        } else {
            deal.setModifyFailureRemark(feedbackVo.getModifyFailureRemark());
        }
        // 状态为需要关注且修改状态为修改成功时
        if ((ObjectUtil.equal(41, deal.getStatus())||ObjectUtil.equal(120, deal.getStatus())) && ObjectUtil.equal(20, feedbackVo.getModifyStatus())) {
            DateTime now = DateTime.now();
            if (ObjectUtil.isNotEmpty(deal.getRealStartDate()) && ObjectUtil.isNotEmpty(deal.getRealEndDate())) {
                if (now.after(deal.getRealStartDate()) && now.before(deal.getRealEndDate())) {
                    //进行中         70
                    deal.setStatus(70);
                } else {
                    //未开始         40
                    //运营已提报          110
                    deal.setStatus(110);
                }
            } else {
                if (ObjectUtil.isNotEmpty(deal.getPlanStartDate()) && ObjectUtil.isNotEmpty(deal.getPlanEndDate())) {
                    if (now.after(deal.getPlanStartDate()) && now.before(deal.getPlanEndDate())) {
                        deal.setStatus(70);
                    } else {
                        deal.setStatus(110);
                    }
                }
            }
        }
        deal.setModifyStatus(feedbackVo.getModifyStatus());
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.updateById(deal);
    }

    /**
     * feedbackCancelResult
     * 反馈取消结果
     *
     * @param feedbackVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void feedbackCancelResult(SomFreeAddDealVo feedbackVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", feedbackVo.getAid()).andEq("status", 50).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前状态不允许反馈取消结果");
        }
        if (ObjectUtil.equal(59, feedbackVo.getModifyStatus()) && StrUtil.isBlank(feedbackVo.getCacenlFailureRemark())) {
            throw new ValidateException("取消失败时,失败原因不能为空");
        } else {
            deal.setCacenlFailureRemark(feedbackVo.getCacenlFailureRemark());
        }
        deal.setStatus(feedbackVo.getStatus());
        deal.setRealStartDate(feedbackVo.getRealStartDate());
        deal.setRealEndDate(feedbackVo.getRealEndDate());
        if (feedbackVo.isBothModify()) {
            deal.setPlanStartDate(deal.getRealStartDate());
            deal.setPlanEndDate(deal.getRealEndDate());
        }
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.updateById(deal);
    }

    /**
     * modifyPlanTime
     * 修改活动时间
     *
     * @param modifyVo
     * @param tokenUser
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void modifyPlanTime(SomFreeAddDealVo modifyVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(modifyVo) || StrUtil.isBlank(modifyVo.getAid()) || ObjectUtil.isEmpty(modifyVo.getPlanStartDate()) || ObjectUtil.isEmpty(modifyVo.getPlanEndDate())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        /**
         * 大促类型
         * 1 = Prime Day Window
         * 2 = Prime Fall Deal Event
         * 3 = BFCM Window
         * 4 = Black Friday Window
         * 5 = Cyber Monday Window
         */
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", modifyVo.getAid()).andEq("campaign_type", 1).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("当前数据不允许修改活动时间");
        }
        deal.setPlanStartDate(modifyVo.getPlanStartDate());
        deal.setPlanEndDate(modifyVo.getPlanEndDate());
        deal.setModifyNum(tokenUser.getJobNumber());
        deal.setModifyName(tokenUser.getUserName());
        deal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.updateById(deal);
    }

    /**
     * cloneById
     * 克隆
     *
     * @param cloneVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void cloneById(SomFreeAddDealExtVo cloneVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(cloneVo) || StrUtil.isEmpty(cloneVo.getAid()) || StrUtil.isEmpty(cloneVo.getInternalDescription())
                || ObjectUtil.isEmpty(cloneVo.getPlanStartDate()) || ObjectUtil.isEmpty(cloneVo.getPlanEndDate())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        SomFreeAddDeal obj = somFreeAddDealMapper.createLambdaQuery().andEq("aid", cloneVo.getAid()).andEq("status", 29).single();
        if (ObjectUtil.isEmpty(obj)) {
            throw new ValidateException("当前数据不允许克隆");
        }
        long descriptionCount = somFreeAddDealMapper.createLambdaQuery().andEq("internal_description", cloneVo.getInternalDescription()).count();
        if (0 < descriptionCount) {
            throw new ValidateException("「Internal Description」不允许出现重复");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", cloneVo.getAid()).single();
        List<SomFreeAddDealItems> dealItemsList = somFreeAddDealItemsMapper.createLambdaQuery().andEq("deal_id", cloneVo.getAid()).select();
        List<String> asinList = dealItemsList.stream().map(m -> m.getAsin()).collect(Collectors.toList());
        List<String> sellerSkuList = dealItemsList.stream().map(m -> m.getAsin()).collect(Collectors.toList());
        Integer countDeal = somFreeAddDealMapper.checkRepeatDeal(Strings.EMPTY, deal.getSite(), asinList, cloneVo.getPlanStartDate(), cloneVo.getPlanEndDate());
        if (0 < countDeal) {
            throw new ValidateException("存在其他类型的Deal,请检查");
        }
        String dealType = "Lighting Deal";
        if (ObjectUtil.equal(20, cloneVo.getDealType())) {
            dealType = "7 Day Deal";
        }
        SomDealRuleConfigVo config = configMapper.queryBySiteDealType(deal.getSite(), dealType);
        if (ObjectUtil.isEmpty(config) || StrUtil.isEmpty(config.getRules())) {
            throw new ValidateException("当前站点下该Deal类型未配置秒杀规则,请前往秒杀规则配置功能进行配置");
        }
        dealService.checkDeal(Strings.EMPTY, deal.getSite(), sellerSkuList, asinList, cloneVo.getPlanStartDate(), config);
        String aid = IdUtil.fastSimpleUUID();
        List<SomFreeAddDealItems> insertList = new ArrayList<>();
        SomCouponSearchVo couponSearchVo = new SomCouponSearchVo();
        couponSearchVo.setPlatform(deal.getPlatform());
        couponSearchVo.setSite(deal.getSite());
        couponSearchVo.setSellerSkuList(dealItemsList.stream().map(m -> m.getSellerSku()).collect(Collectors.toList()));
        List<SomCouponItemsExtVo> couponItemsList = dynamicSqlManager.getMapper(SomCouponItemsMapper.class).queryDiscountList(couponSearchVo);
        List<McProductSales> productSales = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery().andEq("site", couponSearchVo.getSite())
                .andIn("display_product_code", couponSearchVo.getSellerSkuList()).select();
        List<McStockInfoExtVo> stockInfoList = stockInfoMapper.queryBySitesAndSkus(Collections.singletonList(couponSearchVo.getSite()), productSales.stream().map(m -> m.getProductMainCode()).collect(Collectors.toList()));

        // 获取利润计算值Bi 补充所需字段 复用代码
        List<String> displayProductCodes = dealItemsList.stream().map(SomFreeAddDealItems::getSellerSku).collect(Collectors.toList());
        List<McProductSales> mcProductSalesList = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery()
                .andIn("display_product_code", displayProductCodes)
                .andEq("site", deal.getSite())
                .andEq("is_enabled", 1)
                .select();
        Map<String, McProductSales> mcProductSalesMap = mcProductSalesList.stream().collect(Collectors.toMap(
                x -> x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        // 字典值
        List<McDictionaryInfo> mcDictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery()
                .andEq("item_type_code", "LDDealType")
                .select();
        Map<String, String> mcDictionaryInfoMap = mcDictionaryInfoList.stream().collect(Collectors.toMap(
                McDictionaryInfo::getItemValue,
                McDictionaryInfo::getItemLable,
                (x1, x2) -> x1
        ));
        List<BiAmazonProfitCalculationVo> biBody = dealItemsList.stream().map(x -> {
            BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
            bi.setSite(deal.getSite());
            // 美国站点 需传开始结束时间
            if ("Amazon.com".equals(deal.getSite())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String date = sdf.format(cloneVo.getPlanStartDate());
                bi.setDate(date);
            }
            bi.setPrice(x.getDealPrice());//秒杀价
            bi.setDisplayProductCode(x.getSellerSku());
            bi.setProductMainCode(mcProductSalesMap.get(x.getSellerSku()).getProductMainCode());
            bi.setPromotionType(mcDictionaryInfoMap.get(deal.getDealType().toString()));
            return bi;
        }).collect(Collectors.toList());
        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                x -> x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x1
        ));

        for (SomFreeAddDealItems items : dealItemsList) {
            items.setAid(IdUtil.fastSimpleUUID());
            items.setDealId(aid);
            McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
            searchExVo.setSite(deal.getSite());
            searchExVo.setKeyWord(items.getSellerSku());
            searchExVo.setBeginDate(deal.getPlanStartDate());
            searchExVo.setEndDate(deal.getPlanEndDate());
            SomCouponItemsVo couponItemsExtVo = couponItemsList.stream().filter(f -> StrUtil.equals(items.getSellerSku(), f.getSellerSku())
                    && deal.getPlanStartDate().after(f.getBeginDate()) && deal.getPlanEndDate().before(f.getEndDate())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(couponItemsExtVo) && ObjectUtil.isNotEmpty(couponItemsExtVo.getCouponDiscount())) {
                items.setCouponDiscount(couponItemsExtVo.getCouponDiscount());
            } else {
                items.setCouponDiscount(BigDecimal.ZERO);
            }
            BigDecimal promotionDiscount = BigDecimal.ZERO;
            try {
                promotionDiscount = amazonService.queryPromotionDiscount(searchExVo);
            } catch (Exception e) {
                throw new ValidateException("获取promotion折扣信息出错" + e.getMessage());
            }
            items.setPromotionDiscount(promotionDiscount);
            // 总折扣
            items.setTotalDiscount(items.getCouponDiscount().add(items.getDealDiscount()).add(items.getPromotionDiscount()));
            // 总折扣金额
            items.setTotalDiscountAmount(items.getTotalDiscount().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).multiply(items.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
            // 预估成交价
            items.setTransactionPrice(items.getPrice().subtract(items.getTotalDiscountAmount()));
            // 获取sku用于计算库存可售天数
            McProductSales productSale = productSales.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getSite(), deal.getSite()) &&
                    StrUtil.equalsIgnoreCase(f.getDisplayProductCode(), items.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(productSale) && StrUtil.isNotEmpty(productSale.getProductMainCode())) {
                List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(deal.getSite(), f.getSite()) &&
                        StrUtil.equalsIgnoreCase(productSale.getProductMainCode(), f.getProductMainCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(stockInfos)) {
                    long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                    BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                        items.setStockSaleDays(0);
                    } else {
                        // 库存可销售天数 库存/7天发货平均值 只取整数
                        int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                        items.setStockSaleDays(stockSaleDays);
                    }
                } else {
                    items.setStockSaleDays(0);
                }
            } else {
                items.setStockSaleDays(0);
            }

            BigDecimal dealPriceGross = BigDecimal.ZERO; // 秒杀价毛利率
            BigDecimal dealBurstCoefficient = BigDecimal.ZERO; // 活动预计爆发系数
            BigDecimal dmsLast30day = BigDecimal.ZERO; // 近三十天DMS
            BigDecimal categoryGross = BigDecimal.ZERO; // 三级分类近四周毛利率
            String key = items.getSellerSku();
            if (biMap.containsKey(key)) {
                dealPriceGross = biMap.get(key).getGross();
                if (dealPriceGross != null && dealPriceGross.compareTo(BigDecimal.valueOf(-9999)) != 0) {
                    dealPriceGross = dealPriceGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                }
                dealBurstCoefficient = biMap.get(key).getDealBurstCoefficient();
                dealBurstCoefficient = dealBurstCoefficient != null ? dealBurstCoefficient.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                dmsLast30day = biMap.get(key).getDmsLast30day();
                dmsLast30day = dmsLast30day != null ? dmsLast30day.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                categoryGross = biMap.get(key).getCategoryGross();
                categoryGross = categoryGross != null ? categoryGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            }
            items.setDealPriceGross(dealPriceGross);
            items.setDealBurstCoefficient(dealBurstCoefficient);
            items.setDmsLast30day(dmsLast30day);
            items.setCategoryGross(categoryGross);
            // 三值计算 初始化原始值
            dealPriceGross = items.getDealPriceGross().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 秒杀价毛利率
            dmsLast30day = items.getDmsLast30day(); // 近三十天DMS
            dealBurstCoefficient = items.getDealBurstCoefficient().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP); // 活动预计爆发系数
            BigDecimal dealPrice = items.getDealPrice(); // 秒杀价
            // 活动天数
            Date planStartDate = obj.getPlanStartDate(); // 活动开始时间
            Date planEndDate = obj.getPlanEndDate(); // 活动结束时间
            long daysBetween = DateUtil.betweenDay(planStartDate, planEndDate, true);
            daysBetween = daysBetween == 0 ? 1 : daysBetween + 1;
            // 活动预计销量 = 活动预计爆发系数 * 近三十天DMS * 活动天数
            BigDecimal expectedSalesVolume = dealBurstCoefficient.multiply(dmsLast30day).multiply(new BigDecimal(daysBetween)).setScale(2, RoundingMode.HALF_UP);
            items.setExpectedSalesVolume(expectedSalesVolume);
            // 活动预计销售额 = 活动预计销量 * 秒杀价
            BigDecimal estimateOfSales = expectedSalesVolume.multiply(dealPrice).setScale(2, RoundingMode.HALF_UP);
            items.setEstimateOfSales(estimateOfSales);
            // 活动预计毛利额 = 活动预计销售额 * 秒杀价毛利率
            BigDecimal expectedGrossProfitMargin = estimateOfSales.multiply(dealPriceGross).setScale(2, RoundingMode.HALF_UP);
            // 数值过大 强制转换
            if (expectedGrossProfitMargin.toPlainString().replace("-", "").replace(".", "").length() > 8) {
                expectedGrossProfitMargin = BigDecimal.valueOf(-9999);
            }
            items.setExpectedGrossProfitMargin(expectedGrossProfitMargin);
            // 确定审批角色
            SomFreeAddDealItemsVo somFreeAddDealItemsVo = new SomFreeAddDealItemsVo();
            BeanUtils.copyProperties(items, somFreeAddDealItemsVo);
            items.setApprovalRole(initApprovalRole(somFreeAddDealItemsVo));

            insertList.add(items);
        }
        deal.setAid(aid);
        deal.setStatus(10);
        deal.setInternalDescription(cloneVo.getInternalDescription());
        deal.setPlanStartDate(cloneVo.getPlanStartDate());
        deal.setPlanEndDate(cloneVo.getPlanEndDate());
        deal.setCreateNum(tokenUser.getJobNumber());
        deal.setCreateName(tokenUser.getUserName());
        deal.setCreateTime(DateTime.now().toJdkDate());
        deal.setModifyStatus(null);
        deal.setRealStartDate(null);
        deal.setRealEndDate(null);
        deal.setModifyRemark(null);
        deal.setModifyFailureRemark(null);
        deal.setCancelRemark(null);
        deal.setCacenlFailureRemark(null);
        deal.setAuditFailureRemark(null);
        deal.setModifyName(null);
        deal.setModifyNum(null);
        deal.setModifyTime(null);
        somFreeAddDealMapper.insert(deal);
        if (CollectionUtil.isNotEmpty(insertList)) {
            somFreeAddDealItemsMapper.insertBatch(insertList);
        }
    }

    /**
     * summited
     * 标记为已提报
     *
     * @param summitedVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void summited(SomFreeAddDealExtVo summitedVo) throws ValidateException {
        if (ObjectUtil.isEmpty(summitedVo) || StrUtil.isBlank(summitedVo.getAid())) {
            throw new ValidateException("数据存在空值");
        }
        SomFreeAddDeal deal = somFreeAddDealMapper.createLambdaQuery().andEq("aid", summitedVo.getAid()).andEq("status", 21).single();
        if (ObjectUtil.isEmpty(deal)) {
            throw new ValidateException("该数据状态不支持当前操作,请检查");
        }
        deal.setStatus(110);
        somFreeAddDealMapper.updateById(deal);
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String exportExcel(SomFreeAddDealPageSearchVo exportVo) {
        List<SomFreeAddDealExportVo> freeAddDealExportVos = somFreeAddDealMapper.exportExcel(exportVo);
        if (CollUtil.isEmpty(freeAddDealExportVos)) {
            return null;
        }
        List<String> dictCodes = Arrays.asList("LDDealType", "LDStatus", "LDModifySatus", "PDDApplyReason", "CampaignType");
        List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", dictCodes).select();
        Map<String, Map<String, String>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode,
                        Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (v1, v2) -> v1)));
        Map<String, String> ldDealTypeMap = mcDictionaryInfoMap.get("LDDealType");
        Map<String, String> ldStatusMap = mcDictionaryInfoMap.get("LDStatus");
        Map<String, String> ldModifyStatusMap = mcDictionaryInfoMap.get("LDModifySatus");
        Map<String, String> pddApplyReasonMap = mcDictionaryInfoMap.get("PDDApplyReason");
        Map<String, String> campaignTypeMap = mcDictionaryInfoMap.get("CampaignType");
        for (SomFreeAddDealExportVo freeAddDealExportVo : freeAddDealExportVos) {
            freeAddDealExportVo.setDealTypeName(CollUtil.isEmpty(ldDealTypeMap) ? null : ldDealTypeMap.get(String.valueOf(freeAddDealExportVo.getDealType())));
            freeAddDealExportVo.setStatusName(CollUtil.isEmpty(ldStatusMap) ? null : ldStatusMap.get(String.valueOf(freeAddDealExportVo.getStatus())));
            freeAddDealExportVo.setModifyStatusName(CollUtil.isEmpty(ldModifyStatusMap) ? null : ldModifyStatusMap.get(String.valueOf(freeAddDealExportVo.getModifyStatus())));
            freeAddDealExportVo.setApplyReasonName(CollUtil.isEmpty(pddApplyReasonMap) ? null : pddApplyReasonMap.get(String.valueOf(freeAddDealExportVo.getApplyReason())));
            freeAddDealExportVo.setCampaignTypeName(CollUtil.isEmpty(campaignTypeMap) ? null : campaignTypeMap.get(String.valueOf(freeAddDealExportVo.getCampaignType())));
            // 填充错误信息，如果状态是需要关注，需要填充子项的错误信息
            Integer needAttentionStatus = 41;
            freeAddDealExportVo.setErrorMsg(needAttentionStatus.equals(freeAddDealExportVo.getStatus()) ? freeAddDealExportVo.getErrorMsg() : null);
        }
        try {
            Workbook workbook;
            ExportParams params = new ExportParams(null, "LD&7DD");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomFreeAddDealExportVo.class, freeAddDealExportVos);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(byteArray);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelAsin(SomFreeAddDealExtVo dealVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(dealVo) || StrUtil.isEmpty(dealVo.getAid()) || ObjectUtil.isNull(dealVo.getItemsVoList())) {
            throw new ValidateException("数据存在空值");
        }
        //修改主表状态为取消子ASIN中 120
        SomFreeAddDeal somFreeAddDeal = new SomFreeAddDeal();
        somFreeAddDeal.setStatus(120);
        //修改中
        somFreeAddDeal.setModifyStatus(10);
        somFreeAddDeal.setModifyName(tokenUser.getUserName());
        somFreeAddDeal.setModifyNum(tokenUser.getJobNumber());
        somFreeAddDeal.setModifyTime(DateTime.now().toJdkDate());
        somFreeAddDealMapper.createLambdaQuery().andEq("aid", dealVo.getAid()).updateSelective(somFreeAddDeal);
        //修改子表状态为取消 0
        somFreeAddDealItemsMapper.updateAsinStatus(dealVo.getItemsVoList());

    }

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    public String batchCancel(List<SomFreeAddDealImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder(); // 返回的错误信息
        List<SomFreeAddDeal> updateList = new ArrayList<>(); // 需要更新的数据列
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (SomFreeAddDealImportVo importVo : list) {
            if (ObjectUtil.isEmpty(importVo) || ObjectUtil.isEmpty(importVo.getSite()) || ObjectUtil.isEmpty(importVo.getInternalDescription()) || ObjectUtil.isEmpty(importVo.getPlanStartDate()) || ObjectUtil.isEmpty(importVo.getPlanEndDate())) {
                throw new ValidateException("参数存在空值,请检查");
            }

            // 未开始  需要关注  进行中  运营已提报
            SomFreeAddDeal deal = somFreeAddDealMapper
                    .createLambdaQuery()
                    .andEq("site", importVo.getSite())
                    .andEq("internal_description", importVo.getInternalDescription())
                    .andGreatEq("plan_start_date", LocalDateTime.parse(importVo.getPlanStartDate() + " 00:00:00", formatter))
                    .andLessEq("plan_end_date", LocalDateTime.parse(importVo.getPlanEndDate() + " 23:59:59", formatter))
                    .andIn("status", Arrays.asList(40, 41, 70, 110))
                    .single();

            if (ObjectUtil.isEmpty(deal)) {
                resultStr
                        .append("站点:")
                        .append(importVo.getSite())
                        .append(" — 活动命名:")
                        .append(importVo.getInternalDescription())
                        .append(" — 查询数据不存在，或状态不允许取消")
                        .append("\n");
            } else {
                // 状态
                deal.setStatus(50);
                updateList.add(deal);
            }
        }

        if (StrUtil.isNotBlank(resultStr)) {
            return resultStr.toString();
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            somFreeAddDealMapper.batchCancel(updateList);
        }

        return Strings.EMPTY;
    }

    /**
     * batchFeedbackResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    public String batchFeedbackCancelResult(List<SomFreeAddDealImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder resultStr = new StringBuilder(); // 返回的错误信息
        List<SomFreeAddDeal> updateList = new ArrayList<>(); // 需要更新的数据列
        List<McDictionaryInfo> mcDictionaryInfos = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "LDStatus").select(); // 字典值
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (SomFreeAddDealImportVo importVo : list) {
            if (ObjectUtil.isEmpty(importVo) || ObjectUtil.isEmpty(importVo.getSite()) || ObjectUtil.isEmpty(importVo.getInternalDescription())) {
                throw new ValidateException("参数存在空值,请检查");
            }

            SomFreeAddDeal deal = somFreeAddDealMapper
                    .createLambdaQuery()
                    .andEq("site", importVo.getSite())
                    .andEq("internal_description", importVo.getInternalDescription())
                    .andGreatEq("plan_start_date", LocalDateTime.parse(importVo.getPlanStartDate() + " 00:00:00", formatter))
                    .andLessEq("plan_end_date", LocalDateTime.parse(importVo.getPlanEndDate() + " 23:59:59", formatter))
                    .andEq("status", 50)
                    .single();

            if (ObjectUtil.isEmpty(deal)) {
                // throw new ValidateException("站点:" + importVo.getSite() + " 活动命名:" + importVo.getInternalDescription() + " 查询数据不存在，或状态不允许反馈取消结果");
                resultStr
                        .append("站点:")
                        .append(importVo.getSite())
                        .append(" — 活动命名:")
                        .append(importVo.getInternalDescription())
                        .append(" — 查询数据不存在，或状态不允许反馈取消结果")
                        .append("\n");
            } else {
                // 状态
                mcDictionaryInfos.stream()
                        .filter(e -> e.getItemLable().equals(importVo.getStatusName()))
                        .findFirst().ifPresent(ps -> {
                            deal.setStatus(Integer.parseInt(ps.getItemValue()));
                        });
                deal.setCacenlFailureRemark(importVo.getRemark()); // 取消失败原因
                updateList.add(deal);
            }

            // 单条更新 忽略
            // deal.setStatus(importVo.getStatus());
            // deal.setCacenlFailureRemark(importVo.getRemark());
            // somFreeAddDealMapper.updateById(deal);
        }

        if (StrUtil.isNotBlank(resultStr)) {
            return resultStr.toString();
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            somFreeAddDealMapper.batchFeedbackCancelResult(updateList);
        }

        return Strings.EMPTY;
    }

    /**
     * 批量编辑
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登陆用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importBatchEdit(List<SomFreeAndDealBatchEditImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkBatchEditImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        List<SomFreeAddDeal> updateSomFreeAddDeals = new ArrayList<>();
        List<SomFreeAddDealItems> updateSomFreeAddDealItems = new ArrayList<>();
        for (SomFreeAndDealBatchEditImportVo importVo : importVos) {
            SomFreeAddDeal somFreeAddDeal = new SomFreeAddDeal();
            somFreeAddDeal.setAid(importVo.getAid());
            // 修改状态：修改中
            somFreeAddDeal.setModifyStatus(10);
            somFreeAddDeal.setModifyName(tokenUser.getUserName());
            somFreeAddDeal.setModifyNum(tokenUser.getJobNumber());
            somFreeAddDeal.setModifyTime(DateTime.now().toJdkDate());
            updateSomFreeAddDeals.add(somFreeAddDeal);

            SomFreeAddDealItems somFreeAddDealItems = new SomFreeAddDealItems();
            somFreeAddDealItems.setAid(importVo.getItemAid());
            somFreeAddDealItems.setDealPrice(importVo.getDealPrice());
            somFreeAddDealItems.setDealQuantity(importVo.getDealQuantity());
            updateSomFreeAddDealItems.add(somFreeAddDealItems);
        }
        somFreeAddDealMapper.batchImportEdit(updateSomFreeAddDeals);
        somFreeAddDealItemsMapper.batchImportEdit(updateSomFreeAddDealItems);
    }

    /**
     * 导入批量反馈修改结果
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importBatchFeedbackEditResult(List<SomFreeAddDealBatchFeedbackEditResultImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkBatchFeedbackEditResultData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        List<SomFreeAddDeal> updateSomFreeAddDeals = new ArrayList<>();
        for (SomFreeAddDealBatchFeedbackEditResultImportVo importVo : importVos) {
            SomFreeAddDeal somFreeAddDeal = new SomFreeAddDeal();
            somFreeAddDeal.setAid(importVo.getAid());
            somFreeAddDeal.setStatus(importVo.getStatus());
            somFreeAddDeal.setModifyStatus(importVo.getModifyStatus());
            somFreeAddDeal.setModifyFailureRemark(importVo.getModifyFailureRemark());
            somFreeAddDeal.setModifyName(tokenUser.getUserName());
            somFreeAddDeal.setModifyNum(tokenUser.getJobNumber());
            somFreeAddDeal.setModifyTime(DateTime.now().toJdkDate());
            updateSomFreeAddDeals.add(somFreeAddDeal);
        }
        somFreeAddDealMapper.batchImportFeedbackEditResult(updateSomFreeAddDeals);
    }

    /**
     * 核验批量反馈修改结果行数据
     *
     * @param importVos 导入行数据
     * @return 错误信息列表
     */
    private List<String> checkBatchFeedbackEditResultData(List<SomFreeAddDealBatchFeedbackEditResultImportVo> importVos) {
        // 查询 状态=41(需要关注) 修改状态=10（修改中）的数据
        List<SomFreeAddDeal> somFreeAddDeals = somFreeAddDealMapper.createLambdaQuery().andEq("modify_status", 10).andEq("status", 41).select();
        Map<String, SomFreeAddDeal> somFreeAddDealMap = somFreeAddDeals.stream()
                .collect(Collectors.toMap(x-> x.getSite() + "_" + x.getInternalDescription(),  Function.identity(), (v1, v2) -> v1));
        // 查询字典
        List<McDictionaryInfo> ldModifyStatusDictList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "LDModifySatus").select();
        Map<String, McDictionaryInfo> ldModifyStatusMap = ldModifyStatusDictList.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (v1, v2) -> v1));
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomFreeAddDealBatchFeedbackEditResultImportVo importVo : importVos) {
            // 校验必填字段
            String site = importVo.getSite();
            String internalDescription = importVo.getInternalDescription();
            String modifyStatusStr = importVo.getModifyStatusStr();
            if (!StrUtil.isAllNotEmpty(site, internalDescription, modifyStatusStr)) {
                errors.add("错误0：站点、Internal Description、修改状态不能为空！");
                continue;
            }
            String allErrorFieldMsg = StrUtil.concat(true, "站点[", site, "]Internal Description[", internalDescription, "]");
            // 核验数据重复
            String key = StrUtil.concat(true, site, internalDescription);
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, "错误1：", allErrorFieldMsg, "数据重复！"));
                continue;
            }
            repeatCheckSet.add(key);
            // 核验修改状态
            String dictNeedCheck = "1";
            McDictionaryInfo ldModifyStatusDict = ldModifyStatusMap.get(modifyStatusStr);
            if (ldModifyStatusDict == null || !dictNeedCheck.equals(ldModifyStatusDict.getItemValue1())) {
                errors.add(StrUtil.concat(true, "错误2：修改状态[", modifyStatusStr, "]有误！"));
                continue;
            }
            importVo.setModifyStatus(Integer.valueOf(ldModifyStatusDict.getItemValue()));
            if (dictNeedCheck.equals(ldModifyStatusDict.getItemValue2()) && StrUtil.isEmpty(importVo.getModifyFailureRemark())) {
                errors.add(StrUtil.concat(true, "错误3：修改状态为[", modifyStatusStr, "]时，修改原因不能为空！"));
                continue;
            }
            // 核验数据是否存在
            String siteInterDescKey = StrUtil.concat(true, site, "_", internalDescription);
            if (!somFreeAddDealMap.containsKey(siteInterDescKey)) {
                errors.add(StrUtil.concat(true, "错误4：", allErrorFieldMsg, "当前状态不允许反馈修改结果！"));
                continue;
            }
            SomFreeAddDeal somFreeAddDeal = somFreeAddDealMap.get(siteInterDescKey);
            importVo.setAid(somFreeAddDeal.getAid());
            // 变更活动状态
            somFreeAddDeal.setModifyStatus(importVo.getModifyStatus());
            handleStatusByModifyStatus(somFreeAddDeal);
            importVo.setStatus(somFreeAddDeal.getStatus());
        }
        return errors;
    }

    /**
     * 通过【修改状态】，计算【活动状态】的变更
     *      修改成功时，需要变更【活动状态】
     *      非修改成功时，不需要变更【活动状态】
     *
     * @param somFreeAddDeal 营销活动
     */
    private void handleStatusByModifyStatus(SomFreeAddDeal somFreeAddDeal) {
        Integer successModifyStatus = 20;
        // 状态为【需要关注】且修改状态为【修改成功】时
        if ((ObjectUtil.equal(41, somFreeAddDeal.getStatus()) || ObjectUtil.equal(120, somFreeAddDeal.getStatus())) &&
                ObjectUtil.equal(successModifyStatus, somFreeAddDeal.getModifyStatus())) {
            DateTime now = DateTime.now();
            if (ObjectUtil.isAllNotEmpty(somFreeAddDeal.getRealStartDate(), somFreeAddDeal.getRealEndDate())) {
                //  实际截止时间 > 当前时间 > 实际开始时间，活动状态 => 70(进行中)，否则 => 110(运营已提报)
                Integer status = now.after(somFreeAddDeal.getRealStartDate()) && now.before(somFreeAddDeal.getRealEndDate()) ? 70 : 110;
                somFreeAddDeal.setStatus(status);
            } else {
                // 计划截止时间 > 当前时间 > 计划开始时间，活动状态 => 70(进行中)，否则 => 110(运营已提报)
                if (ObjectUtil.isAllNotEmpty(somFreeAddDeal.getPlanStartDate(), somFreeAddDeal.getPlanEndDate())) {
                    Integer status = now.after(somFreeAddDeal.getPlanStartDate()) && now.before(somFreeAddDeal.getPlanEndDate()) ? 70 : 110;
                    somFreeAddDeal.setStatus(status);
                }
            }
        }
    }

    /**
     * 核验批量编辑行数据
     *
     * @param importVos 导入行记录
     * @return 错误信息列表
     */
    private List<String> checkBatchEditImportData(List<SomFreeAndDealBatchEditImportVo> importVos) {
        // 查询 活动状态=41（需要关注）的数据
        List<SomFreeAddDeal> somFreeAddDeals = somFreeAddDealMapper.createLambdaQuery().andEq("status", 41).select();
        Map<String, SomFreeAddDeal> somFreeAddDealMap = somFreeAddDeals.stream()
                .collect(Collectors.toMap(x-> x.getSite() + "_" + x.getInternalDescription(),  Function.identity(), (v1, v2) -> v1));
        // 查询活动的子数据
        List<String> dealIds = somFreeAddDeals.stream().map(SomFreeAddDeal::getAid).collect(Collectors.toList());
        List<SomFreeAddDealItems> items = new ArrayList<>();
        if (CollUtil.isNotEmpty(dealIds)) {
            items = somFreeAddDealItemsMapper.createLambdaQuery().andIn("deal_id", dealIds).select();
        }
        Map<String, Map<String, SomFreeAddDealItems>> itemsMap = items.stream()
                .collect(Collectors.groupingBy(SomFreeAddDealItems::getDealId, Collectors.toMap(SomFreeAddDealItems::getSellerSku, Function.identity(), (v1, v2) -> v1)));
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomFreeAndDealBatchEditImportVo importVo : importVos) {
            // 校验必填字段
            String site = importVo.getSite();
            String internalDescription = importVo.getInternalDescription();
            String sellerSku = importVo.getSellerSku();
            if (!StrUtil.isAllNotEmpty(site, internalDescription, sellerSku)) {
                errors.add("错误0：站点、Internal Description、展示码不能为空！");
                continue;
            }
            String dealPriceStr = importVo.getDealPriceStr();
            String dealQuantityStr = importVo.getDealQuantityStr();
            if (StrUtil.isAllEmpty(dealPriceStr, dealQuantityStr)) {
                errors.add("错误1：秒杀价、秒杀数量不能都为空！");
                continue;
            }
            String allErrorFieldMsg = StrUtil.concat(true, "站点[", site, "]Internal Description[", internalDescription, "]展示码[", sellerSku, "]");
            // 核验数据重复
            String key = StrUtil.concat(true, site, internalDescription, sellerSku);
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, "错误2：",allErrorFieldMsg, "数据重复！"));
                continue;
            }
            repeatCheckSet.add(key);
            // 核验秒杀价
            if (StrUtil.isNotEmpty(dealPriceStr)) {
                try {
                    importVo.setDealPrice(new BigDecimal(importVo.getDealPriceStr()));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, "错误3：秒杀价[", dealPriceStr, "]格式有误！"));
                    continue;
                }
            }
            // 核验秒杀数量
            if (StrUtil.isNotEmpty(dealQuantityStr)) {
                try {
                    importVo.setDealQuantity(Integer.parseInt(dealQuantityStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, "错误4：秒杀数量[", dealQuantityStr, "]格式有误！"));
                    continue;
                }
            }
            // 核验数据是否存在，1：主表不存在；2. 子表不存在
            String siteInterDescKey = StrUtil.concat(true, site, "_", internalDescription);
            SomFreeAddDeal somFreeAddDeal = somFreeAddDealMap.get(siteInterDescKey);
            if (somFreeAddDeal == null) {
                errors.add(StrUtil.concat(true, "错误5：", allErrorFieldMsg, "非需要关注状态，不允许编辑！"));
                continue;
            }
            importVo.setAid(somFreeAddDeal.getAid());
            Map<String, SomFreeAddDealItems> dealItemsMap = itemsMap.get(somFreeAddDeal.getAid());
            if (CollUtil.isEmpty(dealItemsMap)) {
                errors.add(StrUtil.concat(true, "错误5：", allErrorFieldMsg, "非需要关注状态，不允许编辑！"));
                continue;
            }
            SomFreeAddDealItems somFreeAddDealItems = dealItemsMap.get(sellerSku);
            if(somFreeAddDealItems == null) {
                errors.add(StrUtil.concat(true, "错误5：", allErrorFieldMsg, "非需要关注状态，不允许编辑！"));
                continue;
            }
            importVo.setItemAid(somFreeAddDealItems.getAid());
            importVo.setDealPrice(importVo.getDealPrice() == null ? somFreeAddDealItems.getDealPrice() : importVo.getDealPrice());
            importVo.setDealQuantity(importVo.getDealQuantity() == null ? somFreeAddDealItems.getDealQuantity() : importVo.getDealQuantity());
        }
        return errors;
    }


}