package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomVcPointsPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomVcPointsPromotionVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

@SqlResource("somVcPointsPromotion")
public interface SomVcPointsPromotionMapper extends BaseMapper<SomVcPointsPromotion> {

    PageResult<SomVcPointsPromotionVo> queryByPage(@Param("searchVo")SomVcPointsPromotionPageSearchVo searchVo, PageRequest pageRequest);

}
