package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTargetWarehouseConfigService;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigVo;
import com.zielsmart.mc.vo.SomTargetWarehouseVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTargetWarehouseConfigController
 * @description
 * @date 2023-12-06 14:11:14
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTargetWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Target可售仓库配置管理")
public class SomTargetWarehouseConfigController extends BasicController {

    /**
     * somTargetWarehouseConfigService
     */
    @Resource
    SomTargetWarehouseConfigService somTargetWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTargetWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTargetWarehouseConfigVo>> queryByPage(@RequestBody SomTargetWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTargetWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somTargetWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomTargetWarehouseConfigVo somTargetWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTargetWarehouseConfigService.save(somTargetWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somTargetWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomTargetWarehouseConfigVo somTargetWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTargetWarehouseConfigService.update(somTargetWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somTargetWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomTargetWarehouseConfigVo somTargetWarehouseConfigVo) throws ValidateException {
        somTargetWarehouseConfigService.delete(somTargetWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryList
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomTargetWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取平台仓库列表")
    @PostMapping(value = "/queryList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomTargetWarehouseVo>> queryList() {
        return ResultVo.ofSuccess(somTargetWarehouseConfigService.queryList());
    }

}
