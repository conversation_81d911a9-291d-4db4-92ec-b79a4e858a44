package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 客户组配置
* gen by 代码生成器 2024-03-11
*/

@Table(name="mc.mc_customer_group_config")
public class McCustomerGroupConfig implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private Long seqnum ;
	/**
	 * 客户组
	 */
	@Column("customer_group")
	private String customerGroup ;
	/**
	 * 代表客户
	 */
	@Column("delegate_customer")
	private String delegateCustomer ;
	/**
	 * 客户简称
	 */
	@Column("customer_short_name")
	private String customerShortName ;
	/**
	 * 国家
	 */
	@Column("country_name")
	private String countryName ;
	/**
	 * 营销市场
	 */
	@Column("sale_market")
	private String saleMarket ;
	/**
	 * 营销渠道
	 */
	@Column("sale_channel")
	private String saleChannel ;
	/**
	 * 销售区域
	 */
	@Column("sale_area")
	private String saleArea ;
	/**
	 * 发货方式
	 */
	@Column("fulfillment_type")
	private String fulfillmentType ;
	/**
	 * 发货渠道
	 */
	@Column("fulfillment_channel")
	private String fulfillmentChannel ;
	/**
	 * 老客户组
	 */
	@Column("old_customer_group")
	private String oldCustomerGroup ;
	/**
	 * 主要客户组
	 */
	@Column("main_customer_group")
	private Integer mainCustomerGroup ;

	public McCustomerGroupConfig() {
	}

	/**
	* 主键id
	*@return
	*/
	public Long getSeqnum(){
		return  seqnum;
	}
	/**
	* 主键id
	*@param  seqnum
	*/
	public void setSeqnum(Long seqnum ){
		this.seqnum = seqnum;
	}
	/**
	* 客户组
	*@return
	*/
	public String getCustomerGroup(){
		return  customerGroup;
	}
	/**
	* 客户组
	*@param  customerGroup
	*/
	public void setCustomerGroup(String customerGroup ){
		this.customerGroup = customerGroup;
	}
	/**
	* 代表客户
	*@return
	*/
	public String getDelegateCustomer(){
		return  delegateCustomer;
	}
	/**
	* 代表客户
	*@param  delegateCustomer
	*/
	public void setDelegateCustomer(String delegateCustomer ){
		this.delegateCustomer = delegateCustomer;
	}
	/**
	* 客户简称
	*@return
	*/
	public String getCustomerShortName(){
		return  customerShortName;
	}
	/**
	* 客户简称
	*@param  customerShortName
	*/
	public void setCustomerShortName(String customerShortName ){
		this.customerShortName = customerShortName;
	}
	/**
	* 国家
	*@return
	*/
	public String getCountryName(){
		return  countryName;
	}
	/**
	* 国家
	*@param  countryName
	*/
	public void setCountryName(String countryName ){
		this.countryName = countryName;
	}
	/**
	* 营销市场
	*@return
	*/
	public String getSaleMarket(){
		return  saleMarket;
	}
	/**
	* 营销市场
	*@param  saleMarket
	*/
	public void setSaleMarket(String saleMarket ){
		this.saleMarket = saleMarket;
	}
	/**
	* 营销渠道
	*@return
	*/
	public String getSaleChannel(){
		return  saleChannel;
	}
	/**
	* 营销渠道
	*@param  saleChannel
	*/
	public void setSaleChannel(String saleChannel ){
		this.saleChannel = saleChannel;
	}
	/**
	* 销售区域
	*@return
	*/
	public String getSaleArea(){
		return  saleArea;
	}
	/**
	* 销售区域
	*@param  saleArea
	*/
	public void setSaleArea(String saleArea ){
		this.saleArea = saleArea;
	}
	/**
	* 发货方式
	*@return
	*/
	public String getFulfillmentType(){
		return  fulfillmentType;
	}
	/**
	* 发货方式
	*@param  fulfillmentType
	*/
	public void setFulfillmentType(String fulfillmentType ){
		this.fulfillmentType = fulfillmentType;
	}
	/**
	* 发货渠道
	*@return
	*/
	public String getFulfillmentChannel(){
		return  fulfillmentChannel;
	}
	/**
	* 发货渠道
	*@param  fulfillmentChannel
	*/
	public void setFulfillmentChannel(String fulfillmentChannel ){
		this.fulfillmentChannel = fulfillmentChannel;
	}
	/**
	* 老客户组
	*@return
	*/
	public String getOldCustomerGroup(){
		return  oldCustomerGroup;
	}
	/**
	* 老客户组
	*@param  oldCustomerGroup
	*/
	public void setOldCustomerGroup(String oldCustomerGroup ){
		this.oldCustomerGroup = oldCustomerGroup;
	}
	/**
	* 主要客户组
	*@return
	*/
	public Integer getMainCustomerGroup(){
		return  mainCustomerGroup;
	}
	/**
	* 主要客户组
	*@param  mainCustomerGroup
	*/
	public void setMainCustomerGroup(Integer mainCustomerGroup ){
		this.mainCustomerGroup = mainCustomerGroup;
	}

}
