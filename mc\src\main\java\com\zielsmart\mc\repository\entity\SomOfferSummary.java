package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
 *
 * gen by 代码生成器 2022-11-30
 */

@Table(name = "mc.som_offer_summary")
public class SomOfferSummary implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 主表ID
     */
    @Column("main_id ")
    private String mainId;
    /**
     * JSON字符串，存储报价总数列表
     */
    @Column("number_of_offers")
    private String numberOfOffers;
    /**
     * JSON字符串，存储最低价相关信息
     */
    @Column("lowest_prices")
    private String lowestPrices;
    /**
     * JSON字符串，存储buyBox价格相关信息
     */
    @Column("buy_box_prices")
    private String buyBoxPrices;
    /**
     * JSON字符串，包含价格和币种
     */
    @Column("list_price")
    private String listPrice;
    /**
     * JSON字符串，建议最低价，包含价格和币种
     */
    @Column("suggested_lower_price_plus_shipping")
    private String suggestedLowerPricePlusShipping;
    /**
     * JSON字符串，产品在类目中的排名信息，包含类目ID和排名
     */
    @Column("sales_rankings")
    private String salesRankings;
    /**
     * JSON字符串，获得购买箱的所有报价（区分渠道）
     */
    @Column("number_of_buy_box_eligible_offers")
    private String numberOfBuyBoxEligibleOffers;
    /**
     * JSON字符串，其他平台的最低报价。如果Amazon平台的价格高于这个价格，则可能失去购物箱
     */
    @Column("competitive_price_threshold")
    private String competitivePriceThreshold;

    public SomOfferSummary() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 主表ID
     *
     * @return
     */
    public String getMainId() {
        return mainId;
    }

    /**
     * 主表ID
     *
     * @param mainId
     */
    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    /**
     * JSON字符串，存储报价总数列表
     *
     * @return
     */
    public String getNumberOfOffers() {
        return numberOfOffers;
    }

    /**
     * JSON字符串，存储报价总数列表
     *
     * @param numberOfOffers
     */
    public void setNumberOfOffers(String numberOfOffers) {
        this.numberOfOffers = numberOfOffers;
    }

    /**
     * JSON字符串，存储最低价相关信息
     *
     * @return
     */
    public String getLowestPrices() {
        return lowestPrices;
    }

    /**
     * JSON字符串，存储最低价相关信息
     *
     * @param lowestPrices
     */
    public void setLowestPrices(String lowestPrices) {
        this.lowestPrices = lowestPrices;
    }

    /**
     * JSON字符串，存储buyBox价格相关信息
     *
     * @return
     */
    public String getBuyBoxPrices() {
        return buyBoxPrices;
    }

    /**
     * JSON字符串，存储buyBox价格相关信息
     *
     * @param buyBoxPrices
     */
    public void setBuyBoxPrices(String buyBoxPrices) {
        this.buyBoxPrices = buyBoxPrices;
    }

    /**
     * JSON字符串，包含价格和币种
     *
     * @return
     */
    public String getListPrice() {
        return listPrice;
    }

    /**
     * JSON字符串，包含价格和币种
     *
     * @param listPrice
     */
    public void setListPrice(String listPrice) {
        this.listPrice = listPrice;
    }

    /**
     * JSON字符串，建议最低价，包含价格和币种
     *
     * @return
     */
    public String getSuggestedLowerPricePlusShipping() {
        return suggestedLowerPricePlusShipping;
    }

    /**
     * JSON字符串，建议最低价，包含价格和币种
     *
     * @param suggestedLowerPricePlusShipping
     */
    public void setSuggestedLowerPricePlusShipping(String suggestedLowerPricePlusShipping) {
        this.suggestedLowerPricePlusShipping = suggestedLowerPricePlusShipping;
    }

    /**
     * JSON字符串，产品在类目中的排名信息，包含类目ID和排名
     *
     * @return
     */
    public String getSalesRankings() {
        return salesRankings;
    }

    /**
     * JSON字符串，产品在类目中的排名信息，包含类目ID和排名
     *
     * @param salesRankings
     */
    public void setSalesRankings(String salesRankings) {
        this.salesRankings = salesRankings;
    }

    /**
     * JSON字符串，获得购买箱的所有报价（区分渠道）
     *
     * @return
     */
    public String getNumberOfBuyBoxEligibleOffers() {
        return numberOfBuyBoxEligibleOffers;
    }

    /**
     * JSON字符串，获得购买箱的所有报价（区分渠道）
     *
     * @param numberOfBuyBoxEligibleOffers
     */
    public void setNumberOfBuyBoxEligibleOffers(String numberOfBuyBoxEligibleOffers) {
        this.numberOfBuyBoxEligibleOffers = numberOfBuyBoxEligibleOffers;
    }

    /**
     * JSON字符串，其他平台的最低报价。如果Amazon平台的价格高于这个价格，则可能失去购物箱
     *
     * @return
     */
    public String getCompetitivePriceThreshold() {
        return competitivePriceThreshold;
    }

    /**
     * JSON字符串，其他平台的最低报价。如果Amazon平台的价格高于这个价格，则可能失去购物箱
     *
     * @param competitivePriceThreshold
     */
    public void setCompetitivePriceThreshold(String competitivePriceThreshold) {
        this.competitivePriceThreshold = competitivePriceThreshold;
    }

}
