package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McSaleInventoryReportMultipleService;
import com.zielsmart.mc.service.McWarehouseConfigMultipleService;
import com.zielsmart.mc.vo.McSaleInventoryReportMultipleSearchVo;
import com.zielsmart.mc.vo.McSaleInventoryReportMultipleVo;
import com.zielsmart.mc.vo.McSaleInventoryReportVo;
import com.zielsmart.mc.vo.McWarehouseConfigMultipleVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McSaleInventoryReportMultipleController
 * @description 可售库存报表(多仓)Controller
 * @date 2021-10-09 17:51:32
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/sale-inventory-report-multiple", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "可售库存报表(多仓)")
public class McSaleInventoryReportMultipleController extends BasicController {

    @Resource
    private McWarehouseConfigMultipleService mcWarehouseConfigMultipleService;
    @Resource
    private McSaleInventoryReportMultipleService mcSaleInventoryReportMultipleService;

    /**
     * getGroupList
     * 获取可售仓库列表
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McDeptVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可售仓库列表")
    @PostMapping(value = "/get-saleable-warehouse-list")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWarehouseConfigMultipleVo>> getSaleableWarehouseList() {
        return ResultVo.ofSuccess(mcWarehouseConfigMultipleService.getAll());
    }

    /**
     * queryByPage
     * 查询可售库存报表(多仓)
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询可售库存报表(多仓)")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McSaleInventoryReportMultipleVo>> queryByPage(@RequestBody McSaleInventoryReportMultipleSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcSaleInventoryReportMultipleService.querySaleableStockReport(searchVo));
    }

    /**
     * batchPutUp
     * 批量上架
     *
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量上架")
    @PostMapping(value = "/batch-put-up")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutUp(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcSaleInventoryReportMultipleService.batchPutUp(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * batchPutDown
     * 批量下架
     *
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量下架")
    @PostMapping(value = "/batch-put-down")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutDown(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcSaleInventoryReportMultipleService.batchPutDown(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * exportDearanceProduct
     * 导出可售库存报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportDearanceProduct(@RequestBody McSaleInventoryReportMultipleSearchVo searchVo) throws ValidateException {
        String data = mcSaleInventoryReportMultipleService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
