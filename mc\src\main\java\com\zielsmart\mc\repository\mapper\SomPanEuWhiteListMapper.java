package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomPanEuWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomPanEuWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2023-12-13
 */

@SqlResource("somPanEuWhiteList")
public interface SomPanEuWhiteListMapper extends BaseMapper<SomPanEuWhiteList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomPanEuWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPanEuWhiteListVo> queryByPage(@Param("searchVo") SomPanEuWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
