package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomFreeAddDealItems;
import com.zielsmart.mc.repository.mapper.SomFreeAddDealItemsMapper;
import com.zielsmart.mc.vo.SomFreeAddDealItemsPageSearchVo;
import com.zielsmart.mc.vo.SomFreeAddDealItemsVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomFreeAddDealItemsService {
    
    @Resource
    private SomFreeAddDealItemsMapper somFreeAddDealItemsMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomFreeAddDealItemsVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomFreeAddDealItemsVo> queryByPage(SomFreeAddDealItemsPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomFreeAddDealItemsVo> pageResult = dynamicSqlManager.getMapper(SomFreeAddDealItemsMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomFreeAddDealItemsVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somFreeAddDealItemsVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomFreeAddDealItemsVo somFreeAddDealItemsVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeAddDealItemsVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somFreeAddDealItemsVo.setAid(IdUtil.fastSimpleUUID());
        //TODO 根据情况判断是否需要添加创建人信息
        somFreeAddDealItemsMapper.insert(ConvertUtils.beanConvert(somFreeAddDealItemsVo, SomFreeAddDealItems.class));
    }

    /**
     * update
     * 修改
     * @param somFreeAddDealItemsVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomFreeAddDealItemsVo somFreeAddDealItemsVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeAddDealItemsVo) || StrUtil.isEmpty(somFreeAddDealItemsVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somFreeAddDealItemsMapper.createLambdaQuery()
                .andEq("aid",somFreeAddDealItemsVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somFreeAddDealItemsVo, SomFreeAddDealItems.class));
    }

    /**
     * delete
     * 删除
     * @param somFreeAddDealItemsVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomFreeAddDealItemsVo somFreeAddDealItemsVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeAddDealItemsVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        // somFreeAddDealItemsMapper.createLambdaQuery().andIn("aid", somFreeAddDealItemsVo.getAidList()).delete();
        somFreeAddDealItemsMapper.createLambdaQuery().andEq("aid",somFreeAddDealItemsVo.getAid()).delete();
    }
}
