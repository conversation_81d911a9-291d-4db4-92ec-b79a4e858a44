package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McCategoryInfoService;
import com.zielsmart.mc.vo.McCategoryInfoTreeVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McCategoryInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcCategoryInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "三级分类信息管理")
public class McCategoryInfoController extends BasicController{

    @Resource
    McCategoryInfoService mcCategoryInfoService;

    @Operation(summary = "全量获取品类树节点")
    @PostMapping(value = "/all-category-tree",consumes = MediaType.APPLICATION_JSON_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McCategoryInfoTreeVo>> findcategoryForTree(){
        return ResultVo.ofSuccess(mcCategoryInfoService.queryAllDeptForTree());
    }

    @Operation(summary = "全量获取品类树节点 四级分类")
    @PostMapping(value = "/all-category-tree-four",consumes = MediaType.APPLICATION_JSON_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McCategoryInfoTreeVo>> findcategoryForTreeFour(){
        return ResultVo.ofSuccess(mcCategoryInfoService.queryAllDeptForTreeFour());
    }
}
