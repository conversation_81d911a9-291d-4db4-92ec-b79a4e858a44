package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomEanInfo;
import com.zielsmart.mc.vo.SomEanInfoPageSearchVo;
import com.zielsmart.mc.vo.SomEanInfoVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-04-13
*/

@SqlResource("somEanInfo")
public interface SomEanInfoMapper extends BaseMapper<SomEanInfo> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomEanInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomEanInfoVo> queryByPage(@Param("searchVo")SomEanInfoPageSearchVo searchVo, PageRequest pageRequest);

    @Update
    void updateBelong(@Param("sequenceList")List<String> sequenceList,@Param("belong")String belong);

    default void batchOccupy(@Param("updateList") List<SomEanInfo> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somEanInfo.batchOccupy"), updateList);
    }

    default void batchUseSequenceNum(@Param("updateList") List<SomEanInfo> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somEanInfo.batchUseSequenceNum"), updateList);
    }
}
