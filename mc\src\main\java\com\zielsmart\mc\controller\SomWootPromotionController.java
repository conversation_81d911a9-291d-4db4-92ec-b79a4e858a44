package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomWootPromotionService;
import com.zielsmart.mc.vo.SomWootPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomWootPromotionVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWootPromotionController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somWootPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Woot促销活动管理")
public class SomWootPromotionController extends BasicController {

    @Resource
    SomWootPromotionService somWootPromotionService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWootPromotionVo>> queryByPage(@RequestBody SomWootPromotionPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somWootPromotionService.queryByPage(searchVo));
    }

    @Operation(summary = "查看库存详情")
    @PostMapping(value = "/quantityDeatil")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomWootPromotionVo> quantityDetail(@RequestBody SomWootPromotionVo woot) throws Exception {
        return ResultVo.ofSuccess(somWootPromotionService.quantityDetail(woot));
    }

    @Operation(summary = "查看")
    @PostMapping(value = "/look")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomWootPromotionVo> look(@RequestBody SomWootPromotionVo woot) throws Exception {
        return ResultVo.ofSuccess(somWootPromotionService.look(woot));
    }

    @Operation(summary = "展示码列表")
    @PostMapping(value = "/sellerSkus")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<String>> sellerSkus(@RequestBody @Validated SomWootPromotionVo somWootPromotionVo) throws ValidateException {
        return ResultVo.ofSuccess(somWootPromotionService.sellerSkus(somWootPromotionVo));
    }

    @Operation(summary = "Woot提报权限")
    @PostMapping(value = "/permissions")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<Integer> permissions(@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        return ResultVo.ofSuccess(somWootPromotionService.permissions(tokenUser));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        somWootPromotionService.save(somWootPromotionVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "获取展示码详情")
    @PostMapping(value = "/detail")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomWootPromotionVo> detail(@RequestBody @Validated SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        return ResultVo.ofSuccess(somWootPromotionService.detail(somWootPromotionVo, tokenUser));
    }

    @Operation(summary = "删除/批量删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionService.delete(somWootPromotionVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "提交/批量提交")
    @PostMapping(value = "/submit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> submit(@RequestBody SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionService.submit(somWootPromotionVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "审批/批量审批")
    @PostMapping(value = "/approval")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> approval(@RequestBody SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionService.approval(somWootPromotionVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "标记提报失败/批量标记提报失败")
    @PostMapping(value = "/fail")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> fail(@RequestBody SomWootPromotionVo somWootPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionService.fail(somWootPromotionVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/WootImportTempalte.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);

        ExcelImportResult<SomWootPromotionVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomWootPromotionVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result == null || result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somWootPromotionService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomWootPromotionPageSearchVo searchVo) throws Exception {
        String data = somWootPromotionService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出【提报中】数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
