package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 产品层级视图
 * gen by 代码生成器 2023-02-20
 */

@Table(name = "mc.som_product_hierarchy")
public class SomProductHierarchy implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 上层产品类型编码
     */
    @Column("upper_sku_type_code")
    private String upperSkuTypeCode;
    /**
     * 所属上层产品ID,外键
     */
    @Column("upper_sku_id")
    private Integer upperSkuId;
    /**
     * 所属上层产品SKU编码
     */
    @Column("upper_sku_code")
    private String upperSkuCode;
    /**
     * 当前产品类型编码
     */
    @Column("current_sku_type_code")
    private String currentSkuTypeCode;
    /**
     * 所属产品ID,外键
     */
    @Column("current_sku_id")
    private Integer currentSkuId;
    /**
     * 所属下层产品编码
     */
    @Column("current_sku_code")
    private String currentSkuCode;
    /**
     * 下层产品数量,为当前产品为组件产品预留，标示组套中的组件的数量,为上层产品是组合产品预留，标示当前产品的数量
     */
    @Column("current_sku_count")
    private Integer currentSkuCount;
    /**
     * 下层产品成本占比,当前产品为组件产品预留，标示组套中的组件的占比量上层产品是组合产品预留，标示当前产品的占比量
     */
    @Column("current_sku_ratio")
    private BigDecimal currentSkuRatio;
    /**
     * 用于控制这个关系的生效时间，在这个时间之后创建的单据才会使用该关系
     */
    @Column("start_time")
    private Date startTime;
    /**
     * 可用截止日期
     */
    @Column("end_time")
    private Date endTime;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 描述信息
     */
    @Column("remark_desc")
    private String remarkDesc;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;
    /**
     * 所属产品组ID
     */
    @Column("product_group_id")
    private Integer productGroupId;
    /**
     * 所属产品组编码
     */
    @Column("product_group_code")
    private String productGroupCode;
    /**
     * 所属产品组名称
     */
    @Column("product_group_name")
    private String productGroupName;
    /**
     * 所属产品组专员ID
     */
    @Column("product_group_empt_id")
    private Integer productGroupEmptId;
    /**
     * 所属产品组专员工号
     */
    @Column("product_group_empt_code")
    private String productGroupEmptCode;
    /**
     * 所属产品组专员名称
     */
    @Column("product_group_empt_name")
    private String productGroupEmptName;
    /**
     * 包裹序号
     */
    @Column("package_serial_number")
    private Integer packageSerialNumber;

    public SomProductHierarchy() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 上层产品类型编码
     *
     * @return
     */
    public String getUpperSkuTypeCode() {
        return upperSkuTypeCode;
    }

    /**
     * 上层产品类型编码
     *
     * @param upperSkuTypeCode
     */
    public void setUpperSkuTypeCode(String upperSkuTypeCode) {
        this.upperSkuTypeCode = upperSkuTypeCode;
    }

    /**
     * 所属上层产品ID,外键
     *
     * @return
     */
    public Integer getUpperSkuId() {
        return upperSkuId;
    }

    /**
     * 所属上层产品ID,外键
     *
     * @param upperSkuId
     */
    public void setUpperSkuId(Integer upperSkuId) {
        this.upperSkuId = upperSkuId;
    }

    /**
     * 所属上层产品SKU编码
     *
     * @return
     */
    public String getUpperSkuCode() {
        return upperSkuCode;
    }

    /**
     * 所属上层产品SKU编码
     *
     * @param upperSkuCode
     */
    public void setUpperSkuCode(String upperSkuCode) {
        this.upperSkuCode = upperSkuCode;
    }

    /**
     * 当前产品类型编码
     *
     * @return
     */
    public String getCurrentSkuTypeCode() {
        return currentSkuTypeCode;
    }

    /**
     * 当前产品类型编码
     *
     * @param currentSkuTypeCode
     */
    public void setCurrentSkuTypeCode(String currentSkuTypeCode) {
        this.currentSkuTypeCode = currentSkuTypeCode;
    }

    /**
     * 所属产品ID,外键
     *
     * @return
     */
    public Integer getCurrentSkuId() {
        return currentSkuId;
    }

    /**
     * 所属产品ID,外键
     *
     * @param currentSkuId
     */
    public void setCurrentSkuId(Integer currentSkuId) {
        this.currentSkuId = currentSkuId;
    }

    /**
     * 所属下层产品编码
     *
     * @return
     */
    public String getCurrentSkuCode() {
        return currentSkuCode;
    }

    /**
     * 所属下层产品编码
     *
     * @param currentSkuCode
     */
    public void setCurrentSkuCode(String currentSkuCode) {
        this.currentSkuCode = currentSkuCode;
    }

    /**
     * 下层产品数量,为当前产品为组件产品预留，标示组套中的组件的数量,为上层产品是组合产品预留，标示当前产品的数量
     *
     * @return
     */
    public Integer getCurrentSkuCount() {
        return currentSkuCount;
    }

    /**
     * 下层产品数量,为当前产品为组件产品预留，标示组套中的组件的数量,为上层产品是组合产品预留，标示当前产品的数量
     *
     * @param currentSkuCount
     */
    public void setCurrentSkuCount(Integer currentSkuCount) {
        this.currentSkuCount = currentSkuCount;
    }

    /**
     * 下层产品成本占比,当前产品为组件产品预留，标示组套中的组件的占比量上层产品是组合产品预留，标示当前产品的占比量
     *
     * @return
     */
    public BigDecimal getCurrentSkuRatio() {
        return currentSkuRatio;
    }

    /**
     * 下层产品成本占比,当前产品为组件产品预留，标示组套中的组件的占比量上层产品是组合产品预留，标示当前产品的占比量
     *
     * @param currentSkuRatio
     */
    public void setCurrentSkuRatio(BigDecimal currentSkuRatio) {
        this.currentSkuRatio = currentSkuRatio;
    }

    /**
     * 用于控制这个关系的生效时间，在这个时间之后创建的单据才会使用该关系
     *
     * @return
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 用于控制这个关系的生效时间，在这个时间之后创建的单据才会使用该关系
     *
     * @param startTime
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 可用截止日期
     *
     * @return
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 可用截止日期
     *
     * @param endTime
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 描述信息
     *
     * @return
     */
    public String getRemarkDesc() {
        return remarkDesc;
    }

    /**
     * 描述信息
     *
     * @param remarkDesc
     */
    public void setRemarkDesc(String remarkDesc) {
        this.remarkDesc = remarkDesc;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 所属产品组ID
     *
     * @return
     */
    public Integer getProductGroupId() {
        return productGroupId;
    }

    /**
     * 所属产品组ID
     *
     * @param productGroupId
     */
    public void setProductGroupId(Integer productGroupId) {
        this.productGroupId = productGroupId;
    }

    /**
     * 所属产品组编码
     *
     * @return
     */
    public String getProductGroupCode() {
        return productGroupCode;
    }

    /**
     * 所属产品组编码
     *
     * @param productGroupCode
     */
    public void setProductGroupCode(String productGroupCode) {
        this.productGroupCode = productGroupCode;
    }

    /**
     * 所属产品组名称
     *
     * @return
     */
    public String getProductGroupName() {
        return productGroupName;
    }

    /**
     * 所属产品组名称
     *
     * @param productGroupName
     */
    public void setProductGroupName(String productGroupName) {
        this.productGroupName = productGroupName;
    }

    /**
     * 所属产品组专员ID
     *
     * @return
     */
    public Integer getProductGroupEmptId() {
        return productGroupEmptId;
    }

    /**
     * 所属产品组专员ID
     *
     * @param productGroupEmptId
     */
    public void setProductGroupEmptId(Integer productGroupEmptId) {
        this.productGroupEmptId = productGroupEmptId;
    }

    /**
     * 所属产品组专员工号
     *
     * @return
     */
    public String getProductGroupEmptCode() {
        return productGroupEmptCode;
    }

    /**
     * 所属产品组专员工号
     *
     * @param productGroupEmptCode
     */
    public void setProductGroupEmptCode(String productGroupEmptCode) {
        this.productGroupEmptCode = productGroupEmptCode;
    }

    /**
     * 所属产品组专员名称
     *
     * @return
     */
    public String getProductGroupEmptName() {
        return productGroupEmptName;
    }

    /**
     * 所属产品组专员名称
     *
     * @param productGroupEmptName
     */
    public void setProductGroupEmptName(String productGroupEmptName) {
        this.productGroupEmptName = productGroupEmptName;
    }

    /**
     * 包裹序号
     *
     * @return
     */
    public Integer getPackageSerialNumber() {
        return packageSerialNumber;
    }

    /**
     * 包裹序号
     *
     * @param packageSerialNumber
     */
    public void setPackageSerialNumber(Integer packageSerialNumber) {
        this.packageSerialNumber = packageSerialNumber;
    }
}
