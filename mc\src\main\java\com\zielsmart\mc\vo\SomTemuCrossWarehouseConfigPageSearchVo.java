package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
 * Temu跨境可售仓库配置的VO分页查询实体
 */
@Data
@Schema(title = "Temu跨境可售仓库配置分页查询实体", name = "SomTemuCrossWarehouseConfigPageSearchVo")
public class SomTemuCrossWarehouseConfigPageSearchVo extends PageSearchVo {

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "可售仓库", name = "useableWarehouseCode")
    private String useableWarehouseCode;

    @Schema(description = "可售库区", name = "useableStorageCode")
    private String useableStorageCode;


}
