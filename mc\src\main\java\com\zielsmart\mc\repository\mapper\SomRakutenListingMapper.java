package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomRakutenListing;
import com.zielsmart.mc.vo.SomRakutenListingPageSearchVo;
import com.zielsmart.mc.vo.SomRakutenListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-03-13
*/

@SqlResource("somRakutenListing")
public interface SomRakutenListingMapper extends BaseMapper<SomRakutenListing> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomRakutenListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomRakutenListingVo> queryByPage(@Param("searchVo")SomRakutenListingPageSearchVo searchVo, PageRequest pageRequest);
}
