package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 
* gen by 代码生成器 2024-09-11
*/

@Table(name="mc.som_shein_warehouse_info")
public class SomSheinWarehouseInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 仓库编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 仓库名称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 销售国家
	 */
	@Column("sale_country")
	private String saleCountry ;
	/**
	 * 创建类型
	 */
	@Column("create_type")
	private Integer createType ;
	/**
	 * 仓库类型
	 */
	@Column("warehouse_type")
	private Integer warehouseType ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;

	public SomSheinWarehouseInfo() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 仓库编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 仓库编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 仓库名称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 仓库名称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 销售国家
	*@return
	*/
	public String getSaleCountry(){
		return  saleCountry;
	}
	/**
	* 销售国家
	*@param  saleCountry
	*/
	public void setSaleCountry(String saleCountry ){
		this.saleCountry = saleCountry;
	}
	/**
	* 创建类型
	*@return
	*/
	public Integer getCreateType(){
		return  createType;
	}
	/**
	* 创建类型
	*@param  createType
	*/
	public void setCreateType(Integer createType ){
		this.createType = createType;
	}
	/**
	* 仓库类型
	*@return
	*/
	public Integer getWarehouseType(){
		return  warehouseType;
	}
	/**
	* 仓库类型
	*@param  warehouseType
	*/
	public void setWarehouseType(Integer warehouseType ){
		this.warehouseType = warehouseType;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}

}
