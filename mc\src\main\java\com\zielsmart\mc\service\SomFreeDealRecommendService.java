package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomFreeDealRecommend;
import com.zielsmart.mc.repository.mapper.SomFreeDealRecommendMapper;
import com.zielsmart.mc.vo.SomFreeDealRecommendItemVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendPageSearchVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomFreeDealRecommendService {

    @Resource
    private SomFreeDealRecommendMapper somFreeDealRecommendMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomFreeDealRecommendVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomFreeDealRecommendVo> queryByPage(SomFreeDealRecommendPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomFreeDealRecommendVo> pageResult = dynamicSqlManager.getMapper(SomFreeDealRecommendMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomFreeDealRecommendVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somFreeDealRecommendVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomFreeDealRecommendVo somFreeDealRecommendVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeDealRecommendVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somFreeDealRecommendVo.setAid(IdUtil.fastSimpleUUID());
        somFreeDealRecommendMapper.insert(ConvertUtils.beanConvert(somFreeDealRecommendVo, SomFreeDealRecommend.class));
    }

    /**
     * update
     * 修改
     *
     * @param somFreeDealRecommendVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomFreeDealRecommendVo somFreeDealRecommendVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeDealRecommendVo) || StrUtil.isEmpty(somFreeDealRecommendVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        somFreeDealRecommendMapper.createLambdaQuery()
                .andEq("aid", somFreeDealRecommendVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somFreeDealRecommendVo, SomFreeDealRecommend.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somFreeDealRecommendVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomFreeDealRecommendVo somFreeDealRecommendVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somFreeDealRecommendVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somFreeDealRecommendMapper.createLambdaQuery().andEq("aid", somFreeDealRecommendVo.getAid()).delete();
    }

    /**
     * queryBySiteAndDealType
     * 通过站点和类型查询
     * @param pageSearchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomFreeDealRecommendVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomFreeDealRecommendItemVo> queryBySiteAndDealType(SomFreeDealRecommendPageSearchVo pageSearchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(pageSearchVo) || StrUtil.isBlank(pageSearchVo.getSite()) | ObjectUtil.isEmpty(pageSearchVo.getDealType())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        PageRequest pageRequest = DefaultPageRequest.of(pageSearchVo.getCurrent(), pageSearchVo.getPageSize());
        PageResult<SomFreeDealRecommendItemVo> pageResult = somFreeDealRecommendMapper.queryBySiteAndDealType(pageSearchVo,pageRequest);
        AtomicInteger index = new AtomicInteger();
        pageResult.getList().forEach(x->{
            String recommendDate = x.getRecommendDate();
            JSONObject jsonObject = JSONUtil.parseObj(recommendDate);
            x.setRecommendStartDate(new Date(jsonObject.getLong("recommendStartDate")));
            x.setRecommendEndDate(new Date(jsonObject.getLong("recommendEndDate")));
            x.setIndex(index.addAndGet(1));
        });
        return ConvertUtils.pageConvert(pageResult, SomFreeDealRecommendItemVo.class, pageSearchVo);
    }
}
