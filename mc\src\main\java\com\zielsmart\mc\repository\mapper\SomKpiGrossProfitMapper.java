package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomKpiGrossProfit;
import com.zielsmart.mc.vo.SomKpiGrossProfitPageSearchVo;
import com.zielsmart.mc.vo.SomKpiGrossProfitVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-12-26
 */

@SqlResource("somKpiGrossProfit")
public interface SomKpiGrossProfitMapper extends BaseMapper<SomKpiGrossProfit> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomKpiGrossProfitVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomKpiGrossProfitVo> queryByPage(@Param("searchVo") SomKpiGrossProfitPageSearchVo searchVo, PageRequest pageRequest);
}
