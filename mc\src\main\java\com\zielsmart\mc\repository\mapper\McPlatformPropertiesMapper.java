package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McPlatformPropertiesPageSearchVo;
import com.zielsmart.mc.vo.McPlatformPropertiesVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-08-04
*/

@SqlResource("mcPlatformProperties")
public interface McPlatformPropertiesMapper extends BaseMapper<McPlatformProperties> {

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McPlatformPropertiesVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McPlatformPropertiesVo> queryByPage(@Param("searchVo")McPlatformPropertiesPageSearchVo searchVo, PageRequest pageRequest);

    McPlatformPropertiesVo getPlatformProperties(@Param("searchVo")McPlatformPropertiesVo searchVo);

}
