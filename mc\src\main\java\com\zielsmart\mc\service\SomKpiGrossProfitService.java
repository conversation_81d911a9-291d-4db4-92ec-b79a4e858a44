package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomKpiGrossProfit;
import com.zielsmart.mc.repository.mapper.SomKpiGrossProfitMapper;
import com.zielsmart.mc.vo.SomKpiGrossProfitPageSearchVo;
import com.zielsmart.mc.vo.SomKpiGrossProfitVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomKpiGrossProfitService {
    
    @Resource
    private SomKpiGrossProfitMapper somKpiGrossProfitMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomKpiGrossProfitVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomKpiGrossProfitVo> queryByPage(SomKpiGrossProfitPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomKpiGrossProfitVo> pageResult = dynamicSqlManager.getMapper(SomKpiGrossProfitMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomKpiGrossProfitVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somKpiGrossProfitVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomKpiGrossProfitVo somKpiGrossProfitVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somKpiGrossProfitVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somKpiGrossProfitVo.setAid(IdUtil.fastSimpleUUID());
        //TODO 根据情况判断是否需要添加创建人信息
        somKpiGrossProfitMapper.insert(ConvertUtils.beanConvert(somKpiGrossProfitVo, SomKpiGrossProfit.class));
    }

    /**
     * update
     * 修改
     * @param somKpiGrossProfitVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomKpiGrossProfitVo somKpiGrossProfitVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somKpiGrossProfitVo) || StrUtil.isEmpty(somKpiGrossProfitVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somKpiGrossProfitMapper.createLambdaQuery()
                .andEq("aid",somKpiGrossProfitVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somKpiGrossProfitVo, SomKpiGrossProfit.class));
    }

    /**
     * delete
     * 删除
     * @param somKpiGrossProfitVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomKpiGrossProfitVo somKpiGrossProfitVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somKpiGrossProfitVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        // somKpiGrossProfitMapper.createLambdaQuery().andIn("aid", somKpiGrossProfitVo.getAidList()).delete();
        somKpiGrossProfitMapper.createLambdaQuery().andEq("aid",somKpiGrossProfitVo.getAid()).delete();
    }
}
