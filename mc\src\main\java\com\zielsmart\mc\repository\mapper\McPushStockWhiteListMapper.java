package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McPushStockWhiteListPageSearchVo;
import com.zielsmart.mc.vo.McPushStockWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-12-22
*/

@SqlResource("mcPushStockWhiteList")
public interface McPushStockWhiteListMapper extends BaseMapper<McPushStockWhiteList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McPushStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McPushStockWhiteListVo> queryByPage(@Param("searchVo")McPushStockWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
