package com.zielsmart.mc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.enums.AmazonSimplePublishStatusEnum;
import com.zielsmart.mc.enums.ConsignmentSalesTypeEnum;
import com.zielsmart.mc.enums.PublishSellerSkuGenerateType;
import com.zielsmart.mc.enums.PublishTemplateMappingType;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.service.mdm.MdmRequestService;
import com.zielsmart.mc.util.FileMappingExportUtil;
import com.zielsmart.mc.vo.SomAmazonSimplePublishImportVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishVo;
import com.zielsmart.mc.vo.ZlccAmazonMdmVo;
import com.zielsmart.mc.vo.mdm.MdmCustomExhibitGoods;
import com.zielsmart.web.basic.exception.BusinessException;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.file.FileSearchRequest;
import com.zielsmart.web.basic.file.FileSearchResponse;
import com.zielsmart.web.basic.file.IFileHandler;
import com.zielsmart.web.basic.file.s3.S3FileSearch;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zielsmart.mc.McConstants.PLATFORM_AMAZON;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Amazon简单上货管理
 * @date 2025-07-18 10:15:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
public class SomAmazonSimplePublishService {

    @Resource
    private SomAmazonSimplePublishMapper somAmazonSimplePublishMapper;

    @Resource
    private SomAmazonSimplePublishCategoryTemplateMapper somAmazonSimplePublishCategoryTemplateMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private McPlatformPropertiesMapper mcPlatformPropertiesMapper;

    @Resource
    private SomAmazonSimplePublishTemplateMappingMapper somAmazonSimplePublishTemplateMappingMapper;

    @Resource
    private SomPublishRequestMdmLogMapper somPublishRequestMdmLogMapper;

    @Resource
    private SomPublishBasicsConfigMapper somPublishBasicsConfigMapper;

    @Resource
    @Qualifier("s3FileHandlerChina")
    private IFileHandler fileHandler;

    @Resource
    private MdmRequestService mdmRequestService;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return 分页结果
     */
    public PageVo<SomAmazonSimplePublishVo> queryByPage(SomAmazonSimplePublishPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomAmazonSimplePublishVo> pageResult = somAmazonSimplePublishMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomAmazonSimplePublishVo.class, searchVo);
    }

    /**
     * 批量新增简单上货
     *
     * @param recommendPublishLists 推荐上货清单
     * @param tokenUser 当前登录用户
     */
    public void batchSimplePublish(List<SomRecommendPublishList> recommendPublishLists, TokenUserInfo tokenUser) throws Exception {
        if (CollUtil.isEmpty(recommendPublishLists)) {
            return;
        }
        // 基础核验
        checkSimplePublishParams(recommendPublishLists);
        // 判断此产品是否已经简单上货
        checkSimplePublishExist(recommendPublishLists);
        // 调用mdm获取数据，如果查询失败/未查询到，则返回错误
        List<ZlccAmazonMdmVo> mdmVos = mdmRequestService.buildRequestMdmParams(recommendPublishLists);
        List<Map<String, Object>> mdmExhibitGoodsData = mdmRequestService.getMdmExhibitGoodsData(mdmVos);
//        Map<String, Map<String, Object>> mdmGoodsMap = checkMdmExhibitGoodsDataExist(recommendPublishLists, mdmExhibitGoodsData);
        // 转换
        Map<String, Map<String, Object>> mdmGoodsMap = new HashMap<>();
        mdmExhibitGoodsData.forEach(data -> {
            String key = StrUtil.concat(true, MapUtil.getStr(data, "productNum"), "_", MapUtil.getStr(data, "customNum"));
            mdmGoodsMap.put(key, data);
        });
        // 核验刊登基础配置是否存在
        Map<String, SomPublishBasicsConfig> basicsConfigMap = checkSimplePublishBasicsConfigExist(recommendPublishLists);
        // 数据处理
        List<SomAmazonSimplePublish> publishes = buildAmazonSimplePublish(recommendPublishLists);
        // 获取所需要的字典
        Map<String, List<McDictionaryInfo>> dictMap = getSimplePublishDictionary();
        // 查询[平台站点属性配置]
        List<String> sites = publishes.stream().map(SomAmazonSimplePublish::getSite).distinct().collect(Collectors.toList());
        List<McPlatformProperties> platformProperties = mcPlatformPropertiesMapper.createLambdaQuery().andEq("platform", PLATFORM_AMAZON).andIn("site", sites).select();
        Map<String, String> currencyCodeMap = platformProperties.stream().collect(Collectors.toMap(McPlatformProperties::getSite, McPlatformProperties::getCurrencyCode, (x1, x2) -> x1));
        // 整理数据
        Date now = DateTime.now().toJdkDate();
        List<SomPublishRequestMdmLog> mdmLogs = new ArrayList<>();
        for (SomAmazonSimplePublish publish : publishes) {
            // 填充MDM信息
            fillSimplePublishMdmGoods(publish, mdmGoodsMap);
            // 填充字典
            fillSimplePublishDict(publish, dictMap);
            // 填充展示码
            fillSimplePublishSellerSku(publish, basicsConfigMap);
            // 币种
            publish.setCurrency(currencyCodeMap.get(publish.getSite()));
            publish.setAid(IdUtil.fastSimpleUUID());
            publish.setCreateNum(tokenUser.getJobNumber());
            publish.setCreateName(tokenUser.getUserName());
            publish.setCreateTime(now);
            // 构建请求日志
            SomPublishRequestMdmLog mdmLog = buildMdmLog(publish, mdmExhibitGoodsData.get(0));
            mdmLogs.add(mdmLog);
        }
        // 入库
        somAmazonSimplePublishMapper.insertBatch(publishes);
        // mdm请求日志表，后期稳定可以删除
        somPublishRequestMdmLogMapper.insertBatch(mdmLogs);
    }

    /**
     * 导入
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    public void importExcel(List<SomAmazonSimplePublishImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        Date now = DateTime.now().toJdkDate();
        List<SomAmazonSimplePublish> simplePublishes = new ArrayList<>();
        for (SomAmazonSimplePublishImportVo importVo : importVos) {
            SomAmazonSimplePublish simplePublish = buildImportAmazonSimplePublish(importVo);
            simplePublish.setModifyNum(tokenUser.getJobNumber());
            simplePublish.setModifyName(tokenUser.getUserName());
            simplePublish.setModifyTime(now);
            simplePublishes.add(simplePublish);
        }
        somAmazonSimplePublishMapper.importUpdateBatch(simplePublishes);
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64
     */
    public String export(SomAmazonSimplePublishPageSearchVo searchVo) throws Exception {
        // 站点与亚马逊类目不能为空
        String site = searchVo.getSite();
        String platformCategoryId = searchVo.getPlatformCategoryId();
        if (!StrUtil.isAllNotEmpty(site, platformCategoryId)) {
            throw new ValidateException("站点、亚马逊类目不能为空！");
        }
        // 站点与亚马逊类目必须配置了模版
        SomAmazonSimplePublishCategoryTemplate simplePublishCategoryTemplate = checkSimpleCategoryTemplateExist(site, platformCategoryId);
        // 查询未发布数据
        searchVo.setStatus(AmazonSimplePublishStatusEnum.NOT_PUBLISH.getStatus());
        List<SomAmazonSimplePublish> simplePublishes = somAmazonSimplePublishMapper.querySimplePublishExportData(searchVo);
        if (CollUtil.isEmpty(simplePublishes)) {
            throw new ValidateException("导出数据为空！");
        }
        // 导出数据汇总
        List<Map<String, Object>> exportDate = handleExportData(simplePublishes);
        String templateUrl = simplePublishCategoryTemplate.getTemplateUrl();
        // 查询s3文件
        FileSearchRequest fileSearchRequest = S3FileSearch.builder().key(templateUrl).build();
        FileSearchResponse fileSearchResponse = fileHandler.search(fileSearchRequest);
        if (fileSearchResponse == null) {
            throw new ValidateException("模版文件不存在！");
        }
        byte[] buff = Base64.getDecoder().decode(fileSearchResponse.getFileBase64Content());
        InputStream inputStream = new ByteArrayInputStream(buff);
        Integer sheetNum = simplePublishCategoryTemplate.getSheetNum();
        // 需要读取指定sheet页，指定第3行(index=2) (需要注意的是从0开始，但是类目模版配置的是从1开始的)
        sheetNum = sheetNum - 1;
        try {
            byte[] bytes = FileMappingExportUtil.processAndExportFile(inputStream, sheetNum, 2, exportDate);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            log.error(e.toString());
            throw new BusinessException("导出失败" + e.toString());
        }
    }

    /**
     * 基础核验数据
     *
     * @param importVos 导入行数据
     * @return errors
     */
    private List<String> checkImportData(List<SomAmazonSimplePublishImportVo> importVos) {
        // 获取导入字典
        Map<String, Map<String, String>> importDictMap = getImportDictionaryMap();
        // 报错需要提示出行号
        int index = 2;
        // 核验是否重复
        Set<String> repeatCheckSet = new HashSet<>();
        List<String> errors = new ArrayList<>();
        for (SomAmazonSimplePublishImportVo importVo : importVos) {
            // 行号报错
            String lineErrorMsg = StrUtil.concat(true, "第", String.valueOf(index), "行：");
            // 核验必填字段
            String site = importVo.getSite();
            String sku = importVo.getSku();
            String consignmentSalesStr = importVo.getConsignmentSalesStr();
            String itemName = importVo.getItemName();
            String priceStr = importVo.getPriceStr();
            if (!StrUtil.isAllNotEmpty(site, sku, consignmentSalesStr, itemName, priceStr)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "必填项不能为空！"));
                index++;
                continue;
            }
            // 核验是否重复
            String key = StrUtil.concat(true, importVo.getSite(), importVo.getSku(), importVo.getConsignmentSalesStr());
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "站点、SKU、是否寄售数据重复！"));
            }
            repeatCheckSet.add(key);
            // 核验是否寄售是否正确
            List<String> consignmentSalesList = Arrays.asList("是", "否");
            if (!consignmentSalesList.contains(consignmentSalesStr)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "是否寄售有误，仅支持[是/否]！"));
            }
            importVo.setConsignmentSales("是".equals(consignmentSalesStr) ? 1 : 0);
            // 核验品牌是否正确
            Map<String, String> brandMap = importDictMap.get("Brand");
            String brand = importVo.getBrand();
            if (StrUtil.isNotEmpty(brand) && !brandMap.containsKey(brand)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("品牌[{}]数据有误！", brand)));
            }
            // 核验价格是否正确
            try {
                importVo.setPrice(new BigDecimal(priceStr));
            } catch (Exception e) {
                errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("价格[{}]数据有误！", priceStr)));
            }
            // 核验内箱第一长
            String packageLengthStr = importVo.getPackageLengthStr();
            if (StrUtil.isNotEmpty(packageLengthStr)) {
                try {
                    importVo.setPackageLength(new BigDecimal(packageLengthStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("内箱第一长[{}]数据有误！", packageLengthStr)));
                }
            }
            // 核验内箱第二长
            String packageWidthStr = importVo.getPackageWidthStr();
            if (StrUtil.isNotEmpty(packageWidthStr)) {
                try {
                    importVo.setPackageWidth(new BigDecimal(packageWidthStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("内箱第二长[{}]数据有误！", packageWidthStr)));
                }
            }
            // 核验内箱第三长
            String packageHeightStr = importVo.getPackageHeightStr();
            if (StrUtil.isNotEmpty(packageHeightStr)) {
                try {
                    importVo.setPackageHeight(new BigDecimal(packageHeightStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("内箱第三长[{}]数据有误！", packageHeightStr)));
                }
            }
            // 核验内箱毛重
            String packageWeightStr = importVo.getPackageWeightStr();
            if (StrUtil.isNotEmpty(packageWeightStr)) {
                try {
                    importVo.setPackageWeight(new BigDecimal(packageWeightStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("内箱毛重[{}]数据有误！", packageWeightStr)));
                }
            }
            // 核验 List Price
            String listPriceWithTaxStr = importVo.getListPriceWithTaxStr();
            if (StrUtil.isNotEmpty(listPriceWithTaxStr)) {
                try {
                    importVo.setListPriceWithTax(new BigDecimal(listPriceWithTaxStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, lineErrorMsg, StrUtil.format("List Price[{}]数据有误！", listPriceWithTaxStr)));
                }
            }
            List<String> required = Arrays.asList("Yes", "No");
            // 核验是否需要电池
            String batteriesRequired = importVo.getBatteriesRequired();
            if (StrUtil.isNotEmpty(batteriesRequired) && !required.contains(batteriesRequired)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "是否需要电池有误，仅支持[Yes/No]！"));
            }
            // 是否包含电池
            String batteriesIncluded = importVo.getBatteriesIncluded();
            if (StrUtil.isNotEmpty(batteriesIncluded) && !required.contains(batteriesIncluded)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "是否包含电池有误，仅支持[Yes/No]！"));
            }
            index++;
        }
        return errors;
    }

    /**
     * 获取导入字典，导入文件中填充的是字典的
     * Brand：品牌
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, String>> getImportDictionaryMap() {
        List<String> typeCodes = Collections.singletonList("Brand");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1)));
    }

    /**
     * 获取简单上货字典
     * AmazonSimplePublishUpdateDelete：Amazon简单上货模板中的操作标识
     * AmazonSimplePublishUnitCountType：Amazon简单上货PCS单位
     * Amazon：Amazon
     * AmazonSimplePublishFulfillmentCenterId：Amazon简单上货发货方式
     *
     * @return key:item_type_code,value:[key:itemLabel,value:itemValue]
     */
    private Map<String, List<McDictionaryInfo>> getSimplePublishDictionary() {
        List<String> typeCodes = Arrays.asList("AmazonSimplePublishUpdateDelete", "AmazonSimplePublishUnitCountType", PLATFORM_AMAZON, "AmazonSimplePublishFulfillmentCenterId");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode));
    }

    /**
     * 简单上货填充MDM信息
     *  @param publish 简单上货数据
     * @param mdmGoodsMap MDM商品信息
     */
    private void fillSimplePublishMdmGoods(SomAmazonSimplePublish publish, Map<String, Map<String, Object>> mdmGoodsMap) {
//        String key = StrUtil.concat(true, publish.getSku(), "_", publish.getCustomerCode());
//        Map<String, Object> goodsMap = mdmGoodsMap.get(key);
        String key = "BBC310W01_10000003";
        Map<String, Object> goodsMap = mdmGoodsMap.get(key);
        MdmCustomExhibitGoods mdmCustomExhibitGoods = BeanUtil.mapToBean(goodsMap, MdmCustomExhibitGoods.class, true);
        publish.setCountryOfOrigin(mdmCustomExhibitGoods.getMarketdetailOriCountry());
        publish.setItemName(mdmCustomExhibitGoods.getCopywritingTitle());
        String isItComplete = mdmCustomExhibitGoods.getIsItComplete();
        if (StrUtil.isEmpty(isItComplete)) {
            publish.setMaterialIsComplete(null);
        } else {
            publish.setMaterialIsComplete(isItComplete.equals("Yes") ? 1 : 0);
        }
        // 美国站点用：productSizeIn，其他用 productSize
        String site = publish.getSite();
        publish.setSizeName(site.equals("Amazon.com") ? mdmCustomExhibitGoods.getProductSizeIn() : mdmCustomExhibitGoods.getProductSize());
        publish.setPackageLength(new BigDecimal(mdmCustomExhibitGoods.getMarketdetailFirstLength()));
        publish.setPackageWidth(new BigDecimal(mdmCustomExhibitGoods.getMarketdetailSecondLength()));
        publish.setPackageHeight(new BigDecimal(mdmCustomExhibitGoods.getMarketdetailThirdLength()));
        publish.setPackageWeight(new BigDecimal(mdmCustomExhibitGoods.getMarketdetailGrossWeight()));
        publish.setColorName(mdmCustomExhibitGoods.getProductColorFst());
        String copywritingDesHtmml = mdmCustomExhibitGoods.getCopywritingDesHtmml();
        publish.setProductDescription(StrUtil.isEmpty(copywritingDesHtmml) ? publish.getBrand() : copywritingDesHtmml);
        publish.setPackageLengthUnitOfMeasure("cm");
        publish.setPackageWidthUnitOfMeasure("cm");
        publish.setPackageHeightUnitOfMeasure("cm");
        publish.setPackageWeightUnitOfMeasure("g");
    }

    /**
     * 简单上货填充字典
     *
     * @param publish 简单上货
     * @param dictMap 字典
     */
    private void fillSimplePublishDict(SomAmazonSimplePublish publish, Map<String, List<McDictionaryInfo>> dictMap) {
        String site = publish.getSite();
        // PCS单位，如果不包含，取 OTHER
        Map<String, String> unitCountTypeMap = dictMap.get("AmazonSimplePublishUnitCountType").stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        if (unitCountTypeMap.containsKey(site)) {
            publish.setUnitCountType(unitCountTypeMap.get(site));
        } else {
            publish.setUnitCountType(unitCountTypeMap.get("OTHER"));
        }
        // 模板中的操作标识，如果不包含，取 OTHER
        Map<String, String> updateDeleteMap = dictMap.get("AmazonSimplePublishUpdateDelete").stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        if (updateDeleteMap.containsKey(site)) {
            publish.setUpdateDelete(updateDeleteMap.get(site));
        } else {
            publish.setUpdateDelete(updateDeleteMap.get("OTHER"));
        }
        // 发货方式：查询字典表（Amazon），获取到站点所对应的销售市场
        Map<String, String> amazonMap = dictMap.get(PLATFORM_AMAZON).stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
        // 如果寄售标识为"是"，则需要查询字典表
        if (ConsignmentSalesTypeEnum.YES.getType().equals(publish.getConsignmentSales())) {
            if (amazonMap.containsKey(site)) {
                // 根据销售市场，查询字典（AmazonSimplePublishFulfillmentCenterId）获取发货方式
                String itemValue2 = amazonMap.get(site);
                Map<String, String> fulfillmentCenterIdMap = dictMap.get("AmazonSimplePublishFulfillmentCenterId").stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
                publish.setFulfillmentCenterId(fulfillmentCenterIdMap.get(itemValue2));
            }
        } else {
            // 如果寄售标识为"否"，则默认填充 DEFAULT
            publish.setFulfillmentCenterId("DEFAULT");
        }
    }

    /**
     * 简单上货清单填充展示码
     *
     * @param publish 简单上货清单
     * @param basicsConfigMap 刊登基础配置
     */
    private void fillSimplePublishSellerSku(SomAmazonSimplePublish publish, Map<String, SomPublishBasicsConfig> basicsConfigMap) {
        String key = StrUtil.concat(true, publish.getCustomerCode(), String.valueOf(publish.getConsignmentSales()));
        SomPublishBasicsConfig basicsConfig = basicsConfigMap.get(key);
        String sku = publish.getSku();
        // 展示码生成类型
        PublishSellerSkuGenerateType sellerSkuGenerateType = PublishSellerSkuGenerateType.of(basicsConfig.getSellerSkuGenerateType());
        // 需要增加的固定字符
        String sellerSkuGenerateChar = basicsConfig.getSellerSkuGenerateChar();
        String sellerSku;
        switch (sellerSkuGenerateType) {
            case MIDDLE:
                // 展示码生成规则为中间时，需要从第几个字符开始增加特定字符
                Integer sellerSkuGenerateIndex = basicsConfig.getSellerSkuGenerateIndex();
                int index = Math.max(0, Math.min(sellerSkuGenerateIndex, sku.length()));
                sellerSku = sku.substring(0, index) + sellerSkuGenerateChar + sku.substring(index);
                break;
            case PREFIX:
                sellerSku = sellerSkuGenerateChar + sku;
                break;
            case SUFFIX:
                sellerSku = sku + sellerSkuGenerateChar;
                break;
            default:
                sellerSku = sku;
        }
        publish.setSellerSku(sellerSku);
        // 组件编号(取展示码)
        publish.setPartNumber(sellerSku);
    }

    /**
     * 入参基础核验
     *
     * @param recommendPublishLists 推荐上货清单
     * @throws ValidateException ex
     */
    private void checkSimplePublishParams(List<SomRecommendPublishList> recommendPublishLists) throws ValidateException {
        List<String> errors = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            String errorMsgPrefix = StrUtil.concat(true, "客户简称:", recommendPublishList.getCustomerShortName(), ",SKU:", recommendPublishList.getSku(), " ");
            // 用户选择的数据，它们的「需要简单上货？」必须为"是"
            Integer simplePublishTag = recommendPublishList.getSimplePublishTag();
            if (simplePublishTag != 1) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 不支持简单上货！"));
            }
            // 用户选择的数据，它们的「EAN」不允许为空
            String ean = recommendPublishList.getEan();
            if (StrUtil.isEmpty(ean)) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 未维护「EAN」，请先维护EAN！"));
            }
            // 用户选择的数据，它们的「IPQ」不允许为空
            Integer itemPackageQuantity = recommendPublishList.getItemPackageQuantity();
            if (itemPackageQuantity == null) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 未维护「IPQ」，请先维护IPQ！"));
            }
            // 用户选择的数据，它们的「平台类目ID」不允许为空
            String platformCategoryId = recommendPublishList.getPlatformCategoryId();
            if (StrUtil.isEmpty(platformCategoryId)) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 未维护「平台类目ID」，请先维护「平台类目ID」！"));
            }
            // 用户选择的数据，平台是"Amazon"并且「是否寄售」="是"，「不可售处理」为必填项
            Integer consignmentSales = recommendPublishList.getConsignmentSales();
            Integer consignmentStockProcessing = recommendPublishList.getConsignmentStockProcessing();
            if (consignmentSales == 1 && consignmentStockProcessing == null) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, ",是否寄售:是 未维护「不可售处理」，请先维护「不可售处理」！"));
            }
            // 用户选择的数据，平台是"Amazon"，「VC合作标识」为必填项
            Integer vcCooperation = recommendPublishList.getVcCooperation();
            if (vcCooperation == null) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 未维护「VC合作标识」，请先维护「VC合作标识」！"));
            }
            // 用户选择的数据，它们的「业务负责人」、「业务助理」、「业务组」不允许为空
            String salesGroupCode = recommendPublishList.getSalesGroupCode();
            String salesGroupEmptCode = recommendPublishList.getSalesGroupEmptCode();
            String operationEmptCode = recommendPublishList.getOperationEmptCode();
            if (!StrUtil.isAllNotEmpty(salesGroupCode, salesGroupEmptCode, operationEmptCode)) {
                errors.add(StrUtil.concat(true, errorMsgPrefix, " 未维护「业务负责人」、「业务助理」、「业务组」，请先维护「业务负责人」、「业务助理」、「业务组」"));
            }
        }
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
    }

    /**
     * 核验简单上货是否存在
     *
     * @param recommendPublishLists 推荐上货清单
     */
    private void checkSimplePublishExist(List<SomRecommendPublishList> recommendPublishLists) throws ValidateException {
        List<String> sites = recommendPublishLists.stream().map(SomRecommendPublishList::getCustomerShortName).collect(Collectors.toList());
        List<String> skus = recommendPublishLists.stream().map(SomRecommendPublishList::getSku).collect(Collectors.toList());
        List<Integer> consignmentSales = recommendPublishLists.stream().map(SomRecommendPublishList::getConsignmentSales).collect(Collectors.toList());
        if (CollUtil.isEmpty(sites) || CollUtil.isEmpty(skus) || CollUtil.isEmpty(consignmentSales)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        List<SomAmazonSimplePublish> simplePublishes = somAmazonSimplePublishMapper.createLambdaQuery().andIn("site", sites).andIn("sku", skus).andIn("consignment_sales", consignmentSales).select();
        List<String> exitKeys = simplePublishes.stream().map(x -> StrUtil.concat(true, x.getSite(), x.getSku(), String.valueOf(x.getConsignmentSales()))).collect(Collectors.toList());
        List<String> errors = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            String key = StrUtil.concat(true, recommendPublishList.getCustomerShortName(), recommendPublishList.getSku(), String.valueOf(recommendPublishList.getConsignmentSales()));
            if (exitKeys.contains(key)) {
                String errorMsg = StrUtil.concat(true, "站点:", recommendPublishList.getCustomerShortName(), ",SKU:", recommendPublishList.getSku());
                Integer consignmentSale = recommendPublishList.getConsignmentSales();
                errorMsg = StrUtil.concat(true, errorMsg, ",是否寄售:", consignmentSale == 1 ? "是" : "否", " 此产品已经简单上货，不允许简单上货！");
                errors.add(errorMsg);
            }
        }
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
    }


    /**
     * 构建导入的SomAmazonSimplePublish
     *
     * @param importVo 导入行数据
     * @return SomAmazonSimplePublish
     */
    private SomAmazonSimplePublish buildImportAmazonSimplePublish(SomAmazonSimplePublishImportVo importVo) {
        SomAmazonSimplePublish simplePublish = new SomAmazonSimplePublish();
        simplePublish.setSite(importVo.getSite());
        simplePublish.setSku(importVo.getSku());
        simplePublish.setConsignmentSales(importVo.getConsignmentSales());
        simplePublish.setBrand(importVo.getBrand());
        simplePublish.setSellerSku(importVo.getSellerSku());
        simplePublish.setItemName(importVo.getItemName());
        simplePublish.setProductDescription(importVo.getProductDescription());
        simplePublish.setShippingTemplateGroup(importVo.getShippingTemplateGroup());
        simplePublish.setCountryOfOrigin(importVo.getCountryOfOrigin());
        simplePublish.setPrice(importVo.getPrice());
        simplePublish.setSizeName(importVo.getSizeName());
        simplePublish.setPackageLength(importVo.getPackageLength());
        simplePublish.setPackageWidth(importVo.getPackageWidth());
        simplePublish.setPackageHeight(importVo.getPackageHeight());
        simplePublish.setPackageWeight(importVo.getPackageWeight());
        simplePublish.setListPriceWithTax(importVo.getListPriceWithTax());
        simplePublish.setPackageLengthUnitOfMeasure(importVo.getPackageLengthUnitOfMeasure());
        simplePublish.setPackageWidthUnitOfMeasure(importVo.getPackageWidthUnitOfMeasure());
        simplePublish.setPackageHeightUnitOfMeasure(importVo.getPackageHeightUnitOfMeasure());
        simplePublish.setPackageWeightUnitOfMeasure(importVo.getPackageWeightUnitOfMeasure());
        simplePublish.setColorName(importVo.getColorName());
        simplePublish.setListPriceWithTax(importVo.getListPriceWithTax());
        simplePublish.setEuEnergyEfficiencyRating(importVo.getEuEnergyEfficiencyRating());
        simplePublish.setEprelRegistrationNumber(importVo.getEprelRegistrationNumber());
        simplePublish.setBatteriesRequired(importVo.getBatteriesRequired());
        simplePublish.setBatteriesIncluded(importVo.getBatteriesIncluded());
        return simplePublish;
    }


    /**
     * 构建批量简单上货的 SomAmazonSimplePublish
     *
     * @param recommendPublishLists 推荐上货清单
     * @return 简单上货清单集合
     */
    private List<SomAmazonSimplePublish> buildAmazonSimplePublish(List<SomRecommendPublishList> recommendPublishLists) {
        List<SomAmazonSimplePublish> simplePublishes = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            SomAmazonSimplePublish simplePublish = new SomAmazonSimplePublish();
            simplePublish.setCustomerCode(recommendPublishList.getCustomerCode());
            // 亚马逊的客户简称就是站点，customer_short_name
            simplePublish.setSite(recommendPublishList.getCustomerShortName());
            simplePublish.setPlatformCategoryId(recommendPublishList.getPlatformCategoryId());
            simplePublish.setPlatformCategoryTypeKeyword(recommendPublishList.getPlatformCategoryTypeKeyword());
            simplePublish.setMainProductImageLocator(recommendPublishList.getMainImage());
            simplePublish.setSku(recommendPublishList.getSku());
            simplePublish.setEan(recommendPublishList.getEan());
            simplePublish.setConsignmentSales(recommendPublishList.getConsignmentSales());
            simplePublish.setEstimateArrivalWarehouseDate(recommendPublishList.getEstimateArrivalWarehouseDate());
            simplePublish.setBrand(recommendPublishList.getBrand());
            simplePublish.setConsignmentStockProcessing(recommendPublishList.getConsignmentStockProcessing());
            simplePublish.setItemPackageQuantity(recommendPublishList.getItemPackageQuantity());
            // 一个订单最多订购数量,默认值:5
            simplePublish.setMaxOrderQuantity(5);
            simplePublish.setUnitCount(recommendPublishList.getPcs());
            // 库存数量,默认值:0
            simplePublish.setQuantity(0);
            simplePublish.setMainProductImageLocator(recommendPublishList.getMainImage());
            // 危险品法规,默认值:Not Applicable
            simplePublish.setDangerousGoodsRegulations("Not Applicable");
            // GPSR安全认证,默认值:Yes
            simplePublish.setGpsrSafetyAttestation("Yes");
            // 发布状态,默认值:10
            simplePublish.setStatus(AmazonSimplePublishStatusEnum.NOT_PUBLISH.getStatus());
            simplePublish.setSalesGroupCode(recommendPublishList.getSalesGroupCode());
            simplePublish.setSalesGroupEmptCode(recommendPublishList.getSalesGroupEmptCode());
            simplePublish.setOperationEmptCode(recommendPublishList.getOperationEmptCode());
            // 产品ID类型,默认:EAN
            simplePublish.setExternalProductIdType("EAN");
            // 条件类型，默认:New
            simplePublish.setConditionType("New");

            simplePublishes.add(simplePublish);
        }
        return simplePublishes;
    }



    /**
     * 处理导出数据
     *
     * @param simplePublishes 简单上货数据
     * @return Map 数据 key:模版字段,value:值
     */
    private List<Map<String, Object>> handleExportData(List<SomAmazonSimplePublish> simplePublishes) throws ValidateException {
        List<Map<String, Object>> exportData = new ArrayList<>();
        List<SomAmazonSimplePublishTemplateMapping> templateMappings = somAmazonSimplePublishTemplateMappingMapper.all();
        if (CollUtil.isEmpty(templateMappings)) {
            throw new ValidateException("未配置简单上货模版映射数据！");
        }
        Map<String, SomAmazonSimplePublishTemplateMapping> mappingMap = templateMappings.stream().collect(
                Collectors.toMap(SomAmazonSimplePublishTemplateMapping::getTemplateField,
                        Function.identity()));
        for (SomAmazonSimplePublish simplePublish : simplePublishes) {
            Map<String, Object> exportMap = new HashMap<>();
            // simplePublish 转换成 map
            Map<String, Object> simplePublishMap = BeanUtil.beanToMap(simplePublish);
            for (Map.Entry<String, SomAmazonSimplePublishTemplateMapping> entry : mappingMap.entrySet()) {
                String templateField = entry.getKey();
                SomAmazonSimplePublishTemplateMapping mapping = entry.getValue();
                String type = mapping.getType();
                String mappingField = mapping.getMappingField();
                // 日期需要单独处理，后续如果有复杂的转换，可以单独创建一个类去处理
                if (PublishTemplateMappingType.DATE.getType().equals(type)) {
                    Date date = (Date) simplePublishMap.get(mappingField);
                    if (date != null) {
                        exportMap.put(templateField, DateUtil.format(date, mapping.getDateFormat()));
                    }
                } else {
                    exportMap.put(templateField, simplePublishMap.get(mappingField));
                }
            }
            exportData.add(exportMap);
        }
        return exportData;
    }

    /**
     * 核验类目模版配置
     *
     * @param site 站点
     * @param platformCategoryId 平台类目ID
     * @return 类目模版配置
     */
    private SomAmazonSimplePublishCategoryTemplate checkSimpleCategoryTemplateExist(String site, String platformCategoryId) throws ValidateException {
        SomAmazonSimplePublishCategoryTemplate simplePublishCategoryTemplate = somAmazonSimplePublishCategoryTemplateMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("platform_category_id", platformCategoryId)
                .single();
        if (simplePublishCategoryTemplate == null) {
            throw new ValidateException(StrUtil.format("站点[{}]类目ID[{}]未配置类目模板，请先配置类目模板！", site, platformCategoryId));
        }
        String templateUrl = simplePublishCategoryTemplate.getTemplateUrl();
        if (StrUtil.isEmpty(templateUrl)) {
            throw new ValidateException(StrUtil.format("站点[{}]类目ID[{}]未配置类目模版URL，请先配置类目模版！", site, platformCategoryId));
        }
        return simplePublishCategoryTemplate;
    }


    /**
     * 核验MDM返回数据是否存在
     *
     * @param recommendPublishLists 推荐上货清单
     * @param mdmExhibitGoodsData MDM返回商品信息
     * @return key:sku+客户简称,value:MDM返回商品信息
     */
    private Map<String, Map<String, Object>> checkMdmExhibitGoodsDataExist(List<SomRecommendPublishList> recommendPublishLists,
                                                                           List<Map<String, Object>> mdmExhibitGoodsData) throws ValidateException {
        // 转换
        Map<String, Map<String, Object>> goodsMap = new HashMap<>();
        mdmExhibitGoodsData.forEach(data -> {
            String key = StrUtil.concat(true, MapUtil.getStr(data, "productNum"), "_", MapUtil.getStr(data, "customNum"));
            goodsMap.put(key, data);
        });
        List<String> errors = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            String key = StrUtil.concat(true, recommendPublishList.getSku(), "_", recommendPublishList.getCustomerCode());
            if (!goodsMap.containsKey(key)) {
                errors.add(StrUtil.format("客户简称[{}]SKU[{}]查询MDM获取商品信息失败！", recommendPublishList.getCustomerShortName(), recommendPublishList.getSku()));
            }
        }
        if (!CollUtil.isEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        return goodsMap;
    }


    /**
     * 核验刊登基础配置是否存在
     *
     * @param recommendPublishLists 推荐上货清单
     * @return key:客户简称+是否寄售, value:基础配置
     */
    private Map<String, SomPublishBasicsConfig> checkSimplePublishBasicsConfigExist(List<SomRecommendPublishList> recommendPublishLists) throws ValidateException {
        List<SomPublishBasicsConfig> somPublishBasicsConfigs = somPublishBasicsConfigMapper.createLambdaQuery()
                .andEq("platform", PLATFORM_AMAZON)
                .select();
        // key:客户简称 + 是否寄售
        Map<String, SomPublishBasicsConfig> basicsConfigMap = somPublishBasicsConfigs.stream().collect(Collectors.toMap(
                data -> StrUtil.concat(true, data.getCustomerCode(),
                        String.valueOf(data.getConsignmentSales())), Function.identity(), (x1, x2) -> x1));
        List<String> errors = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            String key = StrUtil.concat(true, recommendPublishList.getCustomerCode(), String.valueOf(recommendPublishList.getConsignmentSales()));
            String desc = ConsignmentSalesTypeEnum.getDescByType(recommendPublishList.getConsignmentSales());
            if (!basicsConfigMap.containsKey(key)) {
                errors.add(StrUtil.format("客户简称[{}]是否寄售[{}]未配置刊登基础配置，无法生成展示码！", recommendPublishList.getCustomerShortName(), desc));
            } else {
                SomPublishBasicsConfig somPublishBasicsConfig = basicsConfigMap.get(key);
                Integer sellerSkuGenerateType = somPublishBasicsConfig.getSellerSkuGenerateType();
                if (sellerSkuGenerateType == null) {
                    errors.add(StrUtil.format("客户简称[{}]是否寄售[{}]刊登基础配置未设置展示码生成规则，无法生成展示码！", recommendPublishList.getCustomerShortName(), desc));
                }
            }
        }
        if (!CollUtil.isEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        return basicsConfigMap;
    }

    /**
     * 构建 mdm 请求日志
     *
     * @param publish 简单上货清单
     * @param mdmResponse mdm返回数据
     * @return SomPublishRequestMdmLog
     */
    private SomPublishRequestMdmLog buildMdmLog(SomAmazonSimplePublish publish, Map<String, Object> mdmResponse) {
        SomPublishRequestMdmLog mdmLog = new SomPublishRequestMdmLog();
        mdmLog.setAid(publish.getAid());
        mdmLog.setMdmResponse(JSONUtil.toJsonStr(JSONUtil.parseObj(mdmResponse, false)));
        mdmLog.setCustomerCode(publish.getCustomerCode());
        mdmLog.setPlatform(PLATFORM_AMAZON);
        mdmLog.setSku(publish.getSku());
        mdmLog.setCreateName(publish.getCreateName());
        mdmLog.setCreateNum(publish.getCreateNum());
        mdmLog.setCreateTime(publish.getCreateTime());
        return mdmLog;
    }

}
