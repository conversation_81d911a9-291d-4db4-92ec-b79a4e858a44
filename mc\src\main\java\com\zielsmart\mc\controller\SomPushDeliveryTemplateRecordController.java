package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomPushDeliveryTemplateRecordService;
import com.zielsmart.mc.vo.SomPushDeliveryTemplateRecordPageSearchVo;
import com.zielsmart.mc.vo.SomPushDeliveryTemplateRecordVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPushDeliveryTemplateRecordController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somPushDeliveryTemplateRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "运费模板推送报表管理")
public class SomPushDeliveryTemplateRecordController extends BasicController {

    @Resource
    SomPushDeliveryTemplateRecordService service;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomPushDeliveryTemplateRecordVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPushDeliveryTemplateRecordVo>> queryByPage(@RequestBody SomPushDeliveryTemplateRecordPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(service.queryByPage(searchVo));
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportExcel(@RequestBody SomPushDeliveryTemplateRecordPageSearchVo exportVo) throws ValidateException {
        String data = service.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
