package com.zielsmart.mc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Temu跨境供货价
 */
@Data
public class SomTemuCrossSkuSupplierPriceVo {

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "店铺ID", name = "productName")
    private String accountId;

    @Schema(description = "商品ID", name = "productId")
    private Long productId;

    @Schema(description = "Product Sku Id", name = "productSkuId")
    private Long productSkuId;

    @Schema(description = "供货价", name = "supplierPrice")
    private BigDecimal supplierPrice;

    @Schema(description = "币种", name = "currency")
    private String currency;

}
