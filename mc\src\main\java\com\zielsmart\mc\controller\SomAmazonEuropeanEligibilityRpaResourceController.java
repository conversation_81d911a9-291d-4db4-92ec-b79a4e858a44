package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomAmazonEuropeanEligibilityRpaResourceService;
import com.zielsmart.mc.vo.SomAmazonEuropeanEligibilityRpaResourcePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonEuropeanEligibilityRpaResourceVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonEuropeanEligibilityRpaResourceController
 * @description
 * @date 2024-02-21 15:25:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAmazonEuropeanEligibilityRpaResource", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Pan-Eu物流服务资格监控")
public class SomAmazonEuropeanEligibilityRpaResourceController extends BasicController {

    @Resource
    SomAmazonEuropeanEligibilityRpaResourceService somAmazonEuropeanEligibilityRpaResourceService;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonEuropeanEligibilityRpaResourceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonEuropeanEligibilityRpaResourceVo>> queryByPage(@RequestBody SomAmazonEuropeanEligibilityRpaResourcePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonEuropeanEligibilityRpaResourceService.queryByPage(searchVo));
    }

    /**
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomAmazonEuropeanEligibilityRpaResourcePageSearchVo searchVo) {
        String data = somAmazonEuropeanEligibilityRpaResourceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
