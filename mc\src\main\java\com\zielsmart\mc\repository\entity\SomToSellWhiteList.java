package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 跟卖白名单
* gen by 代码生成器 2023-04-20
*/

@Table(name="mc.som_to_sell_white_list")
public class SomToSellWhiteList implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 国家
	 */
	@Column("country")
	private String country ;
	/**
	 * 跟卖者店铺ID
	 */
	@Column("seller_id")
	private String sellerId ;
	/**
	 * 跟卖者店铺名称
	 */
	@Column("seller_name")
	private String sellerName ;
	/**
	 * 10.亚马逊官方 20.VC店铺 90.其他
	 */
	@Column("store_type")
	private Integer storeType ;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public SomToSellWhiteList() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 国家
	*@return
	*/
	public String getCountry(){
		return  country;
	}
	/**
	* 国家
	*@param  country
	*/
	public void setCountry(String country ){
		this.country = country;
	}
	/**
	* 跟卖者店铺ID
	*@return
	*/
	public String getSellerId(){
		return  sellerId;
	}
	/**
	* 跟卖者店铺ID
	*@param  sellerId
	*/
	public void setSellerId(String sellerId ){
		this.sellerId = sellerId;
	}
	/**
	* 跟卖者店铺名称
	*@return
	*/
	public String getSellerName(){
		return  sellerName;
	}
	/**
	* 跟卖者店铺名称
	*@param  sellerName
	*/
	public void setSellerName(String sellerName ){
		this.sellerName = sellerName;
	}
	/**
	* 10.亚马逊官方 20.VC店铺 90.其他
	*@return
	*/
	public Integer getStoreType(){
		return  storeType;
	}
	/**
	* 10.亚马逊官方 20.VC店铺 90.其他
	*@param  storeType
	*/
	public void setStoreType(Integer storeType ){
		this.storeType = storeType;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemark(){
		return  remark;
	}
	/**
	* 备注
	*@param  remark
	*/
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
