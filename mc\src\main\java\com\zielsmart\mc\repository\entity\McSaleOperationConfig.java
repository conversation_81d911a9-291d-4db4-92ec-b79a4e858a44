package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 产品负责人配置表
* gen by 代码生成器 2022-02-09
*/

@Table(name="mc.mc_sale_operation_config")
public class McSaleOperationConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * sku
	 */
	@Column("sku")
	private String sku ;


	/**
	 * 销售负责人工号
	 */
	@Column("head_of_sales_num")
	private String headOfSalesNum ;
	/**
	 * 运营负责人工号
	 */
	@Column("head_of_operations_num")
	private String headOfOperationsNum ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public McSaleOperationConfig() {
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	/**
	 * 销售负责人工号
	 *@return
	 */
	public String getHeadOfSalesNum(){
		return  headOfSalesNum;
	}
	/**
	 * 销售负责人工号
	 *@param  headOfSalesNum
	 */
	public void setHeadOfSalesNum(String headOfSalesNum ){
		this.headOfSalesNum = headOfSalesNum;
	}
	/**
	 * 运营负责人工号
	 *@return
	 */
	public String getHeadOfOperationsNum(){
		return  headOfOperationsNum;
	}
	/**
	 * 运营负责人工号
	 *@param  headOfOperationsNum
	 */
	public void setHeadOfOperationsNum(String headOfOperationsNum ){
		this.headOfOperationsNum = headOfOperationsNum;
	}
	/**
	 * 创建人工号
	 *@return
	 */
	public String getCreateNum(){
		return  createNum;
	}
	/**
	 * 创建人工号
	 *@param  createNum
	 */
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	 * 创建人姓名
	 *@return
	 */
	public String getCreateName(){
		return  createName;
	}
	/**
	 * 创建人姓名
	 *@param  createName
	 */
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	 * 创建时间
	 *@return
	 */
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	 * 创建时间
	 *@param  createTime
	 */
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	 * 最后修改人工号
	 *@return
	 */
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	 * 最后修改人工号
	 *@param  lastModifyNum
	 */
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	 * 最后修改人姓名
	 *@return
	 */
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	 * 最后修改人姓名
	 *@param  lastModifyName
	 */
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	 * 最后修改时间
	 *@return
	 */
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	 * 最后修改时间
	 *@param  lastModifyTime
	 */
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
