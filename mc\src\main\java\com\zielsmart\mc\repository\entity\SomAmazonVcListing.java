package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* VCListing信息表
* gen by 代码生成器 2024-01-23
*/

@Table(name="mc.som_amazon_vc_listing")
public class SomAmazonVcListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * EAN
	 */
	@Column("ean")
	private String ean ;
	/**
	 * SKU（展示码）
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 供应商编码
	 */
	@Column("vendor_code")
	private String vendorCode ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAmazonVcListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* EAN
	*@return
	*/
	public String getEan(){
		return  ean;
	}
	/**
	* EAN
	*@param  ean
	*/
	public void setEan(String ean ){
		this.ean = ean;
	}
	/**
	* SKU（展示码）
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* SKU（展示码）
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 供应商编码
	*@return
	*/
	public String getVendorCode(){
		return  vendorCode;
	}
	/**
	* 供应商编码
	*@param  vendorCode
	*/
	public void setVendorCode(String vendorCode ){
		this.vendorCode = vendorCode;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
