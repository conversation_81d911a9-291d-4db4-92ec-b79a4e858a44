package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
/*
 * LD&7DD从表
 * gen by 代码生成器 2022-08-11e
 */

@Table(name = "mc.som_free_add_deal_items")
public class SomFreeAddDealItems implements java.io.Serializable {

    //秒杀价毛利率
    @Column("deal_price_gross")
    private BigDecimal dealPriceGross;

    //近四周销售预测达成率
    @Column("completion_rate")
    private BigDecimal completionRate;

    //取消子ASIN 1 正常  0 取消  默认1
    @Column("status")
    private Integer status;


    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 主表ID
     */
    @Column("deal_id")
    private String dealId;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 当前售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 秒杀价
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * 秒杀折扣
     */
    @Column("deal_discount")
    private BigDecimal dealDiscount;
    /**
     * 优惠券折扣
     */
    @Column("coupon_discount")
    private BigDecimal couponDiscount;
    /**
     * promotion折扣
     */
    @Column("promotion_discount")
    private BigDecimal promotionDiscount;
    /**
     * 总折扣
     */
    @Column("total_discount")
    private BigDecimal totalDiscount;
    /**
     * 总折扣金额
     */
    @Column("total_discount_amount")
    private BigDecimal totalDiscountAmount;
    /**
     * 评分
     */
    @Column("score")
    private String score;
    /**
     * 秒杀数量
     */
    @Column("deal_quantity")
    private Integer dealQuantity;
    /**
     * 三级分类名称
     */
    @Column("category_name")
    private String categoryName;
    /**
     * 业务组
     */
    @Column("business_group_code")
    private String businessGroupCode;
    /**
     * 业务负责人
     */
    @Column("business_leader_code")
    private String businessLeaderCode;
    /**
     * 业务助理
     */
    @Column("business_operation_code")
    private String businessOperationCode;
    /**
     * 预估成交价
     */
    @Column("transaction_price")
    private BigDecimal transactionPrice;
    /**
     * 库存可售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;
    /**
     * 精选Asin 0否 1是
     */
    @Column("is_featured")
    private Integer isFeatured;

    /**
     * 错误信息(由RPA提供)
     */
    @Column("error_msg")
    private String errorMsg;

    private BigDecimal dealBurstCoefficient; // 活动预计爆发系数
    @Column("dms_last_30day")
    private BigDecimal dmsLast30day; // 近三十天DMS
    private BigDecimal categoryGross; // 三级分类近四周毛利率
    private BigDecimal expectedSalesVolume; // 活动预计销量
    private BigDecimal estimateOfSales; // 活动预计销售额
    private BigDecimal expectedGrossProfitMargin; // 活动预计毛利额
    private String approvalRole; // 审批角色 审批分支

    public SomFreeAddDealItems() {
    }

    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    public BigDecimal getDealPriceGross() {
        return dealPriceGross;
    }

    public void setDealPriceGross(BigDecimal dealPriceGross) {
        this.dealPriceGross = dealPriceGross;
    }

    public Integer getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Integer isFeatured) {
        this.isFeatured = isFeatured;
    }

    public Integer getisFeatured() {
        return isFeatured;
    }

    public void setisFeatured(Integer isFeatured) {
        this.isFeatured = isFeatured;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 主表ID
     *
     * @return
     */
    public String getDealId() {
        return dealId;
    }

    /**
     * 主表ID
     *
     * @param dealId
     */
    public void setDealId(String dealId) {
        this.dealId = dealId;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 当前售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 当前售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 秒杀价
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 秒杀价
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * 秒杀折扣
     *
     * @return
     */
    public BigDecimal getDealDiscount() {
        return dealDiscount;
    }

    /**
     * 秒杀折扣
     *
     * @param dealDiscount
     */
    public void setDealDiscount(BigDecimal dealDiscount) {
        this.dealDiscount = dealDiscount;
    }

    /**
     * 优惠券折扣
     *
     * @return
     */
    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    /**
     * 优惠券折扣
     *
     * @param couponDiscount
     */
    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    /**
     * promotion折扣
     *
     * @return
     */
    public BigDecimal getPromotionDiscount() {
        return promotionDiscount;
    }

    /**
     * promotion折扣
     *
     * @param promotionDiscount
     */
    public void setPromotionDiscount(BigDecimal promotionDiscount) {
        this.promotionDiscount = promotionDiscount;
    }

    /**
     * 总折扣
     *
     * @return
     */
    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    /**
     * 总折扣
     *
     * @param totalDiscount
     */
    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    /**
     * 总折扣金额
     *
     * @return
     */
    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    /**
     * 总折扣金额
     *
     * @param totalDiscountAmount
     */
    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    /**
     * 评分
     *
     * @return
     */
    public String getScore() {
        return score;
    }

    /**
     * 评分
     *
     * @param score
     */
    public void setScore(String score) {
        this.score = score;
    }

    /**
     * 秒杀数量
     *
     * @return
     */
    public Integer getDealQuantity() {
        return dealQuantity;
    }

    /**
     * 秒杀数量
     *
     * @param dealQuantity
     */
    public void setDealQuantity(Integer dealQuantity) {
        this.dealQuantity = dealQuantity;
    }

    /**
     * 三级分类名称
     *
     * @return
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 三级分类名称
     *
     * @param categoryName
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 业务组
     *
     * @return
     */
    public String getBusinessGroupCode() {
        return businessGroupCode;
    }

    /**
     * 业务组
     *
     * @param businessGroupCode
     */
    public void setBusinessGroupCode(String businessGroupCode) {
        this.businessGroupCode = businessGroupCode;
    }

    /**
     * 业务负责人
     *
     * @return
     */
    public String getBusinessLeaderCode() {
        return businessLeaderCode;
    }

    /**
     * 业务负责人
     *
     * @param businessLeaderCode
     */
    public void setBusinessLeaderCode(String businessLeaderCode) {
        this.businessLeaderCode = businessLeaderCode;
    }

    /**
     * 业务助理
     *
     * @return
     */
    public String getBusinessOperationCode() {
        return businessOperationCode;
    }

    /**
     * 业务助理
     *
     * @param businessOperationCode
     */
    public void setBusinessOperationCode(String businessOperationCode) {
        this.businessOperationCode = businessOperationCode;
    }

    /**
     * 预估成交价
     *
     * @return
     */
    public BigDecimal getTransactionPrice() {
        return transactionPrice;
    }

    /**
     * 预估成交价
     *
     * @param transactionPrice
     */
    public void setTransactionPrice(BigDecimal transactionPrice) {
        this.transactionPrice = transactionPrice;
    }

    public Integer getStockSaleDays() {
        return stockSaleDays;
    }

    public void setStockSaleDays(Integer stockSaleDays) {
        this.stockSaleDays = stockSaleDays;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public void setDealBurstCoefficient(BigDecimal dealBurstCoefficient) {
        this.dealBurstCoefficient = dealBurstCoefficient;
    }
    public BigDecimal getDealBurstCoefficient() {
        return dealBurstCoefficient;
    }

    public void setDmsLast30day(BigDecimal dmsLast30day) {
        this.dmsLast30day = dmsLast30day;
    }
    public BigDecimal getDmsLast30day() {
        return dmsLast30day;
    }

    public void setCategoryGross(BigDecimal categoryGross) {
        this.categoryGross = categoryGross;
    }
    public BigDecimal getCategoryGross() {
        return categoryGross;
    }

    public void setExpectedSalesVolume(BigDecimal expectedSalesVolume) {
        this.expectedSalesVolume = expectedSalesVolume;
    }
    public BigDecimal getExpectedSalesVolume() {
        return expectedSalesVolume;
    }

    public void setEstimateOfSales(BigDecimal estimateOfSales) {
        this.estimateOfSales = estimateOfSales;
    }
    public BigDecimal getEstimateOfSales() {
        return estimateOfSales;
    }

    public void setExpectedGrossProfitMargin(BigDecimal expectedGrossProfitMargin) {
        this.expectedGrossProfitMargin = expectedGrossProfitMargin;
    }
    public BigDecimal getExpectedGrossProfitMargin() {
        return expectedGrossProfitMargin;
    }

    public void setApprovalRole(String approvalRole) {
        this.approvalRole = approvalRole;
    }
    public String getApprovalRole() {
        return approvalRole;
    }
}
