package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 平台价历史表
 * gen by 代码生成器 2022-05-25
 */

@Table(name="mc.som_listing_price_history")
public class SomListingPriceHistory implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 售价
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 运费
	 */
	@Column("shipping_cost")
	private BigDecimal shippingCost ;
	/**
	 * 最低价
	 */
	@Column("minimum_price")
	private BigDecimal minimumPrice ;
	/**
	 * 最高价
	 */
	@Column("maximum_price")
	private BigDecimal maximumPrice ;
	/**
	 * B2B价格
	 */
	@Column("business_price")
	private BigDecimal businessPrice ;
	/**
	 * 10.Percent off business price 20.Fixed prices
	 */
	@Column("discount_type")
	private Integer discountType ;
	@Column("quantity_lower_bound_1")
	private Integer quantityLowerBound1 ;
	@Column("quantity_price_1")
	private BigDecimal quantityPrice1 ;
	@Column("quantity_lower_bound_2")
	private Integer quantityLowerBound2 ;
	@Column("quantity_price_2")
	private BigDecimal quantityPrice2 ;
	@Column("quantity_lower_bound_3")
	private Integer quantityLowerBound3 ;
	@Column("quantity_price_3")
	private BigDecimal quantityPrice3 ;
	@Column("quantity_lower_bound_4")
	private Integer quantityLowerBound4 ;
	@Column("quantity_price_4")
	private BigDecimal quantityPrice4 ;
	@Column("quantity_lower_bound_5")
	private Integer quantityLowerBound5 ;
	@Column("quantity_price_5")
	private BigDecimal quantityPrice5 ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 调价原因
	 */
	@Column("reason_remark")
	private String reasonRemark ;
	/**
	 * 最后调价人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后调价人
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后调价时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;
	/**
	 * 状态 10.审核中 20.审核通过,30:审批未通过,40:无需审批
	 */
	@Column("status")
	private Integer status ;

	public SomListingPriceHistory() {
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * 平台
	 *@return
	 */
	public String getPlatform(){
		return  platform;
	}
	/**
	 * 平台
	 *@param  platform
	 */
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	 * 站点
	 *@return
	 */
	public String getSite(){
		return  site;
	}
	/**
	 * 站点
	 *@param  site
	 */
	public void setSite(String site ){
		this.site = site;
	}
	/**
	 * 展示码
	 *@return
	 */
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	 * 展示码
	 *@param  sellerSku
	 */
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	 * 售价
	 *@return
	 */
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	 * 售价
	 *@param  price
	 */
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	 * 运费
	 *@return
	 */
	public BigDecimal getShippingCost(){
		return  shippingCost;
	}
	/**
	 * 运费
	 *@param  shippingCost
	 */
	public void setShippingCost(BigDecimal shippingCost ){
		this.shippingCost = shippingCost;
	}
	/**
	 * 最低价
	 *@return
	 */
	public BigDecimal getMinimumPrice(){
		return  minimumPrice;
	}
	/**
	 * 最低价
	 *@param  minimumPrice
	 */
	public void setMinimumPrice(BigDecimal minimumPrice ){
		this.minimumPrice = minimumPrice;
	}
	/**
	 * 最高价
	 *@return
	 */
	public BigDecimal getMaximumPrice(){
		return  maximumPrice;
	}
	/**
	 * 最高价
	 *@param  maximumPrice
	 */
	public void setMaximumPrice(BigDecimal maximumPrice ){
		this.maximumPrice = maximumPrice;
	}
	/**
	 * B2B价格
	 *@return
	 */
	public BigDecimal getBusinessPrice(){
		return  businessPrice;
	}
	/**
	 * B2B价格
	 *@param  businessPrice
	 */
	public void setBusinessPrice(BigDecimal businessPrice ){
		this.businessPrice = businessPrice;
	}
	/**
	 * 10.Percent off business price 20.Fixed prices
	 *@return
	 */
	public Integer getDiscountType(){
		return  discountType;
	}
	/**
	 * 10.Percent off business price 20.Fixed prices
	 *@param  discountType
	 */
	public void setDiscountType(Integer discountType ){
		this.discountType = discountType;
	}
	public Integer getQuantityLowerBound1(){
		return  quantityLowerBound1;
	}
	public void setQuantityLowerBound1(Integer quantityLowerBound1 ){
		this.quantityLowerBound1 = quantityLowerBound1;
	}
	public BigDecimal getQuantityPrice1(){
		return  quantityPrice1;
	}
	public void setQuantityPrice1(BigDecimal quantityPrice1 ){
		this.quantityPrice1 = quantityPrice1;
	}
	public Integer getQuantityLowerBound2(){
		return  quantityLowerBound2;
	}
	public void setQuantityLowerBound2(Integer quantityLowerBound2 ){
		this.quantityLowerBound2 = quantityLowerBound2;
	}
	public BigDecimal getQuantityPrice2(){
		return  quantityPrice2;
	}
	public void setQuantityPrice2(BigDecimal quantityPrice2 ){
		this.quantityPrice2 = quantityPrice2;
	}
	public Integer getQuantityLowerBound3(){
		return  quantityLowerBound3;
	}
	public void setQuantityLowerBound3(Integer quantityLowerBound3 ){
		this.quantityLowerBound3 = quantityLowerBound3;
	}
	public BigDecimal getQuantityPrice3(){
		return  quantityPrice3;
	}
	public void setQuantityPrice3(BigDecimal quantityPrice3 ){
		this.quantityPrice3 = quantityPrice3;
	}
	public Integer getQuantityLowerBound4(){
		return  quantityLowerBound4;
	}
	public void setQuantityLowerBound4(Integer quantityLowerBound4 ){
		this.quantityLowerBound4 = quantityLowerBound4;
	}
	public BigDecimal getQuantityPrice4(){
		return  quantityPrice4;
	}
	public void setQuantityPrice4(BigDecimal quantityPrice4 ){
		this.quantityPrice4 = quantityPrice4;
	}
	public Integer getQuantityLowerBound5(){
		return  quantityLowerBound5;
	}
	public void setQuantityLowerBound5(Integer quantityLowerBound5 ){
		this.quantityLowerBound5 = quantityLowerBound5;
	}
	public BigDecimal getQuantityPrice5(){
		return  quantityPrice5;
	}
	public void setQuantityPrice5(BigDecimal quantityPrice5 ){
		this.quantityPrice5 = quantityPrice5;
	}
	/**
	 * 币种
	 *@return
	 */
	public String getCurrency(){
		return  currency;
	}
	/**
	 * 币种
	 *@param  currency
	 */
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	 * 调价原因
	 *@return
	 */
	public String getReasonRemark(){
		return  reasonRemark;
	}
	/**
	 * 调价原因
	 *@param  reasonRemark
	 */
	public void setReasonRemark(String reasonRemark ){
		this.reasonRemark = reasonRemark;
	}
	/**
	 * 最后调价人工号
	 *@return
	 */
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	 * 最后调价人工号
	 *@param  lastModifyNum
	 */
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	 * 最后调价人
	 *@return
	 */
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	 * 最后调价人
	 *@param  lastModifyName
	 */
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	 * 最后调价时间
	 *@return
	 */
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	 * 最后调价时间
	 *@param  lastModifyTime
	 */
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}
