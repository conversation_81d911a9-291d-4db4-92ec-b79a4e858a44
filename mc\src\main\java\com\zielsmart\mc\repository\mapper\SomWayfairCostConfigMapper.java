package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomWayfairCostConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairCostConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-02-27
 */

@SqlResource("somWayfairCostConfig")
public interface SomWayfairCostConfigMapper extends BaseMapper<SomWayfairCostConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult<SomWayfairCostConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWayfairCostConfigVo> queryByPage(@Param("searchVo") SomWayfairCostConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 查询所有的站点
     *
     * @return List<SomWayfairCostConfigSite>
     */
    List<SomWayfairCostConfigSite> queryAllSite();

    /**
     * 填充 site
     *
     * @param aid 主键id
     * @param site 站点
     */
    @Update
    void fixSite(@Param("aid") String aid, @Param("site") String site);
}
