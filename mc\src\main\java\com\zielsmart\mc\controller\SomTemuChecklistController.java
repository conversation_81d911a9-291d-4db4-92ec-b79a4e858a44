package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTemuChecklistService;
import com.zielsmart.mc.vo.SomTemuChecklistExtVo;
import com.zielsmart.mc.vo.SomTemuChecklistPageSearchVo;
import com.zielsmart.mc.vo.SomTemuChecklistUpdateVo;
import com.zielsmart.mc.vo.SomTemuChecklistVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuChecklistController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTemuChecklist", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "核价单管理")
public class SomTemuChecklistController extends BasicController{

    @Resource
    SomTemuChecklistService somTemuChecklistService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTemuChecklistVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuChecklistExtVo>> queryByPage(@RequestBody SomTemuChecklistPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuChecklistService.queryByPage(searchVo));
    }

    /**
     * acceptQuotation
     *
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "一键接受申报价")
    @PostMapping(value = "/accept-quotation")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> acceptQuotation(@RequestBody SomTemuChecklistExtVo somTemuChecklistExtVo) throws ValidateException {
        somTemuChecklistService.acceptQuotation(somTemuChecklistExtVo.getSite());
        return ResultVo.ofSuccess(null);
    }

    /**
     * batchDicker
     *
     * @param somTemuChecklistUpdateVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量重新议价")
    @PostMapping(value = "/batch-dicker")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchDicker(@RequestBody SomTemuChecklistUpdateVo somTemuChecklistUpdateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuChecklistService.batchDicker(somTemuChecklistUpdateVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
