package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.repository.entity.SomTargetWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.repository.mapper.SomTargetWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomTargetWarehouseMapper;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigVo;
import com.zielsmart.mc.vo.SomTargetWarehouseVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTargetWarehouseConfigService
 * @description
 * @date 2023-12-06 09:21:23
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomTargetWarehouseConfigService {

    @Resource
    private SomTargetWarehouseConfigMapper somTargetWarehouseConfigMapper;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private SomTargetWarehouseMapper somTargetWarehouseMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomTargetWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTargetWarehouseConfigVo> queryByPage(SomTargetWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTargetWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomTargetWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (ObjectUtil.isNotEmpty(pageResult.getList())) {
            List<SomTargetWarehouseConfig> configList = somTargetWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, List<SomTargetWarehouseConfig>> configMap = configList.stream().collect(Collectors.groupingBy(SomTargetWarehouseConfig::getWarehouseCode, Collectors.toList()));
            for (SomTargetWarehouseConfigVo config : pageResult.getList()) {
                if (configMap.containsKey(config.getWarehouseCode())) {
                    List<SomTargetWarehouseConfig> warehouseConfigs = configMap.get(config.getWarehouseCode());
                    List<String> nameList = new ArrayList<>();
                    config.setList(warehouseConfigs.stream().map(f -> {
                        SomTargetWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTargetWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableWarehouseCode(f.getUseableWarehouseCode());
                        useableWarehouse.setUseableStorageCode(f.getUseableStorageCode());
                        nameList.add(warehouseMap.get(f.getUseableWarehouseCode()) + "-" + storageMap.get(f.getUseableStorageCode()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    config.setWarehouseNameList(nameList);
                    SomTargetWarehouseConfig warehouseConfig = warehouseConfigs.get(0);
                    config.setCreateNum(warehouseConfig.getCreateNum());
                    config.setCreateName(warehouseConfig.getCreateName());
                    config.setCreateTime(warehouseConfig.getCreateTime());
                    config.setLastModifyNum(warehouseConfig.getLastModifyNum());
                    config.setLastModifyName(warehouseConfig.getLastModifyName());
                    config.setLastModifyTime(warehouseConfig.getLastModifyTime());
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTargetWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 新增
     *
     * @param somTargetWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(SomTargetWarehouseConfigVo somTargetWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTargetWarehouseConfigVo) || StrUtil.isEmpty(somTargetWarehouseConfigVo.getWarehouseCode()) || ObjectUtil.isEmpty(somTargetWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (somTargetWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somTargetWarehouseConfigVo.getWarehouseCode()).count() > 0) {
            throw new ValidateException("您选择的平台仓库在系统中已存在，不允许重复添加");
        }
        List<SomTargetWarehouseConfig> insertList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomTargetWarehouseConfigVo.UseableWarehouse item : somTargetWarehouseConfigVo.getList()) {
            SomTargetWarehouseConfig config = new SomTargetWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(somTargetWarehouseConfigVo.getWarehouseCode());
            config.setWarehouseName(somTargetWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            insertList.add(config);
        }
        if (ObjectUtil.isNotEmpty(insertList)) {
            somTargetWarehouseConfigMapper.insertBatch(insertList);
        }

    }

    /**
     * update
     * 编辑
     *
     * @param somTargetWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomTargetWarehouseConfigVo somTargetWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTargetWarehouseConfigVo) || StrUtil.isEmpty(somTargetWarehouseConfigVo.getWarehouseCode()) || ObjectUtil.isEmpty(somTargetWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somTargetWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somTargetWarehouseConfigVo.getWarehouseCode()).delete();
        List<SomTargetWarehouseConfig> insertList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomTargetWarehouseConfigVo.UseableWarehouse item : somTargetWarehouseConfigVo.getList()) {
            SomTargetWarehouseConfig config = new SomTargetWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(somTargetWarehouseConfigVo.getWarehouseCode());
            config.setWarehouseName(somTargetWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setLastModifyNum(tokenUser.getJobNumber());
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            insertList.add(config);
        }
        if (ObjectUtil.isNotEmpty(insertList)) {
            somTargetWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somTargetWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(SomTargetWarehouseConfigVo somTargetWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTargetWarehouseConfigVo) || StrUtil.isEmpty(somTargetWarehouseConfigVo.getWarehouseCode())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTargetWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somTargetWarehouseConfigVo.getWarehouseCode()).delete();
    }

    /**
     * queryList
     * 获取平台仓库列表
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomTargetWarehouseVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomTargetWarehouseVo> queryList() {
        List<SomTargetWarehouseVo> list = somTargetWarehouseMapper.queryList();
        return ConvertUtils.listConvert(list, SomTargetWarehouseVo.class);
    }

}
