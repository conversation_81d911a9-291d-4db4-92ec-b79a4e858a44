package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 邮件预警
* gen by 代码生成器 2023-02-15
*/

@Table(name="mc.som_email_alert")
public class SomEmailAlert implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private String aid;
	/**
	 * 邮件预警规则名称
	 */
	@Column("email_alert_rule_name")
	private String emailAlertRuleName;
	/**
	 * 市场
	 */
	@Column("market")
	private String market;
	/**
	 * 发件邮箱地址
	 */
	@Column("email_address")
	private String emailAddress;
	/**
	 * 邮件标题
	 */
	@Column("email_header")
	private String emailHeader;
	/**
	 * 识别词组
	 */
	@Column("Identifying_phrases")
	private String identifyingPhrases;
	/**
	 * 预警通知规则名称
	 */
	@Column("alert_notification_rule_name")
	private String alertNotificationRuleName;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime;

	public SomEmailAlert() {
	}

	/**
	* 主键id
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键id
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 邮件预警规则名称
	*@return
	*/
	public String getEmailAlertRuleName(){
		return  emailAlertRuleName;
	}
	/**
	* 邮件预警规则名称
	*@param  emailAlertRuleName
	*/
	public void setEmailAlertRuleName(String emailAlertRuleName ){
		this.emailAlertRuleName = emailAlertRuleName;
	}
	/**
	* 市场
	*@return
	*/
	public String getMarket(){
		return  market;
	}
	/**
	* 市场
	*@param  market
	*/
	public void setMarket(String market ){
		this.market = market;
	}
	/**
	* 发件邮箱地址
	*@return
	*/
	public String getEmailAddress(){
		return  emailAddress;
	}
	/**
	* 发件邮箱地址
	*@param  emailAddress
	*/
	public void setEmailAddress(String emailAddress ){
		this.emailAddress = emailAddress;
	}
	/**
	* 邮件标题
	*@return
	*/
	public String getEmailHeader(){
		return  emailHeader;
	}
	/**
	* 邮件标题
	*@param  emailHeader
	*/
	public void setEmailHeader(String emailHeader ){
		this.emailHeader = emailHeader;
	}
	/**
	* 识别词组
	*@return
	*/
	public String getIdentifyingPhrases(){
		return  identifyingPhrases;
	}
	/**
	* 识别词组
	*@param  identifyingPhrases
	*/
	public void setIdentifyingPhrases(String identifyingPhrases ){
		this.identifyingPhrases = identifyingPhrases;
	}
	/**
	* 预警通知规则名称
	*@return
	*/
	public String getAlertNotificationRuleName(){
		return  alertNotificationRuleName;
	}
	/**
	* 预警通知规则名称
	*@param  alertNotificationRuleName
	*/
	public void setAlertNotificationRuleName(String alertNotificationRuleName ){
		this.alertNotificationRuleName = alertNotificationRuleName;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
