package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McWayfairCustomStock;
import com.zielsmart.mc.vo.McWayfairCustomStockExtVo;
import com.zielsmart.mc.vo.McWayfairCustomStockPageSearchVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2021-11-24
*/

@SqlResource("mcWayfairCustomStock")
public interface McWayfairCustomStockMapper extends BaseMapper<McWayfairCustomStock> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McWayfairCustomStockVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McWayfairCustomStockExtVo> queryByPage(@Param("searchVo")McWayfairCustomStockPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchDelete
     * 批量删除
     * @param deleteList
     * <AUTHOR>
     * @history
     */
    default void batchDelete(@Param("deleteList") List<McWayfairCustomStock> deleteList) {
        this.getSQLManager().updateBatch(SqlId.of("mcWayfairCustomStock.batchDelete"), deleteList);
    }
}
