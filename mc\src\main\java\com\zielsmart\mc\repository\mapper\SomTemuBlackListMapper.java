package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuBlackListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-11-05
*/

@SqlResource("somTemuBlackList")
public interface SomTemuBlackListMapper extends BaseMapper<SomTemuBlackList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuBlackListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuBlackListVo> queryByPage(@Param("searchVo")SomTemuBlackListPageSearchVo searchVo, PageRequest pageRequest);
}
