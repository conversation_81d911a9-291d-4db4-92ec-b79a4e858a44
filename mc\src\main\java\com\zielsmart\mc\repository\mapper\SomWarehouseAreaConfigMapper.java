package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2022-02-14
*/

@SqlResource("somWarehouseAreaConfig")
public interface SomWarehouseAreaConfigMapper extends BaseMapper<SomWarehouseAreaConfig> {

    /**
     * query
     * 查询
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomWarehouseAreaConfigVo>}
     * <AUTHOR>
     * @history
     */
    List<SomWarehouseAreaConfigVo> query(@Param("searchVo")SomWarehouseAreaConfigPageSearchVo searchVo);
}
