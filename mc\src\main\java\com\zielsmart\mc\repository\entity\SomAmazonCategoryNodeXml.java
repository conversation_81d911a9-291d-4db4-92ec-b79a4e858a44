package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.sql.SQLXML;
/*
* 
* gen by 代码生成器 2024-12-16
*/

@Table(name="mc.som_amazon_category_node_xml")
public class SomAmazonCategoryNodeXml implements java.io.Serializable {
	@AssignID
	private Long aid ;

	@Column("site")
	private String site ;

	@Column("node_id")
	private String nodeId ;

	@Column("node_name")
	private String nodeName;

	@Column("node_xml")
	private SQLXML nodeXml;

	@Column("node_store_context_name")
	private String nodeStoreContextName ;

	public SomAmazonCategoryNodeXml() {
	}

	public Long getAid(){
		return  aid;
	}
	public void setAid(Long aid ){
		this.aid = aid;
	}
	public String getSite(){
		return  site;
	}
	public void setSite(String site ){
		this.site = site;
	}
	public String getNodeId(){
		return  nodeId;
	}
	public void setNodeId(String nodeId ){
		this.nodeId = nodeId;
	}
	public String getNodeName(){
		return  nodeName;
	}
	public void setNodeName(String nodeName ){
		this.nodeName = nodeName;
	}
	public SQLXML getNodeXml(){
		return  nodeXml;
	}
	public void setNodeXml(SQLXML nodeXml ){
		this.nodeXml = nodeXml;
	}
	public String getNodeStoreContextName(){
		return  nodeStoreContextName;
	}
	public void setNodeStoreContextName(String nodeStoreContextName ){
		this.nodeStoreContextName = nodeStoreContextName;
	}

}
