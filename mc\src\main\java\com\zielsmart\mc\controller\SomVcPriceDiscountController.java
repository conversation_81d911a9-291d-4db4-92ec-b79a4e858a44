package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomVcPriceDiscountService;
import com.zielsmart.mc.vo.SomVcPriceDiscountImportVo;
import com.zielsmart.mc.vo.SomVcPriceDiscountPageSearchVo;
import com.zielsmart.mc.vo.SomVcPriceDiscountVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcPriceDiscountController
 * @description VC Price Discount表管理
 * @date 2025-05-12 14:01:16
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somVcPriceDiscount", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC Price Discount表管理")
public class SomVcPriceDiscountController extends BasicController {

    @Resource
    SomVcPriceDiscountService somVcPriceDiscountService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcPriceDiscountVo>> queryByPage(@RequestBody SomVcPriceDiscountPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcPriceDiscountService.queryByPage(searchVo));
    }

    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/addOrEdit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomVcPriceDiscountVo somVcPriceDiscountVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcPriceDiscountService.addOrEdit(somVcPriceDiscountVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "标记为已提报")
    @PostMapping(value = "/submit/mark")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> markSubmit(@RequestBody SomVcPriceDiscountVo somVcPriceDiscountVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcPriceDiscountService.markSubmit(somVcPriceDiscountVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> cancel(@RequestBody SomVcPriceDiscountVo somVcPriceDiscountVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcPriceDiscountService.cancel(somVcPriceDiscountVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomVcPriceDiscountVo somVcPriceDiscountVo) throws ValidateException {
        somVcPriceDiscountService.delete(somVcPriceDiscountVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomVcPriceDiscountPageSearchVo searchVo) {
        String data = somVcPriceDiscountService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/VCPriceDiscountImportTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        importParams.setSheetNum(0);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "账号名称", "供应商编码", "展示码", "ASIN", "活动起始日期", "活动截止日期", "折扣比例",
                "活动价格", "评分", "申请原因", "自定义原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcPriceDiscountImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcPriceDiscountImportVo.class, importParams);
        }  catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcPriceDiscountService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }
}
