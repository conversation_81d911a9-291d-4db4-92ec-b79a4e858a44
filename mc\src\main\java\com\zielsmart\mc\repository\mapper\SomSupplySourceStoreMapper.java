package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomSupplySourceStore;
import com.zielsmart.mc.vo.SomSupplySourceStoreExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStorePageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-09-19
 */

@SqlResource("somSupplySourceStore")
public interface SomSupplySourceStoreMapper extends BaseMapper<SomSupplySourceStore> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomSupplySourceStoreVo>}
     * <AUTHOR>
     * @history
     */
    List<SomSupplySourceStoreExtVo> query(@Param("searchVo") SomSupplySourceStorePageSearchVo searchVo);
}
