package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomThreePlatformPriceWhite;
import com.zielsmart.mc.repository.mapper.SomThreePlatformPriceWhiteMapper;
import com.zielsmart.mc.vo.SomThreePlatformPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceWhiteVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description 三方平台价格管理-白名单
 * @date 2024-11-13 16:33:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomThreePlatformPriceWhiteService {

    @Resource
    private SomThreePlatformPriceWhiteMapper somThreePlatformPriceWhiteMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomThreePlatformPriceWhiteVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomThreePlatformPriceWhiteVo> queryByPage(SomThreePlatformPriceWhitePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomThreePlatformPriceWhiteVo> pageResult = somThreePlatformPriceWhiteMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomThreePlatformPriceWhiteVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param threePlatformPriceWhiteVo 入参
     * @param tokenUser                 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomThreePlatformPriceWhiteVo threePlatformPriceWhiteVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(threePlatformPriceWhiteVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String platform = threePlatformPriceWhiteVo.getPlatform();
        String site = threePlatformPriceWhiteVo.getSite();
        String sellerSku = threePlatformPriceWhiteVo.getSellerSku();
        if (!StrUtil.isAllNotEmpty(platform, site, sellerSku)) {
            throw new ValidateException("平台/站点/展示码不能为空！");
        }
        long count = this.somThreePlatformPriceWhiteMapper.createLambdaQuery()
                .andEq("platform", platform)
                .andEq("site", site)
                .andEq("seller_sku", sellerSku)
                .count();
        if (count > 0L) {
            throw new ValidateException("展示码已存在，新增失败！");
        }
        threePlatformPriceWhiteVo.setAid(IdUtil.fastSimpleUUID());
        threePlatformPriceWhiteVo.setCreateNum(tokenUser.getJobNumber());
        threePlatformPriceWhiteVo.setCreateName(tokenUser.getUserName());
        threePlatformPriceWhiteVo.setCreateTime(DateTime.now().toJdkDate());
        this.somThreePlatformPriceWhiteMapper.insert(ConvertUtils.beanConvert(threePlatformPriceWhiteVo, SomThreePlatformPriceWhite.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somThreePlatformPriceWhiteVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomThreePlatformPriceWhiteVo somThreePlatformPriceWhiteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somThreePlatformPriceWhiteVo) || CollUtil.isEmpty(somThreePlatformPriceWhiteVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somThreePlatformPriceWhiteMapper.createLambdaQuery()
                .andIn("aid", somThreePlatformPriceWhiteVo.getAidList())
                .delete();
    }
}


