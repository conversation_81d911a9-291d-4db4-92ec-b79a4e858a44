package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* Temu本本goods
* gen by 代码生成器 2025-06-26
*/

@Table(name="mc.som_temu_local_goods")
public class SomTemuLocalGoods implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 商品ID
	 */
	@Column("goods_id")
	private Long goodsId ;
	/**
	 * 商品名称
	 */
	@Column("goods_name")
	private String goodsName ;
	/**
	 * 产品的规格或类型
	 */
	@Column("spec_name")
	private String specName ;
	/**
	 * 产品缩略图URL
	 */
	@Column("thumb_url")
	private String thumbUrl ;
	/**
	 * 产品外部编码
	 */
	@Column("out_goods_sn")
	private String outGoodsSn ;
	/**
	 * 产品的状态（例如，1表示销售，4表示未发布，等等）
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 产品的子状态（具体含义取决于业务逻辑）
	 */
	@Column("sub_status")
	private Integer subStatus ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 产品的市场价格或建议零售价
	 */
	@Column("market_price")
	private Long marketPrice ;
	/**
	 * 产品的市场价格或建议零售价
	 */
	@Column("list_price")
	private Object listPrice ;
	/**
	 * 产品的外部SKU（库存单位）代码或序列号列表
	 */
	@Column("out_sku_sn_list")
	private Object outSkuSnList ;
	/**
	 * 产品的SKU标识符列表
	 */
	@Column("sku_id_list")
	private Object skuIdList ;
	/**
	 * 产品的销售价格或零售价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 产品的销售价格或零售价格
	 */
	@Column("retail_price")
	private Object retailPrice ;
	/**
	 * 产品的库存数量或库存水平
	 */
	@Column("quantity")
	private Integer quantity ;
	/**
	 * 产品的创建时间
	 */
	@Column("crt_time")
	private Date crtTime ;
	/**
	 * 产品状态变更时间
	 */
	@Column("goods_status_change_time")
	private Date goodsStatusChangeTime ;
	/**
	 * 类目ID
	 */
	@Column("cat_id")
	private Long catId ;
	/**
	 * 品牌ID
	 */
	@Column("brand_id")
	private Long brandId ;
	/**
	 * 商标ID
	 */
	@Column("trademark_id")
	private Long trademarkId ;
	/**
	 * 您的产品可用的交付选项的ID，用逗号分隔
	 */
	@Column("cost_template_id")
	private String costTemplateId ;
	/**
	 * 指示从收到商品订单到可以发货之间的时间（以秒为单位）
	 */
	@Column("shipment_limit_second")
	private Long shipmentLimitSecond ;
	/**
	 * 产品的SKU标识符列表
	 */
	@Column("sku_info_list")
	private Object skuInfoList ;
	/**
	 * 产品子状态过滤器
	 */
	@Column("goods_show_sub_status")
	private Integer goodsShowSubStatus ;
	/**
	 * 低流量标签：1-低流量，2-不低流量
	 */
	@Column("low_traffic_tag")
	private Integer lowTrafficTag ;
	/**
	 * 限制流量标签：1-限制流量，2-非限制流量
	 */
	@Column("restricted_traffic_tag")
	private Integer restrictedTrafficTag ;
	/**
	 * 调用接口下载的时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	public SomTemuLocalGoods() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 商品ID
	*@return
	*/
	public Long getGoodsId(){
		return  goodsId;
	}
	/**
	* 商品ID
	*@param  goodsId
	*/
	public void setGoodsId(Long goodsId ){
		this.goodsId = goodsId;
	}
	/**
	* 商品名称
	*@return
	*/
	public String getGoodsName(){
		return  goodsName;
	}
	/**
	* 商品名称
	*@param  goodsName
	*/
	public void setGoodsName(String goodsName ){
		this.goodsName = goodsName;
	}
	/**
	* 产品的规格或类型
	*@return
	*/
	public String getSpecName(){
		return  specName;
	}
	/**
	* 产品的规格或类型
	*@param  specName
	*/
	public void setSpecName(String specName ){
		this.specName = specName;
	}
	/**
	* 产品缩略图URL
	*@return
	*/
	public String getThumbUrl(){
		return  thumbUrl;
	}
	/**
	* 产品缩略图URL
	*@param  thumbUrl
	*/
	public void setThumbUrl(String thumbUrl ){
		this.thumbUrl = thumbUrl;
	}
	/**
	* 产品外部编码
	*@return
	*/
	public String getOutGoodsSn(){
		return  outGoodsSn;
	}
	/**
	* 产品外部编码
	*@param  outGoodsSn
	*/
	public void setOutGoodsSn(String outGoodsSn ){
		this.outGoodsSn = outGoodsSn;
	}
	/**
	* 产品的状态（例如，1表示销售，4表示未发布，等等）
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 产品的状态（例如，1表示销售，4表示未发布，等等）
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 产品的子状态（具体含义取决于业务逻辑）
	*@return
	*/
	public Integer getSubStatus(){
		return  subStatus;
	}
	/**
	* 产品的子状态（具体含义取决于业务逻辑）
	*@param  subStatus
	*/
	public void setSubStatus(Integer subStatus ){
		this.subStatus = subStatus;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 产品的市场价格或建议零售价
	*@return
	*/
	public Long getMarketPrice(){
		return  marketPrice;
	}
	/**
	* 产品的市场价格或建议零售价
	*@param  marketPrice
	*/
	public void setMarketPrice(Long marketPrice ){
		this.marketPrice = marketPrice;
	}
	/**
	* 产品的市场价格或建议零售价
	*@return
	*/
	public Object getListPrice(){
		return  listPrice;
	}
	/**
	* 产品的市场价格或建议零售价
	*@param  listPrice
	*/
	public void setListPrice(Object listPrice ){
		this.listPrice = listPrice;
	}
	/**
	* 产品的外部SKU（库存单位）代码或序列号列表
	*@return
	*/
	public Object getOutSkuSnList(){
		return  outSkuSnList;
	}
	/**
	* 产品的外部SKU（库存单位）代码或序列号列表
	*@param  outSkuSnList
	*/
	public void setOutSkuSnList(Object outSkuSnList ){
		this.outSkuSnList = outSkuSnList;
	}
	/**
	* 产品的SKU标识符列表
	*@return
	*/
	public Object getSkuIdList(){
		return  skuIdList;
	}
	/**
	* 产品的SKU标识符列表
	*@param  skuIdList
	*/
	public void setSkuIdList(Object skuIdList ){
		this.skuIdList = skuIdList;
	}
	/**
	* 产品的销售价格或零售价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 产品的销售价格或零售价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 产品的销售价格或零售价格
	*@return
	*/
	public Object getRetailPrice(){
		return  retailPrice;
	}
	/**
	* 产品的销售价格或零售价格
	*@param  retailPrice
	*/
	public void setRetailPrice(Object retailPrice ){
		this.retailPrice = retailPrice;
	}
	/**
	* 产品的库存数量或库存水平
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 产品的库存数量或库存水平
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	/**
	* 产品的创建时间
	*@return
	*/
	public Date getCrtTime(){
		return  crtTime;
	}
	/**
	* 产品的创建时间
	*@param  crtTime
	*/
	public void setCrtTime(Date crtTime ){
		this.crtTime = crtTime;
	}
	/**
	* 产品状态变更时间
	*@return
	*/
	public Date getGoodsStatusChangeTime(){
		return  goodsStatusChangeTime;
	}
	/**
	* 产品状态变更时间
	*@param  goodsStatusChangeTime
	*/
	public void setGoodsStatusChangeTime(Date goodsStatusChangeTime ){
		this.goodsStatusChangeTime = goodsStatusChangeTime;
	}
	/**
	* 类目ID
	*@return
	*/
	public Long getCatId(){
		return  catId;
	}
	/**
	* 类目ID
	*@param  catId
	*/
	public void setCatId(Long catId ){
		this.catId = catId;
	}
	/**
	* 品牌ID
	*@return
	*/
	public Long getBrandId(){
		return  brandId;
	}
	/**
	* 品牌ID
	*@param  brandId
	*/
	public void setBrandId(Long brandId ){
		this.brandId = brandId;
	}
	/**
	* 商标ID
	*@return
	*/
	public Long getTrademarkId(){
		return  trademarkId;
	}
	/**
	* 商标ID
	*@param  trademarkId
	*/
	public void setTrademarkId(Long trademarkId ){
		this.trademarkId = trademarkId;
	}
	/**
	* 您的产品可用的交付选项的ID，用逗号分隔
	*@return
	*/
	public String getCostTemplateId(){
		return  costTemplateId;
	}
	/**
	* 您的产品可用的交付选项的ID，用逗号分隔
	*@param  costTemplateId
	*/
	public void setCostTemplateId(String costTemplateId ){
		this.costTemplateId = costTemplateId;
	}
	/**
	* 指示从收到商品订单到可以发货之间的时间（以秒为单位）
	*@return
	*/
	public Long getShipmentLimitSecond(){
		return  shipmentLimitSecond;
	}
	/**
	* 指示从收到商品订单到可以发货之间的时间（以秒为单位）
	*@param  shipmentLimitSecond
	*/
	public void setShipmentLimitSecond(Long shipmentLimitSecond ){
		this.shipmentLimitSecond = shipmentLimitSecond;
	}
	/**
	* 产品的SKU标识符列表
	*@return
	*/
	public Object getSkuInfoList(){
		return  skuInfoList;
	}
	/**
	* 产品的SKU标识符列表
	*@param  skuInfoList
	*/
	public void setSkuInfoList(Object skuInfoList ){
		this.skuInfoList = skuInfoList;
	}
	/**
	* 产品子状态过滤器
	*@return
	*/
	public Integer getGoodsShowSubStatus(){
		return  goodsShowSubStatus;
	}
	/**
	* 产品子状态过滤器
	*@param  goodsShowSubStatus
	*/
	public void setGoodsShowSubStatus(Integer goodsShowSubStatus ){
		this.goodsShowSubStatus = goodsShowSubStatus;
	}
	/**
	* 低流量标签：1-低流量，2-不低流量
	*@return
	*/
	public Integer getLowTrafficTag(){
		return  lowTrafficTag;
	}
	/**
	* 低流量标签：1-低流量，2-不低流量
	*@param  lowTrafficTag
	*/
	public void setLowTrafficTag(Integer lowTrafficTag ){
		this.lowTrafficTag = lowTrafficTag;
	}
	/**
	* 限制流量标签：1-限制流量，2-非限制流量
	*@return
	*/
	public Integer getRestrictedTrafficTag(){
		return  restrictedTrafficTag;
	}
	/**
	* 限制流量标签：1-限制流量，2-非限制流量
	*@param  restrictedTrafficTag
	*/
	public void setRestrictedTrafficTag(Integer restrictedTrafficTag ){
		this.restrictedTrafficTag = restrictedTrafficTag;
	}
	/**
	* 调用接口下载的时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 调用接口下载的时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
