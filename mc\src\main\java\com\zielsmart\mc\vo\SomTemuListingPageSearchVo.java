package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
 * 拼多多平台Listing的VO分页查询实体
 * gen by 代码生成器 2024-04-03
 */

@Data
@Schema(title = "拼多多平台Listing分页查询实体", name = "SomTemuListingPageSearchVo")
public class SomTemuListingPageSearchVo extends PageSearchVo {
    /**
     * 展示码
     */
    @Schema(description = "展示码", name = "sellerSku")
    private String sellerSku;

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "站点集合", name = "sites")
    private String sites;

    @Schema(description = "三级分类", name = "categoryName")
    private String categoryName;

    @Schema(description = "业务组", name = "salesGroupCode")
    private String salesGroupCode;

    @Schema(description = "业务负责人", name = "salesGroupEmptCode")
    private String salesGroupEmptCode;

    @Schema(description = "产品ID", name = "productId")
    private String productId;

    @Schema(description = "账号", name = "accountTag")
    private String accountTag;

    @Schema(description = "账号ID", name = "accountId")
    private String accountId;

    @Schema(description = "清仓标识", name = "clearanceFlag")
    private Boolean clearanceFlag;
}
