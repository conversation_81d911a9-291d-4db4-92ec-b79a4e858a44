package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomMercadolibreListing;
import com.zielsmart.mc.vo.SomMercadolibreListingVo;
import com.zielsmart.web.basic.vo.PageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-11-06
 */

@SqlResource("somMercadolibreListing")
public interface SomMercadolibreListingMapper extends BaseMapper<SomMercadolibreListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomMercadolibreListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMercadolibreListingVo> queryByPage(@Param("searchVo") PageSearchVo searchVo, PageRequest pageRequest);
}
