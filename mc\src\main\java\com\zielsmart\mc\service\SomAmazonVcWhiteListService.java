package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomAmazonVcWhiteList;
import com.zielsmart.mc.repository.mapper.SomAmazonVcWhiteListMapper;
import com.zielsmart.mc.vo.SomAmazonVcWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomAmazonVcWhiteListService
 * @description
 * @date 2024-02-01 16:18:51
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomAmazonVcWhiteListService {

    @Resource
    private SomAmazonVcWhiteListMapper somAmazonVcWhiteListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomAmazonVcWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomAmazonVcWhiteListVo> queryByPage(SomAmazonVcWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomAmazonVcWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomAmazonVcWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomAmazonVcWhiteListVo.class, searchVo);
    }

    /**
     * 删除
     *
     * @param somAmazonVcWhiteListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomAmazonVcWhiteListVo somAmazonVcWhiteListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcWhiteListVo) || ObjectUtil.isEmpty(somAmazonVcWhiteListVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }

        somAmazonVcWhiteListMapper.createLambdaQuery().andIn("aid", somAmazonVcWhiteListVo.getAidList()).delete();
        //somAmazonVcWhiteListMapper.createLambdaQuery().andEq("aid", somAmazonVcWhiteListVo.getAid()).delete();
    }

    /**
     * 导入白名单
     *
     * @param list
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomAmazonVcWhiteListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        if (CollectionUtil.isNotEmpty(list)) {
            for (SomAmazonVcWhiteListVo vo : list) {
                if (StrUtil.isEmpty(vo.getSellerSku()) || StrUtil.isEmpty(vo.getVendorCode())) {
                    throw new ValidateException("展示码/供应商编码不能为空，请检查数据");
                }
            }
            //文件数据去重
            list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(x -> x.getSellerSku() + x.getVendorCode()))), ArrayList::new));
            //查询白名单
            List<SomAmazonVcWhiteList> existList = somAmazonVcWhiteListMapper.all();
            Map<String, SomAmazonVcWhiteList> existMap = existList.stream().collect(Collectors.toMap(f -> f.getSellerSku() + f.getVendorCode(), v -> v));
            //过滤白名单
            list = list.stream().filter(x -> !existMap.containsKey(x.getSellerSku() + x.getVendorCode())).collect(Collectors.toList());
            list.stream().forEach(f -> {
                f.setAid(IdUtil.fastSimpleUUID());
                f.setCreateNum(tokenUser.getJobNumber());
                f.setCreateName(tokenUser.getUserName());
                f.setCreateTime(DateTime.now().toJdkDate());
            });
            somAmazonVcWhiteListMapper.insertBatch(ConvertUtils.listConvert(list, SomAmazonVcWhiteList.class));
        }
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String export(SomAmazonVcWhiteListPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomAmazonVcWhiteListVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC库存推送白名单");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomAmazonVcWhiteListVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
