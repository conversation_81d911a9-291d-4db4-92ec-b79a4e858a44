package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderCase;
import com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderCaseVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-08-18
*/

@SqlResource("somVcAmazonPurchaseOrderCase")
public interface SomVcAmazonPurchaseOrderCaseMapper extends BaseMapper<SomVcAmazonPurchaseOrderCase> {
    List<SomVcAmazonPurchaseOrderCaseVo> queryByPoAids(@Param("aids")List<String> aids);
}
