package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McPushStockWhiteList;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.McPushStockWhiteListMapper;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service.zbpm
 * @title ZBPMPStockWhiteListService
 * @description ZBPM项目库存白名单Service
 * @date 2022-01-13 11:06:21
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class ZBPMStockWhiteListService {
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private McPushStockWhiteListMapper whiteListMapper;
    @Resource
    private McProductSalesMapper productSalesMapper;

    /**
     * getMarket
     * 获取市场
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public List<ZBPMStockWhiteListVo> getMarket() {
        return mcDictionaryInfoMapper.getMarket();
    }

    /**
     * PlatformSites
     * 获取平台
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public List<ZBPMStockWhiteListVo> getPlatforms(ZBPMWhiteListSearchVo searchVo) {
        return mcDictionaryInfoMapper.getPlatforms(searchVo);
    }

    /**
     * saveStockWhiteList
     * 保存库存白名单
     *
     * @param vo
     * @param tokenUserInfo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String saveStockWhiteList(ZBPMStockWhiteListVo vo, TokenUserInfo tokenUserInfo) {
        String result = Strings.EMPTY;
        if (CollectionUtil.isNotEmpty(vo.getAddWhiteList()) || CollectionUtil.isNotEmpty(vo.getDelWhiteList())) {
            if (CollectionUtil.isNotEmpty(vo.getAddWhiteList())) {
                //去重
                List<ZBPMWhiteListVo> addWhiteListNoRepeat = vo.getAddWhiteList().stream().distinct().collect(Collectors.toList());
                List<String> platformList = addWhiteListNoRepeat.stream().map(m -> m.getPlatform()).distinct().collect(Collectors.toList());
                List<String> siteList = addWhiteListNoRepeat.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
                List<String> sellerSkuList = addWhiteListNoRepeat.stream().map(m -> m.getSellerSku()).collect(Collectors.toList());
                List<McPushStockWhiteList> addList = whiteListMapper.createLambdaQuery().andIn("platform", platformList).andIn("site", siteList).andIn("seller_sku", sellerSkuList).select();
                if (!addList.isEmpty()) {
                    List<String> errorMsg = new ArrayList<>();
                    addWhiteListNoRepeat.stream().forEach(f ->{
                        boolean b = addList.stream().anyMatch(s -> StrUtil.equalsIgnoreCase(f.getPlatform(), s.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), s.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), s.getSellerSku()));
                        if(b){
                            String str = String.format("%s%s%s%s", f.getSite(), " ", f.getSellerSku(), "\n");
                            errorMsg.add(str);
                        }
                    });
                    if(!errorMsg.isEmpty()){
                        for(String str :errorMsg){
                            result += str;
                        }
                        result += "在系统中已存在，请检查数据";
                        return result;
                    }
                }
                List<McPushStockWhiteList> list = new ArrayList<>();
                addWhiteListNoRepeat.stream().forEach(f->{
                    McPushStockWhiteList whiteList = ConvertUtils.beanConvert(f,McPushStockWhiteList.class);
                    whiteList.setAid(IdUtil.fastSimpleUUID());
                    whiteList.setCreateName(vo.getPromoterName());
                    whiteList.setCreateNum(vo.getPromoter());
                    whiteList.setCreateTime(DateTime.now().toJdkDate());
                    list.add(whiteList);
                });
                whiteListMapper.insertBatch(list);
            }
            if (CollectionUtil.isNotEmpty(vo.getDelWhiteList())) {
                for (ZBPMWhiteListVo white : vo.getDelWhiteList()) {
                    whiteListMapper.createLambdaQuery().andEq("platform", white.getPlatform()).andEq("site", white.getSite()).andEq("seller_sku",white.getSellerSku()).delete();
                }
            }
        }
        return result;
    }

    public List<ZBPMStockWhiteListVo> getAllProductSales(ZBPMWhiteListSearchVo searchVo) throws ValidateException {
        if(ObjectUtil.isNull(searchVo) || StrUtil.isBlank(searchVo.getOperation())  || StrUtil.isBlank(searchVo.getMarketCode()) || CollectionUtil.isEmpty(searchVo.getPlatforms()) || StrUtil.isBlank(searchVo.getSku())){
            throw new ValidateException("数据存在空值");
        }
        List<ZBPMStockWhiteListVo> voList = CollectionUtil.newArrayList();
        if(StrUtil.equalsIgnoreCase("add",searchVo.getOperation())){
            voList =  productSalesMapper.queryNotExistsWhiteList(searchVo);
        }else {
            voList =  productSalesMapper.queryExistsWhiteList(searchVo);
        }
        return voList;
    }
}
