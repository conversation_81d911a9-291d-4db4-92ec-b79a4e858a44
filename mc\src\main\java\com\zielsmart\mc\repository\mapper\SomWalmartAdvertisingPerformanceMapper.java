package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformancePageSearchVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-12-12
*/

@SqlResource("somWalmartAdvertisingPerformance")
public interface SomWalmartAdvertisingPerformanceMapper extends BaseMapper<SomWalmartAdvertisingPerformance> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWalmartAdvertisingPerformanceVo> queryByPage(@Param("searchVo")SomWalmartAdvertisingPerformancePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchFeedbackCancelResult
     * 批量更新数据
     * <AUTHOR>
     */
    default void batchUpdate(@Param("updateList") List<SomWalmartAdvertisingPerformance> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somWalmartAdvertisingPerformance.batchUpdate"), updateList);
    }
}
