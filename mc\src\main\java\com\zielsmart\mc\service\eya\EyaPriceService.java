package com.zielsmart.mc.service.eya;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.service.PlatformPropertiesService;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.vo.eya.EyaPriceWaringResponseVo;
import com.zielsmart.mc.vo.eya.EyaPriceWaringVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class EyaPriceService {

    @Resource
    private McSkuInfoMapper skuInfoMapper;
    @Resource
    private SomStandardPriceWhiteListMapper whiteListMapper;
    @Resource
    private SomStandardPriceMapper priceMapper;
    @Resource
    private McUserMapper userMapper;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private SysDeptNeweyaMapper eyaDeptMapper;
    @Resource
    private SomProductHierarchyMapper hierarchyMapper;
    @Resource
    private PlatformPropertiesService propertiesService;
    @Resource
    private IMagicService magicService;

    @Value("${magic.head.token}")
    public String token;

    @Resource
    private McPlatformPropertiesMapper mcPlatformPropertiesMapper;

    /**
     * warnPrice
     * 价格预警
     *
     * @param waringVo
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public EyaPriceWaringResponseVo warnPrice(EyaPriceWaringVo waringVo) throws ValidateException {
        EyaPriceWaringResponseVo responseVo = new EyaPriceWaringResponseVo();
        log.info("价格预警接收参数:平台:{}站点:{}展示码:{}Order Id:{}成交价{}", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId(), waringVo.getTransactionPrice());
        if (ObjectUtil.isEmpty(waringVo) || (StrUtil.isBlank(waringVo.getPlatform())) || StrUtil.isBlank(waringVo.getSite()) ||
                StrUtil.isBlank(waringVo.getSellerSku()) || ObjectUtil.isEmpty(waringVo.getTransactionPrice())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        // 与定价管理平台站点保持口径一致
        Map<String, List<McPlatformProperties>> map = propertiesService.searchB2CPlatformSites();
        if (!map.containsKey(waringVo.getPlatform())) {
            log.warn("平台:{}站点:{}展示码:{}Order Id:{},该平台无需进行价格预警", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
            return responseVo;
        }
        List<McPlatformProperties> list = map.get(waringVo.getPlatform());
        long siteCount = list.stream().filter(f -> StrUtil.equals(f.getSite(), waringVo.getSite())).count();
        if (siteCount < 1) {
            log.warn("平台:{}站点:{}展示码:{}Order Id:{},该站点无需进行价格预警", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
            return responseVo;
        }
        String orgSellerSku = waringVo.getSellerSku();
        BigDecimal divide = BigDecimal.ZERO;
        SomProductHierarchy hierarchy = null;
        // 查询产品销售视图
        // 2022.06.14产品确认如果存在多条,随机取一条
        log.info("根据平台:{}站点:{}展示码:{}Order Id:{}查询产品销售视图", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
        McProductSales productSales = productSalesMapper.createLambdaQuery().andEq("platform", waringVo.getPlatform()).andEq("customer_short_name", waringVo.getSite())
                .andEq("display_product_code", waringVo.getSellerSku()).andEq("is_enabled", 1).andEq("sales_flag", 1).single();
        if (ObjectUtil.isEmpty(productSales) || ObjectUtil.isEmpty(productSales.getisConsignmentSales()) || StrUtil.isBlank(productSales.getProductMainCode())
                || StrUtil.isBlank(productSales.getProductTypeCode()) || StrUtil.isBlank(productSales.getSalesGroupEmptCode()) || StrUtil.isBlank(productSales.getSalesGroupCode())) {
            log.warn("平台:{}站点:{}展示码:{}Order Id:{}未能找到有效的产品销售视图信息", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
            try {
                // 查询不到产品销售视图,向产品经理发送消息提醒
                String msg = StrUtil.format("根据平台:{}站点:{}展示码:{}Order Id:{}查询不到有效的产品销售视图信息,请检查数据", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
                log.warn(msg);
                FeiShuUtils.sendNotice("61150", msg);
            } catch (Exception e) {
                throw new ValidateException("发送飞书提醒失败,错误信息:{}", e.getMessage());
            }
            return responseVo;
        } else {
            if (StrUtil.equals("Z005", productSales.getProductTypeCode())) {
                // 查询产品层级视图
                log.warn("平台:{}站点:{}展示码:{}Order Id:{}属于组件产品", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
                hierarchy = hierarchyMapper.createLambdaQuery().andEq("current_sku_code", productSales.getProductMainCode()).single();
                if (ObjectUtil.isNotEmpty(hierarchy)) {
                    // 查询定价白名单
                    waringVo.setSku(hierarchy.getUpperSkuCode());
                    waringVo.setFulfillmentChannel(productSales.getisConsignmentSales());
                    log.info("根据平台:{}站点:{}sku:{}发货方式:{}Order Id:{}查询定价白名单", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSku(), productSales.getisConsignmentSales(), waringVo.getOrderId());
                    long count = whiteListMapper.queryByPlatformAndSite(waringVo);
                    if (count != 0) {
                        return responseVo;
                    }
                    McProductSales upperProductSale = productSalesMapper.createLambdaQuery().andEq("platform", waringVo.getPlatform())
                            .andEq("site", waringVo.getSite()).andEq("product_main_code", hierarchy.getUpperSkuCode())
                            .andEq("is_enabled", 1).andEq("is_consignment_sales", productSales.getisConsignmentSales()).single();
                    log.info("根据平台:{}站点:{}上层sku:{}发货方式:{}Order Id:{}查询产品销售视图", waringVo.getPlatform(), waringVo.getSite(), hierarchy.getUpperSkuCode(), productSales.getisConsignmentSales(), waringVo.getOrderId());
                    if (ObjectUtil.isNotEmpty(upperProductSale)) {
                        waringVo.setSellerSku(upperProductSale.getDisplayProductCode());
                    } else {
                        log.warn("上层SKU:{}未能找到有效的产品层级视图信息", hierarchy.getUpperSkuCode());
                    }
                } else {
                    log.warn("SKU:{}未能找到有效的产品层级视图信息", productSales.getProductMainCode());
                }
            } else {
                waringVo.setSku(productSales.getProductMainCode());
                waringVo.setFulfillmentChannel(productSales.getisConsignmentSales());
                // 查询定价白名单
                log.info("根据平台:{}站点:{}sku:{}发货方式:{}Order Id:{}查询定价白名单", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSku(), productSales.getisConsignmentSales(), waringVo.getOrderId());
                long count = whiteListMapper.queryByPlatformAndSite(waringVo);
                if (count != 0) {
                    return responseVo;
                }
            }
        }
        // 查找定价信息
        log.info("根据平台:{}站点:{}展示码:{}Order Id:{}查询定价信息", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
        SomStandardPrice standardPrice = priceMapper.queryByPlatformAndSite(waringVo);
        if (ObjectUtil.isEmpty(standardPrice) || ObjectUtil.isEmpty(standardPrice.getMinimumPrice())) {
            log.info("根据平台:{}站点:{}展示码:{}Order Id:{}查询不到定价信息", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
            // 查询销售负责人
            log.info("销售负责人工号:{}", productSales.getSalesGroupEmptCode());
            String jobNumbers = productSales.getSalesGroupEmptCode();
            // 查询BUS
            // 查询基础视图中的BUS  product_group_code
            log.info("根据SKU:{}查找BUS", productSales.getProductMainCode());
            //查询国家
            McPlatformProperties single = mcPlatformPropertiesMapper.createLambdaQuery().andEq("platform", waringVo.getPlatform()).andEq("site", waringVo.getSite()).single();
            List<String> countryList = Arrays.asList("US", "DE", "UK", "FR", "IT", "ES");
            McSkuInfo skuInfo = skuInfoMapper.createLambdaQuery().andEq("sku_code", productSales.getProductMainCode()).single("product_group_code", "product_status_code");
            //20240524 判断产品状态是否为新品 10 或者试销 20
            String productStatusCode = skuInfo==null?null:skuInfo.getProductStatusCode();
            if (productStatusCode!=null && (StrUtil.equals("10", productStatusCode) || StrUtil.equals("20", productStatusCode)) && single != null && countryList.contains(single.getCountry())) {
                if (skuInfo != null && skuInfo.getProductGroupCode() != null) {
                    SysDeptNeweya eyaDept = eyaDeptMapper.createLambdaQuery().andEq("dept_code", skuInfo.getProductGroupCode()).single();
                    if (eyaDept != null && StrUtil.isNotEmpty(eyaDept.getBusEmpNum())) {
                        jobNumbers = jobNumbers + "," + eyaDept.getBusEmpNum();
                    } else {
                        log.warn("根据基础视图BUS:{}查找部门为空", skuInfo.getProductGroupCode());
                    }
                } else {
                    log.warn("根据SKU:{}查找基础视图为空", productSales.getProductMainCode());
                }
                log.info("查询出销售负责人和BUS工号:{}", jobNumbers);
            } else {
                //查询销售负责人领导
                McUser userSingle = userMapper.createLambdaQuery().andEq("job_number", productSales.getSalesGroupEmptCode()).single();
                McUser leader = userMapper.single(userSingle.getDefaultLeaderId());
                jobNumbers = jobNumbers + "," + leader.getJobNumber();
                log.info("根据销售负责人工号:{} 查找出上级领导{}", productSales.getSalesGroupEmptCode(),leader.getJobNumber());
            }

            String msg = StrUtil.format("根据平台:{}站点:{}展示码:{}Order Id:{}没有查询到定价，请维护该产品的定价", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
            log.warn(msg);
            try {
                FeiShuUtils.sendNotice(jobNumbers, msg);
            } catch (JsonProcessingException e) {
                throw new ValidateException("发送飞书提醒失败,错误信息:{}", e.getMessage());
            }

        } else {
            // 针对Walmart平台进行特殊处理
            BigDecimal quotient = BigDecimal.ZERO;
            if (StrUtil.equalsIgnoreCase("Walmart", waringVo.getPlatform()) && ObjectUtil.isNotEmpty(hierarchy)) {
                log.info("平台:{}站点:{}展示码:{}Order Id:{}为组件产品", waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId());
                List<SomProductHierarchy> upperList = hierarchyMapper.createLambdaQuery().andEq("upper_sku_code", hierarchy.getUpperSkuCode()).select();
                int skuCount = upperList.stream().mapToInt(m -> m.getCurrentSkuCount()).sum();
                divide = standardPrice.getMinimumPrice().divide(BigDecimal.valueOf(skuCount), 2, BigDecimal.ROUND_HALF_UP);
                quotient = waringVo.getTransactionPrice().divide(divide, 2, BigDecimal.ROUND_HALF_UP);
            } else {
                Assert.isTrue(standardPrice.getMinimumPrice()!=null && standardPrice.getMinimumPrice().compareTo(BigDecimal.ZERO)!=0, "最低价不能为空或为0");
                quotient = waringVo.getTransactionPrice().divide(standardPrice.getMinimumPrice(), 2, BigDecimal.ROUND_HALF_UP);
            }
            // 小于0.6，系统返回false,并发送飞书提醒
            if (quotient.compareTo(BigDecimal.valueOf(0.6)) == -1) {
                log.info("「成交价」/「最低价」小于0.6,发送飞书提醒");
                // 查询销售负责人
                log.info("根据销售负责人工号:{}查找上级领导", productSales.getSalesGroupEmptCode());
                McUser mcUser = userMapper.createLambdaQuery().andEq("job_number", productSales.getSalesGroupEmptCode()).single();
                if (ObjectUtil.isNotNull(mcUser)) {
                    String jobNumbers = mcUser.getJobNumber();
                    McUser obj = userMapper.createLambdaQuery().andEq("aid", mcUser.getDefaultLeaderId()).single();
                    if (ObjectUtil.isNotNull(obj)) {
                        jobNumbers = jobNumbers + "," + obj.getJobNumber();
                    } else {
                        responseVo.setMsg("根据销售负责人:" + mcUser.getJobNumber() + "的默认当前领导ID:" + mcUser.getDefaultLeaderId() + "找不到对应的工号");
                        return responseVo;
                    }
                    String msg = StrUtil.format("平台:{}站点:{}的展示码:{},OrderId:{},成交价:{}低于日常售价低值:{}的{}%,请检查",
                            waringVo.getPlatform(), waringVo.getSite(), waringVo.getSellerSku(), waringVo.getOrderId(), waringVo.getTransactionPrice(), standardPrice.getMinimumPrice(), quotient.multiply(BigDecimal.valueOf(100)));
                    if (StrUtil.equalsIgnoreCase("Walmart", waringVo.getPlatform()) && ObjectUtil.isNotEmpty(hierarchy)) {
                        msg = StrUtil.format("{}平台{}站点的展示码:{}，Order Id：{}，成交价{}低于日常售价低值{}的{}%，请检查",
                                waringVo.getPlatform(), waringVo.getSite(), orgSellerSku, waringVo.getOrderId(), waringVo.getTransactionPrice(), divide, quotient.multiply(BigDecimal.valueOf(100)));
                    }
                    try {

                        FeiShuUtils.sendNotice(jobNumbers, msg);
                    } catch (JsonProcessingException e) {
                        throw new ValidateException("发送飞书提醒失败,错误信息:{}", e.getMessage());
                    }
                    responseVo.setMsg("「成交价」/「最低价」小于0.6,发送飞书提醒");
                    responseVo.setDiscount(quotient);
                    return responseVo;
                } else {
                    responseVo.setMsg("根据平台:" + waringVo.getPlatform() + "站点:" + waringVo.getSite() + "展示码:" + waringVo.getSellerSku() + "无法找到对应的销售负责人");
                    return responseVo;
                }
            }
            // 大于等于0.6,系统返回true
            return responseVo;
        }
        return responseVo;
    }

    public ResultVo warnPriceNew(EyaPriceWaringVo waringVo) {
        log.info("新订单价格预警传参:{}", JSONUtil.toJsonStr(waringVo));
        return magicService.warnPrice(token,waringVo);
    }

}
