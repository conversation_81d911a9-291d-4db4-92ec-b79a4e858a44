package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 区域表
* gen by 代码生成器 2025-07-29
*/

@Table(name="mc.som_area_info")
public class SomAreaInfo implements java.io.Serializable {
	/**
	 * 主键ID
	 */
	@AssignID
	private String aid ;
	/**
	 * 区域编码
	 */
	@Column("area_code")
	private String areaCode ;
	/**
	 * 区域名称中文
	 */
	@Column("area_name_cn")
	private String areaNameCn ;
	/**
	 * 区域名中文称简称
	 */
	@Column("area_short_name_cn")
	private String areaShortNameCn ;
	/**
	 * 区域名称英文
	 */
	@Column("area_name_en")
	private String areaNameEn ;
	/**
	 * 区域名称英文简称
	 */
	@Column("area_short_name_en")
	private String areaShortNameEn ;
	/**
	 * 区域级别:1：大洲
2：国家
3：省/州
4： 城市
5：区/县

	 */
	@Column("area_level")
	private Integer areaLevel ;
	/**
	 * 父区域编码
	 */
	@Column("p_area_code")
	private String pAreaCode ;
	/**
	 * 是否有效
	 */
	@Column("is_enabled")
	private Integer isEnabled ;
	/**
	 * 生效时间
	 */
	@Column("start_time")
	private Date startTime ;
	/**
	 * 失效时间
	 */
	@Column("end_time")
	private Date endTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 描述信息
	 */
	@Column("remark_desc")
	private String remarkDesc ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 业务地点
	 */
	@Column("business_location_code")
	private String businessLocationCode ;
	/**
	 * 销售市场
	 */
	@Column("sales_market_code")
	private String salesMarketCode ;

	public SomAreaInfo() {
	}

	/**
	* 主键ID
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键ID
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 区域编码
	*@return
	*/
	public String getAreaCode(){
		return  areaCode;
	}
	/**
	* 区域编码
	*@param  areaCode
	*/
	public void setAreaCode(String areaCode ){
		this.areaCode = areaCode;
	}
	/**
	* 区域名称中文
	*@return
	*/
	public String getAreaNameCn(){
		return  areaNameCn;
	}
	/**
	* 区域名称中文
	*@param  areaNameCn
	*/
	public void setAreaNameCn(String areaNameCn ){
		this.areaNameCn = areaNameCn;
	}
	/**
	* 区域名中文称简称
	*@return
	*/
	public String getAreaShortNameCn(){
		return  areaShortNameCn;
	}
	/**
	* 区域名中文称简称
	*@param  areaShortNameCn
	*/
	public void setAreaShortNameCn(String areaShortNameCn ){
		this.areaShortNameCn = areaShortNameCn;
	}
	/**
	* 区域名称英文
	*@return
	*/
	public String getAreaNameEn(){
		return  areaNameEn;
	}
	/**
	* 区域名称英文
	*@param  areaNameEn
	*/
	public void setAreaNameEn(String areaNameEn ){
		this.areaNameEn = areaNameEn;
	}
	/**
	* 区域名称英文简称
	*@return
	*/
	public String getAreaShortNameEn(){
		return  areaShortNameEn;
	}
	/**
	* 区域名称英文简称
	*@param  areaShortNameEn
	*/
	public void setAreaShortNameEn(String areaShortNameEn ){
		this.areaShortNameEn = areaShortNameEn;
	}
	/**
	* 区域级别:1：大洲
2：国家
3：省/州
4： 城市
5：区/县

	*@return
	*/
	public Integer getAreaLevel(){
		return  areaLevel;
	}
	/**
	* 区域级别:1：大洲
2：国家
3：省/州
4： 城市
5：区/县

	*@param  areaLevel
	*/
	public void setAreaLevel(Integer areaLevel ){
		this.areaLevel = areaLevel;
	}
	/**
	* 父区域编码
	*@return
	*/
	public String getPAreaCode(){
		return  pAreaCode;
	}
	/**
	* 父区域编码
	*@param  pAreaCode
	*/
	public void setPAreaCode(String pAreaCode ){
		this.pAreaCode = pAreaCode;
	}
	/**
	* 是否有效
	*@return
	*/
	public Integer getisEnabled(){
		return  isEnabled;
	}
	/**
	* 是否有效
	*@param  isEnabled
	*/
	public void setisEnabled(Integer isEnabled ){
		this.isEnabled = isEnabled;
	}
	/**
	* 生效时间
	*@return
	*/
	public Date getStartTime(){
		return  startTime;
	}
	/**
	* 生效时间
	*@param  startTime
	*/
	public void setStartTime(Date startTime ){
		this.startTime = startTime;
	}
	/**
	* 失效时间
	*@return
	*/
	public Date getEndTime(){
		return  endTime;
	}
	/**
	* 失效时间
	*@param  endTime
	*/
	public void setEndTime(Date endTime ){
		this.endTime = endTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 描述信息
	*@return
	*/
	public String getRemarkDesc(){
		return  remarkDesc;
	}
	/**
	* 描述信息
	*@param  remarkDesc
	*/
	public void setRemarkDesc(String remarkDesc ){
		this.remarkDesc = remarkDesc;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 业务地点
	*@return
	*/
	public String getBusinessLocationCode(){
		return  businessLocationCode;
	}
	/**
	* 业务地点
	*@param  businessLocationCode
	*/
	public void setBusinessLocationCode(String businessLocationCode ){
		this.businessLocationCode = businessLocationCode;
	}
	/**
	* 销售市场
	*@return
	*/
	public String getSalesMarketCode(){
		return  salesMarketCode;
	}
	/**
	* 销售市场
	*@param  salesMarketCode
	*/
	public void setSalesMarketCode(String salesMarketCode ){
		this.salesMarketCode = salesMarketCode;
	}

}
