package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IProductSalesService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.eya.BuInfoDto;
import com.zielsmart.mc.vo.zbpm.ProcessDefinitionVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomListingPriceService {

    @Resource
    private SomListingPriceMapper somListingPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private SomListingPriceHistoryMapper somListingPriceHistoryMapper;
    @Resource
    private McPlatformPropertiesMapper mcPlatformPropertiesMapper;
    @Resource
    private McProductSalesMapper mcProductSalesMapper;
    @Resource
    private SomStandardPriceMapper somStandardPriceMapper;

    @Resource
    private IProductSalesService iProductSalesService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomListingPriceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomListingPriceExtVo> queryByPage(SomListingPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomListingPriceExtVo> pageResult = dynamicSqlManager.getMapper(McProductSalesMapper.class).pageSearchListingPrice(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<String> codeList = Arrays.asList(new String[]{"SalesProductStatus", "IsConsignmentSales", "Country"});
            List<McDictionaryInfo> list = mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
            pageResult.getList().forEach(f -> {
                // 展示码状态
                list.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "SalesProductStatus") && StrUtil.equalsIgnoreCase(f.getSellerSkuStatusCode(), t.getItemValue())).findFirst().ifPresent(ps -> {
                    f.setSellerSkuStatusName(ps.getItemLable());
                });
                // 发货方式
                list.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "IsConsignmentSales") && (f.getFulfillmentChannel() == Integer.valueOf(t.getItemValue()))).findFirst().ifPresent(ps -> {
                    f.setFulfillmentChannelName(ps.getItemLable());
                });
                // 国家
                list.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "Country") && StrUtil.equalsIgnoreCase(f.getCountry(), t.getItemValue())).findFirst().ifPresent(ps -> {
                    f.setCountryName(ps.getItemLable());
                });
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomListingPriceExtVo.class, searchVo);
    }

    /**
     * queryListingPrice
     * 根据平台站点展示码查询平台价信息
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomListingPriceVo}
     * <AUTHOR>
     * @history
     */
    public SomListingPriceVo queryListingPrice(SomListingPricePageSearchVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getPlatform()) || StrUtil.isBlank(searchVo.getSite()) || StrUtil.isBlank(searchVo.getKeyWord())) {
            throw new ValidateException("查询参数存在空值,请检查");
        }
        SomListingPrice obj = somListingPriceMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).andEq("seller_sku", searchVo.getKeyWord()).single();
        return ConvertUtils.beanConvert(obj, SomListingPriceVo.class);
    }

    /**
     * defendListingPrice
     * 调整 价格
     *
     * @param defendPriceVo
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void defendListingPrice(SomListingPriceVo defendPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        SomListingPriceHistory history = new SomListingPriceHistory();
        try {
            boolean validate = Validate(defendPriceVo);
            if (!validate) {
                throw new ValidateException("数据存在空值,请检查");
            }
            SomListingPrice obj = somListingPriceMapper.createLambdaQuery().andEq("platform", defendPriceVo.getPlatform()).andEq("site", defendPriceVo.getSite())
                    .andEq("seller_sku", defendPriceVo.getSellerSku()).single();
            // 校验是否发生变化
            if (ObjectUtil.isNotEmpty(obj)) {
                Boolean b = defendPriceVo.getPrice().compareTo(obj.getPrice()) == 0 && defendPriceVo.getShippingCost().compareTo(obj.getShippingCost()) == 0
                        && defendPriceVo.getMinimumPrice().compareTo(obj.getMinimumPrice()) == 0 && defendPriceVo.getMaximumPrice().compareTo(obj.getMaximumPrice()) == 0
                        && defendPriceVo.getBusinessPrice().compareTo(obj.getBusinessPrice()) == 0 && ObjectUtil.equal(defendPriceVo.getQuantityLowerBound1(),obj.getQuantityLowerBound1())
                        && ObjectUtil.equal(defendPriceVo.getQuantityLowerBound2(),obj.getQuantityLowerBound2()) && ObjectUtil.equal(defendPriceVo.getQuantityLowerBound3(),obj.getQuantityLowerBound3())
                        && ObjectUtil.equal(defendPriceVo.getQuantityLowerBound4(),obj.getQuantityLowerBound4()) && ObjectUtil.equal(defendPriceVo.getQuantityLowerBound5(),obj.getQuantityLowerBound5());
                String b1 = Strings.EMPTY,b2 = Strings.EMPTY,b3 = Strings.EMPTY,b4 = Strings.EMPTY,b5 = Strings.EMPTY;
                if (ObjectUtil.isNotEmpty(defendPriceVo.getQuantityPrice1())) {
                    if(ObjectUtil.equal(defendPriceVo.getQuantityPrice1().setScale(2), obj.getQuantityPrice1())){
                        b1+= "error";
                    } else {
                        b1+= "suce";
                    }
                }else {
                    b1+= "error";
                }
                if (ObjectUtil.isNotEmpty(defendPriceVo.getQuantityPrice2())) {
                    if(ObjectUtil.equal(defendPriceVo.getQuantityPrice2().setScale(2), obj.getQuantityPrice2())){
                        b2+= "error";
                    }else {
                        b2+= "suce";
                    }
                }else {
                    b2+= "error";
                }
                if (ObjectUtil.isNotEmpty(defendPriceVo.getQuantityPrice3())) {
                    if(ObjectUtil.equal(defendPriceVo.getQuantityPrice3().setScale(2), obj.getQuantityPrice3())){
                        b3+= "error";
                    }else {
                        b3+= "suce";
                    }
                }else {
                    b3+= "error";
                }
                if (ObjectUtil.isNotEmpty(defendPriceVo.getQuantityPrice4())) {
                    if(ObjectUtil.equal(defendPriceVo.getQuantityPrice4().setScale(2), obj.getQuantityPrice4())){
                        b4+= "error";
                    }else {
                        b4+= "suce";
                    }
                }else {
                    b4+= "error";
                }
                if (ObjectUtil.isNotEmpty(defendPriceVo.getQuantityPrice5())) {
                    if(ObjectUtil.equal(defendPriceVo.getQuantityPrice5().setScale(2), obj.getQuantityPrice5())){
                        b5+= "error";
                    }else {
                        b5+= "suce";
                    }
                }else {
                    b5+= "error";
                }
                if(b && StrUtil.isNotBlank(b1) && StrUtil.equals(b2,"error") && StrUtil.equals(b3,"error") && StrUtil.equals(b4,"error") && StrUtil.equals(b5,"error")){
                    throw new ValidateException("未修改价格，无需保存");
                }
            }
            // 正在审核中,拒绝发起
            SomListingPriceHistory historyObj = somListingPriceHistoryMapper.createLambdaQuery().andEq("platform", defendPriceVo.getPlatform()).andEq("site", defendPriceVo.getSite())
                    .andEq("seller_sku", defendPriceVo.getSellerSku()).andEq("status", 10).single();
            if (ObjectUtil.isNotEmpty(historyObj)) {
                throw new ValidateException("当前产品已申请Price调整，审批结束前不允许二次调价，审批进度可点击「Price」进行查看");
            }
            // 判断是否触发流程
            boolean b = true;
            if (ObjectUtil.isNotEmpty(obj) && ObjectUtil.isNotEmpty(obj.getPrice())) {
                b = defendPriceVo.getPrice().compareTo(obj.getPrice()) == 0;
            }
            if (ObjectUtil.isEmpty(obj) && ObjectUtil.isNotEmpty(defendPriceVo.getPrice()) && defendPriceVo.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                b = false;
            }
            history = ConvertUtils.beanConvert(defendPriceVo, SomListingPriceHistory.class);
            history.setAid(IdUtil.fastSimpleUUID());
            history.setLastModifyName(tokenUser.getUserName());
            history.setLastModifyNum(tokenUser.getJobNumber());
            history.setLastModifyTime(DateTime.now().toJdkDate());
            if (!b) {
                // 需要发起流程
                history.setStatus(10);
                history.setPrice(defendPriceVo.getPrice());
                // 校验定价信息
                SomStandardPrice temp = somStandardPriceMapper.createLambdaQuery().andEq("platform", defendPriceVo.getPlatform()).andEq("site", defendPriceVo.getSite())
                        .andEq("seller_sku", defendPriceVo.getSellerSku()).single();
                if (ObjectUtil.isEmpty(temp)) {
                    throw new ValidateException("当前产品未维护定价，请先维护定价");
                }
            } else {
                history.setStatus(40);
            }
            somListingPriceHistoryMapper.insert(history);
            if (ObjectUtil.isNotNull(obj)) {
                if (b) {
                    obj.setPrice(defendPriceVo.getPrice());
                }
                obj.setShippingCost(defendPriceVo.getShippingCost());
                obj.setMinimumPrice(defendPriceVo.getMinimumPrice());
                obj.setMaximumPrice(defendPriceVo.getMaximumPrice());
                obj.setBusinessPrice(defendPriceVo.getBusinessPrice());
                obj.setDiscountType(defendPriceVo.getDiscountType());
                obj.setQuantityLowerBound1(defendPriceVo.getQuantityLowerBound1());
                obj.setQuantityPrice1(defendPriceVo.getQuantityPrice1());
                obj.setQuantityLowerBound2(defendPriceVo.getQuantityLowerBound2());
                obj.setQuantityPrice2(defendPriceVo.getQuantityPrice2());
                obj.setQuantityLowerBound3(defendPriceVo.getQuantityLowerBound3());
                obj.setQuantityPrice3(defendPriceVo.getQuantityPrice3());
                obj.setQuantityLowerBound4(defendPriceVo.getQuantityLowerBound4());
                obj.setQuantityPrice4(defendPriceVo.getQuantityPrice4());
                obj.setQuantityLowerBound5(defendPriceVo.getQuantityLowerBound5());
                obj.setQuantityPrice5(defendPriceVo.getQuantityPrice5());
                obj.setReasonRemark(defendPriceVo.getReasonRemark());
                obj.setLastModifyNum(tokenUser.getJobNumber());
                obj.setLastModifyName(tokenUser.getUserName());
                obj.setLastModifyTime(DateTime.now().toJdkDate());
                somListingPriceMapper.updateById(obj);
            } else {
                SomListingPrice temp = ConvertUtils.beanConvert(defendPriceVo, SomListingPrice.class);
                temp.setAid(IdUtil.fastSimpleUUID());
                temp.setPushStatus(10);
                if (!b) {
                    temp.setPrice(BigDecimal.ZERO);
                }
                temp.setLastModifyNum(tokenUser.getJobNumber());
                temp.setLastModifyName(tokenUser.getUserName());
                temp.setLastModifyTime(DateTime.now().toJdkDate());
                somListingPriceMapper.insert(temp);
            }
//            if (!b) {
//                // 查询流程信息
//                ProcessDefinitionVo vo = new ProcessDefinitionVo();
//                vo.setKey("PlatformPriceAdjust");
//                ResultVo<ZBPMProcessVo> resultVo1 = zbpmService.getProcessByProcessKey(vo);
//                if (!resultVo1.isSuccess()) {
//                    throw new ValidateException("根据流程关键字(PlatformPriceAdjust)获取流程信息出错{}", resultVo1.getMessage());
//                }
//                if (ObjectUtil.isEmpty(resultVo1.getData())) {
//                    throw new ValidateException("根据流程关键字(PlatformPriceAdjust)获取流程信息为空");
//                }
//                ResultVo<List<BuInfoDto>> resultVo = iProductSalesService.findBuBySkus(Collections.singletonList(defendPriceVo.getSku()));
//                if (!resultVo.isSuccess()) {
//                    throw new ValidateException("根据SKU获取BU信息出错" + resultVo.getMessage());
//                }
//                if (ObjectUtil.isEmpty(resultVo.getData())) {
//                    throw new ValidateException("BU信息没有维护，请维护信息");
//                }
//                // 组织流程信息
//                List<SomListingPriceExtVo> submitList = mcProductSalesMapper.querySubmitInfo(defendPriceVo);
//                if (CollectionUtil.isNotEmpty(submitList)) {
//                    List<SomListingPriceHistory> list = somListingPriceHistoryMapper.createLambdaQuery().andEq("platform", defendPriceVo.getPlatform()).andEq("site", defendPriceVo.getSite()).andEq("seller_sku", defendPriceVo.getSellerSku()).andEq("status", 20).desc("last_modify_time").select();
//                    // 发起流程
//                    List<BuInfoDto> infoDtos = resultVo.getData();
//                    submitList.forEach(f -> {
//                        if (CollectionUtil.isNotEmpty(list)) {
//                            f.setLastModifyTime(list.get(0).getLastModifyTime());
//                        }
//                        infoDtos.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getSkuCode(), f.getSku())).findFirst().ifPresent(ps -> {
//                            f.setProductGroupName(ps.getProductGroupName());
//                            f.setBusEmpCode(ps.getBusEmpCode());
//                            f.setBusEmpNameCN(ps.getBusEmpNameCN());
//                            f.setManagerEmpNameCN(ps.getManagerEmpNameCN());
//                        });
//                    });
//                }
//                ProcessVo processVo = new ProcessVo();
//                processVo.setFlowKey("PlatformPriceAdjust");
//                processVo.setData(JSONUtil.toJsonStr(submitList));
//                processVo.setVersion(resultVo1.getData().getVersion());
//                processVo.setSubject(resultVo1.getData().getProcessName());
//                try {
//                    ResultVo<String> resultVo2 = zbpmService.startProcess(processVo);
//                    if (!resultVo2.isSuccess()) {
//                        // 提报失败
//                        history.setStatus(50);
//                        somListingPriceHistoryMapper.updateById(history);
//                    }
//                } catch (Exception e) {
//                    // 提报异常
//                    history.setStatus(50);
//                    somListingPriceHistoryMapper.updateById(history);
//                    throw new ValidateException(e.getMessage());
//                }
//            }
        }catch (Exception e){
            history.setStatus(50);
            somListingPriceHistoryMapper.updateById(history);
            throw new ValidateException(e.getMessage());
        }
    }

    /**
     * Validate
     * 校验
     *
     * @param defendPriceVo
     * @return boolean
     * <AUTHOR>
     * @history
     */
    private boolean Validate(SomListingPriceVo defendPriceVo) {
        boolean b = true;
        if (ObjectUtil.isEmpty(defendPriceVo) || StrUtil.isBlank(defendPriceVo.getPlatform()) || StrUtil.isBlank(defendPriceVo.getSite()) || StrUtil.isBlank(defendPriceVo.getSku()) || StrUtil.isBlank(defendPriceVo.getSellerSku())) {
            b = false;
            return b;
        }
        if (ObjectUtil.isEmpty(defendPriceVo.getPrice()) || ObjectUtil.isEmpty(defendPriceVo.getShippingCost()) || ObjectUtil.isEmpty(defendPriceVo.getMinimumPrice()) || ObjectUtil.isEmpty(defendPriceVo.getMaximumPrice())) {
            b = false;
            return b;
        }
        return b;
    }

    /**
     * importExcel
     * 导入
     *
     * @param list
     * @param tokenUser
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(List<SomListingPriceVo> list, TokenUserInfo tokenUser) throws ValidateException{
        StringBuilder result = new StringBuilder();
        StringBuilder result1 = new StringBuilder();
        StringBuilder result2 = new StringBuilder();
        StringBuilder result3 = new StringBuilder();
        StringBuilder result4 = new StringBuilder();
        StringBuilder result5 = new StringBuilder();
        StringBuilder result6 = new StringBuilder();
        // 校验重复
        Map<String, List<SomListingPriceVo>> map = list.stream().collect(Collectors.groupingBy(p -> String.format("%s,%s,%s", p.getPlatform(), p.getSite(), p.getSellerSku())));
        for (String key : map.keySet()) {
            if (map.get(key).size() > 1) {
                List<String> strings = Arrays.asList(key.split(","));
                result5.append(strings.get(0)).append("\n").append(strings.get(1)).append("\n").append(strings.get(2)).append("\n");
            }
        }
        if(StrUtil.isNotBlank(result5)){
            result5.append("不允许出现重复，请检查导入的数据");
            return result5.toString();
        }
        // 查询所有B2C平台
        List<McPlatformProperties> platformProperties = mcPlatformPropertiesMapper.createLambdaQuery().andEq("sale_channel", "B2C").select();
        list.forEach(f -> {
            McPlatformProperties obj = platformProperties.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(obj)) {
                result.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n");
            }
            if (StrUtil.isBlank(f.getReasonRemark())) {
                result1.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            }
            if (ObjectUtil.isEmpty(f.getPrice()) || ObjectUtil.isEmpty(f.getMinimumPrice()) || ObjectUtil.isEmpty(f.getMaximumPrice())) {
                result2.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            }
        });
        if (StrUtil.isNotBlank(result)) {
            result.append("不是B2C渠道,请检查数据");
            return result.toString();
        }
        if (StrUtil.isNotBlank(result1)) {
            result1.append("调价原因不能为空");
            return result1.toString();
        }
        if (StrUtil.isNotBlank(result2)) {
            result2.append("Price/Minimum price/Maximum price不能为空");
            return result2.toString();
        }
        List<String> platforms = list.stream().map(m -> m.getPlatform()).distinct().collect(Collectors.toList());
        List<String> sites = list.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
        List<String> sellerSkus = list.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McProductSales> productSalesList = mcProductSalesMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("display_product_code", sellerSkus).select();
        List<SomStandardPrice> standardPriceList = somStandardPriceMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).select();
        List<SomListingPriceHistory> historyList = somListingPriceHistoryMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).andEq("status",10).select();
        if (CollectionUtil.isNotEmpty(productSalesList) || CollectionUtil.isNotEmpty(standardPriceList)) {
            list.forEach(f -> {
                // 产品销售视图
                McProductSales obj = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                        && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(obj)) {
                    result.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
                }
                // 定价管理
                SomStandardPrice obj1 = standardPriceList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                        && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(obj1)) {
                    result4.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
                }
                if (f.getPrice().compareTo(f.getMinimumPrice()) == -1 || f.getPrice().compareTo(f.getMaximumPrice()) == 1) {
                    result3.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
                }
                long count = historyList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())
                        && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).count();
                if (count != 0) {
                    result6.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
                }
            });
        }
        if (StrUtil.isNotBlank(result)) {
            result.append("在系统中不存在，不允许导入");
            return result.toString();
        }
        if (StrUtil.isNotBlank(result3)) {
            result3.append("Price必须介于“Minimum Price”和“Maximum Price”之间！");
            return result3.toString();
        }
        if (StrUtil.isNotBlank(result4)) {
            result4.append("未维护定价，不允许导入");
            return result4.toString();
        }
        if (StrUtil.isNotBlank(result6)) {
            result6.append("在调价流程审批中，需将此数据删除后重新导入");
            return result6.toString();
        }
        List<SomListingPrice> priceAddList = new ArrayList<>();
        List<SomListingPrice> priceUpdateList = new ArrayList<>();
        List<SomListingPriceHistory> priceHistoryList = new ArrayList<>();
        List<SomListingPrice> listingPrices = somListingPriceMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).select();
        list.forEach(f -> {
            SomListingPriceHistory history = ConvertUtils.beanConvert(f, SomListingPriceHistory.class);
            McPlatformProperties temp1 = platformProperties.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(temp1)) {
                history.setCurrency(temp1.getCurrencyCode());
            }
            if (StrUtil.equalsIgnoreCase("Fixed prices", f.getDiscountTypeName())) {
                history.setDiscountType(20);
            } else if (StrUtil.equalsIgnoreCase("Percent off business price", f.getDiscountTypeName())) {
                history.setDiscountType(10);
            }
            history.setAid(IdUtil.fastSimpleUUID());
            history.setStatus(40);
            history.setReasonRemark("运营管理员批量操作");
            history.setLastModifyNum(tokenUser.getJobNumber());
            history.setLastModifyName(tokenUser.getUserName());
            history.setLastModifyTime(DateTime.now().toJdkDate());
            priceHistoryList.add(history);
            SomListingPrice obj = listingPrices.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                    && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(obj)) {
                obj.setPrice(f.getPrice());
                obj.setShippingCost(f.getShippingCost());
                obj.setMinimumPrice(f.getMinimumPrice());
                obj.setMaximumPrice(f.getMaximumPrice());
                obj.setBusinessPrice(f.getBusinessPrice());
                if (StrUtil.equalsIgnoreCase("Fixed prices", f.getDiscountTypeName())) {
                    obj.setDiscountType(20);
                } else if (StrUtil.equalsIgnoreCase("Percent off business price", f.getDiscountTypeName())) {
                    obj.setDiscountType(10);
                }
                obj.setQuantityLowerBound1(f.getQuantityLowerBound1());
                obj.setQuantityPrice1(f.getQuantityPrice1());
                obj.setQuantityLowerBound2(f.getQuantityLowerBound2());
                obj.setQuantityPrice2(f.getQuantityPrice2());
                obj.setQuantityLowerBound3(f.getQuantityLowerBound3());
                obj.setQuantityPrice3(f.getQuantityPrice3());
                obj.setQuantityLowerBound4(f.getQuantityLowerBound4());
                obj.setQuantityPrice4(f.getQuantityPrice4());
                obj.setQuantityLowerBound5(f.getQuantityLowerBound5());
                obj.setQuantityPrice5(f.getQuantityPrice5());
                obj.setReasonRemark(f.getReasonRemark());
                obj.setLastModifyNum(tokenUser.getJobNumber());
                obj.setLastModifyName(tokenUser.getUserName());
                obj.setLastModifyTime(DateTime.now().toJdkDate());
                priceUpdateList.add(obj);
            } else {
                SomListingPrice price = ConvertUtils.beanConvert(f, SomListingPrice.class);
                if (StrUtil.equalsIgnoreCase("Fixed prices", f.getDiscountTypeName())) {
                    price.setDiscountType(20);
                } else if (StrUtil.equalsIgnoreCase("Percent off business price", f.getDiscountTypeName())) {
                    price.setDiscountType(10);
                }
                price.setAid(IdUtil.fastSimpleUUID());
                price.setPushStatus(10);
                McPlatformProperties temp = platformProperties.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(temp)) {
                    price.setCurrency(temp.getCurrencyCode());
                }
                price.setLastModifyNum(tokenUser.getJobNumber());
                price.setLastModifyName(tokenUser.getUserName());
                price.setLastModifyTime(DateTime.now().toJdkDate());
                priceAddList.add(price);
            }
        });
        if (CollectionUtil.isNotEmpty(priceAddList)) {
            somListingPriceMapper.insertBatch(priceAddList);
        }
        if (CollectionUtil.isNotEmpty(priceUpdateList)) {
            somListingPriceMapper.updateBatch(priceUpdateList);
        }
        if (CollectionUtil.isNotEmpty(priceHistoryList)) {
            somListingPriceHistoryMapper.insertBatch(priceHistoryList);
        }
        return result.toString();
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String exportExcel(SomListingPricePageSearchVo exportVo) {
        List<SomListingPriceVo> exportVoList = somListingPriceMapper.queryExportData(exportVo);
        if (!exportVoList.isEmpty()) {
            exportVoList.forEach(f -> {
                if (ObjectUtil.equal(f.getDiscountType(), 20)) {
                    f.setDiscountTypeName("Fixed prices");
                } else {
                    f.setDiscountTypeName("Percent off business price");
                }
            });
            try {
                Workbook workbook;
                ExportParams params = new ExportParams(null, "平台价管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomListingPriceVo.class, exportVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * batchUpdatePrice
     * 批量更新价格
     *
     * @param list
     * @param tokenUser
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String batchUpdatePrice(List<SomListingPriceExtVo> list, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder result1 = new StringBuilder();
        StringBuilder result2 = new StringBuilder();
        StringBuilder result3 = new StringBuilder();
        StringBuilder result4 = new StringBuilder();
        StringBuilder result5 = new StringBuilder();
        StringBuilder result6 = new StringBuilder();
        // 校验重复
        Map<String, List<SomListingPriceVo>> map = list.stream().collect(Collectors.groupingBy(p -> String.format("%s,%s,%s", p.getPlatform(), p.getSite(), p.getSellerSku())));
        for (String key : map.keySet()) {
            if (map.get(key).size() > 1) {
                List<String> strings = Arrays.asList(key.split(","));
                result5.append(strings.get(0)).append("\n").append(strings.get(1)).append("\n").append(strings.get(2)).append("\n");
            }
        }
        if(StrUtil.isNotBlank(result5)){
            result5.append("不允许出现重复，请检查导入的数据");
            return result5.toString();
        }
        List<String> platforms = list.stream().map(m -> m.getPlatform()).distinct().collect(Collectors.toList());
        List<String> sites = list.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
        List<String> sellerSkus = list.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McPlatformProperties> platformProperties = mcPlatformPropertiesMapper.createLambdaQuery().andEq("sale_channel", "B2C").select();
        // 产品销售视图
        List<McProductSales> productSalesList = mcProductSalesMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("display_product_code", sellerSkus).select();
        // 定价信息
        List<SomStandardPrice> standardPriceList = somStandardPriceMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).select();
        // 审核中数据
        List<SomListingPriceHistory> historyList = somListingPriceHistoryMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).andEq("status", 10).select();
        //
        List<McListingInfoAmazon> listingInfoList = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andIn("site", sites).andIn("seller_sku", sellerSkus).select();
        //
        ValidateDealVo validateVo = new ValidateDealVo();
        validateVo.setSiteList(sites);
        validateVo.setSellerSkuList(sellerSkus);
        validateVo.setAsinList(listingInfoList.stream().map(m->m.getAsinCode()).collect(Collectors.toList()));
        List<ValidateDealVo> couponDealList = dynamicSqlManager.getMapper(SomDealMapper.class).checkCouponAndDeal(validateVo);
        // 校验
        list.forEach(f -> {
            // 产品销售视图
            McProductSales obj1 = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                    && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(obj1)) {
                result1.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            } else {
                f.setSku(obj1.getProductMainCode());
            }
            // 定价信息
            SomStandardPrice obj2 = standardPriceList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                    && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(obj2)) {
                result2.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            }
            // 审核数据
            SomListingPriceHistory obj3 = historyList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform())
                    && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(obj3)) {
                result3.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            }
            listingInfoList.stream().filter(t->StrUtil.equals(f.getSite(),t.getSite()) && StrUtil.equals(f.getSellerSku(),t.getSellerSku())).findFirst().ifPresent(ps->{
                f.setAsin(ps.getAsinCode());
            });
            ValidateDealVo couponDeal = couponDealList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getAsin(), p.getAsin())).findFirst().orElse(null);
            SomPromotionAdditionalItemVo promotion = dynamicSqlManager.getMapper(SomPromotionMapper.class).checkPromotion(validateVo);
            if (ObjectUtil.isNotEmpty(couponDeal) || (ObjectUtil.isNotEmpty(promotion))) {
                result6.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
            }
        });
        if (StrUtil.isNotBlank(result1)) {
            result1.append("在系统中不存在，不允许导入");
            return result1.toString();
        }
        if (StrUtil.isNotBlank(result2)) {
            result2.append("未维护定价，不允许导入");
            return result2.toString();
        }
        if (StrUtil.isNotBlank(result3)) {
            result3.append("当前产品已申请Price调整，审批结束前不允许二次调价，审批进度可点击「Price」进行查看");
            return result3.toString();
        }
        if (StrUtil.isNotBlank(result6)) {
            result6.append("正在参加营销活动，不允许调整价格");
            return result6.toString();
        }
        // 查询BU信息
        List<String> skus = list.stream().map(m -> m.getSku()).distinct().collect(Collectors.toList());
        ResultVo<List<BuInfoDto>> resultVo = iProductSalesService.findBuBySkus(skus);
        if (!resultVo.isSuccess()) {
            throw new ValidateException("根据SKU获取BU信息出错:" + resultVo.getMessage());
        }
        if (ObjectUtil.isEmpty(resultVo.getData())) {
            throw new ValidateException("BU信息没有维护，请维护信息");
        }
        // 查询平台价
        List<SomListingPrice> priceAddList = new ArrayList<>();
        List<SomListingPrice> priceUpdateList = new ArrayList<>();
        List<SomListingPriceHistory> priceHistoryList = new ArrayList<>();
        List<SomListingPrice> listingPriceList = somListingPriceMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).select();
        list.forEach(f -> {
            // 平台价
            SomListingPrice obj = listingPriceList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())
                    && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
            SomListingPriceHistory history = new SomListingPriceHistory();
            Integer status = 40;
            SomListingPrice price = new SomListingPrice();
            if (ObjectUtil.isNotNull(obj)) {
                history = ConvertUtils.beanConvert(obj, SomListingPriceHistory.class);
                if(ObjectUtil.isEmpty(obj.getPrice()) || (obj.getPrice().compareTo(f.getPrice()) != 0)){
                    status = 10;
                }
                history.setPrice(f.getPrice());
                history.setStatus(status);
                price = ConvertUtils.beanConvert(obj, SomListingPrice.class);
                price.setLastModifyNum(tokenUser.getJobNumber());
                price.setLastModifyName(tokenUser.getUserName());
                price.setLastModifyTime(DateTime.now().toJdkDate());
                priceUpdateList.add(price);
            } else {
                history = ConvertUtils.beanConvert(f, SomListingPriceHistory.class);
                history.setStatus(10);
                history.setPrice(f.getPrice());
                McPlatformProperties temp = platformProperties.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getPlatform(), p.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), p.getSite())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(temp)) {
                    history.setCurrency(temp.getCurrencyCode());
                    price.setCurrency(temp.getCurrencyCode());
                }
                price = ConvertUtils.beanConvert(f, SomListingPrice.class);
                price.setAid(IdUtil.fastSimpleUUID());
                price.setPrice(BigDecimal.ZERO);
                price.setLastModifyNum(tokenUser.getJobNumber());
                price.setLastModifyName(tokenUser.getUserName());
                price.setLastModifyTime(DateTime.now().toJdkDate());
                priceAddList.add(price);
            }
            history.setAid(IdUtil.fastSimpleUUID());
            history.setLastModifyNum(tokenUser.getJobNumber());
            history.setLastModifyName(tokenUser.getUserName());
            history.setLastModifyTime(DateTime.now().toJdkDate());
            priceHistoryList.add(history);
        });
        if(CollectionUtil.isNotEmpty(priceHistoryList)){
            somListingPriceHistoryMapper.insertBatch(priceHistoryList);
        }
        // 组织流程信息
        SomStandardPriceSearchVo searchVo = new SomStandardPriceSearchVo();
        searchVo.setPlatforms(platforms);
        searchVo.setSites(sites);
        searchVo.setSellerSkus(sellerSkus);
        List<SomListingPriceExtVo> submitList = new ArrayList<>();
        List<SomListingPriceExtVo> tepmList = mcProductSalesMapper.querySubmitInfoList(searchVo);
        for(SomListingPriceExtVo vo : list){
            SomListingPriceExtVo priceExtVo = tepmList.stream().filter(f -> StrUtil.equals(vo.getPlatform(), f.getPlatform()) && StrUtil.equals(vo.getSite(), f.getSite()) && StrUtil.equals(vo.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
            if(ObjectUtil.isNotEmpty(priceExtVo)){
                submitList.add(priceExtVo);
            }
        }
        if (CollectionUtil.isNotEmpty(submitList)) {
            List<SomListingPriceHistory> templist = somListingPriceHistoryMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerSkus).andEq("status", 20).desc("last_modify_time").select();
            submitList.forEach(f -> {
                if(CollectionUtil.isNotEmpty(templist)) {
                    List<SomListingPriceHistory> collect = templist.stream().filter(s -> StrUtil.equalsIgnoreCase(f.getPlatform(), s.getPlatform()) && StrUtil.equalsIgnoreCase(f.getSite(), s.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), s.getSellerSku())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(collect)) {
                        f.setLastModifyTime(collect.get(0).getLastModifyTime());
                    }
                }
                // BU信息
                resultVo.getData().stream().filter(p -> StrUtil.equalsIgnoreCase(p.getSkuCode(), f.getSku())).findFirst().ifPresent(ps -> {
                    f.setBusEmpCode(ps.getBusEmpCode());
                    f.setBusEmpNameCN(ps.getBusEmpNameCN());
                    f.setProductGroupName(ps.getProductGroupName());
                    f.setManagerEmpNameCN(ps.getManagerEmpNameCN());
                });
            });
            long count = submitList.stream().filter(f -> StrUtil.isBlank(f.getBusEmpCode()) || StrUtil.isBlank(f.getBusEmpNameCN())).count();
            if(count > 0){
                submitList.forEach(f -> {
                    result4.append(f.getPlatform()).append("\n").append(f.getSite()).append("\n").append(f.getSellerSku()).append("\n");
                });
            }
            if (StrUtil.isNotBlank(result4)) {
                result4.append("BU信息没有维护，请维护信息");
                List<String> aidList = priceHistoryList.stream().map(m -> m.getAid()).collect(Collectors.toList());
                somListingPriceHistoryMapper.createLambdaQuery().andIn("aid",aidList).delete();
                return result4.toString();
            }
            // 查询流程信息
            ProcessDefinitionVo vo = new ProcessDefinitionVo();
            vo.setKey("PlatformPriceAdjust");
//            ResultVo<ZBPMProcessVo> resultVo1 = zbpmService.getProcessByProcessKey(vo);
//            if (!resultVo1.isSuccess()) {
//                throw new ValidateException("根据流程关键字(PlatformPriceAdjust)获取流程信息出错{}", resultVo1.getMessage());
//            }
//            if (ObjectUtil.isEmpty(resultVo1.getData())) {
//                throw new ValidateException("根据流程关键字(PlatformPriceAdjust)获取流程信息为空");
//            }
//            // 开启流程
//            ProcessVo processVo = new ProcessVo();
//            processVo.setFlowKey("PlatformPriceAdjust");
//            processVo.setData(JSONUtil.toJsonStr(submitList));
//            processVo.setVersion(resultVo1.getData().getVersion());
//            processVo.setSubject(resultVo1.getData().getProcessName());
//            try {
//                ResultVo<String> resultVo2 = zbpmService.startProcess(processVo);
//                if (!resultVo2.isSuccess()) {
//                    // 提报失败
//                    somListingPriceHistoryMapper.updateFailStatus(priceHistoryList);
//                }
//            } catch (Exception e) {
//                // 提报异常
//                somListingPriceHistoryMapper.updateFailStatus(priceHistoryList);
//                throw new ValidateException(e.getMessage());
//            }
//            if(CollectionUtil.isNotEmpty(priceAddList)){
//                somListingPriceMapper.insertBatch(priceAddList);
//            }
//            if(CollectionUtil.isNotEmpty(priceUpdateList)){
//                somListingPriceMapper.updateBatch(priceUpdateList);
//            }
        }
        return Strings.EMPTY;
    }

    /**
     * validateDeal
     * 校验营销活动
     * @param validateVo
     * <AUTHOR>
     * @history
     */
    public void validateDeal(ValidateDealVo validateVo) throws ValidateException {
        if (StrUtil.isBlank(validateVo.getSite()) || (StrUtil.isBlank(validateVo.getSellerSku()))) {
            throw new ValidateException("参数存在空值,请检查");
        }
        McListingInfoAmazon listingInfo = dynamicSqlManager.getMapper(McListingInfoAmazonMapper.class).createLambdaQuery().andEq("site", validateVo.getSite()).andEq("seller_sku", validateVo.getSellerSku()).single();
        if (ObjectUtil.isEmpty(listingInfo) || StrUtil.isBlank(listingInfo.getAsinCode())) {
            throw new ValidateException("查询不到Asin,请检查");
        }
        validateVo.setAsin(listingInfo.getAsinCode());
        List<ValidateDealVo> couponDealList = dynamicSqlManager.getMapper(SomDealMapper.class).checkCouponAndDeal(validateVo);
        if (CollectionUtil.isNotEmpty(couponDealList)) {
            throw new ValidateException("当前产品正在参加营销活动，不允许调整价格");
        }
        SomPromotionAdditionalItemVo promotion = dynamicSqlManager.getMapper(SomPromotionMapper.class).checkPromotion(validateVo);
        if (ObjectUtil.isNotEmpty(promotion)) {
            throw new ValidateException("当前产品正在参加营销活动，不允许调整价格");
        }
    }
}
