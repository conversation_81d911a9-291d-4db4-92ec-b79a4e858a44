package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomListingPriceHistory;
import com.zielsmart.mc.repository.mapper.SomListingPriceHistoryMapper;
import com.zielsmart.mc.repository.mapper.SomListingPriceMapper;
import com.zielsmart.mc.vo.SomListingPriceHistoryVo;
import com.zielsmart.mc.vo.SomListingPricePageSearchVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomListingPriceHistoryService {

    @Resource
    private SomListingPriceHistoryMapper somListingPriceHistoryMapper;
    @Resource
    private SomListingPriceMapper somListingPriceMapper;

    /**
     * searchHistory
     * 历史记录
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomListingPriceHistoryVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomListingPriceHistoryVo> searchHistory(SomListingPricePageSearchVo searchVo) throws ValidateException {
        List<SomListingPriceHistoryVo> historyVoList = new ArrayList<>();
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getKeyWord()) || StrUtil.isBlank(searchVo.getPlatform()) || StrUtil.isBlank(searchVo.getSite())) {
            throw new ValidateException("查询参数存在空值,请检查");
        }
        List<SomListingPriceHistory> historyList = somListingPriceHistoryMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).andEq("seller_sku", searchVo.getKeyWord()).desc("last_modify_time").select();
        if (CollectionUtil.isNotEmpty(historyList)) {
            List<SomListingPriceHistoryVo> tempHistoryVoList = ConvertUtils.listConvert(historyList, SomListingPriceHistoryVo.class);
            if (tempHistoryVoList.size() > 3) {
                historyVoList.addAll(tempHistoryVoList.subList(0, 3));
            } else {
                historyVoList.addAll(tempHistoryVoList);
            }
        }
        historyVoList.forEach(f -> {
            if (ObjectUtil.isNotNull(f.getDiscountType())) {
                if (10 == f.getDiscountType()) {
                    f.setDiscountTypeName("Percent off business price");
                } else if (20 == f.getDiscountType()) {
                    f.setDiscountTypeName("Fixed prices");
                }
            }
            if (ObjectUtil.isNotEmpty(f.getStatus())) {
                switch (f.getStatus()) {
                    case 10:
                        f.setStatusName("审批中");
                        break;
                    case 20:
                        f.setStatusName("审批通过");
                        break;
                    case 30:
                        f.setStatusName("审批未通过");
                        break;
                    case 40:
                        f.setStatusName("无需审批");
                        break;
                }
            }
        });
        return historyVoList;
    }
}
