package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * woot反馈的采购订单
 * gen by 代码生成器 2024-06-04
 */

@Table(name = "mc.som_woot_purchase_order")
public class SomWootPurchaseOrder implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 供应商ID
     */
    @Column("vendor_id")
    private String vendorId;
    /**
     * 客户经理反馈的PO号
     */
    @Column("purchase_order")
    private String purchaseOrder;
    /**
     * 采购订单生成日期
     */
    @Column("purchase_order_date")
    private Date purchaseOrderDate;
    /**
     * Woot客户经理
     */
    @Column("buyer")
    private String buyer;
    /**
     * 字典值：10.FBA  20.Dropship
     */
    @Column("ship_from")
    private String shipFrom;
    /**
     * 退回单号
     */
    @Column("return_order")
    private String returnOrder;
    /**
     * 退回时间
     */
    @Column("return_order_date")
    private Date returnOrderDate;
    /**
     * 活动起始时间
     */
    @Column("start_date")
    private Date startDate;
    /**
     * 活动截止时间
     */
    @Column("end_date")
    private Date endDate;
    /**
     * 采购订单附件
     */
    @Column("purchase_order_file_url")
    private String purchaseOrderFileUrl;
    /**
     * 退回订单附件
     */
    @Column("return_order_file_url")
    private String returnOrderFileUrl;
    /**
     * 发票附件
     */
    @Column("invoice_file_url")
    private String invoiceFileUrl;
    /**
     * 付款状态：10. 开票中 20. 等待60天账期 30.已付款
     */
    @Column("payment_status")
    private Integer paymentStatus;
    /**
     * 预计付款日期
     */
    @Column("estimate_payment_date")
    private Date estimatePaymentDate;
    /**
     * 回款备注
     */
    @Column("cash_remark")
    private String cashRemark;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 数据创建日期
     */
    @Column("create_time")
    private Date createTime;
    /**
     * deal 类型
     */
    @Column("deal_type")
    private String dealType;

    public SomWootPurchaseOrder() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 供应商ID
     *
     * @return
     */
    public String getVendorId() {
        return vendorId;
    }

    /**
     * 供应商ID
     *
     * @param vendorId
     */
    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    /**
     * 客户经理反馈的PO号
     *
     * @return
     */
    public String getPurchaseOrder() {
        return purchaseOrder;
    }

    /**
     * 客户经理反馈的PO号
     *
     * @param purchaseOrder
     */
    public void setPurchaseOrder(String purchaseOrder) {
        this.purchaseOrder = purchaseOrder;
    }

    /**
     * 采购订单生成日期
     *
     * @return
     */
    public Date getPurchaseOrderDate() {
        return purchaseOrderDate;
    }

    /**
     * 采购订单生成日期
     *
     * @param purchaseOrderDate
     */
    public void setPurchaseOrderDate(Date purchaseOrderDate) {
        this.purchaseOrderDate = purchaseOrderDate;
    }

    /**
     * Woot客户经理
     *
     * @return
     */
    public String getBuyer() {
        return buyer;
    }

    /**
     * Woot客户经理
     *
     * @param buyer
     */
    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }

    /**
     * 字典值：10.FBA  20.Dropship
     *
     * @return
     */
    public String getShipFrom() {
        return shipFrom;
    }

    /**
     * 字典值：10.FBA  20.Dropship
     *
     * @param shipFrom
     */
    public void setShipFrom(String shipFrom) {
        this.shipFrom = shipFrom;
    }

    /**
     * 退回单号
     *
     * @return
     */
    public String getReturnOrder() {
        return returnOrder;
    }

    /**
     * 退回单号
     *
     * @param returnOrder
     */
    public void setReturnOrder(String returnOrder) {
        this.returnOrder = returnOrder;
    }

    /**
     * 退回时间
     *
     * @return
     */
    public Date getReturnOrderDate() {
        return returnOrderDate;
    }

    /**
     * 退回时间
     *
     * @param returnOrderDate
     */
    public void setReturnOrderDate(Date returnOrderDate) {
        this.returnOrderDate = returnOrderDate;
    }

    /**
     * 活动起始时间
     *
     * @return
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 活动起始时间
     *
     * @param startDate
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 活动截止时间
     *
     * @return
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 活动截止时间
     *
     * @param endDate
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 采购订单附件
     *
     * @return
     */
    public String getPurchaseOrderFileUrl() {
        return purchaseOrderFileUrl;
    }

    /**
     * 采购订单附件
     *
     * @param purchaseOrderFileUrl
     */
    public void setPurchaseOrderFileUrl(String purchaseOrderFileUrl) {
        this.purchaseOrderFileUrl = purchaseOrderFileUrl;
    }

    /**
     * 退回订单附件
     *
     * @return
     */
    public String getReturnOrderFileUrl() {
        return returnOrderFileUrl;
    }

    /**
     * 退回订单附件
     *
     * @param returnOrderFileUrl
     */
    public void setReturnOrderFileUrl(String returnOrderFileUrl) {
        this.returnOrderFileUrl = returnOrderFileUrl;
    }

    /**
     * 发票附件
     *
     * @return
     */
    public String getInvoiceFileUrl() {
        return invoiceFileUrl;
    }

    /**
     * 发票附件
     *
     * @param invoiceFileUrl
     */
    public void setInvoiceFileUrl(String invoiceFileUrl) {
        this.invoiceFileUrl = invoiceFileUrl;
    }

    /**
     * 付款状态：10. 开票中 20. 等待60天账期 30.已付款
     *
     * @return
     */
    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    /**
     * 付款状态：10. 开票中 20. 等待60天账期 30.已付款
     *
     * @param paymentStatus
     */
    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    /**
     * 预计付款日期
     *
     * @return
     */
    public Date getEstimatePaymentDate() {
        return estimatePaymentDate;
    }

    /**
     * 预计付款日期
     *
     * @param estimatePaymentDate
     */
    public void setEstimatePaymentDate(Date estimatePaymentDate) {
        this.estimatePaymentDate = estimatePaymentDate;
    }

    /**
     * 回款备注
     *
     * @return
     */
    public String getCashRemark() {
        return cashRemark;
    }

    /**
     * 回款备注
     *
     * @param cashRemark
     */
    public void setCashRemark(String cashRemark) {
        this.cashRemark = cashRemark;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 数据创建日期
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 数据创建日期
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDealType() {
        return dealType;
    }

    public void setDealType(String dealType) {
        this.dealType = dealType;
    }
}
