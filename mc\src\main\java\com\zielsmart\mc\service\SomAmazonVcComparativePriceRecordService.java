package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-01-21 18:09:07
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomAmazonVcComparativePriceRecordService {

    @Resource
    private SomAmazonVcComparativePriceRecordMapper somAmazonVcComparativePriceRecordMapper;
    @Resource
    private SomAmazonVcComparativePriceRecordHandleRecordService comparativePriceRecordHandleRecordService;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SysUserNeweyaMapper sysUserNeweyaMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo<SomAmazonVcComparativePriceRecordVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomAmazonVcComparativePriceRecordVo> queryByPage(SomAmazonVcComparativePriceRecordPageSearchVo searchVo) {
        // 创建分页请求
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());

        // 分页查询数据
        PageResult<SomAmazonVcComparativePriceRecordVo> pageResult = somAmazonVcComparativePriceRecordMapper.queryByPage(searchVo, pageRequest);

        if (pageResult.getList().isEmpty()) {
            return ConvertUtils.pageConvert(pageResult, SomAmazonVcComparativePriceRecordVo.class, searchVo);
        }

        // 获取字典值映射
        Map<String, Map<String, String>> dicListMap = getDictionaryMaps();

        // 应用字典值转义
        applyDictionaryValues(pageResult.getList(), dicListMap);

        // 处理三方价格信息
        processThirdPartyPrices(pageResult.getList());

        // 获取并处理操作日志
        processHandleRecords(pageResult.getList(), dicListMap);

        return ConvertUtils.pageConvert(pageResult, SomAmazonVcComparativePriceRecordVo.class, searchVo);
    }

    /**
     * 获取字典值映射
     */
    private Map<String, Map<String, String>> getDictionaryMaps() {
        List<String> typeCodes = Arrays.asList("VcPriceOnPromotion", "VcPriceComparisonType", "VcComparativePriceHandleStatus", "VcComparativeReason", "SalesProductStatus");
        List<McDictionaryInfo> dicList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class)
                .createLambdaQuery()
                .andIn("item_type_code", typeCodes)
                .select();

        return dicList.stream()
                .collect(Collectors.groupingBy(
                        McDictionaryInfo::getItemTypeCode,
                        Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (existing, replacement) -> existing)
                ));
    }

    /**
     * 应用字典值转义
     * 是否正在参加营销活动、比价类型、最新处理状态、最新比价原因
     */
    private void applyDictionaryValues(List<SomAmazonVcComparativePriceRecordVo> records, Map<String, Map<String, String>> dicListMap) {
        records.forEach(record -> {
            // VcPriceOnPromotion
            Optional.ofNullable(record.getOnPromotion())
                    .map(Object::toString)
                    .ifPresent(value -> record.setOnPromotionShow(dicListMap.get("VcPriceOnPromotion") != null ? dicListMap.get("VcPriceOnPromotion").get(value) : null));

            // VcPriceComparisonType
            Optional.ofNullable(record.getComparativeType())
                    .map(Object::toString)
                    .ifPresent(value -> record.setComparativeTypeShow(dicListMap.get("VcPriceComparisonType") != null ? dicListMap.get("VcPriceComparisonType").get(value) : null));

            // VcComparativePriceHandleStatus
            Optional.ofNullable(record.getHandleStatus())
                    .map(Object::toString)
                    .ifPresent(value -> record.setHandleStatusShow(dicListMap.get("VcComparativePriceHandleStatus") != null ? dicListMap.get("VcComparativePriceHandleStatus").get(value) : null));

            // VcComparativeReason
            Optional.ofNullable(record.getComparativeReason())
                    .map(Object::toString)
                    .ifPresent(value -> record.setComparativeReasonShow(dicListMap.get("VcComparativeReason") != null ? dicListMap.get("VcComparativeReason").get(value) : null));

            // 展示码状态
            Optional.ofNullable(record.getSellerSkuStatusCode())
                    .map(Object::toString)
                    .ifPresent(value -> record.setSellerSkuStatusShow(dicListMap.get("SalesProductStatus") != null ? dicListMap.get("SalesProductStatus").get(value) : null));
        });
    }

    /**
     * 处理三方价格信息
     */
    private void processThirdPartyPrices(List<SomAmazonVcComparativePriceRecordVo> records) {
        List<String> aidList = records.stream()
                .map(SomAmazonVcComparativePriceRecordVo::getAid)
                .collect(Collectors.toList());

        if (aidList.isEmpty()) {
            return;
        }

        List<SomAmazonVcPriceComparison> vcPriceComparisonList = dynamicSqlManager.getMapper(SomAmazonVcPriceComparisonMapper.class)
                .createLambdaQuery()
                .andIn("price_aid", aidList)
                .andEq("status", 1)
                .orderBy("create_time desc")
                .select();

        Map<String, List<SomAmazonVcPriceComparison>> vcPriceComparisonListMap = vcPriceComparisonList.stream()
                .collect(Collectors.groupingBy(SomAmazonVcPriceComparison::getPriceAid));

        records.forEach(record -> Optional.ofNullable(record.getAid())
                .filter(vcPriceComparisonListMap::containsKey)
                .ifPresent(aid -> record.setThresholdPriceList(vcPriceComparisonListMap.get(aid))));
    }

    /**
     * 获取并处理操作日志
     */
    private void processHandleRecords(List<SomAmazonVcComparativePriceRecordVo> records, Map<String, Map<String, String>> dicListMap) {
        List<String> aidList = records.stream()
                .map(SomAmazonVcComparativePriceRecordVo::getAid)
                .collect(Collectors.toList());

        if (aidList.isEmpty()) {
            return;
        }

        List<SomAmazonVcComparativePriceRecordHandleRecord> handleRecordList = dynamicSqlManager.getMapper(SomAmazonVcComparativePriceRecordHandleRecordMapper.class)
                .createLambdaQuery()
                .andIn("cid", aidList)
                .orderBy("create_time desc")
                .select();

        Map<String, List<SomAmazonVcComparativePriceRecordHandleRecord>> handleRecordListMap = handleRecordList.stream()
                .collect(Collectors.groupingBy(SomAmazonVcComparativePriceRecordHandleRecord::getCid));

        handleRecordList.forEach(handleRecord -> {
            Optional.ofNullable(handleRecord.getHandleStatus())
                    .map(Object::toString)
                    .ifPresent(value -> handleRecord.setHandleStatusShow(dicListMap.getOrDefault("VcComparativePriceHandleStatus", Collections.emptyMap()).get(value)));

            Optional.ofNullable(handleRecord.getComparativeReason())
                    .map(Object::toString)
                    .ifPresent(value -> handleRecord.setComparativeReasonShow(dicListMap.getOrDefault("VcComparativeReason", Collections.emptyMap()).get(value)));
        });

        records.forEach(record -> Optional.ofNullable(record.getAid())
                .filter(handleRecordListMap::containsKey)
                .ifPresent(aid -> record.setHandleRecordList(handleRecordListMap.get(aid))));
    }

    public String export(SomAmazonVcComparativePriceRecordPageSearchVo searchVo) throws ValidateException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomAmazonVcComparativePriceRecordVo> records = queryByPage(searchVo).getRecords();

        // 获取字典值映射
        Map<String, Map<String, String>> dicListMap = getDictionaryMaps();

        // 应用字典值转义
        applyDictionaryValues(records, dicListMap);

        // 处理三方价格信息
        processThirdPartyPrices(records);

        // 处理导出数据
        List<SomAmazonVcComparativePriceRecordExportVo> exportVos = new ArrayList<>();
        for (SomAmazonVcComparativePriceRecordVo record : records) {
            SomAmazonVcComparativePriceRecordExportVo exportVo = new SomAmazonVcComparativePriceRecordExportVo();
            BeanUtils.copyProperties(record, exportVo);
            exportVo.setThresholdPriceList(record.getThresholdPriceList());
            // 三方售价明细数据
            List<SomAmazonVcPriceComparison> thresholdPriceList = record.getThresholdPriceList();
            if (thresholdPriceList != null && !thresholdPriceList.isEmpty()) {
                StringBuilder thresholdPriceSiteListStr = new StringBuilder();
                StringBuilder thresholdPriceSkuListStr = new StringBuilder();
                StringBuilder thresholdPriceListStr = new StringBuilder();
                int lastIndex = thresholdPriceList.size() - 1;
                for (int i = 0; i < thresholdPriceList.size(); i++) {
                    SomAmazonVcPriceComparison priceComparison = thresholdPriceList.get(i);
                    thresholdPriceSiteListStr.append(priceComparison.getSite());
                    thresholdPriceSkuListStr.append(priceComparison.getSellerSku());
                    String price = priceComparison.getThresholdPrice() != null ? priceComparison.getThresholdPrice() + " " + priceComparison.getCurrency() : "";
                    thresholdPriceListStr.append(price);
                    if (i < lastIndex) {
                        thresholdPriceSiteListStr.append("\n");
                        thresholdPriceSkuListStr.append("\n");
                        thresholdPriceListStr.append("\n");
                    }
                }
                exportVo.setThresholdPriceSiteListStr(thresholdPriceSiteListStr.toString());
                exportVo.setThresholdPriceSkuListStr(thresholdPriceSkuListStr.toString());
                exportVo.setThresholdPriceListStr(thresholdPriceListStr.toString());
            }
            exportVos.add(exportVo);
        }

        if (!exportVos.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC比价记录");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomAmazonVcComparativePriceRecordExportVo.class, exportVos);

                // 自适应高度
                Sheet sheet = workbook.getSheetAt(0);
                // 遍历所有行
                for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;
                    // 遍历每一列，计算最大行高
                    int maxLines = 1;
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        Cell cell = row.getCell(j);
                        if (cell == null || cell.getStringCellValue() == null) continue;
                        // 根据换行符计算行数
                        String cellValue = cell.getStringCellValue();
                        int lines = cellValue.split("\n").length;
                        if (lines > maxLines) {
                            maxLines = lines;
                        }
                    }
                    // 设置行高 每行高度为15*行数
                    row.setHeight((short) (15 * maxLines * 20));
                }

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 查询处理日志
     *
     * @param searchVo 入参
     * @return 处理日志
     */
    public List<SomAmazonVcComparativePriceRecordHandleRecordVo> queryHandleRecord(SomAmazonVcComparativePriceRecordPageSearchVo searchVo) {
        return comparativePriceRecordHandleRecordService.queryByCid(searchVo.getAid());
    }

    /**
     * handle
     * 处理比价
     *
     * @param comparativePriceRecordHandleVo 处理比价入参
     * @param tokenUser                      当前登陆用户
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void handle(SomAmazonVcComparativePriceRecordHandleVo comparativePriceRecordHandleVo, TokenUserInfo tokenUser) throws ValidateException {
        // 处理比价基础参数核验
        checkComparativePriceRecordHandleParam(comparativePriceRecordHandleVo);
        // 数据核验
        SomAmazonVcComparativePriceRecord comparativePriceRecord = somAmazonVcComparativePriceRecordMapper.createLambdaQuery()
                .andEq("aid", comparativePriceRecordHandleVo.getAid())
                .single();
        if (comparativePriceRecord == null) {
            throw new ValidateException("比价记录不存在或已删除，请刷新页面！");
        }
        Integer completeStatus = 50;
        if (comparativePriceRecord.getHandleStatus().equals(completeStatus)) {
            throw new ValidateException("比价记录已处理完成，不允许再次处理！");
        }
        // 核验当前登陆人是否有权限或与处理人工号一致
        if (!tokenUser.getPowerList().contains("2005014006") && !comparativePriceRecord.getHandlePersonCode().equals(tokenUser.getJobNumber())) {
            throw new ValidateException("当前操作人与处理人不一致，不允许处理比价！");
        }
        // 获取最新处理人信息
        List<Integer> APPOINT_HANDLE_STATUS_LIST = Arrays.asList(30, 40);
        if (APPOINT_HANDLE_STATUS_LIST.contains(comparativePriceRecordHandleVo.getHandleStatus())) {
            McDictionaryInfo dictionaryInfo = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class)
                    .createLambdaQuery()
                    .andEq("item_type_code", "VC")
                    .andEq("item_lable", comparativePriceRecord.getSite())
                    .single();
            if (dictionaryInfo == null || dictionaryInfo.getItemValue4() == null) {
                throw new ValidateException("未配置VC Case处理专员");
            }
            McUser user = dynamicSqlManager.getMapper(McUserMapper.class)
                    .createLambdaQuery()
                    .andEq("job_number", dictionaryInfo.getItemValue4())
                    .single();
            if (user == null) {
                throw new ValidateException("VC Case处理专员配置错误");
            }
            comparativePriceRecordHandleVo.setHandlePersonCode(user.getJobNumber());
            comparativePriceRecordHandleVo.setHandlePersonName(user.getUserName());
        } else {
            McProductSales product = dynamicSqlManager.getMapper(McProductSalesMapper.class)
                    .createLambdaQuery()
                    .andEq("platform", "VC")
                    .andEq("sales_flag", 1)
                    .andEq("is_enabled", 1)
                    .andEq("site", comparativePriceRecord.getSite())
                    .andEq("display_product_code", comparativePriceRecord.getSellerSku())
                    .andEq("product_main_code", comparativePriceRecord.getSku())
                    .single();
            McUser user = dynamicSqlManager.getMapper(McUserMapper.class)
                    .createLambdaQuery()
                    .andEq("job_number", product.getSalesGroupEmptCode())
                    .single();
            comparativePriceRecordHandleVo.setHandlePersonCode(user.getJobNumber());
            comparativePriceRecordHandleVo.setHandlePersonName(user.getUserName());
        }

        // 处理比价，主表需要更新：最新处理人工号/最新处理人姓名/最新比价原因/最新处理状态/比价平台/比价价格/比价链接
        comparativePriceRecord = ConvertUtils.beanConvert(comparativePriceRecordHandleVo, SomAmazonVcComparativePriceRecord.class);
        somAmazonVcComparativePriceRecordMapper.batchHandleComparativePriceRecord(Collections.singletonList(comparativePriceRecord));
        // 处理比价，日志表需要新增一条处理日志
        comparativePriceRecordHandleRecordService.save(comparativePriceRecordHandleVo, tokenUser);
    }

    /**
     * vc比价导入批量处理
     *
     * @param handleImportVos 批量处理行
     * @param tokenUser       当前登陆用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchHandleImport(List<SomAmazonVcComparativePriceRecordHandleImportVo> handleImportVos, TokenUserInfo tokenUser) throws ValidateException {
        // 前置处理数据
        List<String> errors = PreMethodBatchHandleImportData(handleImportVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 核验数据，获取错误信息
        errors = checkBatchHandleImportData(handleImportVos, tokenUser);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：主表更新数据，日志表新增数据
        List<SomAmazonVcComparativePriceRecord> priceRecords = new ArrayList<>();
        List<SomAmazonVcComparativePriceRecordHandleRecord> handleRecords = new ArrayList<>();
        for (SomAmazonVcComparativePriceRecordHandleImportVo handleImportVo : handleImportVos) {
            if (StrUtil.isEmpty(handleImportVo.getCid())) {
                continue;
            }
            SomAmazonVcComparativePriceRecord record = ConvertUtils.beanConvert(handleImportVo, SomAmazonVcComparativePriceRecord.class);
            record.setAid(handleImportVo.getCid());
            priceRecords.add(record);
            SomAmazonVcComparativePriceRecordHandleRecord handleRecord = comparativePriceRecordHandleRecordService.transformImportVoToHandleRecord(handleImportVo, tokenUser);
            handleRecords.add(handleRecord);
        }
        somAmazonVcComparativePriceRecordMapper.batchHandleComparativePriceRecord(priceRecords);
        comparativePriceRecordHandleRecordService.batchInsert(handleRecords);
    }

    /**
     * 前置处理数据
     * @param handleImportVos 行记录
     * @return 错误信息
     */
    private List<String> PreMethodBatchHandleImportData(List<SomAmazonVcComparativePriceRecordHandleImportVo> handleImportVos) {
        List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class)
                .createLambdaQuery()
                .andEq("item_type_code", "VC")
                .select("item_lable", "item_value4");
        Map<String, McDictionaryInfo> dictionaryInfoMap = dictionaryInfoList.stream().collect(Collectors.toMap(
                McDictionaryInfo::getItemLable,
                Function.identity(),
                (existing, replacement) -> existing
        ));

        List<String> siteList = handleImportVos.stream().map(SomAmazonVcComparativePriceRecordHandleImportVo::getSite).collect(Collectors.toList());
        List<String> sellerSkuList = handleImportVos.stream().map(SomAmazonVcComparativePriceRecordHandleImportVo::getSellerSku).collect(Collectors.toList());
        List<McProductSales> productSalesList = dynamicSqlManager.getMapper(McProductSalesMapper.class)
                .createLambdaQuery()
                .andEq("platform", "VC")
                .andEq("sales_flag", 1)
                .andEq("is_enabled", 1)
                .andIn("site", siteList)
                .andIn("display_product_code", sellerSkuList)
                .select("site", "display_product_code", "sales_group_empt_code");
        Map<String, McProductSales> productSalesMap = productSalesList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x1)
        );

        // 错误汇总
        List<String> errors = new ArrayList<>();
        List<String> APPOINT_HANDLE_STATUS_LIST = Arrays.asList("VM处理中", "VM反馈结果");
        for (SomAmazonVcComparativePriceRecordHandleImportVo handleImportVo : handleImportVos) {
            if ("非内部渠道比价".equals(handleImportVo.getComparativeReasonStr())) {
                handleImportVo.setComparativeChannel("其他");
            }
            if (handleImportVo.getHandleStatusStr() != null && APPOINT_HANDLE_STATUS_LIST.contains(handleImportVo.getHandleStatusStr())) {
                McDictionaryInfo dictionaryInfo = dictionaryInfoMap.get(handleImportVo.getSite());
                if (dictionaryInfo == null || dictionaryInfo.getItemValue4() == null) {
                    errors.add(StrUtil.concat(true, "错误001：未配置VC Case处理专员 - " + handleImportVo.getSite()));
                    continue;
                }
                handleImportVo.setHandlePersonCode(dictionaryInfo.getItemValue4());
            } else {
                String key = handleImportVo.getSite() + handleImportVo.getSellerSku();
                McProductSales productSales = productSalesMap.get(key);
                if (productSales == null || productSales.getSalesGroupEmptCode() == null) {
                    errors.add(StrUtil.concat(true, "错误002：展示码异常 - " + handleImportVo.getSellerSku()));
                    continue;
                }
                handleImportVo.setHandlePersonCode(productSales.getSalesGroupEmptCode());
            }
        }
        return errors;
    }

    /**
     * 核验批量处理比价导入记录
     *
     * @param handleImportVos 行记录
     * @param tokenUser       当前登陆用户
     * @return 错误信息
     */
    private List<String> checkBatchHandleImportData(List<SomAmazonVcComparativePriceRecordHandleImportVo> handleImportVos,
                                                    TokenUserInfo tokenUser) {
        // 查询 状态 != 50（处理完成）的比价记录
        List<SomAmazonVcComparativePriceRecord> records = somAmazonVcComparativePriceRecordMapper.createLambdaQuery()
                .andNotEq("handle_status", 50)
                .select("aid", "site", "seller_sku", "handle_person_code", "comparative_reason", "comparative_channel", "competitive_price_threshold", "comparative_url");
        Map<String, SomAmazonVcComparativePriceRecord> recordsMap = records.stream().collect(Collectors.toMap(x -> x.getSite() + x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        // 查询字典
        List<McDictionaryInfo> mcDictionaryInfos = comparativePriceRecordHandleRecordService.queryComparativePriceRecordHandleDictionary();
        Map<String, Map<String, McDictionaryInfo>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity())));
        Map<String, McDictionaryInfo> comparativePriceHandleStatusMap = mcDictionaryInfoMap.get("VcComparativePriceHandleStatus");
        Map<String, McDictionaryInfo> comparativeReasonMap = mcDictionaryInfoMap.get("VcComparativeReason");
        Map<String, McDictionaryInfo> platformMap = mcDictionaryInfoMap.get("Platform");
        // 查询所有人
        List<SysUserNeweya> userList = sysUserNeweyaMapper.all();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SysUserNeweya::getEmployeeNumber, SysUserNeweya::getemNameCn, (x1, x2) -> x1));
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomAmazonVcComparativePriceRecordHandleImportVo handleImportVo : handleImportVos) {
            // 核验必填字段
            String site = handleImportVo.getSite();
            String sellerSku = handleImportVo.getSellerSku();
            String handleStatusStr = handleImportVo.getHandleStatusStr();
            String handlePersonCode = handleImportVo.getHandlePersonCode();
            String comparativeReasonStr = handleImportVo.getComparativeReasonStr();
            String key = StrUtil.concat(true, site, sellerSku);
            SomAmazonVcComparativePriceRecord record = recordsMap.get(key);
            if (!StrUtil.isAllNotBlank(site, sellerSku, handleStatusStr, handlePersonCode)) {
                errors.add("错误0：站点、展示码、处理状态、处理人工号不能为空！");
                continue;
            }
            if (!handleStatusStr.equals("处理完成") && StrUtil.isBlank(comparativeReasonStr)) {
                errors.add("错误003：非处理完成时，比价原因不能为空！");
                continue;
            }
            // 核验数据重复
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, "错误1：站点[", site, "]展示码[", sellerSku, "]数据重复！"));
                continue;
            }
            repeatCheckSet.add(key);
            // 核验处理状态，并填充数值
            String needCheck = "1";
            McDictionaryInfo statusDict = comparativePriceHandleStatusMap.get(handleStatusStr);
            if (statusDict == null || !needCheck.equals(statusDict.getItemValue1())) {
                errors.add(StrUtil.concat(true, "错误2：处理状态[", handleStatusStr, "]有误！"));
                continue;
            }
            handleImportVo.setHandleStatus(Integer.valueOf(statusDict.getItemValue()));
            // 核验比价原因，并填充数值，并判断是否需要填充比价平台、比价价格、比价链接
            McDictionaryInfo comparativeReasonDict = comparativeReasonMap.get(comparativeReasonStr);
            if (StrUtil.isNotBlank(comparativeReasonStr) && comparativeReasonDict == null) {
                errors.add(StrUtil.concat(true, "错误3：比价原因[", comparativeReasonStr, "]有误！"));
                continue;
            }
            if (StrUtil.isNotBlank(comparativeReasonStr)) {
                handleImportVo.setComparativeReason(Integer.valueOf(comparativeReasonDict.getItemValue()));
            }
            String comparativeChannel = handleImportVo.getComparativeChannel();
            String competitivePriceThresholdStr = handleImportVo.getCompetitivePriceThresholdStr();
            if (StrUtil.isNotBlank(comparativeReasonStr) && needCheck.equals(comparativeReasonDict.getItemValue1())) {
                if (!StrUtil.isAllNotBlank(comparativeChannel, competitivePriceThresholdStr, handleImportVo.getComparativeUrl())) {
                    errors.add(StrUtil.concat(true, "错误4：比价原因为[", comparativeReasonStr, "]时，比价平台、比价价格、比价链接不能为空！"));
                    continue;
                }
                // 核验比价平台，在处理比价时前端显示的是 item_label，实际传参用的是 item_value
                if (!comparativeChannel.equals("其他")) {
                    McDictionaryInfo platformDict = platformMap.get(comparativeChannel);
                    if (platformDict == null) {
                        errors.add(StrUtil.concat(true, "错误5：比价平台[", comparativeChannel, "]有误！"));
                        continue;
                    }
                    handleImportVo.setComparativeChannel(platformDict.getItemValue());
                }
                // 核验比价价格
                try {
                    handleImportVo.setCompetitivePriceThreshold(new BigDecimal(competitivePriceThresholdStr));
                } catch (Exception e) {
                    errors.add(StrUtil.concat(true, "错误6：比价价格[", competitivePriceThresholdStr, "]格式有误！"));
                    continue;
                }
            } else {
                // 如果不需要填写比价平台/比价价格/比价链接，则置空
                handleImportVo.setCompetitivePriceThreshold(null);
                handleImportVo.setComparativeUrl(null);
                handleImportVo.setComparativeChannel(null);
            }
            // 核验处理人是否存在
            String handlePersonName = userMap.get(handlePersonCode);
            if (StrUtil.isEmpty(handlePersonName)) {
                errors.add(StrUtil.concat(true, "错误7：处理人工号[", handlePersonCode, "]不存在！"));
                continue;
            }
            handleImportVo.setHandlePersonName(handlePersonName);
            // 核验比价记录
            if (record != null) {
                if (!tokenUser.getPowerList().contains("2005014006") && !tokenUser.getJobNumber().equals(record.getHandlePersonCode())) {
                    errors.add(StrUtil.concat(true, "错误8：站点[", site, "]展示码[", sellerSku, "]比价记录的处理人非当前操作人！"));
                    continue;
                }
                handleImportVo.setCid(record.getAid());
            }
            // 兼容处理完成 比价原因为空的情况
            if (StrUtil.isBlank(comparativeReasonStr)) {
                if (record != null) {
                    handleImportVo.setComparativeReason(record.getComparativeReason());
                    handleImportVo.setCompetitivePriceThreshold(record.getCompetitivePriceThreshold());
                    handleImportVo.setComparativeUrl(record.getComparativeUrl());
                    handleImportVo.setComparativeChannel(record.getComparativeChannel());
                }
            }
        }
        return errors;
    }

    /**
     * 处理比价基础参数核验
     *
     * @param comparativePriceRecordHandleVo 入参
     */
    private void checkComparativePriceRecordHandleParam(SomAmazonVcComparativePriceRecordHandleVo comparativePriceRecordHandleVo) throws ValidateException {
        if (ObjectUtil.isEmpty(comparativePriceRecordHandleVo)) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        // 核验参数
        Integer comparativeReason = comparativePriceRecordHandleVo.getComparativeReason();
        Integer handleStatus = comparativePriceRecordHandleVo.getHandleStatus();
        if (StrUtil.isEmpty(comparativePriceRecordHandleVo.getAid()) ||
                comparativeReason == null ||
                handleStatus == null) {
            throw new ValidateException("处理状态/处理人/比价原因不能为空！");
        }
        // 查询字典
        List<McDictionaryInfo> mcDictionaryInfos = comparativePriceRecordHandleRecordService.queryComparativePriceRecordHandleDictionary();
        Map<String, Map<String, McDictionaryInfo>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode,
                        Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity())));
        // 核验处理状态，如果字典中 itemValue1 为 1，处理状态正确
        String defaultCheckValue = "1";
        Map<String, McDictionaryInfo> handleStatusMap = mcDictionaryInfoMap.get("VcComparativePriceHandleStatus");
        McDictionaryInfo handleStatusDict = handleStatusMap.get(String.valueOf(handleStatus));
        if (handleStatusDict == null || !defaultCheckValue.equals(handleStatusDict.getItemValue1())) {
            throw new ValidateException("处理状态参数有误！");
        }
        // 核验比价原因
        Map<String, McDictionaryInfo> comparativeReasonMap = mcDictionaryInfoMap.get("VcComparativeReason");
        McDictionaryInfo comparativeReasonDict = comparativeReasonMap.get(String.valueOf(comparativeReason));
        if (comparativeReasonDict == null) {
            throw new ValidateException("比价原因参数有误！");
        }
        // 如果比价原因 itemValue1 为 1，则证明需要填写比价平台/比价价格/比价链接
        if (defaultCheckValue.equals(comparativeReasonDict.getItemValue1())) {
            if (StrUtil.isEmpty(comparativePriceRecordHandleVo.getComparativeChannel()) ||
                    StrUtil.isEmpty(comparativePriceRecordHandleVo.getComparativeUrl()) ||
                    comparativePriceRecordHandleVo.getCompetitivePriceThreshold() == null) {
                throw new ValidateException(StrUtil.concat(true, "比价原因为", comparativeReasonDict.getItemLable(), "时，比价平台/比价价格/比价链接不能为空！"));
            }
        } else {
            // 手动设置比价平台，比价价格，比价链接为空
            comparativePriceRecordHandleVo.setComparativeChannel(null);
            comparativePriceRecordHandleVo.setCompetitivePriceThreshold(null);
            comparativePriceRecordHandleVo.setComparativeUrl(null);
        }
    }
}
