package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/*
 * FBA产品黑名单表的VO分页查询实体
 * gen by 代码生成器 2024-07-04
 */

@Data
@Schema(title = "FBA产品黑名单表分页查询实体", name = "SomTemuFbaBlackListPageSearchVo")
public class SomTemuFbaBlackListPageSearchVo extends PageSearchVo {
    /**
     * 平台
     */
    @Schema(description = "平台", name = "platform")
    private String platform;
    /**
     * 站点
     */
    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "展示码列表", name = "sellerSkuList")
    private List<String> sellerSkuList;

    @Schema(description = "店铺ID列表", name="accountIds")
    private List<String> accountIds;

}
