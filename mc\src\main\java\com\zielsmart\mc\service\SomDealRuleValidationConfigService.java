package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomDealRuleValidationConfig;
import com.zielsmart.mc.repository.mapper.SomDealRuleValidationConfigMapper;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomDealRuleValidationConfigService
 * @description
 * @date 2024-01-16 11:41:17
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomDealRuleValidationConfigService {

    @Resource
    private SomDealRuleValidationConfigMapper somDealRuleValidationConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomDealRuleValidationConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomDealRuleValidationConfigVo> queryByPage(SomDealRuleValidationConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomDealRuleValidationConfigVo> pageResult = dynamicSqlManager.getMapper(SomDealRuleValidationConfigMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomDealRuleValidationConfigVo.class, searchVo);
    }

    /**
     * 新增
     *
     * @param somDealRuleValidationConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(SomDealRuleValidationConfigVo somDealRuleValidationConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleValidationConfigVo) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getSite()) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somDealRuleValidationConfigMapper.createLambdaQuery().andEq("site", somDealRuleValidationConfigVo.getSite())
                .andEq("type", somDealRuleValidationConfigVo.getType()).count();
        if (count > 0) {
            throw new ValidateException("当前站点该活动类型已配置");
        }
        somDealRuleValidationConfigVo.setAid(IdUtil.fastSimpleUUID());
        somDealRuleValidationConfigVo.setPlatform("Amazon");
        somDealRuleValidationConfigVo.setCreateNum(tokenUser.getJobNumber());
        somDealRuleValidationConfigVo.setCreateName(tokenUser.getUserName());
        somDealRuleValidationConfigVo.setCreateTime(DateTime.now().toJdkDate());
        somDealRuleValidationConfigVo.setRules(JSONUtil.toJsonStr(somDealRuleValidationConfigVo.getRules()));
        somDealRuleValidationConfigMapper.insert(ConvertUtils.beanConvert(somDealRuleValidationConfigVo, SomDealRuleValidationConfig.class));
    }

    /**
     * 修改
     *
     * @param somDealRuleValidationConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomDealRuleValidationConfigVo somDealRuleValidationConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleValidationConfigVo) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getAid()) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getSite()) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somDealRuleValidationConfigMapper.createLambdaQuery().andEq("site", somDealRuleValidationConfigVo.getSite())
                .andEq("type", somDealRuleValidationConfigVo.getType()).andNotEq("aid", somDealRuleValidationConfigVo.getAid()).count();
        if (count > 0) {
            throw new ValidateException("当前站点该活动类型已配置");
        }
        SomDealRuleValidationConfig config = somDealRuleValidationConfigMapper.createLambdaQuery().andEq("aid", somDealRuleValidationConfigVo.getAid()).single();
        if (ObjectUtil.isNotEmpty(config)) {
            config.setSite(somDealRuleValidationConfigVo.getSite());
            config.setType(somDealRuleValidationConfigVo.getType());
            config.setRules(JSONUtil.toJsonStr(somDealRuleValidationConfigVo.getRules()));
            config.setModifyNum(tokenUser.getJobNumber());
            config.setModifyName(tokenUser.getUserName());
            config.setModifyTime(DateTime.now().toJdkDate());
            somDealRuleValidationConfigMapper.updateById(config);
        }
    }

    /**
     * 删除
     *
     * @param somDealRuleValidationConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(SomDealRuleValidationConfigVo somDealRuleValidationConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleValidationConfigVo) || StrUtil.isBlank(somDealRuleValidationConfigVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somDealRuleValidationConfigMapper.createLambdaQuery().andEq("aid", somDealRuleValidationConfigVo.getAid()).delete();
    }

    /**
     * 查看
     *
     * @param somDealRuleValidationConfigVo
     * @return {@link com.zielsmart.mc.vo.SomDealRuleValidationConfigVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomDealRuleValidationConfigVo queryByAid(SomDealRuleValidationConfigVo somDealRuleValidationConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleValidationConfigVo) || StrUtil.isEmpty(somDealRuleValidationConfigVo.getAid())) {
            throw new ValidateException("请选择要查看的数据");
        }
        SomDealRuleValidationConfig config = somDealRuleValidationConfigMapper.createLambdaQuery().andEq("aid", somDealRuleValidationConfigVo.getAid()).single();
        SomDealRuleValidationConfigVo configVo = ConvertUtils.beanConvert(config, SomDealRuleValidationConfigVo.class);
        return configVo;
    }

}
