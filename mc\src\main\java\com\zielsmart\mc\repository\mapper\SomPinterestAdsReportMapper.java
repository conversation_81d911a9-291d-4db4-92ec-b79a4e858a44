package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPinterestAdsReport;
import com.zielsmart.mc.vo.SomPinterestAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomPinterestAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-06-09
*/

@SqlResource("somPinterestAdsReport")
public interface SomPinterestAdsReportMapper extends BaseMapper<SomPinterestAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPinterestAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPinterestAdsReportVo> queryByPage(@Param("searchVo")SomPinterestAdsReportPageSearchVo searchVo, PageRequest pageRequest);
}
