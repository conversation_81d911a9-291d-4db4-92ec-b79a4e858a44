package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomTemuCrossPromotion;
import com.zielsmart.mc.repository.entity.SomTemuCrossSku;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomTemuCrossPromotionMapper;
import com.zielsmart.mc.repository.mapper.SomTemuCrossSkuMapper;
import com.zielsmart.mc.vo.SomTemuCrossPromotionDetailVo;
import com.zielsmart.mc.vo.SomTemuCrossPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossPromotionVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-03-12 09:10:57
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuCrossPromotionService {

    @Resource
    private SomTemuCrossPromotionMapper somTemuCrossPromotionMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private SomTemuCrossSkuMapper somTemuCrossSkuMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo< SomTemuCrossPromotionVo >}
     */
    public PageVo<SomTemuCrossPromotionVo> queryByPage(SomTemuCrossPromotionPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuCrossPromotionVo> pageResult = somTemuCrossPromotionMapper.queryByPage(searchVo, pageRequest);
        List<SomTemuCrossPromotionVo> detailVos = pageResult.getList();
        if (CollUtil.isNotEmpty(detailVos)) {
            List<McDictionaryInfo> dictionaryInfos = queryTemuCrossPromotionDictionary();
            Map<String, List<McDictionaryInfo>> dictMap = dictionaryInfos.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode));
            Map<String, String> activityTypeMap = dictMap.get("TemuCrossPromotionActivityType").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> activityStatusMap = dictMap.get("TemuPromotionActivityStatus").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> enrollStatusMap = dictMap.get("TemuCrossPromotionEnrollStatus").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> temuAccountMap = dictMap.get("TemuAccount").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
            detailVos.forEach(data -> {
                data.setActivityStatusDesc(activityStatusMap.get(data.getActivityStatus() + ""));
                data.setActivityTypeDesc(activityTypeMap.get(data.getActivityType() + ""));
                data.setEnrollStatusDesc(enrollStatusMap.get(data.getEnrollStatus() + ""));
                data.setAccountName(temuAccountMap.get(data.getAccountId() + ""));
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuCrossPromotionVo.class, searchVo);
    }

    /**
     * detail
     * 查询产品明细
     *
     * @param somTemuCrossPromotionVo 入参
     * @return {@link java.util.List<SomTemuCrossPromotionDetailVo>}
     */
    public List<SomTemuCrossPromotionDetailVo> detail(SomTemuCrossPromotionVo somTemuCrossPromotionVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuCrossPromotionVo) || StrUtil.isBlank(somTemuCrossPromotionVo.getAid())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        SomTemuCrossPromotion detail = somTemuCrossPromotionMapper.createLambdaQuery().andEq("aid", somTemuCrossPromotionVo.getAid()).single();
        if (detail == null) {
            return Collections.emptyList();
        }
        Object skcItemsObj = detail.getSkcItems();
        if (skcItemsObj == null) {
            return Collections.emptyList();
        }
        List<SomTemuCrossPromotionDetailVo> itemVos = parseSkcItems(skcItemsObj.toString());
        if (CollUtil.isEmpty(itemVos)) {
            return Collections.emptyList();
        }
        // 查询展示码
        List<String> skuIds = itemVos.stream().map(SomTemuCrossPromotionDetailVo::getSkuId).collect(Collectors.toList());
        List<String> sites = itemVos.stream().map(SomTemuCrossPromotionDetailVo::getSite).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(skuIds)) {
            List<SomTemuCrossSku> listings = somTemuCrossSkuMapper.createLambdaQuery().andEq("account_id", detail.getAccountId()).andIn("site", sites).andIn("product_sku_id", skuIds).select();
            Map<String, String> sellerSkuMap = listings.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getSite() + x.getProductSkuId(), SomTemuCrossSku::getSellerSku, (x1, x2) -> x1));
            itemVos.forEach(data -> data.setSellerSku(sellerSkuMap.get(detail.getAccountId() + data.getSite() + data.getSkuId())));
        }
        return itemVos;
    }

    /**
     * 解析 skcItems
     *
     * @param skcItemsStr skcItems Str
     * @return List<SomTemuCrossPromotionDetailVo>
     */
    private List<SomTemuCrossPromotionDetailVo> parseSkcItems(String skcItemsStr) {
        JSONArray skcItems = JSONUtil.parseArray(skcItemsStr);
        if (skcItems.isEmpty()) {
            return Collections.emptyList();
        }
        List<SomTemuCrossPromotionDetailVo> itemVos = new ArrayList<>();
        for (int i = 0; i < skcItems.size(); i++) {
            JSONObject itemObj = skcItems.getJSONObject(i);
            String skcId = itemObj.getStr("skcId");
            JSONArray skuList = JSONUtil.parseArray(itemObj.getStr("skuList"));
            if (skuList.isEmpty()) {
                continue;
            }
            for (int j = 0; j < skuList.size(); j++) {
                JSONObject skuObj = skuList.getJSONObject(j);
                String skuId = skuObj.getStr("skuId");
                JSONArray sitePriceList = JSONUtil.parseArray(skuObj.get("sitePriceList"));
                if (sitePriceList.isEmpty()) {
                    continue;
                }
                for (int k = 0; k < sitePriceList.size(); k++) {
                    JSONObject sitePrice = sitePriceList.getJSONObject(k);
                    SomTemuCrossPromotionDetailVo itemVo = new SomTemuCrossPromotionDetailVo();
                    itemVo.setSkuId(skuId);
                    itemVo.setSkcId(skcId);
                    itemVo.setSite(sitePrice.getStr("site"));
                    itemVo.setDailyPrice(sitePrice.getBigDecimal("dailyPrice"));
                    itemVo.setActivityPrice(sitePrice.getBigDecimal("activityPrice"));
                    itemVos.add(itemVo);
                }
            }
        }
        return itemVos;
    }

    /**
     * 查询Temu营销活动(Cross)相关字典集合
     * TemuCrossPromotionActivityType：活动类型
     * TemuPromotionActivityStatus：活动状态
     * TemuAccount：店铺
     * TemuCrossPromotionEnrollStatus：Temu跨境（香港半托）营销活动报名状态
     *
     * @return 字典
     */
    private List<McDictionaryInfo> queryTemuCrossPromotionDictionary() {
        List<String> list = Arrays.asList("TemuCrossPromotionActivityType", "TemuPromotionActivityStatus", "TemuAccount", "TemuCrossPromotionEnrollStatus");
        return mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();
    }
}
