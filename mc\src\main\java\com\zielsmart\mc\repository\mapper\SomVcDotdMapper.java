package com.zielsmart.mc.repository.mapper;
import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.vo.SomVcDotdPageSearchVo;
import com.zielsmart.mc.vo.SomVcDotdVo;
import com.zielsmart.mc.vo.SomVcPromotionCheckUniqueVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.mapper
 * @title SomVcDotdMapper
 * @description Vc DOTO表管理
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somVcDotd")
public interface SomVcDotdMapper extends BaseMapper<SomVcDotd> {

    /**
     * 分页查询
     *
     * @param searchVo    查询参数
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomVcDotdVo>}
     */
    PageResult<SomVcDotdVo> queryByPage(@Param("searchVo") SomVcDotdPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 补充大促起止日期
     *
     * @param somVcDotdVo 入参
     */
    @Update
    void supplyPromotionDate(SomVcDotdVo somVcDotdVo);


    /**
     * 批量导入反馈修改结果
     *
     * @param vcDotdList 需要更新的数据
     */
    default void batchUpdateSubmitResult(@Param("vcDotdList") List<SomVcDotd> vcDotdList) {
        if (CollUtil.isEmpty(vcDotdList)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somVcDotd.batchUpdateSubmitResult"), vcDotdList);
    }

    /**
     * 核验活动唯一性
     *
     * @param checkUniqueVo 入参
     * @return List<Integer>
     */
    List<Integer> checkUnique(@Param("checkUniqueVo") SomVcPromotionCheckUniqueVo checkUniqueVo);
}
