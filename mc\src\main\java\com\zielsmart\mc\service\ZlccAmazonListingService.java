package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.McConstants;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.util.AmazonJsonSchemaUtils;
import com.zielsmart.mc.vo.AmazonListingResult;
import com.zielsmart.mc.vo.ZlccAmazonListingPageSearchVo;
import com.zielsmart.mc.vo.ZlccAmazonListingVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.util.WebUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class ZlccAmazonListingService {
    @Resource
    private SysUserNeweyaMapper sysUserNeweyaMapper;

    @Resource
    private ZlccAmazonListingMapper zlccAmazonListingMapper;

    @Resource
    private ZlccAmazonListingHistoryMapper historyMapper;
    @Resource
    private IMagicService iMagicService;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDictionaryInfoMapper dictMapper;
    @Resource
    private ObjectMapper objectMapper;

    @Value("${magic.head.token}")
    private String token;

    @Resource
    private AmazonJsonSchemaUtils amazonJsonSchemaUtils;


    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.ZlccAmazonListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<ZlccAmazonListingVo> queryByPage(ZlccAmazonListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<ZlccAmazonListingVo> pageResult = dynamicSqlManager.getMapper(ZlccAmazonListingMapper.class).queryByPage(searchVo, pageRequest);
        Map<String, String> userMap = sysUserNeweyaMapper.all().stream().collect(Collectors.toMap(
                SysUserNeweya::getEmployeeNumber,
                SysUserNeweya::getemNameCn,
                (v1, v2) -> v1
        ));
        for (ZlccAmazonListingVo listing : pageResult.getList()) {
            listing.setSalesGroupEmptName(userMap.get(listing.getSalesGroupEmptCode()));
            listing.setOperationEmptName(userMap.get(listing.getOperationEmptCode()));
            listing.setStatusList(JSONUtil.toList(JSONUtil.parseArray(listing.getStatus()), String.class));
        }
        return ConvertUtils.pageConvert(pageResult, ZlccAmazonListingVo.class, searchVo);
    }


    /**
     * save
     * 添加
     *
     * @param zlccAmazonListingVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException, SQLException {
        if (ObjectUtil.isEmpty(zlccAmazonListingVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        ZlccAmazonListing single = null;
        if (StrUtil.isNotBlank(zlccAmazonListingVo.getAid())) {
            single = zlccAmazonListingMapper.single(zlccAmazonListingVo.getAid());
        }

        List<McDictionaryInfo> marketList = dictMapper.createLambdaQuery().andEq("item_type_code", "MarketplaceId").select();
        Map<String, String> marketMap = marketList.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue(), (x1, x2) -> x1));
        String marketPlaceId = marketMap.get(zlccAmazonListingVo.getSite());
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String attributes = JSONUtil.toJsonStr(zlccAmazonListingVo.getVerifyJson());
        amazonJsonSchemaUtils.writeToDisk("前端上传json.json", zlccAmazonListingVo.getVerifyJson());

        JsonNode jsonNode = objectMapper.readTree(attributes);
        amazonJsonSchemaUtils.filterNull(jsonNode);
        amazonJsonSchemaUtils.writeToDisk("后端上传json.json", jsonNode);
        attributes = objectMapper.writeValueAsString(jsonNode);

        //region 测试自定义json
//        FileReader fileReader = null;
//        try {
//            fileReader = new FileReader("F:\\sublim\\待校验测试json.json");
//            BufferedReader bufferedReader = new BufferedReader(fileReader);
//            StringBuilder sb = new StringBuilder();
//            String line;
//            while ((line = bufferedReader.readLine()) != null) {
//                sb.append(line);
//            }
//            bufferedReader.close();
//            fileReader.close();
////            attributes = sb.toString();
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
        //endregion

        String result = null;
        try {
            result = amazonJsonSchemaUtils.jsonValidate(attributes, marketPlaceId, zlccAmazonListingVo.getCategory());
        } catch (Exception e) {
            throw new RuntimeException("校验异常:" + e);
        }
        if (!result.isEmpty()) {
            amazonJsonSchemaUtils.writeToDisk("检验JSON结果", result);
            throw new ValidateException("JSON Schema校验不通过：\r\n" + result);
        }

        if (StrUtil.isEmpty(zlccAmazonListingVo.getAid())) {
            ZlccAmazonListing insert = new ZlccAmazonListing();
            insert.setAid(IdUtil.fastSimpleUUID());
            insert.setCreateTime(new Date());
            insert.setCreateNum(tokenUser.getJobNumber());
            insert.setCreateName(tokenUser.getUserName());
            insert.setDataFrom(2);
            insert.setDeleteStatus(10);
            insert.setPublishStatus(1);
            insert.setSellerSku(zlccAmazonListingVo.getSellerSku());
            insert.setSite(zlccAmazonListingVo.getSite());
            insert.setMarketplaceId(marketPlaceId);
            insert.setProductType(zlccAmazonListingVo.getCategory());
            PGobject attributesPgObj = new PGobject();
            attributesPgObj.setType("json");
            attributesPgObj.setValue(attributes);
            insert.setAttributes(attributesPgObj);

            zlccAmazonListingMapper.insert(insert);
        } else {
            single.setPublishStatus(1);
            single.setSellerSku(zlccAmazonListingVo.getSellerSku());
            single.setProductType(zlccAmazonListingVo.getCategory());
            single.setLastModifyName(tokenUser.getUserName());
            single.setLastModifyNum(tokenUser.getJobNumber());
            single.setLastModifyTime(new Date());
            PGobject attributesPgObj = new PGobject();
            attributesPgObj.setType("json");
            attributesPgObj.setValue(attributes);
            single.setAttributes(attributesPgObj);
            single.setSellerSku(zlccAmazonListingVo.getSellerSku());
            zlccAmazonListingMapper.updateById(single);
        }

        if (zlccAmazonListingVo.isPublish()) {
            publish(zlccAmazonListingVo, tokenUser);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param zlccAmazonListingVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(zlccAmazonListingVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        ZlccAmazonListing single = zlccAmazonListingMapper.single(zlccAmazonListingVo.getAid());
        single.setDeleteStatus(99);
        single.setDeleteNum(tokenUser.getJobNumber());
        zlccAmazonListingMapper.updateById(single);
    }

    public String export(ZlccAmazonListingPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<ZlccAmazonListingVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "商品中心亚马逊Listing管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, ZlccAmazonListingVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public void checkProductId(Map jsonMap) throws ValidateException {
        if (jsonMap == null || jsonMap.size() < 2) {
            throw new ValidateException("数据不能为空");
        }
        String tpye = (String) jsonMap.get("type");
        String value = (String) jsonMap.get("value");
        if ("EAN".equals(tpye)) {
            long count = dynamicSqlManager.getMapper(SomEanInfoMapper.class).createLambdaQuery().andEq("ean", value).count();
            if (count == 0) {
                throw new ValidateException("EAN在系统中不存在");
            }
        } else if ("ASIN".equals(tpye)) {
            long count = zlccAmazonListingMapper.findAsinCount(value);
            if (count == 0) {
                throw new ValidateException("ASIN在系统中不存在");
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void publish(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws JsonProcessingException, ValidateException {
        ZlccAmazonListing amazonById = zlccAmazonListingMapper.single(zlccAmazonListingVo.getAid());
        if (amazonById == null) {
            throw new RuntimeException("没有找到数据");
        }
        //查看是否存在正在推送中的数据
        long publishing = historyMapper.createLambdaQuery().andEq(ZlccAmazonListingHistory::getMtAid, amazonById.getAid()).andEq(ZlccAmazonListingHistory::getPublishStatus, 1).count();
        if (publishing > 0) {
            throw new ValidateException("该商品正在推送中，请勿重复操作");
        }

        McDictionaryInfoMapper dictMapper = WebUtils.getApplicationContext().getBean(McDictionaryInfoMapper.class);
        List<McDictionaryInfo> marketList = dictMapper.createLambdaQuery().andEq("item_type_code", "MarketplaceId").select();
        Map<String, String> marketMap = marketList.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue(), (x1, x2) -> x1));
        String marketPlaceId = marketMap.get(amazonById.getSite());

        Map body = new HashMap();
        body.put("sku", amazonById.getSellerSku());
        body.put("productType", amazonById.getProductType());
        body.put("marketPlaceId", marketPlaceId);
        PGobject attributes = amazonById.getAttributes();
        JsonNode jsonNode = objectMapper.readTree(attributes.getValue());
        amazonJsonSchemaUtils.filterNull(jsonNode);
        body.put("body", jsonNode);
        log.info("发布listing请求体：{}", objectMapper.writeValueAsString(body));
        ResultVo<AmazonListingResult> resultVo = null;
        try {
            resultVo = iMagicService.putListingsItem(token, body);
        } catch (Exception e) {
            log.error("推送listing发布异常：{}", e.toString());
            throw new RuntimeException("推送listing发布异常：" + e.getMessage());
        }
        log.info("发布结果:{}", JSONUtil.toJsonStr(resultVo));
        if (!resultVo.isSuccess()) {
            log.error("推送listing返回失败：{}", resultVo.getMessage());
            throw new RuntimeException("推送listing发布失败：" + resultVo.getMessage());
        }

        AmazonListingResult data = resultVo.getData();
        String status = data.getStatus();
        if (McConstants.AMAZON_LISTING_STATUS_INVALID.equals(status)) {
            //无效 展示错误提示
            List<AmazonListingResult.Issue> issues = data.getIssues();
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < issues.size(); i++) {
                sb.append((i + 1) + ". severity: " + issues.get(i).getSeverity() + ", code: " + issues.get(i).getCode() + ", message: " + issues.get(i).getMessage() + "\n");
            }
            throw new ValidateException(sb.toString());
        } else if (McConstants.AMAZON_LISTING_STATUS_ACCEPTED.equals(status)) {
            //已经推送成功，在数据没有更改时不能再次进行推送
            amazonById.setPublishStatus(0);
            ZlccAmazonListingHistory history = ConvertUtils.beanConvert(amazonById, ZlccAmazonListingHistory.class);
            history.setAid(IdUtil.fastSimpleUUID());
            //推送中
            history.setPublishStatus(1);
            history.setPublishTime(new Date());
            //需要更新数据
            history.setAttributes(amazonById.getAttributes());
            history.setMtAid(amazonById.getAid());
            history.setCreateDate(new Date());
            //插入之前查询数据的数量，最多保留10条历史记录
            List<ZlccAmazonListingHistory> historyList = historyMapper.createLambdaQuery().andEq(ZlccAmazonListingHistory::getMtAid, amazonById.getAid()).asc(ZlccAmazonListingHistory::getCreateDate).select();
            if (!historyList.isEmpty() && historyList.size() == 10) {
                historyMapper.deleteById(historyList.get(0).getAid());
            }
            //插入发布记录
            historyMapper.insert(history);
            zlccAmazonListingMapper.updateById(amazonById);
        } else {
            log.info("发布失败，原因：" + data.getIssues().get(0).getMessage());
            throw new ValidateException("发布失败，状态为" + status);
        }

    }

    public Map uiJson(ZlccAmazonListingVo vo) throws JsonProcessingException, SQLException, ValidateException {
        Map<String, Object> result = new HashMap<>();
        List<McDictionaryInfo> marketList = dictMapper.createLambdaQuery().andEq("item_type_code", "MarketplaceId").select();
        Map<String, String> marketMap = marketList.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue(), (x1, x2) -> x1));
        String marketPlaceId = marketMap.get(vo.getSite().trim());
        String uiJson = amazonJsonSchemaUtils.parseJsonSchema(marketPlaceId, vo.getCategory().trim());
        amazonJsonSchemaUtils.writeToDisk(vo.getCategory() + "_前端ui.json", objectMapper.readValue(uiJson, Map.class));
        result.put("uiJson", objectMapper.readValue(uiJson, Map.class));
        result.put("marketPlaceId", marketPlaceId);
        result.put("languageTag", "en_US");
        result.put("category", vo.getCategory());
        if (vo.getAid() != null) {
            ZlccAmazonListing listing = zlccAmazonListingMapper.createLambdaQuery().andEq("aid", vo.getAid()).single();
            //拿到attributes 转成 front_json 并入库
            String value = listing.getAttributes().getValue();
            Map body = new HashMap();
            body.put("sellerSku", listing.getSellerSku());
            body.put("attributes", value);
            ResultVo<String> frontJson1 = iMagicService.getFrontJson(token, body);
            if (!frontJson1.isSuccess()) {
                throw new ValidateException("获取frontJson失败：" + frontJson1.getMessage());
            }
            PGobject frontJson = new PGobject();
            frontJson.setType("json");
            frontJson.setValue(frontJson1.getData());
            listing.setFrontJson(frontJson);
            zlccAmazonListingMapper.updateById(listing);
            result.put("frontJson", frontJson1.getData());
        } else {
            result.put("frontJson", null);
        }
        return result;
    }

    public ZlccAmazonListingVo record(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (zlccAmazonListingVo == null || zlccAmazonListingVo.getAid() == null) {
            throw new ValidateException("请选择要查看的数据");
        }
        ZlccAmazonListing single = zlccAmazonListingMapper.single(zlccAmazonListingVo.getAid());
        if (single == null) {
            throw new ValidateException("没有找到数据");
        }
        List<ZlccAmazonListingHistoryVo> historyList = ConvertUtils.listConvert(historyMapper.createLambdaQuery()
                        .andEq(ZlccAmazonListingHistory::getMtAid, zlccAmazonListingVo.getAid())
                        .desc(ZlccAmazonListingHistory::getCreateDate)
                        .select("aid", "publish_status", "publish_time", "last_modify_name", "last_modify_time", "mt_aid")
                , ZlccAmazonListingHistoryVo.class);
        for (ZlccAmazonListingHistoryVo history : historyList) {
            if (1 == history.getPublishStatus()) {
                history.setPublishStatusStr("发布中");
            } else if (20 == history.getPublishStatus()) {
                history.setPublishStatusStr("发布成功");
            } else {
                history.setPublishStatusStr("发布失败");
            }
        }
        ZlccAmazonListingVo result = new ZlccAmazonListingVo();
        result.setSite(single.getSite());
        result.setSellerSku(single.getSellerSku());
        result.setHistoryList(historyList);
        try {
            String asin = JSONUtil.parseArray(single.getSummaries()).getJSONObject(0).getStr("asin");
            result.setAsin(asin);
            //fulfillment_availability::jsonb->0->>'fulfillmentChannelCode' = 'DEFAULT' then 'Merchant' else 'Amazon' end as fulfillment
            String fulfillment = JSONUtil.parseArray(single.getFulfillmentAvailability()).getJSONObject(0).getStr("fulfillmentChannelCode");
            if (fulfillment != null) {
                if ("DEFAULT".equals(fulfillment)) {
                    result.setFulfillment("Merchant");
                } else {
                    result.setFulfillment("Amazon");
                }
            }
        } catch (Exception e) {
            log.error("sellerSku:{}获取ASIN失败：{}", single.getSellerSku(), e.getMessage());
        }
        return result;
    }

    public void recover(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (zlccAmazonListingVo == null || zlccAmazonListingVo.getAid() == null) {
            throw new ValidateException("请选择要恢复的数据");
        }
        ZlccAmazonListingHistory single = historyMapper.single(zlccAmazonListingVo.getAid());
        if (single == null) {
            throw new ValidateException("没有找到数据");
        }
        ZlccAmazonListing zlccAmazonListing = ConvertUtils.beanConvert(single, ZlccAmazonListing.class);
        zlccAmazonListing.setAid(single.getMtAid());
        zlccAmazonListing.setRecoverAid(single.getAid());
        zlccAmazonListingMapper.updateById(zlccAmazonListing);
    }

    public void item(ZlccAmazonListingVo zlccAmazonListingVo, TokenUserInfo tokenUser) throws ValidateException, SQLException {
        if (zlccAmazonListingVo == null || zlccAmazonListingVo.getAid() == null) {
            throw new ValidateException("请选择要获取Item的数据");
        }
        ZlccAmazonListing single = zlccAmazonListingMapper.single(zlccAmazonListingVo.getAid());

        McDictionaryInfoMapper dictMapper = WebUtils.getApplicationContext().getBean(McDictionaryInfoMapper.class);
        List<McDictionaryInfo> marketList = dictMapper.createLambdaQuery().andEq("item_type_code", "MarketplaceId").select();
        Map<String, String> marketMap = marketList.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue(), (x1, x2) -> x1));
        String marketPlaceId = marketMap.get(single.getSite().trim());

        Map body = new HashMap();
        body.put("sku", single.getSellerSku());
        body.put("marketPlaceId", marketPlaceId);
        ResultVo<JsonNode> listingsItem = iMagicService.getListingsItem(token, body);
        if (!listingsItem.isSuccess()) {
            log.error("获取Item失败：{}", listingsItem.getMessage());
            throw new ValidateException("获取Item失败：" + listingsItem.getMessage());
        }
        JsonNode data = listingsItem.getData();
        PGobject summaries = null;
        if (data.get("summaries").isEmpty()) {
            summaries = new PGobject();
            summaries.setType("json");
            summaries.setValue(data.get("summaries").toString());
        }
        PGobject attributes = null;
        if (data.get("attributes").isEmpty()) {
            attributes = new PGobject();
            attributes.setType("json");
            attributes.setValue(data.get("attributes").toString());
        }
        PGobject fulfillmentAvailability = null;
        if (data.get("fulfillmentAvailability").isEmpty()) {
            fulfillmentAvailability = new PGobject();
            fulfillmentAvailability.setType("json");
            fulfillmentAvailability.setValue(data.get("fulfillmentAvailability").toString());
        }
        PGobject offers = null;
        if (data.get("offers").isEmpty()) {
            offers = new PGobject();
            offers.setType("json");
            offers.setValue(data.get("offers").toString());
        }
        single.setSummaries(summaries);
        single.setAttributes(attributes);
        single.setFulfillmentAvailability(fulfillmentAvailability);
        single.setOffers(offers);
        single.setLastModifyTime(new Date());
        single.setLastModifyName(tokenUser.getUserName());
        single.setLastModifyNum(tokenUser.getJobNumber());
        zlccAmazonListingMapper.updateById(single);
    }
}
