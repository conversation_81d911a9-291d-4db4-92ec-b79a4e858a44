package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuSupplierPricePageSearchVo;
import com.zielsmart.mc.vo.SomTemuSupplierPriceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-09-05
*/

@SqlResource("somTemuSupplierPrice")
public interface SomTemuSupplierPriceMapper extends BaseMapper<SomTemuSupplierPrice> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuSupplierPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuSupplierPriceVo> queryByPage(@Param("searchVo")SomTemuSupplierPricePageSearchVo searchVo, PageRequest pageRequest);
}
