package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomPricingHealthRecordService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPricingHealthRecordController
 * @description
 * @date 2024-08-12 09:39:21
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somPricingHealthRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "丢购记录表管理")
public class SomPricingHealthRecordController extends BasicController {

    @Resource
    SomPricingHealthRecordService somPricingHealthRecordService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPricingHealthRecordVo>> queryByPage(@RequestBody SomPricingHealthRecordPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somPricingHealthRecordService.queryByPage(searchVo));
    }

    @Operation(summary = "处理丢购")
    @PostMapping(value = "/handleLost")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> handleLost(@RequestBody SomPricingHealthRecordVo somPricingHealthRecordVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPricingHealthRecordService.handleLost(somPricingHealthRecordVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomPricingHealthRecordPageSearchVo searchVo) {
        String data = somPricingHealthRecordService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "批量处理")
    @PostMapping(value = "/batchHandle")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchHandle(@RequestBody SomPricingHealthRecordVo somPricingHealthRecordVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        return ResultVo.ofSuccess(somPricingHealthRecordService.batchHandle(somPricingHealthRecordVo, tokenUser));
    }

    @Operation(summary = "下载批量处理模板")
    @GetMapping(value = "/downloadBatchHandleTemplate")
    public String downloadBatchHandleTemplate() { return "forward:/static/excel/LoseBoxBulkTemplate.xlsx"; }

    @Operation(summary = "导入文件批量处理")
    @PostMapping(value = "/importBatchHandle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public ResultVo<String> importBatchHandle(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "处理状态", "丢购原因", "比价平台", "备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPricingHealthRecordImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPricingHealthRecordImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result == null || CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        String str = somPricingHealthRecordService.importBatchHandle(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }
}
