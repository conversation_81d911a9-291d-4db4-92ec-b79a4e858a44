package com.zielsmart.mc.repository.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Kaufland Listing信息表
 * gen by 代码生成器 2023-01-11
 */

@Table(name = "mc.som_kaufland_listing")
public class SomKauflandListing implements java.io.Serializable {
    /**
     * 主键id
     */
    @AssignID
    private String aid;
    /**
     * 状态
     */
    @Column("status")
    private String status;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 最短运输天数
     */
    @Column("transport_time_min")
    private Integer transportTimeMin;
    /**
     * 最长运输天数
     */
    @Column("transport_time_max")
    private Integer transportTimeMax;
    /**
     * 单位ID
     */
    @Column("id_unit")
    private String idUnit;
    /**
     * 注意事项
     */
    @Column("note")
    private String note;
    /**
     * 条件
     */
    @Column("condition")
    private String condition;
    /**
     * 建议零售价
     */
    @Column("listing_price")
    private Integer listingPrice;
    /**
     * 最低价
     */
    @Column("minimum_price")
    private Integer minimumPrice;
    /**
     * 售价
     */
    @Column("price")
    private Integer price;
    /**
     * 报价的ID
     */
    @Column("id_offer")
    private String idOffer;
    /**
     * 产品ID
     */
    @Column("id_product")
    private String idProduct;
    /**
     * 运费模板ID
     */
    @Column("id_shipping_group")
    private String idShippingGroup;
    /**
     * 仓库ID
     */
    @Column("id_warehouse")
    private String idWarehouse;
    /**
     * 库存
     */
    @Column("amount")
    private Integer amount;
    /**
     * 产品的创建时间
     */
    @Column("date_inserted_iso")
    private Date dateInsertedIso;
    /**
     * 最后修改时间
     */
    @Column("date_lastchange_iso")
    private Date dateLastchangeIso;
    /**
     * 备货时间
     */
    @Column("handling_time")
    private Integer handlingTime;
    /**
     * 商店语言
     */
    @Column("storefront")
    private String storefront;
    /**
     * 物流天数
     */
    @Column("shipping_rate")
    private Integer shippingRate;
    /**
     * 发货方式
     */
    @Column("fulfillment_type")
    private String fulfillmentType;
    /**
     * ean
     */
    @Column("ean")
    private String ean;
    /**
     * 等级
     */
    @Column("age_rating")
    private String ageRating;
    /**
     * 产品分类id
     */
    @Column("id_category")
    private String idCategory;
    /**
     * 主图url
     */
    @Column("main_picture")
    private String mainPicture;
    /**
     * 产品标题
     */
    @Column("title")
    private String title;
    /**
     * 产品链接
     */
    @Column("url")
    private String url;
    /**
     * 制造商
     */
    @Column("manufacturer")
    private String manufacturer;
    @Column("is_valid")
    private Integer isValid;
    /**
     * 危险品运输
     */
    @Column("dangerous_goods_li_shipping")
    private String dangerousGoodsLiShipping;
    /**
     * 危险品标签
     */
    @Column("danger_label")
    private String dangerLabel;


    /**
     * 站点
     */
    @Column("site")
    private String site;

    public SomKauflandListing() {
    }

    /**
     * 主键id
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键id
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 状态
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 最短运输天数
     *
     * @return
     */
    public Integer getTransportTimeMin() {
        return transportTimeMin;
    }

    /**
     * 最短运输天数
     *
     * @param transportTimeMin
     */
    public void setTransportTimeMin(Integer transportTimeMin) {
        this.transportTimeMin = transportTimeMin;
    }

    /**
     * 最长运输天数
     *
     * @return
     */
    public Integer getTransportTimeMax() {
        return transportTimeMax;
    }

    /**
     * 最长运输天数
     *
     * @param transportTimeMax
     */
    public void setTransportTimeMax(Integer transportTimeMax) {
        this.transportTimeMax = transportTimeMax;
    }

    /**
     * 单位ID
     *
     * @return
     */
    public String getidUnit() {
        return idUnit;
    }

    /**
     * 单位ID
     *
     * @param idUnit
     */
    public void setidUnit(String idUnit) {
        this.idUnit = idUnit;
    }

    /**
     * 注意事项
     *
     * @return
     */
    public String getNote() {
        return note;
    }

    /**
     * 注意事项
     *
     * @param note
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     * 条件
     *
     * @return
     */
    public String getCondition() {
        return condition;
    }

    /**
     * 条件
     *
     * @param condition
     */
    public void setCondition(String condition) {
        this.condition = condition;
    }

    /**
     * 建议零售价
     *
     * @return
     */
    public Integer getListingPrice() {
        return listingPrice;
    }

    /**
     * 建议零售价
     *
     * @param listingPrice
     */
    public void setListingPrice(Integer listingPrice) {
        this.listingPrice = listingPrice;
    }

    /**
     * 最低价
     *
     * @return
     */
    public Integer getMinimumPrice() {
        return minimumPrice;
    }

    /**
     * 最低价
     *
     * @param minimumPrice
     */
    public void setMinimumPrice(Integer minimumPrice) {
        this.minimumPrice = minimumPrice;
    }

    /**
     * 售价
     *
     * @return
     */
    public Integer getPrice() {
        return price;
    }

    /**
     * 售价
     *
     * @param price
     */
    public void setPrice(Integer price) {
        this.price = price;
    }

    /**
     * 报价的ID
     *
     * @return
     */
    public String getidOffer() {
        return idOffer;
    }

    /**
     * 报价的ID
     *
     * @param idOffer
     */
    public void setidOffer(String idOffer) {
        this.idOffer = idOffer;
    }

    /**
     * 产品ID
     *
     * @return
     */
    public String getidProduct() {
        return idProduct;
    }

    /**
     * 产品ID
     *
     * @param idProduct
     */
    public void setidProduct(String idProduct) {
        this.idProduct = idProduct;
    }

    /**
     * 运费模板ID
     *
     * @return
     */
    public String getidShippingGroup() {
        return idShippingGroup;
    }

    /**
     * 运费模板ID
     *
     * @param idShippingGroup
     */
    public void setidShippingGroup(String idShippingGroup) {
        this.idShippingGroup = idShippingGroup;
    }

    /**
     * 仓库ID
     *
     * @return
     */
    public String getidWarehouse() {
        return idWarehouse;
    }

    /**
     * 仓库ID
     *
     * @param idWarehouse
     */
    public void setidWarehouse(String idWarehouse) {
        this.idWarehouse = idWarehouse;
    }

    /**
     * 库存
     *
     * @return
     */
    public Integer getAmount() {
        return amount;
    }

    /**
     * 库存
     *
     * @param amount
     */
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    /**
     * 产品的创建时间
     *
     * @return
     */
    public Date getDateInsertedIso() {
        return dateInsertedIso;
    }

    /**
     * 产品的创建时间
     *
     * @param dateInsertedIso
     */
    public void setDateInsertedIso(Date dateInsertedIso) {
        this.dateInsertedIso = dateInsertedIso;
    }

    /**
     * 最后修改时间
     *
     * @return
     */
    public Date getDateLastchangeIso() {
        return dateLastchangeIso;
    }

    /**
     * 最后修改时间
     *
     * @param dateLastchangeIso
     */
    public void setDateLastchangeIso(Date dateLastchangeIso) {
        this.dateLastchangeIso = dateLastchangeIso;
    }

    /**
     * 备货时间
     *
     * @return
     */
    public Integer getHandlingTime() {
        return handlingTime;
    }

    /**
     * 备货时间
     *
     * @param handlingTime
     */
    public void setHandlingTime(Integer handlingTime) {
        this.handlingTime = handlingTime;
    }

    /**
     * 商店语言
     *
     * @return
     */
    public String getStorefront() {
        return storefront;
    }

    /**
     * 商店语言
     *
     * @param storefront
     */
    public void setStorefront(String storefront) {
        this.storefront = storefront;
    }

    /**
     * 物流天数
     *
     * @return
     */
    public Integer getShippingRate() {
        return shippingRate;
    }

    /**
     * 物流天数
     *
     * @param shippingRate
     */
    public void setShippingRate(Integer shippingRate) {
        this.shippingRate = shippingRate;
    }

    /**
     * 发货方式
     *
     * @return
     */
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    /**
     * 发货方式
     *
     * @param fulfillmentType
     */
    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    /**
     * ean
     *
     * @return
     */
    public String getEan() {
        return ean;
    }

    /**
     * ean
     *
     * @param ean
     */
    public void setEan(String ean) {
        this.ean = ean;
    }

    /**
     * 等级
     *
     * @return
     */
    public String getAgeRating() {
        return ageRating;
    }

    /**
     * 等级
     *
     * @param ageRating
     */
    public void setAgeRating(String ageRating) {
        this.ageRating = ageRating;
    }

    /**
     * 产品分类id
     *
     * @return
     */
    public String getidCategory() {
        return idCategory;
    }

    /**
     * 产品分类id
     *
     * @param idCategory
     */
    public void setidCategory(String idCategory) {
        this.idCategory = idCategory;
    }

    /**
     * 主图url
     *
     * @return
     */
    public String getMainPicture() {
        return mainPicture;
    }

    /**
     * 主图url
     *
     * @param mainPicture
     */
    public void setMainPicture(String mainPicture) {
        this.mainPicture = mainPicture;
    }

    /**
     * 产品标题
     *
     * @return
     */
    public String getTitle() {
        return title;
    }

    /**
     * 产品标题
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 产品链接
     *
     * @return
     */
    public String getUrl() {
        return url;
    }

    /**
     * 产品链接
     *
     * @param url
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 制造商
     *
     * @return
     */
    public String getManufacturer() {
        return manufacturer;
    }

    /**
     * 制造商
     *
     * @param manufacturer
     */
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getisValid() {
        return isValid;
    }

    public void setisValid(Integer isValid) {
        this.isValid = isValid;
    }

    /**
     * 危险品运输
     *
     * @return
     */
    public String getDangerousGoodsLiShipping() {
        return dangerousGoodsLiShipping;
    }

    /**
     * 危险品运输
     *
     * @param dangerousGoodsLiShipping
     */
    public void setDangerousGoodsLiShipping(String dangerousGoodsLiShipping) {
        this.dangerousGoodsLiShipping = dangerousGoodsLiShipping;
    }

    /**
     * 危险品标签
     *
     * @return
     */
    public String getDangerLabel() {
        return dangerLabel;
    }

    /**
     * 危险品标签
     *
     * @param dangerLabel
     */
    public void setDangerLabel(String dangerLabel) {
        this.dangerLabel = dangerLabel;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param dangerLabel
     */
    public void setSite(String site) {
        this.site = site;
    }
}
