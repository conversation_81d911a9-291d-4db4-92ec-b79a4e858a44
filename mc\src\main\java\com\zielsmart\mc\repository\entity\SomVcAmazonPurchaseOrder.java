package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 短装采购订单表
* gen by 代码生成器 2023-08-16
*/

@Table(name="mc.som_vc_amazon_purchase_order")
public class SomVcAmazonPurchaseOrder implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 市场
	 */
	@Column("marketplace")
	private String marketplace ;
	/**
	 * 采购订单编号
	 */
	@Column("purchase_order")
	private String purchaseOrder ;
	/**
	 * 字典值：10.DDP 20.FOB
	 */
	@Column("purchase_type")
	private Integer purchaseType ;
	/**
	 * 处理状态，字典值：10.Undisposed 20.Dispute 30.Case 1 40.Case 2 50.反馈VM 60.Success
	 */
	@Column("process_state")
	private Integer processState ;
	/**
	 * 财务备注
	 */
	@Column("finance_remark")
	private String financeRemark ;
	/**
	 * 索赔备注
	 */
	@Column("claim_remark")
	private String claimRemark ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomVcAmazonPurchaseOrder() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 市场
	*@return
	*/
	public String getMarketplace(){
		return  marketplace;
	}
	/**
	* 市场
	*@param  marketplace
	*/
	public void setMarketplace(String marketplace ){
		this.marketplace = marketplace;
	}
	/**
	* 采购订单编号
	*@return
	*/
	public String getPurchaseOrder(){
		return  purchaseOrder;
	}
	/**
	* 采购订单编号
	*@param  purchaseOrder
	*/
	public void setPurchaseOrder(String purchaseOrder ){
		this.purchaseOrder = purchaseOrder;
	}
	/**
	* 字典值：10.DDP 20.FOB
	*@return
	*/
	public Integer getPurchaseType(){
		return  purchaseType;
	}
	/**
	* 字典值：10.DDP 20.FOB
	*@param  purchaseType
	*/
	public void setPurchaseType(Integer purchaseType ){
		this.purchaseType = purchaseType;
	}
	/**
	* 处理状态，字典值：10.Undisposed 20.Dispute 30.Case 1 40.Case 2 50.反馈VM 60.Success
	*@return
	*/
	public Integer getProcessState(){
		return  processState;
	}
	/**
	* 处理状态，字典值：10.Undisposed 20.Dispute 30.Case 1 40.Case 2 50.反馈VM 60.Success
	*@param  processState
	*/
	public void setProcessState(Integer processState ){
		this.processState = processState;
	}
	/**
	* 财务备注
	*@return
	*/
	public String getFinanceRemark(){
		return  financeRemark;
	}
	/**
	* 财务备注
	*@param  financeRemark
	*/
	public void setFinanceRemark(String financeRemark ){
		this.financeRemark = financeRemark;
	}
	/**
	* 索赔备注
	*@return
	*/
	public String getClaimRemark(){
		return  claimRemark;
	}
	/**
	* 索赔备注
	*@param  claimRemark
	*/
	public void setClaimRemark(String claimRemark ){
		this.claimRemark = claimRemark;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
