package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* ean管理
* gen by 代码生成器 2023-04-13
*/

@Table(name="mc.som_ean_info")
public class SomEanInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 顺序号
	 */
	@Column("sequence_num")
	private String sequenceNum ;
	/**
	 * 校验码
	 */
	@Column("check_code")
	private Integer checkCode ;
	/**
	 * EAN
	 */
	@Column("ean")
	private String ean ;
	/**
	 * 自发展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 归属方
	 */
	@Column("belong")
	private String belong ;
	/**
	 * 状态 0未占用 1已占用 2已使用
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 备注
	 */
	@Column("remarks")
	private String remarks ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomEanInfo() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 顺序号
	*@return
	*/
	public String getSequenceNum(){
		return  sequenceNum;
	}
	/**
	* 顺序号
	*@param  sequenceNum
	*/
	public void setSequenceNum(String sequenceNum ){
		this.sequenceNum = sequenceNum;
	}
	/**
	* 校验码
	*@return
	*/
	public Integer getCheckCode(){
		return  checkCode;
	}
	/**
	* 校验码
	*@param  checkCode
	*/
	public void setCheckCode(Integer checkCode ){
		this.checkCode = checkCode;
	}
	/**
	* EAN
	*@return
	*/
	public String getEan(){
		return  ean;
	}
	/**
	* EAN
	*@param  ean
	*/
	public void setEan(String ean ){
		this.ean = ean;
	}
	/**
	* 自发展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 自发展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 归属方
	*@return
	*/
	public String getBelong(){
		return  belong;
	}
	/**
	* 归属方
	*@param  belong
	*/
	public void setBelong(String belong ){
		this.belong = belong;
	}
	/**
	* 状态 0未占用 1已占用 2已使用
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态 0未占用 1已占用 2已使用
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemarks(){
		return  remarks;
	}
	/**
	* 备注
	*@param  remarks
	*/
	public void setRemarks(String remarks ){
		this.remarks = remarks;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
