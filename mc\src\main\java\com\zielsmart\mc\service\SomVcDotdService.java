package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.enums.VcDotdPromotionType;
import com.zielsmart.mc.enums.VcPromotionStatusEnum;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomVcDotd;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zielsmart.mc.McConstants.NEED_CHECK;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Vc DOTO表管理
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcDotdService {

    @Resource
    private SomVcDotdMapper vcDotdMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private SomVcPromotionService vcPromotionService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return VC DOTD 营销活动集合
     */
    public PageVo<SomVcDotdVo> queryByPage(SomVcDotdPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcDotdVo> pageResult = vcDotdMapper.queryByPage(searchVo, pageRequest);
        List<SomVcDotdVo> somVcDotdVos = pageResult.getList();
        if (CollUtil.isNotEmpty(somVcDotdVos)) {
            Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getDictionaryMap();
            somVcDotdVos.forEach(data -> {
                // 活动类型/大促类型/状态/申请原因
                McDictionaryInfo promotionTypeDict = dictionaryMap.get("VcPromotionType").get(String.valueOf(data.getPromotionType()));
                data.setPromotionTypeDesc(promotionTypeDict == null ? null : promotionTypeDict.getItemLable());
                McDictionaryInfo campaignTypeDict = dictionaryMap.get("VcPromotionCampaignType").get(String.valueOf(data.getCampaignType()));
                data.setCampaignTypeDesc(campaignTypeDict == null ? null : campaignTypeDict.getItemLable());
                McDictionaryInfo vcDotdStatusDict = dictionaryMap.get("VcDotdStatus").get(String.valueOf(data.getStatus()));
                data.setStatusDesc(vcDotdStatusDict == null ? null : vcDotdStatusDict.getItemLable());
                McDictionaryInfo vcPromotionApplyReasonDict = dictionaryMap.get("VcPromotionApplyReason").get(String.valueOf(data.getApplyReason()));
                data.setApplyReasonDesc(vcPromotionApplyReasonDict == null ? null : vcPromotionApplyReasonDict.getItemLable());
                // 当地税率
                McDictionaryInfo vcDict = dictionaryMap.get("VC").get(data.getSite());
                data.setLocalTaxRate(vcDict == null ? null : vcDict.getItemValue5());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomVcDotdVo.class, searchVo);
    }

    /**
     * 添加/编辑
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void addOrEdit(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcDotdVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        checkSaveOrEditParam(somVcDotdVo);
        String aid = somVcDotdVo.getAid();
        if (StrUtil.isEmpty(aid)) {
            add(somVcDotdVo, tokenUser);
        } else {
            edit(somVcDotdVo, tokenUser);
        }
    }

    /**
     * 新增 VC DOTD 营销活动
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void add(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        // 判端活动是否存在
        checkPromotionExist(somVcDotdVo);
        // 核验销售视图是否存在并填充销售视图相关信息
        SomVcDotd somVcDotd = new SomVcDotd();
        buildSomVcDotd(somVcDotdVo, somVcDotd);
        // 填充销售视图相关信息
        checkProductSalesField(somVcDotd);
        // 主键ID
        somVcDotd.setAid(IdUtil.fastSimpleUUID());
        // 新增默认状态：草稿
        somVcDotd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
        // 新增默认活动类型：DOTD
        somVcDotd.setPromotionType(VcDotdPromotionType.DOTD.getType());
        somVcDotd.setCreateNum(tokenUser.getJobNumber());
        somVcDotd.setCreateName(tokenUser.getUserName());
        somVcDotd.setCreateTime(DateTime.now().toJdkDate());

        vcDotdMapper.insert(somVcDotd);
    }

    /**
     * 修改 VC DOTD 营销活动
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void edit(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据是否存在
        SomVcDotd somVcDotd = checkSomVcDotdExist(somVcDotdVo);
        // [草稿,需要关注]状态才可以编辑
        List<Integer> allowEditStatus = VcPromotionStatusEnum.getAllowEditStatus();
        if (!allowEditStatus.contains(somVcDotd.getStatus())) {
            throw new ValidateException("当前数据不允许编辑！");
        }
        // 判端活动是否存在
        checkPromotionExist(somVcDotdVo);
        // 构建需要更新的字段属性
        buildSomVcDotd(somVcDotdVo, somVcDotd);
        // 核验销售视图是否存在并填充销售视图相关信息
        checkProductSalesField(somVcDotd);
        // 编辑后 -> 草稿
        somVcDotd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
        somVcDotd.setModifyNum(tokenUser.getJobNumber());
        somVcDotd.setModifyName(tokenUser.getUserName());
        somVcDotd.setModifyTime(DateTime.now().toJdkDate());
        vcDotdMapper.updateById(somVcDotd);
    }

    /**
     * 取消 VC DOTD 营销活动
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void cancel(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        SomVcDotd somVcDotd = checkSomVcDotdExist(somVcDotdVo);
        // [进行中,需要关注,未开始]的活动允许取消
        List<Integer> allowCancelStatus = VcPromotionStatusEnum.getAllowCancelStatus();
        if (!allowCancelStatus.contains(somVcDotd.getStatus())) {
            throw new ValidateException("当前数据不允许取消！");
        }
        // 状态 -> 已取消
        somVcDotd.setStatus(VcPromotionStatusEnum.CANCELED.getStatus());
        somVcDotd.setModifyNum(tokenUser.getJobNumber());
        somVcDotd.setModifyName(tokenUser.getUserName());
        somVcDotd.setModifyTime(DateTime.now().toJdkDate());
        vcDotdMapper.updateById(somVcDotd);
    }

    /**
     * 删除
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void delete(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        SomVcDotd somVcDotd = checkSomVcDotdExist(somVcDotdVo);
        // [草稿]状态才可以删除
        List<Integer> allowDeleteStatus = VcPromotionStatusEnum.getAllowDeleteStatus();
        if (!allowDeleteStatus.contains(somVcDotd.getStatus())) {
            throw new ValidateException("当前数据不允许删除！");
        }
        vcDotdMapper.createLambdaQuery().andEq("aid", somVcDotdVo.getAid()).delete();
    }

    /**
     * 反馈提报结果
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    public void feedbackSubmitResult(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        // 核验是否存在
        SomVcDotd somVcDotd = checkSomVcDotdExist(somVcDotdVo);
        // 反馈提报结果状态必须是 [提报成功,提报失败,Need Review]
        Integer submitFailedStatus = VcPromotionStatusEnum.SUBMIT_FAILED.getStatus();
        Integer successStatus = VcPromotionStatusEnum.SUBMIT_SUCCESS.getStatus();
        Integer needReviewStatus = VcPromotionStatusEnum.SUBMIT_NEED_REVIEW.getStatus();
        List<Integer> allowFeedbackStatus = Arrays.asList(submitFailedStatus, successStatus, needReviewStatus);
        Integer submitStatus = somVcDotdVo.getStatus();
        if (!allowFeedbackStatus.contains(submitStatus)) {
            throw new ValidateException("提报结果参数有误！");
        }
        // 核验当前状态
        Integer nowStatus = somVcDotd.getStatus();
        List<Integer> allowSubmitStatus = VcPromotionStatusEnum.getAllowSubmitStatus();
        if (!allowSubmitStatus.contains(nowStatus)) {
            throw new ValidateException("当前数据不允许反馈提报结果！");
        }
        // 如果是提报失败/Need Review，则失败原因必填
        String submissionFailureRemark = somVcDotdVo.getSubmissionFailureRemark();
        List<Integer> needFillFailureRemarkStatus = Arrays.asList(submitFailedStatus, needReviewStatus);
        if (needFillFailureRemarkStatus.contains(submitStatus) && StrUtil.isEmpty(submissionFailureRemark)) {
            throw new ValidateException("提报结果为提报失败或NeedReview时，失败原因不能为空！");
        }
        // 如果是提报成功，状态 -> 未开始；非提报成功，状态 -> 提报的状态
        if (successStatus.equals(submitStatus)) {
            somVcDotd.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
            somVcDotd.setSubmissionFailureRemark(null);
        } else {
            somVcDotd.setStatus(submitStatus);
            somVcDotd.setSubmissionFailureRemark(submissionFailureRemark);
        }
        somVcDotd.setModifyNum(tokenUser.getJobNumber());
        somVcDotd.setModifyName(tokenUser.getUserName());
        somVcDotd.setModifyTime(DateTime.now().toJdkDate());
        vcDotdMapper.updateById(somVcDotd);
    }

    /**
     * 补充大促起止日期
     *
     * @param somVcDotdVo 入参
     * @param tokenUser   当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void supplyPromotionDate(SomVcDotdVo somVcDotdVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcDotdVo)) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        String site = somVcDotdVo.getSite();
        String accountName = somVcDotdVo.getAccountName();
        String vendorCode = somVcDotdVo.getVendorCode();
        Integer campaignType = somVcDotdVo.getCampaignType();
        Date startDate = somVcDotdVo.getStartDate();
        Date endDate = somVcDotdVo.getEndDate();
        if (!ObjectUtil.isAllNotEmpty(site, accountName, vendorCode, campaignType, startDate, endDate)) {
            throw new ValidateException("站点、账号、Vendor Code、大促类型、起止时间不能为空！");
        }
        // 此功能仅开放 大促类型字典 item_value1 != 1 的使用
        Map<String, McDictionaryInfo> campaignTypeDictMap = getDictionaryMap().get("VcPromotionCampaignType");
        McDictionaryInfo campaignTypeDict = campaignTypeDictMap.get(String.valueOf(campaignType));
        if (campaignTypeDict == null || NEED_CHECK.equals(campaignTypeDict.getItemValue1())) {
            throw new ValidateException("大促类型有误！");
        }
        // 核验时间
        vcPromotionService.checkPromotionDate(startDate, endDate, false);
        // 更新数据，注意：只有[未开始]活动才允许补充大促起止日期
        somVcDotdVo.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
        somVcDotdVo.setModifyNum(tokenUser.getJobNumber());
        somVcDotdVo.setModifyName(tokenUser.getUserName());
        somVcDotdVo.setModifyTime(DateTime.now().toJdkDate());
        vcDotdMapper.supplyPromotionDate(somVcDotdVo);
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64编码数据
     */
    public String export(SomVcDotdPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomVcDotdVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook;
            try {
                ExportParams params = new ExportParams(null, "VC DOTD营销活动");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomVcDotdVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * DOTD 导入
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomVcDotdImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验导入的必须是同一站点
        vcPromotionService.checkImportBasic(importVos);
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：更新数据
        Date now = DateTime.now().toJdkDate();
        List<SomVcDotd> vcDotdList = new ArrayList<>();
        for (SomVcDotdImportVo importVo : importVos) {
            SomVcDotd somVcDotd = ConvertUtils.beanConvert(importVo, SomVcDotd.class);
            somVcDotd.setAid(IdUtil.fastSimpleUUID());
            // 币种
            somVcDotd.setCurrency(importVo.getCurrency());
            // 新增默认状态：草稿
            somVcDotd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            // 销售视图信息
            McProductSalesVo productSalesVo = importVo.getProductSalesVo();
            somVcDotd.setSalesGroupCode(productSalesVo.getSalesGroupCode());
            somVcDotd.setSalesGroupName(productSalesVo.getSalesGroupName());
            somVcDotd.setSalesGroupEmptCode(productSalesVo.getSalesGroupEmptCode());
            somVcDotd.setSalesGroupEmptName(productSalesVo.getSalesGroupEmptName());
            somVcDotd.setOperationEmptCode(productSalesVo.getOperationEmptCode());
            somVcDotd.setOperationEmptName(productSalesVo.getOperationEmptName());
            // 新增默认活动类型：DOTD
            somVcDotd.setPromotionType(VcDotdPromotionType.DOTD.getType());
            somVcDotd.setCreateNum(tokenUser.getJobNumber());
            somVcDotd.setCreateName(tokenUser.getUserName());
            somVcDotd.setCreateTime(now);
            vcDotdList.add(somVcDotd);
        }
        vcDotdMapper.insertBatch(vcDotdList);
    }

    /**
     * 反馈反馈结果导入
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    public void importSubmitResult(List<SomVcDotdSubmitResultImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 展示码/ASIN只允许一列有值
        checkImportSubmitResultBasic(importVos);
        // 核验数据，获取错误信息
        List<String> errors = checkSubmitResultImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：更新数据
        List<SomVcDotd> updateVcDotdList = new ArrayList<>();
        for (SomVcDotdSubmitResultImportVo importVo : importVos) {
            SomVcDotd somVcDotd = new SomVcDotd();
            somVcDotd.setAid(importVo.getAid());
            somVcDotd.setStatus(importVo.getStatus());
            somVcDotd.setSubmissionFailureRemark(importVo.getSubmissionFailureRemark());
            somVcDotd.setModifyName(tokenUser.getUserName());
            somVcDotd.setModifyNum(tokenUser.getJobNumber());
            somVcDotd.setModifyTime(DateTime.now().toJdkDate());
            updateVcDotdList.add(somVcDotd);
        }
        vcDotdMapper.batchUpdateSubmitResult(updateVcDotdList);
    }

    /**
     * 核验DOTD导入数据
     *
     * @param importVos 导入行数据
     * @return 错误信息集合
     */
    private List<String> checkImportData(List<SomVcDotdImportVo> importVos) {
        List<String> errors = new ArrayList<>();
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getImportDictionaryMap();
        SomVcPromotionImportInfoVo importInfoVo = vcPromotionService.queryImportSellerSkus(importVos);
        // 核验重复
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomVcDotdImportVo importVo : importVos) {
            // 基础核验，核验必填项，填写的字段是否正确
            if (!checkImportFields(importVo, dictionaryMap, errors, repeatCheckSet)) {
                continue;
            }
            // 核验活动是否存在
            if (checkImportPromotionExist(importVo)) {
                errors.add(StrUtil.concat(true, "错误20：", importVo.getUniqueMsg(), "该产品已存在营销活动，为了防止叠加折扣，不允许重复新增！"));
                continue;
            }
            // 核验销售视图、VC 前台售价、可销售天数、是否存在，并填充，包括 30天DMS
            boolean checkImportBasic = vcPromotionService.checkImportBasic(importVo, importInfoVo, errors);
            if (checkImportBasic) {
                // 处理价格：「折扣」或者「活动价格」
                handleImportPrice(importVo);
            }
            // 币种
            importVo.setCurrency(importInfoVo.getCurrency());
        }
        return errors;
    }

    /**
     * 处理价格：「折扣」或者「活动价格」
     *
     * @param importVo 导入行数据
     */
    private void handleImportPrice(SomVcDotdImportVo importVo) {
        // 「折扣」或者「活动价格」任填其一，需要计算出另一个的值，如果两个都填写了，忽略
        if (importVo.getDiscount() == null && importVo.getDealPrice() != null) {
            //「折扣」 = 1 - (「秒杀价格」 / RRP)
            BigDecimal temp = importVo.getDealPrice().divide(importVo.getRecommendedRetailPrice(), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal discount = BigDecimal.ONE.subtract(temp).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setDiscount(discount);
        } else if (importVo.getDiscount() != null && importVo.getDealPrice() == null) {
            // 「活动价格」 = RRP - RRP * 「折扣」= RRP (1 - 折扣)
            BigDecimal dealPrice = importVo.getRecommendedRetailPrice().multiply(BigDecimal.ONE.subtract(importVo.getDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setDealPrice(dealPrice);
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        } else {
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        }
    }

    /**
     * DOTD 导入行数据基础核验
     *
     * @param importVo       行数据
     * @param dictionaryMap  字典map
     * @param errors         错误集合
     * @param repeatCheckSet 核验重复Set
     * @return boolean
     */
    private boolean checkImportFields(SomVcDotdImportVo importVo,
                                      Map<String, Map<String, McDictionaryInfo>> dictionaryMap,
                                      List<String> errors,
                                      Set<String> repeatCheckSet) {
        // 核验必填项
        String site = importVo.getSite();
        String accountName = importVo.getAccountName();
        String vendorCode = importVo.getVendorCode();
        String cooperationModelStr = importVo.getCooperationModelStr();
        String campaignTypeStr = importVo.getCampaignTypeStr();
        String applyReasonStr = importVo.getApplyReasonStr();
        String perUnitFundingStr = importVo.getPerUnitFundingStr();
        if ((!StrUtil.isAllNotEmpty(site, accountName, vendorCode, cooperationModelStr, campaignTypeStr, applyReasonStr, perUnitFundingStr))) {
            errors.add("错误0：请输入必填项！");
            return false;
        }
        // 展示码和asin不允许都为空
        String sellerSku = importVo.getSellerSku();
        String asin = importVo.getAsin();
        if (StrUtil.isAllEmpty(sellerSku, asin)) {
            errors.add("错误1：展示码、ASIN不能都为空！");
            return false;
        }
        // 折扣比例、秒杀价格不能都为空
        String discountStr = importVo.getDiscountStr();
        String dealPriceStr = importVo.getDealPriceStr();
        if (StrUtil.isAllEmpty(discountStr, dealPriceStr)) {
            errors.add("错误2：折扣比例、秒杀价格不能都为空！");
            return false;
        }
        // 核验重复，以[站点+供应商编码+展示码/ASIN]唯一
        String key = importVo.getUniqueId();
        if (repeatCheckSet.contains(key)) {
            errors.add(StrUtil.concat(true, "错误3：", importVo.getUniqueMsg(), "数据重复！"));
            return false;
        }
        repeatCheckSet.add(key);
        // 核验合作模式
        Map<String, McDictionaryInfo> cooperationModelDictMap = dictionaryMap.get("VcDotdCooperationModel");
        McDictionaryInfo cooperationModelDict = cooperationModelDictMap.get(cooperationModelStr);
        if (cooperationModelDict == null) {
            errors.add(StrUtil.format("错误4：合作模式[{}]有误！", cooperationModelStr));
            return false;
        }
        importVo.setCooperationModel(Integer.valueOf(cooperationModelDict.getItemValue()));
        // 核验自发类型 1、如果合作模式字典的itemValue1为1，核必须填写自发类型；2、自发类型必须正确
        Map<String, McDictionaryInfo> dropshipTypeDictMap = dictionaryMap.get("VcDotdDropshipType");
        String dropshipTypeStr = importVo.getDropshipTypeStr();
        String cooperationModelDictItemValue1 = cooperationModelDict.getItemValue1();
        if (NEED_CHECK.equals(cooperationModelDictItemValue1) && StrUtil.isEmpty(dropshipTypeStr)) {
            errors.add(StrUtil.format("错误5：合作模式为[{}]时，必须填写自发类型！", cooperationModelStr));
            return false;
        }
        if (StrUtil.isNotEmpty(dropshipTypeStr)) {
            McDictionaryInfo dropshipTypeDict = dropshipTypeDictMap.get(dropshipTypeStr);
            if (dropshipTypeDict == null) {
                errors.add(StrUtil.format("错误6：自发类型[{}]有误！",  dropshipTypeStr));
                return false;
            }
            importVo.setDropshipType(Integer.valueOf(dropshipTypeDict.getItemValue()));
        }
        // 核验大促类型
        Map<String, McDictionaryInfo> campaignTypeDictMap = dictionaryMap.get("VcPromotionCampaignType");
        McDictionaryInfo campaignTypeDict = campaignTypeDictMap.get(campaignTypeStr);
        if (campaignTypeDict == null) {
            errors.add(StrUtil.format("错误7：大促类型[{}]有误！", campaignTypeStr));
            return false;
        }
        importVo.setCampaignType(Integer.valueOf(campaignTypeDict.getItemValue()));
        // 大促类型 itemValue2=1时，新增/修改时需要核验其他营销活动是否存在 itemValue2=1时，核验VC，itemValue2!=1时，核验DOTD
        importVo.setNormal(NEED_CHECK.equals(campaignTypeDict.getItemValue2()));
        // 核验活动日期，大促字典 itemValue1=1 时，需要填写活动开始日期、活动截止日期
        if (NEED_CHECK.equals(campaignTypeDict.getItemValue1())) {
            try {
                vcPromotionService.checkPromotionDate(importVo.getStartDate(), importVo.getEndDate(), true);
            } catch (ValidateException ex) {
                errors.add(StrUtil.concat(true, "错误8：", importVo.getUniqueMsg(), ex.getMessage()));
                return false;
            }
        } else {
            // 如果itemValue1!=1时，填了也当没填
            importVo.setStartDate(null);
            importVo.setEndDate(null);
        }
        // 核验申请原因
        Map<String, McDictionaryInfo> applyReasonDictMap = dictionaryMap.get("VcPromotionApplyReason");
        McDictionaryInfo applyReasonDict = applyReasonDictMap.get(applyReasonStr);
        if (applyReasonDict == null) {
            errors.add(StrUtil.format("错误9：申请原因[{}]有误！", applyReasonStr));
            return false;
        }
        importVo.setApplyReason(Integer.valueOf(applyReasonDict.getItemValue()));
        // 如果申请原因字典 itemValue=1，需要填写自定义申请原因，itemValue!=1，填了等于没填
        String customReason = importVo.getCustomReason();
        if (NEED_CHECK.equals(applyReasonDict.getItemValue1()) && StrUtil.isEmpty(customReason)) {
            errors.add(StrUtil.format("错误10：申请原因为[{}]时，必须填写自定义申请原因！",  applyReasonStr));
            return false;
        }
        importVo.setCustomReason(NEED_CHECK.equals(applyReasonDict.getItemValue1()) ? customReason : null);
        // 折扣比例，存在计算，不直接转换
        if (vcPromotionService.isInvalidAmount(discountStr)) {
            errors.add(StrUtil.format("错误11：折扣比例[{}]格式有误！", discountStr));
            return false;
        }
        importVo.setDiscount(StrUtil.isEmpty(discountStr) ? null : new BigDecimal(discountStr));
        // 核验秒杀价
        if (vcPromotionService.isInvalidAmount(dealPriceStr)) {
            errors.add(StrUtil.format("错误12：秒杀价格[{}]格式有误！", dealPriceStr));
            return false;
        }
        importVo.setDealPrice(StrUtil.isEmpty(dealPriceStr) ? null : new BigDecimal(dealPriceStr));
        // 核验 Funding
        if (vcPromotionService.isInvalidAmount(perUnitFundingStr)) {
            errors.add(StrUtil.format("错误13：Funding[{}]格式有误！", perUnitFundingStr));
            return false;
        }
        importVo.setPerUnitFunding(new BigDecimal(perUnitFundingStr));
        // 核验评分
        String scoreStr = importVo.getScoreStr();
        if (StrUtil.isNotEmpty(scoreStr) && vcPromotionService.isInvalidAmount(scoreStr)) {
            errors.add(StrUtil.format("错误14：评分[{}]格式有误！", scoreStr));
            return false;
        }
        importVo.setScore(StrUtil.isEmpty(scoreStr) ? null : new BigDecimal(scoreStr));
        return true;
    }

    /**
     * 核验反馈结果导入数据
     *
     * @param importVos 导入行数据
     * @return 错误信息集合
     */
    private List<String> checkSubmitResultImportData(List<SomVcDotdSubmitResultImportVo> importVos) {
        // 查询[草稿,提报中]活动的数据
        List<Integer> allowSubmitResultStatus = VcPromotionStatusEnum.getAllowSubmitStatus();
        List<SomVcDotd> vcDotdList = vcDotdMapper.createLambdaQuery().andIn("status", allowSubmitResultStatus).select();
        // key=站点+账号名称+供应商编码+大促类型+展示码/ASIN
        // 需要注意的是，导入的时候 展示码/ASIN 列只允许一列有值
        Set<String> asins = importVos.stream().map(SomVcDotdSubmitResultImportVo::getAsin).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        boolean isAsin = CollUtil.isNotEmpty(asins);
        Map<String, List<SomVcDotd>> vcDotdMap;
        if (isAsin) {
            vcDotdMap = vcDotdList.stream().collect(Collectors.groupingBy(x -> StrUtil.concat(true, x.getSite(), x.getAccountName(), x.getVendorCode(), String.valueOf(x.getCampaignType()), x.getAsin())));
        } else {
            vcDotdMap = vcDotdList.stream().collect(Collectors.groupingBy(x -> StrUtil.concat(true, x.getSite(), x.getAccountName(), x.getVendorCode(), String.valueOf(x.getCampaignType()), x.getSellerSku())));
        }
        // 查询字典
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getImportDictionaryMap();
        // 错误汇总
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomVcDotdSubmitResultImportVo importVo : importVos) {
            // 基础核验，核验必填项，填写的字段是否正确
            if (!checkSubmitResultImportFields(importVo, dictionaryMap, errors, repeatCheckSet)) {
                continue;
            }
            // 数据核验
            String site = importVo.getSite();
            String accountName = importVo.getAccountName();
            String vendorCode = importVo.getVendorCode();
            String sellerSku = importVo.getSellerSku();
            String asin = importVo.getAsin();
            String campaignTypeStr = String.valueOf(importVo.getCampaignType());
            // 核验数据数据库是否存在，以[站点+账号名称+供应商编码+大促类型+展示码/ASIN]
            String dataKey;
            if (isAsin) {
                dataKey = StrUtil.concat(true, site, accountName, vendorCode, campaignTypeStr, asin);
            } else {
                dataKey = StrUtil.concat(true, site, accountName, vendorCode, campaignTypeStr, sellerSku);
            }
            if (!vcDotdMap.containsKey(dataKey)) {
                errors.add(StrUtil.concat(true, "错误10：", importVo.getUniqueMsg(), "该活动数据不存在/当前状态不允许反馈提报结果！"));
                continue;
            }
            List<SomVcDotd> dotdList = vcDotdMap.get(dataKey);
            SomVcDotd somVcDotd = dotdList.stream()
                    .filter(x -> ObjectUtil.equal(x.getStartDate(), importVo.getStartDate()) && ObjectUtil.equal(x.getEndDate(), importVo.getEndDate()))
                    .findFirst()
                    .orElse(null);
            if (somVcDotd == null) {
                errors.add(StrUtil.concat(true, "错误10：", importVo.getUniqueMsg(), "该活动数据不存在/当前状态不允许反馈提报结果！"));
                continue;
            }
            importVo.setAid(somVcDotd.getAid());
        }
        return errors;
    }

    /**
     * 导入基础数据核验
     *
     * @param importVo       行数据
     * @param dictionaryMap  字典 map
     * @param errors         错误信息集合
     * @param repeatCheckSet 核验是否重复
     */
    private boolean checkSubmitResultImportFields(SomVcDotdSubmitResultImportVo importVo,
                                                  Map<String, Map<String, McDictionaryInfo>> dictionaryMap,
                                                  List<String> errors,
                                                  Set<String> repeatCheckSet) {
        // 核验必填项
        String site = importVo.getSite();
        String accountName = importVo.getAccountName();
        String vendorCode = importVo.getVendorCode();
        String campaignTypeStr = importVo.getCampaignTypeStr();
        String statusStr = importVo.getStatusStr();
        if ((!StrUtil.isAllNotEmpty(site, accountName, vendorCode, campaignTypeStr,  statusStr))) {
            errors.add("错误0：请输入必填项！");
            return false;
        }
        String asin = importVo.getAsin();
        String sellerSku = importVo.getSellerSku();
        if (StrUtil.isAllEmpty(asin, sellerSku)) {
            errors.add("错误1：展示码、ASIN不能都为空！");
            return false;
        }
        boolean isAsin = StrUtil.isNotEmpty(asin);
        // 核验数据重复
        String key = StrUtil.concat(true, site, vendorCode, isAsin ?  asin : sellerSku);
        if (repeatCheckSet.contains(key)) {
            errors.add(StrUtil.concat(true, "错误2：", importVo.getUniqueMsg(), "数据重复！"));
            return false;
        }
        repeatCheckSet.add(key);
        // 核验大促类型
        Map<String, McDictionaryInfo> campaignTypeDictMap = dictionaryMap.get("VcPromotionCampaignType");
        McDictionaryInfo campaignTypeDict = campaignTypeDictMap.get(campaignTypeStr);
        if (campaignTypeDict == null) {
            errors.add(StrUtil.format("错误2：大促类型[{}]有误！", campaignTypeStr));
            return false;
        }
        importVo.setCampaignType(Integer.valueOf(campaignTypeDict.getItemValue()));
        // 核验活动日期，大促字典 itemValue1=1 时，需要填写活动开始日期、活动截止日期
        if (NEED_CHECK.equals(campaignTypeDict.getItemValue1())) {
            try {
                vcPromotionService.checkPromotionDate(importVo.getStartDate(), importVo.getEndDate(), false);
            } catch (ValidateException ex) {
                errors.add(StrUtil.concat(true, "错误3：", ex.getMessage()));
                return false;
            }
        } else {
            importVo.setStartDate(null);
            importVo.setEndDate(null);
        }
        // 核验提报结果状态，
        Map<String, McDictionaryInfo> submitStatusDictMap = dictionaryMap.get("VcPromotionSubmitStatus");
        McDictionaryInfo importSubmitStatusDict = submitStatusDictMap.get(statusStr);
        if (importSubmitStatusDict == null) {
            errors.add(StrUtil.format("错误4：提报结果[{}]有误！", statusStr));
            return false;
        }
        String itemValue1 = importSubmitStatusDict.getItemValue1();
        if (NEED_CHECK.equals(itemValue1) && StrUtil.isEmpty(importVo.getSubmissionFailureRemark())) {
            errors.add(StrUtil.format("错误5：提报结果为[{}]时，必须填写失败原因！", statusStr));
            return false;
        }
        // 提报成功 -> 未开始，其他是导入文件中的提交状态
        Integer submitStatus = Integer.valueOf(importSubmitStatusDict.getItemValue());
        Integer successStatus = VcPromotionStatusEnum.SUBMIT_SUCCESS.getStatus();
        if (successStatus.equals(submitStatus)) {
            importVo.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
            importVo.setSubmissionFailureRemark(null);
        } else {
            importVo.setStatus(submitStatus);
        }
        return true;
    }

    /**
     * 核验新增/修改参数
     *
     * @param somVcDotdVo 入参
     */
    private void checkSaveOrEditParam(SomVcDotdVo somVcDotdVo) throws ValidateException {
        // 核验
        if (ObjectUtil.isEmpty(somVcDotdVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (!StrUtil.isAllNotEmpty(somVcDotdVo.getSite(), somVcDotdVo.getAccountName(),
                somVcDotdVo.getVendorCode(), somVcDotdVo.getSellerSku())) {
            throw new ValidateException("站点、账号、Vendor Code、展示码不能为空！");
        }
        Integer cooperationModel = somVcDotdVo.getCooperationModel();
        Integer campaignType = somVcDotdVo.getCampaignType();
        if (!ObjectUtil.isAllNotEmpty(cooperationModel, campaignType)) {
            throw new ValidateException("合作模式、大促类型不能为空！");
        }
        if (somVcDotdVo.getRecommendedRetailPrice() == null) {
            throw new ValidateException("该产品没有维护RRP，请先维护RRP！");
        }
        if (somVcDotdVo.getStockSaleDays() == null) {
            throw new ValidateException("库存可销售天数获取失败！");
        }
        // 价格核验
        if (!ObjectUtil.isAllNotEmpty(somVcDotdVo.getDiscount(), somVcDotdVo.getDealPrice(), somVcDotdVo.getPerUnitFunding())) {
            throw new ValidateException("折扣、秒杀价、Funding不能为空！");
        }
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getDictionaryMap();
        // 合作模式itemValue1为1时，自发类型不能为空
        McDictionaryInfo cooperationModelDict = dictionaryMap.get("VcDotdCooperationModel").get(String.valueOf(cooperationModel));
        if (cooperationModelDict == null) {
            throw new ValidateException("合作模式有误！");
        }
        if (NEED_CHECK.equals(cooperationModelDict.getItemValue1()) && somVcDotdVo.getDropshipType() == null) {
            throw new ValidateException("合作模式为" + cooperationModelDict.getItemLable() + "时，自发类型不能为空！");
        }
        // 申请原因itemValue1为1时，自定义原因不能为空
        if (somVcDotdVo.getApplyReason() == null) {
            throw new ValidateException("申请原因不能为空！");
        }
        McDictionaryInfo applyReasonDict = dictionaryMap.get("VcPromotionApplyReason").get(String.valueOf(somVcDotdVo.getApplyReason()));
        if (applyReasonDict == null) {
            throw new ValidateException("申请原因有误！");
        }
        if (NEED_CHECK.equals(applyReasonDict.getItemValue1()) && StrUtil.isEmpty(somVcDotdVo.getCustomReason())) {
            throw new ValidateException("申请原因为" + applyReasonDict.getItemLable() + "时，自定义原因不能为空！");
        }
        // 核验起止日期，如果大促类型[VCPromotionCampaignType]字典中[itemValue1=1]，需要填写起止日期，否则起止日期不能填写，填了也置为空
        McDictionaryInfo campaignTypeDict = dictionaryMap.get("VcPromotionCampaignType").get(String.valueOf(campaignType));
        if (campaignTypeDict == null) {
            throw new ValidateException("大促类型有误！");
        }
        if (NEED_CHECK.equals(campaignTypeDict.getItemValue1())) {
            Date startDate = somVcDotdVo.getStartDate();
            Date endDate = somVcDotdVo.getEndDate();
            vcPromotionService.checkPromotionDate(startDate, endDate, true);
        } else {
            somVcDotdVo.setStartDate(null);
            somVcDotdVo.setEndDate(null);
        }
        // 大促类型 itemValue2=1时，新增/修改时需要核验其他营销活动是否存在 itemValue2=1时，核验VC，itemValue2!=1时，核验DOTD
        somVcDotdVo.setIsNormal(NEED_CHECK.equals(campaignTypeDict.getItemValue2()));
    }

    /**
     * 核验导入的活动是否存在
     * 如果大促类型的 item_value2=1，需要核验「活动起止时间」范围内，VC 营销活动只允许「站点」「Vendor Code」「展示码」维度出现一条数据
     * 如果大促类型的 item_value2!=1，需要核验 DOTD营销活动只允许「站点」「Vendor Code」「展示码」「大促类型」维度出现一条数据
     *
     * @param importVo      导入行数据
     * @return boolean
     */
    private boolean checkImportPromotionExist(SomVcDotdImportVo importVo) {
        // 大促类型
        if (importVo.isNormal()) {
            SomVcPromotionCheckUniqueVo checkUniqueVo = vcPromotionService.buildImportCheckUniqueVo(importVo);
            return vcPromotionService.checkUnique(checkUniqueVo);
        } else {
            String site = importVo.getSite();
            String vendorCode = importVo.getVendorCode();
            String sellerSku = importVo.getSellerSku();
            String asin = importVo.getAsin();
            Integer campaignType = importVo.getCampaignType();
            return checkNonNormalPromotionExist(site, vendorCode, sellerSku, asin, campaignType, null);
        }
    }

    /**
     * 核验活动是否存在
     * 如果大促类型的 item_value2=1，需要核验「活动起止时间」范围内，VC营销活动只允许「站点」「Vendor Code」「展示码」维度出现一条数据
     * 如果大促类型的 item_value2!=1，需要核验DOTD营销活动只允许「站点」「Vendor Code」「展示码」「大促类型」维度出现一条数据
     *
     * @param somVcDotdVo 活动数据
     */
    private void checkPromotionExist(SomVcDotdVo somVcDotdVo) throws ValidateException {
        boolean exist;
        if (Boolean.TRUE.equals(somVcDotdVo.getIsNormal())) {
            SomVcPromotionCheckUniqueVo checkUniqueVo = buildCheckUniqueVo(somVcDotdVo);
            exist = vcPromotionService.checkUnique(checkUniqueVo);
        } else {
            String aid = somVcDotdVo.getAid();
            String site = somVcDotdVo.getSite();
            String vendorCode = somVcDotdVo.getVendorCode();
            String sellerSku = somVcDotdVo.getSellerSku();
            String asin = somVcDotdVo.getAsin();
            Integer campaignType = somVcDotdVo.getCampaignType();
            exist = checkNonNormalPromotionExist(site, vendorCode, sellerSku, asin, campaignType, aid);
        }
        if (exist) {
            throw new ValidateException("该产品已存在营销活动，为了防止叠加折扣，不允许重复新增！");
        }
    }

    /**
     * 核验DOTD营销活动是否存在
     *
     * @param somVcDotdVo 入参
     * @return SomVcDotd
     */
    private SomVcDotd checkSomVcDotdExist(SomVcDotdVo somVcDotdVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcDotdVo) || StrUtil.isEmpty(somVcDotdVo.getAid())) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        SomVcDotd somVcDotd = vcDotdMapper.createLambdaQuery().andEq("aid", somVcDotdVo.getAid()).single();
        if (somVcDotd == null) {
            throw new ValidateException("数据不存在或已删除！");
        }
        return somVcDotd;
    }

    /**
     * 获取字典
     * VC：VC 平台相关信息
     * VcDotdCooperationModel：VC DOTD 营销活动合作模式
     * VcDotdDropshipType：VC DOTD 营销活动自发类型
     * VcDotdStatus：VC DOTD 营销活动状态
     * VCPromotionCampaignType：VC 营销活动大促类型
     * VcPromotionApplyReason：VC 营销活动申请原因
     * VcPromotionType：VC 营销活动类型
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getDictionaryMap() {
        List<String> typeCodes = Arrays.asList("VC", "VcDotdCooperationModel", "VcDotdDropshipType", "VcDotdStatus",
                "VcPromotionCampaignType", "VcPromotionApplyReason", "VcPromotionType");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1)));
    }

    /**
     * 获取导入字典
     * 导入文件中填充的是字典的 label
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getImportDictionaryMap() {
        List<String> typeCodes = Arrays.asList("VcDotdCooperationModel", "VcDotdDropshipType", "VcPromotionCampaignType", "VcPromotionApplyReason", "VcPromotionSubmitStatus");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1)));
    }

    /**
     * 核验非普通营销活动是否存在
     *
     * @param site         站点
     * @param vendorCode   供应商编码
     * @param sellerSku    展示码
     * @param campaignType 大促类型
     * @return boolean
     */
    private boolean checkNonNormalPromotionExist(String site, String vendorCode, String sellerSku, String asin, Integer campaignType, String aid) {
        List<Integer> nonFinalState = VcPromotionStatusEnum.getNonFinalState();
        LambdaQuery<SomVcDotd> somVcDotdLambdaQuery = vcDotdMapper.createLambdaQuery()
                .andEq(SomVcDotd::getSite, site)
                .andEq(SomVcDotd::getVendorCode, vendorCode)
                .andEq(SomVcDotd::getCampaignType, campaignType)
                .andIn(SomVcDotd::getStatus, nonFinalState);
        if (StrUtil.isNotEmpty(aid)) {
            somVcDotdLambdaQuery.andNotEq(SomVcDotd::getAid, aid);
        }
        if (StrUtil.isNotEmpty(asin)) {
            somVcDotdLambdaQuery.andEq(SomVcDotd::getAsin, asin);
        }
        if (StrUtil.isNotEmpty(sellerSku)) {
            somVcDotdLambdaQuery.andEq(SomVcDotd::getSellerSku, sellerSku);
        }
        return somVcDotdLambdaQuery.count() > 0;
    }

    /**
     * 核验导入金额
     *
     * @param amountStr 金额数值
     * @param errorMsg  错误信息
     * @param errors    错误信息集合
     * @return boolean
     */
    private boolean checkImportFieldsAmount(String amountStr, String errorMsg, List<String> errors) {
        try {
            new BigDecimal(amountStr);
        } catch (Exception ex) {
            errors.add(StrUtil.concat(true, StrUtil.replace(errorMsg, "%s", amountStr)));
            return false;
        }
        return true;
    }

    /**
     * 核验并填充销售视图字段
     *
     * @param somVcDotd DOTD 营销活动
     */
    private void checkProductSalesField(SomVcDotd somVcDotd) throws ValidateException {
        String site = somVcDotd.getSite();
        String sellerSku = somVcDotd.getSellerSku();
        List<McProductSalesVo> productSales = vcPromotionService.queryVcProductSales(Collections.singletonList(site), Collections.singletonList(sellerSku));
        if (CollUtil.isEmpty(productSales)) {
            throw new ValidateException("该产品销售视图不存在！");
        }
        McProductSalesVo productSalesVo = productSales.get(0);
        somVcDotd.setSalesGroupCode(productSalesVo.getSalesGroupCode());
        somVcDotd.setSalesGroupName(productSalesVo.getSalesGroupName());
        somVcDotd.setSalesGroupEmptCode(productSalesVo.getSalesGroupEmptCode());
        somVcDotd.setSalesGroupEmptName(productSalesVo.getSalesGroupEmptName());
        somVcDotd.setOperationEmptCode(productSalesVo.getOperationEmptCode());
        somVcDotd.setOperationEmptName(productSalesVo.getOperationEmptName());
    }

    /**
     * 构建查询活动是否存在参数
     *
     * @param somVcDotdVo 活动数据
     * @return SomVcPromotionCheckUniqueVo
     */
    private SomVcPromotionCheckUniqueVo buildCheckUniqueVo(SomVcDotdVo somVcDotdVo) {
        SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
        checkUniqueVo.setDotdAid(somVcDotdVo.getAid());
        checkUniqueVo.setVendorCode(somVcDotdVo.getVendorCode());
        checkUniqueVo.setSite(somVcDotdVo.getSite());
        checkUniqueVo.setSellerSku(somVcDotdVo.getSellerSku());
        checkUniqueVo.setStartDate(somVcDotdVo.getStartDate());
        checkUniqueVo.setEndDate(somVcDotdVo.getEndDate());
        return checkUniqueVo;
    }


    /**
     * 构建需要更新的SomVcDotd对象属性
     *
     * @param somVcDotdVo 入参
     * @param somVcDotd   原有somVcDotd对象
     */
    private void buildSomVcDotd(SomVcDotdVo somVcDotdVo, SomVcDotd somVcDotd) {
        somVcDotd.setSite(somVcDotdVo.getSite());
        somVcDotd.setAccountName(somVcDotdVo.getAccountName());
        somVcDotd.setVendorCode(somVcDotdVo.getVendorCode());
        somVcDotd.setCooperationModel(somVcDotdVo.getCooperationModel());
        somVcDotd.setDropshipType(somVcDotdVo.getDropshipType());
        somVcDotd.setCampaignType(somVcDotdVo.getCampaignType());
        somVcDotd.setSku(somVcDotdVo.getSku());
        somVcDotd.setSellerSku(somVcDotdVo.getSellerSku());
        somVcDotd.setAsin(somVcDotdVo.getAsin());
        somVcDotd.setRecommendedRetailPrice(somVcDotdVo.getRecommendedRetailPrice());
        somVcDotd.setFrontSellPrice(somVcDotdVo.getFrontSellPrice());
        somVcDotd.setDmsLast30day(somVcDotdVo.getDmsLast30day());
        somVcDotd.setStockSaleDays(somVcDotdVo.getStockSaleDays());
        somVcDotd.setCostPrice(somVcDotdVo.getCostPrice());
        somVcDotd.setDiscount(somVcDotdVo.getDiscount());
        somVcDotd.setDealPrice(somVcDotdVo.getDealPrice());
        somVcDotd.setPerUnitFunding(somVcDotdVo.getPerUnitFunding());
        somVcDotd.setCurrency(somVcDotdVo.getCurrency());
        somVcDotd.setScore(somVcDotdVo.getScore());
        somVcDotd.setStartDate(somVcDotdVo.getStartDate());
        somVcDotd.setEndDate(somVcDotdVo.getEndDate());
        somVcDotd.setApplyReason(somVcDotdVo.getApplyReason());
        somVcDotd.setCustomReason(somVcDotdVo.getCustomReason());
    }

    /**
     * 反馈反馈结果导入基础核验
     *
     * @param importVos 行数据
     */
    private void checkImportSubmitResultBasic(List<SomVcDotdSubmitResultImportVo> importVos) throws ValidateException {
        List<String> sellerSkus = importVos.stream().map(SomVcDotdSubmitResultImportVo::getSellerSku).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<String> asins = importVos.stream().map(SomVcDotdSubmitResultImportVo::getAsin).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(asins) && CollUtil.isNotEmpty(sellerSkus)) {
            throw new ValidateException("展示码和ASIN列只允许一列有值，请检查数据！");
        }
    }
}
