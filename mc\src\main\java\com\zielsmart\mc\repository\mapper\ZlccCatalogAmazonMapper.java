package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.ZlccCatalogAmazonPageSearchVo;
import com.zielsmart.mc.vo.ZlccCatalogAmazonVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2023-12-01
 */

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title ZlccCatalogAmazonMapper
 * @description
 * @date 2023-12-01 15:25:58
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("zlccCatalogAmazon")
public interface ZlccCatalogAmazonMapper extends BaseMapper<ZlccCatalogAmazon> {
    /**
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult<ZlccCatalogAmazonVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<ZlccCatalogAmazonVo> queryByPage(@Param("searchVo") ZlccCatalogAmazonPageSearchVo searchVo, PageRequest pageRequest);

    List<ZlccCatalogAmazonVo> queryTreeListWithFavorite(@Param("site") String site, @Param("jobNumber") String jobNumber);

    /**
     * 根据site和父级类目ID查询子集
     *  要把根节点传过来，防止一个类目属于两个不一样的根
     *
     * @param site 站点
     * @param rootNodeId 根节点
     * @param browseParentId 父级ID
     * @return List<ZlccCatalogAmazonVo>
     */
    List<ZlccCatalogAmazonVo> queryChildCategory(@Param("site") String site, @Param("rootNodeId") Long rootNodeId, @Param("browseParentId") Long browseParentId);

    /**
     * 查询拥有子集的节点
     *  要把根节点传过来，防止一个类目属于两个不一样的根
     *
     * @param site 站点
     * @param rootNodeId 根节点
     * @param browseNodeIds 节点
     * @return 节点ID
     */
    List<Long> queryHasChildNodeIds(@Param("site") String site, @Param("rootNodeId") Long rootNodeId, @Param("browseNodeIds") List<Long> browseNodeIds);

    /**
     * 根据站点s和类目IDS查询
     *
     * @param sites 站点集合
     * @param platformIds 平台类目IDS，需要注意的是，其他数据库存储的一般都是 varchar
     * @return List<ZlccCatalogAmazon>
     */
    List<ZlccCatalogAmazon> queryBySitesAndBrowseIds(@Param("sites") List<String> sites, @Param("platformIds") List<String> platformIds);
}
