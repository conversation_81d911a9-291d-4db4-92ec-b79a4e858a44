package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuLocalGoodsVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2025-06-26
*/

@SqlResource("somTemuLocalGoods")
public interface SomTemuLocalGoodsMapper extends BaseMapper<SomTemuLocalGoods> {

}
