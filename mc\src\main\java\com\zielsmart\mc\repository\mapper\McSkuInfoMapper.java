package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McSkuInfoPageSearchVo;
import com.zielsmart.mc.vo.McSkuInfoVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2022-02-09
*/

@SqlResource("mcSkuInfo")
public interface McSkuInfoMapper extends BaseMapper<McSkuInfo> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McSkuInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McSkuInfoVo> queryByPage(@Param("searchVo")McSkuInfoPageSearchVo searchVo, PageRequest pageRequest);

    List<McSkuInfoVo> queryBySku(@Param("skuList")List<String> skuList);
}
