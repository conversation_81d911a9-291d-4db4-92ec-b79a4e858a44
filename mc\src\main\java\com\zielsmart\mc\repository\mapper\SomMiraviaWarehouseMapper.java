package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomMiraviaWarehousePageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-04-29
 */

@SqlResource("somMiraviaWarehouse")
public interface SomMiraviaWarehouseMapper extends BaseMapper<SomMiraviaWarehouse> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomMiraviaWarehouseVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMiraviaWarehouseVo> queryByPage(@Param("searchVo") SomMiraviaWarehousePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryList
     * 查询平台仓库列表
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomMiraviaWarehouseVo>}
     * <AUTHOR>
     * @history
     */
    List<SomMiraviaWarehouseVo> queryList();
}
