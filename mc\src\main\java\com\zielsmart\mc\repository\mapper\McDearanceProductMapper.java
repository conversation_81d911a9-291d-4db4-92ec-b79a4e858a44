package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McDearanceProduct;
import com.zielsmart.mc.vo.McDearanceProductExVo;
import com.zielsmart.mc.vo.McDearanceProductPageSearchVo;
import com.zielsmart.mc.vo.McDearanceProductVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-08-09
 */

@SqlResource("mcDearanceProduct")
public interface McDearanceProductMapper extends BaseMapper<McDearanceProduct> {

    /**
     * checkUnique
     * 重复性校验
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McDearanceProductVo>}
     * <AUTHOR>
     * @history
     */
    Integer checkUnique(@Param("searchVo") McDearanceProductVo searchVo);

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McDearanceProductVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<McDearanceProductExVo> queryByPage(@Param("searchVo") McDearanceProductPageSearchVo searchVo, PageRequest pageRequest);

    List<McDearanceProductExVo> queryList(@Param("searchVo") McDearanceProductPageSearchVo searchVo, @Param("isEu") String isEu);

    /**
     * queryByAids
     * 批量查询
     *
     * @param aids
     * @return {@link java.util.List<com.zielsmart.mc.vo.McDearanceProductVo>}
     * <AUTHOR>
     * @history
     */
    List<McDearanceProductVo> queryByAids(@Param("aids") List<String> aids);

    /**
     * updateByIdBatch
     * 更新状态
     *
     * @param mcDearanceProducts
     * <AUTHOR>
     * @history
     */
    default void updateDearanceProductByAids(@Param("mcDearanceProducts") List<McDearanceProduct> mcDearanceProducts) {
        this.getSQLManager().updateBatch(SqlId.of("mcDearanceProduct.updateDearanceProductByAids"), mcDearanceProducts);
    }
}
