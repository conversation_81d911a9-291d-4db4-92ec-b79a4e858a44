package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomToSellWhiteListService;
import com.zielsmart.mc.vo.McHandlingTimeConfigPageSearchVo;
import com.zielsmart.mc.vo.SomCouponImportVo;
import com.zielsmart.mc.vo.SomToSellWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomToSellWhiteListVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomToSellWhiteListController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somToSellWhiteList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "跟卖白名单管理")
public class SomToSellWhiteListController extends BasicController{

    @Resource
    SomToSellWhiteListService somToSellWhiteListService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomToSellWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomToSellWhiteListVo>> queryByPage(@RequestBody SomToSellWhiteListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somToSellWhiteListService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somToSellWhiteListVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/addOrEdit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomToSellWhiteListVo somToSellWhiteListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somToSellWhiteListService.save(somToSellWhiteListVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somToSellWhiteListVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomToSellWhiteListVo somToSellWhiteListVo) throws ValidateException {
        somToSellWhiteListService.delete(somToSellWhiteListVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel(){
        return "forward:/static/excel/ToSellWhiteTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"国家", "跟卖者店铺ID", "跟卖者店铺名称", "店铺分类", "备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomToSellWhiteListVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomToSellWhiteListVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somToSellWhiteListService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }
}
