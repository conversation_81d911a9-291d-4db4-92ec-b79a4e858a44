package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 客诉订单报表
* gen by 代码生成器 2022-04-11
*/

@Table(name="mc.som_attachment_order_list")
public class SomAttachmentOrderList implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 订单编号
	 */
	@Column("orderid")
	private String orderid ;
	/**
	 * 被投诉的产品编码
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 客诉创建时间
	 */
	@Column("createtime")
	private Date createtime ;
	/**
	 * 同步时间
	 */
	@Column("sync_time")
	private Date syncTime ;

	public SomAttachmentOrderList() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 订单编号
	*@return
	*/
	public String getOrderid(){
		return  orderid;
	}
	/**
	* 订单编号
	*@param  orderid
	*/
	public void setOrderid(String orderid ){
		this.orderid = orderid;
	}
	/**
	* 被投诉的产品编码
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* 被投诉的产品编码
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 客诉创建时间
	*@return
	*/
	public Date getCreatetime(){
		return  createtime;
	}
	/**
	* 客诉创建时间
	*@param  createtime
	*/
	public void setCreatetime(Date createtime ){
		this.createtime = createtime;
	}
	/**
	* 同步时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 同步时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}

}
