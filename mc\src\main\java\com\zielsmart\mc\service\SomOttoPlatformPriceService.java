package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.enums.OttoPlatformPriceAdjustStatusEnum;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description OTTO 平台价调整
 * @date 2025-04-01 15:49:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomOttoPlatformPriceService {

    @Resource
    private SomOttoPlatformPriceMapper ottoPlatformPriceMapper;

    @Resource
    private SomOttoPlatformPriceHistoryMapper ottoPlatformPriceHistoryMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private SomOttoListingMapper ottoListingMapper;

    @Resource
    private McPlatformPropertiesMapper platformPropertiesMapper;

    @Resource
    private IMagicService magicService;

    @Value("${spring.profiles.active}")
    public String activeProfile;

    @Value("${magic.head.token}")
    public String token;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOttoPlatformPriceVo>}
     * <AUTHOR>
     */
    public PageVo<SomOttoPlatformPriceVo> queryByPage(SomOttoPlatformPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOttoPlatformPriceVo> pageResult = ottoPlatformPriceMapper.queryByPage(searchVo, pageRequest);
        List<SomOttoPlatformPriceVo> priceVos = pageResult.getList();
        if (CollUtil.isNotEmpty(priceVos)) {
            Map<String, McDictionaryInfo> mcDictionaryInfoMap = getDictionaryMap().get("OttoPlatformPriceAdjustStatus");
            priceVos.forEach(data -> {
                McDictionaryInfo mcDictionaryInfo = mcDictionaryInfoMap.get(String.valueOf(data.getAdjustStatus()));
                data.setAdjustStatusDesc(mcDictionaryInfo == null ? null : mcDictionaryInfo.getItemLable());
                data.setAdjustStatusColor(mcDictionaryInfo == null ? null : mcDictionaryInfo.getItemValue2());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomOttoPlatformPriceVo.class, searchVo);
    }


    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64
     */
    public String export(SomOttoPlatformPricePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomOttoPlatformPriceVo> records = queryByPage(searchVo).getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        Workbook workbook;
        try {
            ExportParams params = new ExportParams(null, "OTTO平台价管理");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomOttoPlatformPriceVo.class, records);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询最近成功的调整记录
     *
     * @param ottoPlatformPriceOprVo 入参
     * @return SomOttoPlatformPriceHistoryVo
     */
    public SomOttoPlatformPriceHistoryVo queryLatestSuccess(SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo) throws ValidateException {
        String site = ottoPlatformPriceOprVo.getSite();
        String sellerSku = ottoPlatformPriceOprVo.getSellerSku();
        if (!StrUtil.isAllNotBlank(site, sellerSku)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        Integer successStatus = OttoPlatformPriceAdjustStatusEnum.SUCCESS.getStatus();
        return ottoPlatformPriceHistoryMapper.queryLatestSuccess(site, sellerSku, successStatus);
    }

    /**
     * 编辑
     *
     * @param ottoPlatformPriceOprVo 入参
     * @param tokenUser              当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo, TokenUserInfo tokenUser) throws ValidateException {
        String site = ottoPlatformPriceOprVo.getSite();
        String sellerSku = ottoPlatformPriceOprVo.getSellerSku();
        BigDecimal sellPrice = ottoPlatformPriceOprVo.getSellPrice();
        if (StrUtil.isBlank(site) || StrUtil.isBlank(sellerSku) || sellPrice == null) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        // 核验促销时间
        BigDecimal promotionalPrice = ottoPlatformPriceOprVo.getPromotionalPrice();
        String promotionStartTime = ottoPlatformPriceOprVo.getPromotionStartTime();
        String promotionEndTime = ottoPlatformPriceOprVo.getPromotionEndTime();
        if (promotionalPrice != null && (StrUtil.isBlank(promotionStartTime) || StrUtil.isBlank(promotionEndTime))) {
            throw new ValidateException("促销价填写，促销开始时间和促销结束时间必填！");
        }
        if ((StrUtil.isNotBlank(promotionStartTime) || StrUtil.isNotBlank(promotionEndTime)) && promotionalPrice == null) {
            throw new ValidateException("促销开始时间或促销结束时间不为空，促销价不能为空！");
        }
        // 核验 销售价 > 促销价
        if (promotionalPrice != null && sellPrice.compareTo(promotionalPrice) <= 0) {
            throw new ValidateException("不符合”销售价>促销价”规则！");
        }
        // 核验时间
        Date promotionStartDate = parseTime(promotionStartTime);
        Date promotionEndDate = parseTime(promotionEndTime);
        if (promotionStartDate != null && promotionEndDate != null) {
            // 获取当前时间的开始时间
            DateTime beginDate = DateUtil.beginOfDay(new Date());
            if (promotionStartDate.compareTo(beginDate) < 0) {
                throw new ValidateException("不符合”促销开始时间<=当前时间”规则！");
            }
            if (promotionStartDate.compareTo(promotionEndDate) > 0) {
                throw new ValidateException("不符合”促销结束时间>=促销开始时间”规则！");
            }
        }
        // 核验 listing
        SomOttoListing ottoListing = ottoListingMapper.createLambdaQuery().andEq("site", site).andEq("sku", sellerSku).single();
        if (ottoListing == null) {
            throw new ValidateException("OTTO Listing 不存在，请刷新页面重试！");
        }
        // 核验最新的调价记录
        SomOttoPlatformPrice oldOttoPlatformPrice = ottoPlatformPriceMapper.createLambdaQuery().andEq("site", site).andEq("seller_sku", sellerSku).single();
        if (oldOttoPlatformPrice != null && OttoPlatformPriceAdjustStatusEnum.isNotAllowEdit(oldOttoPlatformPrice.getAdjustStatus())) {
            throw new ValidateException("当前调价状态不允许编辑！");
        }
        SomOttoPlatformPrice ottoPlatformPrice = new SomOttoPlatformPrice();
        ottoPlatformPrice.setSite(site);
        ottoPlatformPrice.setSellerSku(sellerSku);
        ottoPlatformPrice.setSellPrice(sellPrice);
        ottoPlatformPrice.setPromotionalPrice(promotionalPrice);
        ottoPlatformPrice.setPromotionStartTime(promotionStartDate);
        ottoPlatformPrice.setPromotionEndTime(promotionEndDate);
        ottoPlatformPrice.setCurrency(getOttoCurrency(site));
        ottoPlatformPrice.setAdjustReason(ottoPlatformPriceOprVo.getAdjustReason());
        // 只要能编辑，编辑后就是草稿状态
        ottoPlatformPrice.setAdjustStatus(OttoPlatformPriceAdjustStatusEnum.DRAFT.getStatus());
        ottoPlatformPrice.setModifyName(tokenUser.getUserName());
        ottoPlatformPrice.setModifyNum(tokenUser.getJobNumber());
        ottoPlatformPrice.setModifyTime(new Date());
        // 数据库操作
        if (oldOttoPlatformPrice == null) {
            ottoPlatformPrice.setAid(IdUtil.fastUUID());
            ottoPlatformPriceMapper.insert(ottoPlatformPrice);
        } else {
            ottoPlatformPrice.setAid(oldOttoPlatformPrice.getAid());
            ottoPlatformPriceMapper.updateById(ottoPlatformPrice);
        }
    }

    /**
     * 批量提交调价申请
     *
     * @param ottoPlatformPriceOprVo 操作参数
     * @param tokenUser              当前登录用户
     * @throws ValidateException ex
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitApply(SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo, TokenUserInfo tokenUser) throws ValidateException {
        List<String> ottoPlatformPriceIds = ottoPlatformPriceOprVo.getOttoPlatformPriceIds();
        if (CollUtil.isEmpty(ottoPlatformPriceIds)) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }
        if (ottoPlatformPriceIds.size() > 500) {
            throw new ValidateException("调价申请数据数量＞500，请调整调价数量！");
        }
        List<SomOttoPlatformPrice> platformPrices = ottoPlatformPriceMapper.createLambdaQuery().andIn("aid", ottoPlatformPriceIds).select();
        if (CollUtil.isEmpty(platformPrices)) {
            throw new ValidateException("调价记录不存在，请刷新页面重试！");
        }
        // 只有草稿状态，才能提交调价申请
        List<String> sellerSkus = platformPrices.stream()
                .filter(data -> !OttoPlatformPriceAdjustStatusEnum.DRAFT.getStatus().equals(data.getAdjustStatus()))
                .map(SomOttoPlatformPrice::getSellerSku)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(sellerSkus)) {
            String errorMsg = StrUtil.concat(true, "展示码", String.join(",", sellerSkus), "的调价状态不是草稿，不允许提交调价申请，请刷新页面重试！");
            throw new ValidateException(errorMsg);
        }
        SomOttoPlatformPriceUpdatePriceResultVo resultVo = new SomOttoPlatformPriceUpdatePriceResultVo();
        // 拼装参数
        List<SomOttoPlatformPriceUpdatePriceVo> updatePriceVos = buildUpdatePriceVoRequestParam(platformPrices);
        // 如果是生产环境需要调用三方接口处理逻辑
        if ("prod".equals(activeProfile)) {
            // 调用三方接口
            resultVo = updatePriceByMagicApi(updatePriceVos);
        }
        // 数据库操作
        Date now = new Date();
        for (SomOttoPlatformPrice platformPrice : platformPrices) {
            // 返回成功调价状态为 调价中；返回失败调价状态为 调价失败
            platformPrice.setAdjustStatus(resultVo.isSuccess() ? OttoPlatformPriceAdjustStatusEnum.ONGOING.getStatus() : OttoPlatformPriceAdjustStatusEnum.FAILURE.getStatus());
            platformPrice.setAdjustFailureReason(resultVo.isSuccess() ? null : "调用magic-api接口失败！");
            platformPrice.setModifyName(tokenUser.getUserName());
            platformPrice.setModifyNum(tokenUser.getJobNumber());
            platformPrice.setModifyTime(now);
        }
        ottoPlatformPriceMapper.batchUpdate(platformPrices);
        List<SomOttoPlatformPriceHistory> priceHistories = ConvertUtils.listConvert(platformPrices, SomOttoPlatformPriceHistory.class);
        for (SomOttoPlatformPriceHistory priceHistory : priceHistories) {
            priceHistory.setAid(IdUtil.fastSimpleUUID());
            priceHistory.setRequestUuidParam(JSONUtil.toJsonStr(updatePriceVos));
            priceHistory.setPushPriceResult(resultVo.getResponseResult());
            priceHistory.setProcessUuid(resultVo.getProcessUuid());
        }
        ottoPlatformPriceHistoryMapper.insertBatch(priceHistories);
    }

    /**
     * 导入数据
     *
     * @param importVos 导入数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomOttoPlatformPriceImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        String site = "OTTO.de";
        // 数据处理
        List<SomOttoPlatformPrice> insertPlatformPrices = new ArrayList<>();
        List<SomOttoPlatformPrice> updatePlatformPrices = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomOttoPlatformPriceImportVo importVo : importVos) {
            SomOttoPlatformPrice ottoPlatformPrice = new SomOttoPlatformPrice();
            ottoPlatformPrice.setSite(site);
            ottoPlatformPrice.setSellerSku(importVo.getSellerSku());
            ottoPlatformPrice.setSellPrice(importVo.getSellPrice());
            ottoPlatformPrice.setPromotionalPrice(importVo.getPromotionalPrice());
            ottoPlatformPrice.setPromotionStartTime(importVo.getPromotionStartTime());
            ottoPlatformPrice.setPromotionEndTime(importVo.getPromotionEndTime());
            ottoPlatformPrice.setCurrency(getOttoCurrency(site));
            ottoPlatformPrice.setAdjustReason(importVo.getAdjustReason());
            ottoPlatformPrice.setAdjustStatus(OttoPlatformPriceAdjustStatusEnum.DRAFT.getStatus());
            ottoPlatformPrice.setModifyName(tokenUser.getUserName());
            ottoPlatformPrice.setModifyNum(tokenUser.getJobNumber());
            ottoPlatformPrice.setModifyTime(now);
            if (StrUtil.isBlank(importVo.getAid())) {
                ottoPlatformPrice.setAid(IdUtil.fastSimpleUUID());
                insertPlatformPrices.add(ottoPlatformPrice);
            } else {
                ottoPlatformPrice.setAid(importVo.getAid());
                updatePlatformPrices.add(ottoPlatformPrice);
            }
        }
        ottoPlatformPriceMapper.insertBatch(insertPlatformPrices);
        ottoPlatformPriceMapper.batchUpdate(updatePlatformPrices);
    }

    /**
     * 获取字典
     *
     * @return 字典 Map
     */
    public Map<String, Map<String, McDictionaryInfo>> getDictionaryMap() {
        List<String> typeCodes = Collections.singletonList("OttoPlatformPriceAdjustStatus");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1)));
    }

    /**
     * 核验导入数据
     *
     * @param importVos 导入的行数据
     * @return 错误信息
     */
    private List<String> checkImportData(List<SomOttoPlatformPriceImportVo> importVos) {
        String site = "OTTO.de";
        // 查询listing数据、调价数据
        List<String> sellerSkus = importVos.stream().map(SomOttoPlatformPriceImportVo::getSellerSku).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        List<String> listingSellerSkus = new ArrayList<>();
        Map<String, SomOttoPlatformPrice> platformPriceMap = new HashMap<>();
        if (CollUtil.isNotEmpty(sellerSkus)) {
            List<SomOttoListing> listings = ottoListingMapper.createLambdaQuery().andIn("sku", sellerSkus).andEq("site", site).select();
            listingSellerSkus = listings.stream().map(SomOttoListing::getSku).collect(Collectors.toList());
            List<SomOttoPlatformPrice> platformPrices = ottoPlatformPriceMapper.createLambdaQuery().andIn("seller_sku", sellerSkus).andEq("site", site).select();
            platformPriceMap = platformPrices.stream().collect(Collectors.toMap(SomOttoPlatformPrice::getSellerSku, Function.identity(), (x1, x2) -> x1));
        }
        List<String> errors = new ArrayList<>();
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomOttoPlatformPriceImportVo importVo : importVos) {
            String sellerSku = importVo.getSellerSku();
            String sellPriceStr = importVo.getSellPriceStr();
            String promotionalPriceStr = importVo.getPromotionalPriceStr();
            Date promotionStartTime = importVo.getPromotionStartTime();
            Date promotionEndTime = importVo.getPromotionEndTime();
            // 核验必填项
            if (!StrUtil.isAllNotBlank(sellerSku, sellPriceStr)) {
                errors.add("错误0：展示码、销售价不能为空！");
                continue;
            }
            if (StrUtil.isNotBlank(promotionalPriceStr) && (promotionStartTime == null || promotionEndTime == null)) {
                errors.add("错误1：促销价不为空，促销开始时间和促销结束时间不能为空，请确保时间格式正确！");
                continue;
            }
            if ((promotionStartTime != null || promotionEndTime != null) && StrUtil.isBlank(promotionalPriceStr)) {
                errors.add("错误2：促销开始时间或促销结束时间不为空，促销价不能为空，请确保时间格式正确！");
            }
            // 核验重复项
            if (repeatCheckSet.contains(sellerSku)) {
                errors.add(StrUtil.concat(true, "错误3：展示码[", sellerSku, "]数据重复！"));
                continue;
            }
            repeatCheckSet.add(sellerSku);
            // 核验导入价格
            if (!checkImportPrice(importVo, errors)) {
                continue;
            }
            // 核验导入时间
            if (!checkImportTime(importVo, errors)) {
                continue;
            }
            // 核验 listing
            if (!listingSellerSkus.contains(sellerSku)) {
                errors.add(StrUtil.concat(true, "错误11：展示码[", sellerSku, "]在listing中不存在！"));
                continue;
            }
            // 核验调价状态
            if (platformPriceMap.containsKey(sellerSku)) {
                SomOttoPlatformPrice ottoPlatformPrice = platformPriceMap.get(sellerSku);
                if (OttoPlatformPriceAdjustStatusEnum.isNotAllowEdit(ottoPlatformPrice.getAdjustStatus())) {
                    errors.add(StrUtil.concat(true, "错误12：展示码[", sellerSku, "]当前调价状态不允许导入！"));
                    continue;
                }
                importVo.setAid(ottoPlatformPrice.getAid());
            }
        }
        return errors;
    }

    /**
     * 核验导入金额
     *
     * @param importVo 导入行数据
     * @param errors   错误
     * @return boolean
     */
    private boolean checkImportTime(SomOttoPlatformPriceImportVo importVo, List<String> errors) {
        // 核验促销开始时间 | 促销结束时间
        Date promotionStartTime = importVo.getPromotionStartTime();
        Date promotionEndTime = importVo.getPromotionEndTime();
        if (promotionStartTime != null && promotionEndTime != null) {
            String promotionStartTimeStr = DateUtil.format(promotionStartTime, "yyyy-MM-dd");
            String promotionEndTimeStr = DateUtil.format(promotionEndTime, "yyyy-MM-dd");
            promotionStartTime = DateUtil.beginOfDay(promotionStartTime).toJdkDate();
            promotionEndTime = DateUtil.beginOfDay(promotionEndTime).toJdkDate();
            DateTime beginDate = DateUtil.beginOfDay(new Date());
            if (promotionStartTime.compareTo(beginDate) < 0) {
                errors.add(StrUtil.concat(true, "错误9：促销开始时间[", promotionStartTimeStr, "]需要满足当天及以后！"));
                return false;
            }
            if (promotionStartTime.compareTo(promotionEndTime) > 0) {
                errors.add(StrUtil.concat(true, "错误10：促销结束时间[", promotionEndTimeStr, "]不能小于促销开始时间[", promotionStartTimeStr + "]"));
                return false;
            }
            importVo.setPromotionStartTime(promotionStartTime);
            importVo.setPromotionEndTime(promotionEndTime);
        }
        return true;
    }

    /**
     * 核验导入金额
     *
     * @param importVo 导入行数据
     * @param errors   错误
     * @return boolean
     */
    private boolean checkImportPrice(SomOttoPlatformPriceImportVo importVo, List<String> errors) {
        // 核验销售价
        String sellPriceStr = importVo.getSellPriceStr();
        try {
            importVo.setSellPrice(new BigDecimal(sellPriceStr));
        } catch (Exception e) {
            errors.add(StrUtil.concat(true, "错误4：销售价[", sellPriceStr, "]格式有误！"));
            return false;
        }
        if (importVo.getSellPrice().compareTo(BigDecimal.ZERO) < 0) {
            errors.add(StrUtil.concat(true, "错误5：销售价[", sellPriceStr, "]不能低于0！"));
            return false;
        }
        // 核验促销价
        String promotionalPriceStr = importVo.getPromotionalPriceStr();
        if (StrUtil.isNotBlank(promotionalPriceStr)) {
            try {
                importVo.setPromotionalPrice(new BigDecimal(promotionalPriceStr));
            } catch (Exception e) {
                errors.add(StrUtil.concat(true, "错误6：促销价[", promotionalPriceStr, "]格式有误！"));
                return false;
            }
            if (importVo.getPromotionalPrice().compareTo(BigDecimal.ZERO) < 0) {
                errors.add(StrUtil.concat(true, "错误7：促销价[", sellPriceStr, "]不能低于0！"));
                return false;
            }
            if (importVo.getSellPrice().compareTo(importVo.getPromotionalPrice()) <= 0) {
                errors.add(StrUtil.concat(true, "错误8：销售价[", sellPriceStr, "]应大于促销价[", promotionalPriceStr, "]！"));
                return false;
            }
        }
        return true;
    }

    /**
     * 调用三方 magic api 接口
     *
     * @param updatePriceVos 参数
     * @return SomOttoPlatformPriceUpdatePriceResultVo
     */
    private SomOttoPlatformPriceUpdatePriceResultVo updatePriceByMagicApi(List<SomOttoPlatformPriceUpdatePriceVo> updatePriceVos) {
        SomOttoPlatformPriceUpdatePriceResultVo updatePriceResultVo = new SomOttoPlatformPriceUpdatePriceResultVo();
        updatePriceResultVo.setSuccess(false);
        try {
            ResultVo resultVo = magicService.updateOttoPrice(token, updatePriceVos);
            updatePriceResultVo.setResponseResult(JSONUtil.toJsonStr(resultVo));
            if (!resultVo.isSuccess()) {
                return updatePriceResultVo;
            }
            if (resultVo.getData() == null) {
                return updatePriceResultVo;
            }
            SomOttoPlatformPriceUpdatePriceResponseVo responseVo = JSONUtil.toBean(resultVo.getData().toString(), SomOttoPlatformPriceUpdatePriceResponseVo.class);
            if (responseVo == null) {
                return updatePriceResultVo;
            }
            List<SomOttoPlatformPriceUpdatePriceResponseVo.Link> links = responseVo.getLinks();
            if (CollUtil.isEmpty(links)) {
                return updatePriceResultVo;
            }
            links = links.stream().filter(x -> "self".equals(x.getRel())).collect(Collectors.toList());
            if (CollUtil.isEmpty(links)) {
                return updatePriceResultVo;
            }
            String href = links.get(0).getHref();
            if (StrUtil.isBlank(href)) {
                return updatePriceResultVo;
            }
            String[] split = href.split("/");
            updatePriceResultVo.setProcessUuid(split[split.length - 1]);
            updatePriceResultVo.setSuccess(true);
        } catch (Exception ex) {
            updatePriceResultVo.setResponseResult(ex.getMessage());
        }
        return updatePriceResultVo;
    }

    /**
     * 构建请求参数
     *
     * @param platformPrices 调价记录
     * @return List<SomOttoPlatformPriceUpdatePriceVo>
     */
    private List<SomOttoPlatformPriceUpdatePriceVo> buildUpdatePriceVoRequestParam(List<SomOttoPlatformPrice> platformPrices) {
        List<SomOttoPlatformPriceUpdatePriceVo> updatePriceVos = new ArrayList<>();
        for (SomOttoPlatformPrice platformPrice : platformPrices) {
            SomOttoPlatformPriceUpdatePriceVo updatePriceVo = new SomOttoPlatformPriceUpdatePriceVo();
            updatePriceVo.setSku(platformPrice.getSellerSku());
            // 销售价
            SomOttoPlatformPriceUpdatePriceVo.Price standardPrice = new SomOttoPlatformPriceUpdatePriceVo.Price();
            standardPrice.setAmount(platformPrice.getSellPrice());
            standardPrice.setCurrency(platformPrice.getCurrency());
            updatePriceVo.setStandardPrice(standardPrice);
            // 促销价
            BigDecimal promotionalPrice = platformPrice.getPromotionalPrice();
            if (promotionalPrice != null) {
                SomOttoPlatformPriceUpdatePriceVo.Sale sale = new SomOttoPlatformPriceUpdatePriceVo.Sale();
                SomOttoPlatformPriceUpdatePriceVo.Price salePrice = new SomOttoPlatformPriceUpdatePriceVo.Price();
                salePrice.setAmount(promotionalPrice);
                salePrice.setCurrency(platformPrice.getCurrency());
                sale.setSalePrice(salePrice);
                // 处理促销开始时间，促销开始时间= 数据库时间-2小时的年月日，拼接 T22:00:00Z
                Date promotionStartTime = platformPrice.getPromotionStartTime();
                String promotionStartTimeStr = DateUtil.format(DateUtil.offsetHour(promotionStartTime, -2), "yyyy-MM-dd");
                sale.setStartDate(StrUtil.concat(true, promotionStartTimeStr, "T22:00:00Z"));
                Date promotionEndTime = platformPrice.getPromotionEndTime();
                // 处理促销结束时间，促销结束时间= 数据库时间的年月日，拼接 T21:59:59Z
                String promotionEndTimeStr = DateUtil.format(promotionEndTime, "yyyy-MM-dd");
                sale.setEndDate(StrUtil.concat(true, promotionEndTimeStr, "T21:59:59Z"));
                updatePriceVo.setSale(sale);
            } else {
                updatePriceVo.setSale(null);
            }
            updatePriceVos.add(updatePriceVo);
        }
        return updatePriceVos;
    }

    /**
     * 获取Otto币种
     *
     * @return 币种
     */
    private String getOttoCurrency(String site) {
        McPlatformProperties platformProperties = platformPropertiesMapper.createLambdaQuery().andEq("platform", "OTTO").andEq("site", site).single();
        return platformProperties == null ? null : platformProperties.getCurrencyCode();
    }

    /**
     * 转换时间
     *
     * @param str 时间字符串
     * @return Date
     */
    private Date parseTime(String str) {
        if (StrUtil.isEmpty(str)) {
            return null;
        }
        return DateUtil.parse(str, "yyyy-MM-dd").toJdkDate();
    }

    /**
     * 解析json数据获取建议采购价
     *
     * @param pricing json str
     * @return 建议采购价
     */
    private BigDecimal analysisPricing(String pricing) {
        if (StrUtil.isBlank(pricing)) {
            return null;
        }
        JSONObject jsonObject = JSONUtil.parseObj(pricing);
        if (jsonObject == null) {
            return null;
        }
        JSONObject msrp = jsonObject.getJSONObject("msrp");
        if (msrp == null) {
            return null;
        }
        return msrp.getBigDecimal("amount");
    }


}
