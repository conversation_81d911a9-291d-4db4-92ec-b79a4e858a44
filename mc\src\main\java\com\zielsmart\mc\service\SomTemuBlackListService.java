package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTemuBlackList;
import com.zielsmart.mc.repository.mapper.SomTemuBlackListMapper;
import com.zielsmart.mc.vo.SomTemuBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuBlackListVo;
import com.zielsmart.mc.vo.SomTemuSkuBasicVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuBlackListService {

    @Resource
    private SomTemuBlackListMapper somTemuBlackListMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private SomTemuSkuService somTemuSkuService;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuBlackListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuBlackListVo> queryByPage(SomTemuBlackListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuBlackListVo> pageResult = dynamicSqlManager.getMapper(SomTemuBlackListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuBlackListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somTemuBlackListVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuBlackListVo somTemuBlackListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuBlackListVo) || StrUtil.isBlank(somTemuBlackListVo.getSite())
                || StrUtil.isBlank(somTemuBlackListVo.getAccountId()) || StrUtil.isBlank(somTemuBlackListVo.getSellerSku())
                || StrUtil.isBlank(somTemuBlackListVo.getProductSkuId())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        //根据店铺id 展示码查询Listing
        String accountId = somTemuBlackListVo.getAccountId();
        String site = somTemuBlackListVo.getSite();
        String sellerSku = somTemuBlackListVo.getSellerSku();
        String productSkuId = somTemuBlackListVo.getProductSkuId();
        SomTemuSkuBasicVo somTemuSkuBasicVo = somTemuSkuService.getTemuSku(accountId, site, sellerSku, productSkuId);
        if (ObjectUtil.isEmpty(somTemuSkuBasicVo)) {
            throw new ValidateException("TemuListing中未找到该商品，店铺id+站点+展示码+SKUID不存在，请检查数据");
        }
        LambdaQuery<SomTemuBlackList> somTemuBlackListLambdaQuery = somTemuBlackListMapper.createLambdaQuery()
                .andEq("account_id", somTemuBlackListVo.getAccountId())
                .andEq("site", somTemuBlackListVo.getSite())
                .andEq("product_sku_id", somTemuBlackListVo.getProductSkuId());
        if (!StrUtil.isBlank(somTemuBlackListVo.getAid())) {
            somTemuBlackListLambdaQuery.andNotEq("aid", somTemuBlackListVo.getAid());
        }
        //检测是否已经存在
        long count = somTemuBlackListLambdaQuery.count();
        if (count > 0) {
            throw new ValidateException("您输入的产品在黑名单中已存在，不允许重复维护");
        }
        if (StrUtil.isBlank(somTemuBlackListVo.getAid())) {
            //插入
            somTemuBlackListVo.setProductSkuId(somTemuSkuBasicVo.getProductSkuId());
            somTemuBlackListVo.setCreateName(tokenUser.getUserName());
            somTemuBlackListVo.setCreateTime(DateTime.now().toJdkDate());
            somTemuBlackListVo.setCreateNum(tokenUser.getJobNumber());
            somTemuBlackListVo.setAid(IdUtil.fastSimpleUUID());
            somTemuBlackListMapper.insert(ConvertUtils.beanConvert(somTemuBlackListVo, SomTemuBlackList.class));
        }else{
            //更新
            somTemuBlackListVo.setProductSkuId(somTemuSkuBasicVo.getProductSkuId());
            somTemuBlackListVo.setCreateName(tokenUser.getUserName());
            somTemuBlackListVo.setCreateTime(DateTime.now().toJdkDate());
            somTemuBlackListVo.setCreateNum(tokenUser.getJobNumber());
            somTemuBlackListMapper.createLambdaQuery().andEq("aid",somTemuBlackListVo.getAid()).updateSelective(ConvertUtils.beanConvert(somTemuBlackListVo, SomTemuBlackList.class));
        }

    }


    /**
     * delete
     * 删除
     * @param somTemuBlackListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuBlackListVo somTemuBlackListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuBlackListVo) || StrUtil.isEmpty(somTemuBlackListVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTemuBlackListMapper.createLambdaQuery().andEq("aid",somTemuBlackListVo.getAid()).delete();
    }

    public String export(SomTemuBlackListPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomTemuBlackListVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "Temu库存推送黑名单管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomTemuBlackListVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }

    public void importExcel(List<SomTemuBlackListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //去重
        list = list.stream().distinct().collect(java.util.stream.Collectors.toList());
        if (list.isEmpty()) {
            return;
        }
        //判断在黑名单中是否已经存在
        List<SomTemuBlackList> allList = somTemuBlackListMapper.all();
        Map<String, SomTemuBlackList> allBlackMap = allList.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), Function.identity(), (x1, x2) -> x1));
        //查询全部 temu listing
        //不确定上传的数量，使用账号进行查询
        List<String> accountIds = list.stream().map(SomTemuBlackListVo::getAccountId).distinct().collect(Collectors.toList());
        List<SomTemuSkuBasicVo> skuBasicVos = somTemuSkuService.getTemuSkuByAccountIds(accountIds);
        //校验在temu listing中是否存在
        Map<String, SomTemuSkuBasicVo> temuMap = skuBasicVos.stream().collect(Collectors.toMap(x -> x.getAccountId() +x.getProductSkuId()+ x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        String errorMsg = list.stream().filter(x -> !temuMap.containsKey(x.getAccountId() +x.getProductSkuId()+ x.getSellerSku()))
                .map(x->"站点:"+x.getSite()+ " 展示码:"+x.getSellerSku())
                .collect(Collectors.joining("\n"));
        if (StrUtil.isNotBlank(errorMsg)) {
            throw new ValidateException(errorMsg + "\n在系统中不存在，请检查数据");
        }
        List<SomTemuBlackListVo> insertList = new ArrayList<>();
        for (SomTemuBlackListVo listVo : list) {
            if (allBlackMap.containsKey(listVo.getAccountId() + listVo.getProductSkuId())) {
                continue;
            }
            SomTemuSkuBasicVo somTemuListing = temuMap.get(listVo.getAccountId() + listVo.getProductSkuId()+ listVo.getSellerSku());
            listVo.setProductSkuId(somTemuListing.getProductSkuId());
            listVo.setCreateName(tokenUser.getUserName());
            listVo.setCreateTime(DateTime.now().toJdkDate());
            listVo.setCreateNum(tokenUser.getJobNumber());
            listVo.setAid(IdUtil.fastSimpleUUID());
            insertList.add(listVo);
        }
        if (!insertList.isEmpty()) {
            somTemuBlackListMapper.insertBatch(ConvertUtils.listConvert(insertList, SomTemuBlackList.class));
        }
    }

    public void batchDelete(List<SomTemuBlackListVo> list) {
        List<SomTemuBlackList> allList = somTemuBlackListMapper.createLambdaQuery().select("aid","account_id","product_sku_id");
        Map<String, String> allBlackMap = allList.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), x->x.getAid(), (x1, x2) -> x1));
        Set<String> aidSet = list.stream().map(x -> allBlackMap.get(x.getAccountId() + x.getProductSkuId())).collect(Collectors.toSet());
        if (!aidSet.isEmpty()) {
            somTemuBlackListMapper.createLambdaQuery().andIn("aid", aidSet).delete();
        }
    }
}
