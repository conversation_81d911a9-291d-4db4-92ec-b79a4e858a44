package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * VC三方平台售价信息表
 * gen by 代码生成器 2024-08-26
 */

@Table(name = "mc.som_amazon_vc_price_comparison")
public class SomAmazonVcPriceComparison implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 三方平台站点
     */
    @Column("site")
    private String site;

    @Column("vc_site")
    private String vcSite;
    /**
     * 三方平台的前台售价
     */
    @Column("threshold_price")
    private BigDecimal thresholdPrice;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 比价类型：10.低价，20.高价
     */
    @Column("comparison_type")
    private Integer comparisonType;
    /**
     * 三方平台Listing前台界面地址
     */
    @Column("threshold_link_url")
    private String thresholdLinkUrl;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * 状态：0.删除，1.正常
     */
    @Column("status")
    private Integer status;
    /**
     * 三方平台站点
     */
    @Column("price_aid")
    private String priceAid;

    public SomAmazonVcPriceComparison() {
    }


    public String getVcSite() {
        return vcSite;
    }

    public void setVcSite(String vcSite) {
        this.vcSite = vcSite;
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 三方平台站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 三方平台站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 三方平台的前台售价
     *
     * @return
     */
    public BigDecimal getThresholdPrice() {
        return thresholdPrice;
    }

    /**
     * 三方平台的前台售价
     *
     * @param thresholdPrice
     */
    public void setThresholdPrice(BigDecimal thresholdPrice) {
        this.thresholdPrice = thresholdPrice;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 比价类型：10.低价，20.高价
     *
     * @return
     */
    public Integer getComparisonType() {
        return comparisonType;
    }

    /**
     * 比价类型：10.低价，20.高价
     *
     * @param comparisonType
     */
    public void setComparisonType(Integer comparisonType) {
        this.comparisonType = comparisonType;
    }

    /**
     * 三方平台Listing前台界面地址
     *
     * @return
     */
    public String getThresholdLinkUrl() {
        return thresholdLinkUrl;
    }

    /**
     * 三方平台Listing前台界面地址
     *
     * @param thresholdLinkUrl
     */
    public void setThresholdLinkUrl(String thresholdLinkUrl) {
        this.thresholdLinkUrl = thresholdLinkUrl;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * SKU
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 状态：0.删除，1.正常
     *
     * @return
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态：0.删除，1.正常
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPriceAid() {
        return priceAid;
    }

    public void setPriceAid(String priceAid) {
        this.priceAid = priceAid;
    }

}
