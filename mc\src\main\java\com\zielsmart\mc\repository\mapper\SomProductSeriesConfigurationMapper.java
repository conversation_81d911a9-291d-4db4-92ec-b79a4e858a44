package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomProductSeriesConfigurationPageSearchVo;
import com.zielsmart.mc.vo.SomProductSeriesConfigurationVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-04-11
 */

@SqlResource("somProductSeriesConfiguration")
public interface SomProductSeriesConfigurationMapper extends BaseMapper<SomProductSeriesConfiguration> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageResult< SomProductSeriesConfigurationVo>}
     * <AUTHOR>
     * @history
     */
    List<SomProductSeriesConfigurationVo> queryByPage(@Param("searchVo") SomProductSeriesConfigurationPageSearchVo searchVo);

    /**
     * querySeriesList
     * 查询产品系列下拉选集合
     *
     * @return {@link java.util.List<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    List<String> querySeriesList(@Param("styleName") String styleName);
}
