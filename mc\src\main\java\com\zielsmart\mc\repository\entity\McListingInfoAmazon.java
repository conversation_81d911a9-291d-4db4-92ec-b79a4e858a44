package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* Amazon listing信息表
* gen by 代码生成器 2021-07-08
*/

@Table(name="mc.mc_listing_info_amazon")
public class McListingInfoAmazon implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 可用库存
	 */
	@Column("available_quantity")
	private Integer availableQuantity ;
	/**
	 * 待入库库存
	 */
	@Column("inbound_quantity")
	private Integer inboundQuantity ;

	/**
	 * 订单占用数量
	 */
	@Column("pending_customer_order_quantity")
	private Integer pendingCustomerOrderQuantity ;
	/**
	 * 正在调拨数量
	 */
	@Column("pending_transshipment_quantity")
	private Integer pendingTransshipmentQuantity ;
	/**
	 * 由于另外的原因搁置的数量
	 */
	@Column("fc_processing_quantity")
	private Integer fcProcessingQuantity ;
	/**
	 * 预留库存
	 */
	@Column("reserved_quantity")
	private Integer reservedQuantity ;
	/**
	 * 标准价/划线价
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 最低价
	 */
	@Column("minimum_price")
	private BigDecimal minimumPrice ;
	/**
	 * 最高价
	 */
	@Column("maximum_price")
	private BigDecimal maximumPrice ;
	/**
	 * B2B价格
	 */
	@Column("business_price")
	private BigDecimal businessPrice ;
	/**
	 * B2B价格类型 present/fixed
	 */
	@Column("business_price_type")
	private String businessPriceType ;
	/**
	 * 数量1
	 */
	@Column("quantity_lower_bound_1")
	private Integer quantityLowerBound1 ;
	/**
	 * 折扣或价格1
	 */
	@Column("quantity_price_1")
	private BigDecimal quantityPrice1 ;
	/**
	 * 数量2
	 */
	@Column("quantity_lower_bound_2")
	private Integer quantityLowerBound2 ;
	/**
	 * 折扣或价格2
	 */
	@Column("quantity_price_2")
	private BigDecimal quantityPrice2 ;
	/**
	 * 数量3
	 */
	@Column("quantity_lower_bound_3")
	private Integer quantityLowerBound3 ;
	/**
	 * 折扣或价格3
	 */
	@Column("quantity_price_3")
	private BigDecimal quantityPrice3 ;
	/**
	 * 数量4
	 */
	@Column("quantity_lower_bound_4")
	private Integer quantityLowerBound4 ;
	/**
	 * 折扣或价格4
	 */
	@Column("quantity_price_4")
	private BigDecimal quantityPrice4 ;
	/**
	 * 数量5
	 */
	@Column("quantity_lower_bound_5")
	private Integer quantityLowerBound5 ;
	/**
	 * 折扣或价格5
	 */
	@Column("quantity_price_5")
	private BigDecimal quantityPrice5 ;
	/**
	 * 促销价
	 */
	@Column("sale_price")
	private BigDecimal salePrice ;
	/**
	 * 促销起始时间
	 */
	@Column("sale_date_start")
	private Date saleDateStart ;
	/**
	 * 促销截止时间
	 */
	@Column("sale_date_end")
	private Date saleDateEnd ;
	/**
	 * 渠道 FBA/FBM
	 */
	@Column("fulfillment_channel")
	private String fulfillmentChannel ;
	/**
	 * EAN/UPC/ASIN
	 */
	@Column("product_id")
	private String productId ;
	/**
	 * 父级SKU
	 */
	@Column("parent_seller_sku")
	private String parentSellerSku ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 平台SKU
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 公司内部SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 平台产品唯一标识
	 */
	@Column("asin_code")
	private String asinCode ;
	/**
	 * 产品名称
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 产品图片地址
	 */
	@Column("image_url")
	private String imageUrl ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 产品状态
	 */
	@Column("status_code")
	private String statusCode ;
	/**
	 * 币种
	 */
	@Column("currency_code")
	private String currencyCode ;
	/**
	 * 11=New；1=Used - Like New；2=Used - Very Good；3=Used - Good；4=Used - Acceptable；0＝无状态，默认为空即可；500＝综合单的父体，无状态，默认为空即可
	 */
	@Column("item_condition")
	private String itemCondition ;
	/**
	 * 运费模板名称
	 */
	@Column("merchant_shipping_group")
	private String merchantShippingGroup ;
	/**
	 * fnsku 
	 */
	@Column("fnsku")
	private String fnsku ;
	/**
	 * 可以打包发货的库存数量
	 */
	@Column("fulfillable_quantity")
	private Integer fulfillableQuantity ;
	/**
	 * 已通知Amazon仓库要入库的数量
	 */
	@Column("inbound_working_quantity")
	private Integer inboundWorkingQuantity ;
	/**
	 * 已通知Amazon仓库要入库并提供了追踪单号的数量
	 */
	@Column("inbound_shipped_quantity")
	private Integer inboundShippedQuantity ;
	/**
	 * 正在处理接收的数量
	 */
	@Column("inbound_receiving_quantity")
	private Integer inboundReceivingQuantity ;
	/**
	 * 入库的总数
	 */
	@Column("total_reserved_quantity")
	private Integer totalReservedQuantity ;
	/**
	 * 目前正在调查中的总数量
	 */
	@Column("total_researching_quantity")
	private Integer totalResearchingQuantity ;
	/**
	 * 无法履行的总数
	 */
	@Column("total_unfulfillable_quantity")
	private Integer totalUnfulfillableQuantity ;
	/**
	 * 在客户损坏的总数量
	 */
	@Column("customer_damaged_quantity")
	private Integer customerDamagedQuantity ;
	/**
	 * 仓库中损坏总数量
	 */
	@Column("warehouse_damaged_quantity")
	private Integer warehouseDamagedQuantity ;
	/**
	 * 分发时损坏的总数量
	 */
	@Column("distributor_damaged_quantity")
	private Integer distributorDamagedQuantity ;
	/**
	 * 承运过程中损坏的数量
	 */
	@Column("carrier_damaged_quantity")
	private Integer carrierDamagedQuantity ;
	/**
	 * 有缺陷的产品的数量
	 */
	@Column("defective_quantity")
	private Integer defectiveQuantity ;
	/**
	 * 过期的数量
	 */
	@Column("expired_quantity")
	private Integer expiredQuantity ;
	/**
	 * amazon仓库的总库存数
	 */
	@Column("total_quantity")
	private Integer totalQuantity ;
	/**
	 * 积分(日本站点)
	 */
	@Column("point")
	private Integer point ;
	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 可用库存
	*@return
	*/
	public Integer getAvailableQuantity(){
		return  availableQuantity;
	}
	/**
	* 可用库存
	*@param  availableQuantity
	*/
	public void setAvailableQuantity(Integer availableQuantity ){
		this.availableQuantity = availableQuantity;
	}
	/**
	* 待入库库存
	*@return
	*/
	public Integer getInboundQuantity(){
		return  inboundQuantity;
	}
	/**
	* 待入库库存
	*@param  inboundQuantity
	*/
	public void setInboundQuantity(Integer inboundQuantity ){
		this.inboundQuantity = inboundQuantity;
	}

	/**
	* 订单占用数量
	*@return
	*/
	public Integer getPendingCustomerOrderQuantity(){
		return  pendingCustomerOrderQuantity;
	}
	/**
	* 订单占用数量
	*@param  pendingCustomerOrderQuantity
	*/
	public void setPendingCustomerOrderQuantity(Integer pendingCustomerOrderQuantity ){
		this.pendingCustomerOrderQuantity = pendingCustomerOrderQuantity;
	}
	/**
	* 正在调拨数量
	*@return
	*/
	public Integer getPendingTransshipmentQuantity(){
		return  pendingTransshipmentQuantity;
	}
	/**
	* 正在调拨数量
	*@param  pendingTransshipmentQuantity
	*/
	public void setPendingTransshipmentQuantity(Integer pendingTransshipmentQuantity ){
		this.pendingTransshipmentQuantity = pendingTransshipmentQuantity;
	}
	/**
	* 由于另外的原因搁置的数量
	*@return
	*/
	public Integer getfcProcessingQuantity(){
		return  fcProcessingQuantity;
	}
	/**
	* 由于另外的原因搁置的数量
	*@param  fcProcessingQuantity
	*/
	public void setfcProcessingQuantity(Integer fcProcessingQuantity ){
		this.fcProcessingQuantity = fcProcessingQuantity;
	}
	/**
	* 预留库存
	*@return
	*/
	public Integer getReservedQuantity(){
		return  reservedQuantity;
	}
	/**
	* 预留库存
	*@param  reservedQuantity
	*/
	public void setReservedQuantity(Integer reservedQuantity ){
		this.reservedQuantity = reservedQuantity;
	}
	/**
	* 标准价/划线价
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 标准价/划线价
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 最低价
	*@return
	*/
	public BigDecimal getMinimumPrice(){
		return  minimumPrice;
	}
	/**
	* 最低价
	*@param  minimumPrice
	*/
	public void setMinimumPrice(BigDecimal minimumPrice ){
		this.minimumPrice = minimumPrice;
	}
	/**
	* 最高价
	*@return
	*/
	public BigDecimal getMaximumPrice(){
		return  maximumPrice;
	}
	/**
	* 最高价
	*@param  maximumPrice
	*/
	public void setMaximumPrice(BigDecimal maximumPrice ){
		this.maximumPrice = maximumPrice;
	}
	/**
	* B2B价格
	*@return
	*/
	public BigDecimal getBusinessPrice(){
		return  businessPrice;
	}
	/**
	* B2B价格
	*@param  businessPrice
	*/
	public void setBusinessPrice(BigDecimal businessPrice ){
		this.businessPrice = businessPrice;
	}
	/**
	* B2B价格类型 present/fixed
	*@return
	*/
	public String getBusinessPriceType(){
		return  businessPriceType;
	}
	/**
	* B2B价格类型 present/fixed
	*@param  businessPriceType
	*/
	public void setBusinessPriceType(String businessPriceType ){
		this.businessPriceType = businessPriceType;
	}
	/**
	* 数量1
	*@return
	*/
	public Integer getQuantityLowerBound1(){
		return  quantityLowerBound1;
	}
	/**
	* 数量1
	*@param  quantityLowerBound1
	*/
	public void setQuantityLowerBound1(Integer quantityLowerBound1 ){
		this.quantityLowerBound1 = quantityLowerBound1;
	}
	/**
	* 折扣或价格1
	*@return
	*/
	public BigDecimal getQuantityPrice1(){
		return  quantityPrice1;
	}
	/**
	* 折扣或价格1
	*@param  quantityPrice1
	*/
	public void setQuantityPrice1(BigDecimal quantityPrice1 ){
		this.quantityPrice1 = quantityPrice1;
	}
	/**
	* 数量2
	*@return
	*/
	public Integer getQuantityLowerBound2(){
		return  quantityLowerBound2;
	}
	/**
	* 数量2
	*@param  quantityLowerBound2
	*/
	public void setQuantityLowerBound2(Integer quantityLowerBound2 ){
		this.quantityLowerBound2 = quantityLowerBound2;
	}
	/**
	* 折扣或价格2
	*@return
	*/
	public BigDecimal getQuantityPrice2(){
		return  quantityPrice2;
	}
	/**
	* 折扣或价格2
	*@param  quantityPrice2
	*/
	public void setQuantityPrice2(BigDecimal quantityPrice2 ){
		this.quantityPrice2 = quantityPrice2;
	}
	/**
	* 数量3
	*@return
	*/
	public Integer getQuantityLowerBound3(){
		return  quantityLowerBound3;
	}
	/**
	* 数量3
	*@param  quantityLowerBound3
	*/
	public void setQuantityLowerBound3(Integer quantityLowerBound3 ){
		this.quantityLowerBound3 = quantityLowerBound3;
	}
	/**
	* 折扣或价格3
	*@return
	*/
	public BigDecimal getQuantityPrice3(){
		return  quantityPrice3;
	}
	/**
	* 折扣或价格3
	*@param  quantityPrice3
	*/
	public void setQuantityPrice3(BigDecimal quantityPrice3 ){
		this.quantityPrice3 = quantityPrice3;
	}
	/**
	* 数量4
	*@return
	*/
	public Integer getQuantityLowerBound4(){
		return  quantityLowerBound4;
	}
	/**
	* 数量4
	*@param  quantityLowerBound4
	*/
	public void setQuantityLowerBound4(Integer quantityLowerBound4 ){
		this.quantityLowerBound4 = quantityLowerBound4;
	}
	/**
	* 折扣或价格4
	*@return
	*/
	public BigDecimal getQuantityPrice4(){
		return  quantityPrice4;
	}
	/**
	* 折扣或价格4
	*@param  quantityPrice4
	*/
	public void setQuantityPrice4(BigDecimal quantityPrice4 ){
		this.quantityPrice4 = quantityPrice4;
	}
	/**
	* 数量5
	*@return
	*/
	public Integer getQuantityLowerBound5(){
		return  quantityLowerBound5;
	}
	/**
	* 数量5
	*@param  quantityLowerBound5
	*/
	public void setQuantityLowerBound5(Integer quantityLowerBound5 ){
		this.quantityLowerBound5 = quantityLowerBound5;
	}
	/**
	* 折扣或价格5
	*@return
	*/
	public BigDecimal getQuantityPrice5(){
		return  quantityPrice5;
	}
	/**
	* 折扣或价格5
	*@param  quantityPrice5
	*/
	public void setQuantityPrice5(BigDecimal quantityPrice5 ){
		this.quantityPrice5 = quantityPrice5;
	}
	/**
	* 促销价
	*@return
	*/
	public BigDecimal getSalePrice(){
		return  salePrice;
	}
	/**
	* 促销价
	*@param  salePrice
	*/
	public void setSalePrice(BigDecimal salePrice ){
		this.salePrice = salePrice;
	}
	/**
	* 促销起始时间
	*@return
	*/
	public Date getSaleDateStart(){
		return  saleDateStart;
	}
	/**
	* 促销起始时间
	*@param  saleDateStart
	*/
	public void setSaleDateStart(Date saleDateStart ){
		this.saleDateStart = saleDateStart;
	}
	/**
	* 促销截止时间
	*@return
	*/
	public Date getSaleDateEnd(){
		return  saleDateEnd;
	}
	/**
	* 促销截止时间
	*@param  saleDateEnd
	*/
	public void setSaleDateEnd(Date saleDateEnd ){
		this.saleDateEnd = saleDateEnd;
	}
	/**
	* 渠道 FBA/FBM
	*@return
	*/
	public String getFulfillmentChannel(){
		return  fulfillmentChannel;
	}
	/**
	* 渠道 FBA/FBM
	*@param  fulfillmentChannel
	*/
	public void setFulfillmentChannel(String fulfillmentChannel ){
		this.fulfillmentChannel = fulfillmentChannel;
	}
	/**
	* EAN/UPC/ASIN
	*@return
	*/
	public String getProductId(){
		return  productId;
	}
	/**
	* EAN/UPC/ASIN
	*@param  productId
	*/
	public void setProductId(String productId ){
		this.productId = productId;
	}
	/**
	* 父级SKU
	*@return
	*/
	public String getParentSellerSku(){
		return  parentSellerSku;
	}
	/**
	* 父级SKU
	*@param  parentSellerSku
	*/
	public void setParentSellerSku(String parentSellerSku ){
		this.parentSellerSku = parentSellerSku;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 平台SKU
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 平台SKU
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 公司内部SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* 公司内部SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 平台产品唯一标识
	*@return
	*/
	public String getAsinCode(){
		return  asinCode;
	}
	/**
	* 平台产品唯一标识
	*@param  asinCode
	*/
	public void setAsinCode(String asinCode ){
		this.asinCode = asinCode;
	}
	/**
	* 产品名称
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 产品名称
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 产品图片地址
	*@return
	*/
	public String getImageUrl(){
		return  imageUrl;
	}
	/**
	* 产品图片地址
	*@param  imageUrl
	*/
	public void setImageUrl(String imageUrl ){
		this.imageUrl = imageUrl;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}
	/**
	* 产品状态
	*@return
	*/
	public String getStatusCode(){
		return  statusCode;
	}
	/**
	* 产品状态
	*@param  statusCode
	*/
	public void setStatusCode(String statusCode ){
		this.statusCode = statusCode;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrencyCode(){
		return  currencyCode;
	}
	/**
	* 币种
	*@param  currencyCode
	*/
	public void setCurrencyCode(String currencyCode ){
		this.currencyCode = currencyCode;
	}
	/**
	* 11=New；1=Used - Like New；2=Used - Very Good；3=Used - Good；4=Used - Acceptable；0＝无状态，默认为空即可；500＝综合单的父体，无状态，默认为空即可
	*@return
	*/
	public String getItemCondition(){
		return  itemCondition;
	}
	/**
	* 11=New；1=Used - Like New；2=Used - Very Good；3=Used - Good；4=Used - Acceptable；0＝无状态，默认为空即可；500＝综合单的父体，无状态，默认为空即可
	*@param  itemCondition
	*/
	public void setItemCondition(String itemCondition ){
		this.itemCondition = itemCondition;
	}
	/**
	* 运费模板名称
	*@return
	*/
	public String getMerchantShippingGroup(){
		return  merchantShippingGroup;
	}
	/**
	* 运费模板名称
	*@param  merchantShippingGroup
	*/
	public void setMerchantShippingGroup(String merchantShippingGroup ){
		this.merchantShippingGroup = merchantShippingGroup;
	}
	/**
	* fnsku 
	*@return
	*/
	public String getFnsku(){
		return  fnsku;
	}
	/**
	* fnsku 
	*@param  fnsku
	*/
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	/**
	* 可以打包发货的库存数量
	*@return
	*/
	public Integer getFulfillableQuantity(){
		return  fulfillableQuantity;
	}
	/**
	* 可以打包发货的库存数量
	*@param  fulfillableQuantity
	*/
	public void setFulfillableQuantity(Integer fulfillableQuantity ){
		this.fulfillableQuantity = fulfillableQuantity;
	}
	/**
	* 已通知Amazon仓库要入库的数量
	*@return
	*/
	public Integer getInboundWorkingQuantity(){
		return  inboundWorkingQuantity;
	}
	/**
	* 已通知Amazon仓库要入库的数量
	*@param  inboundWorkingQuantity
	*/
	public void setInboundWorkingQuantity(Integer inboundWorkingQuantity ){
		this.inboundWorkingQuantity = inboundWorkingQuantity;
	}
	/**
	* 已通知Amazon仓库要入库并提供了追踪单号的数量
	*@return
	*/
	public Integer getInboundShippedQuantity(){
		return  inboundShippedQuantity;
	}
	/**
	* 已通知Amazon仓库要入库并提供了追踪单号的数量
	*@param  inboundShippedQuantity
	*/
	public void setInboundShippedQuantity(Integer inboundShippedQuantity ){
		this.inboundShippedQuantity = inboundShippedQuantity;
	}
	/**
	* 正在处理接收的数量
	*@return
	*/
	public Integer getInboundReceivingQuantity(){
		return  inboundReceivingQuantity;
	}
	/**
	* 正在处理接收的数量
	*@param  inboundReceivingQuantity
	*/
	public void setInboundReceivingQuantity(Integer inboundReceivingQuantity ){
		this.inboundReceivingQuantity = inboundReceivingQuantity;
	}
	/**
	* 入库的总数
	*@return
	*/
	public Integer getTotalReservedQuantity(){
		return  totalReservedQuantity;
	}
	/**
	* 入库的总数
	*@param  totalReservedQuantity
	*/
	public void setTotalReservedQuantity(Integer totalReservedQuantity ){
		this.totalReservedQuantity = totalReservedQuantity;
	}
	/**
	* 目前正在调查中的总数量
	*@return
	*/
	public Integer getTotalResearchingQuantity(){
		return  totalResearchingQuantity;
	}
	/**
	* 目前正在调查中的总数量
	*@param  totalResearchingQuantity
	*/
	public void setTotalResearchingQuantity(Integer totalResearchingQuantity ){
		this.totalResearchingQuantity = totalResearchingQuantity;
	}
	/**
	* 无法履行的总数
	*@return
	*/
	public Integer getTotalUnfulfillableQuantity(){
		return  totalUnfulfillableQuantity;
	}
	/**
	* 无法履行的总数
	*@param  totalUnfulfillableQuantity
	*/
	public void setTotalUnfulfillableQuantity(Integer totalUnfulfillableQuantity ){
		this.totalUnfulfillableQuantity = totalUnfulfillableQuantity;
	}
	/**
	* 在客户损坏的总数量
	*@return
	*/
	public Integer getCustomerDamagedQuantity(){
		return  customerDamagedQuantity;
	}
	/**
	* 在客户损坏的总数量
	*@param  customerDamagedQuantity
	*/
	public void setCustomerDamagedQuantity(Integer customerDamagedQuantity ){
		this.customerDamagedQuantity = customerDamagedQuantity;
	}
	/**
	* 仓库中损坏总数量
	*@return
	*/
	public Integer getWarehouseDamagedQuantity(){
		return  warehouseDamagedQuantity;
	}
	/**
	* 仓库中损坏总数量
	*@param  warehouseDamagedQuantity
	*/
	public void setWarehouseDamagedQuantity(Integer warehouseDamagedQuantity ){
		this.warehouseDamagedQuantity = warehouseDamagedQuantity;
	}
	/**
	* 分发时损坏的总数量
	*@return
	*/
	public Integer getDistributorDamagedQuantity(){
		return  distributorDamagedQuantity;
	}
	/**
	* 分发时损坏的总数量
	*@param  distributorDamagedQuantity
	*/
	public void setDistributorDamagedQuantity(Integer distributorDamagedQuantity ){
		this.distributorDamagedQuantity = distributorDamagedQuantity;
	}
	/**
	* 承运过程中损坏的数量
	*@return
	*/
	public Integer getCarrierDamagedQuantity(){
		return  carrierDamagedQuantity;
	}
	/**
	* 承运过程中损坏的数量
	*@param  carrierDamagedQuantity
	*/
	public void setCarrierDamagedQuantity(Integer carrierDamagedQuantity ){
		this.carrierDamagedQuantity = carrierDamagedQuantity;
	}
	/**
	* 有缺陷的产品的数量
	*@return
	*/
	public Integer getDefectiveQuantity(){
		return  defectiveQuantity;
	}
	/**
	* 有缺陷的产品的数量
	*@param  defectiveQuantity
	*/
	public void setDefectiveQuantity(Integer defectiveQuantity ){
		this.defectiveQuantity = defectiveQuantity;
	}
	/**
	* 过期的数量
	*@return
	*/
	public Integer getExpiredQuantity(){
		return  expiredQuantity;
	}
	/**
	* 过期的数量
	*@param  expiredQuantity
	*/
	public void setExpiredQuantity(Integer expiredQuantity ){
		this.expiredQuantity = expiredQuantity;
	}
	/**
	* amazon仓库的总库存数
	*@return
	*/
	public Integer getTotalQuantity(){
		return  totalQuantity;
	}
	/**
	* amazon仓库的总库存数
	*@param  totalQuantity
	*/
	public void setTotalQuantity(Integer totalQuantity ){
		this.totalQuantity = totalQuantity;
	}

	public Integer getPoint() {
		return point;
	}

	public void setPoint(Integer point) {
		this.point = point;
	}
}
