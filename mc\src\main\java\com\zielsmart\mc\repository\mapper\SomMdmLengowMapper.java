package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomMdmLengowPageSearchVo;
import com.zielsmart.mc.vo.SomMdmLengowVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.HashMap;
import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-09-09
*/

@SqlResource("somMdmLengow")
public interface SomMdmLengowMapper extends BaseMapper<SomMdmLengow> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomMdmLengowVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<HashMap<String, Object>>  queryByPage(@Param("searchVo")SomMdmLengowPageSearchVo searchVo, PageRequest pageRequest);

    default void batchUpdate(List<SomMdmLengow> updateList){
        this.getSQLManager().updateBatch(SqlId.of("somMdmLengow.batchUdate"), updateList);
    }
}
