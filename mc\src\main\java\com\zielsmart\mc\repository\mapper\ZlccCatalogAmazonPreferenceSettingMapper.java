package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingPageSearchVo;
import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2023-12-01
 */

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title ZlccCatalogAmazonPreferenceSettingMapper
 * @description
 * @date 2023-12-01 10:50:35
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("zlccCatalogAmazonPreferenceSetting")
public interface ZlccCatalogAmazonPreferenceSettingMapper extends BaseMapper<ZlccCatalogAmazonPreferenceSetting> {
    /**
     * queryByPage
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< ZlccCatalogAmazonPreferenceSettingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<ZlccCatalogAmazonPreferenceSettingVo> queryByPage(@Param("searchVo") ZlccCatalogAmazonPreferenceSettingPageSearchVo searchVo, PageRequest pageRequest);
}
