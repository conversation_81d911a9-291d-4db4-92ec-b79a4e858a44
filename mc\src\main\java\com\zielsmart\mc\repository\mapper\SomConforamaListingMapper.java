package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomConforamaListing;
import com.zielsmart.mc.vo.SomConforamaListingPageSearchVo;
import com.zielsmart.mc.vo.SomConforamaListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-01-11
 */

@SqlResource("somConforamaListing")
public interface SomConforamaListingMapper extends BaseMapper<SomConforamaListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomConforamaListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomConforamaListingVo> queryByPage(@Param("searchVo") SomConforamaListingPageSearchVo searchVo, PageRequest pageRequest);
}
