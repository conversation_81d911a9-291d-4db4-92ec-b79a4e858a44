package com.zielsmart.mc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @version V2.0
 * @title: EventBusConfig
 * @package: com.zielsmart.mc.config
 * @description: 事件处理 并开启异步
 * @author: 李耀华
 * @date: 2021-04-2210:12
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
@EnableAsync
public class EventBusConfig {

    private static final int corePoolSize = 3;       		// 核心线程数（默认线程数）
    private static final int maxPoolSize = 300;			    // 最大线程数
    private static final int keepAliveTime = 10;			// 允许线程空闲时间（单位：默认为秒）
    private static final int queueCapacity = 200;			// 缓冲队列数
    private static final String threadNamePrefix = "eventbus-async-"; // 线程池名前缀
    /**
     * 自定义异步处理的线程池
     * 默认情况下，Spring将搜索相关的线程池定义：
     * 要么在上下文中搜索唯一的TaskExecutor bean，
     * 要么搜索名为“taskExecutor”的Executor bean。
     * 如果两者都无法解析，则将使用SimpleAsyncTaskExecutor来处理异步方法调用
     * @return
     */
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveTime);
        executor.setThreadNamePrefix(threadNamePrefix);

        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor;
    }
}
