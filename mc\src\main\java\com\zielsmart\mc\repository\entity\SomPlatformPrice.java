package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 平台价格维护表
 * gen by 代码生成器 2022-02-22
 */

@Table(name = "mc.som_platform_price")
public class SomPlatformPrice implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 售价日期
     */
    @Column("import_date")
    private Date importDate;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 发货方式;自发,寄售
     */
    @Column("fulfillment_channel")
    private String fulfillmentChannel;
    /**
     * 售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 国家
     */
    @Column("country")
    private String country;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    @Column("freight")
    private BigDecimal freight;

    public SomPlatformPrice() {
    }


    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 售价日期
     *
     * @return
     */
    public Date getImportDate() {
        return importDate;
    }

    /**
     * 售价日期
     *
     * @param importDate
     */
    public void setImportDate(Date importDate) {
        this.importDate = importDate;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getFulfillmentChannel() {
        return fulfillmentChannel;
    }

    public void setFulfillmentChannel(String fulfillmentChannel) {
        this.fulfillmentChannel = fulfillmentChannel;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 国家
     *
     * @return
     */
    public String getCountry() {
        return country;
    }

    /**
     * 国家
     *
     * @param country
     */
    public void setCountry(String country) {
        this.country = country;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
