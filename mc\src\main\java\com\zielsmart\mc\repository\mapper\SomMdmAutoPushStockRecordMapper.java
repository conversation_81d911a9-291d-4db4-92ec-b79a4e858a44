package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomMdmAutoPushStockRecord;
import com.zielsmart.mc.vo.SomMdmAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomMdmAutoPushStockRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-09
*/

@SqlResource("somMdmAutoPushStockRecord")
public interface SomMdmAutoPushStockRecordMapper extends BaseMapper<SomMdmAutoPushStockRecord> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomMdmAutoPushStockRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMdmAutoPushStockRecordVo> queryByPage(@Param("searchVo") SomMdmAutoPushStockRecordPageSearchVo searchVo, PageRequest pageRequest);

    List<SomMdmAutoPushStockRecordVo> exportExcel(@Param("searchVo")SomMdmAutoPushStockRecordPageSearchVo searchVo);

}
