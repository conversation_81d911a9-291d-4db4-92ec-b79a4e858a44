package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTargetWarehouseVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-12-06
*/

@SqlResource("somTargetWarehouse")
public interface SomTargetWarehouseMapper extends BaseMapper<SomTargetWarehouse> {

    /**
     * 获取平台仓库列表
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomTargetWarehouseVo>}
     * <AUTHOR>
     * @history
     */
    List<SomTargetWarehouseVo> queryList();
}
