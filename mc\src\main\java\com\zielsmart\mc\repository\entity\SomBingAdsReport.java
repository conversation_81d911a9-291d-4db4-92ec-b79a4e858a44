package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-05-29
*/

@Table(name="mc.som_bing_ads_report")
public class SomBingAdsReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 账号ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 账号编码
	 */
	@Column("account_number")
	private String accountNumber ;
	/**
	 * 账号名称
	 */
	@Column("account_name")
	private String accountName ;
	/**
	 * 时间
	 */
	@Column("time_period")
	private Date timePeriod ;
	/**
	 * 广告系列ID
	 */
	@Column("campaigns_id")
	private String campaignsId ;
	/**
	 * 广告系列名称
	 */
	@Column("campaign_name")
	private String campaignName ;
	/**
	 * 广告系列状态
	 */
	@Column("campaign_status")
	private String campaignStatus ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 广告类型
	 */
	@Column("ad_distribution")
	private String adDistribution ;
	/**
	 * 查看次数
	 */
	@Column("impressions")
	private Integer impressions ;
	/**
	 * 点击次数
	 */
	@Column("clicks")
	private Integer clicks ;
	/**
	 * 点击率
	 */
	@Column("ctr")
	private BigDecimal ctr ;
	/**
	 * 平均用户获得成本
	 */
	@Column("average_cpc")
	private BigDecimal averageCpc ;
	/**
	 * 花费
	 */
	@Column("spend")
	private BigDecimal spend ;
	/**
	 * 转化量
	 */
	@Column("conversions")
	private Integer conversions ;
	/**
	 * 转化率
	 */
	@Column("conversion_rate")
	private BigDecimal conversionRate ;
	/**
	 * 每次转化的费用
	 */
	@Column("cost_per_conversion")
	private BigDecimal costPerConversion ;
	/**
	 * 收益
	 */
	@Column("revenue")
	private BigDecimal revenue ;
	/**
	 * 退回的花费
	 */
	@Column("return_on_ad_spend")
	private BigDecimal returnOnAdSpend ;
	/**
	 * 总转化数量
	 */
	@Column("all_conversions")
	private Integer allConversions ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomBingAdsReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 账号ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 账号ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 账号编码
	*@return
	*/
	public String getAccountNumber(){
		return  accountNumber;
	}
	/**
	* 账号编码
	*@param  accountNumber
	*/
	public void setAccountNumber(String accountNumber ){
		this.accountNumber = accountNumber;
	}
	/**
	* 账号名称
	*@return
	*/
	public String getAccountName(){
		return  accountName;
	}
	/**
	* 账号名称
	*@param  accountName
	*/
	public void setAccountName(String accountName ){
		this.accountName = accountName;
	}
	/**
	* 时间
	*@return
	*/
	public Date getTimePeriod(){
		return  timePeriod;
	}
	/**
	* 时间
	*@param  timePeriod
	*/
	public void setTimePeriod(Date timePeriod ){
		this.timePeriod = timePeriod;
	}
	/**
	* 广告系列ID
	*@return
	*/
	public String getCampaignsId(){
		return  campaignsId;
	}
	/**
	* 广告系列ID
	*@param  campaignsId
	*/
	public void setCampaignsId(String campaignsId ){
		this.campaignsId = campaignsId;
	}
	/**
	* 广告系列名称
	*@return
	*/
	public String getCampaignName(){
		return  campaignName;
	}
	/**
	* 广告系列名称
	*@param  campaignName
	*/
	public void setCampaignName(String campaignName ){
		this.campaignName = campaignName;
	}
	/**
	* 广告系列状态
	*@return
	*/
	public String getCampaignStatus(){
		return  campaignStatus;
	}
	/**
	* 广告系列状态
	*@param  campaignStatus
	*/
	public void setCampaignStatus(String campaignStatus ){
		this.campaignStatus = campaignStatus;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 广告类型
	*@return
	*/
	public String getadDistribution(){
		return  adDistribution;
	}
	/**
	* 广告类型
	*@param  adDistribution
	*/
	public void setadDistribution(String adDistribution ){
		this.adDistribution = adDistribution;
	}
	/**
	* 查看次数
	*@return
	*/
	public Integer getImpressions(){
		return  impressions;
	}
	/**
	* 查看次数
	*@param  impressions
	*/
	public void setImpressions(Integer impressions ){
		this.impressions = impressions;
	}
	/**
	* 点击次数
	*@return
	*/
	public Integer getClicks(){
		return  clicks;
	}
	/**
	* 点击次数
	*@param  clicks
	*/
	public void setClicks(Integer clicks ){
		this.clicks = clicks;
	}
	/**
	* 点击率
	*@return
	*/
	public BigDecimal getCtr(){
		return  ctr;
	}
	/**
	* 点击率
	*@param  ctr
	*/
	public void setCtr(BigDecimal ctr ){
		this.ctr = ctr;
	}
	/**
	* 平均用户获得成本
	*@return
	*/
	public BigDecimal getAverageCpc(){
		return  averageCpc;
	}
	/**
	* 平均用户获得成本
	*@param  averageCpc
	*/
	public void setAverageCpc(BigDecimal averageCpc ){
		this.averageCpc = averageCpc;
	}
	/**
	* 花费
	*@return
	*/
	public BigDecimal getSpend(){
		return  spend;
	}
	/**
	* 花费
	*@param  spend
	*/
	public void setSpend(BigDecimal spend ){
		this.spend = spend;
	}
	/**
	* 转化量
	*@return
	*/
	public Integer getConversions(){
		return  conversions;
	}
	/**
	* 转化量
	*@param  conversions
	*/
	public void setConversions(Integer conversions ){
		this.conversions = conversions;
	}
	/**
	* 转化率
	*@return
	*/
	public BigDecimal getConversionRate(){
		return  conversionRate;
	}
	/**
	* 转化率
	*@param  conversionRate
	*/
	public void setConversionRate(BigDecimal conversionRate ){
		this.conversionRate = conversionRate;
	}
	/**
	* 每次转化的费用
	*@return
	*/
	public BigDecimal getCostPerConversion(){
		return  costPerConversion;
	}
	/**
	* 每次转化的费用
	*@param  costPerConversion
	*/
	public void setCostPerConversion(BigDecimal costPerConversion ){
		this.costPerConversion = costPerConversion;
	}
	/**
	* 收益
	*@return
	*/
	public BigDecimal getRevenue(){
		return  revenue;
	}
	/**
	* 收益
	*@param  revenue
	*/
	public void setRevenue(BigDecimal revenue ){
		this.revenue = revenue;
	}
	/**
	* 退回的花费
	*@return
	*/
	public BigDecimal getReturnOnAdSpend(){
		return  returnOnAdSpend;
	}
	/**
	* 退回的花费
	*@param  returnOnAdSpend
	*/
	public void setReturnOnAdSpend(BigDecimal returnOnAdSpend ){
		this.returnOnAdSpend = returnOnAdSpend;
	}
	/**
	* 总转化数量
	*@return
	*/
	public Integer getAllConversions(){
		return  allConversions;
	}
	/**
	* 总转化数量
	*@param  allConversions
	*/
	public void setAllConversions(Integer allConversions ){
		this.allConversions = allConversions;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
