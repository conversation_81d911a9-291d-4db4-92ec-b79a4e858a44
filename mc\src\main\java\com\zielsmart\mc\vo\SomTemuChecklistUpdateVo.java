package com.zielsmart.mc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version V1.0
 * @title: SomTemuChecklistUpdateVo
 * @package: com.zielsmart.mc.vo
 * @description:
 * @author: lv<PERSON><PERSON><PERSON>
 * @date: 2024-09-05 10:50
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class SomTemuChecklistUpdateVo {
    /**
     * 议价原因
     */
    @Schema(description = "议价原因",name="bargainReason")
    private String bargainReason;
    /**
     * 币种
     */
    @Schema(description = "币种",name="supplierPriceCurrency")
    private String supplierPriceCurrency;
    /**
     * 供货价
     */
    @Schema(description = "供货价",name="supplierPrice")
    private BigDecimal supplierPrice;

    private List<String> aids;
}
