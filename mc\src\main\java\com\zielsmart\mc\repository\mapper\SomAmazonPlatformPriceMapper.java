package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonPlatformPrice;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceEditDetailVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceHistoryVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2023-11-14
 */

@SqlResource("somAmazonPlatformPrice")
public interface SomAmazonPlatformPriceMapper extends BaseMapper<SomAmazonPlatformPrice> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonPlatformPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonPlatformPriceVo> queryByPage(@Param("searchVo") SomAmazonPlatformPricePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 查询历史记录表中的数据的状态，不包括 - 和 草稿
     *
     * @param site
     * @param sellerSku
     * @return
     */
    List<SomAmazonPlatformPriceVo> queryWithStatus(@Param("site") String site, @Param("sellerSku") String sellerSku);

    List<SomAmazonPlatformPriceHistoryVo> queryPushPrice(@Param("aids") List<String> aids);

    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryCouponData(@Param("site") String site, @Param("sellerSku") String sellerSku);

    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryLd7ddData(@Param("site") String site, @Param("sellerSku") String sellerSku);

    /**
     * 审批中+未开始+进行中+审批通过+提报成功+运营已提报+取消中+取消失败
     *
     * @param site
     * @param sellerSku
     * @return
     */
    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryDotdData(@Param("site") String site, @Param("sellerSku") String sellerSku);

    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryPddOdData(@Param("site") String site, @Param("sellerSku") String sellerSku, @Param("type") Integer type);

    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryld7ddDataForSalePrice(@Param("site") String site, @Param("sellerSku") String sellerSku);


    //   查询计算成交价中的  Coupon折扣额
    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryCouponDiscountPrice(@Param("site") String site, @Param("sellerSku") String sellerSku);


    //   查询计算成交价中的  Deal秒杀价
    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryDealPrice(@Param("site") String site, @Param("sellerSku") String sellerSku);

    //   查询计算成交价中的  PDD OutletDeal 秒杀价
    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryDealPriceByPddOutlet(@Param("site") String site, @Param("sellerSku") String sellerSku,@Param("dealType") Integer dealType);

    //   查询计算成交价中的  Deal DOTD 秒杀价
    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryDealPriceByDotd(@Param("site") String site, @Param("sellerSku") String sellerSku);

    //   查询计算成交价中的  Deal LD7DD 秒杀价
    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryDealPriceByld7dd(String site, String sellerSku);

    SomAmazonPlatformPriceEditDetailVo.MarketActivity queryPromotionDiscount(@Param("site") String site, @Param("sellerSku") String sellerSku);

    List<SomAmazonPlatformPriceEditDetailVo.MarketActivity> queryPddOutletDataForSalePrice(@Param("site") String site, @Param("sellerSku") String sellerSku);

    @Update
    void updatePriceStatue(@Param("aids") List<String> aids, @Param("status") String status);

    int countLd7ddMarketType(@Param("site") String site, @Param("sellerSku") String sellerSku, @Param("type") String type);

    int countPddOutLetMarketType(@Param("site") String site, @Param("sellerSku") String sellerSku, @Param("type") String type);

    int countCouponMarketType(@Param("site") String site, @Param("sellerSku") String sellerSku, @Param("type") String type);

    /**
     * 更新价格修改时间
     * @param site
     * @param sellerSku
     * @return
     * <AUTHOR>
     * @history
     */
    @Update
    void updatePriceModifyTime(@Param("site")String site, @Param("sellerSku")String sellerSku);

}
