package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomMerchantShippingFee;
import com.zielsmart.mc.repository.mapper.SomMerchantShippingFeeMapper;
import com.zielsmart.mc.vo.SomMerchantShippingFeePageSearchVo;
import com.zielsmart.mc.vo.SomMerchantShippingFeeVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-11-13 11:17:35
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomMerchantShippingFeeService {

    @Resource
    private SomMerchantShippingFeeMapper somMerchantShippingFeeMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomMerchantShippingFeeVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomMerchantShippingFeeVo> queryByPage(SomMerchantShippingFeePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMerchantShippingFeeVo> pageResult = dynamicSqlManager.getMapper(SomMerchantShippingFeeMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomMerchantShippingFeeVo.class, searchVo);
    }

    /**
     * update
     * 修改
     *
     * @param somMerchantShippingFeeVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomMerchantShippingFeeVo somMerchantShippingFeeVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somMerchantShippingFeeVo) || StrUtil.isEmpty(somMerchantShippingFeeVo.getAid()) || ObjectUtil.isEmpty(somMerchantShippingFeeVo.getAmazonFee())) {
            throw new ValidateException("Aid 运费 不能为空");
        }
        somMerchantShippingFeeVo.setModifyName(tokenUser.getUserName());
        somMerchantShippingFeeVo.setModifyTime(DateTime.now().toJdkDate());
        somMerchantShippingFeeVo.setModifyNum(tokenUser.getJobNumber());

        somMerchantShippingFeeMapper.createLambdaQuery()
                .andEq("aid", somMerchantShippingFeeVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somMerchantShippingFeeVo, SomMerchantShippingFee.class));
    }
}
