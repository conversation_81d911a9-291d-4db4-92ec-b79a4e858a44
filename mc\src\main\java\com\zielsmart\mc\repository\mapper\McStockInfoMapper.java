package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McStockInfo;
import com.zielsmart.mc.vo.McStockInfoExtVo;
import com.zielsmart.mc.vo.McStockInfoSearchVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-02-14
*/

@SqlResource("mcStockInfo")
public interface McStockInfoMapper extends BaseMapper<McStockInfo> {

    McStockInfo queryByWhcodeAndSku(@Param("warehouseList") List<String> warehouseList, @Param("productMainCode") String productMainCode);

    /**
     * queryBySiteAndSku
     * 根据站点与sku查询库存信息
     *
     * @param siteList
     * @param skuList
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McStockInfo>}
     * <AUTHOR>
     * @history
     */
    List<McStockInfoExtVo> queryBySitesAndSkus(@Param("siteList") List<String> siteList, @Param("skuList") List<String> skuList);

    /**
     * querybyPlatformSiteSellerSku
     * 根据平台站点展示码查询库存信息
     * @param searchVo
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McStockInfoExtVo>}
     * <AUTHOR>
     * @history
     */
    List<McStockInfoExtVo> querybyPlatformSiteSellerSku(@Param("searchVo") McStockInfoSearchVo searchVo);

    /**
     * 查询所有数据并关联市场
     * @return
     */
    List<McStockInfoExtVo> allWithMarketInSku(@Param("skuList") List<String> skuList);
}
