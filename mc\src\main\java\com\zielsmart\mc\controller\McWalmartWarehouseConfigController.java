package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McWalmartWarehouseConfigService;
import com.zielsmart.mc.vo.McWalmartWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWalmartWarehouseConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWalmartWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcWalmartWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "walmart可售仓库配置")
public class McWalmartWarehouseConfigController extends BasicController {

    @Resource
    McWalmartWarehouseConfigService mcWalmartWarehouseConfigService;


    /**
     * add
     * 新增
     *
     * @param mcWalmartWarehouseConfigVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增")
    @PostMapping(value = "/add")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody @Validated McWalmartWarehouseConfigVo mcWalmartWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWalmartWarehouseConfigService.add(mcWalmartWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McWalmartWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McWalmartWarehouseConfigVo>> queryByPage(@RequestBody McWalmartWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWalmartWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * update
     *
     * @param mcWalmartWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McWalmartWarehouseConfigVo mcWalmartWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWalmartWarehouseConfigService.update(mcWalmartWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcWalmartWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McWalmartWarehouseConfigVo mcWalmartWarehouseConfigVo) throws ValidateException {
        mcWalmartWarehouseConfigService.delete(mcWalmartWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * getWarehouseAreaInfo
     * 获取仓库区域信息(可售库存报表)
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McWalmartWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取仓库区域信息")
    @PostMapping(value = "/getWarehouseAreaInfo")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWalmartWarehouseConfigVo>> getWarehouseAreaInfo(){
        return ResultVo.ofSuccess(mcWalmartWarehouseConfigService.getWarehouseAreaInfo());
    }
}
