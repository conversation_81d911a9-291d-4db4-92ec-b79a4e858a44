package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 邀评规则配置表
 * gen by 代码生成器 2022-04-12
 */

@Table(name = "mc.som_review_rules_config")
public class SomReviewRulesConfig implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 提升留评率
     */
    @Column("improve_retention_rate")
    private BigDecimal improveRetentionRate;
    /**
     * 邮件来评率
     */
    @Column("email_to_rate")
    private BigDecimal emailToRate;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人工号
     */
    @Column("last_modify_num")
    private String lastModifyNum;
    /**
     * 修改人姓名
     */
    @Column("last_modify_name")
    private String lastModifyName;
    /**
     * 修改时间
     */
    @Column("last_modify_time")
    private Date lastModifyTime;

    public SomReviewRulesConfig() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 提升留评率
     *
     * @return
     */
    public BigDecimal getImproveRetentionRate() {
        return improveRetentionRate;
    }

    /**
     * 提升留评率
     *
     * @param improveRetentionRate
     */
    public void setImproveRetentionRate(BigDecimal improveRetentionRate) {
        this.improveRetentionRate = improveRetentionRate;
    }

    /**
     * 邮件来评率
     *
     * @return
     */
    public BigDecimal getEmailToRate() {
        return emailToRate;
    }

    /**
     * 邮件来评率
     *
     * @param emailToRate
     */
    public void setEmailToRate(BigDecimal emailToRate) {
        this.emailToRate = emailToRate;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getLastModifyNum() {
        return lastModifyNum;
    }

    /**
     * 修改人工号
     *
     * @param lastModifyNum
     */
    public void setLastModifyNum(String lastModifyNum) {
        this.lastModifyNum = lastModifyNum;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getLastModifyName() {
        return lastModifyName;
    }

    /**
     * 修改人姓名
     *
     * @param lastModifyName
     */
    public void setLastModifyName(String lastModifyName) {
        this.lastModifyName = lastModifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * 修改时间
     *
     * @param lastModifyTime
     */
    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
}
