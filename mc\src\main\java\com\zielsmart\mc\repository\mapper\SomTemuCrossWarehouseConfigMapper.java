package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuCrossWarehouseConfig;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Temu跨境可售仓库配置管理
 * @date 2025-06-27 11:34:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuCrossWarehouseConfig")
public interface SomTemuCrossWarehouseConfigMapper extends BaseMapper<SomTemuCrossWarehouseConfig> {

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return PageResult<SomTemuCrossWarehouseConfigVo>
     */
    PageResult<SomTemuCrossWarehouseConfigVo> queryByPage(@Param("searchVo") SomTemuCrossWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
