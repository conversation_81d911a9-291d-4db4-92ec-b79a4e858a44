package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomVcPointsPromotionService;
import com.zielsmart.mc.vo.SomVcPointsPromotionImportVo;
import com.zielsmart.mc.vo.SomVcPointsPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomVcPointsPromotionVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
@RequestMapping(value = "/somVcPointsPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC Points")
public class SomVcPointsPromotionController extends BasicController{

    @Resource
    SomVcPointsPromotionService somVcPointsPromotionService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcPointsPromotionVo>> queryByPage(@RequestBody SomVcPointsPromotionPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcPointsPromotionService.queryByPage(searchVo));
    }

    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/addOrEdit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomVcPointsPromotionVo somVcPointsPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcPointsPromotionService.addOrEdit(somVcPointsPromotionVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomVcPointsPromotionVo somVcPointsPromotionVo) throws ValidateException {
        somVcPointsPromotionService.delete(somVcPointsPromotionVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> cancel(@RequestBody SomVcPointsPromotionVo somVcPointsPromotionVo) throws ValidateException {
        somVcPointsPromotionService.cancel(somVcPointsPromotionVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomVcPointsPromotionPageSearchVo searchVo){
        String data = somVcPointsPromotionService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/VCPointsPromotionImportTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // sheet页
        importParams.setSheetNum(0);
        // 导入字段
        String[] arr = {"站点", "账号名称", "供应商编码", "展示码", "ASIN", "活动起始日期", "活动截止日期", "积分比例", "评分", "申请原因", "自定义原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcPointsPromotionImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcPointsPromotionImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somVcPointsPromotionService.importExcel(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }
}
