package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomMiraviaListing;
import com.zielsmart.mc.vo.SomMiraviaListingPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaListingReport;
import com.zielsmart.mc.vo.SomMiraviaListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2024-04-22
 */

@SqlResource("somMiraviaListing")
public interface SomMiraviaListingMapper extends BaseMapper<SomMiraviaListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomMiraviaListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMiraviaListingVo> queryByPage(@Param("searchVo") SomMiraviaListingPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomMiraviaListingReport> stockReport(@Param("searchVo") SomMiraviaListingPageSearchVo searchVo, PageRequest pageRequest);
}
