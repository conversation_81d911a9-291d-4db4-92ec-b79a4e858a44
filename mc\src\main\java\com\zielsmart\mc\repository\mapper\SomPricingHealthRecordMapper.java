package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomPricingHealthRecordPageSearchVo;
import com.zielsmart.mc.vo.SomPricingHealthRecordVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-08-12
 */

@SqlResource("somPricingHealthRecord")
public interface SomPricingHealthRecordMapper extends BaseMapper<SomPricingHealthRecord> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult<SomPricingHealthRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPricingHealthRecordVo> queryByPage(@Param("searchVo") SomPricingHealthRecordPageSearchVo searchVo, PageRequest pageRequest);

    default void batchHandle(@Param("updateList") List<SomPricingHealthRecord> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPricingHealthRecord.batchHandle"), updateList);
    }
}
