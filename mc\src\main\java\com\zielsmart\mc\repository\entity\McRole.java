package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 平台站点角色

* gen by 代码生成器 2021-07-16
*/

@Table(name="mc.mc_role")
public class McRole implements java.io.Serializable {
	/**
	 * 角色主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 角色名称
	 */
	@Column("role_name")
	private String roleName ;
	/**
	 * 角色编码
	 */
	@Column("role_code")
	private String roleCode ;
	/**
	 * 角色状态
	 */
	@Column("role_status")
	private Integer roleStatus ;

	public McRole() {
	}

	/**
	* 角色主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 角色主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 角色名称
	*@return
	*/
	public String getRoleName(){
		return  roleName;
	}
	/**
	* 角色名称
	*@param  roleName
	*/
	public void setRoleName(String roleName ){
		this.roleName = roleName;
	}
	/**
	* 角色编码
	*@return
	*/
	public String getRoleCode(){
		return  roleCode;
	}
	/**
	* 角色编码
	*@param  roleCode
	*/
	public void setRoleCode(String roleCode ){
		this.roleCode = roleCode;
	}
	/**
	* 角色状态
	*@return
	*/
	public Integer getRoleStatus(){
		return  roleStatus;
	}
	/**
	* 角色状态
	*@param  roleStatus
	*/
	public void setRoleStatus(Integer roleStatus ){
		this.roleStatus = roleStatus;
	}

}
