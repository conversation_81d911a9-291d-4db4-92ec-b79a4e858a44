package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomMiraviaWarehouseConfig;
import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomMiraviaWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomMiraviaWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseVo;
import com.zielsmart.mc.vo.SomTargetWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomMiraviaWarehouseConfigService
 * @description
 * @date 2024-04-29 12:10:20
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomMiraviaWarehouseConfigService {

    @Resource
    private SomMiraviaWarehouseConfigMapper somMiraviaWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private SomMiraviaWarehouseMapper somMiraviaWarehouseMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomMiraviaWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomMiraviaWarehouseConfigVo> queryByPage(SomMiraviaWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMiraviaWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomMiraviaWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (ObjectUtil.isNotEmpty(pageResult.getList())) {
            List<SomMiraviaWarehouseConfig> configList = somMiraviaWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, List<SomMiraviaWarehouseConfig>> configMap = configList.stream().collect(Collectors.groupingBy(SomMiraviaWarehouseConfig::getWarehouseCode, Collectors.toList()));
            for (SomMiraviaWarehouseConfigVo configVo : pageResult.getList()) {
                if (configMap.containsKey(configVo.getWarehouseCode())) {
                    List<SomMiraviaWarehouseConfig> configs = configMap.get(configVo.getWarehouseCode());
                    List<String> nameList = new ArrayList<>();
                    configVo.setList(configs.stream().map(f -> {
                        SomTargetWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTargetWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableWarehouseCode(f.getUseableWarehouseCode());
                        useableWarehouse.setUseableStorageCode(f.getUseableStorageCode());
                        nameList.add(warehouseMap.get(f.getUseableWarehouseCode()) + "-" + storageMap.get(f.getUseableStorageCode()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    configVo.setWarehouseNameList(nameList);
                    SomMiraviaWarehouseConfig warehouseConfig = configs.get(0);
                    configVo.setCreateNum(warehouseConfig.getCreateNum());
                    configVo.setCreateName(warehouseConfig.getCreateName());
                    configVo.setCreateTime(warehouseConfig.getCreateTime());
                    configVo.setLastModifyNum(warehouseConfig.getLastModifyNum());
                    configVo.setLastModifyName(warehouseConfig.getLastModifyName());
                    configVo.setLastModifyTime(warehouseConfig.getLastModifyTime());
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomMiraviaWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somMiraviaWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somMiraviaWarehouseConfigVo) || StrUtil.isEmpty(somMiraviaWarehouseConfigVo.getWarehouseCode()) || ObjectUtil.isEmpty(somMiraviaWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (somMiraviaWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somMiraviaWarehouseConfigVo.getWarehouseCode()).count() > 0) {
            throw new ValidateException("您选择的平台仓库在系统中已存在，不允许重复添加");
        }
        List<SomMiraviaWarehouseConfig> insertList = new ArrayList<>();
        Date date = DateTime.now().toJdkDate();
        for (SomTargetWarehouseConfigVo.UseableWarehouse item : somMiraviaWarehouseConfigVo.getList()) {
            SomMiraviaWarehouseConfig config = new SomMiraviaWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(somMiraviaWarehouseConfigVo.getWarehouseCode());
            config.setWarehouseName(somMiraviaWarehouseConfigVo.getWarehouseName());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(date);
            insertList.add(config);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            somMiraviaWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * update
     * 修改
     *
     * @param somMiraviaWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somMiraviaWarehouseConfigVo) || StrUtil.isEmpty(somMiraviaWarehouseConfigVo.getWarehouseCode()) || ObjectUtil.isEmpty(somMiraviaWarehouseConfigVo.getList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somMiraviaWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somMiraviaWarehouseConfigVo.getWarehouseCode()).delete();
        List<SomMiraviaWarehouseConfig> insertList = new ArrayList<>();
        Date date = DateTime.now().toJdkDate();
        for (SomTargetWarehouseConfigVo.UseableWarehouse item : somMiraviaWarehouseConfigVo.getList()) {
            SomMiraviaWarehouseConfig config = new SomMiraviaWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(somMiraviaWarehouseConfigVo.getWarehouseCode());
            config.setWarehouseName(somMiraviaWarehouseConfigVo.getWarehouseName());
            config.setUseableStorageCode(item.getUseableStorageCode());
            config.setUseableWarehouseCode(item.getUseableWarehouseCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(date);
            config.setLastModifyNum(tokenUser.getJobNumber());
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(date);
            insertList.add(config);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            somMiraviaWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somMiraviaWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somMiraviaWarehouseConfigVo) || StrUtil.isEmpty(somMiraviaWarehouseConfigVo.getWarehouseCode())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somMiraviaWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somMiraviaWarehouseConfigVo.getWarehouseCode()).delete();
    }

    /**
     * queryList
     * 获取平台仓库列表
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomMiraviaWarehouseVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomMiraviaWarehouseVo> queryList() {
        List<SomMiraviaWarehouseVo> list = somMiraviaWarehouseMapper.queryList();
        return ConvertUtils.listConvert(list, SomMiraviaWarehouseVo.class);
    }
}
