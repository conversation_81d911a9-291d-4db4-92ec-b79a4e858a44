package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomPromotionService;
import com.zielsmart.mc.vo.SomPromotionExtVo;
import com.zielsmart.mc.vo.SomPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomPromotionVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPromotionController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "promotion表管理")
public class SomPromotionController extends BasicController {

    @Resource
    SomPromotionService somPromotionService;

    /**
     * generClaimCode
     * 生成促销码
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "生成促销码")
    @PostMapping(value = "/generClaimCode")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> generClaimCode() {
        return ResultVo.ofSuccess(somPromotionService.generClaimCode());
    }

    /**
     * add
     *
     * @param somPromotionVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/编辑/克隆")
    @PostMapping(value = "/addOrEditOrClone")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrEditOrClone(@RequestBody @Validated SomPromotionVo somPromotionVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPromotionService.addOrEditOrClone(somPromotionVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomPromotionVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPromotionExtVo>> queryByPage(@RequestBody SomPromotionPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somPromotionService.queryByPage(searchVo));
    }

    /**
     * searchDetail
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomPromotionExtVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询详情")
    @PostMapping(value = "/searchDetail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPromotionExtVo> searchDetail(@RequestBody SomPromotionVo searchVo) throws ValidateException{
        return ResultVo.ofSuccess(somPromotionService.searchDetail(searchVo));
    }

    /**
     * finish
     *
     * @param finishVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "结束活动")
    @PostMapping(value = "/finish")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> finish(@RequestBody SomPromotionVo finishVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPromotionService.finish(finishVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * mark
     *
     * @param markVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记活动状态")
    @PostMapping(value = "/mark")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> mark(@RequestBody SomPromotionVo markVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPromotionService.mark(markVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * submit
     *
     * @param submitVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "提交")
    @PostMapping(value = "/submit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> submit(@RequestBody SomPromotionVo submitVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPromotionService.submit(submitVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * summited
     * 标记为已提报
     * @param summitedVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记为已提报")
    @PostMapping(value = "/submited")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> summited(@RequestBody SomPromotionVo summitedVo) throws ValidateException {
        somPromotionService.summited(summitedVo);
        return ResultVo.ofSuccess(null);
    }
}
