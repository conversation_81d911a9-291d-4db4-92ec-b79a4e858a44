package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomAmazonVcPricePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-08-26
 */

@SqlResource("somAmazonVcPrice")
public interface SomAmazonVcPriceMapper extends BaseMapper<SomAmazonVcPrice> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomAmazonVcPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonVcPriceVo> queryByPage(@Param("searchVo") SomAmazonVcPricePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchUpdate
     * 批量更新
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("updateList") List<SomAmazonVcPrice> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somAmazonVcPrice.batchUpdate"), updateList);
    }
}
