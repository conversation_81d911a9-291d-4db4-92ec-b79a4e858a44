package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 字典表
 * gen by 代码生成器 2021-08-02
 */

@Table(name = "mc.mc_dictionary_info")
public class McDictionaryInfo implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 字典key
     */
    @Column("item_type_code")
    private String itemTypeCode;
    /**
     * 字典描述
     */
    @Column("item_type_name")
    private String itemTypeName;
    /**
     * 选项展示值
     */
    @Column("item_lable")
    private String itemLable;
    /**
     * 选项值
     */
    @Column("item_value")
    private String itemValue;
    /**
     * 选项值1
     */
    @Column("item_value1")
    private String itemValue1;
    /**
     * 选项值2
     */
    @Column("item_value2")
    private String itemValue2;

    /**
     * 选项值3
     */
    @Column("item_value3")
    private String itemValue3;

    @Column("item_value4")
    private String itemValue4;

    @Column("item_value5")
    private String itemValue5;

    @Column("item_value6")
    private String itemValue6;

    @Column("item_value7")
    private String itemValue7;

    @Column("item_value8")
    private String itemValue8;

    @Column("item_value9")
    private String itemValue9;

    @Column("item_value10")
    private String itemValue10;

    @Column("item_value11")
    private String itemValue11;

    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;

    @Column("sort")
    private Integer sort;

    public McDictionaryInfo() {
    }

    public String getItemValue4() {
        return itemValue4;
    }

    public void setItemValue4(String itemValue4) {
        this.itemValue4 = itemValue4;
    }

    public String getItemValue5() {
        return itemValue5;
    }

    public void setItemValue5(String itemValue5) {
        this.itemValue5 = itemValue5;
    }

    public String getItemValue6() {
        return itemValue6;
    }

    public void setItemValue6(String itemValue6) {
        this.itemValue6 = itemValue6;
    }

    public String getItemValue7() {
        return itemValue7;
    }

    public void setItemValue7(String itemValue7) {
        this.itemValue7 = itemValue7;
    }

    public String getItemValue3() {
        return itemValue3;
    }

    public void setItemValue3(String itemValue3) {
        this.itemValue3 = itemValue3;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 字典key
     *
     * @return
     */
    public String getItemTypeCode() {
        return itemTypeCode;
    }

    /**
     * 字典key
     *
     * @param itemTypeCode
     */
    public void setItemTypeCode(String itemTypeCode) {
        this.itemTypeCode = itemTypeCode;
    }

    /**
     * 字典描述
     *
     * @return
     */
    public String getItemTypeName() {
        return itemTypeName;
    }

    /**
     * 字典描述
     *
     * @param itemTypeName
     */
    public void setItemTypeName(String itemTypeName) {
        this.itemTypeName = itemTypeName;
    }

    /**
     * 选项展示值
     *
     * @return
     */
    public String getItemLable() {
        return itemLable;
    }

    /**
     * 选项展示值
     *
     * @param itemLable
     */
    public void setItemLable(String itemLable) {
        this.itemLable = itemLable;
    }

    /**
     * 选项值
     *
     * @return
     */
    public String getItemValue() {
        return itemValue;
    }

    /**
     * 选项值
     *
     * @param itemValue
     */
    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    /**
     * 选项值1
     *
     * @return
     */
    public String getItemValue1() {
        return itemValue1;
    }

    /**
     * 选项值1
     *
     * @param itemValue1
     */
    public void setItemValue1(String itemValue1) {
        this.itemValue1 = itemValue1;
    }

    /**
     * 选项值2
     *
     * @return
     */
    public String getItemValue2() {
        return itemValue2;
    }

    /**
     * 选项值2
     *
     * @param itemValue2
     */
    public void setItemValue2(String itemValue2) {
        this.itemValue2 = itemValue2;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getItemValue8() {
        return itemValue8;
    }

    public void setItemValue8(String itemValue8) {
        this.itemValue8 = itemValue8;
    }

    public String getItemValue9() {
        return itemValue9;
    }

    public void setItemValue9(String itemValue9) {
        this.itemValue9 = itemValue9;
    }

    public String getItemValue10() {
        return itemValue10;
    }

    public void setItemValue10(String itemValue10) {
        this.itemValue10 = itemValue10;
    }

    public String getItemValue11() {
        return itemValue11;
    }

    public void setItemValue11(String itemValue11) {
        this.itemValue11 = itemValue11;
    }
}
