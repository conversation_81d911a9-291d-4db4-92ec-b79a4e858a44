package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomStandardPriceService;
import com.zielsmart.mc.vo.SomStandardPriceExtVo;
import com.zielsmart.mc.vo.SomStandardPriceImportVo;
import com.zielsmart.mc.vo.SomStandardPricePageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomStandardPriceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somStandardPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "定价管理")
public class SomStandardPriceController extends BasicController {

    @Resource
    SomStandardPriceService somStandardPriceService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomStandardPriceExtVo>> queryByPage(@RequestBody SomStandardPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somStandardPriceService.queryByPage(searchVo));
    }

    @Operation(summary = "修改")
    @PostMapping(value = "/edit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody SomStandardPriceExtVo priceExtVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somStandardPriceService.edit(priceExtVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "SKU", "展示码", "发货方式", "RRP", "最低价", "标准价", "最高价"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomStandardPriceImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomStandardPriceImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somStandardPriceService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportExcel(@RequestBody SomStandardPricePageSearchVo exportVo) {
        String data = somStandardPriceService.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "下载价格导入模板")
    @GetMapping(value = "/download-price-template")
    public String downloadPriceExcel() {
        return "forward:/static/excel/StandardPriceTemplate.xlsx";
    }

}
