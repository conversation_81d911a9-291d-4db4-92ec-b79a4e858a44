package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomRecommendPublishListPageSearchVo;
import com.zielsmart.mc.vo.SomRecommendPublishListVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description 推荐上货清单管理
 * @date 2025-07-21 10:17:48
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somRecommendPublishList")
public interface SomRecommendPublishListMapper extends BaseMapper<SomRecommendPublishList> {

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return 分页结果
     */
    PageResult<SomRecommendPublishListVo> queryByPage(@Param("searchVo")SomRecommendPublishListPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 批量更新
     *
     * @param recommendPublishLists 推荐上货清单集合
     */
    default void importUpdateBatch(@Param("simplePublishes") List<SomRecommendPublishList> recommendPublishLists) {
        this.getSQLManager().updateBatch(SqlId.of("somRecommendPublishList.importUpdate"), recommendPublishLists);
    }

    /**
     * 查询客户简称列表
     *
     * @param keyWord 关键字
     * @return 客户简称列表
     */
    List<String> queryCustomerShortName(@Param("keyWord") String keyWord);

    /**
     * 批量更新为已上货
     *
     * @param aids 主键ids
     */
    @Update
    void batchUpdateIsPublished(@Param("aids") List<String> aids);
}
