package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import com.zielsmart.mc.service.SomWayfairCostConfigService;
import com.zielsmart.mc.vo.SomWayfairCostConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairCostConfigSaveVo;
import com.zielsmart.mc.vo.SomWayfairCostConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWayfairCostConfigController
 * @description
 * @date 2024-02-27 15:29:38
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somWayfairCostConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Wayfair费用指标配置表管理")
public class SomWayfairCostConfigController extends BasicController {

    @Resource
    SomWayfairCostConfigService somWayfairCostConfigService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWayfairCostConfigVo>> queryByPage(@RequestBody SomWayfairCostConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWayfairCostConfigService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWayfairCostConfigSaveVo saveVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWayfairCostConfigService.save(saveVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomWayfairCostConfigVo somWayfairCostConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWayfairCostConfigService.update(somWayfairCostConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWayfairCostConfigVo somWayfairCostConfigVo) throws ValidateException {
        somWayfairCostConfigService.delete(somWayfairCostConfigVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/WayfairCostConfigTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"适用站点", "Name", "运算符号", "Weight（KG）", "L + Girth（CM）", "L（CM）", "M（CM）", "S（CM）", "配送费/件", "仓储费/天/m³", "RTV召回费/件", "VAS费/件",
                "跨渠道配送费/件", "跨渠道配送费/2件", "跨渠道配送费/3件", "跨渠道配送费/4件", "跨渠道配送费/5件",
                "币种", "优先级"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomWayfairCostConfigVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomWayfairCostConfigVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误，请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空，请检查数据");
        }
        somWayfairCostConfigService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "修复数据")
    @PostMapping(value = "/fix")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> fix() {
        somWayfairCostConfigService.fix();
        return ResultVo.ofSuccess(null);
    }

}
