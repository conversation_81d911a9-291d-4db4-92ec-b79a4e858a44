package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* Woot促销活动
* gen by 代码生成器 2024-06-04
*/

@Table(name="mc.som_woot_promotion")
public class SomWootPromotion implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 产品标题
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 是否是VC产品 0 否  1 是
	 */
	@Column("vc_flag")
	private Integer vcFlag ;
	/**
	 * 是否是FBA库存 0 否 1是
	 */
	@Column("fba_stock_flag")
	private Integer fbaStockFlag ;
	/**
	 * 标价/市场价
	 */
	@Column("list_price")
	private BigDecimal listPrice ;
	/**
	 * 促销价
	 */
	@Column("promotion_price")
	private BigDecimal promotionPrice ;
	/**
	 * 促销数量
	 */
	@Column("promotion_quantity")
	private String promotionQuantity ;
	/**
	 * SAP到仓价
	 */
	@Column("sap_price")
	private BigDecimal sapPrice ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 活动状态。10.草稿 20.审批中 30.审批未通过 40.提报中 50.提报成功 99.提报失败
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 可销售天数
	 */
	@Column("marketable_days")
	private Integer marketableDays ;
	/**
	 * 近12周可销售天数
	 */
	@Column("sales_days_in_the_last_12_weeks")
	private String salesDaysInTheLast12Weeks ;
	/**
	 * 近四周毛利率
	 */
	@Column("gross_profit_rate_30_day")
	private BigDecimal grossProfitRate30Day ;
	/**
	 * 采购订单编号
	 */
	@Column("purchase_order")
	private String purchaseOrder ;
	/**
	 * 创建日期
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 审批人工号
	 */
	@Column("approval_num")
	private String approvalNum ;
	/**
	 * 审批人姓名
	 */
	@Column("approval_name")
	private String approvalName ;
	/**
	 * 审批时间
	 */
	@Column("approval_time")
	private Date approvalTime ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;

	@Column("fn_sku")
	private String fnSku ;

	@Column("approval_remark")
	private String approvalRemark ;

	@Column("deal_type")
	private String dealType;

	public SomWootPromotion() {
	}


	public String getApprovalRemark() {
		return approvalRemark;
	}

	public void setApprovalRemark(String approvalRemark) {
		this.approvalRemark = approvalRemark;
	}

	public String getFnSku() {
		return fnSku;
	}

	public void setFnSku(String fnSku) {
		this.fnSku = fnSku;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 产品标题
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 产品标题
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 是否是VC产品 0 否  1 是
	*@return
	*/
	public Integer getvcFlag(){
		return  vcFlag;
	}
	/**
	* 是否是VC产品 0 否  1 是
	*@param  vcFlag
	*/
	public void setvcFlag(Integer vcFlag ){
		this.vcFlag = vcFlag;
	}
	/**
	* 是否是FBA库存 0 否 1是
	*@return
	*/
	public Integer getFbaStockFlag(){
		return  fbaStockFlag;
	}
	/**
	* 是否是FBA库存 0 否 1是
	*@param  fbaStockFlag
	*/
	public void setFbaStockFlag(Integer fbaStockFlag ){
		this.fbaStockFlag = fbaStockFlag;
	}
	/**
	* 标价/市场价
	*@return
	*/
	public BigDecimal getListPrice(){
		return  listPrice;
	}
	/**
	* 标价/市场价
	*@param  listPrice
	*/
	public void setListPrice(BigDecimal listPrice ){
		this.listPrice = listPrice;
	}
	/**
	* 促销价
	*@return
	*/
	public BigDecimal getPromotionPrice(){
		return  promotionPrice;
	}
	/**
	* 促销价
	*@param  promotionPrice
	*/
	public void setPromotionPrice(BigDecimal promotionPrice ){
		this.promotionPrice = promotionPrice;
	}
	/**
	* 促销数量
	*@return
	*/
	public String getPromotionQuantity(){
		return  promotionQuantity;
	}
	/**
	* 促销数量
	*@param  promotionQuantity
	*/
	public void setPromotionQuantity(String promotionQuantity ){
		this.promotionQuantity = promotionQuantity;
	}
	/**
	* SAP到仓价
	*@return
	*/
	public BigDecimal getSapPrice(){
		return  sapPrice;
	}
	/**
	* SAP到仓价
	*@param  sapPrice
	*/
	public void setSapPrice(BigDecimal sapPrice ){
		this.sapPrice = sapPrice;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 活动状态。10.草稿 20.审批中 30.审批未通过 40.提报中 50.提报成功 99.提报失败
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 活动状态。10.草稿 20.审批中 30.审批未通过 40.提报中 50.提报成功 99.提报失败
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 可销售天数
	*@return
	*/
	public Integer getMarketableDays(){
		return  marketableDays;
	}
	/**
	* 可销售天数
	*@param  marketableDays
	*/
	public void setMarketableDays(Integer marketableDays ){
		this.marketableDays = marketableDays;
	}
	/**
	* 近12周可销售天数
	*@return
	*/
	public String getSalesDaysInTheLast12Weeks(){
		return  salesDaysInTheLast12Weeks;
	}
	/**
	* 近12周可销售天数
	*@param  salesDaysInTheLast12Weeks
	*/
	public void setSalesDaysInTheLast12Weeks(String salesDaysInTheLast12Weeks ){
		this.salesDaysInTheLast12Weeks = salesDaysInTheLast12Weeks;
	}
	/**
	* 近四周毛利率
	*@return
	*/
	public BigDecimal getGrossProfitRate30Day(){
		return  grossProfitRate30Day;
	}
	/**
	* 近四周毛利率
	*@param  grossProfitRate30Day
	*/
	public void setGrossProfitRate30Day(BigDecimal grossProfitRate30Day ){
		this.grossProfitRate30Day = grossProfitRate30Day;
	}
	/**
	* 采购订单编号
	*@return
	*/
	public String getPurchaseOrder(){
		return  purchaseOrder;
	}
	/**
	* 采购订单编号
	*@param  purchaseOrder
	*/
	public void setPurchaseOrder(String purchaseOrder ){
		this.purchaseOrder = purchaseOrder;
	}
	/**
	* 创建日期
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建日期
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 审批人工号
	*@return
	*/
	public String getApprovalNum(){
		return  approvalNum;
	}
	/**
	* 审批人工号
	*@param  approvalNum
	*/
	public void setApprovalNum(String approvalNum ){
		this.approvalNum = approvalNum;
	}
	/**
	* 审批人姓名
	*@return
	*/
	public String getApprovalName(){
		return  approvalName;
	}
	/**
	* 审批人姓名
	*@param  approvalName
	*/
	public void setApprovalName(String approvalName ){
		this.approvalName = approvalName;
	}
	/**
	* 审批时间
	*@return
	*/
	public Date getApprovalTime(){
		return  approvalTime;
	}
	/**
	* 审批时间
	*@param  approvalTime
	*/
	public void setApprovalTime(Date approvalTime ){
		this.approvalTime = approvalTime;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}

	public String getDealType() {
		return dealType;
	}

	public void setDealType(String dealType) {
		this.dealType = dealType;
	}
}
