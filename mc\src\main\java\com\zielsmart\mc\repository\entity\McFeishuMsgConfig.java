package com.zielsmart.mc.repository.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* 消息提醒人员配置
* gen by 代码生成器 2021-08-31
*/

@Table(name="mc.mc_feishu_msg_config")
public class McFeishuMsgConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 业务组编码
	 */
	@Column("business_group_code")
	private String businessGroupCode ;
	/**
	 * 业务组名称
	 */
	@Column("business_group_name")
	private String businessGroupName ;
	/**
	 * 消息接收人编码
	 */
	@Column("option_person_code")
	private String optionPersonCode ;
	/**
	 * 消息接收人名称
	 */
	@Column("option_person_name")
	private String optionPersonName ;

	/**
	 * 创建人名称
	 */
	@Column("create_name")
	private String createName ;

	/**
	 * uums用户Aid
	 */
	@Column("u_aid")
	private String uAid ;

	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public String getModifyNum() {
		return modifyNum;
	}

	public void setModifyNum(String modifyNum) {
		this.modifyNum = modifyNum;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getuAid() {
		return uAid;
	}

	public void setuAid(String uAid) {
		this.uAid = uAid;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getCreateNum() {
		return createNum;
	}

	public void setCreateNum(String createNum) {
		this.createNum = createNum;
	}

	public McFeishuMsgConfig() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 业务组编码
	*@return
	*/
	public String getBusinessGroupCode(){
		return  businessGroupCode;
	}
	/**
	* 业务组编码
	*@param  businessGroupCode
	*/
	public void setBusinessGroupCode(String businessGroupCode ){
		this.businessGroupCode = businessGroupCode;
	}
	/**
	* 业务组名称
	*@return
	*/
	public String getBusinessGroupName(){
		return  businessGroupName;
	}
	/**
	* 业务组名称
	*@param  businessGroupName
	*/
	public void setBusinessGroupName(String businessGroupName ){
		this.businessGroupName = businessGroupName;
	}
	/**
	* 操作人编码
	*@return
	*/
	public String getOptionPersonCode(){
		return  optionPersonCode;
	}
	/**
	* 操作人编码
	*@param  optionPersonCode
	*/
	public void setOptionPersonCode(String optionPersonCode ){
		this.optionPersonCode = optionPersonCode;
	}
	/**
	* 操作人名称
	*@return
	*/
	public String getOptionPersonName(){
		return  optionPersonName;
	}
	/**
	* 操作人名称
	*@param  optionPersonName
	*/
	public void setOptionPersonName(String optionPersonName ){
		this.optionPersonName = optionPersonName;
	}

}
