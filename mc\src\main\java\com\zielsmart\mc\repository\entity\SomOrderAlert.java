package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 订单预警规则
* gen by 代码生成器 2023-02-15
*/

@Table(name="mc.som_order_alert")
public class SomOrderAlert implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private String aid;
	/**
	 * 订单预警规则名称
	 */
	@Column("order_alert_rule_name")
	private String orderAlertRuleName;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform;
	/**
	 * 站点
	 */
	@Column("site")
	private String site;
	/**
	 * 发货方式AFN
,MFN
,AFN+MFN
	 */
	@Column("delivery_type")
	private String deliveryType;
	/**
	 * 订单数量
	 */
	@Column("order_quantity")
	private Integer orderQuantity;
	/**
	 * 预警通知规则名称
	 */
	@Column("alert_notification_rule_name")
	private String alertNotificationRuleName;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime;

	public SomOrderAlert() {
	}

	/**
	* 主键id
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键id
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 发货方式AFN
,MFN
,AFN+MFN
	*@return
	*/
	public String getDeliveryType(){
		return  deliveryType;
	}
	/**
	* 发货方式AFN
,MFN
,AFN+MFN
	*@param  deliveryType
	*/
	public void setDeliveryType(String deliveryType ){
		this.deliveryType = deliveryType;
	}
	/**
	* 订单数量
	*@return
	*/
	public Integer getOrderQuantity(){
		return  orderQuantity;
	}
	/**
	* 订单数量
	*@param  orderQuantity
	*/
	public void setOrderQuantity(Integer orderQuantity ){
		this.orderQuantity = orderQuantity;
	}
	/**
	* 预警通知规则名称
	*@return
	*/
	public String getAlertNotificationRuleName(){
		return  alertNotificationRuleName;
	}
	/**
	* 预警通知规则名称
	*@param  alertNotificationRuleName
	*/
	public void setAlertNotificationRuleName(String alertNotificationRuleName ){
		this.alertNotificationRuleName = alertNotificationRuleName;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

	/**
	 * 订单预警规则名称
	 *@return
	 */
	public String getOrderAlertRuleName(){
		return  orderAlertRuleName;
	}
	/**
	 * 订单预警规则名称
	 *@param  orderAlertRuleName
	 */
	public void setOrderAlertRuleName(String orderAlertRuleName ){
		this.orderAlertRuleName = orderAlertRuleName;
	}

}
