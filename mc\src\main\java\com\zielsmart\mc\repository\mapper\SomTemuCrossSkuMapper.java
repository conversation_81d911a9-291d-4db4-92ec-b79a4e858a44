package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuCrossSku;
import com.zielsmart.mc.vo.SomTemuCrossSkuVo;
import com.zielsmart.mc.vo.SomTemuImageUrlReport;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description SomTemuCrossSkuService
 * @date 2025-06-26 15:57:05
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuCrossSku")
public interface SomTemuCrossSkuMapper extends BaseMapper<SomTemuCrossSku> {

    /**
     * 查询 sku
     *
     * @param goodsIds 入参
     * @return List<SomTemuCrossSkuVo>
     */
    List<SomTemuCrossSkuVo> querySku(@Param("goodsIds") List<String> goodsIds);

    /**
     * 获取URL链接下载数据
     *
     * @param goodsIds 主表IDS
     * @return List<SomTemuImageUrlReport>
     */
    List<SomTemuImageUrlReport> getUrlExportData(@Param("goodsIds") List<String> goodsIds);

    /**
     * 查询列表
     * @param AccountIdList 店铺ID
     * @param sellerSkuList sellerSkuList
     * @return List<SomTemuCrossSkuVo>
     */
    List<SomTemuCrossSkuVo> queryList(@Param("AccountIdList") List<String> AccountIdList, @Param("sellerSkuList") List<String> sellerSkuList);
}
