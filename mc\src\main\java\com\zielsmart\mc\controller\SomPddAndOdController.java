package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomPddAndOdService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.file.FileSearchRequest;
import com.zielsmart.web.basic.file.FileSearchResponse;
import com.zielsmart.web.basic.file.IFileHandler;
import com.zielsmart.web.basic.file.s3.S3FileSearch;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomDealOfTheDayController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/PddAndOd", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "PDD & Outlet Deal表管理")
@Slf4j
public class SomPddAndOdController extends BasicController {

    @Resource
    SomPddAndOdService somPddAndOdService;

    @Autowired
    @Qualifier("s3FileHandlerChina")
    private IFileHandler fileHandler;


    @Autowired
    @Qualifier("amazonS3China")
    AmazonS3 amazonS3;

    @Value("${aws.s3china.bucket}")
    private String chinaBucket;

    /**
     * add
     *
     * @param addVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加或编辑")
    @PostMapping(value = "/addOrEdit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomPddAndOdVo addVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.addOrEdit(addVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "获取秒杀价毛利率")
    @PostMapping(value = "/dealGross")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<BigDecimal> dealGross(@RequestBody @Validated SomPddAndOdVo addVo) throws ValidateException {
        return ResultVo.ofSuccess(somPddAndOdService.getDealGross(addVo));
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo<PageVo<SomPddAndOdVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPddAndOdExtVo>> queryByPage(@RequestBody SomPddAndOdPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somPddAndOdService.queryByPage(searchVo));
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo<  SomPddAndOdVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPddAndOdExtVo> queryByAid(@RequestBody SomPddAndOdVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somPddAndOdService.queryByAid(searchVo));
    }

    /**
     * delete
     *
     * @param deleteVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPddAndOdExtVo> delete(@RequestBody SomPddAndOdVo deleteVo) throws ValidateException {
        somPddAndOdService.delete(deleteVo);
        return ResultVo.ofSuccess();
    }

    /**
     * submit
     *
     * @param submitVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "提交")
    @PostMapping(value = "/submit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPddAndOdExtVo> submit(@RequestBody SomPddAndOdVo submitVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        somPddAndOdService.submit(submitVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackResult
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈提报结果")
    @PostMapping(value = "/feedbackResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackResult(@RequestBody SomPddAndOdVo feedbackVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.feedbackResult(feedbackVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * modifyPlanTime
     *
     * @param modifyVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改活动时间")
    @PostMapping(value = "/modifyPlanTime")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> modifyPlanTime(@RequestBody SomPddAndOdVo modifyVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.modifyPlanTime(modifyVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/PDDODTemplate.xlsx";
    }

    /**
     * import
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        log.info("PDD&OutletDeal Excel 开始导入");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "秒杀类型", "评分", "活动开始时间", "活动截止时间",
                "展示码", "秒杀价", "承诺商品数量", "秒杀价毛利率", "三级分类近四周毛利率", "活动预计爆发系数", "近三十天DMS",
                "申请原因", "自定义申请原因", "营销活动类型"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPddAndOdExtVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPddAndOdExtVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result == null || result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somPddAndOdService.importExcel(result.getList(), tokenUser);
        stopWatch.stop();
        log.info("PDD&OutletDeal导入日志 Excel 导入完成,总耗时:" + stopWatch.getTotalTimeMillis() + "毫秒");
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * batchSubmit
     * 批量提交
     *
     * @param batchSubmitVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPddAndOdExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量提交")
    @PostMapping(value = "/batchSubmit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPddAndOdExtVo> batchSubmit(@RequestBody SomPddAndOdExtVo batchSubmitVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        somPddAndOdService.batchSubmit(batchSubmitVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * downloadExcel
     * 下载批量反馈提报结果模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载批量反馈提报结果模板")
    @GetMapping(value = "/downloadBatchTemplate")
    public String downloadBatchExcel() {
        return "forward:/static/excel/PDDODFeedBackTemplate.xlsx";
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param file
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量反馈提报结果")
    @PostMapping(value = "/batchFeedbackResult", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchFeedbackResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "秒杀类型", "活动开始时间", "活动截止时间", "展示码", "提报结果", "失败原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPddAndOdExtVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPddAndOdExtVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somPddAndOdService.batchFeedbackResult(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * exportExcel
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> exportExcel(@RequestBody SomPddAndOdPageSearchVo searchVo) {
        String data = somPddAndOdService.exportExcel(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * cancel
     * 取消活动
     *
     * @param cancelVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> cancel(@RequestBody SomPddAndOdVo cancelVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.cancel(cancelVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackCancelResult
     * 反馈取消结果
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈取消结果")
    @PostMapping(value = "/feedbackCancelResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackCancelResult(@RequestBody SomPddAndOdVo feedbackVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.feedbackCancelResult(feedbackVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * clone
     * 克隆
     *
     * @param cloneVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "克隆")
    @PostMapping(value = "/clone")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> clone(@RequestBody SomPddAndOdVo cloneVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.cloneById(cloneVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * summited
     * 标记为已提报
     *
     * @param summitedVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记为已提报")
    @PostMapping(value = "/submited")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> summited(@RequestBody SomPddAndOdExtVo summitedVo) throws ValidateException {
        somPddAndOdService.summited(summitedVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * downFile
     * 下载S3文件
     *
     * @param somPddDownFileVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载S3文件")
    @PostMapping(value = "/downFile")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public void downFile(@RequestBody SomPddDownFileVo somPddDownFileVo, HttpServletResponse response) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        List<String> nameList = new ArrayList<>();
        nameList.add("normal");
        nameList.add("primeFallDeal");
        nameList.add("primeDay");
        for (String name : nameList) {
            String pathStr = name + "/" + somPddDownFileVo.getCreateDate() + "/" + somPddDownFileVo.getSite() + "/";
            //ListObjectsV2Result listObjectsV2Result = amazonS3.listObjectsV2("sanbox.prod.eya.ziel","ziel-rpa/template/pdd/"+pathStr);
            ListObjectsV2Result listObjectsV2Result = amazonS3.listObjectsV2("ziel-rpa", "template/pdd/" + pathStr);
            for (S3ObjectSummary s3ObjectSummary : listObjectsV2Result.getObjectSummaries()) {
                //下载文件
                FileSearchRequest fileSearchRequest = S3FileSearch.builder().key(s3ObjectSummary.getKey()).build();
                try {
//                    boolean b = amazonS3.doesObjectExist(chinaBucket, s3ObjectSummary.getBucketName() + "/" + s3ObjectSummary.getKey());

                    FileSearchResponse fileModel = fileHandler.search(fileSearchRequest);
                    byte[] buff = Base64.getDecoder().decode(fileModel.getFileBase64Content());
                    if (null == buff || buff.length == 0) {
                        continue;
                    }
                    String fileName = fileModel.getSavePath().substring(fileModel.getSavePath().lastIndexOf("/") + 1);
                    compress(buff, pathStr, fileName, zos);
                } catch (Exception e) {
                    log.error("下载文件失败：{}", e.getMessage());
                }

            }
        }
        try {
            if (null != zos) {
                zos.close();
            }

            if (byteArrayOutputStream != null) {
                byteArrayOutputStream.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] buffer = byteArrayOutputStream.toByteArray();
        InputStream inputStream = new ByteArrayInputStream(buffer);
        String zipName = "PDD提报" + ".zip";
        if (zos != null && buffer.length != 22) {
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipName, "utf-8"));
            response.addHeader("Content-Length", "" + buffer.length);
            byte[] buff = new byte[1024];
            BufferedInputStream bis = null;
            OutputStream os = null;
            try {
                os = response.getOutputStream();
                bis = new BufferedInputStream(inputStream);
                int i = 0;
                while ((i = bis.read(buff)) != -1) {
                    os.write(buff, 0, i);
                    os.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } else {
            throw new Exception("未查询到对应的文件！");
        }
    }

    public static void compress(byte[] inputBuff, String baseDir, String fileName, ZipOutputStream zos) {
        BufferedInputStream bis = null;
        InputStream fis = null;
        byte[] buff = new byte[1024 * 10];
        try {
            fis = new ByteArrayInputStream(inputBuff);
            ZipEntry zipEntry = new ZipEntry(baseDir + fileName);//
            zos.putNextEntry(zipEntry);
            //读取待压缩的文件并写进压缩包里
            bis = new BufferedInputStream(fis);
            int i = 0;
            while ((i = bis.read(buff)) != -1) {
                zos.write(buff, 0, i);
            }
            zos.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            //关闭流
            try {
                if (null != bis)
                    bis.close();
                if (null != fis)
                    fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * downloadBatchCancelTemplate
     * 下载批量取消模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载批量取消模板")
    @GetMapping(value = "/downloadBatchCancelTemplate")
    public String downloadBatchCancelTemplate() {
        return "forward:/static/excel/DotdOrPddBatchCancelTemplate.xlsx";
    }

    /**
     * batchCancel
     * 批量取消
     *
     * <AUTHOR>
     */
    @Operation(summary = "批量取消")
    @PostMapping(value = "/batchCancel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchCancel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "提报活动开始时间", "提报活动结束时间"};
        importParams.setImportFields(arr);
        ExcelImportResult<PddAndOdImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), PddAndOdImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result != null && result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        String str = somPddAndOdService.batchCancel(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * downloadBatchFeedbackCancelTemplate
     * 下载批量反馈取消结果模板
     *
     * <AUTHOR>
     */
    @Operation(summary = "下载批量反馈取消结果模板")
    @GetMapping(value = "/downloadBatchFeedbackCancelTemplate")
    public String downloadBatchFeedbackCancelTemplate() {
        return "forward:/static/excel/DotdOrPddBatchFeedbackCancelTemplate.xlsx";
    }

    /**
     * batchFeedbackCancelResult
     * 批量反馈取消结果
     *
     * <AUTHOR>
     */
    @Operation(summary = "批量反馈取消结果")
    @PostMapping(value = "/batchFeedbackCancelResult", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchFeedbackCancelResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "状态", "备注", "提报活动开始时间", "提报活动结束时间"};
        importParams.setImportFields(arr);
        ExcelImportResult<PddAndOdImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), PddAndOdImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result == null || CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        String str = somPddAndOdService.batchFeedbackCancelResult(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    @Operation(summary = "下载批量编辑模板")
    @GetMapping(value = "/batchEditTemplate/download")
    public String downloadBatchEditTemplate() {
        return "forward:/static/excel/PDDODBatchEditTemplate.xlsx";
    }

    @Operation(summary = "导入批量编辑")
    @PostMapping(value = "/batchEdit/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importBatchEdit(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "活动起始时间", "活动截止时间", "秒杀价", "承诺商品数量"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPddAndOdBatchEditImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPddAndOdBatchEditImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somPddAndOdService.importBatchEdit(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "反馈修改结果")
    @PostMapping(value = "/feedbackModifyResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackModifyResult(@RequestBody SomPddAndOdVo somPddAndOdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPddAndOdService.feedbackModifyResult(somPddAndOdVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载批量反馈修改结果模板")
    @GetMapping(value = "/batchFeedbackEditResultTemplate/download")
    public String downloadBatchFeedbackEditResultTemplate() {
        return "forward:/static/excel/PDDODBatchFeedbackEditResultTemplate.xlsx";
    }

    @Operation(summary = "导入批量反馈修改结果")
    @PostMapping(value = "/batchFeedbackEditResult/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importBatchFeedbackEditResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "活动起始时间", "活动截止时间", "修改状态", "失败原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPddAndOdBatchFeedbackEditResultImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPddAndOdBatchFeedbackEditResultImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somPddAndOdService.importBatchFeedbackEditResult(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * getPdd
     * 生成大促文件
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "生成大促文件")
    @PostMapping(value = "/getPdd")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> getPdd(@RequestBody SomPddAndOdPageSearchVo searchVo) throws Exception {
        somPddAndOdService.getPdd(searchVo);
        return ResultVo.ofSuccess();
    }
}
