package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAliExpressListing;
import com.zielsmart.mc.vo.SomAliExpressListingPageSearchVo;
import com.zielsmart.mc.vo.SomAliExpressListingReport;
import com.zielsmart.mc.vo.SomAliExpressListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-11-05
*/

@SqlResource("somAliExpressListing")
public interface SomAliExpressListingMapper extends BaseMapper<SomAliExpressListing> {

    /**
     * 根据分页条件查询商品ID
     *      注：未关联子表 som_ali_express_sku_list
     *
     * @param searchVo 查询参数
     * @param pageRequest 分页条件
     * @return List<product_id>
     */
    PageResult<String> pageQueryProductIds(@Param("searchVo") SomAliExpressListingPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 根据分页条件查询商品ID
     *      注：关联子表 som_ali_express_sku_list
     *
     * @param searchVo 查询参数
     * @param pageRequest 分页条件
     * @return List<product_id>
     */
    PageResult<String> pageQueryProductIdsRelateSkuList(@Param("searchVo") SomAliExpressListingPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 根据商品ID查询
     *
     * @param productIds 商品ID
     * @return List<SomAliExpressListingVo>
     */
    List<SomAliExpressListingVo> queryByProductIds(@Param("productIds") List<String> productIds);

    PageResult<SomAliExpressListingReport> stockReport(@Param("searchVo")SomAliExpressListingPageSearchVo searchVo, PageRequest pageRequest);
}
