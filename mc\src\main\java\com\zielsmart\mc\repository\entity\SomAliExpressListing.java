package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2024-11-05
 */

@Table(name = "mc.som_ali_express_listing")
public class SomAliExpressListing implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 商品创建时间
     */
    @Column("created_time")
    private Date createdTime;
    /**
     * 商品最后修改时间
     */
    @Column("modified_time")
    private Date modifiedTime;
    /**
     * 商品ID
     */
    @Column("product_id")
    private String productId;
    /**
     * 商品最小供货价
     */
    @Column("product_min_price")
    private BigDecimal productMinPrice;
    /**
     * 商品最大供货价
     */
    @Column("product_max_price")
    private BigDecimal productMaxPrice;
    /**
     * 商品总库存
     */
    @Column("total_stocks")
    private Integer totalStocks;
    /**
     * 商品标题
     */
    @Column("title")
    private String title;
    /**
     * 商品审核状态，1.表示审核通过；否则，审核未通过
     */
    @Column("product_audit_status")
    private Integer productAuditStatus;
    /**
     * 审核未通过的原因
     */
    @Column("audit_failure_type")
    private String auditFailureType;
    /**
     * 商品主图链接
     */
    @Column("image_urls")
    private String imageUrls;
    /**
     * 币种
     */
    @Column("currency_code")
    private String currencyCode;
    /**
     * 商品子类目ID
     */
    @Column("leaf_category_id")
    private String leafCategoryId;
    /**
     * 商品发货类型：本地商家仓sw ;本地官方仓pw
     */
    @Column("local_stock_type")
    private String localStockType;
    /**
     * 运费模板名称
     */
    @Column("shipping_template")
    private String shippingTemplate;
    /**
     * 发货国家？猜测的。API文档对不上
     */
    @Column("local_ship_from")
    private String localShipFrom;
    /**
     * 下载时间
     */
    @Column("download_time")
    private Date downloadTime;

    public SomAliExpressListing() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 商品创建时间
     *
     * @return
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * 商品创建时间
     *
     * @param createdTime
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 商品最后修改时间
     *
     * @return
     */
    public Date getModifiedTime() {
        return modifiedTime;
    }

    /**
     * 商品最后修改时间
     *
     * @param modifiedTime
     */
    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    /**
     * 商品ID
     *
     * @return
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 商品ID
     *
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * 商品最小供货价
     *
     * @return
     */
    public BigDecimal getProductMinPrice() {
        return productMinPrice;
    }

    /**
     * 商品最小供货价
     *
     * @param productMinPrice
     */
    public void setProductMinPrice(BigDecimal productMinPrice) {
        this.productMinPrice = productMinPrice;
    }

    /**
     * 商品最大供货价
     *
     * @return
     */
    public BigDecimal getProductMaxPrice() {
        return productMaxPrice;
    }

    /**
     * 商品最大供货价
     *
     * @param productMaxPrice
     */
    public void setProductMaxPrice(BigDecimal productMaxPrice) {
        this.productMaxPrice = productMaxPrice;
    }

    /**
     * 商品总库存
     *
     * @return
     */
    public Integer getTotalStocks() {
        return totalStocks;
    }

    /**
     * 商品总库存
     *
     * @param totalStocks
     */
    public void setTotalStocks(Integer totalStocks) {
        this.totalStocks = totalStocks;
    }

    /**
     * 商品标题
     *
     * @return
     */
    public String getTitle() {
        return title;
    }

    /**
     * 商品标题
     *
     * @param title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 商品审核状态，1.表示审核通过；否则，审核未通过
     *
     * @return
     */
    public Integer getProductAuditStatus() {
        return productAuditStatus;
    }

    /**
     * 商品审核状态，1.表示审核通过；否则，审核未通过
     *
     * @param productAuditStatus
     */
    public void setProductAuditStatus(Integer productAuditStatus) {
        this.productAuditStatus = productAuditStatus;
    }

    /**
     * 审核未通过的原因
     *
     * @return
     */
    public String getAuditFailureType() {
        return auditFailureType;
    }

    /**
     * 审核未通过的原因
     *
     * @param auditFailureType
     */
    public void setAuditFailureType(String auditFailureType) {
        this.auditFailureType = auditFailureType;
    }

    /**
     * 商品主图链接
     *
     * @return
     */
    public String getImageUrls() {
        return imageUrls;
    }

    /**
     * 商品主图链接
     *
     * @param imageUrls
     */
    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrencyCode() {
        return currencyCode;
    }

    /**
     * 币种
     *
     * @param currencyCode
     */
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    /**
     * 商品子类目ID
     *
     * @return
     */
    public String getLeafCategoryId() {
        return leafCategoryId;
    }

    /**
     * 商品子类目ID
     *
     * @param leafCategoryId
     */
    public void setLeafCategoryId(String leafCategoryId) {
        this.leafCategoryId = leafCategoryId;
    }

    /**
     * 商品发货类型：本地商家仓sw ;本地官方仓pw
     *
     * @return
     */
    public String getLocalStockType() {
        return localStockType;
    }

    /**
     * 商品发货类型：本地商家仓sw ;本地官方仓pw
     *
     * @param localStockType
     */
    public void setLocalStockType(String localStockType) {
        this.localStockType = localStockType;
    }

    /**
     * 运费模板名称
     *
     * @return
     */
    public String getShippingTemplate() {
        return shippingTemplate;
    }

    /**
     * 运费模板名称
     *
     * @param shippingTemplate
     */
    public void setShippingTemplate(String shippingTemplate) {
        this.shippingTemplate = shippingTemplate;
    }

    /**
     * 发货国家？猜测的。API文档对不上
     *
     * @return
     */
    public String getLocalShipFrom() {
        return localShipFrom;
    }

    /**
     * 发货国家？猜测的。API文档对不上
     *
     * @param localShipFrom
     */
    public void setLocalShipFrom(String localShipFrom) {
        this.localShipFrom = localShipFrom;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getDownloadTime() {
        return downloadTime;
    }

    /**
     * 下载时间
     *
     * @param downloadTime
     */
    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

}
