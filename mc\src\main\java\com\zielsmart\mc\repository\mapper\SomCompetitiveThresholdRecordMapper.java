package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomCompetitiveThresholdRecordPageSearchVo;
import com.zielsmart.mc.vo.SomCompetitiveThresholdRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-08-12
 */

@SqlResource("somCompetitiveThresholdRecord")
public interface SomCompetitiveThresholdRecordMapper extends BaseMapper<SomCompetitiveThresholdRecord> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult<SomCompetitiveThresholdRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomCompetitiveThresholdRecordVo> queryByPage(@Param("searchVo") SomCompetitiveThresholdRecordPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryByHealthIds
     * 根据主表数据查询子表
     *
     * @param healthIds 主表IDs
     * @return {@link PageResult<SomCompetitiveThresholdRecordVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCompetitiveThresholdRecordVo> queryByHealthIds(@Param("healthIds") List<String> healthIds);
}
