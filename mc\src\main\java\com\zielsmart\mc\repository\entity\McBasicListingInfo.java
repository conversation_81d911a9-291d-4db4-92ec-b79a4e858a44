package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* listing基础信息表
* gen by 代码生成器 2021-06-30
*/

@Table(name="mc.mc_basic_listing_info")
public class McBasicListingInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private Long seqnum ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 平台SKU
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 公司内部SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 平台产品唯一标识
	 */
	@Column("asin_code")
	private String asinCode ;
	/**
	 * 产品名称
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 产品图片地址
	 */
	@Column("image_url")
	private String imageUrl ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 产品状态
	 */
	@Column("status_code")
	private String statusCode ;
	/**
	 * 币种
	 */
	@Column("currency_code")
	private String currencyCode ;

	public McBasicListingInfo() {
	}

	/**
	* 主键
	*@return
	*/
	public Long getSeqnum(){
		return  seqnum;
	}
	/**
	* 主键
	*@param  seqnum
	*/
	public void setSeqnum(Long seqnum ){
		this.seqnum = seqnum;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 平台SKU
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 平台SKU
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 公司内部SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* 公司内部SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 平台产品唯一标识
	*@return
	*/
	public String getAsinCode(){
		return  asinCode;
	}
	/**
	* 平台产品唯一标识
	*@param  asinCode
	*/
	public void setAsinCode(String asinCode ){
		this.asinCode = asinCode;
	}
	/**
	* 产品名称
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 产品名称
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 产品图片地址
	*@return
	*/
	public String getImageUrl(){
		return  imageUrl;
	}
	/**
	* 产品图片地址
	*@param  imageUrl
	*/
	public void setImageUrl(String imageUrl ){
		this.imageUrl = imageUrl;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}
	/**
	* 产品状态
	*@return
	*/
	public String getStatusCode(){
		return  statusCode;
	}
	/**
	* 产品状态
	*@param  statusCode
	*/
	public void setStatusCode(String statusCode ){
		this.statusCode = statusCode;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrencyCode(){
		return  currencyCode;
	}
	/**
	* 币种
	*@param  currencyCode
	*/
	public void setCurrencyCode(String currencyCode ){
		this.currencyCode = currencyCode;
	}

}
