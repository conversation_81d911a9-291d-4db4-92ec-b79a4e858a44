package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomTemuLocalPromotionService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuLocalPromotionController
 * @description
 * @date 2025-03-12 09:03:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuLocalPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu本本营销活动管理")
public class SomTemuLocalPromotionController extends BasicController{

    @Resource
    SomTemuLocalPromotionService somTemuLocalPromotionService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuLocalPromotionVo>> queryByPage(@RequestBody SomTemuLocalPromotionPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuLocalPromotionService.queryByPage(searchVo));
    }

    @Operation(summary = "查看报名产品")
    @PostMapping(value = "/detail/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuLocalPromotionDetailVo>> detailQueryByPage(@RequestBody @Validated SomTemuLocalPromotionDetailPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somTemuLocalPromotionService.detailQueryByPage(searchVo));
    }

    @Operation(summary = "查看推荐产品")
    @PostMapping(value = "/candidate-detail/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuLocalPromotionDetailVo>> detailCandidateQueryByPage(@RequestBody SomTemuLocalPromotionDetailPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somTemuLocalPromotionService.detailCandidateQueryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomTemuLocalPromotionPageSearchVo searchVo) throws ValidateException {
        String data = somTemuLocalPromotionService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
