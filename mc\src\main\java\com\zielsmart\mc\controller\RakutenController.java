package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.RakutenService;
import com.zielsmart.mc.vo.SomRakutenListingPageSearchVo;
import com.zielsmart.mc.vo.SomRakutenListingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version V1.0
 * @title: RakutenController
 * @package: com.zielsmart.mc.controller
 * @description:
 * @author: lv<PERSON><PERSON><PERSON>
 * @date: 2021-04-27 9:19
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/rakuten", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "乐天平台")
public class RakutenController extends BasicController {
    @Resource
    private RakutenService rakutenService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo <  PageVo <  SomRakutenListingVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomRakutenListingVo>> queryByPage(@RequestBody SomRakutenListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(rakutenService.queryByPage(searchVo));
    }

}
