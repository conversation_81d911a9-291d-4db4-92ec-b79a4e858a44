package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.zbpm.ZBPMWareHouseVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ZBPMWareHoseService {

    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseConfigMapper warehouseConfigMapper;
    @Resource
    private McVcWarehouseConfigMapper vcWarehouseConfigMapper;
    @Resource
    private McWayfairWarehouseConfigMapper wayfairWarehouseConfigMapper;
    @Resource
    private McWalmartWarehouseConfigMapper walmartWarehouseConfigMapper;
    @Resource
    private SomSheinWarehouseConfigMapper sheinWarehouseConfigMapper;
    @Resource
    private SomTemuWarehouseConfigMapper temuWarehouseConfigMapper;
    @Resource
    private SomTargetWarehouseConfigMapper targetWarehouseConfigMapper;
    @Resource
    private SomMiraviaWarehouseConfigMapper miraviaWarehouseConfigMapper;
    @Resource
    private SomMiraviaWarehouseMapper miraviaWarehouseMapper;
    @Resource
    private SomTargetWarehouseMapper targetWarehouseMapper;
    @Resource
    private SomSheinWarehouseInfoMapper sheinWarehouseInfoMapper;

    /**
     * getPlatFormSiteList
     * 获取平台站点
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMWareHouseVo>}
     * <AUTHOR>
     * @history
     */
    public List<ZBPMWareHouseVo> getPlatFormSiteList() {
        List<ZBPMWareHouseVo> wareHouseVoList = new ArrayList<>();
        // 全部平台
        List<McDictionaryInfo> platformList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "Platform").select();
        List<String> platforms = platformList.stream().map(x -> x.getItemValue()).distinct().collect(Collectors.toList());
        // 全部站点
        List<McDictionaryInfo> siteList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", platforms).select();
        // 匹配市场
        platforms.forEach(platform -> {
            ZBPMWareHouseVo wareHouseVo = new ZBPMWareHouseVo();
            wareHouseVo.setPlatform(platform);
            List<String> sites = new ArrayList<>();
            siteList.stream().forEach(obj -> {
                if (obj.getItemTypeCode().equalsIgnoreCase(platform)) {
                    sites.add(obj.getItemValue());
                }
            });
            wareHouseVo.setSiteList(sites);
            wareHouseVoList.add(wareHouseVo);
        });
        return wareHouseVoList;
    }

    /**
     * queryByplatFormSite
     * 根据平台站点查询仓库信息
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.zbpm.ZBPMWareHouseVo}
     * <AUTHOR>
     * @history
     */
    public ZBPMWareHouseVo queryByplatFormSite(ZBPMWareHouseVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getPlatform()) || StrUtil.isBlank(searchVo.getSite())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        ZBPMWareHouseVo resultVo = new ZBPMWareHouseVo();
        resultVo.setPlatform(searchVo.getPlatform());
        resultVo.setSite(searchVo.getSite());
        // 查询市场
        McDictionaryInfo dict = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", searchVo.getPlatform()).andEq("item_lable", searchVo.getSite()).single();
        if (ObjectUtil.isNotEmpty(dict)) {
            resultVo.setMarketCode(dict.getItemValue2());
            resultVo.setMarketName(dict.getItemValue1());
        }
        List<SomStorageLocation> locationList = somStorageLocationMapper.createLambdaQuery().select("wh_code", "sl_code", "sl_name");
        List<McWarehouse> allWarehouseList = dynamicSqlManager.getMapper(McWarehouseMapper.class).createLambdaQuery().distinct().select("warehouse_code", "warehouse_name");
        resultVo.setAllWarehouseList(ConvertUtils.listConvert(allWarehouseList, ZBPMWareHouseVo.WareHouseVo.class));
        for (ZBPMWareHouseVo.WareHouseVo obj : resultVo.getAllWarehouseList()) {
            List<SomStorageLocation> tempList = locationList.stream().filter(f -> StrUtil.equals(obj.getWarehouseCode(), f.getwhCode())).collect(Collectors.toList());
            obj.setSlList(ConvertUtils.listConvert(tempList, ZBPMWareHouseVo.StorageLocationVo.class));
        }
        switch (searchVo.getPlatform()) {
            case "VC":
                List<McVcWarehouseConfig> vcConfigured = vcWarehouseConfigMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).select();
                Map<String, List<McVcWarehouseConfig>> vcMap = vcConfigured.stream().collect(Collectors.groupingBy(p -> p.getPlatformWarehouseCode() + "," + p.getWarehouseCode()+","+p.getVendorCode()));
                List<ZBPMWareHouseVo.WareHouseVo> vcConfiguredList = new ArrayList<>();
                for (String code : vcMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String platformWarehouseCode = split[0];
                    temp.setPlatformWarehouseCode(platformWarehouseCode);
                    String warehouseCode = split[1];
                    temp.setWarehouseCode(warehouseCode);
                    temp.setVendorCode(split[2]);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<McVcWarehouseConfig> collect = vcConfigured.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())  && StrUtil.equals(platformWarehouseCode, f.getPlatformWarehouseCode())).collect(Collectors.toList());
                    for (McVcWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUsableStorageLocation());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    vcConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(vcConfiguredList);
                break;
            case "Wayfair":
                List<McWayfairWarehouseConfig> wayfairConfigured = wayfairWarehouseConfigMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).select();
                Map<String, List<McWayfairWarehouseConfig>> wayfairMap = wayfairConfigured.stream().collect(Collectors.groupingBy(p -> p.getPlatformWarehouseCode() + "," + p.getWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> wayfairConfiguredList = new ArrayList<>();
                for (String code : wayfairMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String platformWarehouseCode = split[0];
                    temp.setPlatformWarehouseCode(platformWarehouseCode);
                    String warehouseCode = split[1];
                    temp.setWarehouseCode(warehouseCode);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<McWayfairWarehouseConfig> collect = wayfairConfigured.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode()) && StrUtil.equals(platformWarehouseCode, f.getPlatformWarehouseCode())).collect(Collectors.toList());
                    for (McWayfairWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUsableStorageLocation());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    wayfairConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(wayfairConfiguredList);
                break;
            case "Walmart":
                List<McWalmartWarehouseConfig> walmartConfigured = walmartWarehouseConfigMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).select();
                Map<String, List<McWalmartWarehouseConfig>> walmartMap = walmartConfigured.stream().collect(Collectors.groupingBy(p -> p.getWarehouseAreaCode() + "," + p.getWarehouseAreaName() + "," + p.getWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> walmartConfiguredList = new ArrayList<>();
                for (String code : walmartMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String warehouseAreaCode = split[0];
                    temp.setWarehouseAreaCode(split[0]);
                    temp.setWarehouseAreaName(split[1]);
                    String warehouseCode = split[2];
                    temp.setWarehouseCode(warehouseCode);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<McWalmartWarehouseConfig> collect = walmartConfigured.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode()) && StrUtil.equals(warehouseAreaCode, f.getWarehouseAreaCode())).collect(Collectors.toList());
                    for (McWalmartWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUsableStorageLocation());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    walmartConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(walmartConfiguredList);
                break;
            case "Shein":
                List<SomSheinWarehouseConfig> sheinConfigured = sheinWarehouseConfigMapper.all();
                Map<String, List<SomSheinWarehouseConfig>> sheinMap = sheinConfigured.stream().collect(Collectors.groupingBy(p -> p.getWarehouseCode() + "," + p.getUseableWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> sheinConfiguredList = new ArrayList<>();
                for (String code : sheinMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String platformWarehouseCode = split[0];
                    temp.setPlatformWarehouseCode(platformWarehouseCode);
                    String warehouseCode = split[1];
                    temp.setWarehouseCode(warehouseCode);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<SomSheinWarehouseConfig> collect = sheinMap.get(code);
                    temp.setPlatformWarehouseName(collect.get(0).getWarehouseName());
                    for (SomSheinWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUseableStorageCode());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    sheinConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(sheinConfiguredList);
                List<SomSheinWarehouseInfo> sheinWarehouseInfoList=sheinWarehouseInfoMapper.all();
                List<ZBPMWareHouseVo.PlatformWareHouseVo> platformWareHouseVoList=new ArrayList<>();
                for (SomSheinWarehouseInfo somSheinWarehouseInfo : sheinWarehouseInfoList) {
                    ZBPMWareHouseVo.PlatformWareHouseVo platformWareHouseVo=new ZBPMWareHouseVo.PlatformWareHouseVo();
                    platformWareHouseVo.setPlatformWarehouseCode(somSheinWarehouseInfo.getWarehouseCode());
                    platformWareHouseVo.setPlatformWarehouseName(somSheinWarehouseInfo.getWarehouseName());
                    platformWareHouseVoList.add(platformWareHouseVo);
                }
                resultVo.setPlatformWareHouseVoList(platformWareHouseVoList);
                break;
            case "Target":
                List<SomTargetWarehouseConfig> targetConfigured = targetWarehouseConfigMapper.all();
                Map<String, List<SomTargetWarehouseConfig>> targetMap = targetConfigured.stream().collect(Collectors.groupingBy(p -> p.getWarehouseCode() + "," + p.getUseableWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> targetConfiguredList = new ArrayList<>();
                for (String code : targetMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String platformWarehouseCode = split[0];
                    temp.setPlatformWarehouseCode(platformWarehouseCode);
                    String warehouseCode = split[1];
                    temp.setWarehouseCode(warehouseCode);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<SomTargetWarehouseConfig> collect =targetMap.get(code);
                    temp.setPlatformWarehouseName(collect.get(0).getWarehouseName());
                    for (SomTargetWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUseableStorageCode());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    targetConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(targetConfiguredList);
                List<SomTargetWarehouse> targetWarehouseInfoList=targetWarehouseMapper.all();
                List<ZBPMWareHouseVo.PlatformWareHouseVo> targetPlatformWareHouseVoList=new ArrayList<>();
                for (SomTargetWarehouse targetWarehouse : targetWarehouseInfoList) {
                    ZBPMWareHouseVo.PlatformWareHouseVo platformWareHouseVo=new ZBPMWareHouseVo.PlatformWareHouseVo();
                    platformWareHouseVo.setPlatformWarehouseCode(targetWarehouse.getWarehouseCode());
                    platformWareHouseVo.setPlatformWarehouseName(targetWarehouse.getWarehouseName());
                    targetPlatformWareHouseVoList.add(platformWareHouseVo);
                }
                resultVo.setPlatformWareHouseVoList(targetPlatformWareHouseVoList);
                break;
            case "Miravia":
                List<SomMiraviaWarehouseConfig> miraviaConfigured = miraviaWarehouseConfigMapper.all();
                Map<String, List<SomMiraviaWarehouseConfig>> miraviaMap = miraviaConfigured.stream().collect(Collectors.groupingBy(p -> p.getWarehouseCode() + "," + p.getUseableWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> miraviaConfiguredList = new ArrayList<>();
                for (String code : miraviaMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    String[] split = code.split(",");
                    String platformWarehouseCode = split[0];
                    temp.setPlatformWarehouseCode(platformWarehouseCode);
                    String warehouseCode = split[1];
                    temp.setWarehouseCode(warehouseCode);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(warehouseCode, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<SomMiraviaWarehouseConfig> collect =miraviaMap.get(code);
                    temp.setPlatformWarehouseName(collect.get(0).getWarehouseName());
                    for (SomMiraviaWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUseableStorageCode());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), warehouseCode) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    miraviaConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(miraviaConfiguredList);
                List<SomMiraviaWarehouse> miraviaWarehouseInfoList=miraviaWarehouseMapper.all();
                List<ZBPMWareHouseVo.PlatformWareHouseVo> miraviaPlatformWareHouseVoList=new ArrayList<>();
                for (SomMiraviaWarehouse miraviaWarehouse : miraviaWarehouseInfoList) {
                    ZBPMWareHouseVo.PlatformWareHouseVo platformWareHouseVo=new ZBPMWareHouseVo.PlatformWareHouseVo();
                    platformWareHouseVo.setPlatformWarehouseCode(miraviaWarehouse.getWarehouseCode());
                    platformWareHouseVo.setPlatformWarehouseName(miraviaWarehouse.getWarehouseName());
                    miraviaPlatformWareHouseVoList.add(platformWareHouseVo);
                }
                resultVo.setPlatformWareHouseVoList(miraviaPlatformWareHouseVoList);
                break;
            case "Temu":
                List<SomTemuWarehouseConfig> temuConfigured = temuWarehouseConfigMapper.createLambdaQuery().andEq("site", searchVo.getSite()).select();
                Map<String, List<SomTemuWarehouseConfig>> temuMap = temuConfigured.stream().collect(Collectors.groupingBy(p -> p.getUseableWarehouseCode()));
                List<ZBPMWareHouseVo.WareHouseVo> temuConfiguredList = new ArrayList<>();
                for (String code : temuMap.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    temp.setWarehouseCode(code);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(code, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<SomTemuWarehouseConfig> collect = temuMap.get(code);
                    for (SomTemuWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUseableStorageCode());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), code) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    temuConfiguredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(temuConfiguredList);
                break;
            default:
                List<McWarehouseConfig> configured = warehouseConfigMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).select();
                Map<String, List<McWarehouseConfig>> map = configured.stream().collect(Collectors.groupingBy(p -> p.getUsableWarehouse()));
                List<ZBPMWareHouseVo.WareHouseVo> configuredList = new ArrayList<>();
                for (String code : map.keySet()) {
                    List<ZBPMWareHouseVo.StorageLocationVo> slList = new ArrayList<>();
                    ZBPMWareHouseVo.WareHouseVo temp = new ZBPMWareHouseVo.WareHouseVo();
                    temp.setWarehouseCode(code);
                    allWarehouseList.stream().filter(f -> StrUtil.equals(code, f.getWarehouseCode())).findFirst().ifPresent(ps -> {
                        temp.setWarehouseName(ps.getWarehouseName());
                    });
                    List<McWarehouseConfig> collect = configured.stream().filter(f -> StrUtil.equals(code, f.getUsableWarehouse())).collect(Collectors.toList());
                    for (McWarehouseConfig config : collect) {
                        ZBPMWareHouseVo.StorageLocationVo obj = new ZBPMWareHouseVo.StorageLocationVo();
                        obj.setSlCode(config.getUsableStorageLocation());
                        locationList.stream().filter(f -> StrUtil.equals(f.getwhCode(), code) && StrUtil.equals(f.getslCode(), obj.getSlCode())).findFirst().ifPresent(ps -> {
                            obj.setSlName(ps.getslName());
                        });
                        slList.add(obj);
                    }
                    temp.setSlList(slList);
                    configuredList.add(temp);
                }
                resultVo.setConfiguredWarehouseList(configuredList);
                break;
        }
        return resultVo;
    }

    /**
     * updateWhcodeSlcode
     * 更新仓库库区信息
     *
     * @param updateVo
     * <AUTHOR>
     * @history
     */
    public void updateWhcodeSlcode(ZBPMWareHouseVo updateVo, TokenUserInfo tokenUserInfo) throws ValidateException {
        if (ObjectUtil.isEmpty(updateVo) || StrUtil.isBlank(updateVo.getPlatform()) || StrUtil.isBlank(updateVo.getSite()) || CollectionUtil.isEmpty(updateVo.getConfiguredWarehouseChangeList())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        switch (updateVo.getPlatform()) {
            case "VC":
                vcWarehouseConfigMapper.createLambdaQuery().andEq("platform",updateVo.getPlatform()).andEq("site",updateVo.getSite()).delete();
                List<McVcWarehouseConfig> vcList = new ArrayList<>();
                //「站点」+「供应商编码」+「平台仓库编码」唯一
                Map<String, List<ZBPMWareHouseVo.WareHouseVo>> listMap = updateVo.getConfiguredWarehouseChangeList().stream().collect(Collectors.groupingBy(e -> e.getPlatformWarehouseCode()+e.getVendorCode()+e.getWarehouseCode()));
                for (String key:listMap.keySet()){
                    List<ZBPMWareHouseVo.WareHouseVo> tempList=listMap.get(key);
                    ZBPMWareHouseVo.WareHouseVo whVo=tempList.get(0);
                    if (tempList.size()>1){
                        throw new ValidateException("平台：%s，站点：%s,供应商编码：%s,平台仓库编码:%s,自发仓编码：%s,存在重复数据请检查！",updateVo.getPlatform(),updateVo.getSite(),whVo.getVendorCode(),whVo.getPlatformWarehouseCode(),whVo.getWarehouseCode());
                    }
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        McVcWarehouseConfig vc = new McVcWarehouseConfig();
                        vc.setAid(IdUtil.fastUUID());
                        vc.setPlatform(updateVo.getPlatform());
                        vc.setSite(updateVo.getSite());
                        vc.setPlatformWarehouseCode(whVo.getPlatformWarehouseCode());
                        vc.setWarehouseCode(whVo.getWarehouseCode());
                        vc.setUsableStorageLocation(slVo.getSlCode());
                        vc.setCreateName(tokenUserInfo.getUserName());
                        vc.setCreateNum(tokenUserInfo.getJobNumber());
                        vc.setCreateTime(DateTime.now().toJdkDate());
                        vc.setVendorCode(whVo.getVendorCode());
                        vcList.add(vc);
                    }
                }
//                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
//                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
//                        McVcWarehouseConfig vc = new McVcWarehouseConfig();
//                        vc.setAid(IdUtil.fastUUID());
//                        vc.setPlatform(updateVo.getPlatform());
//                        vc.setSite(updateVo.getSite());
//                        vc.setPlatformWarehouseCode(whVo.getPlatformWarehouseCode());
//                        vc.setWarehouseCode(whVo.getWarehouseCode());
//                        vc.setUsableStorageLocation(slVo.getSlCode());
//                        vc.setCreateName(tokenUserInfo.getUserName());
//                        vc.setCreateNum(tokenUserInfo.getJobNumber());
//                        vc.setCreateTime(DateTime.now().toJdkDate());
//                        vc.setVendorCode(whVo.getVendorCode());
//                        vcList.add(vc);
//                    }
//                }
                vcWarehouseConfigMapper.insertBatch(vcList);
                break;
            case "Wayfair":
                wayfairWarehouseConfigMapper.createLambdaQuery().andEq("platform",updateVo.getPlatform()).andEq("site",updateVo.getSite()).delete();
                List<McWayfairWarehouseConfig> wayfairList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        McWayfairWarehouseConfig wayfair = new McWayfairWarehouseConfig();
                        wayfair.setAid(IdUtil.fastUUID());
                        wayfair.setPlatform(updateVo.getPlatform());
                        wayfair.setSite(updateVo.getSite());
                        wayfair.setPlatformWarehouseCode(whVo.getPlatformWarehouseCode());
                        wayfair.setWarehouseCode(whVo.getWarehouseCode());
                        wayfair.setUsableStorageLocation(slVo.getSlCode());
                        wayfair.setCreateName(tokenUserInfo.getUserName());
                        wayfair.setCreateNum(tokenUserInfo.getJobNumber());
                        wayfair.setCreateTime(DateTime.now().toJdkDate());
                        wayfairList.add(wayfair);
                    }
                }
                wayfairWarehouseConfigMapper.insertBatch(wayfairList);
                break;
            case "Walmart":
                walmartWarehouseConfigMapper.createLambdaQuery().andEq("platform",updateVo.getPlatform()).andEq("site",updateVo.getSite()).delete();
                List<McWalmartWarehouseConfig> walmartList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        McWalmartWarehouseConfig walmart = new McWalmartWarehouseConfig();
                        walmart.setAid(IdUtil.fastUUID());
                        walmart.setPlatform(updateVo.getPlatform());
                        walmart.setSite(updateVo.getSite());
                        walmart.setWarehouseAreaCode(whVo.getWarehouseAreaCode());
                        walmart.setWarehouseAreaName(whVo.getWarehouseAreaName());
                        walmart.setWarehouseCode(whVo.getWarehouseCode());
                        walmart.setUsableStorageLocation(slVo.getSlCode());
                        walmart.setCreateName(tokenUserInfo.getUserName());
                        walmart.setCreateNum(tokenUserInfo.getJobNumber());
                        walmart.setCreateTime(DateTime.now().toJdkDate());
                        walmartList.add(walmart);
                    }
                }
                walmartWarehouseConfigMapper.insertBatch(walmartList);
                break;
            case "Shein":
                sheinWarehouseConfigMapper.createLambdaQuery().delete();
                List<SomSheinWarehouseConfig> sheinList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        SomSheinWarehouseConfig shein = new SomSheinWarehouseConfig();
                        shein.setAid(IdUtil.fastUUID());
                        shein.setWarehouseCode(whVo.getPlatformWarehouseCode());
                        shein.setWarehouseName(whVo.getPlatformWarehouseName());
                        shein.setUseableWarehouseCode(whVo.getWarehouseCode());
                        shein.setUseableStorageCode(slVo.getSlCode());
                        shein.setCreateName(tokenUserInfo.getUserName());
                        shein.setCreateNum(tokenUserInfo.getJobNumber());
                        shein.setCreateTime(DateTime.now().toJdkDate());
                        sheinList.add(shein);
                    }
                }
                sheinWarehouseConfigMapper.insertBatch(sheinList);
                break;
            case "Target":
                targetWarehouseConfigMapper.createLambdaQuery().delete();
                List<SomTargetWarehouseConfig> targetList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        SomTargetWarehouseConfig target = new SomTargetWarehouseConfig();
                        target.setAid(IdUtil.fastUUID());
                        target.setWarehouseCode(whVo.getPlatformWarehouseCode());
                        target.setWarehouseName(whVo.getPlatformWarehouseName());
                        target.setUseableWarehouseCode(whVo.getWarehouseCode());
                        target.setUseableStorageCode(slVo.getSlCode());
                        target.setCreateName(tokenUserInfo.getUserName());
                        target.setCreateNum(tokenUserInfo.getJobNumber());
                        target.setCreateTime(DateTime.now().toJdkDate());
                        targetList.add(target);
                    }
                }
                targetWarehouseConfigMapper.insertBatch(targetList);
                break;
            case "Miravia":
                miraviaWarehouseConfigMapper.createLambdaQuery().delete();
                List<SomMiraviaWarehouseConfig> miraviaList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        SomMiraviaWarehouseConfig miravia = new SomMiraviaWarehouseConfig();
                        miravia.setAid(IdUtil.fastUUID());
                        miravia.setWarehouseCode(whVo.getPlatformWarehouseCode());
                        miravia.setWarehouseName(whVo.getPlatformWarehouseName());
                        miravia.setUseableWarehouseCode(whVo.getWarehouseCode());
                        miravia.setUseableStorageCode(slVo.getSlCode());
                        miravia.setCreateName(tokenUserInfo.getUserName());
                        miravia.setCreateNum(tokenUserInfo.getJobNumber());
                        miravia.setCreateTime(DateTime.now().toJdkDate());
                        miraviaList.add(miravia);
                    }
                }
                miraviaWarehouseConfigMapper.insertBatch(miraviaList);
                break;
            case "Temu":
                temuWarehouseConfigMapper.createLambdaQuery().andEq("site",updateVo.getSite()).delete();
                List<SomTemuWarehouseConfig> temuList = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        SomTemuWarehouseConfig temu = new SomTemuWarehouseConfig();
                        temu.setAid(IdUtil.fastUUID());
                        temu.setSite(updateVo.getSite());
                        temu.setUseableWarehouseCode(whVo.getWarehouseCode());
                        temu.setUseableStorageCode(slVo.getSlCode());
                        temu.setCreateName(tokenUserInfo.getUserName());
                        temu.setCreateNum(tokenUserInfo.getJobNumber());
                        temu.setCreateTime(DateTime.now().toJdkDate());
                        temuList.add(temu);
                    }
                }
                temuWarehouseConfigMapper.insertBatch(temuList);
                break;
            default:
                warehouseConfigMapper.createLambdaQuery().andEq("platform",updateVo.getPlatform()).andEq("site",updateVo.getSite()).delete();
                List<McWarehouseConfig> list = new ArrayList<>();
                for(ZBPMWareHouseVo.WareHouseVo whVo :updateVo.getConfiguredWarehouseChangeList()){
                    for(ZBPMWareHouseVo.StorageLocationVo slVo : whVo.getSlList()){
                        McWarehouseConfig config = new McWarehouseConfig();
                        config.setAid(IdUtil.fastUUID());
                        config.setPlatform(updateVo.getPlatform());
                        config.setSite(updateVo.getSite());
                        config.setUsableWarehouse(whVo.getWarehouseCode());
                        config.setUsableStorageLocation(slVo.getSlCode());
                        config.setCreateName(tokenUserInfo.getUserName());
                        config.setCreateNum(tokenUserInfo.getJobNumber());
                        config.setCreateTime(DateTime.now().toJdkDate());
                        list.add(config);
                    }
                }
                warehouseConfigMapper.insertBatch(list);
                break;
        }
    }
}
