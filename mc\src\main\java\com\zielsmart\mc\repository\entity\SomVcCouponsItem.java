package com.zielsmart.mc.repository.entity;

import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;

@Data
@Table(name = "mc.som_vc_coupons_item")
public class SomVcCouponsItem {

    @AssignID
    private String aid;

    @Column("coupon_aid")
    private String couponAid;

    @Column("seller_sku")
    private String sellerSku;

    @Column("asin")
    private String asin;

    @Column("sku")
    private String sku;

    @Column("sales_group_code")
    private String salesGroupCode;

    @Column("sales_group_name")
    private String salesGroupName;

    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;

    @Column("sales_group_empt_name")
    private String salesGroupEmptName;

    @Column("operation_empt_code")
    private String operationEmptCode;

    @Column("operation_empt_name")
    private String operationEmptName;

    @Column("recommended_retail_price")
    private BigDecimal recommendedRetailPrice;

    @Column("per_unit_funding")
    private BigDecimal perUnitFunding;

    @Column("currency")
    private String currency;

}
