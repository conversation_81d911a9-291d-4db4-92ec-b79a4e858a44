package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* LD&7DD主表
* gen by 代码生成器 2022-08-11
*/

@Table(name="mc.som_free_add_deal")
public class SomFreeAddDeal implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;

	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 秒杀类型 10.Lighting Deal 20.7 Day Deal
	 */
	@Column("deal_type")
	private Integer dealType ;
	/**
	 * 活动命名
	 */
	@Column("internal_description")
	private String internalDescription ;
	/**
	 * 0.非综合单 1.综合单
	 */
	@Column("variations_flag")
	private Integer variationsFlag ;
	/**
	 * 状态10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始  41.需要关注 50.取消中 51.已取消 59.取消失败  70.进行中 80.已结束  110.运营已提报
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 修改状态10.修改中 20.修改成功 30.修改失败
	 */
	@Column("modify_status")
	private Integer modifyStatus ;
	/**
	 * 活动提报开始日期
	 */
	@Column("plan_start_date")
	private Date planStartDate ;
	/**
	 * 活动提报截止日期
	 */
	@Column("plan_end_date")
	private Date planEndDate ;
	/**
	 * 活动实际开始日期
	 */
	@Column("real_start_date")
	private Date realStartDate ;
	/**
	 * 活动实际截止日期
	 */
	@Column("real_end_date")
	private Date realEndDate ;
	/**
	 * 修改原因
	 */
	@Column("modify_remark")
	private String modifyRemark ;
	/**
	 * 修改失败原因
	 */
	@Column("modify_failure_remark")
	private String modifyFailureRemark ;
	/**
	 * 取消原因
	 */
	@Column("cancel_remark")
	private String cancelRemark ;
	/**
	 * 取消失败原因
	 */
	@Column("cacenl_failure_remark")
	private String cacenlFailureRemark ;
	/**
	 * 审核失败原因
	 */
	@Column("audit_failure_remark")
	private String auditFailureRemark ;
	/**
	 * 申请原因 10.提升排名 20.清货 30.稳排名 99.自定义
	 */
	@Column("apply_reason")
	private Integer applyReason ;
	/**
	 * 自定义原因
	 */
	@Column("cutomer_reason")
	private String cutomerReason ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum  ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	/**
	 * 币种
	 */
	@Column("currency_code")
	private String currencyCode ;

	/**
	 * 大促类型
	 * Prime Day Window	1
	 * Prime Fall Deal Event	2
	 * BFCM Window	3
	 * Black Friday Window	4
	 * Cyber Monday Window	5
	 */
	@Column("campaign_type")
	private Integer campaignType ;
	/**
	 * 提交时间
	 */
	@Column("submit_time")
	private Date submitTime;
	/**
	 * 审批时间
	 */
	@Column("audit_time")
	private Date auditTime;
	/**
	 * 审批角色 审批分支
	 */
	@Column("approval_role")
	private String approvalRole;
	/**
	 * 预计活动费用
	 */
	@Column("expected_activity_expense")
	private BigDecimal expectedActivityExpense;


	public SomFreeAddDeal() {
	}

	public BigDecimal getExpectedActivityExpense() {
		return expectedActivityExpense;
	}

	public void setExpectedActivityExpense(BigDecimal expectedActivityExpense) {
		this.expectedActivityExpense = expectedActivityExpense;
	}

	public String getApprovalRole() {
		return approvalRole;
	}

	public void setApprovalRole(String approvalRole) {
		this.approvalRole = approvalRole;
	}

	public Integer getCampaignType() {
		return campaignType;
	}

	public void setCampaignType(Integer campaignType) {
		this.campaignType = campaignType;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 秒杀类型 10.Lighting Deal 20.7 Day Deal
	*@return
	*/
	public Integer getDealType(){
		return  dealType;
	}
	/**
	* 秒杀类型 10.Lighting Deal 20.7 Day Deal
	*@param  dealType
	*/
	public void setDealType(Integer dealType ){
		this.dealType = dealType;
	}
	/**
	* 活动命名
	*@return
	*/
	public String getInternalDescription(){
		return  internalDescription;
	}
	/**
	* 活动命名
	*@param  internalDescription
	*/
	public void setInternalDescription(String internalDescription ){
		this.internalDescription = internalDescription;
	}
	/**
	* 0.非综合单 1.综合单
	*@return
	*/
	public Integer getVariationsFlag(){
		return  variationsFlag;
	}
	/**
	* 0.非综合单 1.综合单
	*@param  variationsFlag
	*/
	public void setVariationsFlag(Integer variationsFlag ){
		this.variationsFlag = variationsFlag;
	}
	/**
	* 状态10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始  41.需要关注 50.取消中 51.已取消 59.取消失败  70.进行中 80.已结束
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态10.草稿 20.审批中 21.审批通过 29.审批未通过 30.提报成功 39.提报失败 40.未开始 50.取消中 51.已取消 59.取消失败  70.进行中 80.已结束
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 修改状态10.修改中 20.修改成功 30.修改失败
	*@return
	*/
	public Integer getModifyStatus(){
		return  modifyStatus;
	}
	/**
	* 修改状态10.修改中 20.修改成功 30.修改失败
	*@param  modifyStatus
	*/
	public void setModifyStatus(Integer modifyStatus ){
		this.modifyStatus = modifyStatus;
	}
	/**
	* 活动提报开始日期
	*@return
	*/
	public Date getPlanStartDate(){
		return  planStartDate;
	}
	/**
	* 活动提报开始日期
	*@param  planStartDate
	*/
	public void setPlanStartDate(Date planStartDate ){
		this.planStartDate = planStartDate;
	}
	/**
	* 活动提报截止日期
	*@return
	*/
	public Date getPlanEndDate(){
		return  planEndDate;
	}
	/**
	* 活动提报截止日期
	*@param  planEndDate
	*/
	public void setPlanEndDate(Date planEndDate ){
		this.planEndDate = planEndDate;
	}
	/**
	* 活动实际开始日期
	*@return
	*/
	public Date getRealStartDate(){
		return  realStartDate;
	}
	/**
	* 活动实际开始日期
	*@param  realStartDate
	*/
	public void setRealStartDate(Date realStartDate ){
		this.realStartDate = realStartDate;
	}
	/**
	* 活动实际截止日期
	*@return
	*/
	public Date getRealEndDate(){
		return  realEndDate;
	}
	/**
	* 活动实际截止日期
	*@param  realEndDate
	*/
	public void setRealEndDate(Date realEndDate ){
		this.realEndDate = realEndDate;
	}
	/**
	* 修改原因
	*@return
	*/
	public String getModifyRemark(){
		return  modifyRemark;
	}
	/**
	* 修改原因
	*@param  modifyRemark
	*/
	public void setModifyRemark(String modifyRemark ){
		this.modifyRemark = modifyRemark;
	}
	/**
	* 修改失败原因
	*@return
	*/
	public String getModifyFailureRemark(){
		return  modifyFailureRemark;
	}
	/**
	* 修改失败原因
	*@param  modifyFailureRemark
	*/
	public void setModifyFailureRemark(String modifyFailureRemark ){
		this.modifyFailureRemark = modifyFailureRemark;
	}
	/**
	* 取消原因
	*@return
	*/
	public String getCancelRemark(){
		return  cancelRemark;
	}
	/**
	* 取消原因
	*@param  cancelRemark
	*/
	public void setCancelRemark(String cancelRemark ){
		this.cancelRemark = cancelRemark;
	}
	/**
	* 取消失败原因
	*@return
	*/
	public String getCacenlFailureRemark(){
		return  cacenlFailureRemark;
	}
	/**
	* 取消失败原因
	*@param  cacenlFailureRemark
	*/
	public void setCacenlFailureRemark(String cacenlFailureRemark ){
		this.cacenlFailureRemark = cacenlFailureRemark;
	}
	/**
	* 审核失败原因
	*@return
	*/
	public String getAuditFailureRemark(){
		return  auditFailureRemark;
	}
	/**
	* 审核失败原因
	*@param  auditFailureRemark
	*/
	public void setAuditFailureRemark(String auditFailureRemark ){
		this.auditFailureRemark = auditFailureRemark;
	}
	/**
	* 申请原因 10.提升排名 20.清货 30.稳排名 99.自定义
	*@return
	*/
	public Integer getApplyReason(){
		return  applyReason;
	}
	/**
	* 申请原因 10.提升排名 20.清货 30.稳排名 99.自定义
	*@param  applyReason
	*/
	public void setApplyReason(Integer applyReason ){
		this.applyReason = applyReason;
	}
	/**
	* 自定义原因
	*@return
	*/
	public String getCutomerReason(){
		return  cutomerReason;
	}
	/**
	* 自定义原因
	*@param  cutomerReason
	*/
	public void setCutomerReason(String cutomerReason ){
		this.cutomerReason = cutomerReason;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum (){
		return  createNum ;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum (String createNum  ){
		this.createNum  = createNum ;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 最后修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 最后修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}
}
