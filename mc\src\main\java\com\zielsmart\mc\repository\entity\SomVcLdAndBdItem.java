package com.zielsmart.mc.repository.entity;

import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;

@Data
@Table(name = "mc.som_vc_ld_and_bd_item")
public class SomVcLdAndBdItem {

    @AssignID
    private String aid;

    @Column("deal_id")
    private String dealId;

    @Column("seller_sku")
    private String sellerSku;

    @Column("asin")
    private String asin;

    @Column("sku")
    private String sku;

    /**
     * 建议零售价，RRP
     */
    @Column("recommended_retail_price")
    private BigDecimal recommendedRetailPrice;

    /**
     * 供货价
     */
    @Column("cost_price")
    private BigDecimal costPrice;

    /**
     * 折扣百分比
     */
    @Column("discount")
    private BigDecimal discount;

    /**
     * 单个产品预估折扣金额
     */
    @Column("per_unit_funding")
    private BigDecimal perUnitFunding;

    /**
     * 预估成交价格
     */
    @Column("likely_promotional_price")
    private BigDecimal likelyPromotionalPrice;

    /**
     * 币种
     */
    @Column("currency")
    private String currency;

    /**
     * 承诺商品数量
     */
    @Column("quantity")
    private Integer quantity;

    /**
     * 评分
     */
    @Column("rating")
    private BigDecimal rating;

    /**
     * 库存可销售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;

    /**
     * 30天dms
     */
    @Column("month_dms")
    private BigDecimal monthDms;

    /**
     * 业务组
     */
    @Column("sales_group_code")
    private String salesGroupCode;

    /**
     * 业务组名称
     */
    @Column("sales_group_name")
    private String salesGroupName;

    /**
     * 销售负责人工号
     */
    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;

    /**
     * 销售负责人姓名
     */
    @Column("sales_group_empt_name")
    private String salesGroupEmptName;

    /**
     * 业务助理工号
     */
    @Column("operation_empt_code")
    private String operationEmptCode;

    /**
     * 业务助理姓名
     */
    @Column("operation_empt_name")
    private String operationEmptName;

}
