package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomThreePlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-11-13
*/

@SqlResource("somThreePlatformPrice")
public interface SomThreePlatformPriceMapper extends BaseMapper<SomThreePlatformPrice> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomThreePlatformPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomThreePlatformPriceVo> queryByPage(@Param("searchVo")SomThreePlatformPricePageSearchVo searchVo, PageRequest pageRequest);
}
