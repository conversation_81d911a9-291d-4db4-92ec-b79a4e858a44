package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomUsInventoryReportService
 * @description
 * @date 2024-07-26 15:17:14
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomUsInventoryReportService {

    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    SomUsSfpProductViewMapper sfpMapper;
    @Resource
    private McProductSalesMapper mcProductSalesMapper;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private McPlatformPropertiesMapper mcPlatformPropertiesMapper;
    @Resource
    private McWarehouseConfigMapper mcWarehouseConfigMapper;
    @Resource
    private McDearanceProductMapper mcDearanceProductMapper;
    @Resource
    private McPushStockWhiteListMapper mcPushStockWhiteListMapper;
    @Resource
    private SomSpreetailSkuMapper somSpreetailSkuMapper;
    @Resource
    private SomSpreetailInventoryItemsMapper somSpreetailInventoryItemsMapper;
    @Resource
    private SomSupplySourceStoreMapper somSupplySourceStoreMapper;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;


    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomUsInventoryReportVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public List<SomUsInventoryReportVo> queryByPage(McSaleInventoryReportSearchVo searchVo) throws ValidateException {
        searchVo.setPlatform("Amazon");
        searchVo.setSite("Amazon.com");
        List<SomUsInventoryReportVo> resultList = new ArrayList<>();
        List<McSaleInventoryReportVo> sfpResultList = new ArrayList<>();
        List<McSaleInventoryReportVo> notSfpResultList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(searchVo.getSfpFlag())) {
            if (ObjectUtil.equal(1, searchVo.getSfpFlag())) {
                sfpResultList = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryNaSFPProduct(searchVo);
                if (CollectionUtil.isNotEmpty(sfpResultList)) {
                    resultList = handleSfp(convertList(sfpResultList));
                }
            } else {
                notSfpResultList = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryNotNaSFPProduct(searchVo);
                if (CollectionUtil.isNotEmpty(notSfpResultList)) {
                    resultList = handleNotSfp(convertList(notSfpResultList));
                }
            }
        } else {
            sfpResultList = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryNaSFPProduct(searchVo);
            if (CollectionUtil.isNotEmpty(sfpResultList)) {
                resultList.addAll(handleSfp(convertList(sfpResultList)));
            }
            notSfpResultList = dynamicSqlManager.getMapper(McProductSalesMapper.class).queryNotNaSFPProduct(searchVo);
            if (CollectionUtil.isNotEmpty(notSfpResultList)) {
                resultList.addAll(handleNotSfp(convertList(notSfpResultList)));
            }
        }
        //查询白名单列表
        List<McPushStockWhiteList> whiteList = mcPushStockWhiteListMapper.all();
        Map<String, McPushStockWhiteList> whiteMap = whiteList.stream().collect(Collectors.toMap(k -> k.getPlatform() + k.getSite() + k.getSellerSku(), v -> v));
        //过滤白名单
        if (CollectionUtil.isNotEmpty(resultList)) {
            resultList = resultList.parallelStream().filter(k -> !whiteMap.containsKey(k.getPlatform() + k.getSite() + k.getDisplayProductCode())).collect(Collectors.toList());
        }
        //2024-10-21  sfp类型查询优化 除了根据sfp表判断外，还有根据区域仓库判断是否是sfp产品
        if (ObjectUtil.isNotEmpty(searchVo.getSfpFlag())) {
            resultList = resultList.stream().filter(x->x.getSfpProductDesc().equals(searchVo.getSfpFlag()==1?"是":"否")).collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public String export(McSaleInventoryReportSearchVo searchVo) throws ValidateException {
        List<SomUsInventoryReportVo> records = queryByPage(searchVo);
        if (!records.isEmpty()) {
            try {
                Workbook workbook = null;
                ExportParams params = new ExportParams(null, "美国可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomUsInventoryReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * handleSfp
     * 处理SFP产品
     *
     * @param list
     * <AUTHOR>
     * @history
     */
    private List<SomUsInventoryReportVo> handleSfp(List<SomUsInventoryReportVo> list) throws ValidateException {
        // 查询SFP产品安全库存
        McDictionaryInfo dictionaryInfo = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "SFPSafetyStock").single();
        if (ObjectUtil.isEmpty(dictionaryInfo) && StrUtil.isEmpty(dictionaryInfo.getItemValue())) {
            throw new ValidateException("请配置SFP产品安全库存");
        }
        // SFP安全库存
        Integer dictSafeStock = Integer.parseInt(dictionaryInfo.getItemValue());
        Integer dictSafeStockAdd5 = dictSafeStock + 5;
        //SFP产品包含Spreetail产品，可能也包含普通产品
        //获取所有的Spreetail产品
        List<SomSpreetailSku> spreetailSkuList = somSpreetailSkuMapper.all();
        Map<String, SomSpreetailSku> spreetailSkuMap = spreetailSkuList.stream().collect(Collectors.toMap(f -> f.getSellerSku(), Function.identity()));
        List<SomUsInventoryReportVo> spreetailList = new ArrayList<>();
        List<SomUsInventoryReportVo> normalList = new ArrayList<>();
        for (SomUsInventoryReportVo obj : list) {
            //通过5仓判断是否是SFP产品
            //2024.8.7修改为判断4仓
            SomUsSfpProductView sfpProductView = sfpMapper.all().stream().filter(f -> StrUtil.equals(obj.getSite(), f.getSite()) && StrUtil.equals(obj.getProductMainCode(), f.getProductMainCode())).findFirst().orElse(null);
            if (sfpProductView != null) {
                boolean boolArea5 = compare(sfpProductView.getEast(), Long.valueOf(dictSafeStockAdd5)) && compare(sfpProductView.getEastnorth(), Long.valueOf(dictSafeStockAdd5)) && compare(sfpProductView.getSlmGa(), Long.valueOf(dictSafeStockAdd5))
                        && compare(sfpProductView.getWest(), Long.valueOf(dictSafeStockAdd5)) && compare(sfpProductView.getCentral(), Long.valueOf(dictSafeStockAdd5));
                boolean boolArea4 = compare(sfpProductView.getEast(), Long.valueOf(dictSafeStockAdd5)) && compare(sfpProductView.getEastnorth(), Long.valueOf(dictSafeStockAdd5)) && compare(sfpProductView.getSlmGa(), Long.valueOf(dictSafeStockAdd5))
                        && compare(sfpProductView.getWest(), Long.valueOf(dictSafeStockAdd5));
                if (boolArea4) {
                    //获取库存
                    List<Long> stockList = Arrays.asList(
                            sfpProductView.getEast() - dictSafeStock,
                            sfpProductView.getEastnorth() - dictSafeStock,
                            sfpProductView.getSlmGa() - dictSafeStock,
                            sfpProductView.getWest() - dictSafeStock,
                            sfpProductView.getCentral() - dictSafeStock);
                    stockList = stockList.stream().filter(f -> f > 0).collect(Collectors.toList());
                    Long finalMin = stockList.stream().sorted().collect(Collectors.toList()).get(0);
                    obj.setStock(finalMin.intValue());
                    obj.setSfpProductDesc("是");
                    //判断是否Spreetail产品
                    if (spreetailSkuMap.containsKey(obj.getDisplayProductCode())) {
                        obj.setSpreetailFlagDesc("是");
                        spreetailList.add(obj);
                    } else {
                        obj.setSpreetailFlagDesc("否");
                    }
                } else {
                    obj.setNotSfpFlag(true);
                    normalList.add(obj);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(spreetailList)) {
            handleSpreetail(spreetailList);
        }
        if (CollectionUtil.isNotEmpty(normalList)) {
            handleNotSfp(normalList);
        }
        return list;
    }

    /**
     * handleNotSfp
     * 处理非SFP产品
     *
     * @param list
     * <AUTHOR>
     * @history
     */
    private List<SomUsInventoryReportVo> handleNotSfp(List<SomUsInventoryReportVo> list) {
        //查询可售仓库
        List<McWarehouseConfig> mcWarehouseConfigList = mcWarehouseConfigMapper.all();
        //查询McStockInfo
        List<String> whcodes = mcWarehouseConfigList.stream().map(m -> m.getUsableWarehouse()).distinct().collect(Collectors.toList());
        List<String> slcodes = mcWarehouseConfigList.stream().map(m -> m.getUsableStorageLocation()).distinct().collect(Collectors.toList());
        List<McStockInfo> mcStockInfoList = mcStockInfoMapper.createLambdaQuery().andIn("warehouse_code", whcodes).andIn("sl_code", slcodes).select();
        Map<String, McStockInfo> mcStockInfoMap = mcStockInfoList.stream().collect(Collectors.toMap(x -> x.getProductMainCode() + x.getWarehouseCode() + x.getSlCode(), Function.identity()));
        List<SomSupplySourceStore> storeList = somSupplySourceStoreMapper.all();
        //自发仓仓库集合
        List<SomSupplySourceStore> zfStoreList = storeList.stream().filter(f -> f.getStatus() == 10 && null != f.getSpreetailFlag() && f.getSpreetailFlag() == 0).collect(Collectors.toList());
        Map<String, List<SomSupplySourceStore>> zfStoreMap = zfStoreList.stream().collect(Collectors.groupingBy(f -> f.getAlias()));
        Integer zfSafeStock = 0;
        McDictionaryInfo zfDictionaryInfo = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "SupplySourceSafetyStock").single();
        if (zfDictionaryInfo != null) {
            zfSafeStock = Integer.valueOf(zfDictionaryInfo.getItemValue());
        }
        //查询清仓产品
        List<McDearanceProduct> mcDearanceProductList = mcDearanceProductMapper.createLambdaQuery().andEq("status", 1).select();
        Map<String, McDearanceProduct> mcDearanceProductMap = mcDearanceProductList.stream().collect(Collectors.toMap(x -> x.getPlatform() + x.getSite() + x.getSellSku(), v -> v, (key1, key2) -> key1));
        //获取所有的Spreetail产品
        List<SomSpreetailSku> spreetailSkuList = somSpreetailSkuMapper.all();
        Map<String, SomSpreetailSku> spreetailSkuMap = spreetailSkuList.stream().collect(Collectors.toMap(f -> f.getSellerSku(), Function.identity()));
        List<SomUsInventoryReportVo> spreetailList = new ArrayList<>();
        for (SomUsInventoryReportVo vo : list) {
            //判断是否是Spreetail产品
            if (spreetailSkuMap.containsKey(vo.getDisplayProductCode())) {
                vo.setSpreetailFlagDesc("是");
                vo.setSfpProductDesc("否");
                spreetailList.add(vo);
                continue;
            }
            List<SomUsInventoryReportVo.WarehouseInventory> warehouseInventoryList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(zfStoreMap)) {
                for (String key : zfStoreMap.keySet()) {
                    List<SomSupplySourceStore> stores = zfStoreMap.get(key);
                    Integer totalStock = 0;
                    SomUsInventoryReportVo.WarehouseInventory warehouseInventory = new SomUsInventoryReportVo.WarehouseInventory();
                    for (SomSupplySourceStore store : stores) {
                        Integer stock = 0;
                        List<SomSupplySourceStoreVo.UseableWarehouse> warehouseList = JSONUtil.toList(JSONUtil.parseArray(store.getMfnWarehouseSl()), SomSupplySourceStoreVo.UseableWarehouse.class);
                        for (SomSupplySourceStoreVo.UseableWarehouse warehouse : warehouseList) {
                            if (mcStockInfoMap.containsKey(vo.getProductMainCode() + warehouse.getMfnWarehouseCode() + warehouse.getMfnSlCode())) {
                                McStockInfo stockInfo = mcStockInfoMap.get(vo.getProductMainCode() + warehouse.getMfnWarehouseCode() + warehouse.getMfnSlCode());
                                stock += stockInfo.getTotalStock();
                            }
                        }
                        totalStock += stock;
                    }
                    warehouseInventory.setWarehouseName(key);
                    //判断是否是清仓产品，清仓产品不需要减去安全库存
                    if (mcDearanceProductMap.containsKey(vo.getPlatform() + vo.getSite() + vo.getDisplayProductCode())) {
                        warehouseInventory.setStock(Math.max(totalStock, 0));
                    } else {
                        warehouseInventory.setStock(Math.max(totalStock - zfSafeStock, 0));
                    }
                    warehouseInventoryList.add(warehouseInventory);
                }
            }
            if (CollectionUtil.isNotEmpty(warehouseInventoryList)) {
                vo.setList(warehouseInventoryList);
                Integer sum = warehouseInventoryList.stream().mapToInt(SomUsInventoryReportVo.WarehouseInventory::getStock).sum();
                vo.setStock(Math.max(sum, 0));
            } else {
                vo.setStock(0);
            }
            vo.setSpreetailFlagDesc("否");
            if (vo.isNotSfpFlag()) {
                vo.setSfpProductDesc("否");
            }
            vo.setSfpProductDesc("否");
        }
        if (CollectionUtil.isNotEmpty(spreetailList)) {
            handleSpreetail(spreetailList);
        }
        return list;
    }

    /**
     * handleSpreetail
     * 处理Spreetail产品
     *
     * @param list
     * <AUTHOR>
     * @history
     */
    private void handleSpreetail(List<SomUsInventoryReportVo> list) {
        //获取Spreetail库存信息
        List<SomSpreetailInventoryItems> inventoryItemsList = somSpreetailInventoryItemsMapper.all();
        Map<String, SomSpreetailInventoryItems> inventoryItemsMap = inventoryItemsList.stream().collect(Collectors.toMap(x -> x.getSellerSku(), Function.identity()));
        //获取Spreetail仓库配置
        List<SomSupplySourceStore> storeList = somSupplySourceStoreMapper.all();
        //spreetail仓库名称集合
        List<SomSupplySourceStore> spreetailStoreList = storeList.stream().filter(f -> f.getStatus() == 10 && null != f.getSpreetailFlag() && f.getSpreetailFlag() == 1).collect(Collectors.toList());
        Map<String, SomSupplySourceStore> spreetailStoreMap = spreetailStoreList.stream().collect(Collectors.toMap(f -> f.getSpreetailWarehouseName(), Function.identity()));
        Integer spreetailSafeStock = 0;
        McDictionaryInfo dictionaryInfo = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "SpreetailSafeStock").single();
        if (dictionaryInfo != null) {
            spreetailSafeStock = Integer.valueOf(dictionaryInfo.getItemValue());
        }
        //查询清仓产品
        List<McDearanceProduct> mcDearanceProductList = mcDearanceProductMapper.createLambdaQuery().andEq("status", 1).select();
        Map<String, McDearanceProduct> mcDearanceProductMap = mcDearanceProductList.stream().collect(Collectors.toMap(x -> x.getPlatform() + x.getSite() + x.getSellSku(), v -> v, (key1, key2) -> key1));
        for (SomUsInventoryReportVo vo : list) {
            SomSpreetailInventoryItems items = inventoryItemsMap.get(vo.getProductMainCode());
            List<SomSpreetailInventoryItemsVo.LocationInventoryDetails> detailsList = new ArrayList<>();
            if (items != null) {
                JSONObject jsonObject = JSONUtil.parseObj(items.getStockInfo());
                detailsList = JSONUtil.toList(jsonObject.getJSONArray("locationInventoryDetails"), SomSpreetailInventoryItemsVo.LocationInventoryDetails.class);
            }
            List<SomUsInventoryReportVo.WarehouseInventory> warehouseInventoryList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(detailsList)) {
                for (SomSpreetailInventoryItemsVo.LocationInventoryDetails details : detailsList) {
                    SomUsInventoryReportVo.WarehouseInventory warehouseInventory = new SomUsInventoryReportVo.WarehouseInventory();
                    if (StrUtil.isNotBlank(details.getLocationName()) && spreetailStoreMap.containsKey(details.getLocationName())) {
                        warehouseInventory.setWarehouseName(details.getLocationName());
                        if (mcDearanceProductMap.containsKey(vo.getPlatform() + vo.getSite() + vo.getDisplayProductCode())) {
                            warehouseInventory.setStock(Math.max(details.getQuantityOnHand(), 0));
                        } else {
                            warehouseInventory.setStock(Math.max((details.getQuantityOnHand() - spreetailSafeStock), 0));
                        }
                    }
                    warehouseInventoryList.add(warehouseInventory);
                }
            }
            if (CollectionUtil.isNotEmpty(warehouseInventoryList)) {
                vo.setList(warehouseInventoryList);
                vo.setStock(warehouseInventoryList.stream().mapToInt(SomUsInventoryReportVo.WarehouseInventory::getStock).sum());
            }
        }
    }

    /**
     * compare
     * 比较方法
     *
     * @param a
     * @param b
     * @return boolean
     * <AUTHOR>
     * @history
     */
    private boolean compare(Long a, Long b) {
        if (a == null || b == null) {
            return false;
        }
        return a > b;
    }

    /**
     * convertList
     * 把查询到的报表数据转为美国可售报表实体
     * @param list
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomUsInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    private List<SomUsInventoryReportVo> convertList(List<McSaleInventoryReportVo> list) {
        List<SomUsInventoryReportVo> reportVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            //转为美国可售报表实体集合
            reportVoList = list.stream().map(f -> {
                SomUsInventoryReportVo vo = new SomUsInventoryReportVo();
                vo.setPlatform(f.getPlatform());
                vo.setSite(f.getSite());
                vo.setDisplayProductCode(f.getDisplayProductCode());
                vo.setProductMainCode(f.getProductMainCode());
                vo.setCategoryName(f.getCategoryName());
                vo.setSalesGroupName(f.getGroupName());
                vo.setSalesGroupEmptName(f.getUserName());
                vo.setSafeStock(f.getSafetyStock());
                return vo;
            }).collect(Collectors.toList());
        }
        return reportVoList;
    }
}
