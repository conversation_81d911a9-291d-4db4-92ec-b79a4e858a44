package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomImpactAdsReport;
import com.zielsmart.mc.repository.mapper.SomImpactAdsReportMapper;
import com.zielsmart.mc.vo.SomImpactAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomImpactAdsReportVo;
import com.zielsmart.mc.vo.SomLostVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomImpactAdsReportService {
    
    @Resource
    private SomImpactAdsReportMapper somImpactAdsReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link PageVo< SomImpactAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomImpactAdsReportVo> queryByPage(SomImpactAdsReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomImpactAdsReportVo> pageResult = dynamicSqlManager.getMapper(SomImpactAdsReportMapper.class).queryByPage(searchVo, pageRequest);
        pageResult.getList().forEach(somImpactAdsReportVo -> {
            if (somImpactAdsReportVo.getActions()==null||somImpactAdsReportVo.getActions()==0||somImpactAdsReportVo.getClicks()==null||somImpactAdsReportVo.getClicks()==0){
                somImpactAdsReportVo.setConversionRate(BigDecimal.ZERO);
            }else {
                somImpactAdsReportVo.setConversionRate(BigDecimal.valueOf(somImpactAdsReportVo.getActions()).divide(BigDecimal.valueOf(somImpactAdsReportVo.getClicks()),3,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
            }
        });
        return ConvertUtils.pageConvert(pageResult, SomImpactAdsReportVo.class, searchVo);
    }

    public String export(SomImpactAdsReportPageSearchVo searchVo) throws ValidateException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomImpactAdsReportVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Impact广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomImpactAdsReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出Impact广告报表失败：" + e.getMessage());
            }
        }
        return null;
    }
}
