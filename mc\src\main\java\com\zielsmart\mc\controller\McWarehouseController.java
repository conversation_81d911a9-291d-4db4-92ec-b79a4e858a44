package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McWarehouseService;
import com.zielsmart.mc.vo.McWarehousePageSearchVo;
import com.zielsmart.mc.vo.McWarehouseVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWarehouseController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcWarehouse", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "仓库基础信息管理")
public class McWarehouseController extends BasicController{

    @Resource
    McWarehouseService mcWarehouseService;

    /**
     * save
     *
     * @param mcWarehouseVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McWarehouseVo mcWarehouseVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWarehouseService.save(mcWarehouseVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McWarehouseVo>> queryByPage(@RequestBody McWarehousePageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWarehouseService.queryByPage(searchVo));
    }

    /**
     * update
     *
     * @param mcWarehouseVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McWarehouseVo mcWarehouseVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWarehouseService.update(mcWarehouseVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcWarehouseVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McWarehouseVo mcWarehouseVo) throws ValidateException {
        mcWarehouseService.delete(mcWarehouseVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryWareHouseInfos
     * 查询仓库基础信息
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询仓库基础信息")
    @PostMapping(value = "/queryWareHouseInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWarehouseVo>> queryWareHouseInfos() {
        return ResultVo.ofSuccess(mcWarehouseService.queryWareHouseInfos());
    }
}
