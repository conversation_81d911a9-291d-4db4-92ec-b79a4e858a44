package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomSheinListingService;
import com.zielsmart.mc.vo.McWalmartSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomSheinListingReport;
import com.zielsmart.mc.vo.SomSheinListingVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomSheinListingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somSheinListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Shein管理")
public class SomSheinListingController extends BasicController {

    @Resource
    SomSheinListingService somSheinListingService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomSheinListingVo>> queryByPage(@RequestBody SomSheinWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somSheinListingService.queryByPage(searchVo));
    }
    @Operation(summary = "库存报表")
    @PostMapping(value = "/stock-report")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomSheinListingReport>> stockReport(@RequestBody SomSheinWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somSheinListingService.stockReport(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomSheinWarehouseConfigPageSearchVo searchVo) throws ValidateException, JsonProcessingException {
        String data = somSheinListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
