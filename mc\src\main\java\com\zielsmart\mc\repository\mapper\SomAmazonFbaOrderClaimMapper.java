package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonFbaOrderClaimPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonFbaOrderClaimVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
/*
* 
* gen by 代码生成器 mapper 2023-10-11
*/

@SqlResource("somAmazonFbaOrderClaim")
public interface SomAmazonFbaOrderClaimMapper extends BaseMapper<SomAmazonFbaOrderClaim> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonFbaOrderClaimVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonFbaOrderClaimVo> queryByPage(@Param("searchVo")SomAmazonFbaOrderClaimPageSearchVo searchVo, PageRequest pageRequest);

    List<String> queryFbaReturnReport(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("site") String site);
}
