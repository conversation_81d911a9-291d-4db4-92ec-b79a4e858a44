package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McSaleOperationConfigPageSearchVo;
import com.zielsmart.mc.vo.McSaleOperationConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2022-02-09
*/

@SqlResource("mcSaleOperationConfig")
public interface McSaleOperationConfigMapper extends BaseMapper<McSaleOperationConfig> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McSaleOperationConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McSaleOperationConfigVo> queryByPage(@Param("searchVo")McSaleOperationConfigPageSearchVo searchVo, PageRequest pageRequest);
}
