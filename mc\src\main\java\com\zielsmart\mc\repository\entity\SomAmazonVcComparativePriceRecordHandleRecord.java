package com.zielsmart.mc.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* VC比价记录操作日志表
* gen by 代码生成器 2025-01-21
*/

@Table(name="mc.som_amazon_vc_comparative_price_record_handle_record")
public class SomAmazonVcComparativePriceRecordHandleRecord implements java.io.Serializable {
	/**
	 * 主键aid
	 */
	@AssignID
	private String aid ;
	/**
	 * 主表比价记录表的主键
	 */
	@Column("cid")
	private String cid ;
	/**
	 * 处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	 */
	@Column("handle_status")
	private Integer handleStatus ;
	/**
	 * 最新处理状态 转义
	 */
	private String handleStatusShow ;
	/**
	 * 处理类型：10.人工 99.系统
	 */
	@Column("handle_type")
	private Integer handleType ;
	/**
	 * 比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	 */
	@Column("comparative_reason")
	private Integer comparativeReason ;
	/**
	 * 最新比价原因 转义
	 */
	private String comparativeReasonShow;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 * 处理人工号(指定的下次处理人工号)
	 */
	@Column("handle_person_code")
	private String handlePersonCode ;
	/**
	 * 处理人姓名(指定的下次处理人姓名)
	 */
	@Column("handle_person_name")
	private String handlePersonName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date createTime ;
	/**
	 * 当前处理人工号
	 */
	@Column("task_charge_person_code")
	private String taskChargePersonCode ;
	/**
	 * 当前处理人姓名
	 */
	@Column("task_charge_person_name")
	private String taskChargePersonName ;

	public SomAmazonVcComparativePriceRecordHandleRecord() {
	}

	/**
	* 主键aid
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键aid
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 主表比价记录表的主键
	*@return
	*/
	public String getCid(){
		return  cid;
	}
	/**
	* 主表比价记录表的主键
	*@param  cid
	*/
	public void setCid(String cid ){
		this.cid = cid;
	}
	/**
	* 处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	*@return
	*/
	public Integer getHandleStatus(){
		return  handleStatus;
	}
	/**
	* 处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	*@param  handleStatus
	*/
	public void setHandleStatus(Integer handleStatus ){
		this.handleStatus = handleStatus;
	}
	/**
	 *
	* 处理类型：10.人工 99.系统
	*@return
	*/
	public Integer getHandleType(){
		return  handleType;
	}
	/**
	* 处理类型：10.人工 99.系统
	*@param  handleType
	*/
	public void setHandleType(Integer handleType ){
		this.handleType = handleType;
	}
	/**
	* 比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	*@return
	*/
	public Integer getComparativeReason(){
		return  comparativeReason;
	}
	/**
	* 比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	*@param  comparativeReason
	*/
	public void setComparativeReason(Integer comparativeReason ){
		this.comparativeReason = comparativeReason;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemark(){
		return  remark;
	}
	/**
	* 备注
	*@param  remark
	*/
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	* 处理人工号(指定的下次处理人工号)
	*@return
	*/
	public String getHandlePersonCode(){
		return  handlePersonCode;
	}
	/**
	* 处理人工号(指定的下次处理人工号)
	*@param  handlePersonCode
	*/
	public void setHandlePersonCode(String handlePersonCode ){
		this.handlePersonCode = handlePersonCode;
	}
	/**
	* 处理人姓名(指定的下次处理人姓名)
	*@return
	*/
	public String getHandlePersonName(){
		return  handlePersonName;
	}
	/**
	* 处理人姓名(指定的下次处理人姓名)
	*@param  handlePersonName
	*/
	public void setHandlePersonName(String handlePersonName ){
		this.handlePersonName = handlePersonName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 当前处理人工号
	*@return
	*/
	public String getTaskChargePersonCode(){
		return  taskChargePersonCode;
	}
	/**
	* 当前处理人工号
	*@param  taskChargePersonCode
	*/
	public void setTaskChargePersonCode(String taskChargePersonCode ){
		this.taskChargePersonCode = taskChargePersonCode;
	}
	/**
	* 当前处理人姓名
	*@return
	*/
	public String getTaskChargePersonName(){
		return  taskChargePersonName;
	}
	/**
	* 当前处理人姓名
	*@param  taskChargePersonName
	*/
	public void setTaskChargePersonName(String taskChargePersonName ){
		this.taskChargePersonName = taskChargePersonName;
	}

	public String getHandleStatusShow(){
		return  handleStatusShow;
	}
	public void setHandleStatusShow(String handleStatusShow ){
		this.handleStatusShow = handleStatusShow;
	}

	public String getComparativeReasonShow(){
		return  comparativeReasonShow;
	}
	public void setComparativeReasonShow(String comparativeReasonShow ){ this.comparativeReasonShow = comparativeReasonShow; }

}
