package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.vo.SomStorageLocationVo;
import com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-08-23
 */

@SqlResource("somStorageLocation")
public interface SomStorageLocationMapper extends BaseMapper<SomStorageLocation> {

    /**
     * queryByWhcode
     * 根据仓库编码查询对应库区
     *
     * @param whcodeList
     * @return {@link java.util.List<com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo>}
     * <AUTHOR>
     * @history
     */
    List<SomStorageLocationTreeVo> queryByWhcode(@Param("whcodeList") List<String> whcodeList);

    /**
     * 获取全量的仓库与库区信息
     * @return
     */
    List<SomStorageLocation> queryAllInfos();

    /**
     * 查询所有仓库库区和市场
     * @return
     */
    List<SomStorageLocationVo> queryWhMarket();
}
