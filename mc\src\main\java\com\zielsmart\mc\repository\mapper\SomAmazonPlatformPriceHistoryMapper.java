package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceHistoryPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonPlatformPriceHistoryVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Template;

import java.util.List;
/*
*
* gen by 代码生成器 mapper 2023-11-16
*/

@SqlResource("somAmazonPlatformPriceHistory")
public interface SomAmazonPlatformPriceHistoryMapper extends BaseMapper<SomAmazonPlatformPriceHistory> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonPlatformPriceHistoryVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonPlatformPriceHistoryVo> queryByPage(@Param("searchVo")SomAmazonPlatformPriceHistoryPageSearchVo searchVo, PageRequest pageRequest);

    @Template("SELECT sh.* FROM som_amazon_platform_price_history  sh\n" +
            "WHERE sh.adjust_price_status = '20' and site = #{site} AND seller_sku = #{sellerSku}\n" +
            "ORDER BY create_time DESC LIMIT 1")
    SomAmazonPlatformPriceHistoryVo queryLatestHistory(String site, String sellerSku);

    /**
     * 查询最近调价成功的7条数据
     * @param site
     * @param sellerSku
     * @return
     */
    List<SomAmazonPlatformPriceHistoryVo> recently7Data(@Param("site") String site, @Param("sellerSku") String sellerSku);
}
