package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.role.McRolePageSearchVo;
import com.zielsmart.mc.vo.role.McRoleVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-07-16
*/

@SqlResource("mcRole")
public interface McRoleMapper extends BaseMapper<McRole> {

    /**
     * queryByPage
     * 平台站点角色分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult< McRoleVo >}
     * <AUTHOR>
     * @history
     */
    PageResult<McRoleVo> queryByPage(@Param("search")McRolePageSearchVo searchVo, PageRequest pageRequest);
}
