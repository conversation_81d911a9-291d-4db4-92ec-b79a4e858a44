package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTemuBlackList;
import com.zielsmart.mc.repository.entity.SomTemuClearanceList;
import com.zielsmart.mc.repository.mapper.SomTemuBlackListMapper;
import com.zielsmart.mc.repository.mapper.SomTemuClearanceListMapper;
import com.zielsmart.mc.vo.SomTemuClearanceListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuClearanceListVo;
import com.zielsmart.mc.vo.SomTemuSkuBasicVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTemuClearanceListService
 * @description
 * @date 2024-12-26 08:52:17
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuClearanceListService {

    @Resource
    private SomTemuClearanceListMapper somTemuClearanceListMapper;

    @Resource
    private SomTemuSkuService somTemuSkuService;

    @Resource
    private SomTemuBlackListMapper somTemuBlackListMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuClearanceListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuClearanceListVo> queryByPage(SomTemuClearanceListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuClearanceListVo> pageResult = somTemuClearanceListMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuClearanceListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param clearanceListVo 入参
     * @param tokenUser       当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuClearanceListVo clearanceListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(clearanceListVo) ||
                StrUtil.isBlank(clearanceListVo.getAccountId()) ||
                StrUtil.isBlank(clearanceListVo.getSite()) ||
                StrUtil.isBlank(clearanceListVo.getSellerSku()) ||
                StrUtil.isBlank(clearanceListVo.getProductSkuId())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 需要注意清仓的新增是 seller_sku 维度
        String accountId = clearanceListVo.getAccountId();
        String site = clearanceListVo.getSite();
        String sellerSku = clearanceListVo.getSellerSku();
        String productSkuId = clearanceListVo.getProductSkuId();
        SomTemuSkuBasicVo temuSku = somTemuSkuService.getTemuSku(accountId, site, sellerSku, productSkuId);
        if (temuSku == null) {
            throw new ValidateException("TemuListing中未找到该商品，店铺id+展示码不存在，请检查数据");
        }
        // 判断在黑名单中是否已经存在
        List<SomTemuBlackList> allList = somTemuBlackListMapper.all();
        Map<String, SomTemuBlackList> allBlackMap = allList.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), Function.identity(), (x1, x2) -> x1));
        if (allBlackMap.containsKey(accountId +  productSkuId)) {
            throw new ValidateException("该商品已存在库存推送黑名单中，不允许维护清仓数据！");
        }
        LambdaQuery<SomTemuClearanceList> clearanceListLambdaQuery = somTemuClearanceListMapper.createLambdaQuery()
                .andEq("account_id", clearanceListVo.getAccountId())
                .andEq("product_sku_id", productSkuId);
        if (StrUtil.isNotBlank(clearanceListVo.getAid())) {
            clearanceListLambdaQuery.andNotEq("aid", clearanceListVo.getAid());
        }
        long count = clearanceListLambdaQuery.count();
        if (count > 0) {
            throw new ValidateException("您输入的产品在清仓列表中已存在，不允许重复维护");
        }
        if (StrUtil.isBlank(clearanceListVo.getAid())) {
            clearanceListVo.setProductSkuId(productSkuId);
            clearanceListVo.setCreateName(tokenUser.getUserName());
            clearanceListVo.setCreateTime(DateTime.now().toJdkDate());
            clearanceListVo.setCreateNum(tokenUser.getJobNumber());
            clearanceListVo.setAid(IdUtil.fastSimpleUUID());
            somTemuClearanceListMapper.insert(ConvertUtils.beanConvert(clearanceListVo, SomTemuClearanceList.class));
        } else {
            clearanceListVo.setProductSkuId(productSkuId);
            clearanceListVo.setCreateName(tokenUser.getUserName());
            clearanceListVo.setCreateTime(DateTime.now().toJdkDate());
            clearanceListVo.setCreateNum(tokenUser.getJobNumber());
            somTemuClearanceListMapper.createLambdaQuery().andEq("aid", clearanceListVo.getAid())
                    .updateSelective(ConvertUtils.beanConvert(clearanceListVo, SomTemuClearanceList.class));
        }
    }

    /**
     * delete
     * 删除
     *
     * @param clearanceListVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuClearanceListVo clearanceListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(clearanceListVo) || StrUtil.isEmpty(clearanceListVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTemuClearanceListMapper.createLambdaQuery().andEq("aid", clearanceListVo.getAid()).delete();
    }

    /**
     * export
     * 导出
     *
     * @param searchVo 入参
     * @return String
     * <AUTHOR>
     */
    public String export(SomTemuClearanceListPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomTemuClearanceListVo> records = queryByPage(searchVo).getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        Workbook workbook;
        try {
            ExportParams params = new ExportParams(null, "Temu清仓列表");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomTemuClearanceListVo.class, records);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * importExcel
     * 导入数据
     *
     * @param clearanceListVos 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomTemuClearanceListVo> clearanceListVos, TokenUserInfo tokenUser) throws ValidateException {
        // 去重
        clearanceListVos = clearanceListVos.stream().distinct().collect(java.util.stream.Collectors.toList());
        if (clearanceListVos.isEmpty()) {
            return;
        }
        // 查询全部 listing，不确定上传的数量，使用【店铺ID】进行查询
        List<String> accountIds = clearanceListVos.stream().map(SomTemuClearanceListVo::getAccountId).distinct().collect(Collectors.toList());
        Map<String, SomTemuSkuBasicVo> listingMap = querySkuBasicMap(accountIds);
        String errorMsg = clearanceListVos.stream()
                .filter(x -> ! listingMap.containsKey(x.getAccountId() + x.getProductSkuId() + x.getSellerSku()))
                .map(x -> "站点:" + x.getSite() + " 展示码:" + x.getSellerSku())
                .collect(Collectors.joining("\n"));
        if (StrUtil.isNotBlank(errorMsg)) {
            throw new ValidateException(errorMsg + "\n在系统中不存在，请检查数据！");
        }
        // 判断在黑名单中是否已经存在
        List<SomTemuBlackList> allList = somTemuBlackListMapper.all();
        Map<String, SomTemuBlackList> allBlackMap = allList.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), Function.identity(), (x1, x2) -> x1));
        // 判断在清仓列表中是否已经存在
        List<SomTemuClearanceList> clearanceLists = somTemuClearanceListMapper.all();
        Map<String, SomTemuClearanceList> clearanceListMap = clearanceLists.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), Function.identity(), (x1, x2) -> x2));

        List<SomTemuClearanceListVo> listVos = new ArrayList<>();
        for (SomTemuClearanceListVo clearanceListVo : clearanceListVos) {
            if (clearanceListMap.containsKey(clearanceListVo.getAccountId() + clearanceListVo.getProductSkuId())) {
                continue;
            }
            // 「店铺ID」+「SKU ID」在 som_temu_black_list 是否已存在，已存在过滤掉
            if (allBlackMap.containsKey(clearanceListVo.getAccountId() + clearanceListVo.getProductSkuId())) {
                continue;
            }
            SomTemuSkuBasicVo somTemuSkuBasicVo = listingMap.get(clearanceListVo.getAccountId() + clearanceListVo.getProductSkuId() + clearanceListVo.getSellerSku());
            clearanceListVo.setProductSkuId(somTemuSkuBasicVo.getProductSkuId());
            clearanceListVo.setCreateNum(tokenUser.getJobNumber());
            clearanceListVo.setCreateName(tokenUser.getUserName());
            clearanceListVo.setCreateTime(DateTime.now().toJdkDate());
            clearanceListVo.setAid(IdUtil.fastSimpleUUID());
            listVos.add(clearanceListVo);
        }
        if (CollUtil.isNotEmpty(listVos)) {
            somTemuClearanceListMapper.insertBatch(ConvertUtils.listConvert(listVos, SomTemuClearanceList.class));
        }
    }

    /**
     * 获取导入涉及的 listing map
     *
     * @param accountIds 店铺IDS
     * @return Map<String, SomTemuListing>
     */
    private Map<String, SomTemuSkuBasicVo> querySkuBasicMap(List<String> accountIds) {
        if (CollUtil.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<SomTemuSkuBasicVo> skuBasicVos = somTemuSkuService.getTemuSkuByAccountIds(accountIds);
        return skuBasicVos.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId() + x.getSellerSku(), Function.identity(), (x1, x2) -> x1));
    }

    /**
     * 批量导入删除
     *
     * @param clearanceListVos 导入数据
     */
    public void batchDelete(List<SomTemuClearanceListVo> clearanceListVos) {
        List<SomTemuClearanceList> clearanceLists = somTemuClearanceListMapper.createLambdaQuery().select("aid","account_id","product_sku_id");
        Map<String, String> clearanceListMap = clearanceLists.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getProductSkuId(), SomTemuClearanceList::getAid, (x1, x2) -> x1));
        Set<String> aidSet = clearanceListVos.stream().map(x -> clearanceListMap.get(x.getAccountId() + x.getProductSkuId())).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(aidSet)) {
            somTemuClearanceListMapper.createLambdaQuery().andIn("aid", aidSet).delete();
        }
    }
}
