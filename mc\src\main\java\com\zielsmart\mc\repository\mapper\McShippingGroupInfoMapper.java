package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McNewGoodInfoMarkVo;
import com.zielsmart.mc.vo.McShippingGroupInfoPageSearchVo;
import com.zielsmart.mc.vo.McShippingGroupInfoVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2022-02-09
*/

@SqlResource("mcShippingGroupInfo")
public interface McShippingGroupInfoMapper extends BaseMapper<McShippingGroupInfo> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McShippingGroupInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McShippingGroupInfoVo> queryByPage(@Param("searchVo")McShippingGroupInfoPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * appointArea
     *指定区域
     * @param vo
     * <AUTHOR>
     * @history
     */
    default void appointArea(@Param("vo") McShippingGroupInfoVo vo) {
        this.getSQLManager().update(SqlId.of("mcShippingGroupInfo.appointArea"), vo);
    }
}
