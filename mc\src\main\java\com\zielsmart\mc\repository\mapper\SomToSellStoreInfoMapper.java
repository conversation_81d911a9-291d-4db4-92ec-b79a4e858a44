package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomToSellStoreInfo;
import com.zielsmart.mc.vo.SomToSellStoreInfoPageSearchVo;
import com.zielsmart.mc.vo.SomToSellStoreInfoVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-11-30
 */

@SqlResource("somToSellStoreInfo")
public interface SomToSellStoreInfoMapper extends BaseMapper<SomToSellStoreInfo> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomToSellStoreInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomToSellStoreInfoVo> queryByPage(@Param("searchVo") SomToSellStoreInfoPageSearchVo searchVo, PageRequest pageRequest);
}
