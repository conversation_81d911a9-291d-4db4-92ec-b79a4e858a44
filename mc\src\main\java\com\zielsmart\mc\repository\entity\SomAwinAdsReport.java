package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* Awin广告
* gen by 代码生成器 2023-05-11
*/

@Table(name="mc.som_awin_ads_report")
public class SomAwinAdsReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 账号ID
	 */
	@Column("account_id")
	private Integer accountId ;
	/**
	 * 广告日期
	 */
	@Column("day")
	private Date day ;
	/**
	 * 广告组名称
	 */
	@Column("campaign")
	private String campaign ;
	/**
	 * 发布者ID
	 */
	@Column("publisher_id")
	private String publisherId ;
	/**
	 * 发布者名称
	 */
	@Column("publisher_name")
	private String publisherName ;
	/**
	 * 按佣金状态划分的交易佣金，“总额”包括已批准的、待处理的和奖金交易
	 */
	@Column("commission_amount")
	private String commissionAmount ;
	/**
	 * 按佣金状态和点击次数的交易数，“total”汇总了已批准的、待处理的和奖金交易
	 */
	@Column("quantity")
	private String quantity ;
	/**
	 * 按佣金状态计算的交易价值，“总额”包括已批准的、待处理的和奖金交易
	 */
	@Column("sale_amount")
	private String saleAmount ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAwinAdsReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 账号ID
	*@return
	*/
	public Integer getAccountId(){
		return  accountId;
	}
	/**
	* 账号ID
	*@param  accountId
	*/
	public void setAccountId(Integer accountId ){
		this.accountId = accountId;
	}
	/**
	* 广告日期
	*@return
	*/
	public Date getDay(){
		return  day;
	}
	/**
	* 广告日期
	*@param  day
	*/
	public void setDay(Date day ){
		this.day = day;
	}
	/**
	* 广告组名称
	*@return
	*/
	public String getCampaign(){
		return  campaign;
	}
	/**
	* 广告组名称
	*@param  campaign
	*/
	public void setCampaign(String campaign ){
		this.campaign = campaign;
	}
	/**
	* 发布者ID
	*@return
	*/
	public String getPublisherId(){
		return  publisherId;
	}
	/**
	* 发布者ID
	*@param  publisherId
	*/
	public void setPublisherId(String publisherId ){
		this.publisherId = publisherId;
	}
	/**
	* 发布者名称
	*@return
	*/
	public String getPublisherName(){
		return  publisherName;
	}
	/**
	* 发布者名称
	*@param  publisherName
	*/
	public void setPublisherName(String publisherName ){
		this.publisherName = publisherName;
	}
	/**
	* 按佣金状态划分的交易佣金，“总额”包括已批准的、待处理的和奖金交易
	*@return
	*/
	public String getCommissionAmount(){
		return  commissionAmount;
	}
	/**
	* 按佣金状态划分的交易佣金，“总额”包括已批准的、待处理的和奖金交易
	*@param  commissionAmount
	*/
	public void setCommissionAmount(String commissionAmount ){
		this.commissionAmount = commissionAmount;
	}
	/**
	* 按佣金状态和点击次数的交易数，“total”汇总了已批准的、待处理的和奖金交易
	*@return
	*/
	public String getQuantity(){
		return  quantity;
	}
	/**
	* 按佣金状态和点击次数的交易数，“total”汇总了已批准的、待处理的和奖金交易
	*@param  quantity
	*/
	public void setQuantity(String quantity ){
		this.quantity = quantity;
	}
	/**
	* 按佣金状态计算的交易价值，“总额”包括已批准的、待处理的和奖金交易
	*@return
	*/
	public String getSaleAmount(){
		return  saleAmount;
	}
	/**
	* 按佣金状态计算的交易价值，“总额”包括已批准的、待处理的和奖金交易
	*@param  saleAmount
	*/
	public void setSaleAmount(String saleAmount ){
		this.saleAmount = saleAmount;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
