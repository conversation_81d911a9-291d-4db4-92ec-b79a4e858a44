package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-04-29
 */

@SqlResource("somMiraviaWarehouseConfig")
public interface SomMiraviaWarehouseConfigMapper extends BaseMapper<SomMiraviaWarehouseConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomMiraviaWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMiraviaWarehouseConfigVo> queryByPage(@Param("searchVo") SomMiraviaWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
