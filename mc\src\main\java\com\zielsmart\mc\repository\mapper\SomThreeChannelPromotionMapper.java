package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomThreeChannelPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomThreeChannelPromotionVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2025-05-13
*/

@SqlResource("somThreeChannelPromotion")
public interface SomThreeChannelPromotionMapper extends BaseMapper<SomThreeChannelPromotion> {

    /**
     * 分页查询
     *
     * @param searchVo    查询参数
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomThreeChannelPromotionVo>}
     */
    PageResult<SomThreeChannelPromotionVo> queryByPage(SomThreeChannelPromotionPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 核验活动是否存在
     *
     * @param somThreeChannelPromotion 活动bean
     * @return boolean
     */
    int checkPromotionExist(SomThreeChannelPromotion somThreeChannelPromotion);
}
