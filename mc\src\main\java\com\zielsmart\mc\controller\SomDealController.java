package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomDealService;
import com.zielsmart.mc.vo.BiAmazonProfitCalculationVo;
import com.zielsmart.mc.vo.SomDealSearchVo;
import com.zielsmart.mc.vo.SomDealVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping(value = "/somDeal", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Deal公共接口")
public class SomDealController extends BasicController {

    @Resource
    private SomDealService service;

    @Operation(summary = "查询Deal折扣")
    @PostMapping(value = "/searchDealList")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomDealVo>> searchDealList(@RequestBody SomDealSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(service.searchDealList(searchVo));
    }

    @Operation(summary = "获取利润计算值Bi")
    @PostMapping(value = "/getBiAmazonProfitCalculation")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<BiAmazonProfitCalculationVo> getBiAmazonProfitCalculation(@RequestBody BiAmazonProfitCalculationVo searchExVo) throws ValidateException {
        return ResultVo.ofSuccess(service.getBiAmazonProfitCalculation(searchExVo));
    }
}
