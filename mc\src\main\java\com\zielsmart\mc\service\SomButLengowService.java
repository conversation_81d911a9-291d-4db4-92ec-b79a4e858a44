package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.repository.entity.SomButLengow;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.SomButLengowMapper;
import com.zielsmart.mc.vo.SomButLengowPageSearchVo;
import com.zielsmart.mc.vo.SomButLengowVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.file.FileSearchResponse;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomButLengowService {
    private final String s3FileName = "ButFrTemplate.xlsx";
    @Resource
    private S3FileService s3FileService;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private McProductSalesMapper mcProductSalesMapper;
    @Resource
    private SomButLengowMapper somButLengowMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    public String getS3Path() {
        //设置S3模版字典值
        McDictionaryInfo pathDict = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "ButTemplateS3Path").single();
        if (pathDict == null) {
            log.error("ButTemplateS3Path字典不存在");
            throw new RuntimeException("ButTemplateS3Path字典不存在");
        }
        return pathDict.getItemValue();
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomButLengowVo>}
     * <AUTHOR>
     * @history
     */
    public HashMap queryByPage(SomButLengowPageSearchVo searchVo) throws Exception {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<HashMap<String, Object>> pageResult = dynamicSqlManager.getMapper(SomButLengowMapper.class).queryByPage(searchVo, pageRequest);
        HashMap<String, Object> result = new HashMap<>();

        List<Object> s3ExcelFields = getS3ExcelFields();
        result.put("fields", s3ExcelFields);

        if (!pageResult.getList().isEmpty()) {

            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);

            for (Map map : pageResult.getList()) {
                Map<String, Object> dataMap = new HashMap<>();
                String excelJson = (String) map.get("excelJson");
                Map excelMap = mapper.readValue(excelJson, Map.class);
                dataMap.putAll(excelMap);
                dataMap.put("aid", map.get("aid"));
                dataMap.put("修改时间", DateUtil.format((Date) map.get("modifyTime"), "yyyy-MM-dd HH:mm:ss"));
                dataMap.put("修改人", map.get("modifyName"));
                dataMap.put("创建时间", DateUtil.format((Date) map.get("createTime"), "yyyy-MM-dd HH:mm:ss"));
                dataMap.put("创建人", map.get("createName"));
                dataMap.put("业务负责人", map.get("salesGroupName"));
                dataMap.put("BU组", map.get("buGroupName"));
                dataMap.put("三级分类", map.get("categoryName"));
                map.clear();
                map.putAll(dataMap);
            }
        }
        PageVo<HashMap> hashMapPageVo = ConvertUtils.pageConvert(pageResult, HashMap.class, searchVo);
        result.put("data", hashMapPageVo);
        return result;
    }

    private List<Object> getS3ExcelFields() throws Exception {
        //获取S3的模版获取到excel中的所有字段，当做前端显示的字段
        String template = s3FileService.download(getS3Path() + s3FileName).getFileBase64Content();
        byte[] decode = Base64.getDecoder().decode(template);
        ByteArrayInputStream bis = new ByteArrayInputStream(decode);
        List<List<Object>> read = ExcelUtil.getReader(bis).read(0);
        List<Object> titleList = read.get(0);
        titleList.add(0, "业务负责人");
        titleList.add(0, "BU组");
        titleList.add(0, "三级分类");
        titleList.add("创建人");
        titleList.add("创建时间");
        titleList.add("修改人");
        titleList.add("修改时间");
        return titleList;
    }


    /**
     * delete
     * 删除
     *
     * @param somButLengowVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomButLengowVo somButLengowVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somButLengowVo) || CollectionUtils.isEmpty(somButLengowVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somButLengowMapper.createLambdaQuery().andIn("aid", somButLengowVo.getAidList()).delete();
    }

    public String export(SomButLengowPageSearchVo searchVo) throws Exception {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        HashMap records = queryByPage(searchVo);
        List<HashMap> data = ((PageVo<HashMap>) records.get("data")).getRecords();
        if (!data.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "乐售listing管理管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                List<ExcelExportEntity> entityList = new ArrayList<>();
                List<Object> s3ExcelFields = getS3ExcelFields();
                for (Object s3ExcelField : s3ExcelFields) {
                    entityList.add(new ExcelExportEntity(s3ExcelField.toString(), s3ExcelField.toString()));
                }
                workbook = ExcelExportUtil.exportExcel(params, entityList, data);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<Map> list, TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        List<SomButLengow> insertList = new ArrayList<>();
        List<SomButLengow> updateList = new ArrayList<>();
        Date createTime = DateTime.now().toJdkDate();
        List<McDictionaryInfo> fieldDict = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "ButExcelField").select();
        if (fieldDict.isEmpty()) {
            log.error("ButExcelField字典不存在");
            throw new ValidateException("ButExcelField字典不存在");
        }
        //取excel表中的展示码，根据配置的字典值取对应的字段
        Optional<McDictionaryInfo> sellerSkuDict = fieldDict.stream().filter(x -> x.getItemLable().equals("SellerSku")).findFirst();
        if (!sellerSkuDict.isPresent()) {
            throw new ValidateException("ButExcelField SellerSku字典不存在");
        }
        String sellerSkuField = sellerSkuDict.get().getItemValue();

        Optional<McDictionaryInfo> skuDict = fieldDict.stream().filter(x -> x.getItemLable().equals("SKU")).findFirst();
        if (!skuDict.isPresent()) {
            throw new ValidateException("ButExcelField Sku字典不存在");
        }
        String skuField = skuDict.get().getItemValue();

        //检查excel表中的展示码是否存在空值
        Optional<Map> first = list.stream().filter(x -> x.getOrDefault(sellerSkuField, null) == null || "null".equals(x.get(sellerSkuField))).findFirst();
        if (first.isPresent()) {
            throw new ValidateException("Excel表格中存在空值展示码");
        }
        //excel表格去重
        Set<String> repeatSet = list.stream().map(x -> x.get(sellerSkuField)).collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream().filter(x -> x.getValue() > 1).map(x -> x.getKey().toString()).collect(Collectors.toSet());
        if (!repeatSet.isEmpty()) {
            throw new ValidateException("表中存在展示码重复的Listing数据，导入失败：" + repeatSet.stream().collect(Collectors.joining(",")));
        }
        //查询数据库中是否存在该展示码，如果存在则更新
        List<SomButLengow> dbList = somButLengowMapper.all();
        Map<String, SomButLengow> dbSellerSkuMap = dbList.stream().collect(Collectors.toMap(SomButLengow::getSellerSku, Function.identity(), (x1, x2) -> x1));
        //查BUT销售视图
        List<McProductSales> salesList = mcProductSalesMapper.createLambdaQuery().andEq("site", "BUT.fr")
                .andEq("sales_flag", 1).andEq("is_consignment_sales", 0)
                .select("product_main_code", "display_product_code");
        Map<String, String> salesMap = salesList.stream().collect(Collectors.toMap(McProductSales::getDisplayProductCode, McProductSales::getProductMainCode, (x1, x2) -> x1));

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        //根据展示码查询SKU，写入excel 和 表中
        for (Map map : list) {
            String sellerSku = (String) map.get(sellerSkuField);
            String sku = salesMap.get(sellerSku);
            if (StrUtil.isEmpty(sku)) {
                throw new ValidateException("展示码：" + sellerSku + " 不存在");
            }
            map.put(skuField, sku);
            if (dbSellerSkuMap.containsKey(sellerSku)) {
                SomButLengow somButLengow = dbSellerSkuMap.get(sellerSku);
                somButLengow.setProductMainCode(sku);
                somButLengow.setExcelJson(mapper.writeValueAsString(map));
                somButLengow.setModifyNum(tokenUser.getJobNumber());
                somButLengow.setModifyName(tokenUser.getUserName());
                somButLengow.setModifyTime(createTime);
                updateList.add(somButLengow);
                continue;
            }
            SomButLengow somButLengow = new SomButLengow();
            somButLengow.setAid(IdUtil.fastSimpleUUID());
            somButLengow.setProductMainCode(sku);
            somButLengow.setSellerSku(sellerSku);
            somButLengow.setExcelJson(mapper.writeValueAsString(map));
            somButLengow.setCreateNum(tokenUser.getJobNumber());
            somButLengow.setCreateName(tokenUser.getUserName());
            somButLengow.setCreateTime(createTime);
            somButLengow.setModifyNum(tokenUser.getJobNumber());
            somButLengow.setModifyName(tokenUser.getUserName());
            somButLengow.setModifyTime(createTime);
            insertList.add(somButLengow);
        }
        if (!updateList.isEmpty()) {
            somButLengowMapper.batchUpdate(updateList);
        }
        if (!insertList.isEmpty()) {
            somButLengowMapper.insertBatch(insertList);
        }
    }

    public String download() throws Exception {
        FileSearchResponse download = s3FileService.download(getS3Path() + s3FileName);
        return download.getFileBase64Content();
    }

    public ResultVo<String> uploadS3(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        Map<String, String> result = new HashMap<>();
        Class<? extends MultipartFile> aClass = file.getClass();
        Field field = aClass.getDeclaredField("filename");
        field.setAccessible(true);
        //设置S3模版名称
        field.set(file, s3FileName);
        result.put("dirPath", getS3Path());
        s3FileService.upload(result, new MultipartFile[]{file}, tokenUser);
        return ResultVo.ofSuccess();
    }
}
