package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 短装CASE明细表
* gen by 代码生成器 2023-08-18
*/

@Table(name="mc.som_vc_amazon_purchase_order_case_item")
public class SomVcAmazonPurchaseOrderCaseItem implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * CASE主表ID
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 数量
	 */
	@Column("quantity")
	private Integer quantity ;
	/**
	 * 索赔金额
	 */
	@Column("claim_amount")
	private BigDecimal claimAmount ;
	/**
	 * 实际赔付金额
	 */
	@Column("amount_paid")
	private BigDecimal amountPaid ;
	/**
	 * 币种符号，例如：€
	 */
	@Column("currency_symbol")
	private String currencySymbol ;
	/**
	 * 币种，例如：EUR
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomVcAmazonPurchaseOrderCaseItem() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* CASE主表ID
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* CASE主表ID
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 数量
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 数量
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	/**
	* 索赔金额
	*@return
	*/
	public BigDecimal getClaimAmount(){
		return  claimAmount;
	}
	/**
	* 索赔金额
	*@param  claimAmount
	*/
	public void setClaimAmount(BigDecimal claimAmount ){
		this.claimAmount = claimAmount;
	}
	/**
	* 实际赔付金额
	*@return
	*/
	public BigDecimal getAmountPaid(){
		return  amountPaid;
	}
	/**
	* 实际赔付金额
	*@param  amountPaid
	*/
	public void setAmountPaid(BigDecimal amountPaid ){
		this.amountPaid = amountPaid;
	}
	/**
	* 币种符号，例如：€
	*@return
	*/
	public String getCurrencySymbol(){
		return  currencySymbol;
	}
	/**
	* 币种符号，例如：€
	*@param  currencySymbol
	*/
	public void setCurrencySymbol(String currencySymbol ){
		this.currencySymbol = currencySymbol;
	}
	/**
	* 币种，例如：EUR
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种，例如：EUR
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
