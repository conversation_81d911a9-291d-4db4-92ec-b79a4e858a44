package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPickUpStockPushRecord;
import com.zielsmart.mc.vo.SomPickUpStockPushRecordExtVo;
import com.zielsmart.mc.vo.SomPickUpStockPushRecordPageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-09-22
 */

@SqlResource("somPickUpStockPushRecord")
public interface SomPickUpStockPushRecordMapper extends BaseMapper<SomPickUpStockPushRecord> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPickUpStockPushRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPickUpStockPushRecordExtVo> queryByPage(@Param("searchVo") SomPickUpStockPushRecordPageSearchVo searchVo, PageRequest pageRequest);
}
