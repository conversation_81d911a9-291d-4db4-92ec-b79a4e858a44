package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McTargetPrice;
import com.zielsmart.mc.vo.McTargetPriceEXVo;
import com.zielsmart.mc.vo.McTargetPricePageSearchVo;
import com.zielsmart.mc.vo.OATargetPriceSearchVo;
import com.zielsmart.mc.vo.OATargetPriceSellerSKUVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-11-03
 */

@SqlResource("mcTargetPrice")
public interface McTargetPriceMapper extends BaseMapper<McTargetPrice> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McTargetPriceVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<McTargetPriceEXVo> queryByPage(@Param("searchVo") McTargetPricePageSearchVo searchVo, PageRequest pageRequest);

    DefaultPageResult<McTargetPriceEXVo> querynotMatchInfo(McTargetPricePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * getsellerSku
     * 根据平台、站点获取目标价
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link java.util.List<com.zielsmart.mc.vo.OATargetPriceSellerSKUVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<OATargetPriceSellerSKUVo> getsellerSku(@Param("searchVo") OATargetPriceSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchUpdatePrice
     * 批量更新目标价
     *
     * @param priceList
     * <AUTHOR>
     * @history
     */
    default void updatePriceByAids(@Param("priceList") List<McTargetPrice> priceList) {
        this.getSQLManager().updateBatch(SqlId.of("mcTargetPrice.updatePriceByAids"), priceList);
    }

    default void auditByIdBatch(@Param("priceList") List<McTargetPrice> priceList) {
        this.getSQLManager().updateBatch(SqlId.of("mcTargetPrice.auditPriceByAids"), priceList);
    }
}
