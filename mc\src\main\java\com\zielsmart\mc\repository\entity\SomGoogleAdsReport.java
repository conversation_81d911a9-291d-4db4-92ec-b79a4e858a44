package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* Google广告报表
* gen by 代码生成器 2023-04-27
*/

@Table(name="mc.som_google_ads_report")
public class SomGoogleAdsReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 账号名称
	 */
	@Column("account_name")
	private String accountName ;
	/**
	 * 广告ID
	 */
	@Column("campaign_id")
	private Long campaignId ;
	/**
	 * 广告名称
	 */
	@Column("campaign_name")
	private String campaignName ;
	/**
	 * 广告类型
	 */
	@Column("campaign_type")
	private String campaignType ;
	/**
	 * 广告日期，格式：“yyyy-MM-dd”
	 */
	@Column("day")
	private Date day ;
	/**
	 * 曝光量
	 */
	@Column("impr")
	private Integer impr ;
	/**
	 * 查看次数
	 */
	@Column("views")
	private Integer views ;
	/**
	 * 点击次数
	 */
	@Column("clicks")
	private Integer clicks ;
	/**
	 * 花费
	 */
	@Column("cost")
	private Long cost ;
	/**
	 * 总加购数
	 */
	@Column("all_conversions")
	private BigDecimal allConversions ;
	/**
	 * 订单转化量
	 */
	@Column("conversions")
	private BigDecimal conversions ;
	/**
	 * 当天销售额
	 */
	@Column("conversions_value")
	private BigDecimal conversionsValue ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 资源名称
	 */
	@Column("resouce_name")
	private String resouceName ;
	/**
	 * 站点
	 */
	@Column("labels_on_campaign")
	private String labelsOnCampaign ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomGoogleAdsReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 账号名称
	*@return
	*/
	public String getAccountName(){
		return  accountName;
	}
	/**
	* 账号名称
	*@param  accountName
	*/
	public void setAccountName(String accountName ){
		this.accountName = accountName;
	}
	/**
	* 广告ID
	*@return
	*/
	public Long getCampaignId(){
		return  campaignId;
	}
	/**
	* 广告ID
	*@param  campaignId
	*/
	public void setCampaignId(Long campaignId ){
		this.campaignId = campaignId;
	}
	/**
	* 广告名称
	*@return
	*/
	public String getCampaignName(){
		return  campaignName;
	}
	/**
	* 广告名称
	*@param  campaignName
	*/
	public void setCampaignName(String campaignName ){
		this.campaignName = campaignName;
	}
	/**
	* 广告类型
	*@return
	*/
	public String getCampaignType(){
		return  campaignType;
	}
	/**
	* 广告类型
	*@param  campaignType
	*/
	public void setCampaignType(String campaignType ){
		this.campaignType = campaignType;
	}
	/**
	* 广告日期，格式：“yyyy-MM-dd”
	*@return
	*/
	public Date getDay(){
		return  day;
	}
	/**
	* 广告日期，格式：“yyyy-MM-dd”
	*@param  day
	*/
	public void setDay(Date day ){
		this.day = day;
	}
	/**
	* 曝光量
	*@return
	*/
	public Integer getImpr(){
		return  impr;
	}
	/**
	* 曝光量
	*@param  impr
	*/
	public void setImpr(Integer impr ){
		this.impr = impr;
	}
	/**
	* 查看次数
	*@return
	*/
	public Integer getViews(){
		return  views;
	}
	/**
	* 查看次数
	*@param  views
	*/
	public void setViews(Integer views ){
		this.views = views;
	}
	/**
	* 点击次数
	*@return
	*/
	public Integer getClicks(){
		return  clicks;
	}
	/**
	* 点击次数
	*@param  clicks
	*/
	public void setClicks(Integer clicks ){
		this.clicks = clicks;
	}
	/**
	* 花费
	*@return
	*/
	public Long getCost(){
		return  cost;
	}
	/**
	* 花费
	*@param  cost
	*/
	public void setCost(Long cost ){
		this.cost = cost;
	}
	/**
	* 总加购数
	*@return
	*/
	public BigDecimal getAllConversions(){
		return  allConversions;
	}
	/**
	* 总加购数
	*@param  allConversions
	*/
	public void setAllConversions(BigDecimal allConversions ){
		this.allConversions = allConversions;
	}
	/**
	* 订单转化量
	*@return
	*/
	public BigDecimal getConversions(){
		return  conversions;
	}
	/**
	* 订单转化量
	*@param  conversions
	*/
	public void setConversions(BigDecimal conversions ){
		this.conversions = conversions;
	}
	/**
	* 当天销售额
	*@return
	*/
	public BigDecimal getConversionsValue(){
		return  conversionsValue;
	}
	/**
	* 当天销售额
	*@param  conversionsValue
	*/
	public void setConversionsValue(BigDecimal conversionsValue ){
		this.conversionsValue = conversionsValue;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 资源名称
	*@return
	*/
	public String getResouceName(){
		return  resouceName;
	}
	/**
	* 资源名称
	*@param  resouceName
	*/
	public void setResouceName(String resouceName ){
		this.resouceName = resouceName;
	}
	/**
	* 站点
	*@return
	*/
	public String getLabelsOnCampaign(){
		return  labelsOnCampaign;
	}
	/**
	* 站点
	*@param  labelsOnCampaign
	*/
	public void setLabelsOnCampaign(String labelsOnCampaign ){
		this.labelsOnCampaign = labelsOnCampaign;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
