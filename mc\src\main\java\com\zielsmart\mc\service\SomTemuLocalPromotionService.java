package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-03-12 09:03:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuLocalPromotionService {
    
    @Resource
    private SomTemuLocalPromotionMapper somTemuLocalPromotionMapper;

    @Resource
    private SomTemuLocalPromotionDetailMapper somTemuLocalPromotionDetailMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuLocalPromotionVo>}
     */
    public PageVo<SomTemuLocalPromotionVo> queryByPage(SomTemuLocalPromotionPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuLocalPromotionVo> pageResult = somTemuLocalPromotionMapper.queryByPage(searchVo, pageRequest);
        List<SomTemuLocalPromotionVo> localPromotionVos = pageResult.getList();
        if (CollUtil.isNotEmpty(localPromotionVos)) {
            List<McDictionaryInfo> dictionaryInfos = queryTemuLocalPromotionDictionary();
            Map<String, List<McDictionaryInfo>> dictMap = dictionaryInfos.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode));
            Map<String, String> activityStatusMap = dictMap.get("TemuPromotionActivityStatus").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> activityTypeMap = dictMap.get("TemuLocalPromotionActivityType").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> temuAccountMap = dictMap.get("TemuAccount").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
            localPromotionVos.forEach(data -> {
                data.setActivityStatusDesc(activityStatusMap.get(data.getActivityStatus() + ""));
                data.setActivityTypeDesc(activityTypeMap.get(data.getActivityType() + ""));
                data.setAccountName(temuAccountMap.get(data.getAccountId() + ""));
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuLocalPromotionVo.class, searchVo);
    }

    /**
     * detail
     * 分页查询产品明细
     *
     * @param searchVo 入参
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomTemuLocalPromotionDetailVo>}
     */
    public PageVo<SomTemuLocalPromotionDetailVo> detailQueryByPage(SomTemuLocalPromotionDetailPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuLocalPromotionDetailVo> pageResult = somTemuLocalPromotionDetailMapper.queryByPage(searchVo, pageRequest);

        // 关联Temu供货价
        if (!pageResult.getList().isEmpty()) {
            // 本本sku_id
            List<Long> localSkuIdList = new ArrayList<>();
            pageResult.getList().forEach(item -> {
                localSkuIdList.add(Long.parseLong(item.getSkuId()));
            });

            // 本本供货价
            List<SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceList = Collections.emptyList();
            if (!localSkuIdList.isEmpty()) {
                somTemuLocalSupplierPriceList = dynamicSqlManager.getMapper(SomTemuLocalSupplierPriceMapper.class).createLambdaQuery()
                        .andIn("sku_id", localSkuIdList)
                        .select();
            }
            Map<String, SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceMap = somTemuLocalSupplierPriceList.stream().collect(Collectors.toMap(
                    x -> x.getSkuId().toString() + x.getAccountId(),
                    Function.identity(),
                    (x1, x2) -> x1
            ));

            // 关联供货价
            pageResult.getList().forEach(item -> {
                String key = item.getSkuId() + item.getAccountId();
                if (somTemuLocalSupplierPriceMap.containsKey(key)) {
                    SomTemuLocalSupplierPrice somTemuLocalSupplierPrice = somTemuLocalSupplierPriceMap.get(key);
                    if (ObjectUtil.isNotEmpty(somTemuLocalSupplierPrice)) {
                        item.setDailySupplyPrice(somTemuLocalSupplierPrice.getSupplierPrice());
                    }
                }
            });
        }

        return ConvertUtils.pageConvert(pageResult, SomTemuLocalPromotionDetailVo.class, searchVo);
    }

    /**
     * 查询Temu营销活动(Local)相关字典集合
     *  TemuLocalPromotionActivityType：活动类型
     *  TemuPromotionActivityStatus：活动状态
     *  TemuAccount：店铺
     *
     * @return 字典
     */
    public List<McDictionaryInfo> queryTemuLocalPromotionDictionary() {
        List<String> list = Arrays.asList("TemuLocalPromotionActivityType", "TemuPromotionActivityStatus", "TemuAccount");
        return mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();
    }

    public PageVo<SomTemuLocalPromotionDetailVo> detailCandidateQueryByPage(SomTemuLocalPromotionDetailPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuLocalPromotionDetailVo> pageResult = somTemuLocalPromotionMapper.detailCandidateQueryByPage(searchVo,pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuLocalPromotionDetailVo.class, searchVo);
    }

    public String export(SomTemuLocalPromotionPageSearchVo searchVo) throws ValidateException {
        List<SomTemuLocalPromotionVo> records = somTemuLocalPromotionMapper.exportExcel(searchVo);
        if (!records.isEmpty()) {
            //根据searchVo 的exportType 的值动态查询，提取map
            Set<String> accountIds = records.stream().map(SomTemuLocalPromotionVo::getAccountId).collect(Collectors.toSet());
            if (accountIds.isEmpty()) {
                return null;
            }
            Map<String, List<SomTemuLocalPromotionDetailVo>> promotionMap = new HashMap<>();
            Map<String, List<SomTemuLocalPromotionDetailVo>> candidateMap = new HashMap<>();
            if(searchVo.getExportType() == 1 || searchVo.getExportType() == 2){
                List<SomTemuLocalPromotionDetailVo> promotionList = somTemuLocalPromotionDetailMapper.promotionSelect(accountIds);
                // 关联Temu供货价
                if (CollUtil.isNotEmpty(promotionList)) {
                    // 本本sku_id
                    List<Long> localSkuIdList = new ArrayList<>();
                    promotionList.forEach(item -> {
                        localSkuIdList.add(Long.parseLong(item.getSkuId()));
                    });

                    // 本本供货价
                    List<SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceList = Collections.emptyList();
                    if (!localSkuIdList.isEmpty()) {
                        somTemuLocalSupplierPriceList = dynamicSqlManager.getMapper(SomTemuLocalSupplierPriceMapper.class).createLambdaQuery()
                                .andIn("sku_id", localSkuIdList)
                                .select();
                    }
                    Map<String, SomTemuLocalSupplierPrice> somTemuLocalSupplierPriceMap = somTemuLocalSupplierPriceList.stream().collect(Collectors.toMap(
                            x -> x.getSkuId().toString() + x.getAccountId(),
                            Function.identity(),
                            (x1, x2) -> x1
                    ));

                    // 关联供货价
                    promotionList.forEach(item -> {
                        String key = item.getSkuId() + item.getAccountId();
                        if (somTemuLocalSupplierPriceMap.containsKey(key)) {
                            SomTemuLocalSupplierPrice somTemuLocalSupplierPrice = somTemuLocalSupplierPriceMap.get(key);
                            if (ObjectUtil.isNotEmpty(somTemuLocalSupplierPrice)) {
                                item.setDailySupplyPrice(somTemuLocalSupplierPrice.getSupplierPrice());
                            }
                        }
                    });
                }
                promotionMap = promotionList.stream().collect(Collectors.groupingBy(x->x.getAccountId()+x.getActivityId()));
            }
            if(searchVo.getExportType() == 1 || searchVo.getExportType() == 3){
                List<SomTemuLocalPromotionDetailVo> candidateList = somTemuLocalPromotionMapper.candidateSelect(accountIds);
                candidateMap = candidateList.stream().collect(Collectors.groupingBy(x -> x.getAccountId() + x.getActivityId()));
            }

            List<McDictionaryInfo> dictionaryInfos = queryTemuLocalPromotionDictionary();
            Map<String, List<McDictionaryInfo>> dictMap = dictionaryInfos.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode));
            Map<String, String> activityStatusMap = dictMap.get("TemuPromotionActivityStatus").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> activityTypeMap = dictMap.get("TemuLocalPromotionActivityType").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable));
            Map<String, String> temuAccountMap = dictMap.get("TemuAccount").stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));

            List<SomTemuLocalPromotionDetailVo> detailVos = new ArrayList<>();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (SomTemuLocalPromotionVo record : records) {
                List<SomTemuLocalPromotionDetailVo> promotionList = promotionMap.get(record.getAccountId() + record.getActivityId());
                if(CollUtil.isNotEmpty(promotionList)){
                    for (SomTemuLocalPromotionDetailVo el : promotionList) {
                        SomTemuLocalPromotionDetailVo elVo = ConvertUtils.beanConvert(el,SomTemuLocalPromotionDetailVo.class);
                        elVo.setActivityName(record.getActivityName());
                        elVo.setSite(record.getSite());
                        elVo.setActivityStatusDesc(activityStatusMap.get(record.getActivityStatus() + ""));
                        elVo.setActivityTypeDesc(activityTypeMap.get(record.getActivityType() + ""));
                        elVo.setAccountName(temuAccountMap.get(record.getAccountId() + ""));
                        elVo.setActivityTime((record.getActivityStartTime()!=null?sdf.format(record.getActivityStartTime()): "-") + " ~ " + (record.getActivityEndTime()!=null?sdf.format(record.getActivityEndTime()):"-"));
                        elVo.setType("已报名产品");
                        detailVos.add(elVo);
                    }
                }
                List<SomTemuLocalPromotionDetailVo> candidateList = candidateMap.get(record.getAccountId() + record.getActivityId());
                if(CollUtil.isNotEmpty(candidateList)){
                    for (SomTemuLocalPromotionDetailVo el : candidateList) {
                        SomTemuLocalPromotionDetailVo elVo = ConvertUtils.beanConvert(el,SomTemuLocalPromotionDetailVo.class);
                        elVo.setActivityName(record.getActivityName());
                        elVo.setSite(record.getSite());
                        elVo.setActivityStatusDesc(activityStatusMap.get(record.getActivityStatus() + ""));
                        elVo.setActivityTypeDesc(activityTypeMap.get(record.getActivityType() + ""));
                        elVo.setAccountName(temuAccountMap.get(record.getAccountId() + ""));
                        elVo.setActivityTime((record.getActivityStartTime()!=null?sdf.format(record.getActivityStartTime()): "-") + " ~ " + (record.getActivityEndTime()!=null?sdf.format(record.getActivityEndTime()):"-"));
                        elVo.setType("推荐产品");
                        detailVos.add(elVo);
                    }
                }
            }

            if (detailVos.isEmpty()) {
                return null;
            }

            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Temu营销活动");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuLocalPromotionDetailVo.class, detailVos);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出可售库存报表失败：" + e.getMessage());
            }
        }
        return null;
    }
}
