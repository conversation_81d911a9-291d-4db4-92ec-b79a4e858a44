package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McNewGoodInfoMarkVo;
import com.zielsmart.mc.vo.McNewGoodInfoPageSearchVo;
import com.zielsmart.mc.vo.McNewGoodInfoVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2022-02-09
*/

@SqlResource("mcNewGoodInfo")
public interface McNewGoodInfoMapper extends BaseMapper<McNewGoodInfo> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McNewGoodInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McNewGoodInfoVo> queryByPage(@Param("searchVo") McNewGoodInfoPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * batchFinish
     * 完成上货
     * @param goodInfoList
     * <AUTHOR>
     * @history
     */
    default void batchFinish(@Param("goodInfoList") List<McNewGoodInfo> goodInfoList) {
        this.getSQLManager().updateBatch(SqlId.of("mcNewGoodInfo.batchFinish"), goodInfoList);
    }

    /**
     * batchUpdate
     * 批量更新
     *
     * @param goodInfoList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("goodInfoList") List<McNewGoodInfo> goodInfoList) {
        this.getSQLManager().updateBatch(SqlId.of("mcNewGoodInfo.batchUpdate"), goodInfoList);
    }

    /**
     * editRemark
     * 编辑备注
     *
     * @param markVo
     * <AUTHOR>
     * @history
     */
    default void editRemark(@Param("markVo") McNewGoodInfoMarkVo markVo) {
        this.getSQLManager().update(SqlId.of("mcNewGoodInfo.editRemark"), markVo);
    }

    /**
     * editErrorMsg
     * 标记错误信息
     *
     * @param markVo
     * <AUTHOR>
     * @history
     */
    default void editErrorMsg(@Param("markVo") McNewGoodInfoMarkVo markVo) {
        this.getSQLManager().update(SqlId.of("mcNewGoodInfo.editErrorMsg"), markVo);
    }

    List<String> noticeSite(@Param("site")String site);
}
