package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.zielsmart.mc.repository.mapper.SomConforamaAutoPushStockRecordMapper;
import com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-04-09 16:44:41
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomConforamaAutoPushStockRecordService {
    
    @Resource
    private SomConforamaAutoPushStockRecordMapper somConforamaAutoPushStockRecordMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;


    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomConforamaAutoPushStockRecordVo> queryByPage(SomConforamaAutoPushStockRecordPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomConforamaAutoPushStockRecordVo> pageResult = dynamicSqlManager.getMapper(SomConforamaAutoPushStockRecordMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomConforamaAutoPushStockRecordVo.class, searchVo);
    }


    public String export(SomConforamaAutoPushStockRecordPageSearchVo searchVo) {
        List<SomConforamaAutoPushStockRecordVo> records = somConforamaAutoPushStockRecordMapper.exportExcel(searchVo);
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Conforama fr Offers历史记录管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);

                workbook = ExcelExportUtil.exportExcel(params, SomConforamaAutoPushStockRecordVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
