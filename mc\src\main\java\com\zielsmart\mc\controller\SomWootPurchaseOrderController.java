package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomWootPurchaseOrderService;
import com.zielsmart.mc.vo.SomWootPromotionVo;
import com.zielsmart.mc.vo.SomWootPurchaseOrderAsinQueryVo;
import com.zielsmart.mc.vo.SomWootPurchaseOrderPageSearchVo;
import com.zielsmart.mc.vo.SomWootPurchaseOrderVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWootPurchaseOrderController
 * @description
 * @date 2024-06-04 10:40:52
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somWootPurchaseOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "woot反馈的采购订单管理")
public class SomWootPurchaseOrderController extends BasicController {

    @Resource
    SomWootPurchaseOrderService somWootPurchaseOrderService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWootPurchaseOrderVo>> queryByPage(@RequestBody SomWootPurchaseOrderPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWootPurchaseOrderService.queryByPage(searchVo));
    }

    @Operation(summary = "新增/编辑")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWootPurchaseOrderVo somWootPurchaseOrderVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        somWootPurchaseOrderService.save(somWootPurchaseOrderVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "编辑付款进度")
    @PostMapping(value = "/editPaymentStatus")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> editPaymentStatus(@RequestBody SomWootPurchaseOrderVo somWootPurchaseOrderVo) throws ValidateException, JsonProcessingException {
        somWootPurchaseOrderService.editPaymentStatus(somWootPurchaseOrderVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWootPurchaseOrderVo somWootPurchaseOrderVo) throws ValidateException {
        somWootPurchaseOrderService.delete(somWootPurchaseOrderVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "根据ASIN获取展示码")
    @PostMapping(value = "/queryByAsin")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomWootPromotionVo> queryByAsin(@RequestBody SomWootPurchaseOrderAsinQueryVo somWootPurchaseOrderAsinQueryVo) throws ValidateException {
        return ResultVo.ofSuccess(somWootPurchaseOrderService.queryByAsin(somWootPurchaseOrderAsinQueryVo));
    }

    @Operation(summary = "上传附件{purchaseOrder:'',typeName:'',type:''}")
    @PostMapping(value = "/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> uploadFile(@RequestParam Map param, @RequestParam("file") MultipartFile multipartFile) throws IOException {
        return ResultVo.ofSuccess(somWootPurchaseOrderService.uploadFile(param, multipartFile));
    }

    @Operation(summary = "下载文件{aid:'',type:''}")
    @PostMapping(value = "/downloadFile")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> downloadFile(@RequestBody Map param) throws Exception {
        return ResultVo.ofSuccess(somWootPurchaseOrderService.downloadFile(param));
    }

    @Operation(summary = "生成发票")
    @PostMapping(value = "/generateInvoice")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> generateInvoice(@RequestBody SomWootPurchaseOrderVo orderVo) throws Exception {
        somWootPurchaseOrderService.generateInvoice(orderVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomWootPurchaseOrderPageSearchVo searchVo) {
        String data = somWootPurchaseOrderService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
