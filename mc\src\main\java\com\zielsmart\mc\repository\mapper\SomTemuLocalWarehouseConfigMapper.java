package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuLocalWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Temu本本可售仓库配置管理
 * @date 2025-06-27 11:34:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuLocalWarehouseConfig")
public interface SomTemuLocalWarehouseConfigMapper extends BaseMapper<SomTemuLocalWarehouseConfig> {

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return PageResult<SomTemuLocalWarehouseConfigVo>
     */
    PageResult<SomTemuLocalWarehouseConfigVo> queryByPage(SomTemuLocalWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
