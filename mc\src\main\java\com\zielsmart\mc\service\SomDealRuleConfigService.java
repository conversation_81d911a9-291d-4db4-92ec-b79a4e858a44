package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomDealRuleConfig;
import com.zielsmart.mc.repository.mapper.SomDealRuleConfigMapper;
import com.zielsmart.mc.vo.SomDealRuleConfigExtVo;
import com.zielsmart.mc.vo.SomDealRuleConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomDealRuleConfigService {

    @Resource
    private SomDealRuleConfigMapper somDealRuleConfigMapper;


    /**
     * save
     * 新增或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void addOrEdit(SomDealRuleConfigVo addOrEditVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addOrEditVo) || StrUtil.isEmpty(addOrEditVo.getSite()) || StrUtil.isEmpty(addOrEditVo.getDealType()) || CollectionUtil.isEmpty(addOrEditVo.getExtVoList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isEmpty(addOrEditVo.getAid())) {
            long count = somDealRuleConfigMapper.createLambdaQuery().andEq("site", addOrEditVo.getSite()).andEq("deal_type", addOrEditVo.getDealType()).count();
            if (count > 0) {
                throw new ValidateException("当前站点该类型已配置");
            }
            addOrEditVo.setAid(IdUtil.fastSimpleUUID());
            addOrEditVo.setPlatform("Amazon");
            addOrEditVo.setSite(addOrEditVo.getSite());
            addOrEditVo.setDealType(addOrEditVo.getDealType());
            addOrEditVo.setRules(JSONUtil.toJsonStr(addOrEditVo.getExtVoList()));
            addOrEditVo.setCreateNum(tokenUser.getJobNumber());
            addOrEditVo.setCreateName(tokenUser.getUserName());
            addOrEditVo.setCreateTime(DateTime.now().toJdkDate());
            somDealRuleConfigMapper.insert(ConvertUtils.beanConvert(addOrEditVo, SomDealRuleConfig.class));
        } else {
            Long count = somDealRuleConfigMapper.createLambdaQuery().andEq("site", addOrEditVo.getSite()).andEq("deal_type", addOrEditVo.getDealType())
                    .andNotEq("aid", addOrEditVo.getAid()).count();
            if (count > 0) {
                throw new ValidateException("当前站点该类型已配置");
            }
            SomDealRuleConfig obj = somDealRuleConfigMapper.createLambdaQuery().andEq("aid", addOrEditVo.getAid()).single();
            if (ObjectUtil.isNotEmpty(obj)) {
                obj.setSite(addOrEditVo.getSite());
                obj.setDealType(addOrEditVo.getDealType());
                obj.setRules(JSONUtil.toJsonStr(addOrEditVo.getExtVoList()));
                obj.setModifyNum(tokenUser.getJobNumber());
                obj.setModifyName(tokenUser.getUserName());
                obj.setModifyTime(DateTime.now().toJdkDate());
                somDealRuleConfigMapper.updateById(obj);
            }
        }

    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomDealRuleConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomDealRuleConfigVo> queryByPage(SomDealRuleConfigPageSearchVo pageSearchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(pageSearchVo.getCurrent(), pageSearchVo.getPageSize());
        PageResult<SomDealRuleConfigVo> pageResult = somDealRuleConfigMapper.queryByPage(pageSearchVo, pageRequest);
        for (SomDealRuleConfigVo configVo : pageResult.getList()) {
            JSONArray array = JSONUtil.parseArray(configVo.getRules());
            configVo.setExtVoList(JSONUtil.toList(array, SomDealRuleConfigExtVo.class));
        }
        return ConvertUtils.pageConvert(pageResult, SomDealRuleConfigVo.class, pageSearchVo);
    }

    /**
     * queryByAid
     * 查看
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomDealRuleConfigVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomDealRuleConfigVo queryByAid(SomDealRuleConfigVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isEmpty(searchVo.getAid())) {
            throw new ValidateException("请选择要查看的数据");
        }
        SomDealRuleConfig obj = somDealRuleConfigMapper.createLambdaQuery().andEq("aid", searchVo.getAid()).single();
        SomDealRuleConfigVo configVo = ConvertUtils.beanConvert(obj, SomDealRuleConfigVo.class);
        if (ObjectUtil.isNotEmpty(configVo) && StrUtil.isNotEmpty(configVo.getRules())) {
            JSONArray array = JSONUtil.parseArray(configVo.getRules());
            configVo.setExtVoList(JSONUtil.toList(array, SomDealRuleConfigExtVo.class));
        }
        return configVo;
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomDealRuleConfigVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || StrUtil.isEmpty(deleteVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somDealRuleConfigMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).delete();
    }
}
