package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomDealRuleWhiteList;
import com.zielsmart.mc.repository.mapper.SomDealRuleWhiteListMapper;
import com.zielsmart.mc.vo.SomDealRuleWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.Base64;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomDealRuleWhiteListService
 * @description
 * @date 2024-03-06 10:02:04
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomDealRuleWhiteListService {

    @Resource
    private SomDealRuleWhiteListMapper somDealRuleWhiteListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomDealRuleWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomDealRuleWhiteListVo> queryByPage(SomDealRuleWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomDealRuleWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomDealRuleWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomDealRuleWhiteListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somDealRuleWhiteListVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomDealRuleWhiteListVo somDealRuleWhiteListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleWhiteListVo) || StrUtil.isBlank(somDealRuleWhiteListVo.getPlatform()) || StrUtil.isBlank(somDealRuleWhiteListVo.getSite()) || StrUtil.isBlank(somDealRuleWhiteListVo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somDealRuleWhiteListMapper.createLambdaQuery().andEq("platform", somDealRuleWhiteListVo.getPlatform()).andEq("site", somDealRuleWhiteListVo.getSite())
                .andEq("seller_sku", somDealRuleWhiteListVo.getSellerSku()).count();
        if (count > 0) {
            throw new ValidateException("数据已存在，不允许重复");
        }
        somDealRuleWhiteListVo.setAid(IdUtil.fastSimpleUUID());
        somDealRuleWhiteListVo.setCreateNum(tokenUser.getJobNumber());
        somDealRuleWhiteListVo.setCreateName(tokenUser.getUserName());
        somDealRuleWhiteListVo.setCreateTime(DateTime.now().toJdkDate());
        somDealRuleWhiteListMapper.insert(ConvertUtils.beanConvert(somDealRuleWhiteListVo, SomDealRuleWhiteList.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somDealRuleWhiteListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomDealRuleWhiteListVo somDealRuleWhiteListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somDealRuleWhiteListVo) || CollectionUtil.isEmpty(somDealRuleWhiteListVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somDealRuleWhiteListMapper.createLambdaQuery().andIn("aid", somDealRuleWhiteListVo.getAidList()).delete();
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String export(SomDealRuleWhiteListPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomDealRuleWhiteListVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "校验规则白名单列表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomDealRuleWhiteListVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * importExcel
     * 导入
     *
     * @param list
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomDealRuleWhiteListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        if (CollectionUtil.isNotEmpty(list)) {
            for (SomDealRuleWhiteListVo vo : list) {
                if (StrUtil.isBlank(vo.getPlatform()) || StrUtil.isBlank(vo.getSite()) || StrUtil.isBlank(vo.getSellerSku())) {
                    throw new ValidateException("「平台」「站点」「展示码」不允许为空，请检查导入的文件");
                }
            }
            StringBuilder msg = new StringBuilder();
            //校验Excel中是否有重复数据
            Map<String, List<SomDealRuleWhiteListVo>> excelMap = list.stream().collect(Collectors.groupingBy(f -> f.getPlatform() + f.getSite() + f.getSellerSku()));
            for (String key : excelMap.keySet()) {
                List<SomDealRuleWhiteListVo> voList = excelMap.get(key);
                if (voList.size() > 1) {
                    SomDealRuleWhiteListVo vo = voList.get(0);
                    msg.append(vo.getPlatform()).append(" ").append(vo.getSite()).append(" ").append(vo.getSellerSku()).append("\n");
                }
            }
            if (StrUtil.isNotEmpty(msg.toString())) {
                throw new ValidateException(msg + "不允许出现重复的数据，请检查后重新导入");
            }
            List<String> skuList = list.stream().filter(f -> StrUtil.isNotBlank(f.getSellerSku())).map(SomDealRuleWhiteListVo::getSellerSku).distinct().collect(Collectors.toList());
            //校验平台+站点+展示码在系统中是否存在
            List<SomDealRuleWhiteList> whiteList = somDealRuleWhiteListMapper.createLambdaQuery().andIn("seller_sku", skuList).select();
            Map<String, SomDealRuleWhiteList> whiteListMap = whiteList.stream().collect(Collectors.toMap(x -> x.getPlatform() + x.getSite() + x.getSellerSku(), Function.identity(), (x1, x2) -> x2));
            list.forEach(f -> {
                if (whiteListMap.containsKey(f.getPlatform() + f.getSite() + f.getSellerSku())) {
                    msg.append(f.getPlatform()).append(" ").append(f.getSite()).append(" ").append(f.getSellerSku()).append("\n");
                }
            });
            if (StrUtil.isNotEmpty(msg.toString())) {
                throw new ValidateException(msg + "已在白名单中存在，不允许重复维护");
            }
            list.forEach(f -> {
                f.setAid(IdUtil.fastSimpleUUID());
                f.setCreateNum(tokenUser.getJobNumber());
                f.setCreateName(tokenUser.getUserName());
                f.setCreateTime(DateTime.now().toJdkDate());
            });
            somDealRuleWhiteListMapper.insertBatch(ConvertUtils.listConvert(list, SomDealRuleWhiteList.class));
        }
    }
}
