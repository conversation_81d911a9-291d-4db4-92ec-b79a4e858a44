package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomInventoryChannelConfigMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.McDearanceProductExVo;
import com.zielsmart.mc.vo.SomInventoryChannelConfigPageSearchVo;
import com.zielsmart.mc.vo.SomInventoryChannelConfigVo;
import com.zielsmart.mc.vo.SomInventoryChannelImportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.annotation.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomInventoryChannelConfigService {

    @Resource
    private SomInventoryChannelConfigMapper somInventoryChannelConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McWarehouseMapper warehouseMapper;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomInventoryChannelConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomInventoryChannelConfigVo> queryByPage(SomInventoryChannelConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomInventoryChannelConfigVo> pageResult = dynamicSqlManager.getMapper(SomInventoryChannelConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomInventoryChannelConfig> list = somInventoryChannelConfigMapper.all();
            Map<String, List<SomInventoryChannelConfig>> configMap = list.stream().collect(Collectors.groupingBy(e -> e.getPlatform() + e.getSite() + e.getDeliveryType() + e.getStorageMarket()));
            List<SomStorageLocation> storageList = somStorageLocationMapper.all();
            Map<String, String> slMap = storageList.stream().collect(Collectors.toMap(x -> x.getslCode(), y -> y.getslName(), (x1, x2) -> x1));
            List<McWarehouse> whList = warehouseMapper.all();
            Map<String, String> whMap = whList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), y -> y.getWarehouseName(), (x1, x2) -> x1));

            for (SomInventoryChannelConfigVo config : pageResult.getList()) {
                String key = config.getPlatform() + config.getSite() + config.getDeliveryType() + config.getStorageMarket();
                if (configMap.containsKey(key)) {
                    List<SomInventoryChannelConfig> configs = configMap.get(key);
                    List<SomInventoryChannelConfigVo.ConfigMarket> configMarketList = new ArrayList<>();
                    List<String> warehouseList = new ArrayList<>();
                    for (SomInventoryChannelConfig config1 : configs) {
                        SomInventoryChannelConfigVo.ConfigMarket configMarket = new SomInventoryChannelConfigVo.ConfigMarket();
                        String wlName = whMap.get(config1.getWarehouseCode());
                        String slName = slMap.get(config1.getStorageLocation());
                        configMarket.setWarehouseCode(config1.getWarehouseCode());
                        configMarket.setWarehouseName(wlName);
                        configMarket.setStorageLocation(config1.getStorageLocation());
                        configMarket.setStorageLocationName(slName);
                        warehouseList.add(wlName + "-" + slName);
                        configMarketList.add(configMarket);
                    }
                    config.setWarehouseList(warehouseList);
                    config.setConfigMarketList(configMarketList);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomInventoryChannelConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param configVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(SomInventoryChannelConfigVo configVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(configVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String platform = configVo.getPlatform();
        String site = configVo.getSite();
        Integer deliveryType = configVo.getDeliveryType();
        String storageMarket = configVo.getStorageMarket();
        List<SomInventoryChannelConfigVo.ConfigMarket> configMarketList = configVo.getConfigMarketList();
        validate(platform, site, deliveryType, storageMarket, configMarketList);
        long count = somInventoryChannelConfigMapper.createLambdaQuery()
                .andEq("platform", platform)
                .andEq("site", site)
                .andEq("delivery_type", deliveryType)
                .count();
        if (count > 0) {
            throw new ValidateException("平台：" + platform + "站点：" + site + " 发货方式：" + (deliveryType == 0 ? "非寄售" : "寄售") + " 已配置库存渠道，请编辑已存在的数据。");
        }
        List<SomInventoryChannelConfig> list = new ArrayList<>();
        Date now = new Date();
        for (SomInventoryChannelConfigVo.ConfigMarket configMarket : configMarketList) {
            SomInventoryChannelConfig config = new SomInventoryChannelConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setPlatform(platform);
            config.setSite(site);
            config.setDeliveryType(deliveryType);
            config.setStorageMarket(storageMarket);
            config.setWarehouseCode(configMarket.getWarehouseCode());
            config.setStorageLocation(configMarket.getStorageLocation());
            config.setCreateTime(now);
            config.setCreateName(tokenUser.getUserName());
            config.setCreateNum(tokenUser.getJobNumber());
            list.add(config);
        }
        if (!list.isEmpty()) {
            somInventoryChannelConfigMapper.insertBatch(list);
        }
    }

    private void validate(String platform, String site, Integer deliveryType, String storageMarket, List<SomInventoryChannelConfigVo.ConfigMarket> configMarketList) throws ValidateException {
        if (StrUtil.isEmpty(platform)) {
            throw new ValidateException("平台不能为空");
        }
        if (StrUtil.isEmpty(site)) {
            throw new ValidateException("站点不能为空");
        }
        if (deliveryType == null) {
            throw new ValidateException("发货方式不能为空");
        }
        if (StrUtil.isEmpty(storageMarket)) {
            throw new ValidateException("库存市场不能为空");
        }
        if (ObjectUtil.isEmpty(configMarketList)) {
            throw new ValidateException("库存渠道不能为空");
        }
    }

    /**
     * update
     * 修改
     *
     * @param somInventoryChannelConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    @Update
    public void update(SomInventoryChannelConfigVo configVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(configVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String platform = configVo.getPlatform();
        String site = configVo.getSite();
        Integer deliveryType = configVo.getDeliveryType();
        String storageMarket = configVo.getStorageMarket();
        List<SomInventoryChannelConfigVo.ConfigMarket> configMarketList = configVo.getConfigMarketList();
        validate(platform, site, deliveryType, storageMarket, configMarketList);
        List<SomInventoryChannelConfig> list = somInventoryChannelConfigMapper.createLambdaQuery()
                .andEq("platform", platform)
                .andEq("site", site)
                .andEq("delivery_type", deliveryType)
                .select();
        Map<String, SomInventoryChannelConfigVo.ConfigMarket> configMap = configMarketList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getStorageLocation(), Function.identity(), (x1, x2) -> x1));

        //判断configMarketList 和 list的关系，再执行新增 删除 修改操作
        if (!list.isEmpty()) {
            for (SomInventoryChannelConfig config : list) {
                String key = config.getWarehouseCode() + config.getStorageLocation();
                SomInventoryChannelConfigVo.ConfigMarket configMarket = configMap.getOrDefault(key, null);
                if (configMarket == null) {
                    somInventoryChannelConfigMapper.createLambdaQuery().andEq("aid", config.getAid()).delete();
                    continue;
                }
                configMap.remove(key);
                if (!config.getStorageMarket().equals(storageMarket)) {
                    config.setStorageMarket(storageMarket);
                    config.setLastModifyName(tokenUser.getUserName());
                    config.setLastModifyNum(tokenUser.getJobNumber());
                    config.setLastModifyTime(new Date());
                    somInventoryChannelConfigMapper.updateById(config);
                }
            }
            if (!configMap.isEmpty()) {
                List<SomInventoryChannelConfig> insertList = new ArrayList<>();
                for (Map.Entry<String, SomInventoryChannelConfigVo.ConfigMarket> entry : configMap.entrySet()) {
                    SomInventoryChannelConfig config = new SomInventoryChannelConfig();
                    config.setAid(IdUtil.fastSimpleUUID());
                    config.setPlatform(platform);
                    config.setSite(site);
                    config.setDeliveryType(deliveryType);
                    config.setWarehouseCode(entry.getValue().getWarehouseCode());
                    config.setStorageLocation(entry.getValue().getStorageLocation());
                    config.setStorageMarket(storageMarket);
                    config.setCreateTime(new Date());
                    config.setCreateName(tokenUser.getUserName());
                    config.setCreateNum(tokenUser.getJobNumber());
                    insertList.add(config);
                }
                if (!insertList.isEmpty()) {
                    somInventoryChannelConfigMapper.insertBatch(insertList);
                }
            }
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somInventoryChannelConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomInventoryChannelConfigVo somInventoryChannelConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somInventoryChannelConfigVo) || ObjectUtil.isEmpty(somInventoryChannelConfigVo.getPlatform()) || ObjectUtil.isEmpty(somInventoryChannelConfigVo.getSite()) || ObjectUtil.isEmpty(somInventoryChannelConfigVo.getDeliveryType())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somInventoryChannelConfigMapper.createLambdaQuery()
                .andEq("platform", somInventoryChannelConfigVo.getPlatform())
                .andEq("site", somInventoryChannelConfigVo.getSite())
                .andEq("delivery_type", somInventoryChannelConfigVo.getDeliveryType())
                .delete();
    }

    public String importExcel(List<SomInventoryChannelImportVo> importList, TokenUserInfo tokenUser) throws ValidateException {
        boolean platformIsEmpty=importList.stream().anyMatch(i->StrUtil.isEmpty(i.getPlatform()));
        if (platformIsEmpty) {
            throw new ValidateException("存在平台为空的数据，请检查！");
        }
        boolean siteIsEmpty=importList.stream().anyMatch(i->StrUtil.isEmpty(i.getSite()));
        if (siteIsEmpty) {
            throw new ValidateException("存在站点为空的数据，请检查！");
        }
        boolean deliveryTypeIsEmpty=importList.stream().anyMatch(i->StrUtil.isEmpty(i.getDeliveryType()));
        if (deliveryTypeIsEmpty) {
            throw new ValidateException("存在寄售标识为空的数据，请检查！");
        }
        boolean storageMarketIsEmpty=importList.stream().anyMatch(i->StrUtil.isEmpty(i.getStorageMarketName()));
        if (storageMarketIsEmpty) {
            throw new ValidateException("存在库存市场为空的数据，请检查！");
        }
        boolean storageWarehouseIsEmpty=importList.stream().anyMatch(i->StrUtil.isEmpty(i.getStorageWarehouseName()));
        if (storageWarehouseIsEmpty) {
            throw new ValidateException("存在库存渠道为空的数据，请检查！");
        }
        List<McDictionaryInfo> deliveryDicList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "NewDeliveryConsignmentFlag").select();
        List<McDictionaryInfo> storageMarketDicList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "StorageMarket").select();
        List<SomStorageLocation> storageList = somStorageLocationMapper.all();
        Map<String, String> slMap = storageList.stream().collect(Collectors.toMap(x -> x.getslName(), y -> y.getslCode(), (x1, x2) -> x1));
        List<McWarehouse> whList = warehouseMapper.all();
        Map<String, String> whMap = whList.stream().collect(Collectors.toMap(x -> x.getWarehouseName(), y -> y.getWarehouseCode(), (x1, x2) -> x1));
        List<String> errerList=new ArrayList<>();
        List<SomInventoryChannelConfigVo> somInventoryChannelConfigVoList=ConvertUtils.listConvert(somInventoryChannelConfigMapper.all(),SomInventoryChannelConfigVo.class);
        Map<String, List<SomInventoryChannelConfigVo>> inventoryChannelConfigMap = somInventoryChannelConfigVoList.stream().collect(Collectors.groupingBy(e -> e.getPlatform()+e.getSite()+e.getDeliveryType()+e.getStorageMarketName()));
        //以“平台” + “站点” + “寄售标识” + “库存市场”对excel中的数据进行去重
        Map<String, List<SomInventoryChannelImportVo>> importMap = importList.stream().collect(Collectors.groupingBy(e -> e.getPlatform()+e.getSite()+e.getDeliveryType()+e.getStorageMarketName()));
        List<SomInventoryChannelConfig> insertList = new ArrayList<>();
        for (String key : importMap.keySet()) {
            SomInventoryChannelImportVo somInventoryChannelImportVo=importMap.get(key).stream().findFirst().get();
            String deliveryTypeCode=deliveryDicList.stream().filter(d->StrUtil.equals(d.getItemLable(),somInventoryChannelImportVo.getDeliveryType())).findFirst().get().getItemValue();
            String storageMarket=storageMarketDicList.stream().filter(d->StrUtil.equals(d.getItemLable(),somInventoryChannelImportVo.getStorageMarketName())).findFirst().get().getItemValue();
            if (CollectionUtil.isNotEmpty(inventoryChannelConfigMap.get(somInventoryChannelImportVo.getPlatform()+somInventoryChannelImportVo.getSite()+deliveryTypeCode+storageMarket))){
                errerList.add(StrUtil.format("平台：{},站点：{} 寄售标识：{} 库存市场：{}",somInventoryChannelImportVo.getPlatform(),somInventoryChannelImportVo.getSite(),somInventoryChannelImportVo.getDeliveryType(),somInventoryChannelImportVo.getStorageMarketName()));
            }
            Date now = new Date();
            //somInventoryChannelConfig.setWarehouseCode();
            List<String> warehouseList= Arrays.asList(somInventoryChannelImportVo.getStorageWarehouseName().split(","));
            for (String warehouseName : warehouseList) {
                List<String> wSList=Arrays.asList(warehouseName.split(":"));
                SomInventoryChannelConfig somInventoryChannelConfig=new SomInventoryChannelConfig();
                somInventoryChannelConfig.setAid(IdUtil.fastSimpleUUID());
                somInventoryChannelConfig.setPlatform(somInventoryChannelImportVo.getPlatform());
                somInventoryChannelConfig.setSite(somInventoryChannelImportVo.getSite());
                somInventoryChannelConfig.setDeliveryType(Integer.valueOf(deliveryTypeCode));
                somInventoryChannelConfig.setStorageMarket(storageMarket);
                somInventoryChannelConfig.setWarehouseCode(whMap.get(wSList.get(0)));
                somInventoryChannelConfig.setStorageLocation(slMap.get(wSList.get(1)));
                somInventoryChannelConfig.setCreateTime(now);
                somInventoryChannelConfig.setCreateName(tokenUser.getUserName());
                somInventoryChannelConfig.setCreateNum(tokenUser.getJobNumber());
                insertList.add(somInventoryChannelConfig);
            }
        }
        if (CollectionUtil.isNotEmpty(errerList)) {
            throw new ValidateException(errerList.stream().collect(Collectors.joining("\n"))+" 已经存在渠道库存配置，不允许重复配置");
        }
        somInventoryChannelConfigMapper.insertBatch(insertList);
        return Strings.EMPTY;

    }
}
