package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McHandlingTimeConfigService;
import com.zielsmart.mc.vo.McHandlingTimeConfigPageSearchVo;
import com.zielsmart.mc.vo.McHandlingTimeConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McHandlingTimeConfigController
 * @description
 * @date 2021-12-01 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcHandlingTimeConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Handling Time管理")
public class McHandlingTimeConfigController extends BasicController{

    @Resource
    McHandlingTimeConfigService mcHandlingTimeConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McHandlingTimeConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<McHandlingTimeConfigVo>> queryByPage(@RequestBody McHandlingTimeConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcHandlingTimeConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcHandlingTimeConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated McHandlingTimeConfigVo mcHandlingTimeConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcHandlingTimeConfigService.save(mcHandlingTimeConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param mcHandlingTimeConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> update(@RequestBody McHandlingTimeConfigVo mcHandlingTimeConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcHandlingTimeConfigService.update(mcHandlingTimeConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcHandlingTimeConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody McHandlingTimeConfigVo mcHandlingTimeConfigVo) throws ValidateException {
        mcHandlingTimeConfigService.delete(mcHandlingTimeConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载导入模板
     * @param
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel(){
        return "forward:/static/excel/HandlingTimeTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"平台", "站点", "展示码", "Handling Time"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<McHandlingTimeConfigVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McHandlingTimeConfigVo.class, params);
        }catch (Exception e){
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if(CollectionUtil.isEmpty(list)){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        mcHandlingTimeConfigService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出数据")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> export(@RequestBody McHandlingTimeConfigPageSearchVo searchVo) {
        String data = mcHandlingTimeConfigService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("");
        }
        return ResultVo.ofSuccess(data);
    }

}
