package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McListingInfoAmazon;
import com.zielsmart.mc.vo.McListingInfoAmazonExVo;
import com.zielsmart.mc.vo.McListingInfoAmazonPageSearchVo;
import com.zielsmart.mc.vo.McListingInfoAmazonSearchExVo;
import com.zielsmart.mc.vo.McListingInfoAmazonVo;
import com.zielsmart.web.basic.vo.PageSearchVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Set;
/*
 *
 * gen by 代码生成器 mapper 2021-07-08
 */

@SqlResource("mcListingInfoAmazon")
public interface McListingInfoAmazonMapper extends BaseMapper<McListingInfoAmazon> {

    /**
     * updateByIdBatch
     * 更新库存价格
     * @param mcListingInfoAmazonList
     * <AUTHOR>
     * @history
     */
//    default void updateByIdBatch(@Param("mcListingInfoAmazonList") List<McListingInfoAmazon> mcListingInfoAmazonList){
//        this.getSQLManager().updateBatch(SqlId.of("mcListingInfoAmazon.updateMcListingInfoByAid"), mcListingInfoAmazonList);
//    }

    /**
     * updateByAsinSiteSkuBatch
     * 更新父变体sku
     *
     * @param mcListingInfoAmazonList
     * <AUTHOR>
     * @history
     */
    default void updateByAsinSiteSkuBatch(@Param("mcListingInfoAmazonList") List<McListingInfoAmazon> mcListingInfoAmazonList) {
        this.getSQLManager().updateBatch(SqlId.of("mcListingInfoAmazon.updateParentSkuByAsinSiteSku"), mcListingInfoAmazonList);
    }

    /**
     * queryByPage
     * 分页获取List报表
     *
     * @param search
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McListingInfoAmazonVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McListingInfoAmazonVo> queryByPage(@Param("search") PageSearchVo search, PageRequest pageRequest);

    /**
     * selectParentSkuByList
     * 批量查询父变体
     *
     * @param parentSkuSet
     * @param platformSet
     * @param siteSet
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonVo> selectParentSkuByList(@Param("parentSkuSet") Set<String> parentSkuSet, @Param("platformSet") Set<String> platformSet, @Param("siteSet") Set<String> siteSet);

    /**
     * queryByPlatformSiteSellerSku
     * 根据平台站点展示码查询
     *
     * @param searchExVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonExVo> queryByPlatformSiteSellerSku(@Param("searchExVo") McListingInfoAmazonSearchExVo searchExVo);

    /**
     * queryBySiteSellerSkus
     * 根据站点展示码集合查询
     *
     * @param site
     * @param sellerSkus
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonExVo> queryBySiteSellerSkus(@Param("site") String site, @Param("sellerSkus") List<String> sellerSkus);

    /**
     * queryBySiteSellerSkusPromotion
     * 根据站点展示码集合查询
     *
     * @param site
     * @param sellerSkus
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonExVo> queryBySiteSellerSkusPromotion(@Param("site") String site, @Param("sellerSkus") List<String> sellerSkus);

    /**
     * queryBySellerSkuOrAsin
     *
     * @param site
     * @param keyWord
     * @return {@link com.zielsmart.mc.vo.McListingInfoAmazonExVo}
     * <AUTHOR>
     * @history
     */
    McListingInfoAmazon queryBySellerSkuOrAsin(@Param("site") String site, @Param("keyWord") String keyWord);

    /**
     * queryBySiteSellerSkus
     * 根据站点展示码集合查询基础信息
     *
     * @param site
     * @param sellerSkus
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonExVo> searchBySiteSellerSkus(@Param("site") String site, @Param("sellerSkus") List<String> sellerSkus);

    /**
     * queryForDOTO
     * 根据平台站点展示码查询(DOTO)
     *
     * @param searchExVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonVo>}
     * <AUTHOR>
     * @history
     */
    List<McListingInfoAmazonVo> queryForDOTO(@Param("searchExVo") McListingInfoAmazonSearchExVo searchExVo);

    /**
     * queryForLD
     * 根据站点展示码或asin查询
     *
     * @param pageSearchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McListingInfoAmazonExVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McListingInfoAmazonExVo> queryForLD(@Param("pageSearchVo") McListingInfoAmazonPageSearchVo pageSearchVo, PageRequest pageRequest);

    List<McListingInfoAmazonExVo> searchSellerSkuBySite(@Param("site") String site);
}
