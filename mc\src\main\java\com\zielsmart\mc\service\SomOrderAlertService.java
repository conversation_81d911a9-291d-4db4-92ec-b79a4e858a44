package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomOrderAlert;
import com.zielsmart.mc.repository.mapper.SomOrderAlertMapper;
import com.zielsmart.mc.vo.SomBatchDeleteVo;
import com.zielsmart.mc.vo.SomOrderAlertPageSearchVo;
import com.zielsmart.mc.vo.SomOrderAlertVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomOrderAlertService {
    
    @Resource
    private SomOrderAlertMapper somOrderAlertMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOrderAlertVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomOrderAlertVo> queryByPage(SomOrderAlertPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOrderAlertVo> pageResult = dynamicSqlManager.getMapper(SomOrderAlertMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomOrderAlertVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somOrderAlertVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomOrderAlertVo somOrderAlertVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somOrderAlertVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isEmpty(somOrderAlertVo.getOrderAlertRuleName())){
            throw new ValidateException("订单预警规则名称不能为空！");
        }
        if (StrUtil.isEmpty(somOrderAlertVo.getPlatform())){
            throw new ValidateException("平台不能为空！");
        }
        if (StrUtil.isEmpty(somOrderAlertVo.getSite())){
            throw new ValidateException("站点不能为空！");
        }
        if (StrUtil.isEmpty(somOrderAlertVo.getDeliveryType())){
            throw new ValidateException("发货方式不能为空！");
        }
        if (ObjectUtil.isEmpty(somOrderAlertVo.getOrderQuantity())){
            throw new ValidateException("订单数量不能为空！");
        }
        if (StrUtil.isEmpty(somOrderAlertVo.getAlertNotificationRuleName())){
            throw new ValidateException("预警通知规则名称不能为空！");
        }
        somOrderAlertVo.setAid(IdUtil.fastSimpleUUID());
        somOrderAlertVo.setCreateName(tokenUser.getUserName());
        somOrderAlertVo.setCreateNum(tokenUser.getJobNumber());
        somOrderAlertVo.setCreateTime(DateTime.now().toJdkDate());
        somOrderAlertMapper.insert(ConvertUtils.beanConvert(somOrderAlertVo, SomOrderAlert.class));
    }

    /**
     * update
     * 修改
     * @param somOrderAlertVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomOrderAlertVo somOrderAlertVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somOrderAlertVo) || StrUtil.isEmpty(somOrderAlertVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somOrderAlertMapper.createLambdaQuery()
                .andEq("aid",somOrderAlertVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somOrderAlertVo, SomOrderAlert.class));
    }

    /**
     * delete
     * 删除
     * @param somBatchDeleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomBatchDeleteVo somBatchDeleteVo) throws ValidateException {
        if (CollectionUtil.isEmpty(somBatchDeleteVo.getAids())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somOrderAlertMapper.createLambdaQuery().andIn("aid", somBatchDeleteVo.getAids()).delete();
    }
}
