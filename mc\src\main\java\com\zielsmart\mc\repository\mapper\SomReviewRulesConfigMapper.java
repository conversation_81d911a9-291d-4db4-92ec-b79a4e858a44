package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomReviewRulesConfig;
import com.zielsmart.mc.vo.SomReviewRulesConfigPageSearchVo;
import com.zielsmart.mc.vo.SomReviewRulesConfigVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-04-12
 */

@SqlResource("somReviewRulesConfig")
public interface SomReviewRulesConfigMapper extends BaseMapper<SomReviewRulesConfig> {

    /**
     * query
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomReviewRulesConfigVo>}
     * <AUTHOR>
     * @history
     */
    List<SomReviewRulesConfigVo> query(@Param("searchVo") SomReviewRulesConfigPageSearchVo searchVo);
}
