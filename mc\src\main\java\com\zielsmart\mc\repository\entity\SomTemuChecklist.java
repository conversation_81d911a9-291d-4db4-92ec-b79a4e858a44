package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2024-09-05
*/

@Table(name="mc.som_temu_checklist")
public class SomTemuChecklist implements java.io.Serializable {
	/**
	 * 主键aid
	 */
	@AssignID
	private String aid;
	/**
	 * 核价单ID
	 */
	@Column("price_order_id")
	private Long priceOrderId;
	/**
	 * TEMU产品ID
	 */
	@Column("goods_id")
	private Long goodsId;
	/**
	 * TEMU SKU ID
	 */
	@Column("sku_id")
	private Long skuId;
	/**
	 * 核价记录ID
	 */
	@Column("price_commit_id")
	private Long priceCommitId;
	/**
	 * 核价单版本
	 */
	@Column("price_commit_version")
	private Integer priceCommitVersion;
	/**
	 * 参考申报价
	 */
	@Column("suggest_supplier_price")
	private BigDecimal suggestSupplierPrice;
	/**
	 * 参考申报价-币种
	 */
	@Column("suggest_supplier_price_currency")
	private String suggestSupplierPriceCurrency;
	/**
	 * 目标申报价  也就是我司提报的供货价
	 */
	@Column("target_supplier_price")
	private BigDecimal targetSupplierPrice;
	/**
	 * 目标申报价-币种
	 */
	@Column("target_supplier_price_currency")
	private String targetSupplierPriceCurrency;
	/**
	 * 如果需要议价，这个值为我司业务手工输入的“供货价”。
	 */
	@Column("supplier_price")
	private BigDecimal supplierPrice;
	/**
	 * 币种
	 */
	@Column("supplier_price_currency")
	private String supplierPriceCurrency;
	/**
	 * 议价原因
	 */
	@Column("bargain_reason")
	private String bargainReason;
	/**
	 * 10.接受“参考供货价” 99.议价
	 */
	@Column("handle_type")
	private Integer handleType;
	/**
	 * 下载时间
	 */
	@Column("sync_time")
	private Date syncTime;

	@Column("account_id")
	private String accountId;

	/**
	 * Temu平台下载的站点列表
	 */
	@Column("sites")
	private PGobject sites;

	public SomTemuChecklist() {
	}

	public PGobject getSites() {
		return sites;
	}

	public void setSites(PGobject sites) {
		this.sites = sites;
	}

	/**
	* 主键aid
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键aid
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 核价单ID
	*@return
	*/
	public Long getPriceOrderId(){
		return  priceOrderId;
	}
	/**
	* 核价单ID
	*@param  priceOrderId
	*/
	public void setPriceOrderId(Long priceOrderId ){
		this.priceOrderId = priceOrderId;
	}
	/**
	* TEMU产品ID
	*@return
	*/
	public Long getGoodsId(){
		return  goodsId;
	}
	/**
	* TEMU产品ID
	*@param  goodsId
	*/
	public void setGoodsId(Long goodsId ){
		this.goodsId = goodsId;
	}
	/**
	* TEMU SKU ID
	*@return
	*/
	public Long getSkuId(){
		return  skuId;
	}
	/**
	* TEMU SKU ID
	*@param  skuId
	*/
	public void setSkuId(Long skuId ){
		this.skuId = skuId;
	}
	/**
	* 核价记录ID
	*@return
	*/
	public Long getPriceCommitId(){
		return  priceCommitId;
	}
	/**
	* 核价记录ID
	*@param  priceCommitId
	*/
	public void setPriceCommitId(Long priceCommitId ){
		this.priceCommitId = priceCommitId;
	}
	/**
	* 核价单版本
	*@return
	*/
	public Integer getPriceCommitVersion(){
		return  priceCommitVersion;
	}
	/**
	* 核价单版本
	*@param  priceCommitVersion
	*/
	public void setPriceCommitVersion(Integer priceCommitVersion ){
		this.priceCommitVersion = priceCommitVersion;
	}
	/**
	* 参考申报价
	*@return
	*/
	public BigDecimal getSuggestSupplierPrice(){
		return  suggestSupplierPrice;
	}
	/**
	* 参考申报价
	*@param  suggestSupplierPrice
	*/
	public void setSuggestSupplierPrice(BigDecimal suggestSupplierPrice ){
		this.suggestSupplierPrice = suggestSupplierPrice;
	}
	/**
	* 参考申报价-币种
	*@return
	*/
	public String getSuggestSupplierPriceCurrency(){
		return  suggestSupplierPriceCurrency;
	}
	/**
	* 参考申报价-币种
	*@param  suggestSupplierPriceCurrency
	*/
	public void setSuggestSupplierPriceCurrency(String suggestSupplierPriceCurrency ){
		this.suggestSupplierPriceCurrency = suggestSupplierPriceCurrency;
	}
	/**
	* 目标申报价  也就是我司提报的供货价
	*@return
	*/
	public BigDecimal getTargetSupplierPrice(){
		return  targetSupplierPrice;
	}
	/**
	* 目标申报价  也就是我司提报的供货价
	*@param  targetSupplierPrice
	*/
	public void setTargetSupplierPrice(BigDecimal targetSupplierPrice ){
		this.targetSupplierPrice = targetSupplierPrice;
	}
	/**
	* 目标申报价-币种
	*@return
	*/
	public String getTargetSupplierPriceCurrency(){
		return  targetSupplierPriceCurrency;
	}
	/**
	* 目标申报价-币种
	*@param  targetSupplierPriceCurrency
	*/
	public void setTargetSupplierPriceCurrency(String targetSupplierPriceCurrency ){
		this.targetSupplierPriceCurrency = targetSupplierPriceCurrency;
	}
	/**
	* 如果需要议价，这个值为我司业务手工输入的“供货价”。
	*@return
	*/
	public BigDecimal getSupplierPrice(){
		return  supplierPrice;
	}
	/**
	* 如果需要议价，这个值为我司业务手工输入的“供货价”。
	*@param  supplierPrice
	*/
	public void setSupplierPrice(BigDecimal supplierPrice ){
		this.supplierPrice = supplierPrice;
	}
	/**
	* 币种
	*@return
	*/
	public String getSupplierPriceCurrency(){
		return  supplierPriceCurrency;
	}
	/**
	* 币种
	*@param  supplierPriceCurrency
	*/
	public void setSupplierPriceCurrency(String supplierPriceCurrency ){
		this.supplierPriceCurrency = supplierPriceCurrency;
	}
	/**
	* 议价原因
	*@return
	*/
	public String getBargainReason(){
		return  bargainReason;
	}
	/**
	* 议价原因
	*@param  bargainReason
	*/
	public void setBargainReason(String bargainReason ){
		this.bargainReason = bargainReason;
	}
	/**
	* 10.接受“参考供货价” 99.议价
	*@return
	*/
	public Integer getHandleType(){
		return  handleType;
	}
	/**
	* 10.接受“参考供货价” 99.议价
	*@param  handleType
	*/
	public void setHandleType(Integer handleType ){
		this.handleType = handleType;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 下载时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}

	/**
	 * 店铺ID
	 *@return
	 */
	public String getAccountId(){
		return  accountId;
	}
	/**
	 * 店铺ID
	 *@param  accountId
	 */
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
}
