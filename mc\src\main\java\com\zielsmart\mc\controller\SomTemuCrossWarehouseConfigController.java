package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTemuCrossWarehouseConfigService;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuCrossWarehouseConfigController
 * @description Temu跨境可售仓库配置管理
 * @date 2025-06-27 11:34:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuCrossWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu跨境可售仓库配置管理")
public class SomTemuCrossWarehouseConfigController extends BasicController {

    @Resource
    SomTemuCrossWarehouseConfigService somTemuCrossWarehouseConfigService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuCrossWarehouseConfigVo>> queryByPage(@RequestBody SomTemuCrossWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuCrossWarehouseConfigService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated SomTemuCrossWarehouseConfigVo somTemuCrossWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuCrossWarehouseConfigService.save(somTemuCrossWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> update(@RequestBody SomTemuCrossWarehouseConfigVo somTemuCrossWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuCrossWarehouseConfigService.update(somTemuCrossWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomTemuCrossWarehouseConfigVo somTemuCrossWarehouseConfigVo) throws ValidateException {
        somTemuCrossWarehouseConfigService.delete(somTemuCrossWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }
}
