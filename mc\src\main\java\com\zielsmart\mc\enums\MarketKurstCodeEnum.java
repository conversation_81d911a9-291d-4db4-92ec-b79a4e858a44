package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

/**
 * 市场-汇率类型编码映射
 */
@AllArgsConstructor
public enum MarketKurstCodeEnum {
    EU("EU", "M1"),
    FE("FE", "M3"),
    AU("AU", "M6"),
    OTHER("OTHER", "M2"),
    ;
    /**
     * 市场
     */
    private final String market;
    /**
     * 汇率类型编码
     */
    private final String kurstCode;

    public static String getByMarket(String market) {
        for (MarketKurstCodeEnum marketKurstCodeEnum : values()) {
            if (marketKurstCodeEnum.market.equals(market)) {
                return marketKurstCodeEnum.getKurstCode();
            }
        }
        return OTHER.kurstCode;
    }

    public String getMarket() {
        return market;
    }

    public String getKurstCode() {
        return kurstCode;
    }
}
