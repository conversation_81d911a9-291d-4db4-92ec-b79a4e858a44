package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 库区表
* gen by 代码生成器 2022-08-23
*/

@Table(name="mc.som_storage_location")
public class SomStorageLocation implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 所属仓库id
	 */
	@Column("wh_id")
	private Integer whId ;
	/**
	 * 所属仓库编码
	 */
	@Column("wh_code")
	private String whCode ;
	/**
	 * 仓库中文简称，仓库简称
	 */
	@Column("wh_name")
	private String whName ;
	/**
	 * 库区编码
	 */
	@Column("sl_code")
	private String slCode ;
	/**
	 * 库区名称
	 */
	@Column("sl_name")
	private String slName ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomStorageLocation() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 所属仓库id
	*@return
	*/
	public Integer getwhId(){
		return  whId;
	}
	/**
	* 所属仓库id
	*@param  whId
	*/
	public void setwhId(Integer whId ){
		this.whId = whId;
	}
	/**
	* 所属仓库编码
	*@return
	*/
	public String getwhCode(){
		return  whCode;
	}
	/**
	* 所属仓库编码
	*@param  whCode
	*/
	public void setwhCode(String whCode ){
		this.whCode = whCode;
	}
	/**
	* 仓库中文简称，仓库简称
	*@return
	*/
	public String getwhName(){
		return  whName;
	}
	/**
	* 仓库中文简称，仓库简称
	*@param  whName
	*/
	public void setwhName(String whName ){
		this.whName = whName;
	}
	/**
	* 库区编码
	*@return
	*/
	public String getslCode(){
		return  slCode;
	}
	/**
	* 库区编码
	*@param  slCode
	*/
	public void setslCode(String slCode ){
		this.slCode = slCode;
	}
	/**
	* 库区名称
	*@return
	*/
	public String getslName(){
		return  slName;
	}
	/**
	* 库区名称
	*@param  slName
	*/
	public void setslName(String slName ){
		this.slName = slName;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
