package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomImpactAdsReportService;
import com.zielsmart.mc.vo.SomImpactAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomImpactAdsReportVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomImpactAdsReportController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somImpactAdsReport", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "ImpactAdsReport管理")
public class SomImpactAdsReportController extends BasicController{

    @Resource
    SomImpactAdsReportService somImpactAdsReportService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomImpactAdsReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomImpactAdsReportVo>> queryByPage(@RequestBody SomImpactAdsReportPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somImpactAdsReportService.queryByPage(searchVo));
    }

    /**
     * importList
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomImpactAdsReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomImpactAdsReportPageSearchVo searchVo) throws ValidateException {
        String data = somImpactAdsReportService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
