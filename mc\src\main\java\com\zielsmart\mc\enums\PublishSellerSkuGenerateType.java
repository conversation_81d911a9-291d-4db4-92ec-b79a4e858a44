package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum PublishSellerSkuGenerateType {
    PREFIX(10, "前缀"),
    SUFFIX(20, "后缀"),
    MIDDLE(30, "中间"),

    ;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static PublishSellerSkuGenerateType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (PublishSellerSkuGenerateType value : PublishSellerSkuGenerateType.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }


    /**
     * 根据类型获取描述
     *
     * @param type 状态
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (PublishSellerSkuGenerateType value : PublishSellerSkuGenerateType.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }

    private final Integer type;
    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
