package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomBingAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomBingAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-05-29
*/

@SqlResource("somBingAdsReport")
public interface SomBingAdsReportMapper extends BaseMapper<SomBingAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomBingAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomBingAdsReportVo> queryByPage(@Param("searchVo")SomBingAdsReportPageSearchVo searchVo, PageRequest pageRequest);
}
