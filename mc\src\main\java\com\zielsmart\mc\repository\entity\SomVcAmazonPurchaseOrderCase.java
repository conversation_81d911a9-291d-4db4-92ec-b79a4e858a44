package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 短装CASE表
* gen by 代码生成器 2023-08-18
*/

@Table(name="mc.som_vc_amazon_purchase_order_case")
public class SomVcAmazonPurchaseOrderCase implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 发票表ID
	 */
	@Column("invoice_id")
	private String invoiceId ;
	/**
	 * 字典值：20.Dispute 30.Case 1 40.Case 2 
	 */
	@Column("tag")
	private Integer tag ;
	/**
	 * CASE ID
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人工号
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 反馈人工号
	 */
	@Column("feedback_num")
	private String feedbackNum ;
	/**
	 * 反馈人姓名
	 */
	@Column("feedback_name")
	private String feedbackName ;
	/**
	 * 反馈时间
	 */
	@Column("feedback_time")
	private Date feedbackTime ;

	public SomVcAmazonPurchaseOrderCase() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 发票表ID
	*@return
	*/
	public String getInvoiceId(){
		return  invoiceId;
	}
	/**
	* 发票表ID
	*@param  invoiceId
	*/
	public void setInvoiceId(String invoiceId ){
		this.invoiceId = invoiceId;
	}
	/**
	* 字典值：20.Dispute 30.Case 1 40.Case 2 
	*@return
	*/
	public Integer getTag(){
		return  tag;
	}
	/**
	* 字典值：20.Dispute 30.Case 1 40.Case 2 
	*@param  tag
	*/
	public void setTag(Integer tag ){
		this.tag = tag;
	}
	/**
	* CASE ID
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* CASE ID
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人工号
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 反馈人工号
	*@return
	*/
	public String getFeedbackNum(){
		return  feedbackNum;
	}
	/**
	* 反馈人工号
	*@param  feedbackNum
	*/
	public void setFeedbackNum(String feedbackNum ){
		this.feedbackNum = feedbackNum;
	}
	/**
	* 反馈人姓名
	*@return
	*/
	public String getFeedbackName(){
		return  feedbackName;
	}
	/**
	* 反馈人姓名
	*@param  feedbackName
	*/
	public void setFeedbackName(String feedbackName ){
		this.feedbackName = feedbackName;
	}
	/**
	* 反馈时间
	*@return
	*/
	public Date getFeedbackTime(){
		return  feedbackTime;
	}
	/**
	* 反馈时间
	*@param  feedbackTime
	*/
	public void setFeedbackTime(Date feedbackTime ){
		this.feedbackTime = feedbackTime;
	}

}
