package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomDealOfTheDay;
import com.zielsmart.mc.vo.SomDealOfTheDayExportVo;
import com.zielsmart.mc.vo.SomDealOfTheDayExtVo;
import com.zielsmart.mc.vo.SomDealOfTheDayPageSearchVo;
import com.zielsmart.mc.vo.SomDealOfTheDayVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.Date;
import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-08-09
 */

@SqlResource("somDealOfTheDay")
public interface SomDealOfTheDayMapper extends BaseMapper<SomDealOfTheDay> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomDealOfTheDayVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomDealOfTheDayExtVo> queryByPage(@Param("searchVo") SomDealOfTheDayPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * checkUnique
     * 唯一性校验
     *
     * @param addOrEditVo
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    List<Integer> checkUnique(@Param("searchVo") SomDealOfTheDayVo addOrEditVo);

    /**
     * checkRepeat
     * 检查有无未结束活动
     *
     * @param childAsin
     * @param beginDate
     * @param endDate
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @history
     */
    Integer checkRepeat(@Param("aid") String aid,@Param("site") String site,@Param("childAsin") String childAsin, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate);

    /**
     * batchSubmit
     * 批量提报
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchSubmit(@Param("updateList") List<SomDealOfTheDay> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchSubmit"), updateList);
    }

    /**
     * batchUpdate
     * 批量更新
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("updateList") List<SomDealOfTheDay> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchUpdate"), updateList);
    }

    /**
     * exportExcel
     * 导出
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomDealOfTheDayExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomDealOfTheDayExportVo> exportExcel(@Param("searchVo") SomDealOfTheDayPageSearchVo searchVo);

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchFeedbackResult(@Param("updateList")List<SomDealOfTheDay> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchFeedbackResult"), updateList);
    }

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    default void batchCancel(@Param("updateList")List<SomDealOfTheDay> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchCancel"), updateList);
    }

    /**
     * batchFeedbackCancelResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    default void batchFeedbackCancelResult(@Param("updateList")List<SomDealOfTheDay> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchFeedbackCancelResult"), updateList);
    }


    /**
     * 批量导入编辑
     *
     * @param updateSomDealOfTheDays 需要更新的数据
     */
    default void batchImportEdit(@Param("updateSomDealOfTheDays") List<SomDealOfTheDay> updateSomDealOfTheDays) {
        if (CollUtil.isEmpty(updateSomDealOfTheDays)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchImportEdit"), updateSomDealOfTheDays);
    }

    /**
     * 批量导入反馈修改结果
     *
     * @param updateSomDealOfTheDays 需要更新的数据
     */
    default void batchImportFeedbackEditResult(@Param("updateSomDealOfTheDays") List<SomDealOfTheDay> updateSomDealOfTheDays) {
        if (CollUtil.isEmpty(updateSomDealOfTheDays)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somDealOfTheDay.batchImportFeedbackEditResult"), updateSomDealOfTheDays);
    }
}
