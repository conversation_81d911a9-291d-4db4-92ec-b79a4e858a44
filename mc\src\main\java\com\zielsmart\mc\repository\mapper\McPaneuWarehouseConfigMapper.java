package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McPaneuWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McPaneuWarehouseConfigVo;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-04-20
*/

@SqlResource("mcPaneuWarehouseConfig")
public interface McPaneuWarehouseConfigMapper extends BaseMapper<McPaneuWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McPaneuWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McPaneuWarehouseConfigVo> queryByPage(@Param("searchVo")McPaneuWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<McWareHouseAndSlVo> findWarehouseCodeAndName(@Param("platforms")List<String> platforms, @Param("sites")List<String> sites);
}
