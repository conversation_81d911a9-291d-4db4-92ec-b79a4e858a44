package com.zielsmart.mc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/*
 * Temu跨境可售仓库配置的VO实体对象
 */
@Data
@Schema(title = "Temu跨境可售仓库配置", name = "SomTemuCrossWarehouseConfigVo")
public class SomTemuCrossWarehouseConfigVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "店铺名称", name = "UseableWarehouse")
    private String accountName;

    @Schema(description = "创建人工号", name = "createNum")
    private String createNum;

    @Schema(description = "创建人姓名", name = "createName")
    private String createName;

    @Schema(description = "创建时间", name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "修改人工号", name = "lastModifyNum")
    private String lastModifyNum;

    @Schema(description = "修改人姓名", name = "lastModifyName")
    private String lastModifyName;

    @Schema(description = "修改时间", name = "lastModifyTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyTime;

    @Schema(description = "可售仓库&可售库区集合", name = "list")
    private List<SomTemuCrossWarehouseConfigVo.UseableWarehouse> list;

    @Schema(description = "可售仓库名称集合", name = "warehouseNameList")
    private List<String> warehouseNameList;

    @Schema(description = "Temu平台仓库ID", name = "temuWarehouseId")
    private String temuWarehouseId;

    @Schema(description = "Temu平台仓库名称", name = "temuWarehouseName")
    private String temuWarehouseName;

    @Data
    public static class UseableWarehouse implements Serializable {

        @Schema(description = "主键", name = "aid")
        private String aid;

        @Schema(description = "可售仓库", name = "useableWarehouseCode")
        private String useableWarehouseCode;

        @Schema(description = "可售库区", name = "useableStorageCode")
        private String useableStorageCode;
    }

}
