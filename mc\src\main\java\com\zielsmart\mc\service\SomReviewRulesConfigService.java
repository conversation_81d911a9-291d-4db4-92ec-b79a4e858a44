package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomReviewRulesConfig;
import com.zielsmart.mc.repository.mapper.SomReviewRulesConfigMapper;
import com.zielsmart.mc.vo.SomReviewRulesConfigPageSearchVo;
import com.zielsmart.mc.vo.SomReviewRulesConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomReviewRulesConfigService {

    @Resource
    private SomReviewRulesConfigMapper somReviewRulesConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * save
     * 添加
     *
     * @param saveVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomReviewRulesConfigVo saveVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(saveVo) || StrUtil.isBlank(saveVo.getPlatform()) || StrUtil.isBlank(saveVo.getSite()) || ObjectUtil.isNull(saveVo.getImproveRetentionRate()) || ObjectUtil.isNull(saveVo.getEmailToRate())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somReviewRulesConfigMapper.createLambdaQuery().andEq("platform", saveVo.getPlatform()).andEq("site", saveVo.getSite()).count();
        if (count > 0) {
            throw new ValidateException("数据已存在，请检查数据");
        }
        saveVo.setAid(IdUtil.fastSimpleUUID());
        saveVo.setCreateNum(tokenUser.getJobNumber());
        saveVo.setCreateName(tokenUser.getUserName());
        saveVo.setCreateTime(DateTime.now().toJdkDate());
        somReviewRulesConfigMapper.insert(ConvertUtils.beanConvert(saveVo, SomReviewRulesConfig.class));
    }

    /**
     * query
     * 查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomReviewRulesConfigVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomReviewRulesConfigVo> query(SomReviewRulesConfigPageSearchVo searchVo) {
        List<SomReviewRulesConfigVo> list = dynamicSqlManager.getMapper(SomReviewRulesConfigMapper.class).query(searchVo);
        list.stream().forEach(f -> {
            f.setImproveRetentionRateName(f.getImproveRetentionRate() + "%");
            f.setEmailToRateName(f.getEmailToRate() + "%");
        });
        return list;
    }

    /**
     * queryByID
     * 查询详情
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomReviewRulesConfigVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomReviewRulesConfigVo queryByID(SomReviewRulesConfigPageSearchVo searchVo) throws ValidateException {
        if (ObjectUtil.isNull(searchVo) || StrUtil.isBlank(searchVo.getAid())) {
            throw new ValidateException("数据为空,请检查数据");
        }
        SomReviewRulesConfigVo vo = ConvertUtils.beanConvert(somReviewRulesConfigMapper.createLambdaQuery().andEq("aid", searchVo.getAid()).single(), SomReviewRulesConfigVo.class);
        vo.setImproveRetentionRateName(vo.getImproveRetentionRate() + "%");
        vo.setEmailToRateName(vo.getEmailToRate() + "%");
        return vo;
    }

    /**
     * update
     * 修改
     *
     * @param updateVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomReviewRulesConfigVo updateVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(updateVo) || StrUtil.isEmpty(updateVo.getAid()) || StrUtil.isBlank(updateVo.getPlatform()) || StrUtil.isBlank(updateVo.getSite()) || ObjectUtil.isNull(updateVo.getImproveRetentionRate()) || ObjectUtil.isNull(updateVo.getEmailToRate())) {
            throw new ValidateException("数据存在空值,请检查");
        }
        SomReviewRulesConfig vo = somReviewRulesConfigMapper.createLambdaQuery().andEq("aid", updateVo.getAid()).single();
        updateVo.setCreateNum(vo.getCreateNum());
        updateVo.setCreateName(vo.getCreateName());
        updateVo.setCreateTime(vo.getCreateTime());
        updateVo.setLastModifyNum(tokenUser.getJobNumber());
        updateVo.setLastModifyName(tokenUser.getUserName());
        updateVo.setLastModifyTime(DateTime.now().toJdkDate());
        somReviewRulesConfigMapper.createLambdaQuery().andEq("aid", updateVo.getAid()).updateSelective(ConvertUtils.beanConvert(updateVo, SomReviewRulesConfig.class));
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomReviewRulesConfigVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somReviewRulesConfigMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).delete();
    }
}
