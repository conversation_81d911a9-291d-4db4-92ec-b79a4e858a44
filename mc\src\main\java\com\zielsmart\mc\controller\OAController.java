package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.OAService;
import com.zielsmart.mc.vo.McWarehouseVo;
import com.zielsmart.mc.vo.OAWarehouseMultipleVo;
import com.zielsmart.mc.vo.OAWarehouseSearchVo;
import com.zielsmart.mc.vo.OAWarehouseVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title OAController
 * @description
 * @date 2021-10-29 13:58:18
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/oa-warehouse", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "OA仓库管理")
public class OAController extends BasicController {

    /**
     * oaService
     */
    @Resource
    private OAService oaService;


    /**
     * getPlatSiteMarket
     * OA接口-清仓产品新增申请
     * 获取平台站点市场
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取平台站点市场")
    @PostMapping(value = "/get-plat-site-market")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<OAWarehouseVo>> getPlatSiteMarket(){
        return ResultVo.ofSuccess(oaService.getPlatSiteMarket());
    }

    /**
     * getSku
     * OA接口-清仓产品新增申请
     * 获取到该平台站点下可选的SKU
     * @param oaWarehouseVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.OAWarehouseVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取到该平台站点下可选的SKU")
    @PostMapping(value = "/get-sku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<OAWarehouseVo> getSku(@RequestBody OAWarehouseVo oaWarehouseVo) throws ValidateException {
        return ResultVo.ofSuccess(oaService.getSku(oaWarehouseVo));
    }

    /**
     * getGroup
     * OA接口-清仓产品新增申请
     * 获取业务组
     * @param oaWarehouseVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.OAWarehouseVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取业务组")
    @PostMapping(value = "/get-group")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<OAWarehouseVo> getGroup(@RequestBody OAWarehouseVo oaWarehouseVo) throws ValidateException {
        return ResultVo.ofSuccess(oaService.getGroup(oaWarehouseVo));
    }

    /**
     * saveClearProduct
     * OA接口-清仓产品新增申请
     * 保存清仓产品
     * @param oaWarehouseVo
     * @param tokenUserInfo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "保存清仓产品")
    @PostMapping(value = "/save-clear-product")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveClearProduct(@RequestBody OAWarehouseVo oaWarehouseVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) throws ValidateException {
        oaService.saveClearProduct(oaWarehouseVo,tokenUserInfo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * getAddableInfos
     * OA接口-可售仓库配置申请
     * 获取可新增信息(平台、站点与市场)
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可新增信息")
    @PostMapping(value = "/getAddableInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<OAWarehouseVo>> getAddableInfos() {
        return ResultVo.ofSuccess(oaService.getAddableInfos());
    }

    /**
     * getPlatSiteMarket
     * OA接口-可售仓库配置申请
     * 根据市场获取可配置仓库
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可配置仓库")
    @PostMapping(value = "/getAddableWarehouses")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWarehouseVo>> getAddableWarehouses(@RequestBody OAWarehouseSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(oaService.getAddableWarehouses(searchVo));
    }

    /**
     * saveAddedWarehouses
     * OA接口-可售仓库配置申请
     * 保存新增可售仓库
     * @param oaWarehouseVo
     * @param tokenUserInfo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "保存新增可售仓库")
    @PostMapping(value = "/saveAddedWarehouses")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveAddedWarehouses(@RequestBody OAWarehouseVo oaWarehouseVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) throws ValidateException {
        String str = oaService.saveAddedWarehouses(oaWarehouseVo, tokenUserInfo);
        if(str.isEmpty()){
            return ResultVo.ofSuccess(null);
        }else {
            return  ResultVo.ofFail(str);
        }
    }

    /**
     * getAddableInfos
     * OA接口-可售仓库变更申请
     * 获取可变更信息(平台、站点与市场)
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可变更信息")
    @PostMapping(value = "/getChangeableInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<OAWarehouseVo>> getChangeableInfos() {
        return ResultVo.ofSuccess(oaService.getChangeableInfos());
    }

    /**
     * getPlatSiteMarket
     * OA接口-可售仓库变更申请
     * 根据获取可变更仓库、市场获取可变更仓库
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.OAWarehouseVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可变更仓库")
    @PostMapping(value = "/getConfiguredWarehouses")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<OAWarehouseVo> getConfiguredWarehouses(@RequestBody OAWarehouseSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(oaService.getConfiguredWarehouses(searchVo));
    }

    /**
     * saveAddedWarehouses
     * OA接口-可售仓库配置变更
     * 保存新增可售仓库
     * @param oaWarehouseVo
     * @param tokenUserInfo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "保存变更可售仓库")
    @PostMapping(value = "/saveChangedWarehouses")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveChangedWarehouses(@RequestBody OAWarehouseVo oaWarehouseVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) throws ValidateException {
        String str = oaService.saveAddedWarehouses(oaWarehouseVo, tokenUserInfo);
        if(str.isEmpty()){
            return ResultVo.ofSuccess(null);
        }else {
            return  ResultVo.ofFail(str);
        }
    }

    /**
     * getWarehouseAreaInfos
     * 获取Walmart平台下所有的仓库区域信息
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseMultipleVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取仓库区域信息")
    @PostMapping(value = "/getWarehouseAreaInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<OAWarehouseMultipleVo>> getWarehouseAreaInfos() {
        return ResultVo.ofSuccess(oaService.getWarehouseAreaInfos());
    }
}
