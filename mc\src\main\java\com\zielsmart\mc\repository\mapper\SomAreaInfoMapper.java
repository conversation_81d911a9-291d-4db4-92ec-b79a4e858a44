package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAreaInfoPageSearchVo;
import com.zielsmart.mc.vo.SomAreaInfoVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2025-07-29
*/

@SqlResource("somAreaInfo")
public interface SomAreaInfoMapper extends BaseMapper<SomAreaInfo> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAreaInfoVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAreaInfoVo> queryByPage(@Param("searchVo")SomAreaInfoPageSearchVo searchVo, PageRequest pageRequest);
}
