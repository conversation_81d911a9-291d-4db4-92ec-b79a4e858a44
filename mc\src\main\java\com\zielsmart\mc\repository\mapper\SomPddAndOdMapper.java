package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomPddAndOd;
import com.zielsmart.mc.vo.*;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-08-03
 */

@SqlResource("somPddAndOd")
public interface SomPddAndOdMapper extends BaseMapper<SomPddAndOd> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult< SomPddAndOdVo >}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPddAndOdExtVo> queryByPage(@Param("searchVo") SomPddAndOdPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * checkUnique
     * 唯一性校验
     *
     * @param addVo
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    List<Integer> checkUnique(@Param("searchVo") SomPddAndOdVo addVo);
    List<Integer> checkUniqueWithSellerSku(@Param("searchVo") SomPddAndOdVo addVo);

    /**
     * batchSubmit
     * 批量提报
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchSubmit(@Param("updateList") List<SomPddAndOd> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchSubmit"), updateList);
    }

    /**
     * batchUpdate
     * 批量更新
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("updateList") List<SomPddAndOd> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchUpdate"), updateList);
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void batchFeedbackResult(@Param("updateList")List<SomPddAndOd> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchFeedbackResult"), updateList);
    }

    /**
     * exportExcel
     * 导出
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomPddAndExportVo>}
     * <AUTHOR>
     * @history
     */
    List<SomPddAndExportVo> exportExcel(@Param("searchVo") SomPddAndOdPageSearchVo searchVo);

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    default void batchCancel(@Param("updateList")List<SomPddAndOd> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchCancel"), updateList);
    }

    /**
     * batchFeedbackCancelResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    default void batchFeedbackCancelResult(@Param("updateList")List<SomPddAndOd> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchFeedbackCancelResult"), updateList);
    }

    /**
     * 批量导入编辑
     *
     * @param updateSomPddAndOds 需要更新的数据
     */
    default void batchImportEdit(@Param("updateSomPddAndOds") List<SomPddAndOd> updateSomPddAndOds) {
        if (CollUtil.isEmpty(updateSomPddAndOds)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchImportEdit"), updateSomPddAndOds);
    }

    /**
     * 批量导入反馈修改结果
     *
     * @param updateSomPddAndOds 需要更新的数据
     */
    default void batchImportFeedbackEditResult(@Param("updateSomPddAndOds") List<SomPddAndOd> updateSomPddAndOds) {
        if (CollUtil.isEmpty(updateSomPddAndOds)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somPddAndOd.batchImportFeedbackEditResult"), updateSomPddAndOds);
    }

    /**
     * getPdd
     * 生成大促文件所需数据
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.SomPddAndOd>}
     * <AUTHOR>
     * @history
     */
    List<SomPddAndOdGetPddVo> getPdd(@Param("searchVo") SomPddAndOdPageSearchVo searchVo);
}
