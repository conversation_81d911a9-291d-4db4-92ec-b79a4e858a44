package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McPlatformSiteResources;
import com.zielsmart.mc.vo.role.McPlatformSiteResourcesVo;
import com.zielsmart.mc.vo.role.PlatformSiteResourcesPageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2021-07-14
*/

@SqlResource("mcPlatformSiteResources")
public interface McPlatformSiteResourcesMapper extends BaseMapper<McPlatformSiteResources> {

    /**
     * queryByPage
     * 运营平台站点配置表分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult< McPlatformSiteResourcesVo >}
     * <AUTHOR>
     * @history
     */
    PageResult<McPlatformSiteResourcesVo> queryByPage(@Param("search")PlatformSiteResourcesPageSearchVo searchVo, PageRequest pageRequest);

}
