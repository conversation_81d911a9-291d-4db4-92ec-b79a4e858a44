package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomSupplySourceStoreService;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStorePageSearchVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomSupplySourceStoreController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somSupplySourceStore", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "提货仓管理")
public class SomSupplySourceStoreController extends BasicController {

    @Resource
    SomSupplySourceStoreService somSupplySourceStoreService;

    /**
     * add
     * 新增
     *
     * @param addVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增")
    @PostMapping(value = "/add")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody @Validated SomSupplySourceStoreExtVo addVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSupplySourceStoreService.add(addVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomSupplySourceStoreVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomSupplySourceStoreExtVo>> query(@RequestBody SomSupplySourceStorePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somSupplySourceStoreService.query(searchVo));
    }

    /**
     * queryByAid
     * 查看
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomSupplySourceStoreVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomSupplySourceStoreConfigExtVo> queryByAid(@RequestBody SomSupplySourceStoreVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somSupplySourceStoreService.queryByAid(searchVo));
    }

    /**
     * edit
     * 编辑
     *
     * @param editVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomSupplySourceStoreConfigExtVo editVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSupplySourceStoreService.edit(editVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * inactive
     * 禁用
     *
     * @param inactiveVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "禁用")
    @PostMapping(value = "/inactive")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> inactive(@RequestBody SomSupplySourceStoreVo inactiveVo) throws ValidateException {
        somSupplySourceStoreService.inactive(inactiveVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * active
     * 启用
     *
     * @param activeVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "启用")
    @PostMapping(value = "/active")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> active(@RequestBody SomSupplySourceStoreVo activeVo) throws ValidateException {
        somSupplySourceStoreService.active(activeVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * disable
     * 归档
     *
     * @param disableVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "归档")
    @PostMapping(value = "/disable")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> disable(@RequestBody SomSupplySourceStoreVo disableVo) throws ValidateException {
        somSupplySourceStoreService.disable(disableVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * pushSupplySources
     * 推送提货仓资料
     *
     * @param pushVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "推送提货仓资料")
    @PostMapping(value = "/pushSupplySources")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> pushSupplySources(@RequestBody SomSupplySourceStoreVo pushVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSupplySourceStoreService.pushSupplySources(pushVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryAllInfos
     * 获取所有的提货仓
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomSupplySourceStoreVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取所有的提货仓")
    @PostMapping(value = "/queryAllInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomSupplySourceStoreVo>> queryAllInfos() {
        return ResultVo.ofSuccess(somSupplySourceStoreService.queryAllInfos());
    }
}
