package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomConforamaAutoPushStockRecord;
import com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-09
*/

@SqlResource("somConforamaAutoPushStockRecord")
public interface SomConforamaAutoPushStockRecordMapper extends BaseMapper<SomConforamaAutoPushStockRecord> {
    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomConforamaAutoPushStockRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomConforamaAutoPushStockRecordVo> queryByPage(@Param("searchVo") SomConforamaAutoPushStockRecordPageSearchVo searchVo, PageRequest pageRequest);

    List<SomConforamaAutoPushStockRecordVo> exportExcel(@Param("searchVo")SomConforamaAutoPushStockRecordPageSearchVo searchVo);
}
