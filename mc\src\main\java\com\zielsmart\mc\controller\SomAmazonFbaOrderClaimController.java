package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.csv.CsvImportUtil;
import cn.afterturn.easypoi.csv.entity.CsvImportParams;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IReadHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zielsmart.mc.repository.entity.SomAmazonFbaOrderClaim;
import com.zielsmart.mc.repository.mapper.SomAmazonFbaOrderClaimMapper;
import com.zielsmart.mc.service.SomAmazonFbaOrderClaimService;
import com.zielsmart.mc.vo.SomAmazonFbaClaimVo;
import com.zielsmart.mc.vo.SomAmazonFbaOrderClaimPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonFbaOrderClaimVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonFbaOrderClaimController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAmazonFbaOrderClaim", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon订单索赔管理")
public class SomAmazonFbaOrderClaimController extends BasicController{

    @Resource
    SomAmazonFbaOrderClaimService somAmazonFbaOrderClaimService;
    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonFbaOrderClaimVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonFbaOrderClaimVo>> queryByPage(@RequestBody SomAmazonFbaOrderClaimPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonFbaOrderClaimService.queryByPage(searchVo));
    }

    /**
     * update
     *
     * @param somAmazonFbaOrderClaimVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈索赔结果")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomAmazonFbaOrderClaimVo.SomAmazonFbaOrderClaimFeedback claimVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonFbaOrderClaimService.update(claimVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "索赔")
    @PostMapping(value = "/claim")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> claim(@RequestBody SomAmazonFbaOrderClaimVo.SomAmazonFbaOrderClaimFeedback claimVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonFbaOrderClaimService.claim(claimVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "根据caseId加载所有数据")
    @PostMapping(value = "/caseData-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomAmazonFbaOrderClaimVo>> loadCaseData(@RequestBody SomAmazonFbaOrderClaimVo claimVo) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonFbaOrderClaimService.loadCaseData(claimVo));
    }

    /**
     * delete
     *
     * @param somAmazonFbaOrderClaimVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomAmazonFbaOrderClaimVo somAmazonFbaOrderClaimVo) throws ValidateException {
        somAmazonFbaOrderClaimService.delete(somAmazonFbaOrderClaimVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomAmazonFbaOrderClaimPageSearchVo searchVo){
        String data = somAmazonFbaOrderClaimService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("files") MultipartFile[] files, @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate, @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somAmazonFbaOrderClaimService.importExcel(files,beginDate,endDate, tokenUser);
        return ResultVo.ofSuccess();
    }


}
