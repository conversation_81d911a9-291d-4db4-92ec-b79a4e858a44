package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomTemuListingService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuListingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTemuListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "拼多多平台Listing管理")
public class SomTemuListingController extends BasicController {

    @Resource
    SomTemuListingService somTemuListingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTemuListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuListingVo>> queryByPage(@RequestBody SomTemuListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuListingService.queryByPage(searchVo));
    }


    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomTemuListingPageSearchVo searchVo) {
        String data = somTemuListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "TEMU图片URL下载")
    @PostMapping(value = "/export-temu-image-url", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportTemuImageUrl(@RequestBody SomTemuListingPageSearchVo searchVo) throws ValidateException {
        String data = somTemuListingService.exportTemuImageUrl(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "库存报表")
    @PostMapping(value = "/stock-report")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuListingReport>> stockReport(@RequestBody SomTemuListingPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somTemuListingService.stockReport(searchVo));
    }
}
