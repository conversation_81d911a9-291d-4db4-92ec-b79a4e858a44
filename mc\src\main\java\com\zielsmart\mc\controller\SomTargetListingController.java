package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTargetListingService;
import com.zielsmart.mc.vo.SomTargetListingPageSearchVo;
import com.zielsmart.mc.vo.SomTargetListingVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTargetListingController
 * @description
 * @date 2023-12-05 15:35:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTargetListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Target Listing管理")
public class SomTargetListingController extends BasicController {

    @Resource
    SomTargetListingService somTargetListingService;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTargetListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTargetListingVo>> queryByPage(@RequestBody SomTargetListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTargetListingService.queryByPage(searchVo));
    }

}
