package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomGoogleAdsReport;
import com.zielsmart.mc.vo.SomGoogleAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomGoogleAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Map;
/*
* 
* gen by 代码生成器 mapper 2023-04-27
*/

@SqlResource("somGoogleAdsReport")
public interface SomGoogleAdsReportMapper extends BaseMapper<SomGoogleAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomGoogleAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomGoogleAdsReportVo> queryByPage(@Param("searchVo")SomGoogleAdsReportPageSearchVo searchVo, PageRequest pageRequest);

    List<Map<String,Object>> getSite();
}
