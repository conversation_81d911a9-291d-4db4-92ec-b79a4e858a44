package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuLocalPromotionDetail;
import com.zielsmart.mc.vo.SomTemuLocalPromotionDetailPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalPromotionDetailVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Set;

/*
 *
 * gen by 代码生成器 mapper 2025-03-12
 */
@SqlResource("somTemuLocalPromotionDetail")
public interface SomTemuLocalPromotionDetailMapper extends BaseMapper<SomTemuLocalPromotionDetail> {

    /**
     * detailQueryByPage
     * 分页查询活动明细
     *
     * @param searchVo    入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuLocalPromotionDetailVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuLocalPromotionDetailVo> queryByPage(SomTemuLocalPromotionDetailPageSearchVo searchVo, PageRequest pageRequest);

    List<SomTemuLocalPromotionDetailVo> promotionSelect(@Param("accountIds")Set<String> accountIds);
}
