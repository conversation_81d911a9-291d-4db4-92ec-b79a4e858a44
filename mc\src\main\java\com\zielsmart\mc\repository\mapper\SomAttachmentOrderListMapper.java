package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAttachmentOrderListPageSearchVo;
import com.zielsmart.mc.vo.SomAttachmentOrderListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2022-04-11
*/

@SqlResource("somAttachmentOrderList")
public interface SomAttachmentOrderListMapper extends BaseMapper<SomAttachmentOrderList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAttachmentOrderListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAttachmentOrderListVo> queryByPage(@Param("searchVo")SomAttachmentOrderListPageSearchVo searchVo, PageRequest pageRequest);
}
