package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomWootPromotionWarehouseConfigService;
import com.zielsmart.mc.vo.SomWootPromotionWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWootPromotionWarehouseConfigVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWootPromotionWarehouseConfigController
 * @description
 * @date 2024-06-03 11:56:35
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somWootPromotionWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "woot促销活动仓库配置管理")
public class SomWootPromotionWarehouseConfigController extends BasicController {

    @Resource
    SomWootPromotionWarehouseConfigService somWootPromotionWarehouseConfigService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomWootPromotionWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWootPromotionWarehouseConfigVo>> queryByPage(@RequestBody SomWootPromotionWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWootPromotionWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     * 添加
     *
     * @param somWootPromotionWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionWarehouseConfigService.save(somWootPromotionWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     * 修改
     *
     * @param somWootPromotionWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWootPromotionWarehouseConfigService.update(somWootPromotionWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param somWootPromotionWarehouseConfigVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWootPromotionWarehouseConfigVo somWootPromotionWarehouseConfigVo) throws ValidateException {
        somWootPromotionWarehouseConfigService.delete(somWootPromotionWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

}
