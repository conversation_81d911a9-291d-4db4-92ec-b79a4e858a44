package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.RoleResourceService;
import com.zielsmart.mc.vo.role.McRoleResourcePageSearchVo;
import com.zielsmart.mc.vo.role.McRoleResourceVo;
import com.zielsmart.mc.vo.role.PlatformAndSiteVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;
import java.util.List;


/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McRoleResourceController
 * @description
 * @date 2021-07-19 14:07:19
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/role-resource", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "角色关联平台站点")
public class McRoleResourceController extends BasicController {
    /**
     * roleResourceService
     */
    @Resource
    private RoleResourceService roleResourceService;


    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link ResultVo< PageVo< McRoleResourceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McRoleResourceVo>> queryByPage(@RequestBody McRoleResourcePageSearchVo searchVo) {
        return ResultVo.ofSuccess(roleResourceService.queryByPage(searchVo));
    }

    /**
     * getPermissionList
     * 获取权限列表  如果用户没有被分配权限则返回默认权限
     * @param tokenUserInfo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List< McRoleResourceVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取权限列表")
    @PostMapping(value = "/get-all-platform-and-site")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<PlatformAndSiteVo>> getPermissionList(@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) {
        return ResultVo.ofSuccess(roleResourceService.getPermissionList(tokenUserInfo));
    }


    /**
     * saveOrUpdate
     * 保存修改
     * @param mcRoleResourceVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link RuntimeException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "保存修改")
    @PostMapping(value = "/save-or-update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveOrUpdate(@RequestBody @Validated McRoleResourceVo mcRoleResourceVo) throws RuntimeException {
        roleResourceService.saveOrUpdate(mcRoleResourceVo);
        return ResultVo.ofSuccess(null);
    }


    /**
     * delete
     * 删除
     * @param aidList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody List<String> aidList) throws ValidationException {
        roleResourceService.delete(aidList);
        return ResultVo.ofSuccess(null);
    }

}
