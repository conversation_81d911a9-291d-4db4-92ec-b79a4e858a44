package com.zielsmart.mc.vo;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.beetl.sql.annotation.entity.Column;

import java.io.Serializable;

/*
* 拼多多平台Listing的VO实体对象
* gen by 代码生成器 2024-04-03
*/

@Data
@Schema(title = "拼多多平台Listing",name = "SomTemuListingVo")
public class SomTemuListingVo implements java.io.Serializable {

	@Schema(description = "站点名称集合",name="siteNames")
	private String siteNames;

	@Schema(description = "非本本skc加站点状态",name="skcSiteStatus")
	private Integer skcSiteStatus;

	@Schema(description = "本本商品状态",name="status")
	private Integer status;

	@Schema(description = "本本商品子状态",name="subStatus")
	private Integer subStatus;

	//类目ID
	@Schema(description = "类目ID",name="catId")
	private Integer catId;

	//类目名称
	@Schema(description = "类目名称",name="catName")
	private String catName;

	@Schema(description = "颜色",name="colorName")
	private String colorName;
	
	


	@Schema(description = "售价",name="price")
	private BigDecimal price ;

	@Schema(description = "供货价",name="supplierPrice")
	private BigDecimal supplierPrice ;

	@Schema(description = "产品类型 1正常/非在售  4.未完成发布  null是香港主体的数据",name="goodsType")
	private Integer goodsType ;

	@Schema(description = "币种",name="currency")
	private String currency ;

	/**
	 * 主键
	 */
    @Schema(description = "主键",name="aid")
	private String aid ;

	/**
	 * 站点
	 */
    @Schema(description = "站点",name="site")
	private String site ;
	/**
	 * 展示码
	 */
    @Schema(description = "展示码",name="sellerSku")
	private String sellerSku ;
	/**
	 * 库存数量
	 */
    @Schema(description = "库存数量",name="virtualStock")
	private Integer virtualStock ;
	/**
	 * 产品ID
	 */
    @Schema(description = "产品ID",name="productId")
	private String productId ;
	/**
	 * 平台SKU唯一编码
	 */
    @Schema(description = "平台SKU唯一编码",name="productSkuId")
	private String productSkuId ;
	/**
	 * Skc id
	 */
    @Schema(description = "Skc id",name="productSkcId")
	private String productSkcId ;
	/**
	 * 产品名称
	 */
    @Schema(description = "产品名称",name="productName")
	private String productName ;
	/**
	 * 主图地址
	 */
    @Schema(description = "主图地址",name="mainImageUrl")
	private String mainImageUrl ;
	/**
	 * 下载时间
	 */
    @Schema(description = "下载时间",name="downloadTime")
	private Date downloadTime ;

	private String accountTag ;

	private String accountId ;

}
