package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomOffersInfoService;
import com.zielsmart.mc.vo.SomOfferPageSearchVo;
import com.zielsmart.mc.vo.SomOffersInfoExtVo;
import com.zielsmart.mc.vo.SomOffersInfoPageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomOffersInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somOffersInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "跟卖者分析管理")
public class SomOffersInfoController extends BasicController {

    @Resource
    SomOffersInfoService somOffersInfoService;

    /**
     * queryByPage
     * 跟卖者分析
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomOffersInfoExtVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "跟卖者分析")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomOffersInfoExtVo>> queryByPage(@RequestBody SomOffersInfoPageSearchVo pageSearchVo) {
        return ResultVo.ofSuccess(somOffersInfoService.queryByPage(pageSearchVo));
    }

    /**
     * export
     * 导出
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomOffersInfoPageSearchVo searchVo) throws ValidateException {
        String data = somOffersInfoService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
