package com.zielsmart.mc.config;

import com.zielsmart.mc.util.ExceptionHandlerProxy;
import com.zielsmart.web.basic.GlobalExceptionAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

@Component
public class CustomizePostProcessor implements BeanPostProcessor {
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof GlobalExceptionAdvice) {
            //代理GlobalExceptionAdvice  打印详细异常信息
            return ExceptionHandlerProxy.createProxy(bean);
        }
        return bean;
    }
}
