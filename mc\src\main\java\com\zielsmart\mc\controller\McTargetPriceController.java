package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McTargetPriceService;
import com.zielsmart.mc.vo.McTargetPriceEXVo;
import com.zielsmart.mc.vo.McTargetPricePageSearchVo;
import com.zielsmart.mc.vo.McTargetPriceVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McTargetPriceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcTargetPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "目标价管理")
public class McTargetPriceController extends BasicController {

    @Resource
    McTargetPriceService mcTargetPriceService;

    /**
     * importExcel
     * 导入数据
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台", "站点", "展示码", "目标价", "预估毛利率(%)"};
        importParams.setImportFields(arr);
        ExcelImportResult<McTargetPriceVo> result = null;
        try{
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), McTargetPriceVo.class, importParams);
        }catch (Exception e){
                throw new ValidateException("导入模板有误,请检查模板");
        }
        List<McTargetPriceVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        String str = mcTargetPriceService.importExcel(list, tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McTargetPriceVo>>}
     * <AUTHOR> @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McTargetPriceEXVo>> queryByPage(@RequestBody McTargetPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcTargetPriceService.queryByPage(searchVo));
    }

    /**
     * export
     * 导出数据
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出数据")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody McTargetPricePageSearchVo searchVo) {
        String data = mcTargetPriceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * save
     * 维护目标价
     *
     * @param mcTargetPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "维护目标价")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McTargetPriceVo mcTargetPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcTargetPriceService.save(mcTargetPriceVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * audit
     * 审核/批量审核
     *
     * @param exVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "审核")
    @PostMapping(value = "/audit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> audit(@RequestBody McTargetPriceEXVo exVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcTargetPriceService.audit(exVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "批量编辑")
    @PostMapping(value = "/edit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台", "站点", "展示码", "目标价", "预估毛利率(%)"};
        importParams.setImportFields(arr);
        ExcelImportResult<McTargetPriceVo> result = null;
        try{
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), McTargetPriceVo.class, importParams);
        }catch (Exception e){
            if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        List<McTargetPriceVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        String str = mcTargetPriceService.edit(list, tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * downloadExcel
     * 下载导入模板
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/TargetPriceTemplate.xlsx";
    }
}
