package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McWarehouseConfigMultiple;
import com.zielsmart.mc.vo.McWarehouseConfigMultiplePageSearchVo;
import com.zielsmart.mc.vo.McWarehouseConfigMultipleVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2021-10-09
*/

@SqlResource("mcWarehouseConfigMultiple")
public interface McWarehouseConfigMultipleMapper extends BaseMapper<McWarehouseConfigMultiple> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McWarehouseConfigMultipleVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McWarehouseConfigMultipleVo> queryByPage(@Param("searchVo")McWarehouseConfigMultiplePageSearchVo searchVo, PageRequest pageRequest);

    List<McWarehouseConfigMultipleVo> findWarehouseMultipleCodeAndName(@Param("warehouse")McWarehouseConfigMultipleVo warehouse);
}
