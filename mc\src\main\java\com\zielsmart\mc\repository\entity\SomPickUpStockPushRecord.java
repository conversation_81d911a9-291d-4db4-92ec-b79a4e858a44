package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 提货仓库推送报表
* gen by 代码生成器 2022-09-22
*/

@Table(name="mc.som_pick_up_stock_push_record")
public class SomPickUpStockPushRecord implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 提货仓唯一ID
	 */
	@Column("supply_source_id")
	private String supplySourceId ;
	/**
	 * 产品编码
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 库存
	 */
	@Column("total_stock")
	private Integer totalStock ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 批次
	 */
	@Column("version")
	private String version ;
	/**
	 * 推送时间
	 */
	@Column("push_time")
	private Date pushTime ;
	/**
	 * 推送状态 10.未推送 20.推送成功 99.推送失败
	 */
	@Column("push_status")
	private Integer pushStatus ;
	/**
	 * 接口返回的错误消息
	 */
	@Column("error_msg")
	private String errorMsg ;

	public SomPickUpStockPushRecord() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 提货仓唯一ID
	*@return
	*/
	public String getSupplySourceId(){
		return  supplySourceId;
	}
	/**
	* 提货仓唯一ID
	*@param  supplySourceId
	*/
	public void setSupplySourceId(String supplySourceId ){
		this.supplySourceId = supplySourceId;
	}
	/**
	* 产品编码
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* 产品编码
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 库存
	*@return
	*/
	public Integer getTotalStock(){
		return  totalStock;
	}
	/**
	* 库存
	*@param  totalStock
	*/
	public void setTotalStock(Integer totalStock ){
		this.totalStock = totalStock;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 批次
	*@return
	*/
	public String getVersion(){
		return  version;
	}
	/**
	* 批次
	*@param  version
	*/
	public void setVersion(String version ){
		this.version = version;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getPushTime(){
		return  pushTime;
	}
	/**
	* 推送时间
	*@param  pushTime
	*/
	public void setPushTime(Date pushTime ){
		this.pushTime = pushTime;
	}
	/**
	* 推送状态 10.未推送 20.推送成功 99.推送失败
	*@return
	*/
	public Integer getPushStatus(){
		return  pushStatus;
	}
	/**
	* 推送状态 10.未推送 20.推送成功 99.推送失败
	*@param  pushStatus
	*/
	public void setPushStatus(Integer pushStatus ){
		this.pushStatus = pushStatus;
	}
	/**
	* 接口返回的错误消息
	*@return
	*/
	public String getErrorMsg(){
		return  errorMsg;
	}
	/**
	* 接口返回的错误消息
	*@param  errorMsg
	*/
	public void setErrorMsg(String errorMsg ){
		this.errorMsg = errorMsg;
	}

}
