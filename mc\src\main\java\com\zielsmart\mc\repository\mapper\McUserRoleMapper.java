package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.role.McUserRoleVo;
import com.zielsmart.mc.vo.user.McUserVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2021-07-16
*/

@SqlResource("mcUserRole")
public interface McUserRoleMapper extends BaseMapper<McUserRole> {

}
