package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonVcComparativePriceRecordService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonVcComparativePriceRecordController
 * @description
 * @date 2025-01-21 18:09:07
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonVcComparativePriceRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC比价记录表管理")
public class SomAmazonVcComparativePriceRecordController extends BasicController{

    @Resource
    SomAmazonVcComparativePriceRecordService somAmazonVcComparativePriceRecordService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomAmazonVcComparativePriceRecordVo>> queryByPage(@RequestBody SomAmazonVcComparativePriceRecordPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonVcComparativePriceRecordService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonVcComparativePriceRecordPageSearchVo searchVo) throws ValidateException {
        String data = somAmazonVcComparativePriceRecordService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "处理比价记录")
    @PostMapping(value = "/handle")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> handle(@RequestBody SomAmazonVcComparativePriceRecordHandleVo comparativePriceRecordHandleVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonVcComparativePriceRecordService.handle(comparativePriceRecordHandleVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "查看处理日志")
    @PostMapping(value = "/handleRecord/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomAmazonVcComparativePriceRecordHandleRecordVo>> queryHandleRecord(@RequestBody SomAmazonVcComparativePriceRecordPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonVcComparativePriceRecordService.queryHandleRecord(searchVo));
    }

    @Operation(summary = "下载批量处理导入模板")
    @GetMapping(value="/batchHandleTemplate/download")
    public String batchHandleTemplateDownload(){
        return "forward:/static/excel/SomAmazonVcComparativePriceRecordHandleTemplate.xlsx";
    }

    @Operation(summary = "导入批量处理")
    @PostMapping(value = "/batchHandle/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchHandleImport(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "处理状态", "比价原因", "比价平台", "比价价格", "比价链接", "备注"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomAmazonVcComparativePriceRecordHandleImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonVcComparativePriceRecordHandleImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonVcComparativePriceRecordService.batchHandleImport(result.getList(), tokenUser);
        return ResultVo.ofSuccess();

    }
}
