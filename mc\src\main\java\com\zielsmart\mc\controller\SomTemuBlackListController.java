package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import com.zielsmart.mc.service.SomTemuBlackListService;
import com.zielsmart.mc.vo.SomDealRuleWhiteListVo;
import com.zielsmart.mc.vo.SomTemuBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuBlackListVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuBlackListController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuBlackList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu库存推送黑名单管理")
public class SomTemuBlackListController extends BasicController{

    @Resource
    SomTemuBlackListService somTemuBlackListService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTemuBlackListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuBlackListVo>> queryByPage(@RequestBody SomTemuBlackListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuBlackListService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somTemuBlackListVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomTemuBlackListVo somTemuBlackListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuBlackListService.save(somTemuBlackListVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }


    /**
     * delete
     *
     * @param somTemuBlackListVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomTemuBlackListVo somTemuBlackListVo) throws ValidateException {
        somTemuBlackListService.delete(somTemuBlackListVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomTemuBlackListPageSearchVo searchVo){
        String data = somTemuBlackListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/TemuBlackTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"站点","店铺ID","SKU ID","展示码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomTemuBlackListVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomTemuBlackListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somTemuBlackListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "批量删除")
    @PostMapping(value = "/batch-delete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDelete(@RequestParam("file") MultipartFile file) throws Exception {
        String[] importFields = {"站点","店铺ID","SKU ID","展示码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomTemuBlackListVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomTemuBlackListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somTemuBlackListService.batchDelete(list);
        return ResultVo.ofSuccess();
    }
}
