package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* Temu供货价管理
* gen by 代码生成器 2024-09-05
*/

@Table(name="mc.som_temu_supplier_price")
public class SomTemuSupplierPrice implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 产品ID
	 */
	@Column("product_id")
	private String productId ;
	/**
	 * SKU ID
	 */
	@Column("product_sku_id")
	private String productSkuId ;
	/**
	 * 成本价
	 */
	@Column("cost")
	private BigDecimal cost ;
	/**
	 * 成本价币种
	 */
	@Column("cost_currency")
	private String costCurrency ;
	/**
	 * 预期毛利率
	 */
	@Column("estimate_gross_margin")
	private BigDecimal estimateGrossMargin ;
	/**
	 * 预期供货价
	 */
	@Column("estimate_supplier_price")
	private BigDecimal estimateSupplierPrice ;
	/**
	 * 最低供货价
	 */
	@Column("minimum_supplier_price")
	private BigDecimal minimumSupplierPrice ;

	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * SKC ID
	 */
	@Column("product_skc_id")
	private String productSkcId ;

	public SomTemuSupplierPrice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 产品ID
	*@return
	*/
	public String getProductId(){
		return  productId;
	}
	/**
	* 产品ID
	*@param  productId
	*/
	public void setProductId(String productId ){
		this.productId = productId;
	}
	/**
	* SKU ID
	*@return
	*/
	public String getProductSkuId(){
		return  productSkuId;
	}
	/**
	* SKU ID
	*@param  productSkuId
	*/
	public void setProductSkuId(String productSkuId ){
		this.productSkuId = productSkuId;
	}
	/**
	* 成本价
	*@return
	*/
	public BigDecimal getCost(){
		return  cost;
	}
	/**
	* 成本价
	*@param  cost
	*/
	public void setCost(BigDecimal cost ){
		this.cost = cost;
	}
	/**
	* 成本价币种
	*@return
	*/
	public String getCostCurrency(){
		return  costCurrency;
	}
	/**
	* 成本价币种
	*@param  costCurrency
	*/
	public void setCostCurrency(String costCurrency ){
		this.costCurrency = costCurrency;
	}
	/**
	* 预期毛利率
	*@return
	*/
	public BigDecimal getEstimateGrossMargin(){
		return  estimateGrossMargin;
	}
	/**
	* 预期毛利率
	*@param  estimateGrossMargin
	*/
	public void setEstimateGrossMargin(BigDecimal estimateGrossMargin ){
		this.estimateGrossMargin = estimateGrossMargin;
	}
	/**
	* 预期供货价
	*@return
	*/
	public BigDecimal getEstimateSupplierPrice(){
		return  estimateSupplierPrice;
	}
	/**
	* 预期供货价
	*@param  estimateSupplierPrice
	*/
	public void setEstimateSupplierPrice(BigDecimal estimateSupplierPrice ){
		this.estimateSupplierPrice = estimateSupplierPrice;
	}
	/**
	* 最低供货价
	*@return
	*/
	public BigDecimal getMinimumSupplierPrice(){
		return  minimumSupplierPrice;
	}
	/**
	* 最低供货价
	*@param  minimumSupplierPrice
	*/
	public void setMinimumSupplierPrice(BigDecimal minimumSupplierPrice ){
		this.minimumSupplierPrice = minimumSupplierPrice;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* SKC ID
	*@return
	*/
	public String getProductSkcId(){
		return  productSkcId;
	}
	/**
	* SKC ID
	*@param  productSkcId
	*/
	public void setProductSkcId(String productSkcId ){
		this.productSkcId = productSkcId;
	}

}
