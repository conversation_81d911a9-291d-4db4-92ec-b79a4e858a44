package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 三方平台价格推送表
* gen by 代码生成器 2024-11-13
*/

@Table(name="mc.som_three_platform_price")
public class SomThreePlatformPrice implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 三级分类编码
	 */
	@Column("category_inline_code")
	private String categoryInlineCode ;
	/**
	 * 三级分类名称
	 */
	@Column("category_name_cn")
	private String categoryNameCn ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 划线价
	 */
	@Column("underline_price")
	private BigDecimal underlinePrice ;
	/**
	 * 亚马逊价格
	 */
	@Column("amazon_price")
	private BigDecimal amazonPrice ;
	/**
	 * 亚马逊活动类型
	 */
	@Column("amazon_activity_type")
	private String amazonActivityType ;
	/**
	 * 亚马逊秒杀价格
	 */
	@Column("amazon_seckill_price")
	private BigDecimal amazonSeckillPrice ;
	/**
	 * 亚马逊运费
	 */
	@Column("amazon_fee")
	private BigDecimal amazonFee ;
	/**
	 * 推送价格
	 */
	@Column("push_price")
	private BigDecimal pushPrice ;
	/**
	 * 推送划线价
	 */
	@Column("push_underline_price")
	private BigDecimal pushUnderlinePrice ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 所属BU组Code
	 */
	@Column("product_group_code")
	private String productGroupCode ;
	/**
	 * 所属BU组名称
	 */
	@Column("product_group_name")
	private String productGroupName ;
	/**
	 * 业务组Code
	 */
	@Column("business_group_code")
	private String businessGroupCode ;
	/**
	 * 业务组名称
	 */
	@Column("business_group_name")
	private String businessGroupName ;
	/**
	 * 所属销售/业务负责人工号
	 */
	@Column("sales_group_empt_code")
	private String salesGroupEmptCode ;
	/**
	 * 所属销售/业务负责人名称
	 */
	@Column("sales_group_empt_name")
	private String salesGroupEmptName ;
	/**
	 * 推送状态
	 */
	@Column("sync_status")
	private Integer syncStatus ;
	/**
	 * 错误信息
	 */
	@Column("sync_error_msg")
	private String syncErrorMsg ;
	/**
	 * 推送时间
	 */
	@Column("sync_time")
	private Date syncTime ;
	/**
	 * 批次号
	 */
	@Column("batch_id")
	private String batchId ;

	public SomThreePlatformPrice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* SKU
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 三级分类编码
	*@return
	*/
	public String getCategoryInlineCode(){
		return  categoryInlineCode;
	}
	/**
	* 三级分类编码
	*@param  categoryInlineCode
	*/
	public void setCategoryInlineCode(String categoryInlineCode ){
		this.categoryInlineCode = categoryInlineCode;
	}
	/**
	* 三级分类名称
	*@return
	*/
	public String getCategoryNameCn(){
		return  categoryNameCn;
	}
	/**
	* 三级分类名称
	*@param  categoryNameCn
	*/
	public void setCategoryNameCn(String categoryNameCn ){
		this.categoryNameCn = categoryNameCn;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 划线价
	*@return
	*/
	public BigDecimal getUnderlinePrice(){
		return  underlinePrice;
	}
	/**
	* 划线价
	*@param  underlinePrice
	*/
	public void setUnderlinePrice(BigDecimal underlinePrice ){
		this.underlinePrice = underlinePrice;
	}
	/**
	* 亚马逊价格
	*@return
	*/
	public BigDecimal getAmazonPrice(){
		return  amazonPrice;
	}
	/**
	* 亚马逊价格
	*@param  amazonPrice
	*/
	public void setAmazonPrice(BigDecimal amazonPrice ){
		this.amazonPrice = amazonPrice;
	}
	/**
	* 亚马逊活动类型
	*@return
	*/
	public String getAmazonActivityType(){
		return  amazonActivityType;
	}
	/**
	* 亚马逊活动类型
	*@param  amazonActivityType
	*/
	public void setAmazonActivityType(String amazonActivityType ){
		this.amazonActivityType = amazonActivityType;
	}
	/**
	* 亚马逊秒杀价格
	*@return
	*/
	public BigDecimal getAmazonSeckillPrice(){
		return  amazonSeckillPrice;
	}
	/**
	* 亚马逊秒杀价格
	*@param  amazonSeckillPrice
	*/
	public void setAmazonSeckillPrice(BigDecimal amazonSeckillPrice ){
		this.amazonSeckillPrice = amazonSeckillPrice;
	}
	/**
	* 亚马逊运费
	*@return
	*/
	public BigDecimal getAmazonFee(){
		return  amazonFee;
	}
	/**
	* 亚马逊运费
	*@param  amazonFee
	*/
	public void setAmazonFee(BigDecimal amazonFee ){
		this.amazonFee = amazonFee;
	}
	/**
	* 推送价格
	*@return
	*/
	public BigDecimal getPushPrice(){
		return  pushPrice;
	}
	/**
	* 推送价格
	*@param  pushPrice
	*/
	public void setPushPrice(BigDecimal pushPrice ){
		this.pushPrice = pushPrice;
	}
	/**
	* 推送划线价
	*@return
	*/
	public BigDecimal getPushUnderlinePrice(){
		return  pushUnderlinePrice;
	}
	/**
	* 推送划线价
	*@param  pushUnderlinePrice
	*/
	public void setPushUnderlinePrice(BigDecimal pushUnderlinePrice ){
		this.pushUnderlinePrice = pushUnderlinePrice;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 所属BU组Code
	*@return
	*/
	public String getProductGroupCode(){
		return  productGroupCode;
	}
	/**
	* 所属BU组Code
	*@param  productGroupCode
	*/
	public void setProductGroupCode(String productGroupCode ){
		this.productGroupCode = productGroupCode;
	}
	/**
	* 所属BU组名称
	*@return
	*/
	public String getProductGroupName(){
		return  productGroupName;
	}
	/**
	* 所属BU组名称
	*@param  productGroupName
	*/
	public void setProductGroupName(String productGroupName ){
		this.productGroupName = productGroupName;
	}
	/**
	* 业务组Code
	*@return
	*/
	public String getBusinessGroupCode(){
		return  businessGroupCode;
	}
	/**
	* 业务组Code
	*@param  businessGroupCode
	*/
	public void setBusinessGroupCode(String businessGroupCode ){
		this.businessGroupCode = businessGroupCode;
	}
	/**
	* 业务组名称
	*@return
	*/
	public String getBusinessGroupName(){
		return  businessGroupName;
	}
	/**
	* 业务组名称
	*@param  businessGroupName
	*/
	public void setBusinessGroupName(String businessGroupName ){
		this.businessGroupName = businessGroupName;
	}
	/**
	* 所属销售/业务负责人工号
	*@return
	*/
	public String getSalesGroupEmptCode(){
		return  salesGroupEmptCode;
	}
	/**
	* 所属销售/业务负责人工号
	*@param  salesGroupEmptCode
	*/
	public void setSalesGroupEmptCode(String salesGroupEmptCode ){
		this.salesGroupEmptCode = salesGroupEmptCode;
	}
	/**
	* 所属销售/业务负责人名称
	*@return
	*/
	public String getSalesGroupEmptName(){
		return  salesGroupEmptName;
	}
	/**
	* 所属销售/业务负责人名称
	*@param  salesGroupEmptName
	*/
	public void setSalesGroupEmptName(String salesGroupEmptName ){
		this.salesGroupEmptName = salesGroupEmptName;
	}
	/**
	* 推送状态
	*@return
	*/
	public Integer getSyncStatus(){
		return  syncStatus;
	}
	/**
	* 推送状态
	*@param  syncStatus
	*/
	public void setSyncStatus(Integer syncStatus ){
		this.syncStatus = syncStatus;
	}
	/**
	* 错误信息
	*@return
	*/
	public String getSyncErrorMsg(){
		return  syncErrorMsg;
	}
	/**
	* 错误信息
	*@param  syncErrorMsg
	*/
	public void setSyncErrorMsg(String syncErrorMsg ){
		this.syncErrorMsg = syncErrorMsg;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 推送时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}
	/**
	* 批次号
	*@return
	*/
	public String getBatchId(){
		return  batchId;
	}
	/**
	* 批次号
	*@param  batchId
	*/
	public void setBatchId(String batchId ){
		this.batchId = batchId;
	}

}
