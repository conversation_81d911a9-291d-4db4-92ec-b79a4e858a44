package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description SomTemuLocalSkuService
 * @date 2025-06-26 15:49:15
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuLocalSkuService {

    @Resource
    private SomTemuLocalSkuMapper somTemuLocalSkuMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private SomStorageLocationMapper storageLocationMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private SomTemuFbaBlackListMapper somTemuFbaBlackListMapper;

    @Resource
    private SomTemuClearanceListMapper somTemuClearanceListMapper;

    @Resource
    private SomTemuLocalWarehouseConfigMapper somTemuLocalWarehouseConfigMapper;

    @Resource
    private McStockInfoMapper mcStockInfoMapper;

    @Value("${aws.s3china.bucket}")
    private String bucketName;

    @Autowired
    @Qualifier("amazonS3China")
    private AmazonS3 amazonS3;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return 分页结果
     */
    public PageVo<SomTemuLocalSkuVo> queryByPage(SomTemuLocalSkuPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuLocalSkuVo> pageResult = somTemuLocalSkuMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuLocalSkuVo.class, searchVo);
    }

    /**
     * URL 链接下载
     *
     * @param searchVo 入参
     * @return String
     */
    public String exportUrl(SomTemuLocalSkuPageSearchVo searchVo) {
        // 获取URL链接下载需要的sku记录
        List<SomTemuImageUrlReport> records = somTemuLocalSkuMapper.getUrlExportData(searchVo);
        // 抽出公共方法，导出URL
        return exportUrl(records);
    }

    /**
     * URL 链接下载公共方法
     *
     * @param records 信息集合
     * @return Base64
     */
    public String exportUrl(List<SomTemuImageUrlReport> records) {
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        // 整合信息
        Map<String, String> productMainCodeMap = records.stream()
                .filter(data -> StrUtil.isNotEmpty(data.getProductMainCode()))
                .collect(Collectors.toMap(SomTemuImageUrlReport::getProductMainCode, SomTemuImageUrlReport::getSellerSku, (x1, x2) -> x2));
        // 获取 Temu 站点
        List<McDictionaryInfo> temuDictInfos = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "Temu").select();
        List<String> sites = temuDictInfos.stream().map(McDictionaryInfo::getItemValue).collect(Collectors.toList());
        // 获取国内商品中心S3图片服务地址
        McDictionaryInfo urlDict = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "ChinaZlccImageUrl").single();
        String imageUrl = urlDict.getItemValue();

        ListObjectsV2Request request = new ListObjectsV2Request().withBucketName(bucketName).withPrefix("som/TemuProductImage/");
        List<SomTemuImageUrlReport> reports = new ArrayList<>();
        ListObjectsV2Result response;
        do {
            response = amazonS3.listObjectsV2(request);
            for (S3ObjectSummary objectSummary : response.getObjectSummaries()) {
                if (isPicture(objectSummary.getKey())) {
                    String[] parts = objectSummary.getKey().split("/");
                    String sku = parts[2];
                    String platform = "temu";
                    String site = parts[3];
                    String imgName = parts[5];
                    String imgUrl = generatePreSignedUrl(imageUrl, bucketName, objectSummary.getKey());
                    if (sites.contains(site)) {
                        SomTemuImageUrlReport report = new SomTemuImageUrlReport();
                        report.setPlatform(platform);
                        report.setSite(site);
                        report.setProductMainCode(sku);
                        // 使用Map.getOrDefault方法替换containsKey检查
                        report.setSellerSku(productMainCodeMap.getOrDefault(sku, ""));
                        report.setImgName(imgName);
                        report.setImgUrl(imgUrl);
                        reports.add(report);
                    }
                }
            }
            // 处理分页
            request.setContinuationToken(response.getNextContinuationToken());
        } while (response.isTruncated());
        Workbook workbook;
        try {
            ExportParams params = new ExportParams(null, "TEMU图片URL");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomTemuImageUrlReport.class, reports);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(byteArray);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 判断是否是图片
     *
     * @param fileName 文件名称
     * @return boolean
     */
    private boolean isPicture(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            Set<String> imagesExtensions = new HashSet<>();
            imagesExtensions.add("png");
            imagesExtensions.add("jpg");
            imagesExtensions.add("jpeg");
            imagesExtensions.add("bmp");
            imagesExtensions.add("gif");
            imagesExtensions.add("ico");
            imagesExtensions.add("tiff");
            imagesExtensions.add("tif");
            imagesExtensions.add("raw");
            String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
            return imagesExtensions.contains(fileExtension);
        }
        return false;
    }


    /**
     * 生成图片链接
     *
     * @param imageUrl   url
     * @param bucketName 桶地址
     * @param objectKey  s3 key
     * @return url
     */
    private String generatePreSignedUrl(String imageUrl, String bucketName, String objectKey) {
        String url = imageUrl + bucketName + "/" + objectKey;
        url = url.replace(" ", "%20");
        return url;
    }

    public PageVo<SomTemuListingReport> stockReport(SomTemuLocalSkuPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuListingReport> pageResult = somTemuLocalSkuMapper.stockReport(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomStorageLocation> storageLocationList = storageLocationMapper.all();
            Map<String, String> slMap = storageLocationList.stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, String> whMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            // 获取Temu仓库配置
            List<SomTemuLocalWarehouseConfig> warehouseList = somTemuLocalWarehouseConfigMapper.all();

            Set<String> allTemuWarehouseCode = warehouseList.stream().map(SomTemuLocalWarehouseConfig::getUseableWarehouseCode).collect(Collectors.toSet());

            Map<String, List<SomTemuLocalWarehouseConfig>> warehouseMap = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getAccountId() + e.getSite()));

            // 获取Temu清仓配置
            List<SomTemuClearanceList> clearanceLists = somTemuClearanceListMapper.all();
            List<String> clearanceKeys = clearanceLists.stream().map(data -> data.getAccountId() + data.getProductSkuId()).collect(Collectors.toList());
            // 获取人工指定发货方式配置
            List<SomTemuFbaBlackList> allBlackLists = somTemuFbaBlackListMapper.createLambdaQuery().andEq("platform", "Temu").andEq("delete_flag", 10).select();
            // 获取所有仓库编码去重
            List<String> warehouseCodes = allBlackLists.stream().flatMap(e -> {
                String useableWarehouseCode = e.getUseableWarehouseStorage();
                if (StrUtil.isBlank(useableWarehouseCode) || useableWarehouseCode.length() < 3) {
                    return Stream.empty();
                }
                JSONArray array = JSONUtil.parseArray(useableWarehouseCode);
                List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
                return useableWarehouseList.stream().map(SomTemuFbaBlackListVo.UseableWarehouse::getWarehouseCode);
            }).distinct().collect(Collectors.toList());
            allTemuWarehouseCode.addAll(warehouseCodes);
            Map<String, SomTemuFbaBlackList> blackListMap = allBlackLists.stream().collect(Collectors.toMap(f -> f.getAccountId() + f.getPlatform() + f.getSite() + f.getSellerSku(), Function.identity(), (x1, x2) -> x1));
            // 获取 Temu 所有仓库的库存
            List<McStockInfo> stockList = mcStockInfoMapper.createLambdaQuery().andIn("warehouse_code", allTemuWarehouseCode).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));

            for (SomTemuListingReport report : pageResult.getList()) {
                // 判断是否是清仓商品
                boolean isClearance = clearanceKeys.contains(report.getAccountId() + report.getProductSkuId());
                report.setClearanceFlag(isClearance);
                //判断展示码是否存在人工指定发货方式配置，如果存在，可售库存按照配置的发货仓库&库区展示库存
                if (blackListMap.containsKey(report.getAccountId() + report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
                    SomTemuFbaBlackList blackList = blackListMap.get(report.getAccountId() + report.getPlatform() + report.getSite() + report.getDisplayProductCode());
                    if (StrUtil.isNotBlank(blackList.getUseableWarehouseStorage())) {
                        JSONArray array = JSONUtil.parseArray(blackList.getUseableWarehouseStorage());
                        List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
                        List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
                        int totalStock = 0;
                        for (SomTemuFbaBlackListVo.UseableWarehouse warehouse : useableWarehouseList) {
                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
                            warehouseInventory.setWarehouseName(whMap.get(warehouse.getWarehouseCode()));
                            warehouseInventory.setSlName(slMap.get(warehouse.getSlCode()));
                            McStockInfo mcStockInfo = stockMap.get(warehouse.getWarehouseCode() + warehouse.getSlCode() + report.getProductMainCode());
                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
                            totalStock += stock;
                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
                            list.add(warehouseInventory);
                        }
                        // 如果清仓产品中包含此产品，则不需要减去安全库存
                        if (isClearance) {
                            report.setStock(Math.max(totalStock, 0));
                        } else {
                            report.setStock(Math.max(totalStock - (report.getSafetyStock() == null ? 0 : report.getSafetyStock()), 0));
                        }
                        report.setList(list);
                    }
                } else {
                    //根据站点获取仓库 库区
                    List<SomTemuLocalWarehouseConfig> somTemuWarehouseConfigs = warehouseMap.get(report.getAccountId() + report.getSite());
                    List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
                    if (null != somTemuWarehouseConfigs && !somTemuWarehouseConfigs.isEmpty()) {
                        BigDecimal totalStock = BigDecimal.ZERO;
                        for (SomTemuLocalWarehouseConfig config : somTemuWarehouseConfigs) {
                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
                            warehouseInventory.setWarehouseName(whMap.get(config.getUseableWarehouseCode()));
                            warehouseInventory.setSlName(slMap.get(config.getUseableStorageCode()));
                            McStockInfo mcStockInfo = stockMap.get(config.getUseableWarehouseCode() + config.getUseableStorageCode() + report.getProductMainCode());
                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
                            totalStock = totalStock.add(BigDecimal.valueOf(stock));
                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
                            list.add(warehouseInventory);
                        }
                        // 如果清仓产品中包含此产品，则不需要减去安全库存
                        if (isClearance) {
                            report.setStock(Math.max(totalStock.intValue(), 0));
                        } else {
                            report.setStock(Math.max(totalStock.subtract(Optional.ofNullable(report.getSafetyStock()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO)).intValue(), 0));
                        }
                        report.setList(list);
                    }
                }

                report.setStock(report.getStock() == null ? 0 : report.getStock());
                if (report.getStock() < 0) {
                    report.setStock(0);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuListingReport.class, searchVo);
    }

    public String export(SomTemuLocalSkuPageSearchVo searchVo) {
        searchVo.setPageSize(Integer.MAX_VALUE);
        searchVo.setCurrent(1);
        PageVo<SomTemuListingReport> records = stockReport(searchVo);
        if (records!=null && !records.getRecords().isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Temu本本库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuListingReport.class, records.getRecords());
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}