package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* Amazon订单索赔
* gen by 代码生成器 2023-10-11
*/

@Table(name="mc.som_amazon_fba_order_claim")
public class SomAmazonFbaOrderClaim implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 订单创建日期
	 */
	@Column("order_create_date")
	private Date orderCreateDate ;
	/**
	 * 订单编号
	 */
	@Column("order_id")
	private String orderId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 10.未开case 20.已开case
	 */
	@Column("status")
	private Integer status ;
	/**
	 * CASE ID
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * 10.已索赔 99.拒绝索赔
	 */
	@Column("claim_status")
	private Integer claimStatus ;
	/**
	 * 拒绝赔付的原因
	 */
	@Column("refusal_reason")
	private String refusalReason ;
	/**
	 * 索赔金额
	 */
	@Column("claim_amount")
	private BigDecimal claimAmount ;
	/**
	 * 赔付的金额
	 */
	@Column("compensate_amount")
	private BigDecimal compensateAmount ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 选择的时间from
	 */
	@Column("date_from")
	private Date dateFrom ;
	/**
	 * 选择的时间to
	 */
	@Column("date_to")
	private Date dateTo ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAmazonFbaOrderClaim() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 订单创建日期
	*@return
	*/
	public Date getOrderCreateDate(){
		return  orderCreateDate;
	}
	/**
	* 订单创建日期
	*@param  orderCreateDate
	*/
	public void setOrderCreateDate(Date orderCreateDate ){
		this.orderCreateDate = orderCreateDate;
	}
	/**
	* 订单编号
	*@return
	*/
	public String getOrderId(){
		return  orderId;
	}
	/**
	* 订单编号
	*@param  orderId
	*/
	public void setOrderId(String orderId ){
		this.orderId = orderId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 10.未开case 20.已开case
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 10.未开case 20.已开case
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* CASE ID
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* CASE ID
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* 10.已索赔 99.拒绝索赔
	*@return
	*/
	public Integer getClaimStatus(){
		return  claimStatus;
	}
	/**
	* 10.已索赔 99.拒绝索赔
	*@param  claimStatus
	*/
	public void setClaimStatus(Integer claimStatus ){
		this.claimStatus = claimStatus;
	}
	/**
	* 拒绝赔付的原因
	*@return
	*/
	public String getRefusalReason(){
		return  refusalReason;
	}
	/**
	* 拒绝赔付的原因
	*@param  refusalReason
	*/
	public void setRefusalReason(String refusalReason ){
		this.refusalReason = refusalReason;
	}
	/**
	* 索赔金额
	*@return
	*/
	public BigDecimal getClaimAmount(){
		return  claimAmount;
	}
	/**
	* 索赔金额
	*@param  claimAmount
	*/
	public void setClaimAmount(BigDecimal claimAmount ){
		this.claimAmount = claimAmount;
	}
	/**
	* 赔付的金额
	*@return
	*/
	public BigDecimal getCompensateAmount(){
		return  compensateAmount;
	}
	/**
	* 赔付的金额
	*@param  compensateAmount
	*/
	public void setCompensateAmount(BigDecimal compensateAmount ){
		this.compensateAmount = compensateAmount;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 选择的时间from
	*@return
	*/
	public Date getDateFrom(){
		return  dateFrom;
	}
	/**
	* 选择的时间from
	*@param  dateFrom
	*/
	public void setDateFrom(Date dateFrom ){
		this.dateFrom = dateFrom;
	}
	/**
	* 选择的时间to
	*@return
	*/
	public Date getDateTo(){
		return  dateTo;
	}
	/**
	* 选择的时间to
	*@param  dateTo
	*/
	public void setDateTo(Date dateTo ){
		this.dateTo = dateTo;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
