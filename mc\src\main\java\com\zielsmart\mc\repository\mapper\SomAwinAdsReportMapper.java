package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAwinAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomAwinAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-05-11
*/

@SqlResource("somAwinAdsReport")
public interface SomAwinAdsReportMapper extends BaseMapper<SomAwinAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAwinAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAwinAdsReportVo> queryByPage(@Param("searchVo")SomAwinAdsReportPageSearchVo searchVo, PageRequest pageRequest);
}
