package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonReimbursements;
import com.zielsmart.mc.vo.SomAmazonReimbursementsPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonReimbursementsVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-14
*/

@SqlResource("somAmazonReimbursements")
public interface SomAmazonReimbursementsMapper extends BaseMapper<SomAmazonReimbursements> {
	/**
     * 分页查询
     * 
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonReimbursementsVo>}
     * <AUTHOR>
     */
    PageResult<SomAmazonReimbursementsVo> queryByPage(@Param("searchVo")SomAmazonReimbursementsPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 根据赔付日期和赔偿报表ID批量删除数据
     *
     * @param reimbursementsList 赔付数据
     */

    default void batchDeleteByApprovalDateAndReimbursementId(@Param("reimbursementsList") List<SomAmazonReimbursements> reimbursementsList) {
        this.getSQLManager().updateBatch(SqlId.of("somAmazonReimbursements.batchDeleteByApprovalDateAndReimbursementId"), reimbursementsList);
    }

}
