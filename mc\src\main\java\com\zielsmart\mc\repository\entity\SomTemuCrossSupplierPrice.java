package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
/*
* Temu跨境供货价
* gen by 代码生成器 2025-06-27
*/

@Table(name="mc.som_temu_cross_supplier_price")
public class SomTemuCrossSupplierPrice implements java.io.Serializable {
	/**
	 * 主键aid
	 */
	@AssignID
	private String aid ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private Long accountId ;
	/**
	 * 商品ID
	 */
	@Column("product_id")
	private Long productId ;
	/**
	 * SKC ID
	 */
	@Column("product_skc_id")
	private Long productSkcId ;
	/**
	 * Product sku id
	 */
	@Column("product_sku_id")
	private Long productSkuId ;
	/**
	 * 供货价
	 */
	@Column("supplier_price")
	private BigDecimal supplierPrice ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 站点供货价列表，仅半托管有值
	 */
	@Column("site_supplier_prices")
	private Object siteSupplierPrices ;
	/**
	 * 调用接口下载的时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	public SomTemuCrossSupplierPrice() {
	}

	/**
	* 主键aid
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键aid
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}
	/**
	* 店铺ID
	*@return
	*/
	public Long getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(Long accountId ){
		this.accountId = accountId;
	}
	/**
	* 商品ID
	*@return
	*/
	public Long getProductId(){
		return  productId;
	}
	/**
	* 商品ID
	*@param  productId
	*/
	public void setProductId(Long productId ){
		this.productId = productId;
	}
	/**
	* SKC ID
	*@return
	*/
	public Long getProductSkcId(){
		return  productSkcId;
	}
	/**
	* SKC ID
	*@param  productSkcId
	*/
	public void setProductSkcId(Long productSkcId ){
		this.productSkcId = productSkcId;
	}
	/**
	* Product sku id
	*@return
	*/
	public Long getProductSkuId(){
		return  productSkuId;
	}
	/**
	* Product sku id
	*@param  productSkuId
	*/
	public void setProductSkuId(Long productSkuId ){
		this.productSkuId = productSkuId;
	}
	/**
	* 供货价
	*@return
	*/
	public BigDecimal getSupplierPrice(){
		return  supplierPrice;
	}
	/**
	* 供货价
	*@param  supplierPrice
	*/
	public void setSupplierPrice(BigDecimal supplierPrice ){
		this.supplierPrice = supplierPrice;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 站点供货价列表，仅半托管有值
	*@return
	*/
	public Object getSiteSupplierPrices(){
		return  siteSupplierPrices;
	}
	/**
	* 站点供货价列表，仅半托管有值
	*@param  siteSupplierPrices
	*/
	public void setSiteSupplierPrices(Object siteSupplierPrices ){
		this.siteSupplierPrices = siteSupplierPrices;
	}
	/**
	* 调用接口下载的时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 调用接口下载的时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
