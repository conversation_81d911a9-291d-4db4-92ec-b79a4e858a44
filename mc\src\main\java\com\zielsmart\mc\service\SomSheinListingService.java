package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.McDearanceProduct;
import com.zielsmart.mc.repository.entity.McStockInfo;
import com.zielsmart.mc.repository.entity.SomSheinWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McDearanceProductMapper;
import com.zielsmart.mc.repository.mapper.McStockInfoMapper;
import com.zielsmart.mc.repository.mapper.SomSheinListingMapper;
import com.zielsmart.mc.repository.mapper.SomSheinWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomSheinListingReport;
import com.zielsmart.mc.vo.SomSheinListingVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomSheinListingService {

    @Resource
    private SomSheinListingMapper somSheinListingMapper;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private SomSheinWarehouseConfigMapper somSheinWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDearanceProductMapper mcDearanceProductMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomSheinListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomSheinListingVo> queryByPage(SomSheinWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        if (null != searchVo.getSite()) {
            //Shein.us  to shein-us
            searchVo.setSite(searchVo.getSite().replace("Shein.", "shein-"));
        }
        PageResult<SomSheinListingVo> pageResult = dynamicSqlManager.getMapper(SomSheinListingMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            Map<String, String> warehouseMap = somSheinWarehouseConfigMapper.all().stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), x -> x.getWarehouseName(), (x1, x2) -> x1));
            for (SomSheinListingVo vo : pageResult.getList()) {
                String imageList = vo.getImageList();
                if (StrUtil.isNotBlank(imageList)) {
                    JSONArray jsonArray = JSONUtil.parseArray(imageList);
                    JSONObject json = (JSONObject) jsonArray.stream().filter(x -> {
                        JSONObject jsonObject = JSONUtil.parseObj(x);
                        return jsonObject.getInt("sort", -1) == 1;
                    }).findAny().orElse(null);
                    if (null != json) {
                        vo.setImageSmallUrl(json.getStr("imageSmallUrl"));
                        vo.setImageUrl(json.getStr("imageUrl"));
                        vo.setImageList(null);
                    }
                }
                if (StrUtil.isNotEmpty(vo.getCurrentPrices())) {
                    List<SomSheinListingVo.CurrentPrice> currentPrices = objectMapper.readValue(vo.getCurrentPrices(), new TypeReference<List<SomSheinListingVo.CurrentPrice>>() {
                    });
                    vo.setCurrentPriceList(currentPrices);
                    vo.setCurrentPrices(null);
                }
                if (StrUtil.isNotEmpty(vo.getGoodInventory())) {
                    SomSheinListingVo.GoodInventory goodInventory = objectMapper.readValue(vo.getGoodInventory(), SomSheinListingVo.GoodInventory.class);
                    for (SomSheinListingVo.WarehouseInventoryList warehouse : goodInventory.getWarehouseInventoryList()) {
                        warehouse.setWarehouseName(warehouseMap.get(warehouse.getWarehouseCode()));
                    }
                    if(CollUtil.isEmpty(goodInventory.getWarehouseInventoryList())) {
                        System.out.println(vo.getId());
                        continue;
                    }
                    if (CollUtil.isEmpty(goodInventory.getWarehouseInventoryList())) {
                        vo.setStock(BigDecimal.ZERO);
                    } else {
                        vo.setStock(goodInventory.getWarehouseInventoryList().get(0).getInventoryQuantity());
                    }
                    vo.setWarehouseInventoryList(goodInventory.getWarehouseInventoryList());
                    vo.setGoodInventory(null);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomSheinListingVo.class, searchVo);
    }


    public PageVo<SomSheinListingReport> stockReport(SomSheinWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomSheinListingReport> pageResult = somSheinListingMapper.stockReport(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<McStockInfo> stockList = mcStockInfoMapper.createQuery().andIn("product_main_code", pageResult.getList().stream().map(x -> x.getProductMainCode()).collect(Collectors.toList())).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
            List<SomSheinWarehouseConfig> warehouseList = somSheinWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = warehouseList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode(), x -> x.getWarehouseName(), (x1, x2) -> x1));
            Map<String, List<SomSheinWarehouseConfig>> warehouseByCode = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode()));

            // 获取清仓产品
            List<McDearanceProduct> mcDearanceProductList = mcDearanceProductMapper.createLambdaQuery().andEq("platform", "Shein").andEq("status", 1).select();
            Map<String, McDearanceProduct> mcDearanceProductMap = mcDearanceProductList.stream().collect(Collectors.toMap(x -> x.getPlatform() + x.getSite() + x.getSellSku(), v -> v, (key1, key2) -> key1));


            for (SomSheinListingReport report : pageResult.getList()) {
                if (StrUtil.isNotEmpty(report.getGoodInventory())) {
                    SomSheinListingVo.GoodInventory goodInventory = objectMapper.readValue(report.getGoodInventory(), SomSheinListingVo.GoodInventory.class);
                    List<SomSheinListingVo.WarehouseInventoryList> warehouseInventoryList = goodInventory.getWarehouseInventoryList();

                    for (SomSheinListingVo.WarehouseInventoryList warehouse : warehouseInventoryList) {
                        warehouse.setWarehouseName(warehouseMap.get(warehouse.getWarehouseCode()));
                        List<SomSheinWarehouseConfig> configList = warehouseByCode.get(warehouse.getWarehouseCode());
                        if (null != configList && !configList.isEmpty()) {
                            configList.stream().map(x -> formatInteger(stockMap.getOrDefault(x.getUseableWarehouseCode() + x.getUseableStorageCode() + report.getProductMainCode(), new McStockInfo()).getTotalStock())).reduce(Integer::sum).ifPresent(x -> warehouse.setCalcStock(x));
                            configList.stream().map(x -> formatBigdecial(stockMap.getOrDefault(x.getUseableWarehouseCode() + x.getUseableStorageCode() + report.getProductMainCode(), new McStockInfo()).getSevenDayNumber())).reduce(BigDecimal::add).ifPresent(x -> warehouse.setSevenDayNumber(x));
                        }
                    }
                    // 获取库存同步频次
//                    Integer inventorySyncFrequency = (null == report.getSyncFrequency() ? 0 : report.getSyncFrequency());
//                    if (inventorySyncFrequency == null || inventorySyncFrequency == 0) {
//                        throw new RuntimeException("库存同步频次不能为空或为0");
//                    }
                    // 获取设置的安全库存
                    Integer safetyInventory = (null == report.getSafetyStock() ? 0 : report.getSafetyStock());
                    // 计算安全库存
                    BigDecimal sevenDayNumber = warehouseInventoryList.stream().filter(x -> null != x.getSevenDayNumber()).map(x -> x.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    int safeInventory = Integer.max((sevenDayNumber.divide(BigDecimal.valueOf(2),5, RoundingMode.DOWN).intValue()), safetyInventory);
                    //先按照清仓产品计算，如果不是清仓产品再计算
                    report.setStock(warehouseInventoryList.stream().filter(x -> null != x.getCalcStock()).map(x -> x.getCalcStock()).reduce(Integer::sum).orElse(0));
                    // 判断是否清仓产品
                    String platformSiteSku = report.getPlatform() + report.getSite() + report.getDisplayProductCode();
                    if (!mcDearanceProductMap.containsKey(platformSiteSku)) {
                        //正常产品
                        report.setStock(report.getStock() - safeInventory);
                    }
                    //if stock is less than 0,set the value to 0
                    if (report.getStock() < 0) {
                        report.setStock(0);
                    }
                    report.setList(warehouseInventoryList);
                    report.setGoodInventory(null);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomSheinListingReport.class, searchVo);
    }

    private int formatInteger(Integer integer) {
        if (integer == null) {
            return 0;
        }
        return integer.intValue();
    }

    private BigDecimal formatBigdecial(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    public String export(SomSheinWarehouseConfigPageSearchVo searchVo) throws JsonProcessingException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomSheinListingReport> records = stockReport(searchVo).getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Shein可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomSheinListingReport.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
