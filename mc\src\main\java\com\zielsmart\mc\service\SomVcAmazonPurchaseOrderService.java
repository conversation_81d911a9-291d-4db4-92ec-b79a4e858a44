package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrder;
import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderCase;
import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderCaseItem;
import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderInvoice;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.annotation.Update;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcAmazonPurchaseOrderService {

    private static final int FOB = 20;
    private static final int DDP = 10;
    @Resource
    private SomVcAmazonPurchaseOrderMapper somVcAmazonPurchaseOrderMapper;
    @Resource
    private SomVcAmazonPurchaseOrderInvoiceMapper orderInvoiceMapper;
    @Resource
    private SomVcAmazonPurchaseOrderCaseMapper caseMapper;
    @Resource
    private SomVcAmazonPurchaseOrderCaseItemMapper caseItemMapper;
    @Resource
    private McDictionaryInfoMapper dictMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private IMagicService magicService;
    @Value("${magic.head.token}")
    private String token;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomVcAmazonPurchaseOrderVo> queryByPage(SomVcAmazonPurchaseOrderPageSearchVo searchVo) {
        //https://songmicshome.feishu.cn/docx/T2CedbWJ4oRhnixwrQNcnBulnvh 文档地址
        //字典值：20.Dispute   30.Case 1     40.Case 2
        if (ObjectUtil.isNotNull(searchVo.getDisputeDateBegin()) || ObjectUtil.isNotNull(searchVo.getDisputeFeedbackDateBegin())) {
            searchVo.getTags().add(20);
        }
        if (ObjectUtil.isNotNull(searchVo.getCase1DateBegin()) || ObjectUtil.isNotNull(searchVo.getCase1FeedbackDateBegin())) {
            searchVo.getTags().add(30);
        }
        if (ObjectUtil.isNotNull(searchVo.getCase2DateBegin()) || ObjectUtil.isNotNull(searchVo.getCase2FeedbackDateBegin())) {
            searchVo.getTags().add(40);
        }

        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcAmazonPurchaseOrderVo> pageResult = dynamicSqlManager.getMapper(SomVcAmazonPurchaseOrderMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<String> aidList = pageResult.getList().stream().map(x -> x.getAid()).collect(Collectors.toList());
            List<SomVcAmazonPurchaseOrderInvoice> invoiceList = orderInvoiceMapper.createLambdaQuery().andIn("purchase_id", aidList).select();
            List<SomVcAmazonPurchaseOrderInvoiceVo> invoiceVoList = ConvertUtils.listConvert(invoiceList, SomVcAmazonPurchaseOrderInvoiceVo.class);
            Map<String, List<SomVcAmazonPurchaseOrderInvoiceVo>> invoiceMap = invoiceVoList.stream().collect(Collectors.groupingBy(x -> x.getPurchaseId()));
            for (SomVcAmazonPurchaseOrderVo order : pageResult.getList()) {
                if (invoiceMap.containsKey(order.getAid())) {
                    order.setInvoiceList(invoiceMap.get(order.getAid()));
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomVcAmazonPurchaseOrderVo.class, searchVo);
    }

    /**
     * financeRemark
     * 财务备注
     *
     * @param somVcAmazonPurchaseOrderVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void financeRemark(SomVcAmazonPurchaseOrderVo somVcAmazonPurchaseOrderVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcAmazonPurchaseOrderVo) || StrUtil.isEmpty(somVcAmazonPurchaseOrderVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        SomVcAmazonPurchaseOrder single = somVcAmazonPurchaseOrderMapper.single(somVcAmazonPurchaseOrderVo.getAid());
        single.setFinanceRemark(somVcAmazonPurchaseOrderVo.getFinanceRemark());
        somVcAmazonPurchaseOrderMapper.updateById(single);
    }

    /**
     * 关闭索赔
     *
     * @param somVcAmazonPurchaseOrderVo
     * @param tokenUser
     * @throws ValidateException
     */
    public void close(SomVcAmazonPurchaseOrderVo somVcAmazonPurchaseOrderVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcAmazonPurchaseOrderVo) || StrUtil.isEmpty(somVcAmazonPurchaseOrderVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        SomVcAmazonPurchaseOrder single = somVcAmazonPurchaseOrderMapper.single(somVcAmazonPurchaseOrderVo.getAid());
        single.setProcessState(somVcAmazonPurchaseOrderVo.getProcessState());
        single.setClaimRemark(somVcAmazonPurchaseOrderVo.getClaimRemark());
        somVcAmazonPurchaseOrderMapper.updateById(single);
    }

    /**
     * 导出
     *
     * @param searchVo
     * @return
     */
    public String export(SomVcAmazonPurchaseOrderPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomVcAmazonPurchaseOrderVo> records = queryByPage(searchVo).getRecords();
        if (records.isEmpty()) {
            return null;
        }
        List<String> aids = records.stream().map(x -> x.getAid()).collect(Collectors.toList());
        List<SomVcAmazonPurchaseOrderCaseVo> caseList = caseMapper.queryByPoAids(aids);
        List<SomVcAmazonPurchaseOrderCaseItemVo> caseItemList = caseItemMapper.queryByPoAids(aids);
        Map<String, List<SomVcAmazonPurchaseOrderCaseVo>> caseMap = caseList.stream().collect(Collectors.groupingBy(x -> x.getInvoiceId()));
        Map<String, List<SomVcAmazonPurchaseOrderCaseItemVo>> caseItemMap = caseItemList.stream().collect(Collectors.groupingBy(x -> x.getPoAid()));
        Map<String, List<SomVcAmazonPurchaseOrderCaseItemVo>> caseItemByInvoiceMap = caseItemList.stream().collect(Collectors.groupingBy(x -> x.getInvoiceAid()));
        Map<String, List<SomVcAmazonPurchaseOrderCaseItemVo>> caseItemByCaseIdMap = caseItemList.stream().collect(Collectors.groupingBy(x -> x.getCaseId()));


        List<SomVcAmazonPurcaseOrderExport> exportList = new ArrayList<>();
        for (SomVcAmazonPurchaseOrderVo record : records) {
            List<SomVcAmazonPurchaseOrderCaseItemVo> caseItem = null;
            BigDecimal totalAmount = null;
            BigDecimal remainder = null;
            if (caseItemMap.containsKey(record.getAid())) {
                caseItem = caseItemMap.get(record.getAid());
                totalAmount = caseItem.stream().filter(x -> ObjectUtil.isNotNull(x.getClaimAmount())).map(x -> x.getClaimAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal paidSum = caseItem.stream().filter(x -> ObjectUtil.isNotNull(x.getAmountPaid())).map(x -> x.getAmountPaid()).reduce(BigDecimal.ZERO, BigDecimal::add);
                remainder = totalAmount.subtract(paidSum);
            }
            List<SomVcAmazonPurchaseOrderInvoiceVo> invoices = record.getInvoiceList();
            List<SomVcAmazonPurcaseOrderExport.Invoice> invoiceList = new ArrayList<>();
            for (SomVcAmazonPurchaseOrderInvoiceVo invoice : invoices) {
                List<SomVcAmazonPurchaseOrderCaseItemVo> caseItemByInvoice = null;
                if (caseItemByInvoiceMap.containsKey(invoice.getAid())) {
                    caseItemByInvoice = caseItemByInvoiceMap.get(invoice.getAid());
                }
                SomVcAmazonPurcaseOrderExport.Invoice invoiceEle = new SomVcAmazonPurcaseOrderExport.Invoice();
                invoiceEle.setInvoiceNumber(invoice.getInvoiceNumber());
                invoiceEle.setPaymentDueDate(invoice.getPaymentDueDate());
                invoiceEle.setCaseItem(caseItemByInvoice);

                SomVcAmazonPurcaseOrderExport.Dispute dispute = new SomVcAmazonPurcaseOrderExport.Dispute();
                SomVcAmazonPurcaseOrderExport.Case1 case1 = new SomVcAmazonPurcaseOrderExport.Case1();
                SomVcAmazonPurcaseOrderExport.Case2 case2 = new SomVcAmazonPurcaseOrderExport.Case2();
                if (caseMap.containsKey(invoice.getAid())) {
                    //按照业务逻辑，这个List最多有3条
                    List<SomVcAmazonPurchaseOrderCaseVo> caseItemEle = caseMap.get(invoice.getAid());
                    if (caseItemEle.size() > 3) {
                        log.error("VC短装索赔Case数量：" + caseItemEle.size());
                    }
                    for (SomVcAmazonPurchaseOrderCaseVo caseVo : caseItemEle) {
                        getCaseDataByTag(caseVo, dispute, case1, case2, caseItemByCaseIdMap);
                    }
                }
                invoiceEle.setDispute(dispute);
                invoiceEle.setCase1(case1);
                invoiceEle.setCase2(case2);
                invoiceList.add(invoiceEle);
            }

            SomVcAmazonPurcaseOrderExport export = SomVcAmazonPurcaseOrderExport.builder()
                    .purchaseOrder(record.getPurchaseOrder())
                    .marketplace(record.getMarketplace())
                    .invoiceList(invoiceList)
                    .totalAmount(totalAmount)
                    .processState(record.getProcessState())
                    .remainder(remainder)
                    .financeRemark(record.getFinanceRemark())
                    .build();

            exportList.add(export);
        }

        Workbook workbook = null;
        try {
            ExportParams params = new ExportParams(null, "短装采购订单表管理");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomVcAmazonPurcaseOrderExport.class, exportList);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(barray);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private void getCaseDataByTag(SomVcAmazonPurchaseOrderCaseVo caseVo, SomVcAmazonPurcaseOrderExport.Dispute dispute, SomVcAmazonPurcaseOrderExport.Case1 case1, SomVcAmazonPurcaseOrderExport.Case2 case2, Map<String, List<SomVcAmazonPurchaseOrderCaseItemVo>> caseItemByCaseIdMap) {
        //字典值：20.Dispute 30.Case 1 40.Case 2
        BigDecimal amount = BigDecimal.ZERO;
        if (caseItemByCaseIdMap.containsKey(caseVo.getAid())) {
            List<SomVcAmazonPurchaseOrderCaseItemVo> itemList = caseItemByCaseIdMap.get(caseVo.getAid());
            amount = itemList.stream().filter(x -> ObjectUtil.isNotNull(x.getAmountPaid())).map(x -> x.getAmountPaid()).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        switch (caseVo.getTag()) {
            case 20:
                dispute.setCaseId(caseVo.getCaseId());
                dispute.setCreateDate(caseVo.getCreateTime());
                dispute.setFeedbackDate(caseVo.getFeedbackTime());
                dispute.setAmountPaid(amount);
                break;
            case 30:
                case1.setCaseId(caseVo.getCaseId());
                case1.setCreateDate(caseVo.getCreateTime());
                case1.setFeedbackDate(caseVo.getFeedbackTime());
                case1.setAmountPaid(amount);
                break;
            case 40:
                case2.setCaseId(caseVo.getCaseId());
                case2.setCreateDate(caseVo.getCreateTime());
                case2.setFeedbackDate(caseVo.getFeedbackTime());
                case2.setAmountPaid(amount);
                break;
            default:
                break;
        }
    }

    /**
     * 导入
     *
     * @param list
     * @param tokenUser
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomVcAmazonPurchaseOrderImportVo> list, TokenUserInfo tokenUser) throws ParseException, ValidateException {
        //欧洲德国货币格式 13.847,80 €
        NumberFormat deNumber = NumberFormat.getInstance(Locale.GERMANY);
        //美国 $55,997.50
        NumberFormat usNumber = NumberFormat.getInstance(Locale.US);
        //查询市场站点

        List<SomVcAmazonPurchaseOrder> purchaseOrderList = new ArrayList<>();
        List<SomVcAmazonPurchaseOrderInvoice> purchaseOrderInvoicesList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        Date nowDate = Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant());
        /**
         * 若当前数据的marketplace为"DE","IT","GB","ES","FR","PL","SE","NL","BE","TR"其中的一个时，判断当前数据的type是否为FOB，若为FOB，则只能向系统写入Payment due date < (当前日期 - 20天)的数据。
         */
        List<String> marketplaceList = Arrays.asList("DE", "IT", "GB", "ES", "FR", "PL", "SE", "NL", "BE", "TR");
        List<String> invoiceNumber = list.stream().map(x -> x.getInvoiceNumber()).distinct().collect(Collectors.toList());
        List<String> marketplaces = list.stream().map(x -> x.getMarketplace()).distinct().collect(Collectors.toList());
        List<String> purchaseOrders = list.stream().map(x -> x.getPurchaseOrder()).distinct().collect(Collectors.toList());
        Map<String, SomVcAmazonPurchaseOrder> purchaseOrderMap = somVcAmazonPurchaseOrderMapper.createLambdaQuery()
                .andIn("marketplace", marketplaces)
                .andIn("purchase_order", purchaseOrders)
                .select()
                .stream()
                .collect(Collectors.toMap(x -> x.getMarketplace() + x.getPurchaseOrder(), Function.identity(), (x1, x2) -> x1));
        Map<String, String> invoiceMap = orderInvoiceMapper.createLambdaQuery()
                .andIn("invoice_number", invoiceNumber)
                .select()
                .stream()
                .collect(Collectors.toMap(x -> x.getInvoiceNumber(), x -> x.getInvoiceNumber(), (x1, x2) -> x1));
        Map<String, String> currencyMap = dictMapper.createLambdaQuery()
                .andEq("item_type_code", "Currency")
                .select()
                .stream()
                .collect(Collectors.toMap(x -> x.getItemValue1(), y -> y.getItemValue(), (x1, x2) -> x1));

        Map<String, String> marketplaceMap = dictMapper.createLambdaQuery()
                .andEq("item_type_code", "PurchaseMarketplace")
                .select()
                .stream()
                .filter(x -> StrUtil.isNotEmpty(x.getItemValue1()))
                .collect(Collectors.toMap(x -> x.getItemValue(), y -> y.getItemValue1(), (x1, x2) -> x1));

        Map<String, SomVcAmazonPurchaseOrder> insertPoMap = new HashMap<>();
        //调用接口查询 传po数据，去掉下划线和后面的内容
        List<String> poListNo_ = purchaseOrders.stream().map(x -> {
            if (x.contains("_")) {
                x = x.substring(0, x.indexOf("_"));
            }
            return x;
        }).collect(Collectors.toList());
        Map<String, List<String>> poBody = new HashMap<>();
        poBody.put("customOrderCode", poListNo_);
        List<String> fobPoList = null;
        ResultVo<List<String>> resultVo = null;
        try {
            resultVo = magicService.findCgsq(token, poBody);
        } catch (Exception e) {
            throw new RuntimeException("调用EYA接口异常，查询FOB订单类型失败。" + e + "\ntoken:" + token);
        }
        if (!resultVo.isSuccess()) {
            throw new ValidateException("调用EYA接口返回状态失败：" + resultVo.getMessage() + "\ntoken:" + token);
        }
        fobPoList = resultVo.getData();
        if (fobPoList == null) {
            log.error("查询FOB类型返回数据为null");
            throw new ValidateException("查询FOB类型返回数据为null" + "\ntoken:" + token);
        }

        for (SomVcAmazonPurchaseOrderImportVo order : list) {
            if (order == null) {
                continue;
            }
            if (null == order.getCurrencySymbol()) {
                continue;
            }
            String poTmp = order.getPurchaseOrder();
            if (poTmp.contains("_")) {
                poTmp = poTmp.substring(0, poTmp.indexOf("_"));
            }
            int purchaseType = fobPoList.contains(poTmp) ? FOB : DDP;
            NumberFormat numberFormat;
            SimpleDateFormat sdf = new SimpleDateFormat();
            String symbol;
            //region 处理日期  由于德美是两个不同格式的excel 要特殊处理
            if (!marketplaceMap.containsKey(order.getMarketplace())) {
                throw new ValidateException("字典值 PurchaseMarketplace中" + order.getMarketplace() + " 没有配置value1 时间格式");
            }
            sdf.applyPattern(marketplaceMap.get(order.getMarketplace()));

            if (StrUtil.isNotEmpty(order.getPaymentDueDateStr())) {
                order.setPaymentDueDate(sdf.parse(order.getPaymentDueDateStr()));
                //比较时间
                LocalDate localDate = now.minusDays(20);
                LocalDate minuDays1 = now.minusDays(1);
                Date min20 = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                Date min1 = Date.from(minuDays1.atStartOfDay(ZoneId.systemDefault()).toInstant());
                //只能向系统写入Payment due date < (当前日期 - 20天)的数据
                if (marketplaceList.contains(order.getMarketplace())) {
                    if (FOB == purchaseType) {
                        //过滤掉日期大于小于min20的数据
                        if (order.getPaymentDueDate().compareTo(min20) >= 0) {
                            continue;
                        }
                    } else {
                        //过滤掉日期大于等于min1的数据
                        if (order.getPaymentDueDate().compareTo(min1) >= 0) {
                            continue;
                        }
                    }
                } else {
                    //过滤掉日期大于等于min1的数据
                    if (order.getPaymentDueDate().compareTo(min1) >= 0) {
                        continue;
                    }
                }
            } else {
                log.info("paymentDueDate日期为空：" + order.getPurchaseOrder());
                continue;
            }
            if (StrUtil.isNotEmpty(order.getCerationDateStr())) {
                order.setCerationDate(sdf.parse(order.getCerationDateStr()));
            }

            if (StrUtil.isNotEmpty(order.getInvoiceDateStr())) {
                order.setInvoiceDate(sdf.parse(order.getInvoiceDateStr()));
            }
            //endregion
            //region 处理货币符号 金额
            String currencySymbol = order.getCurrencySymbol();
            if (StrUtil.isNotEmpty(currencySymbol) && !currencySymbol.contains("€")) {
                //美元 $55,997.50  excel展示包含$ 实际值是不含$的      目前确认只有两种货币  $ 和 €
                numberFormat = usNumber;
                symbol = "$";
            } else if (StrUtil.isNotEmpty(currencySymbol) && currencySymbol.contains("€")) {
                //欧元
                numberFormat = deNumber;
                symbol = "€";
            } else {
                log.error("金额:{} 不在预期类型内，跳过此条数据", order.getCurrencySymbol());
                continue;
            }
            try {
                BigDecimal bd = parseCurrency(currencySymbol, symbol, numberFormat);
                order.setInvoiceAmount(bd);
                order.setCurrencySymbol(symbol);
            } catch (ParseException e) {
                log.error("解析金额:{} 出错，跳过此条数据 {}", order.getCurrencySymbol(), e);
                continue;
            }
            //endregion

            SomVcAmazonPurchaseOrder purchaseOrder;
            String purcheseKey = order.getMarketplace() + order.getPurchaseOrder();
            if (purchaseOrderMap.containsKey(purcheseKey)) {
                purchaseOrder = purchaseOrderMap.get(purcheseKey);
            } else if (insertPoMap.containsKey(purcheseKey)) {
                purchaseOrder = insertPoMap.get(purcheseKey);
            } else {
                purchaseOrder = ConvertUtils.beanConvert(order, SomVcAmazonPurchaseOrder.class);
                purchaseOrder.setAid(IdUtil.fastSimpleUUID());
                purchaseOrder.setCreateName(tokenUser.getUserName());
                purchaseOrder.setCreateNum(tokenUser.getJobNumber());
                purchaseOrder.setCreateTime(nowDate);
                purchaseOrder.setPurchaseType(purchaseType);
                purchaseOrder.setProcessState(10);
                purchaseOrderList.add(purchaseOrder);
                insertPoMap.put(purcheseKey, purchaseOrder);
            }
            if (invoiceMap.containsKey(order.getInvoiceNumber())) {
                continue;
            }
            SomVcAmazonPurchaseOrderInvoice invoice = new SomVcAmazonPurchaseOrderInvoice();
            invoice.setAid(IdUtil.fastSimpleUUID());
            invoice.setPurchaseId(purchaseOrder.getAid());
            invoice.setInvoiceNumber(order.getInvoiceNumber());
            invoice.setPaymentDueDate(order.getPaymentDueDate());
            invoice.setInvoiceDate(order.getInvoiceDate());
            invoice.setCerationDate(order.getCerationDate());
            invoice.setInvoiceAmount(order.getInvoiceAmount());
            invoice.setCurrencySymbol(order.getCurrencySymbol());
            invoice.setCurrency(currencyMap.getOrDefault(invoice.getCurrencySymbol(), null));
            invoice.setCreateName(tokenUser.getUserName());
            invoice.setCreateNum(tokenUser.getJobNumber());
            invoice.setCreateTime(nowDate);
            purchaseOrderInvoicesList.add(invoice);
        }
        if (!purchaseOrderList.isEmpty()) {
            somVcAmazonPurchaseOrderMapper.insertBatch(purchaseOrderList);
        }
        if (!purchaseOrderInvoicesList.isEmpty()) {
            orderInvoiceMapper.insertBatch(purchaseOrderInvoicesList);
        }
    }

    private BigDecimal parseCurrency(String currency, String symbol, NumberFormat format) throws ParseException {
        currency = currency.replace(symbol, "").trim();
        Number number = format.parse(currency);
        return new BigDecimal(number.toString());
    }

    public List<SomVcAmazonPurchaseOrderCaseVo> loadCase(SomVcAmazonPurchaseOrderVo orderVo) throws ValidateException {
        if (ObjectUtil.isEmpty(orderVo) || StrUtil.isEmpty(orderVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        SomVcAmazonPurchaseOrderInvoice invoice = orderInvoiceMapper.single(orderVo.getAid());
        SomVcAmazonPurchaseOrder order = somVcAmazonPurchaseOrderMapper.single(invoice.getPurchaseId());
        List<SomVcAmazonPurchaseOrderCase> caseList = caseMapper.createLambdaQuery()
                .andEq("invoice_id", orderVo.getAid())
                .select();
        List<SomVcAmazonPurchaseOrderCaseVo> caseVoList = ConvertUtils.listConvert(caseList, SomVcAmazonPurchaseOrderCaseVo.class);
        for (SomVcAmazonPurchaseOrderCaseVo vo : caseVoList) {
            vo.setPurchaseOrder(order.getPurchaseOrder());
            vo.setCurrencySymbol(invoice.getCurrencySymbol());
            vo.setInvoiceNumber(invoice.getInvoiceNumber());
            vo.setInvoiceAmount(invoice.getInvoiceAmount());
        }
        return caseVoList;
    }

    public List<SomVcAmazonPurchaseOrderCaseItemVo> loadCaseDetail(SomVcAmazonPurchaseOrderCaseVo caseVo) throws ValidateException {
        if (ObjectUtil.isEmpty(caseVo) || StrUtil.isEmpty(caseVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        List<SomVcAmazonPurchaseOrderCaseItem> caseList = caseItemMapper.createLambdaQuery()
                .andEq("case_id", caseVo.getAid())
                .select();
        return ConvertUtils.listConvert(caseList, SomVcAmazonPurchaseOrderCaseItemVo.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Update
    public void feedbackCase(SomVcAmazonPurchaseOrderCaseVo caseVo, TokenUserInfo tokenUser) throws ValidateException {
        List<SomVcAmazonPurchaseOrderCaseItemVo> caseItem = caseVo.getCaseItem();
        String caseAid = caseVo.getAid();
        if (StrUtil.isEmpty(caseAid)) {
            throw new ValidateException("CASE AID 不能为空");
        }
        SomVcAmazonPurchaseOrderCase caseData = caseMapper.single(caseAid);
        caseData.setFeedbackTime(caseVo.getFeedbackTime());
        caseMapper.updateById(caseData);
        SomVcAmazonPurchaseOrderInvoice invoice = orderInvoiceMapper.single(caseData.getInvoiceId());

        if (caseItem.isEmpty()) {
            caseItemMapper.createLambdaQuery().andEq("case_id", caseAid).delete();
            return;
        }
        Map<String, SomVcAmazonPurchaseOrderCaseItem> caseItemMap = caseItemMapper.createLambdaQuery().andEq("case_id", caseAid)
                .select()
                .stream()
                .collect(Collectors.toMap(x -> x.getAid(), Function.identity()));
        List<SomVcAmazonPurchaseOrderCaseItem> updateList = new ArrayList<>();
        List<SomVcAmazonPurchaseOrderCaseItem> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomVcAmazonPurchaseOrderCaseItemVo item : caseItem) {
            if (null == item.getAid()) {
                SomVcAmazonPurchaseOrderCaseItem ele = ConvertUtils.beanConvert(item, SomVcAmazonPurchaseOrderCaseItem.class);
                ele.setAid(IdUtil.fastSimpleUUID());
                ele.setCaseId(caseData.getAid());
                ele.setCurrency(invoice.getCurrency());
                ele.setCurrencySymbol(invoice.getCurrencySymbol());
                ele.setCreateName(tokenUser.getUserName());
                ele.setCreateTime(now);
                ele.setCreateNum(tokenUser.getJobNumber());
                insertList.add(ele);
                continue;
            }
            if (caseItemMap.containsKey(item.getAid())) {
                SomVcAmazonPurchaseOrderCaseItem ele = caseItemMap.get(item.getAid());
                ele.setAmountPaid(item.getAmountPaid());
                ele.setAsin(item.getAsin());
                ele.setQuantity(item.getQuantity());
                ele.setClaimAmount(item.getClaimAmount());
                updateList.add(ele);
                caseItemMap.remove(item.getAid());
            }
        }

        if (!updateList.isEmpty()) {
            caseItemMapper.updateBatch(updateList);
        }
        if (!insertList.isEmpty()) {
            caseItemMapper.insertBatch(insertList);
        }
        if (!caseItemMap.isEmpty()) {
            caseItemMapper.createLambdaQuery().andIn("aid", caseItemMap.keySet()).delete();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void openCase(SomVcAmazonPurchaseOrderCaseVo caseVo, TokenUserInfo tokenUser) throws ValidateException {
        //发票ID
        String invoiceId = caseVo.getAid();
        String caseId = caseVo.getCaseId();
        if (StrUtil.isEmpty(caseId) || StrUtil.isEmpty(invoiceId)) {
            throw new ValidateException("发票ID 或 caseId 不能为空");
        }
        List<SomVcAmazonPurchaseOrderCaseItemVo> caseItem = caseVo.getCaseItem();
        if (caseItem.isEmpty()) {
            throw new ValidateException("用户至少需要添加一条索赔明细");
        }

        //Case ID 唯一，不允许出现重复的Case ID
        long caseCount = caseMapper.createLambdaQuery().andEq("case_id", caseId).count();
        if (caseCount > 0) {
            throw new ValidateException("您输入的Case ID在系统中已存在。");
        }
        //根据「TAG」判断，每种TAG的数据只允许有一条
        long tagCount = caseMapper.createLambdaQuery().andEq("invoice_id", invoiceId).andEq("tag", caseVo.getTag()).count();
        if (tagCount > 0) {
            throw new ValidateException("您输入的Tag在系统中已存在。");
        }
        //查询发票数据
        SomVcAmazonPurchaseOrderInvoice invoice = orderInvoiceMapper.single(invoiceId);

        String caseAid = IdUtil.fastSimpleUUID();
        LocalDate now = LocalDate.now();
        Date nowDate = Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<SomVcAmazonPurchaseOrderCaseItem> caseItemList = new ArrayList<>();

        for (SomVcAmazonPurchaseOrderCaseItemVo item : caseItem) {
            if (StrUtil.isEmpty(item.getAsin())) {
                throw new ValidateException("Asin不允许为空");
            }
            if (ObjectUtil.isNull(item.getQuantity()) || item.getQuantity() < 0) {
                throw new ValidateException("数量不允许为空 或 小于0");
            }
            if (ObjectUtil.isNull(item.getClaimAmount()) || item.getClaimAmount().doubleValue() < 0) {
                throw new ValidateException("索赔金额不允许为空 或 小于0");
            }
            SomVcAmazonPurchaseOrderCaseItem caseItemVo = new SomVcAmazonPurchaseOrderCaseItem();
            caseItemVo.setAid(IdUtil.fastSimpleUUID());
            caseItemVo.setCaseId(caseAid);
            caseItemVo.setCurrency(invoice.getCurrency());
            caseItemVo.setCurrencySymbol(invoice.getCurrencySymbol());
            caseItemVo.setQuantity(item.getQuantity());
            caseItemVo.setClaimAmount(item.getClaimAmount());
            caseItemVo.setAsin(item.getAsin());
            caseItemVo.setCreateName(tokenUser.getUserName());
            caseItemVo.setCreateNum(tokenUser.getJobNumber());
            caseItemVo.setCreateTime(nowDate);
            caseItemList.add(caseItemVo);
        }
        //更新PO状态
        SomVcAmazonPurchaseOrder purchaseOrder = somVcAmazonPurchaseOrderMapper.single(invoice.getPurchaseId());
        purchaseOrder.setProcessState(caseVo.getTag());
        somVcAmazonPurchaseOrderMapper.updateById(purchaseOrder);
        //新增CASE数据
        SomVcAmazonPurchaseOrderCase caseData = new SomVcAmazonPurchaseOrderCase();
        caseData.setAid(caseAid);
        caseData.setCaseId(caseId);
        caseData.setInvoiceId(invoiceId);
        caseData.setTag(caseVo.getTag());
        caseData.setCreateName(tokenUser.getUserName());
        caseData.setCreateNum(tokenUser.getJobNumber());
        caseData.setCreateTime(nowDate);
        caseMapper.insert(caseData);
        //新增CASE明细数据
        caseItemMapper.insertBatch(caseItemList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(JSONObject map) throws ValidateException {
        JSONArray array = map.getJSONArray("data");
        List<String> aidList = new ArrayList<>();
        for (Object order : array) {
            JSONObject orderJson = (JSONObject) order;
            String purchaseOrder = orderJson.getStr("purchaseOrder");
            String marketplace = orderJson.getStr("marketplace");
            if (StrUtil.isEmpty(purchaseOrder)) {
                throw new ValidateException("purchaseOrder不能数为空");
            }
            if (StrUtil.isEmpty(marketplace)) {
                throw new ValidateException("marketplace不能数为空");
            }
            List<String> orderList = somVcAmazonPurchaseOrderMapper.createLambdaQuery().andEq("marketplace", marketplace)
                    .andEq("purchase_order", purchaseOrder)
                    .andEq("process_state", 10)//只删除Undisposed的数据
                    .select("aid")
                    .stream().map(x -> x.getAid())
                    .collect(Collectors.toList());
            aidList.addAll(orderList);
        }
        if (aidList.isEmpty()) {
            throw new ValidateException("没有找到相关PO数据,请刷新界面重试");
        }
        //删除order数据
        somVcAmazonPurchaseOrderMapper.createLambdaQuery().andIn("aid", aidList).delete();
        List<String> invoiceIdList = orderInvoiceMapper.createLambdaQuery().andIn("purchase_id", aidList)
                .select("aid")
                .stream()
                .map(x -> x.getAid())
                .collect(Collectors.toList());
        if (invoiceIdList.isEmpty()) {
            return;
        }
        //删除invoice数据
        orderInvoiceMapper.createLambdaQuery().andIn("aid", invoiceIdList).delete();

        List<String> caseAidList = caseMapper.createLambdaQuery().andIn("invoice_id", invoiceIdList).select("aid")
                .stream()
                .map(x -> x.getAid())
                .collect(Collectors.toList());
        if (!caseAidList.isEmpty()) {
            //删除case数据
            caseMapper.createLambdaQuery().andIn("aid", caseAidList).delete();
            //删除case明细数据
            caseItemMapper.createLambdaQuery().andIn("case_id", caseAidList).delete();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void editTag(SomVcAmazonPurchaseOrderCaseVo caseVo) throws ValidateException {
        if (caseVo == null || caseVo.getAid() == null || caseVo.getTag() == null) {
            throw new ValidateException("数据不能为空");
        }
        SomVcAmazonPurchaseOrderCase orderCase = caseMapper.single(caseVo.getAid());
        orderCase.setTag(caseVo.getTag());
        caseMapper.updateById(orderCase);

        if (caseVo.isBoth()) {
            String invoiceId = orderCase.getInvoiceId();
            SomVcAmazonPurchaseOrderInvoice invoice = orderInvoiceMapper.single(invoiceId);
            SomVcAmazonPurchaseOrder order = somVcAmazonPurchaseOrderMapper.single(invoice.getPurchaseId());
            order.setProcessState(caseVo.getTag());
            somVcAmazonPurchaseOrderMapper.updateById(order);
        }
    }
}
