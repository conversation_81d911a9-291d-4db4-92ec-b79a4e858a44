package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.zielsmart.mc.repository.mapper.SomFacebookMetaAdsReportMapper;
import com.zielsmart.mc.vo.SomFacebookMetaAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomFacebookMetaAdsReportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomFacebookMetaAdsReportService {
    
    @Resource
    private SomFacebookMetaAdsReportMapper somFacebookMetaAdsReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomFacebookMetaAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomFacebookMetaAdsReportVo> queryByPage(SomFacebookMetaAdsReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomFacebookMetaAdsReportVo> pageResult = dynamicSqlManager.getMapper(SomFacebookMetaAdsReportMapper.class).queryByPage(searchVo, pageRequest);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        for (SomFacebookMetaAdsReportVo reportVo : pageResult.getList()) {
            reportVo.setBeginEndDate((reportVo.getDateStart()==null?"":sf.format(reportVo.getDateStart())) + "~" + (reportVo.getDateStop()==null?"":sf.format(reportVo.getDateStop())));
        }
        return ConvertUtils.pageConvert(pageResult, SomFacebookMetaAdsReportVo.class, searchVo);
    }

    public String export(SomFacebookMetaAdsReportPageSearchVo searchVo) throws ValidateException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomFacebookMetaAdsReportVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            try {
                Workbook workbook = null;
                ExportParams params = new ExportParams(null, "Facebook Meta站外广告");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomFacebookMetaAdsReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
