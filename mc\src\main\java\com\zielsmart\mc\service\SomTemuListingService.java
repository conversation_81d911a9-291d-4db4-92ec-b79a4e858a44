package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomTemuListingService {
    @Resource
    private SomTemuWarehouseMapper somTemuWarehouseMapper;
    @Value("${aws.s3china.bucket}")
    private String bucketName;
    @Autowired
    @Qualifier("amazonS3China")
    private AmazonS3 amazonS3;
    @Resource
    private S3FileService s3FileService;
    @Resource
    private SomTemuManualQuantityMapper somTemuManualQuantityMapper;
    @Resource
    private McDictionaryInfoMapper dictMapper;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private SomTemuListingMapper somTemuListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private SomTemuWarehouseConfigMapper somTemuWarehouseConfigMapper;
    @Resource
    private SomStorageLocationMapper storageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private SomTemuFbaBlackListMapper somTemuFbaBlackListMapper;
    @Resource
    private SomTemuClearanceListMapper somTemuClearanceListMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuListingVo> queryByPage(SomTemuListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuListingVo> pageResult = dynamicSqlManager.getMapper(SomTemuListingMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomTemuListingVo.class, searchVo);
    }

    public String export(SomTemuListingPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomTemuListingReport> records = stockReport(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "拼多多平台Listing管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuListingReport.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public String exportTemuImageUrl(SomTemuListingPageSearchVo searchVo) throws ValidateException {
        List<SomTemuImageUrlReport> records = somTemuListingMapper.getTemuImageUrlData(searchVo);
        List<String> skus = records.stream().map(record -> record.getProductMainCode()).collect(Collectors.toList());

        Map<String, SomTemuImageUrlReport> skuMap = new HashMap<>();
        for (SomTemuImageUrlReport record : records) {
            skuMap.put(record.getProductMainCode(), record);
        }

        List<String> sites = new ArrayList<>();
        if (StrUtil.isNotBlank(searchVo.getSite())) {
            sites = Arrays.asList(searchVo.getSite());
        } else {
            List<McDictionaryInfo> codes = dictMapper.createLambdaQuery().andEq("item_type_code", "Temu").select();
            sites = codes.stream().map(code -> code.getItemValue()).collect(Collectors.toList());
        }

        McDictionaryInfo urlDict = dictMapper.createLambdaQuery().andEq("item_type_code", "ChinaZlccImageUrl").single();
        String imageUrl = urlDict.getItemValue();

        ListObjectsV2Request request = new ListObjectsV2Request().withBucketName(bucketName).withPrefix("som/TemuProductImage/");
        ListObjectsV2Result response = amazonS3.listObjectsV2(request);

        List<SomTemuImageUrlReport> reports = new ArrayList<>();

        do {
            response = amazonS3.listObjectsV2(request);
            for (S3ObjectSummary objectSummary : response.getObjectSummaries()) {
                if (isPicture(objectSummary.getKey())) {
                    String[] parts = objectSummary.getKey().split("/");
                    String sku = parts[2];
                    String platform = "temu";
                    String site = parts[3];
                    String imgName = parts[5];
                    String imgUrl = generatePresignedUrl(imageUrl, bucketName, objectSummary.getKey());
                    if (sites.contains(site)) {
                        SomTemuImageUrlReport report = new SomTemuImageUrlReport();
                        report.setPlatform(platform);
                        report.setSite(site);
                        report.setProductMainCode(sku);
                        if (skus.contains(sku)) {
                            report.setSellerSku(skuMap.get(sku).getSellerSku());
                        } else {
                            report.setSellerSku("");
                        }
                        report.setImgName(imgName);
                        report.setImgUrl(imgUrl);
                        reports.add(report);
                    }
                }
            }
            // 处理分页
            request.setContinuationToken(response.getNextContinuationToken());
        } while (response.isTruncated());

        if (!reports.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "TEMU图片URL");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuImageUrlReport.class, reports);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    private boolean isPicture(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            Set<String> imagesExtensions = new HashSet<>();
            imagesExtensions.add("png");
            imagesExtensions.add("jpg");
            imagesExtensions.add("jpeg");
            imagesExtensions.add("bmp");
            imagesExtensions.add("gif");
            imagesExtensions.add("ico");
            imagesExtensions.add("tiff");
            imagesExtensions.add("tif");
            imagesExtensions.add("raw");
            String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
            return imagesExtensions.contains(fileExtension);
        }
        return false;
    }

    private String generatePresignedUrl(String imageUrl, String bucketName, String objectKey) {
        String url = imageUrl + bucketName + "/" + objectKey;
        url = url.replace(" ", "%20");
        return url;
    }

    public PageVo<SomTemuListingReport> stockReport(SomTemuListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuListingReport> pageResult = somTemuListingMapper.stockReport(searchVo, pageRequest);
//        if (!pageResult.getList().isEmpty()) {
//            List<SomStorageLocation> storageLocationList = storageLocationMapper.all();
//            Map<String, String> slMap = storageLocationList.stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
//            Map<String, String> whMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
//            // 获取Temu仓库配置
//            List<SomTemuWarehouseConfig> warehouseList = somTemuWarehouseConfigMapper.all();
//
//            //查询所有的Temu后台的仓库
//            List<SomTemuWarehouse> temuWarehouseList = somTemuWarehouseMapper.all();
//            Map<String, String> warehouseNameMap = temuWarehouseList.stream().collect(Collectors.toMap(x -> x.getWarehouseId(), v -> v.getWarehouseName(), (x1, x2) -> x1));
//
//            Set<String> allTemuWarehouseCode = warehouseList.stream().map(SomTemuWarehouseConfig::getUseableWarehouseCode).collect(Collectors.toSet());
//            Map<String, List<SomTemuWarehouseConfig>> warehouseMap = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getAccountId()+e.getSite()));
//            //查询跨境 香港账号配置的Temu仓库ID
//            Map<String, String> temuWarehouseMap = warehouseList.stream().filter(x -> x.getTemuWarehouseId() != null).collect(Collectors.toMap(x -> x.getAccountId() + x.getSite(), v -> warehouseNameMap.get(v.getTemuWarehouseId()), (x1, x2) -> x1));
//
//            // 获取Temu清仓配置
//            List<SomTemuClearanceList> clearanceLists = somTemuClearanceListMapper.all();
//            List<String> clearanceKeys = clearanceLists.stream().map(data -> data.getAccountId() + data.getProductSkuId()).collect(Collectors.toList());
//            // 获取人工指定发货方式配置
//            List<SomTemuFbaBlackList> allBlackLists = somTemuFbaBlackListMapper.createLambdaQuery().andEq("platform","Temu").andEq("delete_flag", 10).select();
//            // 获取所有仓库编码去重
//            List<String> warehouseCodes = allBlackLists.stream().flatMap(e ->{
//                String useableWarehouseCode = e.getUseableWarehouseStorage();
//                if (StrUtil.isBlank(useableWarehouseCode) || useableWarehouseCode.length()<3) {
//                    return Stream.empty();
//                }
//                JSONArray array = JSONUtil.parseArray(useableWarehouseCode);
//                List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
//                return useableWarehouseList.stream().map(SomTemuFbaBlackListVo.UseableWarehouse::getWarehouseCode);
//            }).distinct().collect(Collectors.toList());
//            allTemuWarehouseCode.addAll(warehouseCodes);
//            Map<String, SomTemuFbaBlackList> blackListMap = allBlackLists.stream().collect(Collectors.toMap(f ->f.getAccountId() + f.getPlatform() + f.getSite() + f.getSellerSku(), Function.identity(), (x1, x2) -> x1));
//            // 获取 Temu 所有仓库的库存
//            List<McStockInfo> stockList = mcStockInfoMapper.createLambdaQuery().andIn("warehouse_code", allTemuWarehouseCode).select();
//            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
//
//            for (SomTemuListingReport report : pageResult.getList()) {
//                //设置temu仓库名称
//                report.setTemuWarehouseName(temuWarehouseMap.get(report.getAccountId() + report.getSite()));
//
//                // 判断是否是清仓商品
//                boolean isClearance = clearanceKeys.contains(report.getAccountId() + report.getProductSkuId());
//                report.setClearanceFlag(isClearance);
//                //判断展示码是否存在人工指定发货方式配置，如果存在，可售库存按照配置的发货仓库&库区展示库存
//                //2024.8.19调整 美线不需要查人工指定发货方式
//                //2024.10.31调整 香港 美国 欧洲都需要人工指定发货方式
//                if (blackListMap.containsKey(report.getAccountId() + report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
//                    SomTemuFbaBlackList blackList = blackListMap.get(report.getAccountId() +report.getPlatform() + report.getSite() + report.getDisplayProductCode());
//                    if (StrUtil.isNotBlank(blackList.getUseableWarehouseStorage())) {
//                        JSONArray array = JSONUtil.parseArray(blackList.getUseableWarehouseStorage());
//                        List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
//                        List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
//                        int totalStock = 0;
//                        for (SomTemuFbaBlackListVo.UseableWarehouse warehouse : useableWarehouseList) {
//                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
//                            warehouseInventory.setWarehouseName(whMap.get(warehouse.getWarehouseCode()));
//                            warehouseInventory.setSlName(slMap.get(warehouse.getSlCode()));
//                            McStockInfo mcStockInfo = stockMap.get(warehouse.getWarehouseCode() + warehouse.getSlCode() + report.getProductMainCode());
//                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
//                            totalStock += stock;
//                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
//                            list.add(warehouseInventory);
//                        }
//                        // 如果清仓产品中包含此产品，则不需要减去安全库存
//                        if (isClearance) {
//                            report.setStock(Math.max(totalStock, 0));
//                        } else {
//                            report.setStock(Math.max(totalStock - (report.getSafetyStock() == null ? 0 : report.getSafetyStock()), 0));
//                        }
//                        report.setList(list);
//                    }
//                } else {
//                    //根据站点获取仓库 库区
//                    List<SomTemuWarehouseConfig> somTemuWarehouseConfigs = warehouseMap.get(report.getAccountId() + report.getSite());
//                    List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
//                    if (null != somTemuWarehouseConfigs && !somTemuWarehouseConfigs.isEmpty()) {
//                        BigDecimal totalStock = BigDecimal.ZERO;
//                        for (SomTemuWarehouseConfig config : somTemuWarehouseConfigs) {
//                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
//                            warehouseInventory.setWarehouseName(whMap.get(config.getUseableWarehouseCode()));
//                            warehouseInventory.setSlName(slMap.get(config.getUseableStorageCode()));
//                            McStockInfo mcStockInfo = stockMap.get(config.getUseableWarehouseCode() + config.getUseableStorageCode() + report.getProductMainCode());
//                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
//                            totalStock = totalStock.add(BigDecimal.valueOf(stock));
//                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
//                            list.add(warehouseInventory);
//                        }
//                        // 如果清仓产品中包含此产品，则不需要减去安全库存
//                        if (isClearance) {
//                            report.setStock(Math.max(totalStock.intValue(), 0));
//                        } else {
//                            report.setStock(Math.max(totalStock.subtract(Optional.ofNullable(report.getSafetyStock()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO)).intValue(), 0));
//                        }
//                        report.setList(list);
//                    }
//                }
//
////                if (!StrUtil.equals("Temu.us", report.getSite())) {
////                    if (blackListMap.containsKey(report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
////                        SomTemuFbaBlackList blackList = blackListMap.get(report.getPlatform() + report.getSite() + report.getDisplayProductCode());
////                        if (StrUtil.isNotBlank(blackList.getUseableWarehouseStorage())) {
////                            JSONArray array = JSONUtil.parseArray(blackList.getUseableWarehouseStorage());
////                            List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
////                            List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
////                            Integer totalStock = 0;
////                            for (SomTemuFbaBlackListVo.UseableWarehouse warehouse : useableWarehouseList) {
////                                SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
////                                warehouseInventory.setWarehouseName(whMap.get(warehouse.getWarehouseCode()));
////                                warehouseInventory.setSlName(slMap.get(warehouse.getSlCode()));
////                                McStockInfo mcStockInfo = stockMap.get(warehouse.getWarehouseCode() + warehouse.getSlCode() + report.getProductMainCode());
////                                Integer stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
////                                totalStock += stock;
////                                warehouseInventory.setStock(BigDecimal.valueOf(stock));
////                                list.add(warehouseInventory);
////                            }
////                            report.setStock(Math.max(totalStock - (report.getSafetyStock() == null ? 0 : report.getSafetyStock()), 0));
////                            report.setList(list);
////                        }
////                    } else {
////                        //根据站点获取仓库 库区
////                        List<SomTemuWarehouseConfig> somTemuWarehouseConfigs = warehouseMap.get(report.getSite());
////                        List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
////                        if (null != somTemuWarehouseConfigs && !somTemuWarehouseConfigs.isEmpty()) {
////                            BigDecimal totalStock = BigDecimal.ZERO;
////                            for (SomTemuWarehouseConfig config : somTemuWarehouseConfigs) {
////                                SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
////                                warehouseInventory.setWarehouseName(whMap.get(config.getUseableWarehouseCode()));
////                                warehouseInventory.setSlName(slMap.get(config.getUseableStorageCode()));
////                                McStockInfo mcStockInfo = stockMap.get(config.getUseableWarehouseCode() + config.getUseableStorageCode() + report.getProductMainCode());
////                                Integer stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
////                                if (stock == null) {
////                                    stock = 0;
////                                }
////                                totalStock = totalStock.add(BigDecimal.valueOf(stock));
////                                warehouseInventory.setStock(BigDecimal.valueOf(stock));
////                                list.add(warehouseInventory);
////                            }
////                            totalStock = totalStock.subtract(Optional.ofNullable(report.getSafetyStock()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
////                            report.setStock(totalStock.intValue());
////                            report.setList(list);
////                        }
////                    }
////                }
////                else {
////                    //根据站点获取仓库 库区
////                    List<SomTemuWarehouseConfig> somTemuWarehouseConfigs = warehouseMap.get(report.getSite());
////                    List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
////                    if (null != somTemuWarehouseConfigs && !somTemuWarehouseConfigs.isEmpty()) {
////                        BigDecimal totalStock = BigDecimal.ZERO;
////                        for (SomTemuWarehouseConfig config : somTemuWarehouseConfigs) {
////                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
////                            warehouseInventory.setWarehouseName(whMap.get(config.getUseableWarehouseCode()));
////                            warehouseInventory.setSlName(slMap.get(config.getUseableStorageCode()));
////                            McStockInfo mcStockInfo = stockMap.get(config.getUseableWarehouseCode() + config.getUseableStorageCode() + report.getProductMainCode());
////                            Integer stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
////                            if (stock == null) {
////                                stock = 0;
////                            }
////                            totalStock = totalStock.add(BigDecimal.valueOf(stock));
////                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
////                            list.add(warehouseInventory);
////                        }
////                        totalStock = totalStock.subtract(Optional.ofNullable(report.getSafetyStock()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
////                        report.setStock(totalStock.intValue());
////                        report.setList(list);
////                    }
////                }
//                report.setStock(report.getStock() == null ? 0 : report.getStock());
//                if (report.getStock() < 0) {
//                    report.setStock(0);
//                }
//
//                //根据站点 展示码查询 人工指定库存表
//                //详情添加 FBT待发库存  VC可供应  SC可供应
//                //欧洲站点不需要统计 “FBT待发库存” + “VC可供应” + “SC可供应”
//                //2024.8.5修改为美国站点也不需要统计“FBT待发库存” + “VC可供应” + “SC可供应”
//                //2024.10.31 去掉  “FBT待发库存” + “VC可供应” + “SC可供应”
////                if (!StrUtil.equals("Temu.us", report.getSite())) {
////                    if (blackListMap.containsKey(report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
////                        Integer totalStock = 0;
////                        SomTemuFbaBlackList blackList = blackListMap.get(report.getPlatform() + report.getSite() + report.getDisplayProductCode());
////                        if (StrUtil.isNotBlank(blackList.getUseableWarehouseStorage())) {
////                            JSONArray array =  JSONUtil.parseArray(blackList.getUseableWarehouseStorage());
////                            List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
////                            for (SomTemuFbaBlackListVo.UseableWarehouse warehouse : useableWarehouseList) {
////                                McStockInfo mcStockInfo = stockMap.get(warehouse.getWarehouseCode() + warehouse.getSlCode() + report.getProductMainCode());
////                                Integer stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
////                                totalStock += stock;
////                            }
////                        }
////                        report.setStock(Math.max(totalStock - (report.getSafetyStock() == null ? 0 : report.getSafetyStock()), 0));
////                    } else {
////                        report.setStock(report.getStock());
////                    }
////                }
//            }
//        }
        return ConvertUtils.pageConvert(pageResult, SomTemuListingReport.class, searchVo);
    }

    private int formatInteger(Integer integer) {
        if (integer == null) {
            return 0;
        }
        return integer.intValue();
    }

    private BigDecimal formatBigdecial(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }
}
