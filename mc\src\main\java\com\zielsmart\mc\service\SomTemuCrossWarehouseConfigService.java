package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Temu跨境可售仓库配置管理
 * @date 2025-06-27 11:34:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuCrossWarehouseConfigService {
    
    @Resource
    private SomTemuCrossWarehouseConfigMapper somTemuCrossWarehouseConfigMapper;

    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private SomTemuWarehouseMapper somTemuWarehouseMapper;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return PageVo<SomTemuCrossWarehouseConfigVo>
     */
    public PageVo<SomTemuCrossWarehouseConfigVo> queryByPage(SomTemuCrossWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuCrossWarehouseConfigVo> pageResult = somTemuCrossWarehouseConfigMapper.queryByPage(searchVo, pageRequest);
        List<SomTemuCrossWarehouseConfigVo> localWarehouseConfigVos = pageResult.getList();
        // 处理数据
        handleSomTemuCrossWarehouseConfig(localWarehouseConfigVos);
        return ConvertUtils.pageConvert(pageResult, SomTemuCrossWarehouseConfigVo.class, searchVo);
    }

    /**
     * 新增
     *
     * @param crossWarehouseConfigVo 入参
     * @param tokenUser              当前登录用户
     */
    public void save(SomTemuCrossWarehouseConfigVo crossWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(crossWarehouseConfigVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 核验新增和更新入参
        checkSaveOrUpdateParam(crossWarehouseConfigVo);
        // 判断是否存在了可售仓库配置
        String accountId = crossWarehouseConfigVo.getAccountId();
        String site = crossWarehouseConfigVo.getSite();
        List<SomTemuCrossWarehouseConfig> warehouseConfigs = somTemuCrossWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("account_id", accountId)
                .select();
        List<String> temuWarehouseIds = warehouseConfigs.stream().map(SomTemuCrossWarehouseConfig::getTemuWarehouseId).collect(Collectors.toList());
        String temuWarehouseId = crossWarehouseConfigVo.getTemuWarehouseId();
        if (temuWarehouseIds.contains(temuWarehouseId)) {
            String errorMsg = StrUtil.isEmpty(temuWarehouseId) ? "此店铺、站点已维护了可售仓库！" : "此店铺、站点、平台仓库已维护了可售仓库！";
            throw new ValidateException(errorMsg);
        }
        // 特殊逻辑处理：数据库存在默认的(即平台仓库为空)。如果用户输入的有平台仓库不为空，提示用户：店铺、站点已经配置了默认推送库存方式，不允许重复配置
        // 数据库不存在默认(平台仓库为空)的。如果用户输入的有的平台仓库为空，提示用户：店铺、站点已经配置了按照平台仓的方式推送库存，不允许按照默认仓库推送库存
        if (CollUtil.isNotEmpty(warehouseConfigs)) {
            if (StrUtil.isEmpty(temuWarehouseId) && !temuWarehouseIds.contains(null)) {
                throw new ValidateException("此店铺、站点已经配置了按照平台仓的方式推送库存，不允许按照默认仓库推送库存！");
            }
            if (StrUtil.isNotEmpty(temuWarehouseId) && temuWarehouseIds.contains(null)) {
                throw new ValidateException("此店铺、站点已经配置了默认推送库存方式，不允许按照平台仓的方式推送库存！");
            }
        }
        // 整合数据入库
        List<SomTemuCrossWarehouseConfig> crossWarehouseConfigs = buildCrossWarehouseConfigs(crossWarehouseConfigVo, tokenUser);
        somTemuCrossWarehouseConfigMapper.insertBatch(crossWarehouseConfigs);
    }

    /**
     * 修改
     *
     * @param crossWarehouseConfigVo 入参
     * @param tokenUser              当前登录用户
     * @throws ValidateException 核验exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomTemuCrossWarehouseConfigVo crossWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        // 核验入参
        checkSaveOrUpdateParam(crossWarehouseConfigVo);
        // 处理数据
        List<SomTemuCrossWarehouseConfig> crossWarehouseConfigs = buildCrossWarehouseConfigs(crossWarehouseConfigVo, tokenUser);
        // 刪除旧数据
        String site = crossWarehouseConfigVo.getSite();
        String accountId = crossWarehouseConfigVo.getAccountId();
        String temuWarehouseId = crossWarehouseConfigVo.getTemuWarehouseId();
        LambdaQuery<SomTemuCrossWarehouseConfig> queryLambda = somTemuCrossWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("account_id", accountId);
        if (StrUtil.isEmpty(temuWarehouseId)) {
            queryLambda.andIsNull("temu_warehouse_id");
        } else {
            queryLambda.andEq("temu_warehouse_id", temuWarehouseId);
        }
        queryLambda.delete();
        // 新增新数据
        somTemuCrossWarehouseConfigMapper.insertBatch(crossWarehouseConfigs);
    }

    /**
     * 删除
     *
     * @param crossWarehouseConfigVo 入参
     */
    public void delete(SomTemuCrossWarehouseConfigVo crossWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(crossWarehouseConfigVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        String site = crossWarehouseConfigVo.getSite();
        String accountId = crossWarehouseConfigVo.getAccountId();
        if (StrUtil.isEmpty(accountId) || StrUtil.isEmpty(site)) {
            throw new ValidateException("请选择要删除的数据");
        }
        LambdaQuery<SomTemuCrossWarehouseConfig> lambdaQuery = somTemuCrossWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("account_id", accountId);
        String temuWarehouseId = crossWarehouseConfigVo.getTemuWarehouseId();
        if (StrUtil.isEmpty(temuWarehouseId)) {
            lambdaQuery.andIsNull("temu_warehouse_id");
        } else {
            lambdaQuery.andEq("temu_warehouse_id", temuWarehouseId);
        }
        lambdaQuery.delete();
    }

    /**
     * 处理数据
     *
     * @param crossWarehouseConfigVos 仓库配置
     */
    private void handleSomTemuCrossWarehouseConfig(List<SomTemuCrossWarehouseConfigVo> crossWarehouseConfigVos) {
        if (CollUtil.isEmpty(crossWarehouseConfigVos)) {
            return;
        }
        // 查询所有的数据
        List<String> sites = crossWarehouseConfigVos.stream().map(SomTemuCrossWarehouseConfigVo::getSite).distinct().collect(Collectors.toList());
        List<SomTemuCrossWarehouseConfig> warehouseConfigs = somTemuCrossWarehouseConfigMapper.createLambdaQuery().andIn("site", sites).select();
        Map<String, List<SomTemuCrossWarehouseConfig>> warehouseConfigsMap = warehouseConfigs.stream()
                .collect(Collectors.groupingBy(x -> StrUtil.concat(true, x.getSite(), x.getAccountId(), x.getTemuWarehouseId()), Collectors.toList()));
        // 库区名称映射，key:StorageCode,value:StorageName
        Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
        // Temu 店铺映射
        Map<String, String> temuAccountMap = queryCrossAccount().stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
        // 仓库名称映射，key:WarehouseCode,value:WarehouseName
        Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
        // Temu 平台仓库映射
        Map<String, String> temuWarehouseMap = somTemuWarehouseMapper.all().stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getSite() +  x.getWarehouseId(), SomTemuWarehouse::getWarehouseName, (x1, x2) -> x1));
         crossWarehouseConfigVos.forEach(config -> {
             String key = StrUtil.concat(true, config.getSite(), config.getAccountId(), config.getTemuWarehouseId());
            List<SomTemuCrossWarehouseConfig> configs = warehouseConfigsMap.get(key);
            if (CollUtil.isNotEmpty(configs)) {
                List<String> warehouseNameList = new ArrayList<>();
                List<SomTemuCrossWarehouseConfigVo.UseableWarehouse> useableWarehouses = configs.stream().map(x -> {
                    SomTemuCrossWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTemuCrossWarehouseConfigVo.UseableWarehouse();
                    useableWarehouse.setAid(x.getAid());
                    useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                    useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                    warehouseNameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                    return useableWarehouse;
                }).collect(Collectors.toList());
                config.setWarehouseNameList(warehouseNameList);
                config.setList(useableWarehouses);
                config.setAccountName(temuAccountMap.get(config.getAccountId()));
                if (StrUtil.isNotEmpty(config.getTemuWarehouseId())) {
                    String temuWarehouseKey = config.getAccountId() + config.getSite() + config.getTemuWarehouseId();
                    config.setTemuWarehouseName(temuWarehouseMap.getOrDefault(temuWarehouseKey, "Temu平台仓库未匹配到！"));
                }
            }
        });
    }

    /**
     * 获取跨境的账号信息
     *
     * @return List<McDictionaryInfo>
     */
    private List<McDictionaryInfo> queryCrossAccount() {
        // 获取字典里面的 TemuAccount，有效:item_value4=1，跨境:item_value8=跨境
        return mcDictionaryInfoMapper.createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .andEq("item_value8", "跨境")
                .select();
    }

    /**
     * 构建 SomTemuCrossWarehouseConfig 集合
     *
     * @param crossWarehouseConfigVo 入参
     * @param tokenUser              当前登录
     * @return List<SomTemuCrossWarehouseConfig>
     */
    private List<SomTemuCrossWarehouseConfig> buildCrossWarehouseConfigs(SomTemuCrossWarehouseConfigVo crossWarehouseConfigVo, TokenUserInfo tokenUser) {
        List<SomTemuCrossWarehouseConfig> crossWarehouseConfigs = new ArrayList<>();
        // 整合数据
        Date now = DateTime.now().toJdkDate();
        List<SomTemuCrossWarehouseConfigVo.UseableWarehouse> useableWarehouses = crossWarehouseConfigVo.getList();
        for (SomTemuCrossWarehouseConfigVo.UseableWarehouse useableWarehouse : useableWarehouses) {
            SomTemuCrossWarehouseConfig crossWarehouseConfig = new SomTemuCrossWarehouseConfig();
            crossWarehouseConfig.setAid(IdUtil.fastSimpleUUID());
            crossWarehouseConfig.setAccountId(crossWarehouseConfigVo.getAccountId());
            crossWarehouseConfig.setSite(crossWarehouseConfigVo.getSite());
            crossWarehouseConfig.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            crossWarehouseConfig.setTemuWarehouseId(crossWarehouseConfigVo.getTemuWarehouseId());
            crossWarehouseConfig.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            crossWarehouseConfig.setLastModifyName(tokenUser.getUserName());
            crossWarehouseConfig.setLastModifyNum(tokenUser.getJobNumber());
            crossWarehouseConfig.setLastModifyTime(now);
            crossWarehouseConfig.setCreateName(tokenUser.getUserName());
            crossWarehouseConfig.setCreateNum(tokenUser.getJobNumber());
            crossWarehouseConfig.setCreateTime(now);
            crossWarehouseConfigs.add(crossWarehouseConfig);
        }
        return crossWarehouseConfigs;
    }

    /**
     * 核验新增和更新入参
     *
     * @param crossWarehouseConfigVo 入参
     * @throws ValidateException ex
     */
    private void checkSaveOrUpdateParam(SomTemuCrossWarehouseConfigVo crossWarehouseConfigVo) throws ValidateException {
        List<SomTemuCrossWarehouseConfigVo.UseableWarehouse> useableWarehouses = crossWarehouseConfigVo.getList();
        String accountId = crossWarehouseConfigVo.getAccountId();
        String site = crossWarehouseConfigVo.getSite();
        if (StrUtil.isEmpty(accountId) || StrUtil.isEmpty(site) || CollUtil.isEmpty(useableWarehouses)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 核验店铺ID是否正确,itemValue3中存储的是店铺ID
        List<String> accountIds = queryCrossAccount().stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        if (!accountIds.contains(accountId)) {
            throw new ValidateException("店铺已删除或已被注销！");
        }
        // 核验平台仓库
        String temuWarehouseId = crossWarehouseConfigVo.getTemuWarehouseId();
        checkTemuWarehouseExist(accountId, site, temuWarehouseId);
    }

    /**
     * 核验 Temu 平台仓库是否存在
     *
     * @param temuWarehouseId Temu仓库ID
     */
    private void checkTemuWarehouseExist(String accountId, String site, String temuWarehouseId) throws ValidateException {
        if (StrUtil.isEmpty(temuWarehouseId)) {
            return;
        }
        SomTemuWarehouse temuWarehouse = somTemuWarehouseMapper.createLambdaQuery()
                .andEq(SomTemuWarehouse::getAccountId, accountId)
                .andEq(SomTemuWarehouse::getSite, site)
                .andEq(SomTemuWarehouse::getWarehouseId, temuWarehouseId)
                .single();
        if (temuWarehouse == null) {
            throw new ValidateException("Temu平台仓库不存在/已删除，请检查数据！");
        }
    }
}
