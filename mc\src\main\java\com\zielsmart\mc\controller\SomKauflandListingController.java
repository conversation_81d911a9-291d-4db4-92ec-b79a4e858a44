package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomKauflandListingService;
import com.zielsmart.mc.vo.SomKauflandListingExtVo;
import com.zielsmart.mc.vo.SomKauflandListingPageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomKauflandListingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somKauflandListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Kaufland Listing信息表管理")
public class SomKauflandListingController extends BasicController {

    @Resource
    SomKauflandListingService somKauflandListingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomKauflandListingExtVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomKauflandListingExtVo>> queryByPage(@RequestBody SomKauflandListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somKauflandListingService.queryByPage(searchVo));
    }
}
