package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPushDeliveryTemplateRecord;
import com.zielsmart.mc.vo.SomPushDeliveryTemplateRecordPageSearchVo;
import com.zielsmart.mc.vo.SomPushDeliveryTemplateRecordVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-02-16
 */

@SqlResource("somPushDeliveryTemplateRecord")
public interface SomPushDeliveryTemplateRecordMapper extends BaseMapper<SomPushDeliveryTemplateRecord> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPushDeliveryTemplateRecordVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPushDeliveryTemplateRecordVo> queryByPage(@Param("searchVo") SomPushDeliveryTemplateRecordPageSearchVo searchVo, PageRequest pageRequest);
}
