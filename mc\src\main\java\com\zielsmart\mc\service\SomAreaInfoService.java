package com.zielsmart.mc.service;


import com.zielsmart.mc.repository.entity.SomAreaInfo;
import com.zielsmart.mc.repository.mapper.SomAreaInfoMapper;
import com.zielsmart.mc.vo.SomAreaInfoPageSearchVo;
import com.zielsmart.mc.vo.SomAreaInfoVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-07-29 12:09:41
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomAreaInfoService {
    
    @Resource
    private SomAreaInfoMapper somAreaInfoMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByLevel
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomAreaInfoVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomAreaInfoVo> queryByLevel(SomAreaInfoPageSearchVo searchVo) {
        List<SomAreaInfo> resultList = somAreaInfoMapper.createLambdaQuery().andEq("area_level",searchVo.getAreaLevel()).select();
        return ConvertUtils.listConvert(resultList,SomAreaInfoVo.class);
    }
}
