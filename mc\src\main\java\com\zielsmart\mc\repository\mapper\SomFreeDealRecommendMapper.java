package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomFreeDealRecommend;
import com.zielsmart.mc.vo.SomFreeDealRecommendItemVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendPageSearchVo;
import com.zielsmart.mc.vo.SomFreeDealRecommendVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-10-25
 */

@SqlResource("somFreeDealRecommend")
public interface SomFreeDealRecommendMapper extends BaseMapper<SomFreeDealRecommend> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomFreeDealRecommendVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomFreeDealRecommendVo> queryByPage(@Param("searchVo") SomFreeDealRecommendPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryBySiteAndDealType
     * 通过站点和类型查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomFreeDealRecommendVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomFreeDealRecommendItemVo> queryBySiteAndDealType(@Param("searchVo") SomFreeDealRecommendPageSearchVo searchVo, PageRequest pageRequest);
}
