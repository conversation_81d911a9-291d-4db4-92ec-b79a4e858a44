package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomEmailAlertPageSearchVo;
import com.zielsmart.mc.vo.SomEmailAlertVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-02-15
*/

@SqlResource("somEmailAlert")
public interface SomEmailAlertMapper extends BaseMapper<SomEmailAlert> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomEmailAlertVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomEmailAlertVo> queryByPage(@Param("searchVo") SomEmailAlertPageSearchVo searchVo, PageRequest pageRequest);
}
