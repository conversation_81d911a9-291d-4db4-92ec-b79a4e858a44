package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomLeroymerlinOffers;
import com.zielsmart.mc.vo.SomLeroymerlinOffersPageSearchVo;
import com.zielsmart.mc.vo.SomLeroymerlinOffersVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-08
*/

@SqlResource("somLeroymerlinOffers")
public interface SomLeroymerlinOffersMapper extends BaseMapper<SomLeroymerlinOffers> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomLeroymerlinOffersVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomLeroymerlinOffersVo> queryByPage(@Param("searchVo")SomLeroymerlinOffersPageSearchVo searchVo, PageRequest pageRequest);

    List<SomLeroymerlinOffersVo> exportExcel(@Param("searchVo") SomLeroymerlinOffersPageSearchVo searchVo);

    List<SomLeroymerlinOffers> allLeroymerlinListing();

    default void updateBatch(@Param("updateList") List<SomLeroymerlinOffers> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somLeroymerlinOffers.updateBatch"), updateList);
    }
}
