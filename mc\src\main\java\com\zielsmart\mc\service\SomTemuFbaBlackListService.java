package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomStorageLocation;
import com.zielsmart.mc.repository.entity.SomTemuFbaBlackList;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.repository.mapper.SomTemuFbaBlackListMapper;
import com.zielsmart.mc.vo.SomTemuFbaBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuFbaBlackListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTemuFbaBlackListService
 * @description
 * @date 2024-07-04 11:41:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuFbaBlackListService {

    @Resource
    private SomTemuFbaBlackListMapper somTemuFbaBlackListMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo<SomTemuFbaBlackListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuFbaBlackListVo> queryByPage(SomTemuFbaBlackListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            String keyWord = searchVo.getKeyWord();
            //去除空格
            keyWord = keyWord.replaceAll(" ", "");
            //去除换行符
            keyWord = keyWord.replaceAll("\\n", "");
            //转换逗号
            keyWord = keyWord.replaceAll("，", ",");
            //去除制表符
            keyWord = keyWord.replaceAll("\\t", "");
            searchVo.setSellerSkuList(Arrays.asList(keyWord.split(",")));
        }
        // 查询需要展示的店铺ID
        List<McDictionaryInfo> accounts = this.queryNeedShowAccounts();
        List<String> accountIds = accounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        searchVo.setAccountIds(accountIds);
        PageResult<SomTemuFbaBlackListVo> pageResult = dynamicSqlManager.getMapper(SomTemuFbaBlackListMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));
            Map<String, String> accountMap = accounts.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            for (SomTemuFbaBlackListVo vo : pageResult.getList()) {
                if (StrUtil.isNotBlank(vo.getUseableWarehouseStorage())) {
                    JSONArray array = JSONUtil.parseArray(vo.getUseableWarehouseStorage());
                    List<SomTemuFbaBlackListVo.UseableWarehouse> list = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
                    List<String> nameList = list.stream().map(f -> warehouseMap.get(f.getWarehouseCode()) + "-" + storageMap.get(f.getSlCode())).collect(Collectors.toList());
                    vo.setList(list);
                    vo.setNameList(nameList);
                    vo.setWarehouseStorageName(String.join(",", nameList));
                }
                vo.setAccountName(accountMap.get(vo.getAccountId()));
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuFbaBlackListVo.class, searchVo);
    }

    /**
     * save
     * 新增/编辑
     *
     * @param somTemuFbaBlackListVo 入参
     * @param tokenUser             当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuFbaBlackListVo somTemuFbaBlackListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuFbaBlackListVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isBlank(somTemuFbaBlackListVo.getAid())) {
            if (StrUtil.isBlank(somTemuFbaBlackListVo.getPlatform()) ||
                    StrUtil.isBlank(somTemuFbaBlackListVo.getSite()) ||
                    StrUtil.isBlank(somTemuFbaBlackListVo.getSellerSku()) ||
                    StrUtil.isBlank(somTemuFbaBlackListVo.getFulfillmentChannel()) ||
                    StrUtil.isBlank(somTemuFbaBlackListVo.getAccountId())) {
                throw new ValidateException("数据存在空值，请检查数据");
            }
            long count = somTemuFbaBlackListMapper.createLambdaQuery()
                    .andEq("platform", somTemuFbaBlackListVo.getPlatform())
                    .andEq("site", somTemuFbaBlackListVo.getSite())
                    .andEq("seller_sku", somTemuFbaBlackListVo.getSellerSku())
                    .andEq("account_id", somTemuFbaBlackListVo.getAccountId())
                    .andEq("delete_flag", 10)
                    .count();
            if (count > 0) {
                throw new ValidateException("数据已存在，不允许重复！");
            }
            // 核验店铺是否已经被注销
            List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
            List<String> accountIds = needShowAccounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
            if (! accountIds.contains(somTemuFbaBlackListVo.getAccountId())) {
                throw new ValidateException("店铺已删除或已被注销！");
            }
            if (CollectionUtil.isNotEmpty(somTemuFbaBlackListVo.getList())) {
                somTemuFbaBlackListVo.setUseableWarehouseStorage(JSONUtil.toJsonStr(somTemuFbaBlackListVo.getList()));
            }
            somTemuFbaBlackListVo.setAccountId(somTemuFbaBlackListVo.getAccountId());
            somTemuFbaBlackListVo.setAid(IdUtil.fastSimpleUUID());
            somTemuFbaBlackListVo.setDeleteFlag(10);
            somTemuFbaBlackListVo.setCreateNum(tokenUser.getJobNumber());
            somTemuFbaBlackListVo.setCreateName(tokenUser.getUserName());
            somTemuFbaBlackListVo.setCreateTime(DateTime.now().toJdkDate());
            somTemuFbaBlackListMapper.insert(ConvertUtils.beanConvert(somTemuFbaBlackListVo, SomTemuFbaBlackList.class));
        } else {
            // 不允许修改平台/站点/展示码/店铺
            somTemuFbaBlackListVo.setPlatform(null);
            somTemuFbaBlackListVo.setSite(null);
            somTemuFbaBlackListVo.setSellerSku(null);
            somTemuFbaBlackListVo.setAccountId(null);
            if (CollectionUtil.isNotEmpty(somTemuFbaBlackListVo.getList())) {
                somTemuFbaBlackListVo.setUseableWarehouseStorage(JSONUtil.toJsonStr(somTemuFbaBlackListVo.getList()));
            } else {
                somTemuFbaBlackListVo.setUseableWarehouseStorage("");
            }
            somTemuFbaBlackListVo.setCreateNum(tokenUser.getJobNumber());
            somTemuFbaBlackListVo.setCreateName(tokenUser.getUserName());
            somTemuFbaBlackListVo.setCreateTime(DateTime.now().toJdkDate());
            somTemuFbaBlackListMapper.createLambdaQuery()
                    .andEq("aid", somTemuFbaBlackListVo.getAid())
                    .updateSelective(ConvertUtils.beanConvert(somTemuFbaBlackListVo, SomTemuFbaBlackList.class));
        }
    }


    /**
     * delete
     * 删除
     *
     * @param somTemuFbaBlackListVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuFbaBlackListVo somTemuFbaBlackListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuFbaBlackListVo) || StrUtil.isBlank(somTemuFbaBlackListVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        SomTemuFbaBlackList blackList = new SomTemuFbaBlackList();
        blackList.setDeleteFlag(99);
        somTemuFbaBlackListMapper.createLambdaQuery()
                .andEq("aid", somTemuFbaBlackListVo.getAid())
                .updateSelective(blackList);
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String export(SomTemuFbaBlackListPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomTemuFbaBlackListVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "人工指定发货方式");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuFbaBlackListVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * importExcel
     * 导入
     *
     * @param list      行数据
     * @param tokenUser 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomTemuFbaBlackListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验Excel中的平台、站点、展示码不允许为空
        for (SomTemuFbaBlackListVo vo : list) {
            if (StrUtil.isBlank(vo.getPlatform()) ||
                    StrUtil.isBlank(vo.getSite()) ||
                    StrUtil.isBlank(vo.getSellerSku()) ||
                    StrUtil.isBlank(vo.getFulfillmentChannel()) ||
                    StrUtil.isBlank(vo.getAccountId())) {
                throw new ValidateException("店铺ID、平台、站点、展示码、发货方式 不允许为空");
            }
        }
        // 过滤掉已经被注销|不存在的店铺ID
        List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
        List<String> accountIds = needShowAccounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        list = list.stream().filter(data -> accountIds.contains(data.getAccountId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 校验Excel中是否有重复数据
        List<String> duplicateList = new ArrayList<>();
        Map<String, List<SomTemuFbaBlackListVo>> importMap = list.stream()
                .collect(Collectors.groupingBy(x -> x.getAccountId() + x.getPlatform() + x.getSite() + x.getSellerSku()));
        for (String key : importMap.keySet()) {
            List<SomTemuFbaBlackListVo> listVo = importMap.get(key);
            if (listVo.size() > 1) {
                SomTemuFbaBlackListVo vo = listVo.get(0);
                duplicateList.add("店铺ID" + vo.getAccountId() + "，平台：" + vo.getPlatform() + "，站点：" + vo.getSite() + "，展示码：" + vo.getSellerSku() + "重复，不允许导入");
            }
        }
        if (CollectionUtil.isNotEmpty(duplicateList)) {
            throw new ValidateException(String.join("\n", duplicateList));
        }
        List<McWarehouse> warehouseList = mcWarehouseMapper.all();
        Map<String, String> warehouseMap = warehouseList.stream().collect(Collectors.toMap(McWarehouse::getWarehouseName, McWarehouse::getWarehouseCode, (x1, x2) -> x1));
        List<SomStorageLocation> storageLocationList = somStorageLocationMapper.all();
        Map<String, String> storageMap = storageLocationList.stream().collect(Collectors.toMap(SomStorageLocation::getslName, SomStorageLocation::getslCode, (x1, x2) -> x1));
        // 处理发货方式，防止存在中文逗号 ，防止末尾出现逗号
        // 处理仓库库区中文逗号 冒号 ，结尾逗号
        for (SomTemuFbaBlackListVo f : list) {
            //防止存在中文逗号
            String fc = f.getFulfillmentChannel().replaceAll("，", ",");
            //去掉末尾单个出现的逗号
            if (fc.endsWith(",")) {
                fc = fc.substring(0, fc.length() - 1);
            }
            f.setFulfillmentChannel(fc);
            // [{"slCode":"1002","warehouseCode":"6303"}]
            String useableWarehouseStorage = f.getWarehouseStorageName();
            if (StrUtil.isNotBlank(useableWarehouseStorage)) {
                useableWarehouseStorage = useableWarehouseStorage.replace("，", ",");
                useableWarehouseStorage = useableWarehouseStorage.replace("：", ":");
                if (useableWarehouseStorage.endsWith(",")) {
                    useableWarehouseStorage = useableWarehouseStorage.substring(0, useableWarehouseStorage.length() - 1);
                }
                String[] split = useableWarehouseStorage.split(",");
                List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = new ArrayList<>();
                for (String cnWarehouse : split) {
                    String[] warehouseAndStorage = cnWarehouse.split(":");
                    if (warehouseAndStorage.length == 2) {
                        String warehouseName = warehouseAndStorage[0];
                        String storageName = warehouseAndStorage[1];
                        String warehouseCode = warehouseMap.get(warehouseName);
                        String storageCode = storageMap.get(storageName);
                        if (warehouseCode == null || storageCode == null) {
                            throw new ValidateException(cnWarehouse + " 没有找到对应的仓库库区编码，请检查仓库库区");
                        }
                        SomTemuFbaBlackListVo.UseableWarehouse useableWarehouse = new SomTemuFbaBlackListVo.UseableWarehouse();
                        useableWarehouse.setSlCode(storageCode);
                        useableWarehouse.setWarehouseCode(warehouseCode);
                        useableWarehouseList.add(useableWarehouse);
                    } else {
                        throw new ValidateException(cnWarehouse + " 仓库库区格式错误，请检查数据");
                    }
                }
                f.setUseableWarehouseStorage(JSONUtil.toJsonStr(useableWarehouseList));
            }
        }

        // 校验Excel中的店铺ID+平台+站点+展示码是否在数据库中已存在
        List<SomTemuFbaBlackList> allBlackLists = somTemuFbaBlackListMapper.createLambdaQuery().andEq("delete_flag", 10).select();
        Map<String, SomTemuFbaBlackList> allBlackListMap = allBlackLists.stream().collect(Collectors.toMap(f -> f.getAccountId() + f.getPlatform() + f.getSite() + f.getSellerSku(), Function.identity(), (x1, x2) -> x1));
        Iterator<SomTemuFbaBlackListVo> iterator = list.iterator();
        List<SomTemuFbaBlackList> updateList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        while (iterator.hasNext()) {
            SomTemuFbaBlackListVo f = iterator.next();
            // 当前导入key
            String importKey = f.getAccountId() + f.getPlatform() + f.getSite() + f.getSellerSku();
            if (allBlackListMap.containsKey(importKey)) {
                // 从list中删除的数据执行更新操作
                SomTemuFbaBlackList existFbaBlack = allBlackListMap.get(importKey);
                existFbaBlack.setFulfillmentChannel(f.getFulfillmentChannel());
                existFbaBlack.setCreateNum(tokenUser.getJobNumber());
                existFbaBlack.setCreateName(tokenUser.getUserName());
                existFbaBlack.setCreateTime(now);
                existFbaBlack.setUseableWarehouseStorage(f.getUseableWarehouseStorage());
                existFbaBlack.setAccountId(f.getAccountId());
                updateList.add(existFbaBlack);
                iterator.remove();
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            somTemuFbaBlackListMapper.batchUpdateById(updateList);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            List<SomTemuFbaBlackList> insertList = list.stream().map(f -> {
                SomTemuFbaBlackList black = new SomTemuFbaBlackList();
                black.setAid(IdUtil.fastSimpleUUID());
                black.setPlatform(f.getPlatform());
                black.setSite(f.getSite());
                black.setSellerSku(f.getSellerSku());
                black.setDeleteFlag(10);
                black.setCreateNum(tokenUser.getJobNumber());
                black.setCreateName(tokenUser.getUserName());
                black.setFulfillmentChannel(f.getFulfillmentChannel());
                black.setUseableWarehouseStorage(f.getUseableWarehouseStorage());
                black.setCreateTime(now);
                black.setAccountId(f.getAccountId());
                return black;
            }).collect(Collectors.toList());
            somTemuFbaBlackListMapper.insertBatch(insertList);
        }
    }

    /**
     * batchDelete
     * 批量删除
     *
     * @param somTemuFbaBlackListVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void batchDelete(SomTemuFbaBlackListVo somTemuFbaBlackListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuFbaBlackListVo) || somTemuFbaBlackListVo.getAidList() == null || somTemuFbaBlackListVo.getAidList().isEmpty()) {
            throw new ValidateException("请选择要删除的数据");
        }
        SomTemuFbaBlackList blackList = new SomTemuFbaBlackList();
        blackList.setDeleteFlag(99);
        somTemuFbaBlackListMapper.createLambdaQuery()
                .andIn("aid", somTemuFbaBlackListVo.getAidList())
                .updateSelective(blackList);
    }

    /**
     * 查询需要忽略的店铺ID
     *
     * @return List<String>
     */
    private List<McDictionaryInfo> queryNeedShowAccounts() {
        // 获取字典里面的 TemuAccount，需要展示 item_value4 = 1
        return mcDictionaryInfoMapper.createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .select();
    }
}
