package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuCrossSupplierPrice;
import com.zielsmart.mc.vo.SomTemuCrossSkuSupplierPriceVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-06-27
*/

@SqlResource("somTemuCrossSupplierPrice")
public interface SomTemuCrossSupplierPriceMapper extends BaseMapper<SomTemuCrossSupplierPrice> {

    /**
     * 查询Temu跨境供货价
     *
     * @param sites 站点
     * @param accountIds 店铺ID
     * @param productIds 商品ID
     * @param productSkuIds Product sku id
     * @return List<SomTemuCrossSkuSupplierPriceVo>
     */
    List<SomTemuCrossSkuSupplierPriceVo> querySupplierPrice(@Param("accountIds") List<String> accountIds, @Param("productIds") List<Long> productIds, @Param("productSkuIds") List<Long> productSkuIds);
}
