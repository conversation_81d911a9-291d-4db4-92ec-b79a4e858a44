package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
 * Temu本本营销活动明细的VO分页查询实体
 */
@Data
@Schema(title = "Temu本本营销活动明细的VO分页查询实体", name = "SomTemuLocalPromotionDetailPageSearchVo")
public class SomTemuLocalPromotionDetailPageSearchVo extends PageSearchVo {

    @Schema(description = "活动ID", name = "activityId")
    private String activityId;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;
}
