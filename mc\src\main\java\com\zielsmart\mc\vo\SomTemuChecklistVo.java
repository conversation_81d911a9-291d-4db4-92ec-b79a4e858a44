package com.zielsmart.mc.vo;

import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.postgresql.util.PGobject;

import java.io.Serializable;

/*
* 的VO实体对象
* gen by 代码生成器 2024-09-05
*/

@Data
@Schema(title = "",name = "SomTemuChecklistVo")
public class SomTemuChecklistVo implements java.io.Serializable {
	/**
	 * 主键aid
	 */
    @Schema(description = "主键aid",name="aid")
	private String aid;
	/**
	 * 核价单ID
	 */
    @Schema(description = "核价单ID",name="priceOrderId")
	private Long priceOrderId;
	/**
	 * TEMU产品ID
	 */
    @Schema(description = "TEMU产品ID",name="goodsId")
	private Long goodsId;
	/**
	 * TEMU SKU ID
	 */
    @Schema(description = "TEMU SKU ID",name="skuId")
	private Long skuId;
	/**
	 * 核价记录ID
	 */
    @Schema(description = "核价记录ID",name="priceCommitId")
	private Long priceCommitId;
	/**
	 * 核价单版本
	 */
    @Schema(description = "核价单版本",name="priceCommitVersion")
	private Integer priceCommitVersion;
	/**
	 * 参考申报价
	 */
    @Schema(description = "参考申报价",name="suggestSupplierPrice")
	private BigDecimal suggestSupplierPrice;
	/**
	 * 参考申报价-币种
	 */
    @Schema(description = "参考申报价-币种",name="suggestSupplierPriceCurrency")
	private String suggestSupplierPriceCurrency;
	/**
	 * 目标申报价  也就是我司提报的供货价
	 */
    @Schema(description = "目标申报价  也就是我司提报的供货价",name="targetSupplierPrice")
	private BigDecimal targetSupplierPrice;
	/**
	 * 目标申报价-币种
	 */
    @Schema(description = "目标申报价-币种",name="targetSupplierPriceCurrency")
	private String targetSupplierPriceCurrency;
	/**
	 * 如果需要议价，这个值为我司业务手工输入的“供货价”。
	 */
    @Schema(description = "如果需要议价，这个值为我司业务手工输入的“供货价”。",name="supplierPrice")
	private BigDecimal supplierPrice;
	/**
	 * 币种
	 */
    @Schema(description = "币种",name="supplierPriceCurrency")
	private String supplierPriceCurrency;
	/**
	 * 议价原因
	 */
    @Schema(description = "议价原因",name="bargainReason")
	private String bargainReason;
	/**
	 * 10.接受“参考供货价” 99.议价
	 */
    @Schema(description = "10.接受“参考供货价” 99.议价",name="handleType")
	private Integer handleType;
	/**
	 * 下载时间
	 */
    @Schema(description = "下载时间",name="syncTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date syncTime;

	/**
	 * 店铺ID
	 */
	@Schema(description = "店铺ID",name="accountId")
	private String accountId;

	@Schema(description = "站点集合",name="sites")
	private PGobject sites;

}
