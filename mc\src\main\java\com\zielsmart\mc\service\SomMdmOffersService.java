package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomMdmOffers;
import com.zielsmart.mc.repository.mapper.SomMdmOffersMapper;
import com.zielsmart.mc.util.InventoryCalcUtil;
import com.zielsmart.mc.vo.SomMdmOffersPageSearchVo;
import com.zielsmart.mc.vo.SomMdmOffersVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-04-08 11:13:10
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomMdmOffersService {
    
    @Resource
    private SomMdmOffersMapper somMdmOffersMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomMdmOffersVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomMdmOffersVo> queryByPage(SomMdmOffersPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomMdmOffersVo> pageResult = somMdmOffersMapper.queryByPage(searchVo, pageRequest);
        if(!pageResult.getList().isEmpty()){
            List<String> skuList = null;
            if(pageResult.getList().size()<200){
                skuList = pageResult.getList().stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("MaisonsduMonde.fr",skuList);
            calcInventory(pageResult.getList(), inventoryMap);
        }
        return ConvertUtils.pageConvert(pageResult, SomMdmOffersVo.class, searchVo);
    }

    private static void calcInventory(List<SomMdmOffersVo> list, Map<String, BigDecimal> inventoryMap) {
        for (SomMdmOffersVo offersVo : list) {
            BigDecimal inventory = inventoryMap.getOrDefault(offersVo.getSku()+"INVENTORY", BigDecimal.ZERO);
            BigDecimal dms = inventoryMap.getOrDefault(offersVo.getSku()+"DMS", BigDecimal.ZERO);
            //减去安全库存
            int stock = inventory.intValue() - Math.max(dms.divide(BigDecimal.valueOf(3),2,  RoundingMode.DOWN).intValue(), offersVo.getSafetyStock());
            offersVo.setStock(Math.max(stock, 0));
        }
    }

    /**
     * save
     * 添加
     * @param somMdmOffersVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomMdmOffersVo somMdmOffersVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somMdmOffersVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if(StrUtil.isBlank(somMdmOffersVo.getSellerSku()) || somMdmOffersVo.getPrice()==null || StrUtil.isBlank(somMdmOffersVo.getLogistic()) || somMdmOffersVo.getLeadtime()==null){
            throw new ValidateException("必填项未填写，请检查数据");
        }
        somMdmOffersVo.setModifyNum(tokenUser.getJobNumber());
        somMdmOffersVo.setModifyName(tokenUser.getUserName());
        somMdmOffersVo.setModifyTime(DateTime.now().toJdkDate());

        if (somMdmOffersVo.getDiscountPrice() != null) {
            if(somMdmOffersVo.getDiscountStartDate()==null || somMdmOffersVo.getDiscountEndDate()==null){
                throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
            }

            if(somMdmOffersVo.getDiscountStartDate().after(somMdmOffersVo.getDiscountEndDate())){
                throw new ValidateException("展示码"+somMdmOffersVo.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
            }
        }

        if (StrUtil.isBlank(somMdmOffersVo.getAid())) {
            //插入
            //判断展示码是否存在
            long count = somMdmOffersMapper.createLambdaQuery().andEq("seller_sku", somMdmOffersVo.getSellerSku()).count();
            if (count > 0) {
                throw new ValidateException("展示码已存在，请刷新界面检查数据");
            }
            somMdmOffersVo.setAid(IdUtil.fastSimpleUUID());
            somMdmOffersMapper.insert(ConvertUtils.beanConvert(somMdmOffersVo, SomMdmOffers.class));
        }else {
            //更新
            somMdmOffersMapper.updateById(ConvertUtils.beanConvert(somMdmOffersVo, SomMdmOffers.class));
        }
    }

    public String export(SomMdmOffersPageSearchVo searchVo) {
        List<SomMdmOffersVo> records = somMdmOffersMapper.exportExcel(searchVo);
        if (!records.isEmpty()) {
            //计算库存
            List<String> skuList = null;
            if(records.size()<200){
                skuList = records.stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("BUT.fr",skuList);
            calcInventory(records, inventoryMap);

            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "But fr Offers管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomMdmOffersVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomMdmOffersVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验"展示码","划线价格", "折扣价格","折扣开始时间","折扣结束时间","物流方式", "备货时间","备注" 是否为空
        HashSet<String> sellerSet = new HashSet<>();
        List<SomMdmOffers> existSellerSku = somMdmOffersMapper.createLambdaQuery().select("seller_sku","aid");
        Map<String, String> existSellerSkuMap = existSellerSku.stream().collect(Collectors.toMap(SomMdmOffers::getSellerSku, x -> x.getAid()));
        List<SomMdmOffers> allMdmListing = somMdmOffersMapper.allMdmListing();
        //转map
        Map<String, SomMdmOffers> mdmOffersMap = allMdmListing.stream().collect(Collectors.toMap(SomMdmOffers::getSellerSku, x -> x));
        //校验展示码是否存在
        String notExistSellerSkus = list.stream().map(SomMdmOffersVo::getSellerSku).filter(sellerSku -> !mdmOffersMap.containsKey(sellerSku)).collect(Collectors.joining(","));
        if(StrUtil.isNotBlank(notExistSellerSkus)){
            throw new ValidateException("展示码"+notExistSellerSkus+"不存在，批量导入失败!");
        }

        List<SomMdmOffersVo> insertList = new ArrayList<>();
        List<SomMdmOffersVo> updateList = new ArrayList<>();

        for (SomMdmOffersVo item : list) {
            if(!sellerSet.add(item.getSellerSku())){
                throw new ValidateException(" 展示码"+item.getSellerSku()+"重复，批量导入失败");
            }
            if(StrUtil.isBlank(item.getSellerSku()) || item.getPrice()==null || StrUtil.isBlank(item.getLogistic()) || item.getLeadtime()==null){
                throw new ValidateException("必填项未填写，批量导入失败");
            }
            if (item.getDiscountPrice() != null) {
                if(item.getDiscountStartDate()==null || item.getDiscountEndDate()==null){
                    throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
                }
                if(item.getPrice().compareTo(item.getDiscountPrice())<=0){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“划线价格>折扣价格”规则，批量导入失败！");
                }
                if(item.getDiscountStartDate().after(item.getDiscountEndDate())){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
                }
            }
            if(item.getDiscountStartDate()!=null || item.getDiscountEndDate()!=null){
                if (item.getDiscountPrice() == null) {
                    throw new ValidateException("折扣开始时间和结束时间已填写，折扣价格必须填写");
                }
            }

            if (existSellerSkuMap.containsKey(item.getSellerSku())) {
                //执行更新操作
                updateList.add(item);
            }else {
                //执行插入操作
                item.setAid(IdUtil.fastSimpleUUID());
                insertList.add(item);
            }
            item.setModifyNum(tokenUser.getJobNumber());
            item.setModifyName(tokenUser.getUserName());
            item.setModifyTime(DateTime.now().toJdkDate());
        }
        if(!insertList.isEmpty()){
            somMdmOffersMapper.insertBatch(ConvertUtils.listConvert(insertList, SomMdmOffers.class));
        }
        if(!updateList.isEmpty()){
            somMdmOffersMapper.updateBatch(ConvertUtils.listConvert(updateList, SomMdmOffers.class));
        }
    }
}
