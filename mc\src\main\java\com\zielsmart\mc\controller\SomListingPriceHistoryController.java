package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomListingPriceHistoryService;
import com.zielsmart.mc.vo.SomListingPriceHistoryVo;
import com.zielsmart.mc.vo.SomListingPricePageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomListingPriceHistoryController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somListingPriceHistory", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台价历史管理")
public class SomListingPriceHistoryController extends BasicController {

    @Resource
    SomListingPriceHistoryService somListingPriceHistoryService;

    /**
     * searchHistory
     * 查询历史记录
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomStandardPriceHistoryVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询历史记录")
    @PostMapping(value = "searchHistory")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomListingPriceHistoryVo>> searchHistory(@RequestBody SomListingPricePageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somListingPriceHistoryService.searchHistory(searchVo));
    }
}
