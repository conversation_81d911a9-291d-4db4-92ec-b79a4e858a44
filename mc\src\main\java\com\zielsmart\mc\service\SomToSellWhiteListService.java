package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomToSellWhiteList;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomToSellWhiteListMapper;
import com.zielsmart.mc.vo.SomToSellWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomToSellWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.beetl.sql.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomToSellWhiteListService {
    
    @Resource
    private SomToSellWhiteListMapper somToSellWhiteListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDictionaryInfoMapper dictMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomToSellWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomToSellWhiteListVo> queryByPage(SomToSellWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomToSellWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomToSellWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        //店铺分类
        List<McDictionaryInfo> storeType = dictMapper.createLambdaQuery().andEq("item_type_code", "StoreCategory").select();
        Map<String, String> storeMap = storeType.stream().collect(Collectors.toMap(x -> x.getItemValue(), y -> y.getItemLable()));
        //国家
        List<McDictionaryInfo> countryType = dictMapper.createLambdaQuery().andEq("item_type_code", "Country").select();
        Map<String, String> countryMap = countryType.stream().collect(Collectors.toMap(x -> x.getItemValue(), y -> y.getItemLable()));
        for (SomToSellWhiteListVo vo : pageResult.getList()) {
            vo.setCountryName(countryMap.getOrDefault(vo.getCountry(), vo.getCountry()));
            if (ObjectUtil.isNotNull(vo.getStoreType())) {
                vo.setStoreName(storeMap.get(String.valueOf(vo.getStoreType())));
            }
            vo.setUrl("https://www.amazon.de/sp?ie=UTF8&seller=" + vo.getSellerId());
        }
        return ConvertUtils.pageConvert(pageResult, SomToSellWhiteListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somToSellWhiteListVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional
    public void save(SomToSellWhiteListVo vo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(vo) || StrUtil.isBlank(vo.getCountry()) || StrUtil.isBlank(vo.getSellerId()) || StrUtil.isBlank(vo.getSellerName()) || ObjectUtil.isNull(vo.getStoreType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        LambdaQuery<SomToSellWhiteList> query = somToSellWhiteListMapper.createLambdaQuery().andEq("seller_id", vo.getSellerId());
        if (StrUtil.isNotBlank(vo.getAid())) {
            query.andNotEq("aid", vo.getAid());
        }
        long count = query.count();
        if (count > 0) {
            throw new ValidateException("您输入的跟卖者店铺在系统中已存在");
        }
        if (StrUtil.isBlank(vo.getAid())) {
            //新增
            vo.setAid(IdUtil.fastSimpleUUID());
            vo.setCreateName(tokenUser.getUserName());
            vo.setCreateNum(tokenUser.getJobNumber());
            vo.setCreateTime(DateTime.now().toJdkDate());
            somToSellWhiteListMapper.insert(ConvertUtils.beanConvert(vo,SomToSellWhiteList.class));
        }else{
            //更新
            SomToSellWhiteList single = somToSellWhiteListMapper.single(vo.getAid());
            if (single == null) {
                throw new ValidateException("没有此数据");
            }
            single.setCountry(vo.getCountry());
            single.setSellerId(vo.getSellerId());
            single.setSellerName(vo.getSellerName());
            single.setStoreType(vo.getStoreType());
            single.setRemark(vo.getRemark());
            single.setLastModifyName(tokenUser.getUserName());
            single.setLastModifyNum(tokenUser.getJobNumber());
            single.setLastModifyTime(DateTime.now().toJdkDate());
            somToSellWhiteListMapper.updateById(single);
        }
    }
    /**
     * delete
     * 删除
     * @param somToSellWhiteListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomToSellWhiteListVo somToSellWhiteListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somToSellWhiteListVo) || ObjectUtil.isEmpty(somToSellWhiteListVo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        //somToSellWhiteListMapper.createLambdaQuery().andEq("aid",somToSellWhiteListVo.getAid()).delete();
        somToSellWhiteListMapper.createLambdaQuery().andIn("aid", somToSellWhiteListVo.getAidList()).delete();
    }

    @Transactional
    public void importExcel(List<SomToSellWhiteListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验店铺ID是否重复  数据是否为空
        Set<String> sellerSet = new HashSet<>();
        for (SomToSellWhiteListVo vo : list) {
            if (ObjectUtil.isEmpty(vo) || StrUtil.isBlank(vo.getCountryName()) || StrUtil.isBlank(vo.getSellerId()) || StrUtil.isBlank(vo.getSellerName()) || ObjectUtil.isNull(vo.getStoreName())) {
                throw new ValidateException("国家 跟卖者店铺ID 跟卖者店铺名称 店铺分类 等列不允许为空，请检查导入文件");
            }
            if (sellerSet.contains(vo.getSellerId())) {
                throw new ValidateException("Excel中存在重复店铺ID:" + vo.getSellerId());
            }
            sellerSet.add(vo.getSellerId());
        }
        List<String> sellerIds = list.stream().map(x -> x.getSellerId()).collect(Collectors.toList());
        List<SomToSellWhiteList> data = somToSellWhiteListMapper.createLambdaQuery().andIn("seller_id", sellerIds).select();
        Map<String, SomToSellWhiteList> dataMap = data.stream().collect(Collectors.toMap(x -> x.getSellerId(), Function.identity()));
        //店铺分类
        List<McDictionaryInfo> storeType = dictMapper.createLambdaQuery().andEq("item_type_code", "StoreCategory").select();
        Map<String, String> storeMap = storeType.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue()));
        //国家
        List<McDictionaryInfo> countryType = dictMapper.createLambdaQuery().andEq("item_type_code", "Country").select();
        Map<String, String> countryMap = countryType.stream().collect(Collectors.toMap(x -> x.getItemLable(), y -> y.getItemValue()));

        StringBuffer sb = new StringBuffer();
        for (SomToSellWhiteListVo vo : list) {
            if (dataMap.containsKey(vo.getSellerId())) {
                sb.append("跟卖者店铺ID: " + vo.getSellerId() + "\n");
            }
            vo.setAid(IdUtil.fastSimpleUUID());
            vo.setCreateName(tokenUser.getUserName());
            vo.setCreateNum(tokenUser.getJobNumber());
            vo.setCreateTime(DateTime.now().toJdkDate());
            vo.setStoreType(Integer.valueOf(storeMap.get(vo.getStoreName())));
            vo.setCountry(countryMap.getOrDefault(vo.getCountryName(),vo.getCountryName()));
        }
        if (sb.length() > 0) {
            String msg = sb + "在系统中已存在，不允许重复维护";
            throw new ValidateException(msg);
        }
        somToSellWhiteListMapper.insertBatch(ConvertUtils.listConvert(list, SomToSellWhiteList.class));
    }
}
