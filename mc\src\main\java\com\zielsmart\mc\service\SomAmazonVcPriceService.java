package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.SomAmazonVcPriceComparisonMapper;
import com.zielsmart.mc.repository.mapper.SomAmazonVcPriceMapper;
import com.zielsmart.mc.vo.SomAmazonVcPricePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.math.BigDecimal;
import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomAmazonVcPriceService
 * @description
 * @date 2024-08-26 16:42:14
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomAmazonVcPriceService {

    @Resource
    private SomAmazonVcPriceMapper somAmazonVcPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McProductSalesMapper mcProductSalesMapper;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private SomAmazonVcPriceComparisonMapper somAmazonVcPriceComparisonMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomAmazonVcPriceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomAmazonVcPriceVo> queryByPage(SomAmazonVcPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomAmazonVcPriceVo> pageResult = dynamicSqlManager.getMapper(SomAmazonVcPriceMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomAmazonVcPriceVo.class, searchVo);
    }

    /**
     * save
     * 新增/编辑
     *
     * @param somAmazonVcPriceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomAmazonVcPriceVo somAmazonVcPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcPriceVo) || StrUtil.isEmpty(somAmazonVcPriceVo.getSite()) || StrUtil.isEmpty(somAmazonVcPriceVo.getSellerSku())
                || StrUtil.isEmpty(somAmazonVcPriceVo.getAsin()) || ObjectUtil.isEmpty(somAmazonVcPriceVo.getRecommendedRetailPrice()) || StrUtil.isEmpty(somAmazonVcPriceVo.getCurrency())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isEmpty(somAmazonVcPriceVo.getAid())) {
            long count = somAmazonVcPriceMapper.createLambdaQuery().andEq("site", somAmazonVcPriceVo.getSite()).andEq("seller_sku", somAmazonVcPriceVo.getSellerSku()).andEq("asin", somAmazonVcPriceVo.getAsin()).count();
            if (count > 0) {
                throw new ValidateException("站点：" + somAmazonVcPriceVo.getSite() + "，展示码：" + somAmazonVcPriceVo.getSellerSku() + "，ASIN：" + somAmazonVcPriceVo.getAsin() + "已存在，不允许重复");
            }
            somAmazonVcPriceVo.setAid(IdUtil.fastSimpleUUID());
            somAmazonVcPriceVo.setCreateNum(tokenUser.getJobNumber());
            somAmazonVcPriceVo.setCreateName(tokenUser.getUserName());
            somAmazonVcPriceVo.setCreateTime(DateTime.now().toJdkDate());
            somAmazonVcPriceMapper.insert(ConvertUtils.beanConvert(somAmazonVcPriceVo, SomAmazonVcPrice.class));
        } else {
            somAmazonVcPriceVo.setCreateNum(tokenUser.getJobNumber());
            somAmazonVcPriceVo.setCreateName(tokenUser.getUserName());
            somAmazonVcPriceVo.setCreateTime(DateTime.now().toJdkDate());
            somAmazonVcPriceMapper.createLambdaQuery().andEq("aid", somAmazonVcPriceVo.getAid()).updateSelective(ConvertUtils.beanConvert(somAmazonVcPriceVo, SomAmazonVcPrice.class));
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somAmazonVcPriceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomAmazonVcPriceVo somAmazonVcPriceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somAmazonVcPriceVo) || StrUtil.isEmpty(somAmazonVcPriceVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somAmazonVcPriceMapper.createLambdaQuery().andEq("aid", somAmazonVcPriceVo.getAid()).delete();
    }

    /**
     * 导入批量删除
     */
    public void importBatchDelete(List<SomAmazonVcPriceVo> somAmazonVcPriceVoList, TokenUserInfo tokenUser) {
        Set<String> asinSet = somAmazonVcPriceVoList.stream().map(SomAmazonVcPriceVo::getAsin).collect(Collectors.toSet());
        List<SomAmazonVcPrice> somAmazonVcPrices = new ArrayList<>();
        CollUtil.split(asinSet, 500).forEach(batch -> {
            somAmazonVcPrices.addAll(somAmazonVcPriceMapper.createLambdaQuery()
                    .andIn("asin", batch)
                    .select("aid", "site", "asin"));
        });
        Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = somAmazonVcPrices.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getAsin(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        List<SomAmazonVcPrice> somAmazonVcPriceList = somAmazonVcPriceVoList.stream()
                .map(x -> somAmazonVcPriceMap.get(x.getSite() + x.getAsin()))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(somAmazonVcPriceList)) {
            return;
        }
        List<String> aidList = somAmazonVcPriceList.stream().map(SomAmazonVcPrice::getAid).collect(Collectors.toList());
        CollUtil.split(aidList, 500).forEach(batch -> {
            int deleted = somAmazonVcPriceMapper.createLambdaQuery()
                    .andIn("aid", batch)
                    .delete();
            log.info("删除了 {} 条记录", deleted);
        });
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String export(SomAmazonVcPricePageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomAmazonVcPriceVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC价格信息表管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomAmazonVcPriceVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * importExcel
     * 导入文件
     *
     * @param list
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomAmazonVcPriceVo> list, TokenUserInfo tokenUser) throws ValidateException {
        for (SomAmazonVcPriceVo vo : list) {
            if (StrUtil.isEmpty(vo.getSite()) || StrUtil.isEmpty(vo.getSellerSku()) || ObjectUtil.isEmpty(vo.getRecommendedRetailPrice()) || StrUtil.isEmpty(vo.getCurrency())) {
                throw new ValidateException("导入文件中的数据存在空值，请检查后重新导入");
            }
        }
        //查询销售视图  查询ASIN和SKU
        List<McProductSales> salesList = mcProductSalesMapper.createLambdaQuery()
                .andIn("site", list.stream().map(SomAmazonVcPriceVo::getSite).distinct().collect(Collectors.toList()))
                .andIn("display_product_code", list.stream().map(SomAmazonVcPriceVo::getSellerSku).distinct().collect(Collectors.toList()))
                .andEq("sales_flag", 1).andEq("is_enabled", 1)
                .select();
        Map<String, McProductSales> salesMap = salesList.stream().collect(Collectors.toMap(f -> f.getSite() + f.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));

        StringBuilder sb = new StringBuilder();
        for (SomAmazonVcPriceVo vo : list) {
            if (salesMap.containsKey(vo.getSite() + vo.getSellerSku())) {
                McProductSales mcProductSales = salesMap.get(vo.getSite() + vo.getSellerSku());
                vo.setSku(mcProductSales.getProductMainCode());
                vo.setAsin(mcProductSales.getAsinCode());
            }else{
                sb.append("站点" + vo.getSite() + " 展示码:" + vo.getSellerSku() + "\n");
            }
        }
        if (sb.length() > 0) {
            throw new ValidateException(sb.toString()+"\n 未创建销售视图，不允许维护RRP");
        }

        Set<SomAmazonVcPriceVo> voSet = new HashSet<>(list);
        List<SomAmazonVcPrice> priceList = somAmazonVcPriceMapper.all();
        Map<String, SomAmazonVcPrice> priceMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(priceList)) {
            priceMap = priceList.stream().collect(Collectors.toMap(f -> f.getSite() + f.getSellerSku() + f.getAsin(), Function.identity(), (x1, x2) -> x2));
        }
        List<SomAmazonVcPrice> updateList = new ArrayList<>();
        List<SomAmazonVcPrice> insertList = new ArrayList<>();
        Date now = DateTime.now().toJdkDate();
        for (SomAmazonVcPriceVo vo : voSet) {
            SomAmazonVcPrice price = new SomAmazonVcPrice();
            if (priceMap.containsKey(vo.getSite() + vo.getSellerSku() + vo.getAsin())) {
                String aid = priceMap.get(vo.getSite() + vo.getSellerSku() + vo.getAsin()).getAid();
                price.setAid(aid);
                price.setSite(vo.getSite());
                price.setSellerSku(vo.getSellerSku());
                price.setSku(vo.getSku());
                price.setAsin(vo.getAsin());
                price.setRecommendedRetailPrice(vo.getRecommendedRetailPrice());
                price.setCurrency(vo.getCurrency());
                updateList.add(price);
            } else {
                price.setAid(IdUtil.fastSimpleUUID());
                price.setSite(vo.getSite());
                price.setSellerSku(vo.getSellerSku());
                price.setSku(vo.getSku());
                price.setAsin(vo.getAsin());
                price.setRecommendedRetailPrice(vo.getRecommendedRetailPrice());
                price.setCurrency(vo.getCurrency());
                price.setCreateNum(tokenUser.getJobNumber());
                price.setCreateName(tokenUser.getUserName());
                price.setCreateTime(now);
                insertList.add(price);
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            somAmazonVcPriceMapper.batchUpdate(updateList);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            somAmazonVcPriceMapper.insertBatch(insertList);
        }
    }

    /**
     * queryAsinAndSku
     * 根据站点和展示码查询销售视图，获取ASIN和SKU
     *
     * @param vo
     * @return {@link com.zielsmart.mc.vo.SomAmazonVcPriceVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomAmazonVcPriceVo queryAsinAndSku(SomAmazonVcPriceVo vo) throws ValidateException {
        if (StrUtil.isEmpty(vo.getSite()) || StrUtil.isEmpty(vo.getSellerSku())) {
            throw new ValidateException("站点、展示码不能为空");
        }
        SomAmazonVcPriceVo priceVo = new SomAmazonVcPriceVo();
        List<McProductSales> salesList = mcProductSalesMapper.createLambdaQuery().andEq("site", vo.getSite()).andEq("display_product_code", vo.getSellerSku()).andEq("sales_flag", 1).andEq("is_enabled", 1).select();
        if (CollectionUtil.isNotEmpty(salesList)) {
            salesList.stream().findFirst().ifPresent(f -> {
                priceVo.setAsin(f.getAsinCode());
                priceVo.setSku(f.getProductMainCode());
            });
        }
        return priceVo;
    }
}
