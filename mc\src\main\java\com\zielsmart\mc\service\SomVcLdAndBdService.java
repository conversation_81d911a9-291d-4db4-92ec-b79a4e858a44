package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.enums.VcPromotionStatusEnum;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SomVcLdAndBdService {
    
    @Resource
    private SomVcLdAndBdMapper somVcLdAndBdMapper;
    @Resource
    private SomVcLdAndBdItemMapper somVcLdAndBdItemMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomVcPromotionService somVcPromotionService;

    public PageVo<SomVcLdAndBdVo> queryByPage(SomVcLdAndBdPageSearchVo searchVo) {
        List<String> aidList = new ArrayList<>();
        List<String> itemAidList = new ArrayList<>();
        int searchIdent = 1;
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        aidList = aidList.stream().distinct().collect(Collectors.toList());
        if (searchIdent == 2 && CollUtil.isEmpty(aidList)) {
            // 给一个永远不会出现的值吧 避免逻辑异常
            aidList.add("-_-");
        }
        searchVo.setAidList(aidList);

        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcLdAndBdVo> pageResult = dynamicSqlManager.getMapper(SomVcLdAndBdMapper.class).queryByPage(searchVo, pageRequest);

        if (CollUtil.isNotEmpty(pageResult.getList())) {
            List<String> dealIdList = pageResult.getList().stream().map(SomVcLdAndBdVo::getAid).collect(Collectors.toList());
            LambdaQuery<SomVcLdAndBdItem> query = dynamicSqlManager.getMapper(SomVcLdAndBdItemMapper.class).createLambdaQuery();
            if (CollUtil.isNotEmpty(itemAidList)) {
                query.andIn("aid", itemAidList);
            }
            List<SomVcLdAndBdItem> somVcLdAndBdItemList = query.andIn("deal_id", dealIdList).select();
            pageResult.getList().forEach(somVcLdAndBdVo -> somVcLdAndBdVo.setItemList(somVcLdAndBdItemList.stream().filter(item -> item.getDealId().equals(somVcLdAndBdVo.getAid())).collect(Collectors.toList())));

            // 字典值、其他基础字段转换
            // 提报方式
            List<McDictionaryInfo> dictListVcLdbdRegistrationType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcLdbdRegistrationType").select();
            Map<String, String> dictMapVcLdbdRegistrationType = dictListVcLdbdRegistrationType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 秒杀类型
            List<McDictionaryInfo> dictListVcPromotionType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionType").select();
            Map<String, String> dictMapVcPromotionType = dictListVcPromotionType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 大促类型
            List<McDictionaryInfo> dictListVcPromotionCampaignType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionCampaignType").select();
            Map<String, String> dictMapVcPromotionCampaignType = dictListVcPromotionCampaignType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 状态
            List<McDictionaryInfo> dictListVcLdbdStatus = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcLdBdStatus").select();
            Map<String, String> dictMapVcLdbdStatus = dictListVcLdbdStatus.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 申请原因
            List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
            Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1));
            for (SomVcLdAndBdVo somVcLdAndBdVo : pageResult.getList()) {
                somVcLdAndBdVo.setRegistrationTypeShow(dictMapVcLdbdRegistrationType.get(somVcLdAndBdVo.getRegistrationType().toString())); // 提报方式
                somVcLdAndBdVo.setDealTypeShow(dictMapVcPromotionType.get(somVcLdAndBdVo.getDealType().toString())); // 秒杀类型
                somVcLdAndBdVo.setCampaignTypeShow(dictMapVcPromotionCampaignType.get(somVcLdAndBdVo.getCampaignType().toString())); // 大促类型
                somVcLdAndBdVo.setStatusShow(dictMapVcLdbdStatus.get(somVcLdAndBdVo.getStatus().toString())); // 状态
                somVcLdAndBdVo.setApplyReasonShow(dictMapVcPromotionApplyReason.get(somVcLdAndBdVo.getApplyReason().toString()).getItemLable()); // 申请原因
            }
        }

        return ConvertUtils.pageConvert(pageResult, SomVcLdAndBdVo.class, searchVo);
    }

    private void setSearchPreData(SomVcLdAndBdPageSearchVo searchVo, List<String> aidList, List<String> itemAidList) {
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            // 活动名称模糊搜索
//            List<SomVcLdAndBd> searchSomVcLdAndBdList = dynamicSqlManager.getMapper(SomVcLdAndBdMapper.class).createLambdaQuery().andLike(SomVcLdAndBd::getDealName, "%" + searchVo.getKeyWord() + "%").select("aid");
//            aidList.addAll(searchSomVcLdAndBdList.stream().map(SomVcLdAndBd::getAid).collect(Collectors.toList()));

            List<SomVcLdAndBdItem> searchSomVcLdAndBdItemList = dynamicSqlManager.getMapper(SomVcLdAndBdItemMapper.class).createLambdaQuery()
                    .andEq("seller_sku", searchVo.getKeyWord())
                    .orEq("asin", searchVo.getKeyWord())
                    .orEq("sku", searchVo.getKeyWord())
                    .select("aid", "deal_id");
            aidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getDealId).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getAid).collect(Collectors.toList()));
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            List<SomVcLdAndBdItem> searchSomVcLdAndBdItemList = dynamicSqlManager.getMapper(SomVcLdAndBdItemMapper.class).createLambdaQuery().andEq("sales_group_code", searchVo.getSalesGroupCode()).select("aid", "deal_id");
            aidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getDealId).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getAid).collect(Collectors.toList()));
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            List<SomVcLdAndBdItem> searchSomVcLdAndBdItemList = dynamicSqlManager.getMapper(SomVcLdAndBdItemMapper.class).createLambdaQuery().andEq("sales_group_empt_code", searchVo.getSalesGroupEmptCode()).select("aid", "deal_id");
            aidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getDealId).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcLdAndBdItemList.stream().map(SomVcLdAndBdItem::getAid).collect(Collectors.toList()));
        }
    }

    /**
     * 新增或修改
     * @param somVcLdAndBdVo SomVcLdAndBdVo
     * @param tokenUser TokenUserInfo
     * @throws ValidateException ValidateException
     */
    public void addOrEdit(SomVcLdAndBdVo somVcLdAndBdVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcLdAndBdVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        SomVcLdAndBd somVcLdAndBd = new SomVcLdAndBd();
        BeanUtil.copyProperties(somVcLdAndBdVo, somVcLdAndBd);
        List<SomVcLdAndBdItem> itemList = new ArrayList<>();
        // 唯一性验证
        SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
        BeanUtil.copyProperties(somVcLdAndBdVo, checkUniqueVo);
        for (SomVcLdAndBdItem vo : somVcLdAndBdVo.getItemList()) {
            checkUniqueVo.setSellerSku(vo.getSellerSku());
            SomVcLdAndBdItem item = new SomVcLdAndBdItem();
            BeanUtil.copyProperties(vo, item);
            item.setAid(IdUtil.fastSimpleUUID());
            itemList.add(item);
        }
        if (StrUtil.isEmpty(somVcLdAndBdVo.getAid())) {
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                throw new ValidateException("该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增");
            }
            String aid = IdUtil.fastSimpleUUID();
            somVcLdAndBd.setAid(aid);
            somVcLdAndBd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcLdAndBd.setCreateNum(tokenUser.getJobNumber());
            somVcLdAndBd.setCreateName(tokenUser.getUserName());
            somVcLdAndBd.setCreateTime(DateTime.now().toJdkDate());
            somVcLdAndBdMapper.insert(somVcLdAndBd);
            // itemList
            for (SomVcLdAndBdItem item : itemList) {
                item.setDealId(aid);
                item.setAid(IdUtil.fastSimpleUUID());
            }
            somVcLdAndBdItemMapper.insertBatch(itemList);
        } else {
            checkUniqueVo.setLdbdAid(somVcLdAndBdVo.getAid());
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                throw new ValidateException("该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增");
            }
            // 编辑后 -> 草稿
            somVcLdAndBd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcLdAndBd.setModifyNum(tokenUser.getJobNumber());
            somVcLdAndBd.setModifyName(tokenUser.getUserName());
            somVcLdAndBd.setModifyTime(DateTime.now().toJdkDate());
            somVcLdAndBdMapper.updateById(somVcLdAndBd);
            // itemList
            for (SomVcLdAndBdItem item : itemList) {
                item.setDealId(somVcLdAndBdVo.getAid());
                item.setAid(IdUtil.fastSimpleUUID());
            }
            somVcLdAndBdItemMapper.createLambdaQuery().andEq("deal_id", somVcLdAndBdVo.getAid()).delete();
            somVcLdAndBdItemMapper.insertBatch(itemList);
        }
    }

    public void delete(SomVcLdAndBdVo somVcLdAndBdVo) throws ValidateException {
        SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMapper.createLambdaQuery().andEq("aid", somVcLdAndBdVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcLdAndBd)) {
            throw new ValidateException("内容不存在");
        }
        // [草稿]状态才可以删除
        if (!ObjectUtil.equal(somVcLdAndBd.getStatus(), VcPromotionStatusEnum.DRAFT.getStatus())) {
            throw new ValidateException("只能删除状态为草稿的内容");
        }
        somVcLdAndBdMapper.createLambdaQuery().andEq("aid", somVcLdAndBdVo.getAid()).delete();
        somVcLdAndBdItemMapper.createLambdaQuery().andEq("deal_id", somVcLdAndBdVo.getAid()).delete();
    }

    public void cancel(SomVcLdAndBdVo somVcLdAndBdVo) throws ValidateException {
        SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMapper.createLambdaQuery().andEq("aid", somVcLdAndBdVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcLdAndBd)) {
            throw new ValidateException("内容不存在");
        }
        // [进行中,需要关注,未开始]的活动允许取消
        List<Integer> allowCancelStatus = VcPromotionStatusEnum.getAllowCancelStatus();
        if (!allowCancelStatus.contains(somVcLdAndBd.getStatus())) {
            throw new ValidateException("当前数据不允许取消！");
        }
        somVcLdAndBd.setStatus(VcPromotionStatusEnum.CANCELED.getStatus());
        somVcLdAndBdMapper.createLambdaQuery().andEq("aid", somVcLdAndBdVo.getAid()).update(somVcLdAndBd);
    }

    public String export(SomVcLdAndBdPageSearchVo searchVo) {
        List<String> aidList = new ArrayList<>();
        List<String> itemAidList = new ArrayList<>();
        int searchIdent = 1;
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        aidList = aidList.stream().distinct().collect(Collectors.toList());
        if (searchIdent == 2 && CollUtil.isEmpty(aidList)) {
            // 给一个永远不会出现的值吧 避免逻辑异常
            aidList.add("-_-");
        }
        searchVo.setAidList(aidList);

        List<SomVcLdAndBdVo> records = somVcLdAndBdMapper.queryList(searchVo);

        if (CollUtil.isNotEmpty(records)) {
            List<String> dealIdList = records.stream().map(SomVcLdAndBdVo::getAid).collect(Collectors.toList());
            LambdaQuery<SomVcLdAndBdItem> query = dynamicSqlManager.getMapper(SomVcLdAndBdItemMapper.class).createLambdaQuery();
            if (CollUtil.isNotEmpty(itemAidList)) {
                query.andIn("aid", itemAidList);
            }
            List<SomVcLdAndBdItem> somVcLdAndBdItemList = query.andIn("deal_id", dealIdList).select();
            records.forEach(somVcLdAndBdVo -> somVcLdAndBdVo.setItemList(somVcLdAndBdItemList.stream().filter(item -> item.getDealId().equals(somVcLdAndBdVo.getAid())).collect(Collectors.toList())));

            // 字典值、其他基础字段转换
            // 提报方式
            List<McDictionaryInfo> dictListVcLdbdRegistrationType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcLdbdRegistrationType").select();
            Map<String, String> dictMapVcLdbdRegistrationType = dictListVcLdbdRegistrationType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 秒杀类型
            List<McDictionaryInfo> dictListVcPromotionType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionType").select();
            Map<String, String> dictMapVcPromotionType = dictListVcPromotionType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 大促类型
            List<McDictionaryInfo> dictListVcPromotionCampaignType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionCampaignType").select();
            Map<String, String> dictMapVcPromotionCampaignType = dictListVcPromotionCampaignType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 状态
            List<McDictionaryInfo> dictListVcLdbdStatus = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcLdBdStatus").select();
            Map<String, String> dictMapVcLdbdStatus = dictListVcLdbdStatus.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 申请原因
            List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
            Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1));
            for (SomVcLdAndBdVo somVcLdAndBdVo : records) {
                somVcLdAndBdVo.setRegistrationTypeShow(dictMapVcLdbdRegistrationType.get(somVcLdAndBdVo.getRegistrationType().toString())); // 提报方式
                somVcLdAndBdVo.setDealTypeShow(dictMapVcPromotionType.get(somVcLdAndBdVo.getDealType().toString())); // 秒杀类型
                somVcLdAndBdVo.setCampaignTypeShow(dictMapVcPromotionCampaignType.get(somVcLdAndBdVo.getCampaignType().toString())); // 大促类型
                somVcLdAndBdVo.setStatusShow(dictMapVcLdbdStatus.get(somVcLdAndBdVo.getStatus().toString())); // 状态
                somVcLdAndBdVo.setApplyReasonShow(dictMapVcPromotionApplyReason.get(somVcLdAndBdVo.getApplyReason().toString()).getItemLable()); // 申请原因
            }
        }

        // 组装成以item为行主体的导出数据
        List<SomVcLdAndBdExportVo> exportVos = new ArrayList<>();
        for (SomVcLdAndBdVo somVcLdAndBdVo : records) {
            for (SomVcLdAndBdItem somVcLdAndBdItem : somVcLdAndBdVo.getItemList()) {
                SomVcLdAndBdExportVo exportVo = new SomVcLdAndBdExportVo();
                BeanUtil.copyProperties(somVcLdAndBdVo, exportVo);
                BeanUtil.copyProperties(somVcLdAndBdItem, exportVo);
                // 起止时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                exportVo.setStartDateAndEndDate(sdf.format(somVcLdAndBdVo.getStartDate()) + "\n" + sdf.format(somVcLdAndBdVo.getEndDate()));
                exportVos.add(exportVo);
            }
        }

        if (CollUtil.isNotEmpty(exportVos)) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "VC LD&BD");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomVcLdAndBdExportVo.class, exportVos);
                // 自适应高度
                Sheet sheet = workbook.getSheetAt(0);
                // 遍历所有行
                for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;
                    // 遍历每一列，计算最大行高
                    int maxLines = 1;
                    for (int j = 0; j < row.getLastCellNum(); j++) {
                        Cell cell = row.getCell(j);
                        if (cell == null || cell.getStringCellValue() == null) continue;
                        // 根据换行符计算行数
                        String cellValue = cell.getStringCellValue();
                        int lines = cellValue.split("\n").length;
                        if (lines > maxLines) {
                            maxLines = lines;
                        }
                    }
                    // 设置行高 每行高度为15*行数
                    row.setHeight((short) (15 * maxLines * 20));
                }
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 表格批量导入
     * @param list List<SomVcLdAndBdImportVo>
     * @param tokenUser TokenUserInfo
     * @return String
     */
    public String importExcel(List<SomVcLdAndBdImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        // 站点唯一验证
        long siteCount = list.stream().map(SomVcLdAndBdImportVo::getSite).distinct().count();
        if (siteCount > 1) {
            throw new ValidateException("导入数据存在多个站点，请检查数据！");
        }

        StringBuilder stringBuilder = new StringBuilder();
        List<String> siteList = list.stream().map(SomVcLdAndBdImportVo::getSite).collect(Collectors.toList());
        List<String> sellerSkuList = list.stream().map(SomVcLdAndBdImportVo::getSellerSku).collect(Collectors.toList());
        List<String> asinList = list.stream().map(SomVcLdAndBdImportVo::getAsin).collect(Collectors.toList());

        // 确定使用展示码还是asin
        SomVcLdAndBdImportVo firstRow = list.get(0);
        String sellerSkuFirst = firstRow.getSellerSku();
        String asinFirst = firstRow.getAsin();
        if (StrUtil.isNotBlank(sellerSkuFirst) && StrUtil.isNotBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN只能使用一项");
        }
        if (StrUtil.isBlank(sellerSkuFirst) && StrUtil.isBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN至少填写一项");
        }
        String useSellerSkuOrAsin = "sellerSku";
        if (StrUtil.isNotEmpty(asinFirst)) {
            useSellerSkuOrAsin = "asin";
        }

        // 必填项
        int i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            StringBuilder rowErrors = new StringBuilder();
            if (StrUtil.isEmpty(vo.getSite())) rowErrors.append("站点、");
            if (StrUtil.isEmpty(vo.getAccountName())) rowErrors.append("账号名称、");
            if (StrUtil.isEmpty(vo.getVendorCode())) rowErrors.append("供应商编码、");
            if (StrUtil.isEmpty(vo.getDealName())) rowErrors.append("活动名称、");
            if (StrUtil.isEmpty(vo.getDealTypeShow())) rowErrors.append("秒杀类型、");
            if (StrUtil.isEmpty(vo.getCampaignTypeShow())) rowErrors.append("大促类型、");
            if (ObjectUtil.isEmpty(vo.getRegistrationTypeShow())) rowErrors.append("提报方式、");
            if (ObjectUtil.isEmpty(vo.getStartDate())) rowErrors.append("活动起始日期、");
            if (ObjectUtil.isEmpty(vo.getEndDate())) rowErrors.append("活动截止日期、");
            //if (StrUtil.isEmpty(vo.getSellerSku())) rowErrors.append("展示码、");
            if (ObjectUtil.isEmpty(vo.getQuantity())) rowErrors.append("承诺商品数量、");
            if (StrUtil.isEmpty(vo.getApplyReasonShow())) rowErrors.append("申请原因、");
            if (rowErrors.length() > 0) {
                rowErrors.setLength(rowErrors.length() - 1);
                stringBuilder.append("第").append(i).append("行 - 缺少必填项: ").append(rowErrors).append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 基础字段验证
        i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            // 折扣比例 活动价格 最少需填一个
            if (ObjectUtil.isEmpty(vo.getDiscount()) && ObjectUtil.isEmpty(vo.getLikelyPromotionalPrice())) {
                stringBuilder.append("第").append(i).append("行 - [折扣比例]和[活动价格]最少需填一个").append("\n");
            }
            // 活动时间
            if (vo.getStartDate().after(vo.getEndDate())) {
                stringBuilder.append("第").append(i).append("行 - 活动截止日期必须晚于起始日期").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 唯一性验证
        i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
            BeanUtil.copyProperties(vo, checkUniqueVo);
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                stringBuilder.append("第").append(i).append("行 - 该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 字典值、其他基础字段转换
        List<McDictionaryInfo> dictListVcPromotionType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionType").select();
        Map<String, String> dictMapVcPromotionType = dictListVcPromotionType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcLdbdRegistrationType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcLdbdRegistrationType").select();
        Map<String, String> dictMapVcLdbdRegistrationType = dictListVcLdbdRegistrationType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcPromotionCampaignType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionCampaignType").select();
        Map<String, String> dictMapVcPromotionCampaignType = dictListVcPromotionCampaignType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
        Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1));
        // 平台站点属性配置 获取币种
        List<McPlatformProperties> mcPlatformPropertiesList = dynamicSqlManager.getMapper(McPlatformPropertiesMapper.class).createLambdaQuery().andIn("site", siteList).select();
        Map<String, String> mcPlatformPropertiesMap = mcPlatformPropertiesList.stream().collect(Collectors.toMap(McPlatformProperties::getSite, McPlatformProperties::getCurrencyCode, (x1, x2) -> x1));
        for (SomVcLdAndBdImportVo vo : list) {
            // 字典值转换
            vo.setDealType(Integer.valueOf(dictMapVcPromotionType.get(vo.getDealTypeShow()))); // 营销活动类型 秒杀类型
            vo.setRegistrationType(Integer.valueOf(dictMapVcLdbdRegistrationType.get(vo.getRegistrationTypeShow()))); // 营销活动注册方式
            vo.setCampaignType(Integer.valueOf(dictMapVcPromotionCampaignType.get(vo.getCampaignTypeShow()))); // 大促类型
            vo.setApplyReason(Integer.valueOf(dictMapVcPromotionApplyReason.get(vo.getApplyReasonShow()).getItemValue())); // 申请原因
            // 币种
            vo.setCurrency(mcPlatformPropertiesMap.get(vo.getSite()));
            // 折扣比例 -> 折扣比值 与前端保持一致
            if (ObjectUtil.isNotEmpty(vo.getDiscount())) {
                vo.setDiscount(vo.getDiscount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            }
        }

        // 获取ASIN、SKU等销售视图相关数据
        Map<String, McProductSalesVo> mcProductSalesVoMap = null;
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setSiteList(siteList);
        if (useSellerSkuOrAsin.equals("sellerSku")) {
            mcProductSalesSearchVo.setDisplayProductCodeList(sellerSkuList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));
        }
        if (useSellerSkuOrAsin.equals("asin")) {
            mcProductSalesSearchVo.setAsinList(asinList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getAsinCode(), Function.identity(), (x1, x2) -> x1));
        }
        i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            String key = "";
            if (useSellerSkuOrAsin.equals("sellerSku")) {
                key = vo.getSite() + vo.getSellerSku();
            }
            if (useSellerSkuOrAsin.equals("asin")) {
                key = vo.getSite() + vo.getAsin();
            }
            McProductSalesVo mcProductSalesVo = mcProductSalesVoMap.get(key);
            if (mcProductSalesVo != null) {
                vo.setSellerSku(mcProductSalesVo.getDisplayProductCode());
                vo.setAsin(mcProductSalesVo.getAsinCode());
                vo.setSku(mcProductSalesVo.getProductMainCode());
                vo.setSalesGroupCode(mcProductSalesVo.getSalesGroupCode());
                vo.setSalesGroupName(mcProductSalesVo.getSalesGroupName());
                vo.setSalesGroupEmptCode(mcProductSalesVo.getSalesGroupEmptCode());
                vo.setSalesGroupEmptName(mcProductSalesVo.getSalesGroupEmptName());
                vo.setOperationEmptCode(mcProductSalesVo.getOperationEmptCode());
                vo.setOperationEmptName(mcProductSalesVo.getOperationEmptName());
            } else {
                stringBuilder.append("第").append(i).append("行 - 销售视图查询不存在").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 建议零售价RRP/前台售价/30天DMS/单个产品预估积分
        // 确定折扣比例、活动价格
        asinList = list.stream().map(SomVcLdAndBdImportVo::getAsin).collect(Collectors.toList());
        sellerSkuList = list.stream().map(SomVcLdAndBdImportVo::getSellerSku).collect(Collectors.toList());
        List<SomAmazonVcPrice> somAmazonVcPriceList = dynamicSqlManager.getMapper(SomAmazonVcPriceMapper.class).createLambdaQuery()
                .andIn("site", siteList)
                .andIn("seller_sku", sellerSkuList)
                .andIn("asin", asinList)
                .select();
        Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = somAmazonVcPriceList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getSellerSku() + x.getAsin(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            String key = vo.getSite() + vo.getSellerSku() + vo.getAsin();
            SomAmazonVcPrice somAmazonVcPrice = somAmazonVcPriceMap.get(key);
            if (somAmazonVcPrice != null) {
                // 前台售价
                vo.setFrontSellPrice(somAmazonVcPrice.getPrice());
                // 30天DMS
                vo.setMonthDms(somAmazonVcPrice.getMonthDms());
                // 建议零售价RRP
                vo.setRecommendedRetailPrice(somAmazonVcPrice.getRecommendedRetailPrice());
                // 确定折扣比例、活动价格
                BigDecimal rrp = somAmazonVcPrice.getRecommendedRetailPrice();
                if (ObjectUtil.isNotEmpty(vo.getDiscount())) {
                    // 活动价格 = RRP - RRP * 折扣比例
                    BigDecimal discount = vo.getDiscount().divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                    BigDecimal likelyPromotionalPrice = rrp.subtract(rrp.multiply(discount)).setScale(2, RoundingMode.HALF_UP);
                    vo.setLikelyPromotionalPrice(likelyPromotionalPrice);
                } else {
                    // 折扣比例 = (RRP - 活动价格) / RRP
                    BigDecimal likelyPromotionalPrice = vo.getLikelyPromotionalPrice();
                    BigDecimal discount = rrp.subtract(likelyPromotionalPrice)
                            .divide(rrp, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    vo.setDiscount(discount);
                }
                // 单个产品预估折扣金额
                // RRP * 折扣百分比
                BigDecimal perUnitFunding = BigDecimal.ZERO;
                BigDecimal discount = vo.getDiscount().divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                perUnitFunding = rrp.multiply(discount).setScale(2, RoundingMode.HALF_UP);
                vo.setPerUnitFunding(perUnitFunding);
            } else {
                stringBuilder.append("第").append(i).append("行 - 此产品未维护RRP，请先维护RRP").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 获取库存可销售天数
        List<SomSellerSkuStockSaleDaysQueryVo> queryVos = new ArrayList<>();
        for (SomVcLdAndBdImportVo vo : list) {
            SomSellerSkuStockSaleDaysQueryVo queryVo = new SomSellerSkuStockSaleDaysQueryVo();
            queryVo.setSite(vo.getSite());
            queryVo.setSellerSku(vo.getSellerSku());
            queryVo.setStartDate(vo.getStartDate());
            queryVo.setEndDate(vo.getEndDate());
            queryVos.add(queryVo);
        }
        Map<String, Object> vcStockSaleDaysMap = somVcPromotionService.queryVcStockSaleDays(queryVos);
        for (SomVcLdAndBdImportVo vo : list) {
            // 库存可销售天数
            if (vcStockSaleDaysMap.containsKey(vo.getSellerSku())) {
                vo.setStockSaleDays((Integer) vcStockSaleDaysMap.get(vo.getSellerSku()));
            }
        }

        // 根据「活动名称」分组，同一组创建一条营销活动
        Map<String, List<SomVcLdAndBdImportVo>> somVcLdAndBdImportVoMap = list.stream().collect(Collectors.groupingBy(
                SomVcLdAndBdImportVo::getDealName
        ));

        List<SomVcLdAndBd> insertList = new ArrayList<>();
        List<SomVcLdAndBdItem> insertListItem = new ArrayList<>();
        for (Map.Entry<String, List<SomVcLdAndBdImportVo>> entry : somVcLdAndBdImportVoMap.entrySet()) {
            String key = entry.getKey();
            List<SomVcLdAndBdImportVo> voList = entry.getValue();
            SomVcLdAndBdImportVo vo = voList.get(0);

            // 主数据
            SomVcLdAndBd somVcLdAndBd = new SomVcLdAndBd();
            String dealAid = IdUtil.fastSimpleUUID();
            somVcLdAndBd.setAid(dealAid);
            somVcLdAndBd.setSite(vo.getSite());
            somVcLdAndBd.setAccountName(vo.getAccountName());
            somVcLdAndBd.setVendorCode(vo.getVendorCode());
            somVcLdAndBd.setDealName(vo.getDealName());
            somVcLdAndBd.setDealType(vo.getDealType());
            somVcLdAndBd.setRegistrationType(vo.getRegistrationType());
            somVcLdAndBd.setCampaignType(vo.getCampaignType());
            somVcLdAndBd.setStartDate(vo.getStartDate());
            somVcLdAndBd.setEndDate(vo.getEndDate());
            somVcLdAndBd.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcLdAndBd.setApplyReason(vo.getApplyReason());
            somVcLdAndBd.setCustomReason(vo.getCustomReason());
            somVcLdAndBd.setCreateTime(DateTime.now().toJdkDate());
            somVcLdAndBd.setCreateName(tokenUser.getUserName());
            somVcLdAndBd.setCreateNum(tokenUser.getJobNumber());
            insertList.add(somVcLdAndBd);

            // 从数据
            for (SomVcLdAndBdImportVo itemVo : voList) {
                SomVcLdAndBdItem somVcLdAndBdItem = new SomVcLdAndBdItem();
                somVcLdAndBdItem.setAid(IdUtil.fastSimpleUUID());
                somVcLdAndBdItem.setDealId(dealAid);
                somVcLdAndBdItem.setSellerSku(itemVo.getSellerSku());
                somVcLdAndBdItem.setAsin(itemVo.getAsin());
                somVcLdAndBdItem.setSku(itemVo.getSku());
                somVcLdAndBdItem.setRecommendedRetailPrice(itemVo.getRecommendedRetailPrice());
                somVcLdAndBdItem.setDiscount(itemVo.getDiscount());
                somVcLdAndBdItem.setLikelyPromotionalPrice(itemVo.getLikelyPromotionalPrice());
                somVcLdAndBdItem.setCurrency(itemVo.getCurrency());
                somVcLdAndBdItem.setQuantity(itemVo.getQuantity());
                somVcLdAndBdItem.setStockSaleDays(itemVo.getStockSaleDays());
                somVcLdAndBdItem.setRating(itemVo.getRating());
                somVcLdAndBdItem.setMonthDms(itemVo.getMonthDms());
                somVcLdAndBdItem.setSalesGroupCode(itemVo.getSalesGroupCode());
                somVcLdAndBdItem.setSalesGroupName(itemVo.getSalesGroupName());
                somVcLdAndBdItem.setSalesGroupEmptCode(itemVo.getSalesGroupEmptCode());
                somVcLdAndBdItem.setSalesGroupEmptName(itemVo.getSalesGroupEmptName());
                somVcLdAndBdItem.setOperationEmptCode(itemVo.getOperationEmptCode());
                somVcLdAndBdItem.setOperationEmptName(itemVo.getOperationEmptName());
                somVcLdAndBdItem.setPerUnitFunding(itemVo.getPerUnitFunding());
                insertListItem.add(somVcLdAndBdItem);
            }
        }
        if (CollUtil.isNotEmpty(insertList)) {
            somVcLdAndBdMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(insertListItem)) {
            somVcLdAndBdItemMapper.insertBatch(insertListItem);
        }

        return "";
    }

    /**
     * 反馈提报结果
     * @param somVcLdAndBdVo SomVcLdAndBdVo
     * @param tokenUser TokenUserInfo
     * @throws ValidateException ValidateException
     */
    public void feedbackSubmitResult(SomVcLdAndBdVo somVcLdAndBdVo, TokenUserInfo tokenUser) throws ValidateException {
        SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMapper.createLambdaQuery().andEq("aid", somVcLdAndBdVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcLdAndBd)) {
            throw new ValidateException("内容不存在");
        }
        // 反馈提报结果状态必须是 [提报成功,提报失败,Need Review]
        Integer successStatus = VcPromotionStatusEnum.SUBMIT_SUCCESS.getStatus();
        Integer submitFailedStatus = VcPromotionStatusEnum.SUBMIT_FAILED.getStatus();
        Integer submitNeedReviewStatus = VcPromotionStatusEnum.SUBMIT_NEED_REVIEW.getStatus();
        List<Integer> allowFeedbackStatus = Arrays.asList(successStatus, submitFailedStatus, submitNeedReviewStatus);
        if (!allowFeedbackStatus.contains(somVcLdAndBdVo.getStatus())) {
            throw new ValidateException("提报结果参数有误");
        }
        // 只允许提报[草稿,提报中]的活动
        List<Integer> allowSubmitStatus = Arrays.asList(VcPromotionStatusEnum.DRAFT.getStatus(), VcPromotionStatusEnum.SUBMITTING.getStatus());
        if (!allowSubmitStatus.contains(somVcLdAndBd.getStatus())) {
            throw new ValidateException("只允许提报[草稿,提报中]的活动");
        }
        // 提报失败/Need Review，失败原因必填
        List<Integer> needFailureMessageStatus = Arrays.asList(submitFailedStatus, submitNeedReviewStatus);
        if (needFailureMessageStatus.contains(somVcLdAndBdVo.getStatus()) && StrUtil.isEmpty(somVcLdAndBdVo.getFailureMessage())) {
            throw new ValidateException("提报结果为提报失败/Need Review时，失败原因/BS Feedback不能为空！");
        }
        // 提报成功 -> 即将开始
        if (successStatus.equals(somVcLdAndBdVo.getStatus())) {
            somVcLdAndBd.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
            somVcLdAndBd.setFailureMessage(null);
        } else {
            somVcLdAndBd.setStatus(submitFailedStatus);
            somVcLdAndBd.setFailureMessage(somVcLdAndBdVo.getFailureMessage());
        }
        somVcLdAndBd.setModifyNum(tokenUser.getJobNumber());
        somVcLdAndBd.setModifyName(tokenUser.getUserName());
        somVcLdAndBd.setModifyTime(DateTime.now().toJdkDate());
        somVcLdAndBdMapper.updateById(somVcLdAndBd);
    }

    /**
     * 批量反馈提报结果
     * @param list List<SomVcLdAndBdImportVo>
     * @param tokenUser TokenUserInfo
     * @return String
     * @throws ValidateException ValidateException
     */
    public String batchFeedbackSubmitResult(List<SomVcLdAndBdImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        List<String> dealNameList = list.stream().map(SomVcLdAndBdImportVo::getDealName).collect(Collectors.toList());
        List<SomVcLdAndBd> somVcLdAndBdList = dynamicSqlManager.getMapper(SomVcLdAndBdMapper.class).createLambdaQuery()
                .andIn(SomVcLdAndBd::getDealName, dealNameList)
                .select();
        Map<String, SomVcLdAndBd> somVcLdAndBdMap = somVcLdAndBdList.stream().collect(Collectors.toMap(
                SomVcLdAndBd::getDealName,
                Function.identity(),
                (x1, x2) -> x1
        ));

        // 验证数据
        // 只允许提报[草稿,提报中]的活动
        List<Integer> allowSubmitStatus = Arrays.asList(VcPromotionStatusEnum.DRAFT.getStatus(), VcPromotionStatusEnum.SUBMITTING.getStatus());
        List<String> needFailureMessageStatusShow = Arrays.asList(VcPromotionStatusEnum.SUBMIT_FAILED.getDesc(), VcPromotionStatusEnum.SUBMIT_NEED_REVIEW.getDesc()); // 提报失败/Need Review
        StringBuilder stringBuilder = new StringBuilder();
        int i = 2;
        for (SomVcLdAndBdImportVo vo : list) {
            // 活动是否存在
            // 只允许提报[草稿,提报中]的活动
            SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMap.get(vo.getDealName());
            if (ObjectUtil.isEmpty(somVcLdAndBd)) {
                stringBuilder.append("第").append(i).append("行 - 活动不存在").append("\n");
            } else if (!allowSubmitStatus.contains(somVcLdAndBd.getStatus())) {
                stringBuilder.append("第").append(i).append("行 - 只允许提报[草稿,提报中]的活动").append("\n");
            }
            // 提报失败/Need Review时，失败原因/BS Feedback不能为空
            if (needFailureMessageStatusShow.contains(vo.getSubmissionResultShow()) && StrUtil.isEmpty(vo.getFailureMessage())) {
                stringBuilder.append("第").append(i).append("行 - 提报失败/Need Review时，失败原因/BS Feedback不能为空").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 更新数据
        List<SomVcLdAndBd> updateList = new ArrayList<>();
        for (SomVcLdAndBdImportVo vo : list) {
            SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMap.get(vo.getDealName());
            // 提报成功 -> 即将开始
            if (VcPromotionStatusEnum.SUBMIT_SUCCESS.getDesc().equals(vo.getSubmissionResultShow())) {
                somVcLdAndBd.setStatus(VcPromotionStatusEnum.NOT_STARTED.getStatus());
                somVcLdAndBd.setFailureMessage(null);
            } else {
                somVcLdAndBd.setStatus(VcPromotionStatusEnum.SUBMIT_FAILED.getStatus());
                somVcLdAndBd.setFailureMessage(vo.getFailureMessage());
            }
            somVcLdAndBd.setModifyName(tokenUser.getUserName());
            somVcLdAndBd.setModifyNum(tokenUser.getJobNumber());
            somVcLdAndBd.setModifyTime(DateTime.now().toJdkDate());
            updateList.add(somVcLdAndBd);
        }
        somVcLdAndBdMapper.batchFeedbackSubmitResult(updateList);

        return "";
    }

    /**
     * 补充大促起止日期
     * @param somVcLdAndBdVo SomVcLdAndBdVo
     * @param tokenUser TokenUserInfo
     */
    public void supplyPromotionDate(SomVcLdAndBdVo somVcLdAndBdVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcLdAndBdVo)) {
            throw new ValidateException("数据存在空值，请检查数据！");
        }

        SomVcLdAndBd somVcLdAndBd = somVcLdAndBdMapper.createLambdaQuery()
                .andEq(SomVcLdAndBd::getSite, somVcLdAndBdVo.getSite())
                .andEq(SomVcLdAndBd::getAccountName, somVcLdAndBdVo.getAccountName())
                .andEq(SomVcLdAndBd::getVendorCode, somVcLdAndBdVo.getVendorCode())
                .andEq(SomVcLdAndBd::getDealType, somVcLdAndBdVo.getDealType())
                .andEq(SomVcLdAndBd::getCampaignType, somVcLdAndBdVo.getCampaignType())
                .single();
        if (ObjectUtil.isEmpty(somVcLdAndBd)) {
            throw new ValidateException("内容不存在");
        }

        somVcLdAndBd.setStartDate(somVcLdAndBdVo.getStartDate());
        somVcLdAndBd.setEndDate(somVcLdAndBdVo.getEndDate());
        somVcLdAndBdVo.setModifyNum(tokenUser.getJobNumber());
        somVcLdAndBdVo.setModifyName(tokenUser.getUserName());
        somVcLdAndBdVo.setModifyTime(DateTime.now().toJdkDate());
        somVcLdAndBdMapper.updateById(somVcLdAndBd);
    }
}
