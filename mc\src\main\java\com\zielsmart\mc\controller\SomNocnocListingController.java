package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomNocnocListingService;
import com.zielsmart.mc.vo.SomNocnocListingPageSearchVo;
import com.zielsmart.mc.vo.SomNocnocListingVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomNocnocListingController
 * @description
 * @date 2024-12-25 14:39:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somNocnocListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Nocnoc Listing信息表管理")
public class SomNocnocListingController extends BasicController{

    @Resource
    SomNocnocListingService somNocnocListingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomNocnocListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomNocnocListingVo>> queryByPage(@RequestBody SomNocnocListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somNocnocListingService.queryByPage(searchVo));
    }
}
