package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPricingHealthRecordWhite;
import com.zielsmart.mc.vo.SomPricingHealthRecordWhitePageSearchVo;
import com.zielsmart.mc.vo.SomPricingHealthRecordWhiteVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 * 丢购白名单
 * gen by 代码生成器 mapper 2024-11-25
 */
@SqlResource("somPricingHealthRecordWhite")
public interface SomPricingHealthRecordWhiteMapper extends BaseMapper<SomPricingHealthRecordWhite> {

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPricingHealthRecordWhiteVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPricingHealthRecordWhiteVo> queryByPage(@Param("searchVo") SomPricingHealthRecordWhitePageSearchVo searchVo, PageRequest pageRequest);

}
