package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.ZlccCatalogAmazonPreferenceSettingService;
import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingPageSearchVo;
import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title ZlccCatalogAmazonPreferenceSettingController
 * @description
 * @date 2023-12-01 10:49:23
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/zlccCatalogAmazonPreferenceSetting", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon类目配置")
public class ZlccCatalogAmazonPreferenceSettingController extends BasicController {

    @Resource
    ZlccCatalogAmazonPreferenceSettingService zlccCatalogAmazonPreferenceSettingService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<ZlccCatalogAmazonPreferenceSettingVo>> queryByPage(@RequestBody ZlccCatalogAmazonPreferenceSettingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(zlccCatalogAmazonPreferenceSettingService.queryByPage(searchVo));
    }


    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated ZlccCatalogAmazonPreferenceSettingVo zlccCatalogAmazonPreferenceSettingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        zlccCatalogAmazonPreferenceSettingService.save(zlccCatalogAmazonPreferenceSettingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody ZlccCatalogAmazonPreferenceSettingVo zlccCatalogAmazonPreferenceSettingVo) throws ValidateException {
        zlccCatalogAmazonPreferenceSettingService.delete(zlccCatalogAmazonPreferenceSettingVo);
        return ResultVo.ofSuccess(null);
    }
}
