package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomSupplySourceStoreConfigService;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomSupplySourceStoreConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somSupplySourceStoreConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "提货仓配置管理")
public class SomSupplySourceStoreConfigController extends BasicController {

    @Resource
    SomSupplySourceStoreConfigService somSupplySourceStoreConfigService;

    /**
     * queryDetail
     * 查看
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomSupplySourceStoreConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/queryDetail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomSupplySourceStoreConfigExtVo> queryDetail(@RequestBody SomSupplySourceStoreConfigSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somSupplySourceStoreConfigService.queryDetail(searchVo));
    }

    /**
     * pushSupplyConfig
     * 推送提货仓配置
     * @param pushVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "推送提货仓配置")
    @PostMapping(value = "/pushSupplyConfig")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> pushSupplyConfig(@RequestBody SomSupplySourceStoreConfigSearchVo pushVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somSupplySourceStoreConfigService.pushSupplyConfig(pushVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
