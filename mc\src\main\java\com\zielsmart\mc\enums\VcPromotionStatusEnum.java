package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * VC营销活动状态枚举整合
 *      注：可能有的状态在营销活动中不存在/不做处理，但是要保证[状态<=>数值]是一致的
 *      不要出现：状态=10 在某个VC营销活动中表示 A，在另一个VC营销活动中表示 B 的情况
 */
@AllArgsConstructor
public enum VcPromotionStatusEnum {
    // -------- 10~19（草稿） -------
    DRAFT(10, "草稿"),
    // --------- 20~29（审批） -------
    // 审批中
    APPROVAL_ING(20, "审批中"),
    APPROVAL_SUCCESS(21, "审批通过"),
    APPROVAL_FAILED(29, "审批未通过"),

    // --------- 30~39（提报） -------
    SUBMIT_SUCCESS(30, "提报成功"),
    SUBMITTING(31, "提报中"),
    SUBMIT_NEED_REVIEW(32, "Need Review"),
    SUBMIT_FAILED(39, "提报失败"),

    // --------- 40~49（未开始） -------
    NOT_STARTED(40, "未开始"),
    NEED_ATTENTION(41, "需要关注"),

    // --------- 70~79（进行中） -------
    ONGOING(70, "进行中"),

    // --------- 80~89（结束） -------
    ENDED(80, "已结束"),

    // --------- 90~99（取消） -------
    CANCEL_ING(90, "取消中"),
    CANCELED_SUCCESS(91, "取消失败"),
    CANCELED(92, "已取消"),

    //  --------- 110（运营已提报） -------
    OPERATION_SUBMIT(110, "运营已提报"),

    ;

    private final Integer status;
    private final String desc;
    public Integer getStatus() {
        return status;
    }
    public String getDesc() {
        return desc;
    }


    /**
     * 获取非终态
     *  营销活动共享状态值，非终态所有的营销活动都是非终态
     *
     * @return 状态集合
     */
    public static List<Integer> getNonFinalState() {
        // 目前终态有：APPROVAL_FAILED(审批失败), SUBMIT_FAILED(提交失败), CANCELED(已取消), ENDED(已结束), OPERATION_SUBMIT(运营已提报)
        List<Integer> finalStatus = Arrays.asList(APPROVAL_FAILED.getStatus(), SUBMIT_FAILED.getStatus(), CANCELED.getStatus(), ENDED.getStatus(), OPERATION_SUBMIT.getStatus());
        return Arrays.stream(VcPromotionStatusEnum.values())
                .map(VcPromotionStatusEnum::getStatus)
                .filter(state -> !finalStatus.contains(state))
                .collect(Collectors.toList());
    }

    /**
     * 获取允许编辑的状态
     *  目前逻辑统一，如果后期出现不同活动不同状态允许取消的情况，需要修改此处
     */
    public static List<Integer> getAllowEditStatus() {
        // [草稿,需要关注]状态才可以编辑
        return Arrays.asList(DRAFT.getStatus(), NEED_ATTENTION.getStatus());
    }

    /**
     * 获取允许取消的状态
     *  目前逻辑统一，如果后期出现不同活动不同状态允许取消的情况，需要修改此处
     */
    public static List<Integer> getAllowCancelStatus() {
        // [进行中,需要关注,未开始]的活动允许取消
        return Arrays.asList(ONGOING.getStatus(), NEED_ATTENTION.getStatus(), NOT_STARTED.getStatus());
    }

    /**
     * 获取允许提报的状态
     *  目前逻辑统一，如果后期出现不同活动不同状态允许提报的情况，需要修改此处
     */
    public static List<Integer> getAllowSubmitStatus() {
        // [草稿,提报中]的活动允许提报
        return Arrays.asList(DRAFT.getStatus(),SUBMITTING.getStatus());
    }

    /**
     * 获取允许删除的状态
     *  目前逻辑统一，如果后期出现不同活动不同状态允许删除的情况，需要修改此处
     */
    public static List<Integer> getAllowDeleteStatus() {
        // [草稿]的活动允许删除
        return Collections.singletonList(DRAFT.getStatus());
    }
}
