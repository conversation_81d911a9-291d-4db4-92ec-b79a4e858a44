package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 三方渠道营销活动
 * gen by 代码生成器 2025-05-13
 */

@Table(name = "mc.som_three_channel_promotion")
public class SomThreeChannelPromotion implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 店铺ID
     */
    @Column("account_id")
    private String accountId;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 活动类型
     */
    @Column("promotion_type")
    private String promotionType;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 产品SKU编码
     */
    @Column("sku")
    private String sku;
    /**
     * 售价
     */
    @Column("sell_price")
    private BigDecimal sellPrice;
    /**
     * 折扣百分比
     */
    @Column("discount")
    private BigDecimal discount;
    /**
     * 秒杀价
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * 花费
     */
    @Column("spend")
    private BigDecimal spend;
    /**
     * 成交费占比
     */
    @Column("success_fee_ratio")
    private BigDecimal successFeeRatio;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 起始日期
     */
    @Column("start_date")
    private Date startDate;
    /**
     * 截止日期
     */
    @Column("end_date")
    private Date endDate;
    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     */
    @Column("apply_reason")
    private Integer applyReason;
    /**
     * 自定义申请原因
     */
    @Column("custom_reason")
    private String customReason;
    /**
     * BU组编码
     */
    @Column("product_group_code")
    private String productGroupCode;
    /**
     * BU组名称
     */
    @Column("product_group_name")
    private String productGroupName;
    /**
     * 业务组
     */
    @Column("sales_group_code")
    private String salesGroupCode;
    /**
     * 业务组名称
     */
    @Column("sales_group_name")
    private String salesGroupName;
    /**
     * 销售负责人工号
     */
    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;
    /**
     * 销售负责人姓名
     */
    @Column("sales_group_empt_name")
    private String salesGroupEmptName;
    /**
     * 业务助理工号
     */
    @Column("operation_empt_code")
    private String operationEmptCode;
    /**
     * 业务助理姓名
     */
    @Column("operation_empt_name")
    private String operationEmptName;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 最后修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 最后修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;
    /**
     * 综合单名称
     */
    @Column("synthesis_bill_name")
    private String synthesisBillName;

    public SomThreeChannelPromotion() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 店铺ID
     *
     * @return
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 店铺ID
     *
     * @param accountId
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 活动类型
     *
     * @return
     */
    public String getPromotionType() {
        return promotionType;
    }

    /**
     * 活动类型
     *
     * @param promotionType
     */
    public void setPromotionType(String promotionType) {
        this.promotionType = promotionType;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getSellPrice() {
        return sellPrice;
    }

    /**
     * 售价
     *
     * @param sellPrice
     */
    public void setSellPrice(BigDecimal sellPrice) {
        this.sellPrice = sellPrice;
    }

    /**
     * 折扣百分比
     *
     * @return
     */
    public BigDecimal getDiscount() {
        return discount;
    }

    /**
     * 折扣百分比
     *
     * @param discount
     */
    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    /**
     * 秒杀价
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 秒杀价
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * 花费
     *
     * @return
     */
    public BigDecimal getSpend() {
        return spend;
    }

    /**
     * 花费
     *
     * @param spend
     */
    public void setSpend(BigDecimal spend) {
        this.spend = spend;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 起始日期
     *
     * @return
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 起始日期
     *
     * @param startDate
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 截止日期
     *
     * @return
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 截止日期
     *
     * @param endDate
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     *
     * @return
     */
    public Integer getApplyReason() {
        return applyReason;
    }

    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     *
     * @param applyReason
     */
    public void setApplyReason(Integer applyReason) {
        this.applyReason = applyReason;
    }

    /**
     * 自定义申请原因
     *
     * @return
     */
    public String getCustomReason() {
        return customReason;
    }

    /**
     * 自定义申请原因
     *
     * @param customReason
     */
    public void setCustomReason(String customReason) {
        this.customReason = customReason;
    }

    /**
     * BU组编码
     *
     * @return
     */
    public String getProductGroupCode() {
        return productGroupCode;
    }

    /**
     * BU组编码
     *
     * @param productGroupCode
     */
    public void setProductGroupCode(String productGroupCode) {
        this.productGroupCode = productGroupCode;
    }

    /**
     * BU组名称
     *
     * @return
     */
    public String getProductGroupName() {
        return productGroupName;
    }

    /**
     * BU组名称
     *
     * @param productGroupName
     */
    public void setProductGroupName(String productGroupName) {
        this.productGroupName = productGroupName;
    }

    /**
     * 业务组
     *
     * @return
     */
    public String getSalesGroupCode() {
        return salesGroupCode;
    }

    /**
     * 业务组
     *
     * @param salesGroupCode
     */
    public void setSalesGroupCode(String salesGroupCode) {
        this.salesGroupCode = salesGroupCode;
    }

    /**
     * 业务组名称
     *
     * @return
     */
    public String getSalesGroupName() {
        return salesGroupName;
    }

    /**
     * 业务组名称
     *
     * @param salesGroupName
     */
    public void setSalesGroupName(String salesGroupName) {
        this.salesGroupName = salesGroupName;
    }

    /**
     * 销售负责人工号
     *
     * @return
     */
    public String getSalesGroupEmptCode() {
        return salesGroupEmptCode;
    }

    /**
     * 销售负责人工号
     *
     * @param salesGroupEmptCode
     */
    public void setSalesGroupEmptCode(String salesGroupEmptCode) {
        this.salesGroupEmptCode = salesGroupEmptCode;
    }

    /**
     * 销售负责人姓名
     *
     * @return
     */
    public String getSalesGroupEmptName() {
        return salesGroupEmptName;
    }

    /**
     * 销售负责人姓名
     *
     * @param salesGroupEmptName
     */
    public void setSalesGroupEmptName(String salesGroupEmptName) {
        this.salesGroupEmptName = salesGroupEmptName;
    }

    /**
     * 业务助理工号
     *
     * @return
     */
    public String getOperationEmptCode() {
        return operationEmptCode;
    }

    /**
     * 业务助理工号
     *
     * @param operationEmptCode
     */
    public void setOperationEmptCode(String operationEmptCode) {
        this.operationEmptCode = operationEmptCode;
    }

    /**
     * 业务助理姓名
     *
     * @return
     */
    public String getOperationEmptName() {
        return operationEmptName;
    }

    /**
     * 业务助理姓名
     *
     * @param operationEmptName
     */
    public void setOperationEmptName(String operationEmptName) {
        this.operationEmptName = operationEmptName;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 最后修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 最后修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 最后修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public BigDecimal getSuccessFeeRatio() {
        return successFeeRatio;
    }

    public void setSuccessFeeRatio(BigDecimal successFeeRatio) {
        this.successFeeRatio = successFeeRatio;
    }

    public String getSynthesisBillName() {
        return synthesisBillName;
    }

    public void setSynthesisBillName(String synthesisBillName) {
        this.synthesisBillName = synthesisBillName;
    }
}
