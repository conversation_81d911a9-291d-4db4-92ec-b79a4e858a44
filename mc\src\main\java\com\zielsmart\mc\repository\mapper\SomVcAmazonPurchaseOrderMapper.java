package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderPageSearchVo;
import com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-08-16
*/

@SqlResource("somVcAmazonPurchaseOrder")
public interface SomVcAmazonPurchaseOrderMapper extends BaseMapper<SomVcAmazonPurchaseOrder> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomVcAmazonPurchaseOrderVo> queryByPage(@Param("searchVo")SomVcAmazonPurchaseOrderPageSearchVo searchVo, PageRequest pageRequest);
}
