package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McWalmartWarehouseConfig;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.vo.McWalmartWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWalmartWarehouseConfigVo;
import com.zielsmart.mc.vo.McWareHouseAndSlVo;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-12-08
 */

@SqlResource("mcWalmartWarehouseConfig")
public interface McWalmartWarehouseConfigMapper extends BaseMapper<McWalmartWarehouseConfig> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McWalmartWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<McWalmartWarehouseConfigVo> queryByPage(@Param("searchVo") McWalmartWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * findWarehouseAndSl
     * 查询仓库库区信息
     * @param platformWarehouseCodes
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWareHouseAndSlVo>}
     * <AUTHOR>
     * @history
     */
    List<McWareHouseAndSlVo> findWarehouseAndSl(@Param("platformWarehouseCodes")List<String> platformWarehouseCodes);

    /**
     * queryConfigurableWarehouses
     * 查询可配置仓库
     * @param marketCode
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McWarehouse>}
     * <AUTHOR>
     * @history
     */
    List<McWarehouse> queryConfigurableWarehouses(@Param("marketCode") String marketCode);
}
