package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomToSellStoreInfoService;
import com.zielsmart.mc.vo.SomToSellStoreInfoPageSearchVo;
import com.zielsmart.mc.vo.SomToSellStoreInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomToSellStoreInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somToSellStoreInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "跟卖者管理")
public class SomToSellStoreInfoController extends BasicController {

    @Resource
    SomToSellStoreInfoService somToSellStoreInfoService;

    /**
     * addOrEdit
     *
     * @param addOrEditVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加或编辑")
    @PostMapping(value = "/addOrEdit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomToSellStoreInfoVo addOrEditVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somToSellStoreInfoService.addOrEdit(addOrEditVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param pageSearchVo
     * @return {@link ResultVo< PageVo< SomToSellStoreInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomToSellStoreInfoVo>> queryByPage(@RequestBody SomToSellStoreInfoPageSearchVo pageSearchVo) {
        return ResultVo.ofSuccess(somToSellStoreInfoService.queryByPage(pageSearchVo));
    }

    /**
     * delete
     *
     * @param deleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomToSellStoreInfoVo deleteVo) throws ValidateException {
        somToSellStoreInfoService.delete(deleteVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * downloadTemplate
     * 下载导入模板
     * @param response
     * @throws {@link IOException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping("/download-template")
    public String downloadTemplate(HttpServletResponse response) throws IOException {
        return "forward:/static/excel/StoreInfoTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"跟卖者店铺ID", "跟卖者店铺名称"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomToSellStoreInfoVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomToSellStoreInfoVo.class,importParams);
        } catch (Exception e) {
            if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        List<SomToSellStoreInfoVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        String str = somToSellStoreInfoService.importExcel(list, tokenUser);
        if(StrUtil.isEmpty(str)){
            return ResultVo.ofSuccess();
        }else {
            return ResultVo.ofFail(str);
        }
    }
}
