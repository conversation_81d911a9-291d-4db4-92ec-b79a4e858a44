package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomAmazonVcComparativePriceRecordHandleRecord;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomAmazonVcComparativePriceRecordHandleRecordMapper;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordHandleImportVo;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordHandleRecordVo;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordHandleVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-01-21 18:12:16
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomAmazonVcComparativePriceRecordHandleRecordService {
    
    @Resource
    private SomAmazonVcComparativePriceRecordHandleRecordMapper comparativePriceRecordHandleRecordMapper;

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    /**
     * queryByCid
     *
     * @param cid 入参
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordHandleRecordVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomAmazonVcComparativePriceRecordHandleRecordVo> queryByCid(String cid) {
        if (StrUtil.isEmpty(cid)) {
            return null;
        }
        List<SomAmazonVcComparativePriceRecordHandleRecordVo> recordVos = comparativePriceRecordHandleRecordMapper.queryByCid(cid);
        // 处理数据
        handleComparativePriceRecord(recordVos);
        return recordVos;
    }

    /**
     * 新增比价处理日志
     *
     * @param comparativePriceRecordHandleVo 入参
     * @param tokenUser 当前登陆人
     */
    public void save(SomAmazonVcComparativePriceRecordHandleVo comparativePriceRecordHandleVo, TokenUserInfo tokenUser) {
        SomAmazonVcComparativePriceRecordHandleRecord handleRecord = new SomAmazonVcComparativePriceRecordHandleRecord();
        handleRecord.setAid(IdUtil.fastSimpleUUID());
        handleRecord.setCid(comparativePriceRecordHandleVo.getAid());
        handleRecord.setHandleStatus(comparativePriceRecordHandleVo.getHandleStatus());
        handleRecord.setHandleType(10);
        handleRecord.setComparativeReason(comparativePriceRecordHandleVo.getComparativeReason());
        handleRecord.setRemark(comparativePriceRecordHandleVo.getRemark());
        handleRecord.setHandlePersonCode(comparativePriceRecordHandleVo.getHandlePersonCode());
        handleRecord.setHandlePersonName(comparativePriceRecordHandleVo.getHandlePersonName());
        handleRecord.setCreateTime(DateTime.now().toJdkDate());
        handleRecord.setTaskChargePersonCode(tokenUser.getJobNumber());
        handleRecord.setTaskChargePersonName(tokenUser.getUserName());
        comparativePriceRecordHandleRecordMapper.insert(handleRecord);
    }

    /**
     * 批量新增比价处理日志
     *
     * @param handleRecords 处理日志
     */
    public void batchInsert(List<SomAmazonVcComparativePriceRecordHandleRecord> handleRecords) {
        if (CollUtil.isEmpty(handleRecords)) {
            return;
        }
        comparativePriceRecordHandleRecordMapper.insertBatch(handleRecords);
    }

    /**
     * importVo -> handleRecord
     *
     * @param importVo 导入行记录
     * @param tokenUser 当前登陆人
     * @return handleRecord
     */
    public SomAmazonVcComparativePriceRecordHandleRecord transformImportVoToHandleRecord(SomAmazonVcComparativePriceRecordHandleImportVo importVo, TokenUserInfo tokenUser) {
        SomAmazonVcComparativePriceRecordHandleRecord handleRecord = new SomAmazonVcComparativePriceRecordHandleRecord();
        handleRecord.setCid(importVo.getCid());
        handleRecord.setAid(IdUtil.fastSimpleUUID());
        handleRecord.setHandleStatus(importVo.getHandleStatus());
        handleRecord.setHandleType(10);
        handleRecord.setComparativeReason(importVo.getComparativeReason());
        handleRecord.setRemark(importVo.getRemark());
        handleRecord.setTaskChargePersonCode(tokenUser.getJobNumber());
        handleRecord.setTaskChargePersonName(tokenUser.getUserName());
        handleRecord.setHandlePersonCode(importVo.getHandlePersonCode());
        handleRecord.setHandlePersonName(importVo.getHandlePersonName());
        handleRecord.setCreateTime(DateTime.now().toJdkDate());
        return handleRecord;
    }

    /**
     * 查询比价记录处理相关字典集合
     *  VcComparativeReason：比价原因
     *  VcComparativePriceHandleStatus：比价处理状态
     *  VcComparativeHandleType：比价处理方式
     *  Platform：比价平台
     *
     * @return 字典
     */
    public List<McDictionaryInfo> queryComparativePriceRecordHandleDictionary() {
        List<String> list = Arrays.asList("VcComparativeReason", "VcComparativePriceHandleStatus", "VcComparativeHandleType", "Platform");
        return mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();
    }

    /**
     * 处理比价操作记录
     *
     * @param recordVos 操作记录
     */
    private void handleComparativePriceRecord(List<SomAmazonVcComparativePriceRecordHandleRecordVo> recordVos) {
        if (CollUtil.isEmpty(recordVos)) {
            return;
        }
        // 查询字典
        List<String> list = Arrays.asList("VcComparativeReason", "VcComparativePriceHandleStatus", "VcComparativePriceHandleType");
        List<McDictionaryInfo> mcDictionaryInfos = mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", list).select();
        Map<String, Map<String, String>> mcDictionaryInfoMap = mcDictionaryInfos.stream()
                .collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode,
                        Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable)));
        for (SomAmazonVcComparativePriceRecordHandleRecordVo recordVo : recordVos) {
            // 处理状态
            Map<String, String> handleStatusMap = mcDictionaryInfoMap.get("VcComparativePriceHandleStatus");
            recordVo.setHandleStatusDesc(CollUtil.isEmpty(handleStatusMap) ? null : handleStatusMap.get(String.valueOf(recordVo.getHandleStatus())));
            // 处理类型
            Map<String, String> handleTypeMap = mcDictionaryInfoMap.get("VcComparativePriceHandleType");
            recordVo.setHandleTypeDesc(CollUtil.isEmpty(handleTypeMap) ? null : handleTypeMap.get(String.valueOf(recordVo.getHandleType())));
            // 比价原因
            Map<String, String> comparativeReasonMap = mcDictionaryInfoMap.get("VcComparativeReason");
            recordVo.setComparativeReasonDesc(CollUtil.isEmpty(comparativeReasonMap) ? null : comparativeReasonMap.get(String.valueOf(recordVo.getComparativeReason())));
        }
    }


}
