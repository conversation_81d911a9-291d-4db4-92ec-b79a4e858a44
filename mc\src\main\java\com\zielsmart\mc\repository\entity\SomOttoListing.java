package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* OTTO listing列表
* gen by 代码生成器 2024-04-26
*/

@Table(name="mc.som_otto_listing")
public class SomOttoListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 产品参考
	 */
	@Column("product_reference")
	private String productReference ;
	/**
	 * 展示码
	 */
	@Column("sku")
	private String sku ;
	/**
	 * EAN
	 */
	@Column("ean")
	private String ean ;
	/**
	 * 展示码状态
	 */
	@Column("status")
	private String status ;
	/**
	 * 产品描述
	 */
	@Column("product_description")
	private String productDescription ;
	/**
	 * 多媒体资源
	 */
	@Column("media_assets")
	private String mediaAssets ;
	/**
	 * 订单限制
	 */
	@Column("order")
	private String order ;
	/**
	 * 发货方式
	 */
	@Column("delivery")
	private String delivery ;
	/**
	 * 价格信息
	 */
	@Column("pricing")
	private String pricing ;
	/**
	 * 物流信息
	 */
	@Column("logistics")
	private String logistics ;
	/**
	 * 下载时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	public SomOttoListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 产品参考
	*@return
	*/
	public String getProductReference(){
		return  productReference;
	}
	/**
	* 产品参考
	*@param  productReference
	*/
	public void setProductReference(String productReference ){
		this.productReference = productReference;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* 展示码
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* EAN
	*@return
	*/
	public String getEan(){
		return  ean;
	}
	/**
	* EAN
	*@param  ean
	*/
	public void setEan(String ean ){
		this.ean = ean;
	}
	/**
	* 展示码状态
	*@return
	*/
	public String getStatus(){
		return  status;
	}
	/**
	* 展示码状态
	*@param  status
	*/
	public void setStatus(String status ){
		this.status = status;
	}
	/**
	* 产品描述
	*@return
	*/
	public String getProductDescription(){
		return  productDescription;
	}
	/**
	* 产品描述
	*@param  productDescription
	*/
	public void setProductDescription(String productDescription ){
		this.productDescription = productDescription;
	}
	/**
	* 多媒体资源
	*@return
	*/
	public String getMediaAssets(){
		return  mediaAssets;
	}
	/**
	* 多媒体资源
	*@param  mediaAssets
	*/
	public void setMediaAssets(String mediaAssets ){
		this.mediaAssets = mediaAssets;
	}
	/**
	* 订单限制
	*@return
	*/
	public String getOrder(){
		return  order;
	}
	/**
	* 订单限制
	*@param  order
	*/
	public void setOrder(String order ){
		this.order = order;
	}
	/**
	* 发货方式
	*@return
	*/
	public String getDelivery(){
		return  delivery;
	}
	/**
	* 发货方式
	*@param  delivery
	*/
	public void setDelivery(String delivery ){
		this.delivery = delivery;
	}
	/**
	* 价格信息
	*@return
	*/
	public String getPricing(){
		return  pricing;
	}
	/**
	* 价格信息
	*@param  pricing
	*/
	public void setPricing(String pricing ){
		this.pricing = pricing;
	}
	/**
	* 物流信息
	*@return
	*/
	public String getLogistics(){
		return  logistics;
	}
	/**
	* 物流信息
	*@param  logistics
	*/
	public void setLogistics(String logistics ){
		this.logistics = logistics;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 下载时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
