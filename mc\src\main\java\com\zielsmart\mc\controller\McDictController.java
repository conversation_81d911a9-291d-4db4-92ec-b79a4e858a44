package com.zielsmart.mc.controller;

import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.DictSerializeService;
import com.zielsmart.mc.service.McDictService;
import com.zielsmart.mc.vo.dict.McDictAddVo;
import com.zielsmart.mc.vo.dict.McDictPageSearchVo;
import com.zielsmart.mc.vo.dict.McDictionaryInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/dict", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "字典管理")
public class McDictController extends BasicController {
    @Resource
    McDictService mcDictService;

    @Resource
    private DictSerializeService dictSerializeService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo< McDictionaryInfoVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McDictionaryInfoVo>> queryByPage(@RequestBody McDictPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcDictService.queryByPage(searchVo));
    }

    /**
     * save
     * 添加
     *
     * @param mcDictAddVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link RuntimeException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加字典")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McDictAddVo mcDictAddVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcDictService.save(mcDictAddVo, tokenUser);
        dictSerializeService.clearDictList();
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "修改字典")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McDictionaryInfoVo mcDictionaryInfoVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcDictService.update(mcDictionaryInfoVo, tokenUser);
        dictSerializeService.clearDictList();
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除字典
     *
     * @param aidList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除字典")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McDictionaryInfoVo mcDictionaryInfoVo) throws ValidateException {
        mcDictService.delete(mcDictionaryInfoVo);
        dictSerializeService.clearDictList();
        return ResultVo.ofSuccess(null);
    }

    /**
     * getDict
     * 根据key获取字典
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<java.lang.String>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据key获取字典")
    @PostMapping(value = "/get-dict")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McDictionaryInfoVo>> getDict(@RequestBody JSONObject json) throws ValidateException {
        return ResultVo.ofSuccess(mcDictService.getDict(json));
    }


    @Operation(summary = "根据key获取单个字典")
    @PostMapping(value = "/get-dict-single")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<McDictionaryInfoVo> getDictSingle(@RequestBody JSONObject json) throws ValidateException {
        return ResultVo.ofSuccess(mcDictService.getDictSingle(json));
    }

    /**
     * getDict
     * 根据选项value获取字典
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<java.lang.String>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据选项value获取字典")
    @PostMapping(value = "/get-by-value")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McDictionaryInfoVo>> getByValue(@RequestBody McDictionaryInfoVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcDictService.getByValue(searchVo));
    }
}
