package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomMiraviaWarehouseConfigService;
import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseConfigVo;
import com.zielsmart.mc.vo.SomMiraviaWarehouseVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomMiraviaWarehouseConfigController
 * @description
 * @date 2024-04-29 12:09:13
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somMiraviaWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Miravia可售仓库配置表管理")
public class SomMiraviaWarehouseConfigController extends BasicController {

    @Resource
    SomMiraviaWarehouseConfigService somMiraviaWarehouseConfigService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomMiraviaWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomMiraviaWarehouseConfigVo>> queryByPage(@RequestBody SomMiraviaWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somMiraviaWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     * 添加
     *
     * @param somMiraviaWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somMiraviaWarehouseConfigService.save(somMiraviaWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     * 修改
     *
     * @param somMiraviaWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somMiraviaWarehouseConfigService.update(somMiraviaWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param somMiraviaWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomMiraviaWarehouseConfigVo somMiraviaWarehouseConfigVo) throws ValidateException {
        somMiraviaWarehouseConfigService.delete(somMiraviaWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryList
     * 获取平台仓库列表
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomMiraviaWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取平台仓库列表")
    @PostMapping(value = "/queryList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomMiraviaWarehouseVo>> queryList() {
        return ResultVo.ofSuccess(somMiraviaWarehouseConfigService.queryList());
    }
}
