package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McFeishuMsgConfigPageSearchVo;
import com.zielsmart.mc.vo.McFeishuMsgConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-08-31
*/

@SqlResource("mcFeishuMsgConfig")
public interface McFeishuMsgConfigMapper extends BaseMapper<McFeishuMsgConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McFeishuMsgConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McFeishuMsgConfigVo> queryByPage(@Param("searchVo")McFeishuMsgConfigPageSearchVo searchVo, PageRequest pageRequest);
}
