package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description SomTemuCrossGoodsService
 * @date 2025-06-26 15:52:12
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuCrossGoodsService {

    @Resource
    private SomTemuCrossGoodsMapper somTemuCrossGoodsMapper;

    @Resource
    private SomTemuCrossSkuMapper somTemuCrossSkuMapper;

    @Resource
    private SomTemuCrossSupplierPriceMapper supplierPriceMapper;

    @Resource
    private SomStorageLocationMapper storageLocationMapper;

    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    @Resource
    private SomTemuFbaBlackListMapper somTemuFbaBlackListMapper;

    @Resource
    private SomTemuClearanceListMapper somTemuClearanceListMapper;

    @Resource
    private SomTemuCrossWarehouseConfigMapper somTemuCrossWarehouseConfigMapper;

    @Resource
    private SomTemuWarehouseMapper somTemuWarehouseMapper;

    @Resource
    private McStockInfoMapper mcStockInfoMapper;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return PageVo<SomTemuCrossGoodsVo>
     */
    public PageVo<SomTemuCrossGoodsVo> queryByPage(SomTemuCrossGoodsPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuCrossGoodsVo> pageResult = somTemuCrossGoodsMapper.queryByPage(searchVo, pageRequest);
        // 查询子
        List<SomTemuCrossGoodsVo> goodsVos = pageResult.getList();
        if (CollUtil.isNotEmpty(goodsVos)) {
            List<String> goodsIds = goodsVos.stream().map(SomTemuCrossGoodsVo::getAid).collect(Collectors.toList());
            List<SomTemuCrossSkuVo> crossSkuVos = somTemuCrossSkuMapper.querySku(goodsIds);
            // 查询供货价
            if (CollUtil.isNotEmpty(crossSkuVos)) {
                List<Long> productSkuIds = crossSkuVos.stream().map(SomTemuCrossSkuVo::getProductSkuId).collect(Collectors.toList());
                List<Long> productIds = crossSkuVos.stream().map(SomTemuCrossSkuVo::getProductId).collect(Collectors.toList());
                List<String> accountIds = crossSkuVos.stream().map(SomTemuCrossSkuVo::getAccountId).distinct().collect(Collectors.toList());
                List<SomTemuCrossSkuSupplierPriceVo> supplierPriceVos = supplierPriceMapper.querySupplierPrice(accountIds, productIds, productSkuIds);
                Map<String, SomTemuCrossSkuSupplierPriceVo> supplierPriceMap = supplierPriceVos.stream().collect(Collectors.toMap(
                        x -> StrUtil.concat(true, x.getSite(), x.getAccountId(), String.valueOf(x.getProductId()), String.valueOf(x.getProductSkuId())), Function.identity(), (x1, x2) -> x1
                ));
                crossSkuVos.forEach(crossSkuVo -> {
                    String key = StrUtil.concat(true, crossSkuVo.getSite(), crossSkuVo.getAccountId(), String.valueOf(crossSkuVo.getProductId()), String.valueOf(crossSkuVo.getProductSkuId()));
                    SomTemuCrossSkuSupplierPriceVo supplierPriceVo = supplierPriceMap.get(key);
                    BigDecimal supplierPrice = null;
                    if (supplierPriceVo != null && supplierPriceVo.getSupplierPrice() != null) {
                        supplierPrice = supplierPriceVo.getSupplierPrice().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_UP);
                    }
                    crossSkuVo.setSupplierPrice(supplierPrice);
                });
                // 根据 goodsId 分组
                Map<String, List<SomTemuCrossSkuVo>> skuMap = crossSkuVos.stream().collect(Collectors.groupingBy(SomTemuCrossSkuVo::getGoodsId, Collectors.toList()));
                goodsVos.forEach(data -> data.setSkus(skuMap.get(data.getAid())));
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuCrossGoodsVo.class, searchVo);
    }

    public PageVo<SomTemuListingReport> stockReport(SomTemuCrossGoodsPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuListingReport> pageResult = somTemuCrossGoodsMapper.stockReport(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomStorageLocation> storageLocationList = storageLocationMapper.all();
            Map<String, String> slMap = storageLocationList.stream().collect(Collectors.toMap(SomStorageLocation::getslCode, SomStorageLocation::getslName, (x1, x2) -> x1));
            Map<String, String> whMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(McWarehouse::getWarehouseCode, McWarehouse::getWarehouseName, (x1, x2) -> x1));

            //查询所有的Temu后台的仓库
            List<SomTemuWarehouse> temuWarehouseList = somTemuWarehouseMapper.all();
            Map<String, String> warehouseNameMap = temuWarehouseList.stream().collect(Collectors.toMap(x -> x.getWarehouseId(), v -> v.getWarehouseName(), (x1, x2) -> x1));

            // 获取Temu仓库配置
            List<SomTemuCrossWarehouseConfig> warehouseList = somTemuCrossWarehouseConfigMapper.all();
            Set<String> allTemuWarehouseCode = warehouseList.stream().map(SomTemuCrossWarehouseConfig::getUseableWarehouseCode).collect(Collectors.toSet());
            Map<String, List<SomTemuCrossWarehouseConfig>> warehouseMap = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getAccountId() + e.getSite()+e.getTemuWarehouseId()));

            // 获取Temu清仓配置
            List<SomTemuClearanceList> clearanceLists = somTemuClearanceListMapper.all();
            List<String> clearanceKeys = clearanceLists.stream().map(data -> data.getAccountId() + data.getProductSkuId()).collect(Collectors.toList());
            // 获取人工指定发货方式配置
            List<SomTemuFbaBlackList> allBlackLists = somTemuFbaBlackListMapper.createLambdaQuery().andEq("platform", "Temu").andEq("delete_flag", 10).select();
            // 获取所有仓库编码去重
            List<String> warehouseCodes = allBlackLists.stream().flatMap(e -> {
                String useableWarehouseCode = e.getUseableWarehouseStorage();
                if (StrUtil.isBlank(useableWarehouseCode) || useableWarehouseCode.length() < 3) {
                    return Stream.empty();
                }
                JSONArray array = JSONUtil.parseArray(useableWarehouseCode);
                List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
                return useableWarehouseList.stream().map(SomTemuFbaBlackListVo.UseableWarehouse::getWarehouseCode);
            }).distinct().collect(Collectors.toList());


            allTemuWarehouseCode.addAll(warehouseCodes);
            Map<String, SomTemuFbaBlackList> blackListMap = allBlackLists.stream().collect(Collectors.toMap(f -> f.getAccountId() + f.getPlatform() + f.getSite() + f.getSellerSku(), Function.identity(), (x1, x2) -> x1));
            // 获取 Temu 所有仓库的库存
            List<McStockInfo> stockList = mcStockInfoMapper.createLambdaQuery().andIn("warehouse_code", allTemuWarehouseCode).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));

            for (SomTemuListingReport report : pageResult.getList()) {
                //设置temu仓库名称
                report.setTemuWarehouseName(warehouseNameMap.get(report.getTemuWarehouseId()));

                // 判断是否是清仓商品
                boolean isClearance = clearanceKeys.contains(report.getAccountId() + report.getProductSkuId());
                report.setClearanceFlag(isClearance);
                //判断展示码是否存在人工指定发货方式配置，如果存在，可售库存按照配置的发货仓库&库区展示库存

                if (blackListMap.containsKey(report.getAccountId() + report.getPlatform() + report.getSite() + report.getDisplayProductCode())) {
                    SomTemuFbaBlackList blackList = blackListMap.get(report.getAccountId() + report.getPlatform() + report.getSite() + report.getDisplayProductCode());
                    if (StrUtil.isNotBlank(blackList.getUseableWarehouseStorage())) {
                        JSONArray array = JSONUtil.parseArray(blackList.getUseableWarehouseStorage());
                        List<SomTemuFbaBlackListVo.UseableWarehouse> useableWarehouseList = JSONUtil.toList(array, SomTemuFbaBlackListVo.UseableWarehouse.class);
                        List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
                        int totalStock = 0;
                        for (SomTemuFbaBlackListVo.UseableWarehouse warehouse : useableWarehouseList) {
                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
                            warehouseInventory.setWarehouseName(whMap.get(warehouse.getWarehouseCode()));
                            warehouseInventory.setSlName(slMap.get(warehouse.getSlCode()));
                            McStockInfo mcStockInfo = stockMap.get(warehouse.getWarehouseCode() + warehouse.getSlCode() + report.getProductMainCode());
                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
                            totalStock += stock;
                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
                            list.add(warehouseInventory);
                        }
                        // 如果清仓产品中包含此产品，则不需要减去安全库存
                        if (isClearance) {
                            report.setStock(Math.max(totalStock, 0));
                        } else {
                            report.setStock(Math.max(totalStock - (report.getSafetyStock() == null ? 0 : report.getSafetyStock()), 0));
                        }
                        report.setList(list);
                    }
                } else {
                    //根据站点获取仓库 库区
                    List<SomTemuCrossWarehouseConfig> somTemuWarehouseConfigs = warehouseMap.get(report.getAccountId() + report.getSite()+report.getTemuWarehouseId());
                    List<SomTemuListingReport.WarehouseInventory> list = new ArrayList<>();
                    if (null != somTemuWarehouseConfigs && !somTemuWarehouseConfigs.isEmpty()) {
                        BigDecimal totalStock = BigDecimal.ZERO;
                        for (SomTemuCrossWarehouseConfig config : somTemuWarehouseConfigs) {
                            SomTemuListingReport.WarehouseInventory warehouseInventory = new SomTemuListingReport.WarehouseInventory();
                            warehouseInventory.setWarehouseName(whMap.get(config.getUseableWarehouseCode()));
                            warehouseInventory.setSlName(slMap.get(config.getUseableStorageCode()));
                            McStockInfo mcStockInfo = stockMap.get(config.getUseableWarehouseCode() + config.getUseableStorageCode() + report.getProductMainCode());
                            int stock = Optional.ofNullable(mcStockInfo).map(McStockInfo::getTotalStock).orElse(0);
                            totalStock = totalStock.add(BigDecimal.valueOf(stock));
                            warehouseInventory.setStock(BigDecimal.valueOf(stock));
                            list.add(warehouseInventory);
                        }
                        // 如果清仓产品中包含此产品，则不需要减去安全库存
                        if (isClearance) {
                            report.setStock(Math.max(totalStock.intValue(), 0));
                        } else {
                            report.setStock(Math.max(totalStock.subtract(Optional.ofNullable(report.getSafetyStock()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO)).intValue(), 0));
                        }
                        report.setList(list);
                    }
                }

                report.setStock(report.getStock() == null ? 0 : report.getStock());
                if (report.getStock() < 0) {
                    report.setStock(0);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuListingReport.class, searchVo);
    }

    public String export(SomTemuCrossGoodsPageSearchVo searchVo) {
        searchVo.setPageSize(Integer.MAX_VALUE);
        searchVo.setCurrent(1);
        PageVo<SomTemuListingReport> records = stockReport(searchVo);
        if (records!=null && !records.getRecords().isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "Temu跨境库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTemuListingReport.class, records.getRecords());
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
