package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomVcInventoryReportService;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomVcSaleInventoryReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcInventoryReportController
 * @description
 * @date 2024-01-23 16:30:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/vc-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC可售库存报表(新)")
public class SomVcInventoryReportController extends BasicController {
    @Resource
    private SomVcInventoryReportService service;

    /**
     * VC可售库存报表分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomVcSaleInventoryReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomVcSaleInventoryReportVo>> queryVcInventoryReport(@RequestBody SomVcSaleInventoryReportSearchVo searchVo) {
       return ResultVo.ofSuccess(service.queryVcInventoryReport(searchVo));
    }

    /**
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomVcSaleInventoryReportSearchVo searchVo) throws ValidateException {
        String data = service.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
