package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * Cdiscount Listing信息表
 * gen by 代码生成器 2023-01-11
 */

@Table(name = "mc.som_cdiscount_listing")
public class SomCdiscountListing implements java.io.Serializable {
    /**
     * 主键id
     */
    @AssignID
    private String aid;
    /**
     * 最佳运费价格
     */
    @Column("best_shipping_charges")
    private BigDecimal bestShippingCharges;
    /**
     * 备注
     */
    @Column("comments")
    private String comments;
    /**
     * 创建时间
     */
    @Column("creation_date")
    private Date creationDate;
    /**
     * 扣除税额
     */
    @Column("dea_tax")
    private BigDecimal deaTax;
    /**
     * 生态税金额
     */
    @Column("eco_tax")
    private BigDecimal ecoTax;
    /**
     * 报价中包含价格
     */
    @Column("integration_price")
    private BigDecimal integrationPrice;
    /**
     * 是否受益于CDAV计划
     */
    @Column("is_cdav")
    private Integer isCdav;
    /**
     * 最后修改时间
     */
    @Column("last_update_date")
    private Date lastUpdateDate;
    /**
     * 最低价格
     */
    @Column("minimum_price_for_price_alignment")
    private BigDecimal minimumPriceForPriceAlignment;
    /**
     * 父级产品ID
     */
    @Column("parent_product_id")
    private String parentProductId;
    /**
     * 产品价格 含税价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * EAN编码
     */
    @Column("product_ean")
    private String productEan;
    /**
     * 产品ID
     */
    @Column("product_id")
    private String productId;
    /**
     * 产品单价
     */
    @Column("product_packaging_unit_price")
    private BigDecimal productPackagingUnitPrice;
    /**
     * 产品包装价值
     */
    @Column("product_packaging_value")
    private Long productPackagingValue;
    /**
     * 展示码
     */
    @Column("seller_product_id")
    private String sellerProductId;
    /**
     * 库存数量
     */
    @Column("stock")
    private Integer stock;
    /**
     * 获取或设置执行价格
     */
    @Column("striked_price")
    private BigDecimal strikedPrice;
    /**
     * 以百分比表示的增值税
     */
    @Column("vat_rate")
    private BigDecimal vatRate;
    /**
     * 价格调整活跃
     */
    @Column("price_must_be_aligned")
    private String priceMustBeAligned;
    /**
     * 产品包装
     */
    @Column("product_packaging_unit")
    private String productPackagingUnit;
    /**
     * 报价的状态
     */
    @Column("offer_state")
    private String offerState;
    /**
     * 产品的状态
     */
    @Column("product_condition")
    private String productCondition;
    /**
     * 运输模型
     */
    @Column("logistic_mode")
    private String logisticMode;
    /**
     * 售罄管理
     */
    @Column("sold_out_management")
    private String soldOutManagement;
    /**
     * 物流管理
     */
    @Column("logistics_management")
    private String logisticsManagement;
    @Column("offer_pool_list")
    private String offerPoolList;
    @Column("associated_stocks")
    private String associatedStocks;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomCdiscountListing() {
    }

    /**
     * 主键id
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键id
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 最佳运费价格
     *
     * @return
     */
    public BigDecimal getBestShippingCharges() {
        return bestShippingCharges;
    }

    /**
     * 最佳运费价格
     *
     * @param bestShippingCharges
     */
    public void setBestShippingCharges(BigDecimal bestShippingCharges) {
        this.bestShippingCharges = bestShippingCharges;
    }

    /**
     * 备注
     *
     * @return
     */
    public String getComments() {
        return comments;
    }

    /**
     * 备注
     *
     * @param comments
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * 创建时间
     *
     * @param creationDate
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * 扣除税额
     *
     * @return
     */
    public BigDecimal getDeaTax() {
        return deaTax;
    }

    /**
     * 扣除税额
     *
     * @param deaTax
     */
    public void setDeaTax(BigDecimal deaTax) {
        this.deaTax = deaTax;
    }

    /**
     * 生态税金额
     *
     * @return
     */
    public BigDecimal getEcoTax() {
        return ecoTax;
    }

    /**
     * 生态税金额
     *
     * @param ecoTax
     */
    public void setEcoTax(BigDecimal ecoTax) {
        this.ecoTax = ecoTax;
    }

    /**
     * 报价中包含价格
     *
     * @return
     */
    public BigDecimal getIntegrationPrice() {
        return integrationPrice;
    }

    /**
     * 报价中包含价格
     *
     * @param integrationPrice
     */
    public void setIntegrationPrice(BigDecimal integrationPrice) {
        this.integrationPrice = integrationPrice;
    }

    /**
     * 是否受益于CDAV计划
     *
     * @return
     */
    public Integer getisCdav() {
        return isCdav;
    }

    /**
     * 是否受益于CDAV计划
     *
     * @param isCdav
     */
    public void setisCdav(Integer isCdav) {
        this.isCdav = isCdav;
    }

    /**
     * 最后修改时间
     *
     * @return
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     * 最后修改时间
     *
     * @param lastUpdateDate
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    /**
     * 最低价格
     *
     * @return
     */
    public BigDecimal getMinimumPriceForPriceAlignment() {
        return minimumPriceForPriceAlignment;
    }

    /**
     * 最低价格
     *
     * @param minimumPriceForPriceAlignment
     */
    public void setMinimumPriceForPriceAlignment(BigDecimal minimumPriceForPriceAlignment) {
        this.minimumPriceForPriceAlignment = minimumPriceForPriceAlignment;
    }

    /**
     * 父级产品ID
     *
     * @return
     */
    public String getParentProductId() {
        return parentProductId;
    }

    /**
     * 父级产品ID
     *
     * @param parentProductId
     */
    public void setParentProductId(String parentProductId) {
        this.parentProductId = parentProductId;
    }

    /**
     * 产品价格 含税价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 产品价格 含税价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * EAN编码
     *
     * @return
     */
    public String getProductEan() {
        return productEan;
    }

    /**
     * EAN编码
     *
     * @param productEan
     */
    public void setProductEan(String productEan) {
        this.productEan = productEan;
    }

    /**
     * 产品ID
     *
     * @return
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 产品ID
     *
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * 产品单价
     *
     * @return
     */
    public BigDecimal getProductPackagingUnitPrice() {
        return productPackagingUnitPrice;
    }

    /**
     * 产品单价
     *
     * @param productPackagingUnitPrice
     */
    public void setProductPackagingUnitPrice(BigDecimal productPackagingUnitPrice) {
        this.productPackagingUnitPrice = productPackagingUnitPrice;
    }

    /**
     * 产品包装价值
     *
     * @return
     */
    public Long getProductPackagingValue() {
        return productPackagingValue;
    }

    /**
     * 产品包装价值
     *
     * @param productPackagingValue
     */
    public void setProductPackagingValue(Long productPackagingValue) {
        this.productPackagingValue = productPackagingValue;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerProductId() {
        return sellerProductId;
    }

    /**
     * 展示码
     *
     * @param sellerProductId
     */
    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }

    /**
     * 库存数量
     *
     * @return
     */
    public Integer getStock() {
        return stock;
    }

    /**
     * 库存数量
     *
     * @param stock
     */
    public void setStock(Integer stock) {
        this.stock = stock;
    }

    /**
     * 获取或设置执行价格
     *
     * @return
     */
    public BigDecimal getStrikedPrice() {
        return strikedPrice;
    }

    /**
     * 获取或设置执行价格
     *
     * @param strikedPrice
     */
    public void setStrikedPrice(BigDecimal strikedPrice) {
        this.strikedPrice = strikedPrice;
    }

    /**
     * 以百分比表示的增值税
     *
     * @return
     */
    public BigDecimal getVatRate() {
        return vatRate;
    }

    /**
     * 以百分比表示的增值税
     *
     * @param vatRate
     */
    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    /**
     * 价格调整活跃
     *
     * @return
     */
    public String getPriceMustBeAligned() {
        return priceMustBeAligned;
    }

    /**
     * 价格调整活跃
     *
     * @param priceMustBeAligned
     */
    public void setPriceMustBeAligned(String priceMustBeAligned) {
        this.priceMustBeAligned = priceMustBeAligned;
    }

    /**
     * 产品包装
     *
     * @return
     */
    public String getProductPackagingUnit() {
        return productPackagingUnit;
    }

    /**
     * 产品包装
     *
     * @param productPackagingUnit
     */
    public void setProductPackagingUnit(String productPackagingUnit) {
        this.productPackagingUnit = productPackagingUnit;
    }

    /**
     * 报价的状态
     *
     * @return
     */
    public String getOfferState() {
        return offerState;
    }

    /**
     * 报价的状态
     *
     * @param offerState
     */
    public void setOfferState(String offerState) {
        this.offerState = offerState;
    }

    /**
     * 产品的状态
     *
     * @return
     */
    public String getProductCondition() {
        return productCondition;
    }

    /**
     * 产品的状态
     *
     * @param productCondition
     */
    public void setProductCondition(String productCondition) {
        this.productCondition = productCondition;
    }

    /**
     * 运输模型
     *
     * @return
     */
    public String getLogisticMode() {
        return logisticMode;
    }

    /**
     * 运输模型
     *
     * @param logisticMode
     */
    public void setLogisticMode(String logisticMode) {
        this.logisticMode = logisticMode;
    }

    /**
     * 售罄管理
     *
     * @return
     */
    public String getSoldOutManagement() {
        return soldOutManagement;
    }

    /**
     * 售罄管理
     *
     * @param soldOutManagement
     */
    public void setSoldOutManagement(String soldOutManagement) {
        this.soldOutManagement = soldOutManagement;
    }

    /**
     * 物流管理
     *
     * @return
     */
    public String getLogisticsManagement() {
        return logisticsManagement;
    }

    /**
     * 物流管理
     *
     * @param logisticsManagement
     */
    public void setLogisticsManagement(String logisticsManagement) {
        this.logisticsManagement = logisticsManagement;
    }

    public String getOfferPoolList() {
        return offerPoolList;
    }

    public void setOfferPoolList(String offerPoolList) {
        this.offerPoolList = offerPoolList;
    }

    public String getAssociatedStocks() {
        return associatedStocks;
    }

    public void setAssociatedStocks(String associatedStocks) {
        this.associatedStocks = associatedStocks;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
