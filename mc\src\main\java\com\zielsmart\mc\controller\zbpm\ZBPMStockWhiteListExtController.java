package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMStockWhiteListExtService;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchExtVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/zbpm-stockwhitelist-ext", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "库存白名单接口(扩展)")
public class ZBPMStockWhiteListExtController extends BasicController {

    @Resource
    private ZBPMStockWhiteListExtService service;

    /**
     * getMarketsPlatforms
     * 获取市场和平台
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取市场和平台")
    @PostMapping(value = "/getMarketsPlatforms")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<ZBPMStockWhiteListExtVo> getMarketsPlatforms() {
        return ResultVo.ofSuccess(service.getMarketsPlatforms());
    }

    /**
     * queryListing
     * 根据市场、平台和产品编码查询Listing
     *
     * @param searchVoList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据市场、平台和产品编码查询Listing")
    @PostMapping(value = "/queryListings")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZBPMStockWhiteListExtVo>> queryListings(@RequestBody List<ZBPMWhiteListSearchExtVo> searchVoList) throws ValidateException {
        return ResultVo.ofSuccess(service.queryListings(searchVoList));
    }

    /**
     * addOrDelStockWhiteList
     * 新增或删除库存白名单
     *
     * @param addOrDelList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增或删除库存白名单")
    @PostMapping(value = "/addOrDelStockWhiteList")
    public ResultVo<String> addOrDelStockWhiteList(@RequestBody List<ZBPMStockWhiteListExtVo> addOrDelList) throws ValidateException {
        service.addOrDelStockWhiteList(addOrDelList);
        return ResultVo.ofSuccess(null);
    }
}
