package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomThreePlatformPriceWhiteService;
import com.zielsmart.mc.vo.SomThreePlatformPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceWhiteVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomThreePlatformPriceWhiteController
 * @description 三方平台价格管理-白名单
 * @date 2024-11-13 16:33:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somThreePlatformPriceWhite", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "三方平台价格推送白名单管理")
public class SomThreePlatformPriceWhiteController extends BasicController {

    @Resource
    SomThreePlatformPriceWhiteService somThreePlatformPriceWhiteService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomThreePlatformPriceWhiteVo>> queryByPage(@RequestBody SomThreePlatformPriceWhitePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somThreePlatformPriceWhiteService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomThreePlatformPriceWhiteVo somThreePlatformPriceWhiteVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somThreePlatformPriceWhiteService.save(somThreePlatformPriceWhiteVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomThreePlatformPriceWhiteVo somThreePlatformPriceWhiteVo) throws ValidateException {
        somThreePlatformPriceWhiteService.delete(somThreePlatformPriceWhiteVo);
        return ResultVo.ofSuccess(null);
    }
}
