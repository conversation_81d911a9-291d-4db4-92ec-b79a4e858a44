package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
import java.util.Date;
/*
 * woot促销活动仓库配置
 * gen by 代码生成器 2024-06-03
 */

@Table(name = "mc.som_woot_promotion_warehouse_config")
public class SomWootPromotionWarehouseConfig implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * VC标识。0.否 1.是
     */
    @Column("vc_flag")
    private Integer vcFlag;
    /**
     * FBA标识。0.否 1.是
     */
    @Column("fba_stock_flag")
    private Integer fbaStockFlag;
    /**
     * 可售仓库
     */
    @Column("useable_warehouse_code")
    private String useableWarehouseCode;
    /**
     * 可售库区
     */
    @Column("useable_storage_code")
    private String useableStorageCode;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 修改人工号
     */
    @Column("last_modify_num")
    private String lastModifyNum;
    /**
     * 修改人姓名
     */
    @Column("last_modify_name")
    private String lastModifyName;
    /**
     * 修改时间
     */
    @Column("last_modify_time")
    private Date lastModifyTime;

    public SomWootPromotionWarehouseConfig() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * VC标识。0.否 1.是
     *
     * @return
     */
    public Integer getvcFlag() {
        return vcFlag;
    }

    /**
     * VC标识。0.否 1.是
     *
     * @param vcFlag
     */
    public void setvcFlag(Integer vcFlag) {
        this.vcFlag = vcFlag;
    }

    /**
     * FBA标识。0.否 1.是
     *
     * @return
     */
    public Integer getFbaStockFlag() {
        return fbaStockFlag;
    }

    /**
     * FBA标识。0.否 1.是
     *
     * @param fbaStockFlag
     */
    public void setFbaStockFlag(Integer fbaStockFlag) {
        this.fbaStockFlag = fbaStockFlag;
    }

    /**
     * 可售仓库
     *
     * @return
     */
    public String getUseableWarehouseCode() {
        return useableWarehouseCode;
    }

    /**
     * 可售仓库
     *
     * @param useableWarehouseCode
     */
    public void setUseableWarehouseCode(String useableWarehouseCode) {
        this.useableWarehouseCode = useableWarehouseCode;
    }

    /**
     * 可售库区
     *
     * @return
     */
    public String getUseableStorageCode() {
        return useableStorageCode;
    }

    /**
     * 可售库区
     *
     * @param useableStorageCode
     */
    public void setUseableStorageCode(String useableStorageCode) {
        this.useableStorageCode = useableStorageCode;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人工号
     *
     * @return
     */
    public String getLastModifyNum() {
        return lastModifyNum;
    }

    /**
     * 修改人工号
     *
     * @param lastModifyNum
     */
    public void setLastModifyNum(String lastModifyNum) {
        this.lastModifyNum = lastModifyNum;
    }

    /**
     * 修改人姓名
     *
     * @return
     */
    public String getLastModifyName() {
        return lastModifyName;
    }

    /**
     * 修改人姓名
     *
     * @param lastModifyName
     */
    public void setLastModifyName(String lastModifyName) {
        this.lastModifyName = lastModifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * 修改时间
     *
     * @param lastModifyTime
     */
    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

}
