package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomOttoPlatformPriceService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomOttoPlatformPriceController
 * @description OTTO 平台价调整表
 * @date 2025-04-01 15:49:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somOttoPlatformPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "OTTO 平台价调整表")
public class SomOttoPlatformPriceController extends BasicController{

    @Resource
    SomOttoPlatformPriceService somOttoPlatformPriceService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomOttoPlatformPriceVo>> queryByPage(@RequestBody SomOttoPlatformPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somOttoPlatformPriceService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomOttoPlatformPricePageSearchVo searchVo) {
        String data = somOttoPlatformPriceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "最近一次成功的调价记录查询")
    @PostMapping(value = "/latestSuccess/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<SomOttoPlatformPriceHistoryVo> queryLatestSuccess(@RequestBody SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo) throws ValidateException {
        SomOttoPlatformPriceHistoryVo historyVo =  somOttoPlatformPriceService.queryLatestSuccess(ottoPlatformPriceOprVo);
        return ResultVo.ofSuccess(historyVo);
    }

    @Operation(summary = "编辑")
    @PostMapping(value = "/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> edit(@RequestBody SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somOttoPlatformPriceService.edit(ottoPlatformPriceOprVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "批量提交调价申请")
    @PostMapping(value = "/apply/submit", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> submitApply(@RequestBody SomOttoPlatformPriceOprVo ottoPlatformPriceOprVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somOttoPlatformPriceService.submitApply(ottoPlatformPriceOprVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/OttoPlatformPriceTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"展示码", "销售价", "促销价", "促销开始时间", "促销结束时间", "调价原因"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomOttoPlatformPriceImportVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomOttoPlatformPriceImportVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somOttoPlatformPriceService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }


}
