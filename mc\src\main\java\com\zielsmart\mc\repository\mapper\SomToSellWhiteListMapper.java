package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomToSellWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomToSellWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-04-20
*/

@SqlResource("somToSellWhiteList")
public interface SomToSellWhiteListMapper extends BaseMapper<SomToSellWhiteList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomToSellWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomToSellWhiteListVo> queryByPage(@Param("searchVo")SomToSellWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
