package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* Temu本本营销活动
* gen by 代码生成器 2025-03-12
*/

@Table(name="mc.som_temu_local_promotion")
public class SomTemuLocalPromotion implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 营销活动ID
	 */
	@Column("activity_id")
	private String activityId ;
	/**
	 * 营销活动名称
	 */
	@Column("activity_name")
	private String activityName ;
	/**
	 * 活动起始时间
	 */
	@Column("activity_start_time")
	private Date activityStartTime ;
	/**
	 * 活动截止时间
	 */
	@Column("activity_end_time")
	private Date activityEndTime ;
	/**
	 * 活动状态：1. 未开始 2. 运行中 3. 已结束
	 */
	@Column("activity_status")
	private Integer activityStatus ;
	/**
	 * 活动类型 2 Lightning deals 13 Advanced big sale 27 Clearance deals 100 Official big sale
	 */
	@Column("activity_type")
	private Integer activityType ;
	/**
	 * 是否加入了此活动，0否 1是
	 */
	@Column("is_joined_activity")
	private Integer isJoinedActivity ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomTemuLocalPromotion() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 营销活动ID
	*@return
	*/
	public String getActivityId(){
		return  activityId;
	}
	/**
	* 营销活动ID
	*@param  activityId
	*/
	public void setActivityId(String activityId ){
		this.activityId = activityId;
	}
	/**
	* 营销活动名称
	*@return
	*/
	public String getActivityName(){
		return  activityName;
	}
	/**
	* 营销活动名称
	*@param  activityName
	*/
	public void setActivityName(String activityName ){
		this.activityName = activityName;
	}
	/**
	* 活动起始时间
	*@return
	*/
	public Date getActivityStartTime(){
		return  activityStartTime;
	}
	/**
	* 活动起始时间
	*@param  activityStartTime
	*/
	public void setActivityStartTime(Date activityStartTime ){
		this.activityStartTime = activityStartTime;
	}
	/**
	* 活动截止时间
	*@return
	*/
	public Date getActivityEndTime(){
		return  activityEndTime;
	}
	/**
	* 活动截止时间
	*@param  activityEndTime
	*/
	public void setActivityEndTime(Date activityEndTime ){
		this.activityEndTime = activityEndTime;
	}
	/**
	* 活动状态：1. 未开始 2. 运行中 3. 已结束
	*@return
	*/
	public Integer getActivityStatus(){
		return  activityStatus;
	}
	/**
	* 活动状态：1. 未开始 2. 运行中 3. 已结束
	*@param  activityStatus
	*/
	public void setActivityStatus(Integer activityStatus ){
		this.activityStatus = activityStatus;
	}
	/**
	* 活动类型 2 Lightning deals 13 Advanced big sale 27 Clearance deals 100 Official big sale
	*@return
	*/
	public Integer getActivityType(){
		return  activityType;
	}
	/**
	* 活动类型 2 Lightning deals 13 Advanced big sale 27 Clearance deals 100 Official big sale
	*@param  activityType
	*/
	public void setActivityType(Integer activityType ){
		this.activityType = activityType;
	}
	/**
	* 是否加入了此活动，0否 1是
	*@return
	*/
	public Integer getisJoinedActivity(){
		return  isJoinedActivity;
	}
	/**
	* 是否加入了此活动，0否 1是
	*@param  isJoinedActivity
	*/
	public void setisJoinedActivity(Integer isJoinedActivity ){
		this.isJoinedActivity = isJoinedActivity;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
