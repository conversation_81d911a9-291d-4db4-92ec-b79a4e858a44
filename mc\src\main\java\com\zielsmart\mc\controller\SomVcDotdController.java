package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomVcDotdService;
import com.zielsmart.mc.vo.SomVcDotdPageSearchVo;
import com.zielsmart.mc.vo.SomVcDotdVo;
import com.zielsmart.mc.vo.SomVcDotdImportVo;
import com.zielsmart.mc.vo.SomVcDotdSubmitResultImportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcDotdController
 * @description
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somVcDotd", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC DOTO表管理")
public class SomVcDotdController extends BasicController {

    @Resource
    SomVcDotdService somVcDotdService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcDotdVo>> queryByPage(@RequestBody SomVcDotdPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcDotdService.queryByPage(searchVo));
    }

    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/addOrEdit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> addOrEdit(@RequestBody SomVcDotdVo somVcDotdVo, @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcDotdService.addOrEdit(somVcDotdVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> cancel(@RequestBody SomVcDotdVo somVcDotdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcDotdService.cancel(somVcDotdVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomVcDotdVo somVcDotdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcDotdService.delete(somVcDotdVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "反馈提报结果")
    @PostMapping(value = "/submitResult/feedback")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackSubmitResult(@RequestBody SomVcDotdVo somVcDotdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcDotdService.feedbackSubmitResult(somVcDotdVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "补充大促起止日期")
    @PostMapping(value = "/promotionDate/supply")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> supplyPromotionDate(@RequestBody SomVcDotdVo somVcDotdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcDotdService.supplyPromotionDate(somVcDotdVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomVcDotdPageSearchVo searchVo) {
        String data = somVcDotdService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/VCDOTDImportTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        importParams.setSheetNum(0);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "账号名称", "供应商编码", "合作模式", "自发类型", "大促类型", "活动起始日期",
                "活动截止日期", "展示码", "ASIN", "折扣比例", "秒杀价格", "Funding", "评分", "申请原因", "自定义原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcDotdImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcDotdImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcDotdService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载批量反馈提报结果导入模板")
    @GetMapping(value = "/submitResultTemplate/download")
    public String downloadSubmitResultTemplate() {
        return "forward:/static/excel/VCDOTDBatchFeedbackSubmitResultTemplate.xlsx";
    }

    @Operation(summary = "导入批量反馈提报结果")
    @PostMapping(value = "/submitResult/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importSubmitResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        importParams.setSheetNum(0);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "账号名称", "供应商编码", "大促类型", "活动起始日期", "活动截止日期", "展示码", "ASIN", "提报结果", "BS Feedback"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcDotdSubmitResultImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcDotdSubmitResultImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcDotdService.importSubmitResult(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

}
