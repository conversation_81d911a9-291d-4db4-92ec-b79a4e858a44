package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-04-23
*/

@Table(name="mc.som_shein_listing")
public class SomSheinListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * SKC CODE
	 */
	@Column("skc_code")
	private String skcCode ;
	/**
	 * SKC NAME
	 */
	@Column("skc_name")
	private String skcName ;
	/**
	 * 平台的SKU_CODE
	 */
	@Column("sku_code")
	private String skuCode ;
	/**
	 * 平台的SPU名称
	 */
	@Column("spu_name")
	private String spuName ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 产品名称
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 产品描述
	 */
	@Column("product_desc")
	private String productDesc ;
	/**
	 * 产品编码
	 */
	@Column("product_number")
	private String productNumber ;
	/**
	 * 库存数量
	 */
	@Column("inventory_quantity")
	private Integer inventoryQuantity ;
	/**
	 * 锁定的库存数量
	 */
	@Column("locked_quantity")
	private Integer lockedQuantity ;
	/**
	 * 可用库存
	 */
	@Column("usable_inventory")
	private Integer usableInventory ;
	/**
	 * 下载时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 图片组
	 */
	@Column("image_list")
	private String imageList ;
	/**
	 * 价格
	 */
	@Column("current_prices")
	private String currentPrices ;

	public SomSheinListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* SKC CODE
	*@return
	*/
	public String getSkcCode(){
		return  skcCode;
	}
	/**
	* SKC CODE
	*@param  skcCode
	*/
	public void setSkcCode(String skcCode ){
		this.skcCode = skcCode;
	}
	/**
	* SKC NAME
	*@return
	*/
	public String getSkcName(){
		return  skcName;
	}
	/**
	* SKC NAME
	*@param  skcName
	*/
	public void setSkcName(String skcName ){
		this.skcName = skcName;
	}
	/**
	* 平台的SKU_CODE
	*@return
	*/
	public String getSkuCode(){
		return  skuCode;
	}
	/**
	* 平台的SKU_CODE
	*@param  skuCode
	*/
	public void setSkuCode(String skuCode ){
		this.skuCode = skuCode;
	}
	/**
	* 平台的SPU名称
	*@return
	*/
	public String getSpuName(){
		return  spuName;
	}
	/**
	* 平台的SPU名称
	*@param  spuName
	*/
	public void setSpuName(String spuName ){
		this.spuName = spuName;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 产品名称
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 产品名称
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 产品描述
	*@return
	*/
	public String getProductDesc(){
		return  productDesc;
	}
	/**
	* 产品描述
	*@param  productDesc
	*/
	public void setProductDesc(String productDesc ){
		this.productDesc = productDesc;
	}
	/**
	* 产品编码
	*@return
	*/
	public String getProductNumber(){
		return  productNumber;
	}
	/**
	* 产品编码
	*@param  productNumber
	*/
	public void setProductNumber(String productNumber ){
		this.productNumber = productNumber;
	}
	/**
	* 库存数量
	*@return
	*/
	public Integer getInventoryQuantity(){
		return  inventoryQuantity;
	}
	/**
	* 库存数量
	*@param  inventoryQuantity
	*/
	public void setInventoryQuantity(Integer inventoryQuantity ){
		this.inventoryQuantity = inventoryQuantity;
	}
	/**
	* 锁定的库存数量
	*@return
	*/
	public Integer getLockedQuantity(){
		return  lockedQuantity;
	}
	/**
	* 锁定的库存数量
	*@param  lockedQuantity
	*/
	public void setLockedQuantity(Integer lockedQuantity ){
		this.lockedQuantity = lockedQuantity;
	}
	/**
	* 可用库存
	*@return
	*/
	public Integer getUsableInventory(){
		return  usableInventory;
	}
	/**
	* 可用库存
	*@param  usableInventory
	*/
	public void setUsableInventory(Integer usableInventory ){
		this.usableInventory = usableInventory;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 下载时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 图片组
	*@return
	*/
	public String getImageList(){
		return  imageList;
	}
	/**
	* 图片组
	*@param  imageList
	*/
	public void setImageList(String imageList ){
		this.imageList = imageList;
	}
	/**
	* 价格
	*@return
	*/
	public String getCurrentPrices(){
		return  currentPrices;
	}
	/**
	* 价格
	*@param  currentPrices
	*/
	public void setCurrentPrices(String currentPrices ){
		this.currentPrices = currentPrices;
	}

}
