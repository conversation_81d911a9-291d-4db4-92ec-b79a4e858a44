package com.zielsmart.mc.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(title = "拼多多可售库存报表", name = "SomTemuListingReport")
public class SomTemuListingReport {
    @Excel(name="账号")
    private String accountTag;

    @Excel(name="账号ID")
    private String accountId;

    @Excel(name = "平台")
    private String platform;

    @Excel(name="站点")
    private String site;

    @Excel(name="展示码")
    private String displayProductCode;

    @Excel(name ="平台仓库名称")
    private String temuWarehouseName;

    private String temuWarehouseId;

    @Excel(name="产品编码")
    private String productMainCode;

    @Excel(name="三级分类")
    private String categoryName;

    @Schema(description = "业务组名称", name = "salesGroupName")
    @Excel(name = "业务组")
    private String salesGroupName;

    @Schema(description = "业务负责人名称", name = "salesGroupEmptName")
    @Excel(name = "业务负责人")
    private String salesGroupEmptName;

    @Schema(description = "可售库存", name = "stock")
    @Excel(name="可售库存")
    private Integer stock;

    private Integer safetyStock;

    private Integer syncFrequency;

    @Schema(description = "平台SKU唯一编码", name = "productSkuId")
    private Long productSkuId;

    @Schema(description = "清仓产品标识", name = "clearanceFlag")
    private Boolean clearanceFlag;

    @Schema(description = "清仓产品标识翻译", name = "clearanceFlagDesc")
    @Excel(name = "清仓产品标识")
    private String clearanceFlagDesc;

    private List<WarehouseInventory> list;

    @Data
    public static class WarehouseInventory {
        @Schema(description = "仓库名称", name = "warehouseName")
        private String warehouseName;
        @Schema(description = "库区名称", name ="slName")
        private String slName;
        @Schema(description = "库存", name = "stock")
        private BigDecimal stock;
    }

    public String getClearanceFlagDesc() {
        if (clearanceFlag != null) {
            return clearanceFlag ? "是" : "否";
        }
        return null;
    }

}
