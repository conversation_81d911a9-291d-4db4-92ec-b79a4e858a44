package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomInventoryInTransitNewVo;
import com.zielsmart.mc.vo.SomInventoryInTransitPageSearchVo;
import com.zielsmart.mc.vo.SomInventoryInTransitVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-09-01
*/

@SqlResource("somInventoryInTransit")
public interface SomInventoryInTransitMapper extends BaseMapper<SomInventoryInTransit> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomInventoryInTransitVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomInventoryInTransitVo> queryByPage(@Param("searchVo")SomInventoryInTransitPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomInventoryInTransitNewVo> queryByPageNew(@Param("searchVo")SomInventoryInTransitPageSearchVo searchVo, PageRequest pageRequest);
}
