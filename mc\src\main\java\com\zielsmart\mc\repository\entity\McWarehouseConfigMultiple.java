package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* wayfair walmart多仓配置
* gen by 代码生成器 2021-10-09
*/

@Table(name="mc.mc_warehouse_config_multiple")
public class McWarehouseConfigMultiple implements java.io.Serializable {
	/**
	 * 主键ID
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 平台仓库编码
	 */
	@Column("platform_warehouse_code")
	private String platformWarehouseCode ;
	/**
	 * 自发仓编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 自发仓名称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public McWarehouseConfigMultiple() {
	}

	/**
	* 主键ID
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键ID
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 平台仓库编码
	*@return
	*/
	public String getPlatformWarehouseCode(){
		return  platformWarehouseCode;
	}
	/**
	* 平台仓库编码
	*@param  platformWarehouseCode
	*/
	public void setPlatformWarehouseCode(String platformWarehouseCode ){
		this.platformWarehouseCode = platformWarehouseCode;
	}
	/**
	* 自发仓编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 自发仓编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 自发仓名称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 自发仓名称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
