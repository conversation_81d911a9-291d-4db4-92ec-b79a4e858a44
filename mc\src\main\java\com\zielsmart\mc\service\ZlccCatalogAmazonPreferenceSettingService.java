package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.ZlccCatalogAmazonPreferenceSetting;
import com.zielsmart.mc.repository.mapper.SomAmazonCategoryNodeXmlMapper;
import com.zielsmart.mc.repository.mapper.ZlccCatalogAmazonMapper;
import com.zielsmart.mc.repository.mapper.ZlccCatalogAmazonPreferenceSettingMapper;
import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingPageSearchVo;
import com.zielsmart.mc.vo.ZlccCatalogAmazonPreferenceSettingVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title ZlccCatalogAmazonPreferenceSettingService
 * @description
 * @date 2023-12-01 10:49:58
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class ZlccCatalogAmazonPreferenceSettingService {

    @Resource
    private ZlccCatalogAmazonPreferenceSettingMapper zlccCatalogAmazonPreferenceSettingMapper;

    @Resource
    private ZlccCatalogAmazonMapper zlccCatalogAmazonMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private SomAmazonCategoryNodeXmlMapper somAmazonCategoryNodeXmlMapper;

    @Resource
    private ZlccCatalogAmazonService zlccCatalogAmazonService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @return {@link PageVo<ZlccCatalogAmazonPreferenceSettingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<ZlccCatalogAmazonPreferenceSettingVo> queryByPage(ZlccCatalogAmazonPreferenceSettingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<ZlccCatalogAmazonPreferenceSettingVo> pageResult = dynamicSqlManager.getMapper(ZlccCatalogAmazonPreferenceSettingMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, ZlccCatalogAmazonPreferenceSettingVo.class, searchVo);
    }

    /**
     * save
     * 新增
     *
     * @param zlccCatalogAmazonPreferenceSettingVo 入参
     * @param tokenUser 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(ZlccCatalogAmazonPreferenceSettingVo zlccCatalogAmazonPreferenceSettingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(zlccCatalogAmazonPreferenceSettingVo.getSite()) || ObjectUtil.isEmpty(zlccCatalogAmazonPreferenceSettingVo.getBrowseNodeId()) || ObjectUtil.isEmpty(zlccCatalogAmazonPreferenceSettingVo.getBrowseNodeName())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String site = zlccCatalogAmazonPreferenceSettingVo.getSite();
        Long browseNodeId = zlccCatalogAmazonPreferenceSettingVo.getBrowseNodeId();
        long count = zlccCatalogAmazonPreferenceSettingMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("browse_node_id", browseNodeId)
                .count();
        if (count > 0) {
            throw new ValidateException("此站点已维护了此类目，不允许重复维护！");
        }
        // 核验节点是否存在
        zlccCatalogAmazonService.checkNodeExist(site, browseNodeId);
        zlccCatalogAmazonPreferenceSettingVo.setAid(IdUtil.fastSimpleUUID());
        zlccCatalogAmazonPreferenceSettingVo.setCreateNum(tokenUser.getJobNumber());
        zlccCatalogAmazonPreferenceSettingVo.setCreateName(tokenUser.getUserName());
        zlccCatalogAmazonPreferenceSettingVo.setCreateDate(DateTime.now().toJdkDate());
        zlccCatalogAmazonPreferenceSettingMapper.insert(ConvertUtils.beanConvert(zlccCatalogAmazonPreferenceSettingVo, ZlccCatalogAmazonPreferenceSetting.class));
    }


    @Transactional(rollbackFor = Exception.class)
    public void delete(ZlccCatalogAmazonPreferenceSettingVo settingVo) throws ValidateException {
        if (ObjectUtil.isEmpty(settingVo) || StrUtil.isEmpty(settingVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        String aid = settingVo.getAid();
        List<ZlccCatalogAmazonPreferenceSetting> settings = zlccCatalogAmazonPreferenceSettingMapper.createLambdaQuery()
                .andEq("aid", aid)
                .select();
        if (CollUtil.isEmpty(settings)) {
            return;
        }
        ZlccCatalogAmazonPreferenceSetting setting = settings.get(0);
        // 删除这个根下面的所有子，如果是收藏进来的，且它不是根，那么不会删除类目树里面的数据
        zlccCatalogAmazonMapper.createLambdaQuery()
                .andEq("root_node_id", setting.getBrowseNodeId())
                .delete();
        // 删除本条数据
        zlccCatalogAmazonPreferenceSettingMapper.deleteById(aid);
    }


}
