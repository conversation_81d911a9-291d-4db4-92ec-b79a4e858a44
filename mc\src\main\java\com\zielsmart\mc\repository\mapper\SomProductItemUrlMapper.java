package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomProductItemUrlPageSearchVo;
import com.zielsmart.mc.vo.SomProductItemUrlVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2025-01-07
*/

@SqlResource("somProductItemUrl")
public interface SomProductItemUrlMapper extends BaseMapper<SomProductItemUrl> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomProductItemUrlVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomProductItemUrlVo> queryByPage(@Param("searchVo")SomProductItemUrlPageSearchVo searchVo, PageRequest pageRequest);
}
