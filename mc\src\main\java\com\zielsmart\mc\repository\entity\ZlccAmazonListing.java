package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;

import java.util.Date;
/*
 * 商品中心亚马逊Listing
 * gen by 代码生成器 2023-12-11
 */

@Table(name = "mc.zlcc_amazon_listing")
public class ZlccAmazonListing implements java.io.Serializable {

    /**
     * 前端数据json包含表单对象关系
     */
    @Column("product_type")
    private String productType;

    @Column("front_json")
    private PGobject frontJson;


    /**
     * 状态 1.平台下载正常数据  2.发布Listing 3.发布成功
     */
    @Column("data_from")
    private Integer dataFrom;

    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 市场ID
     */
    @Column("marketplace_id")
    private String marketplaceId;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * Summaries
     */
    @Column("summaries")
    private PGobject summaries;
    /**
     * Attributes
     */
    @Column("attributes")
    private PGobject attributes;
    /**
     * Offers
     */
    @Column("offers")
    private PGobject offers;
    /**
     * Fulfillment
     */
    @Column("fulfillment_availability")
    private PGobject fulfillmentAvailability;
    /**
     * 删除状态 10正常  99删除
     */
    @Column("delete_status")
    private Integer deleteStatus;
    /**
     * 删除人工号
     */
    @Column("delete_num")
    private String deleteNum;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 最后修改人工号
     */
    @Column("last_modify_num")
    private String lastModifyNum;
    /**
     * 最后修改人姓名
     */
    @Column("last_modify_name")
    private String lastModifyName;
    /**
     * 最后修改人时间
     */
    @Column("last_modify_time")
    private Date lastModifyTime;
    /**
     * 从平台更新至系统的时间
     */
    @Column("get_sync_time")
    private Date getSyncTime;

    /**
     * 发布状态 0无需发布  1待发布  20发布成功 30 发布中
     */
    @Column("publish_status")
    private Integer publishStatus;

    @Column("recover_aid")
    private String recoverAid;

    @Column("is_consignment_sales")
    private Integer isConsignmentSales;

    @Column("sales_group_code")
    private String salesGroupCode;

    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;

    @Column("operation_empt_code")
    private String operationEmptCode;

    @Column("material_is_complete")
    private Integer materialIsComplete;

    public ZlccAmazonListing() {
    }

    public Integer getIsConsignmentSales() {
        return isConsignmentSales;
    }

    public void setIsConsignmentSales(Integer isConsignmentSales) {
        this.isConsignmentSales = isConsignmentSales;
    }

    public String getSalesGroupCode() {
        return salesGroupCode;
    }

    public void setSalesGroupCode(String salesGroupCode) {
        this.salesGroupCode = salesGroupCode;
    }

    public String getSalesGroupEmptCode() {
        return salesGroupEmptCode;
    }

    public void setSalesGroupEmptCode(String salesGroupEmptCode) {
        this.salesGroupEmptCode = salesGroupEmptCode;
    }

    public String getOperationEmptCode() {
        return operationEmptCode;
    }

    public void setOperationEmptCode(String operationEmptCode) {
        this.operationEmptCode = operationEmptCode;
    }

    public Integer getMaterialIsComplete() {
        return materialIsComplete;
    }

    public void setMaterialIsComplete(Integer materialIsComplete) {
        this.materialIsComplete = materialIsComplete;
    }

    public String getRecoverAid() {
        return recoverAid;
    }

    public void setRecoverAid(String recoverAid) {
        this.recoverAid = recoverAid;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public PGobject getFrontJson() {
        return frontJson;
    }

    public void setFrontJson(PGobject frontJson) {
        this.frontJson = frontJson;
    }

    public PGobject getSummaries() {
        return summaries;
    }

    public void setSummaries(PGobject summaries) {
        this.summaries = summaries;
    }

    public PGobject getAttributes() {
        return attributes;
    }

    public void setAttributes(PGobject attributes) {
        this.attributes = attributes;
    }

    public PGobject getOffers() {
        return offers;
    }

    public void setOffers(PGobject offers) {
        this.offers = offers;
    }

    public PGobject getFulfillmentAvailability() {
        return fulfillmentAvailability;
    }

    public void setFulfillmentAvailability(PGobject fulfillmentAvailability) {
        this.fulfillmentAvailability = fulfillmentAvailability;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 市场ID
     *
     * @return
     */
    public String getMarketplaceId() {
        return marketplaceId;
    }

    /**
     * 市场ID
     *
     * @param marketplaceId
     */
    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 删除状态 10正常  99删除
     *
     * @return
     */
    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    /**
     * 删除状态 10正常  99删除
     *
     * @param deleteStatus
     */
    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    /**
     * 删除人工号
     *
     * @return
     */
    public String getDeleteNum() {
        return deleteNum;
    }

    /**
     * 删除人工号
     *
     * @param deleteNum
     */
    public void setDeleteNum(String deleteNum) {
        this.deleteNum = deleteNum;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改人工号
     *
     * @return
     */
    public String getLastModifyNum() {
        return lastModifyNum;
    }

    /**
     * 最后修改人工号
     *
     * @param lastModifyNum
     */
    public void setLastModifyNum(String lastModifyNum) {
        this.lastModifyNum = lastModifyNum;
    }

    /**
     * 最后修改人姓名
     *
     * @return
     */
    public String getLastModifyName() {
        return lastModifyName;
    }

    /**
     * 最后修改人姓名
     *
     * @param lastModifyName
     */
    public void setLastModifyName(String lastModifyName) {
        this.lastModifyName = lastModifyName;
    }

    /**
     * 最后修改人时间
     *
     * @return
     */
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * 最后修改人时间
     *
     * @param lastModifyTime
     */
    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    /**
     * 从平台更新至系统的时间
     *
     * @return
     */
    public Date getGetSyncTime() {
        return getSyncTime;
    }

    /**
     * 从平台更新至系统的时间
     *
     * @param getSyncTime
     */
    public void setGetSyncTime(Date getSyncTime) {
        this.getSyncTime = getSyncTime;
    }

}
