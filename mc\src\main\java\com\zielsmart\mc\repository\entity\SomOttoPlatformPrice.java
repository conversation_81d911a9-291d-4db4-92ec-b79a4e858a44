package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* OTTO 平台价调整表
* gen by 代码生成器 2025-04-01
*/

@Table(name="mc.som_otto_platform_price")
public class SomOttoPlatformPrice implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 销售价
	 */
	@Column("sell_price")
	private BigDecimal sellPrice ;
	/**
	 * 促销价
	 */
	@Column("promotional_price")
	private BigDecimal promotionalPrice ;
	/**
	 * 促销开始时间
	 */
	@Column("promotion_start_time")
	private Date promotionStartTime ;
	/**
	 * 促销结束时间
	 */
	@Column("promotion_end_time")
	private Date promotionEndTime ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 调价原因
	 */
	@Column("adjust_reason")
	private String adjustReason ;
	/**
	 * 调价状态
	 */
	@Column("adjust_status")
	private Integer adjustStatus ;
	/**
	 * 调价失败原因
	 */
	@Column("adjust_failure_reason")
	private String adjustFailureReason ;
	/**
	 * 修改人
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomOttoPlatformPrice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 销售价
	*@return
	*/
	public BigDecimal getSellPrice(){
		return  sellPrice;
	}
	/**
	* 销售价
	*@param  sellPrice
	*/
	public void setSellPrice(BigDecimal sellPrice ){
		this.sellPrice = sellPrice;
	}
	/**
	* 促销价
	*@return
	*/
	public BigDecimal getPromotionalPrice(){
		return  promotionalPrice;
	}
	/**
	* 促销价
	*@param  promotionalPrice
	*/
	public void setPromotionalPrice(BigDecimal promotionalPrice ){
		this.promotionalPrice = promotionalPrice;
	}
	/**
	* 促销开始时间
	*@return
	*/
	public Date getPromotionStartTime(){
		return  promotionStartTime;
	}
	/**
	* 促销开始时间
	*@param  promotionStartTime
	*/
	public void setPromotionStartTime(Date promotionStartTime ){
		this.promotionStartTime = promotionStartTime;
	}
	/**
	* 促销结束时间
	*@return
	*/
	public Date getPromotionEndTime(){
		return  promotionEndTime;
	}
	/**
	* 促销结束时间
	*@param  promotionEndTime
	*/
	public void setPromotionEndTime(Date promotionEndTime ){
		this.promotionEndTime = promotionEndTime;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 调价原因
	*@return
	*/
	public String getAdjustReason(){
		return  adjustReason;
	}
	/**
	* 调价原因
	*@param  adjustReason
	*/
	public void setAdjustReason(String adjustReason ){
		this.adjustReason = adjustReason;
	}
	/**
	* 调价状态
	*@return
	*/
	public Integer getAdjustStatus(){
		return  adjustStatus;
	}
	/**
	* 调价状态
	*@param  adjustStatus
	*/
	public void setAdjustStatus(Integer adjustStatus ){
		this.adjustStatus = adjustStatus;
	}
	/**
	* 调价失败原因
	*@return
	*/
	public String getAdjustFailureReason(){
		return  adjustFailureReason;
	}
	/**
	* 调价失败原因
	*@param  adjustFailureReason
	*/
	public void setAdjustFailureReason(String adjustFailureReason ){
		this.adjustFailureReason = adjustFailureReason;
	}
	/**
	* 修改人
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
