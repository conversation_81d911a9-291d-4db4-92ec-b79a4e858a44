package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.zielsmart.mc.service.SomAmazonVcPriceService;
import com.zielsmart.mc.vo.McDearanceProductExVo;
import com.zielsmart.mc.vo.SomAmazonVcPricePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonVcPriceController
 * @description
 * @date 2024-08-26 16:43:20
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonVcPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC价格信息表管理")
public class SomAmazonVcPriceController extends BasicController {

    @Resource
    SomAmazonVcPriceService somAmazonVcPriceService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonVcPriceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonVcPriceVo>> queryByPage(@RequestBody SomAmazonVcPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonVcPriceService.queryByPage(searchVo));
    }

    /**
     * save
     * 新增/编辑
     *
     * @param somAmazonVcPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增/编辑")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomAmazonVcPriceVo somAmazonVcPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonVcPriceService.save(somAmazonVcPriceVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param somAmazonVcPriceVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomAmazonVcPriceVo somAmazonVcPriceVo) throws ValidateException {
        somAmazonVcPriceService.delete(somAmazonVcPriceVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载批量删除模板
     */
    @Operation(summary = "下载批量删除模板")
    @GetMapping(value = "/download-batch-delete-template")
    public String downloadBatchDeleteTemplate() {
        return "forward:/static/excel/VcPriceBatchDeleteTemplate.xlsx";
    }

    @Operation(summary = "导入批量删除")
    @PostMapping(value = "/import-batch-delete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importBatchDelete(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        importParams.setTitleRows(0);
        importParams.setHeadRows(1);
        String[] arr = {"站点", "ASIN"};
        importParams.setImportFields(arr);
        List<SomAmazonVcPriceVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomAmazonVcPriceVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonVcPriceService.importBatchDelete(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonVcPricePageSearchVo searchVo) {
        String data = somAmazonVcPriceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/SomAmazonVcPriceTemplate.xlsx";
    }

    /**
     * importExcel
     * 导入文件
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        String[] importFields = {"站点", "展示码","RRP", "币种"};
        importParams.setImportFields(importFields);
        List<SomAmazonVcPriceVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomAmazonVcPriceVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if(CollectionUtil.isEmpty(list)){
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonVcPriceService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * queryAsinAndSku
     * 根据站点+展示码查询ASIN和SKU
     *
     * @param vo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomAmazonVcPriceVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询ASIN和SKU")
    @PostMapping(value = "/queryAsinAndSku")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomAmazonVcPriceVo> queryAsinAndSku(@RequestBody SomAmazonVcPriceVo vo) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonVcPriceService.queryAsinAndSku(vo));
    }
}
