package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomSpreetailInventoryItemsVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-07-25
*/

@SqlResource("somSpreetailInventoryItems")
public interface SomSpreetailInventoryItemsMapper extends BaseMapper<SomSpreetailInventoryItems> {

}
