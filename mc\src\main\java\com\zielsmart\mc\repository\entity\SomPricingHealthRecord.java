package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * 丢购记录表
 * gen by 代码生成器 2024-08-12
 */

@Table(name = "mc.som_pricing_health_record")
public class SomPricingHealthRecord implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 销售店铺ID
     */
    @Column("seller_id")
    private String sellerId;
    /**
     * 销售市场ID
     */
    @Column("marketplace_id")
    private String marketplaceId;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 发货方式，MFN或AFN
     */
    @Column("fulfillment_type")
    private String fulfillmentType;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 销售负责人工号
     */
    @Column("salesman_code")
    private String salesmanCode;
    /**
     * 销售负责人姓名
     */
    @Column("salesman_name")
    private String salesmanName;
    /**
     * 售价+运费（Price + shipping）
     */
    @Column("landed_price")
    private BigDecimal landedPrice;
    /**
     * 售价
     */
    @Column("listing_price")
    private BigDecimal listingPrice;
    /**
     * 运费
     */
    @Column("shipping")
    private BigDecimal shipping;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 积分，只有日本站点有积分
     */
    @Column("points")
    private Integer points;
    /**
     * 购物车售价+运费（Price + shipping）
     */
    @Column("buy_box_landed_price")
    private BigDecimal buyBoxLandedPrice;
    /**
     * 购物车售价
     */
    @Column("buy_box_listing_price")
    private BigDecimal buyBoxListingPrice;
    /**
     * 购物车运费
     */
    @Column("buy_box_shipping")
    private BigDecimal buyBoxShipping;
    /**
     * 购物车币种
     */
    @Column("buy_box_currency")
    private String buyBoxCurrency;
    /**
     * 购物车积分，只有日本站点有积分
     */
    @Column("buy_box_points")
    private Integer buyBoxPoints;
    /**
     * 不同类目的排名
     */
    @Column("sales_rankings")
    private String salesRankings;
    /**
     * 60天平均售价
     */
    @Column("average_selling_price")
    private BigDecimal averageSellingPrice;
    /**
     * 三方平台售价
     */
    @Column("competitive_price_threshold")
    private BigDecimal competitivePriceThreshold;
    /**
     * 供应商建议零售价
     */
    @Column("msrp_price")
    private BigDecimal msrpPrice;
    /**
     * 最高的14天零售价格
     */
    @Column("retail_offer_price")
    private BigDecimal retailOfferPrice;
    /**
     * 处理状态：10.未处理 20.处理中  30.处理完成 99.无需处理
     */
    @Column("handle_status")
    private Integer handleStatus;
    /**
     * 丢购状态：99.丢失购物车  10.恢复购物车
     */
    @Column("lose_status")
    private Integer loseStatus;
    /**
     * 丢购类型：10.价格不具备优势  20.三方比价 30.未知原因
     */
    @Column("lose_type")
    private Integer loseType;
    /**
     * 丢失购物车时间
     */
    @Column("lose_time")
    private Date loseTime;
    /**
     * 恢复购物车时间
     */
    @Column("recover_time")
    private Date recoverTime;
    /**
     * 备注
     */
    @Column("remark")
    private String remark;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 站点
     */
    @Column("site")
    private String site;

    /**
     * 业务组编码
     */
    @Column("business_group_code")
    private String businessGroupCode;

    /**
     * 60天DMS
     */
    @Column("average_dms")
    private BigDecimal averageDms;

    /**
     * 丢购原因
     */
    @Column("lose_reason")
    private Integer loseReason;

    /**
     * 丢购的三方平台
     */
    @Column("lose_threshold_channel")
    private String loseThresholdChannel;

    /**
     * 比价平台标记方式 10.系统  20.人工
     */
    @Column("lose_threshold_source")
    private Integer loseThresholdSource;

    @Column("rpa_update")
    private Integer rpaUpdate;



    public SomPricingHealthRecord() {
    }

    public Integer getRpaUpdate() {
        return rpaUpdate;
    }

    public void setRpaUpdate(Integer rpaUpdate) {
        this.rpaUpdate = rpaUpdate;
    }

    public Integer getLoseThresholdSource() {
        return loseThresholdSource;
    }

    public void setLoseThresholdSource(Integer loseThresholdSource) {
        this.loseThresholdSource = loseThresholdSource;
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 销售店铺ID
     *
     * @return
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 销售店铺ID
     *
     * @param sellerId
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    /**
     * 销售市场ID
     *
     * @return
     */
    public String getMarketplaceId() {
        return marketplaceId;
    }

    /**
     * 销售市场ID
     *
     * @param marketplaceId
     */
    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 发货方式，MFN或AFN
     *
     * @return
     */
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    /**
     * 发货方式，MFN或AFN
     *
     * @param fulfillmentType
     */
    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 销售负责人工号
     *
     * @return
     */
    public String getSalesmanCode() {
        return salesmanCode;
    }

    /**
     * 销售负责人工号
     *
     * @param salesmanCode
     */
    public void setSalesmanCode(String salesmanCode) {
        this.salesmanCode = salesmanCode;
    }

    /**
     * 销售负责人姓名
     *
     * @return
     */
    public String getSalesmanName() {
        return salesmanName;
    }

    /**
     * 销售负责人姓名
     *
     * @param salesmanName
     */
    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }

    /**
     * 售价+运费（Price + shipping）
     *
     * @return
     */
    public BigDecimal getLandedPrice() {
        return landedPrice;
    }

    /**
     * 售价+运费（Price + shipping）
     *
     * @param landedPrice
     */
    public void setLandedPrice(BigDecimal landedPrice) {
        this.landedPrice = landedPrice;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getListingPrice() {
        return listingPrice;
    }

    /**
     * 售价
     *
     * @param listingPrice
     */
    public void setListingPrice(BigDecimal listingPrice) {
        this.listingPrice = listingPrice;
    }

    /**
     * 运费
     *
     * @return
     */
    public BigDecimal getShipping() {
        return shipping;
    }

    /**
     * 运费
     *
     * @param shipping
     */
    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 积分，只有日本站点有积分
     *
     * @return
     */
    public Integer getPoints() {
        return points;
    }

    /**
     * 积分，只有日本站点有积分
     *
     * @param points
     */
    public void setPoints(Integer points) {
        this.points = points;
    }

    /**
     * 购物车售价+运费（Price + shipping）
     *
     * @return
     */
    public BigDecimal getBuyBoxLandedPrice() {
        return buyBoxLandedPrice;
    }

    /**
     * 购物车售价+运费（Price + shipping）
     *
     * @param buyBoxLandedPrice
     */
    public void setBuyBoxLandedPrice(BigDecimal buyBoxLandedPrice) {
        this.buyBoxLandedPrice = buyBoxLandedPrice;
    }

    /**
     * 购物车售价
     *
     * @return
     */
    public BigDecimal getBuyBoxListingPrice() {
        return buyBoxListingPrice;
    }

    /**
     * 购物车售价
     *
     * @param buyBoxListingPrice
     */
    public void setBuyBoxListingPrice(BigDecimal buyBoxListingPrice) {
        this.buyBoxListingPrice = buyBoxListingPrice;
    }

    /**
     * 购物车运费
     *
     * @return
     */
    public BigDecimal getBuyBoxShipping() {
        return buyBoxShipping;
    }

    /**
     * 购物车运费
     *
     * @param buyBoxShipping
     */
    public void setBuyBoxShipping(BigDecimal buyBoxShipping) {
        this.buyBoxShipping = buyBoxShipping;
    }

    /**
     * 购物车币种
     *
     * @return
     */
    public String getBuyBoxCurrency() {
        return buyBoxCurrency;
    }

    /**
     * 购物车币种
     *
     * @param buyBoxCurrency
     */
    public void setBuyBoxCurrency(String buyBoxCurrency) {
        this.buyBoxCurrency = buyBoxCurrency;
    }

    /**
     * 购物车积分，只有日本站点有积分
     *
     * @return
     */
    public Integer getBuyBoxPoints() {
        return buyBoxPoints;
    }

    /**
     * 购物车积分，只有日本站点有积分
     *
     * @param buyBoxPoints
     */
    public void setBuyBoxPoints(Integer buyBoxPoints) {
        this.buyBoxPoints = buyBoxPoints;
    }

    /**
     * 不同类目的排名
     *
     * @return
     */
    public String getSalesRankings() {
        return salesRankings;
    }

    /**
     * 不同类目的排名
     *
     * @param salesRankings
     */
    public void setSalesRankings(String salesRankings) {
        this.salesRankings = salesRankings;
    }

    /**
     * 60天平均售价
     *
     * @return
     */
    public BigDecimal getAverageSellingPrice() {
        return averageSellingPrice;
    }

    /**
     * 60天平均售价
     *
     * @param averageSellingPrice
     */
    public void setAverageSellingPrice(BigDecimal averageSellingPrice) {
        this.averageSellingPrice = averageSellingPrice;
    }

    /**
     * 三方平台售价
     *
     * @return
     */
    public BigDecimal getCompetitivePriceThreshold() {
        return competitivePriceThreshold;
    }

    /**
     * 三方平台售价
     *
     * @param competitivePriceThreshold
     */
    public void setCompetitivePriceThreshold(BigDecimal competitivePriceThreshold) {
        this.competitivePriceThreshold = competitivePriceThreshold;
    }

    /**
     * 供应商建议零售价
     *
     * @return
     */
    public BigDecimal getMsrpPrice() {
        return msrpPrice;
    }

    /**
     * 供应商建议零售价
     *
     * @param msrpPrice
     */
    public void setMsrpPrice(BigDecimal msrpPrice) {
        this.msrpPrice = msrpPrice;
    }

    /**
     * 最高的14天零售价格
     *
     * @return
     */
    public BigDecimal getRetailOfferPrice() {
        return retailOfferPrice;
    }

    /**
     * 最高的14天零售价格
     *
     * @param retailOfferPrice
     */
    public void setRetailOfferPrice(BigDecimal retailOfferPrice) {
        this.retailOfferPrice = retailOfferPrice;
    }

    /**
     * 处理状态：10.未处理 20.处理中  30.处理完成 99.无需处理
     *
     * @return
     */
    public Integer getHandleStatus() {
        return handleStatus;
    }

    /**
     * 处理状态：10.未处理 20.处理中  30.处理完成 99.无需处理
     *
     * @param handleStatus
     */
    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    /**
     * 丢购状态：99.丢失购物车  10.恢复购物车
     *
     * @return
     */
    public Integer getLoseStatus() {
        return loseStatus;
    }

    /**
     * 丢购状态：99.丢失购物车  10.恢复购物车
     *
     * @param loseStatus
     */
    public void setLoseStatus(Integer loseStatus) {
        this.loseStatus = loseStatus;
    }

    /**
     * 丢购类型：10.价格不具备优势  20.三方比价 30.未知原因
     *
     * @return
     */
    public Integer getLoseType() {
        return loseType;
    }

    /**
     * 丢购类型：10.价格不具备优势  20.三方比价 30.未知原因
     *
     * @param loseType
     */
    public void setLoseType(Integer loseType) {
        this.loseType = loseType;
    }

    /**
     * 丢失购物车时间
     *
     * @return
     */
    public Date getLoseTime() {
        return loseTime;
    }

    /**
     * 丢失购物车时间
     *
     * @param loseTime
     */
    public void setLoseTime(Date loseTime) {
        this.loseTime = loseTime;
    }

    /**
     * 恢复购物车时间
     *
     * @return
     */
    public Date getRecoverTime() {
        return recoverTime;
    }

    /**
     * 恢复购物车时间
     *
     * @param recoverTime
     */
    public void setRecoverTime(Date recoverTime) {
        this.recoverTime = recoverTime;
    }

    /**
     * 备注
     *
     * @return
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     *
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    public String getBusinessGroupCode() {
        return businessGroupCode;
    }

    public void setBusinessGroupCode(String businessGroupCode) {
        this.businessGroupCode = businessGroupCode;
    }

    public BigDecimal getAverageDms() {
        return averageDms;
    }

    public void setAverageDms(BigDecimal averageDms) {
        this.averageDms = averageDms;
    }

    public Integer getLoseReason() {
        return loseReason;
    }

    public void setLoseReason(Integer loseReason) {
        this.loseReason = loseReason;
    }

    public String getLoseThresholdChannel() {
        return loseThresholdChannel;
    }

    public void setLoseThresholdChannel(String loseThresholdChannel) {
        this.loseThresholdChannel = loseThresholdChannel;
    }
}
