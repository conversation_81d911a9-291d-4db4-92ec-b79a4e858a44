package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
/*
 * Miravia Listing信息表
 * gen by 代码生成器 2024-04-29
 */

@Table(name = "mc.som_miravia_listing")
public class SomMiraviaListing implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * Listing唯一标识
     */
    @Column("item_id")
    private String itemId;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * Listing状态
     */
    @Column("status")
    private String status;
    /**
     * 数量
     */
    @Column("quantity")
    private Integer quantity;
    /**
     * 售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 调用接口下载的时间
     */
    @Column("download_time")
    private Date downloadTime;
    /**
     * 每个仓库的库存数量，JSON字符串
     */
    @Column("warehouse_quantities")
    private String warehouseQuantities;
    /**
     * 折扣价
     */
    @Column("sale_price")
    private BigDecimal salePrice;

    public SomMiraviaListing() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * Listing唯一标识
     *
     * @return
     */
    public String getItemId() {
        return itemId;
    }

    /**
     * Listing唯一标识
     *
     * @param itemId
     */
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * Listing状态
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * Listing状态
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 数量
     *
     * @return
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 数量
     *
     * @param quantity
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 调用接口下载的时间
     *
     * @return
     */
    public Date getDownloadTime() {
        return downloadTime;
    }

    /**
     * 调用接口下载的时间
     *
     * @param downloadTime
     */
    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

    /**
     * 每个仓库的库存数量，JSON字符串
     *
     * @return
     */
    public String getWarehouseQuantities() {
        return warehouseQuantities;
    }

    /**
     * 每个仓库的库存数量，JSON字符串
     *
     * @param warehouseQuantities
     */
    public void setWarehouseQuantities(String warehouseQuantities) {
        this.warehouseQuantities = warehouseQuantities;
    }

    /**
     * 折扣价
     *
     * @return
     */
    public BigDecimal getSalePrice() {
        return salePrice;
    }

    /**
     * 折扣价
     *
     * @param salePrice
     */
    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

}
