package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* Temu跨境goods
* gen by 代码生成器 2025-06-26
*/

@Table(name="mc.som_temu_cross_goods")
public class SomTemuCrossGoods implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 商品ID
	 */
	@Column("product_id")
	private Long productId ;
	/**
	 * 商品名称
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 站点集合
	 */
	@Column("sites")
	private String sites ;
	/**
	 * 商品普通属性
	 */
	@Column("product_properties")
	private Object productProperties ;
	/**
	 * 货品JIT模式信息
	 */
	@Column("product_jit_mode")
	private Object productJitMode ;
	/**
	 * sku概要信息列表
	 */
	@Column("product_sku_summaries")
	private Object productSkuSummaries ;
	/**
	 * 上架时间
	 */
	@Column("created_at")
	private Date createdAt ;
	/**
	 * 商品半托管信息
	 */
	@Column("product_semi_managed")
	private Object productSemiManaged ;
	/**
	 * 是否支持订制品  0.否 1.是
	 */
	@Column("is_support_personalization")
	private Integer isSupportPersonalization ;
	/**
	 * SKC外部编码
	 */
	@Column("ext_code")
	private String extCode ;
	/**
	 * 叶子类目，包含：类目ID、类目名称
	 */
	@Column("leaf_cat")
	private Object leafCat ;
	/**
	 * 类目ID
	 */
	@Column("cat_id")
	private Long catId ;
	/**
	 * 类目名称
	 */
	@Column("cat_name")
	private String catName ;
	/**
	 * skc加站点状态
	 */
	@Column("skc_site_status")
	private Integer skcSiteStatus ;
	/**
	 * 类目详情，包含：所有层级类目的ID、所有层级类目的名称
	 */
	@Column("categories")
	private Object categories ;
	/**
	 * SKC ID
	 */
	@Column("product_skc_id")
	private Long productSkcId ;
	/**
	 * SKC主图链接
	 */
	@Column("main_image_url")
	private String mainImageUrl ;
	/**
	 * 是否命中SKC层JIT模式  0.否 1.是
	 */
	@Column("match_skc_jit_mode")
	private Integer matchSkcJitMode ;
	/**
	 * 调用接口下载的时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	public SomTemuCrossGoods() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 商品ID
	*@return
	*/
	public Long getProductId(){
		return  productId;
	}
	/**
	* 商品ID
	*@param  productId
	*/
	public void setProductId(Long productId ){
		this.productId = productId;
	}
	/**
	* 商品名称
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 商品名称
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 站点集合
	*@return
	*/
	public String getSites(){
		return  sites;
	}
	/**
	* 站点集合
	*@param  sites
	*/
	public void setSites(String sites ){
		this.sites = sites;
	}
	/**
	* 商品普通属性
	*@return
	*/
	public Object getProductProperties(){
		return  productProperties;
	}
	/**
	* 商品普通属性
	*@param  productProperties
	*/
	public void setProductProperties(Object productProperties ){
		this.productProperties = productProperties;
	}
	/**
	* 货品JIT模式信息
	*@return
	*/
	public Object getProductJitMode(){
		return  productJitMode;
	}
	/**
	* 货品JIT模式信息
	*@param  productJitMode
	*/
	public void setProductJitMode(Object productJitMode ){
		this.productJitMode = productJitMode;
	}
	/**
	* sku概要信息列表
	*@return
	*/
	public Object getProductSkuSummaries(){
		return  productSkuSummaries;
	}
	/**
	* sku概要信息列表
	*@param  productSkuSummaries
	*/
	public void setProductSkuSummaries(Object productSkuSummaries ){
		this.productSkuSummaries = productSkuSummaries;
	}
	/**
	* 上架时间
	*@return
	*/
	public Date getCreatedAt(){
		return  createdAt;
	}
	/**
	* 上架时间
	*@param  createdAt
	*/
	public void setCreatedAt(Date createdAt ){
		this.createdAt = createdAt;
	}
	/**
	* 商品半托管信息
	*@return
	*/
	public Object getProductSemiManaged(){
		return  productSemiManaged;
	}
	/**
	* 商品半托管信息
	*@param  productSemiManaged
	*/
	public void setProductSemiManaged(Object productSemiManaged ){
		this.productSemiManaged = productSemiManaged;
	}
	/**
	* 是否支持订制品  0.否 1.是
	*@return
	*/
	public Integer getisSupportPersonalization(){
		return  isSupportPersonalization;
	}
	/**
	* 是否支持订制品  0.否 1.是
	*@param  isSupportPersonalization
	*/
	public void setisSupportPersonalization(Integer isSupportPersonalization ){
		this.isSupportPersonalization = isSupportPersonalization;
	}
	/**
	* SKC外部编码
	*@return
	*/
	public String getExtCode(){
		return  extCode;
	}
	/**
	* SKC外部编码
	*@param  extCode
	*/
	public void setExtCode(String extCode ){
		this.extCode = extCode;
	}
	/**
	* 叶子类目，包含：类目ID、类目名称
	*@return
	*/
	public Object getLeafCat(){
		return  leafCat;
	}
	/**
	* 叶子类目，包含：类目ID、类目名称
	*@param  leafCat
	*/
	public void setLeafCat(Object leafCat ){
		this.leafCat = leafCat;
	}
	/**
	* 类目ID
	*@return
	*/
	public Long getCatId(){
		return  catId;
	}
	/**
	* 类目ID
	*@param  catId
	*/
	public void setCatId(Long catId ){
		this.catId = catId;
	}
	/**
	* 类目名称
	*@return
	*/
	public String getCatName(){
		return  catName;
	}
	/**
	* 类目名称
	*@param  catName
	*/
	public void setCatName(String catName ){
		this.catName = catName;
	}
	/**
	* skc加站点状态
	*@return
	*/
	public Integer getSkcSiteStatus(){
		return  skcSiteStatus;
	}
	/**
	* skc加站点状态
	*@param  skcSiteStatus
	*/
	public void setSkcSiteStatus(Integer skcSiteStatus ){
		this.skcSiteStatus = skcSiteStatus;
	}
	/**
	* 类目详情，包含：所有层级类目的ID、所有层级类目的名称
	*@return
	*/
	public Object getCategories(){
		return  categories;
	}
	/**
	* 类目详情，包含：所有层级类目的ID、所有层级类目的名称
	*@param  categories
	*/
	public void setCategories(Object categories ){
		this.categories = categories;
	}
	/**
	* SKC ID
	*@return
	*/
	public Long getProductSkcId(){
		return  productSkcId;
	}
	/**
	* SKC ID
	*@param  productSkcId
	*/
	public void setProductSkcId(Long productSkcId ){
		this.productSkcId = productSkcId;
	}
	/**
	* SKC主图链接
	*@return
	*/
	public String getMainImageUrl(){
		return  mainImageUrl;
	}
	/**
	* SKC主图链接
	*@param  mainImageUrl
	*/
	public void setMainImageUrl(String mainImageUrl ){
		this.mainImageUrl = mainImageUrl;
	}
	/**
	* 是否命中SKC层JIT模式  0.否 1.是
	*@return
	*/
	public Integer getMatchSkcJitMode(){
		return  matchSkcJitMode;
	}
	/**
	* 是否命中SKC层JIT模式  0.否 1.是
	*@param  matchSkcJitMode
	*/
	public void setMatchSkcJitMode(Integer matchSkcJitMode ){
		this.matchSkcJitMode = matchSkcJitMode;
	}
	/**
	* 调用接口下载的时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 调用接口下载的时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
