package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomNocnocListing;
import com.zielsmart.mc.repository.mapper.SomNocnocListingMapper;
import com.zielsmart.mc.vo.SomNocnocListingPageSearchVo;
import com.zielsmart.mc.vo.SomNocnocListingVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-12-25 14:39:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomNocnocListingService {
    
    @Resource
    private SomNocnocListingMapper somNocnocListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomNocnocListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomNocnocListingVo> queryByPage(SomNocnocListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomNocnocListingVo> pageResult = dynamicSqlManager.getMapper(SomNocnocListingMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomNocnocListingVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somNocnocListingVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomNocnocListingVo somNocnocListingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somNocnocListingVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somNocnocListingVo.setAid(IdUtil.fastSimpleUUID());
        //TODO 根据情况判断是否需要添加创建人信息
        somNocnocListingMapper.insert(ConvertUtils.beanConvert(somNocnocListingVo, SomNocnocListing.class));
    }

    /**
     * update
     * 修改
     * @param somNocnocListingVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomNocnocListingVo somNocnocListingVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somNocnocListingVo) || StrUtil.isEmpty(somNocnocListingVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somNocnocListingMapper.createLambdaQuery()
                .andEq("aid",somNocnocListingVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somNocnocListingVo, SomNocnocListing.class));
    }

    /**
     * delete
     * 删除
     * @param somNocnocListingVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomNocnocListingVo somNocnocListingVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somNocnocListingVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        // somNocnocListingMapper.createLambdaQuery().andIn("aid", somNocnocListingVo.getAidList()).delete();
        somNocnocListingMapper.createLambdaQuery().andEq("aid",somNocnocListingVo.getAid()).delete();
    }

    public String export(SomNocnocListingPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomNocnocListingVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomNocnocListingVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }
}
