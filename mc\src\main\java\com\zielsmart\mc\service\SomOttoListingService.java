package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomOttoListing;
import com.zielsmart.mc.repository.mapper.SomOttoListingMapper;
import com.zielsmart.mc.vo.SomOttoListingPageSearchVo;
import com.zielsmart.mc.vo.SomOttoListingVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomOttoListingService {
    
    @Resource
    private SomOttoListingMapper somOttoListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOttoListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomOttoListingVo> queryByPage(SomOttoListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOttoListingVo> pageResult = dynamicSqlManager.getMapper(SomOttoListingMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomOttoListingVo.class, searchVo);
    }

    public String export(SomOttoListingPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomOttoListingVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "OTTO listing列表管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomOttoListingVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }
}
