package com.zielsmart.mc.controller.zbpm;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.util.JSONPObject;
import com.zielsmart.mc.service.zbpm.ZBPMWareHoseService;
import com.zielsmart.mc.vo.zbpm.ZBPMWareHouseVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/zbpm-warehouse", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "ZBPM仓库管理")
public class ZBPMWareHouseController extends BasicController {

    @Resource
    private ZBPMWareHoseService service;

    /**
     * getplatFormSiteList
     * 获取平台站点
     *
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.OAWarehouseVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取平台站点")
    @PostMapping(value = "/getplatFormSiteList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZBPMWareHouseVo>> getplatFormSiteList() {
        return ResultVo.ofSuccess(service.getPlatFormSiteList());
    }


    /**
     * queryByplatFormSite
     * 根据平台站点查询仓库信息
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.zbpm.ZBPMWareHouseVo>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点查询仓库信息")
    @PostMapping(value = "/queryByplatFormSite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<ZBPMWareHouseVo> queryByplatFormSite(@RequestBody ZBPMWareHouseVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(service.queryByplatFormSite(searchVo));
    }

    /**
     * updateWhcodeSlcode
     *
     * @param jsonObject
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新仓库库区信息")
    @PostMapping(value = "/updateWhcodeSlcode")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> updateWhcodeSlcode(@RequestBody JSONObject jsonObject, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUserInfo) throws ValidateException {
        ZBPMWareHouseVo updateVo = JSONUtil.toBean(jsonObject, ZBPMWareHouseVo.class);
        service.updateWhcodeSlcode(updateVo,tokenUserInfo);
        return ResultVo.ofSuccess();
    }
}
