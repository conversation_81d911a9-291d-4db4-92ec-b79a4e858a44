package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McListingInfoAmazonMapper;
import com.zielsmart.mc.repository.mapper.SomHandlingTimeChronopostMapper;
import com.zielsmart.mc.vo.McDearanceProductExVo;
import com.zielsmart.mc.vo.McListingInfoAmazonExVo;
import com.zielsmart.mc.vo.SomHandlingTimeChronopostPageSearchVo;
import com.zielsmart.mc.vo.SomHandlingTimeChronopostVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.logging.log4j.util.Strings;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-03-11 10:45:06
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomHandlingTimeChronopostService {
    
    @Resource
    private SomHandlingTimeChronopostMapper somHandlingTimeChronopostMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McListingInfoAmazonMapper mcListingInfoAmazonMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomHandlingTimeChronopostVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomHandlingTimeChronopostVo> queryByPage(SomHandlingTimeChronopostPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomHandlingTimeChronopostVo> pageResult = dynamicSqlManager.getMapper(SomHandlingTimeChronopostMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomHandlingTimeChronopostVo.class, searchVo);
    }

    /**
     * delete
     * 删除
     * @param somHandlingTimeChronopostVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomHandlingTimeChronopostVo somHandlingTimeChronopostVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somHandlingTimeChronopostVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        somHandlingTimeChronopostMapper.createLambdaQuery().andIn("aid", somHandlingTimeChronopostVo.getAidList()).delete();
    }

    public String export(SomHandlingTimeChronopostPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomHandlingTimeChronopostVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "CHRONOPOST列表(法国站点专用)管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomHandlingTimeChronopostVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public String importExcel(List<SomHandlingTimeChronopostVo> importList, TokenUserInfo tokenUser) throws ValidateException {
        List<SomHandlingTimeChronopost> insertList = new ArrayList<>();
        importList = importList.stream().distinct().collect(Collectors.toList());
        //校验展示码在Amazon listing表中存在
        List<McListingInfoAmazonExVo> frList = mcListingInfoAmazonMapper.searchSellerSkuBySite("Amazon.fr");
        List<String> frSellerSkuList = frList.stream().map(McListingInfoAmazonExVo::getSellerSku).collect(Collectors.toList());
        List<String> errerSkuList = new ArrayList<>();
        for (SomHandlingTimeChronopostVo importVo : importList) {
            if (!frSellerSkuList.contains(importVo.getSellerSku())) {
                errerSkuList.add(importVo.getSellerSku());
            }
            SomHandlingTimeChronopost obj = ConvertUtils.beanConvert(importVo, SomHandlingTimeChronopost.class);
            obj.setAid(IdUtil.fastSimpleUUID());
            obj.setCreateNum(tokenUser.getJobNumber());
            obj.setCreateName(tokenUser.getUserName());
            obj.setCreateTime(DateTime.now().toJdkDate());
            insertList.add(obj);
        }
        if (!errerSkuList.isEmpty()) {
            throw new ValidateException("展示码："+ String.join(",", errerSkuList)+"在亚马逊Listing表中不存在，请检查！");
        }
        somHandlingTimeChronopostMapper.deleteBySellerSkus(insertList.stream().map(SomHandlingTimeChronopost::getSellerSku).collect(Collectors.toList()));
        somHandlingTimeChronopostMapper.insertBatch(insertList);
        return Strings.EMPTY;
    }
}
