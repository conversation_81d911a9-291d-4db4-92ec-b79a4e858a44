package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomWalmartListing;
import com.zielsmart.mc.vo.SomWalmartListingExtVo;
import com.zielsmart.mc.vo.SomWalmartListingPageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-02-08
 */

@SqlResource("somWalmartListing")
public interface SomWalmartListingMapper extends BaseMapper<SomWalmartListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomWalmartListingExtVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWalmartListingExtVo> queryByPage(@Param("pageSearchVo") SomWalmartListingPageSearchVo pageSearchVo, PageRequest pageRequest);
}
