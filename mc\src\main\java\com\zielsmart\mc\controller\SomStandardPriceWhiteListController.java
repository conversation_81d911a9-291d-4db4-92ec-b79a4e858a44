package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomStandardPriceWhiteListService;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomStandardPriceWhiteListController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somStandardPriceWhiteList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "定价白名单管理")
public class SomStandardPriceWhiteListController extends BasicController {

    @Resource
    SomStandardPriceWhiteListService somStandardPriceWhiteListService;

    /**
     * add
     *
     * @param addVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody @Validated SomStandardPriceWhiteListVo addVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somStandardPriceWhiteListService.add(addVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "根据平台站点查询SKU")
    @PostMapping(value = "/select-sku")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<Map<String, Object>>> selectSku(@RequestBody SomStandardPriceWhiteListVo addVo) throws ValidateException {
        return ResultVo.ofSuccess(somStandardPriceWhiteListService.selectSku(addVo));
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomStandardPriceWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomStandardPriceWhiteListVo>> queryByPage(@RequestBody SomStandardPriceWhiteListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somStandardPriceWhiteListService.queryByPage(searchVo));
    }

    /**
     * delete
     *
     * @param deleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomStandardPriceWhiteListVo deleteVo) throws ValidateException {
        somStandardPriceWhiteListService.delete(deleteVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * downloadExcel
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/StandardPriceWhiteListTemplate.xlsx";
    }

    /**
     * importExcel
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "SKU", "发货方式", "生效开始日期", "生效结束日期"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomStandardPriceWhiteListVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomStandardPriceWhiteListVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somStandardPriceWhiteListService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportExcel(@RequestBody SomStandardPriceWhiteListPageSearchVo exportVo) {
        String data = somStandardPriceWhiteListService.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
