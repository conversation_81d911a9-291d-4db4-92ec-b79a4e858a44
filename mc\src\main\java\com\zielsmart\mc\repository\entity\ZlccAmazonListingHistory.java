package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;

import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 商品中心亚马逊Listing推送记录表
* gen by 代码生成器 2024-04-15
*/

@Table(name="mc.zlcc_amazon_listing_history")
public class ZlccAmazonListingHistory implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;

	@Column("mt_aid")
	private String mtAid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 市场ID
	 */
	@Column("marketplace_id")
	private String marketplaceId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * Summaries
	 */
	@Column("summaries")
	private PGobject summaries ;
	/**
	 * Attributes
	 */
	@Column("attributes")
	private PGobject attributes ;
	/**
	 * Offers
	 */
	@Column("offers")
	private PGobject offers ;
	/**
	 * Fulfillment
	 */
	@Column("fulfillment_availability")
	private PGobject fulfillmentAvailability ;
	/**
	 * 删除状态 10正常  99删除
	 */
	@Column("delete_status")
	private Integer deleteStatus ;
	/**
	 * 删除人工号
	 */
	@Column("delete_num")
	private String deleteNum ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改人时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;
	/**
	 * 从平台更新至系统的时间
	 */
	@Column("get_sync_time")
	private Date getSyncTime ;
	/**
	 * 前端数据json
	 */
	@Column("front_json")
	private PGobject frontJson ;
	/**
	 * 状态 1.平台下载正常数据  2.手动新增的数据
	 */
	@Column("data_from")
	private Integer dataFrom ;
	/**
	 * 类目
	 */
	@Column("product_type")
	private String productType ;
	/**
	 * 发布状态 0无需发布  1待发布  20发布成功 30 发布中
	 */
	@Column("publish_status")
	private Integer publishStatus ;
	/**
	 * 推送时间
	 */
	@Column("publish_time")
	private Date publishTime ;
	/**
	 * 插入记录表的创建时间
	 */
	@Column("create_date")
	private Date createDate ;

	public ZlccAmazonListingHistory() {
	}

	public String getMtAid() {
		return mtAid;
	}

	public void setMtAid(String mtAid) {
		this.mtAid = mtAid;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 市场ID
	*@return
	*/
	public String getMarketplaceId(){
		return  marketplaceId;
	}
	/**
	* 市场ID
	*@param  marketplaceId
	*/
	public void setMarketplaceId(String marketplaceId ){
		this.marketplaceId = marketplaceId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}

	/**
	* 删除状态 10正常  99删除
	*@return
	*/
	public Integer getDeleteStatus(){
		return  deleteStatus;
	}
	/**
	* 删除状态 10正常  99删除
	*@param  deleteStatus
	*/
	public void setDeleteStatus(Integer deleteStatus ){
		this.deleteStatus = deleteStatus;
	}
	/**
	* 删除人工号
	*@return
	*/
	public String getDeleteNum(){
		return  deleteNum;
	}
	/**
	* 删除人工号
	*@param  deleteNum
	*/
	public void setDeleteNum(String deleteNum ){
		this.deleteNum = deleteNum;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 最后修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改人时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改人时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}
	/**
	* 从平台更新至系统的时间
	*@return
	*/
	public Date getGetSyncTime(){
		return  getSyncTime;
	}
	/**
	* 从平台更新至系统的时间
	*@param  getSyncTime
	*/
	public void setGetSyncTime(Date getSyncTime ){
		this.getSyncTime = getSyncTime;
	}

	public PGobject getSummaries() {
		return summaries;
	}

	public void setSummaries(PGobject summaries) {
		this.summaries = summaries;
	}

	public PGobject getAttributes() {
		return attributes;
	}

	public void setAttributes(PGobject attributes) {
		this.attributes = attributes;
	}

	public PGobject getOffers() {
		return offers;
	}

	public void setOffers(PGobject offers) {
		this.offers = offers;
	}

	public PGobject getFulfillmentAvailability() {
		return fulfillmentAvailability;
	}

	public void setFulfillmentAvailability(PGobject fulfillmentAvailability) {
		this.fulfillmentAvailability = fulfillmentAvailability;
	}

	public PGobject getFrontJson() {
		return frontJson;
	}

	public void setFrontJson(PGobject frontJson) {
		this.frontJson = frontJson;
	}

	/**
	* 类目
	*@return
	*/
	public String getProductType(){
		return  productType;
	}
	/**
	* 类目
	*@param  productType
	*/
	public void setProductType(String productType ){
		this.productType = productType;
	}

	public Integer getPublishStatus(){
		return  publishStatus;
	}

	public void setPublishStatus(Integer publishStatus ){
		this.publishStatus = publishStatus;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getPublishTime(){
		return  publishTime;
	}
	/**
	* 推送时间
	*@param  publishTime
	*/
	public void setPublishTime(Date publishTime ){
		this.publishTime = publishTime;
	}

	/**
	* 插入记录表的创建时间
	*@return
	*/
	public Date getCreateDate(){
		return  createDate;
	}
	/**
	* 插入记录表的创建时间
	*@param  createDate
	*/
	public void setCreateDate(Date createDate ){
		this.createDate = createDate;
	}

}
