package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomSheinListing;
import com.zielsmart.mc.vo.SomSheinListingReport;
import com.zielsmart.mc.vo.SomSheinListingVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.web.basic.vo.PageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-04-23
*/

@SqlResource("somSheinListing")
public interface SomSheinListingMapper extends BaseMapper<SomSheinListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomSheinListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomSheinListingVo> queryByPage(@Param("searchVo") SomSheinWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 可售库存报表
     * @param searchVo
     * @param pageRequest
     * @return
     */
    PageResult<SomSheinListingReport> stockReport(@Param("searchVo") SomSheinWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

}
