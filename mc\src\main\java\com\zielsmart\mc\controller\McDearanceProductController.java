package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.DearanceProductService;
import com.zielsmart.mc.vo.McDearanceProductExVo;
import com.zielsmart.mc.vo.McDearanceProductPageSearchVo;
import com.zielsmart.mc.vo.McDearanceProductVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title PlatformPropertiesController
 * @description
 * @date 2021-08-09 10:50:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/dearance-product", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "清仓产品管理")
public class McDearanceProductController extends BasicController {

    @Resource
    DearanceProductService dearanceProductService;

    @Operation(summary = "新增")
    @PostMapping(value = "/add")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody @Validated McDearanceProductVo mcDearanceProductVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        dearanceProductService.add(mcDearanceProductVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McDearanceProductExVo>> queryByPage(@RequestBody McDearanceProductPageSearchVo searchVo) {
        return ResultVo.ofSuccess(dearanceProductService.queryByPage(searchVo));
    }

    @Operation(summary = "导出清仓产品信息")
    @PostMapping(value = "/export-mc-dearance-product", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> exportDearanceProduct(@RequestBody McDearanceProductPageSearchVo searchVo) {
        String data = dearanceProductService.exportDearanceProduct(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "生效")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McDearanceProductExVo exVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        dearanceProductService.update(exVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McDearanceProductExVo exVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        dearanceProductService.delete(exVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/DearanceProductTemplate.xlsx";
    }

    @Operation(summary = "下载批量删除导入模板")
    @GetMapping(value="/batch-delete/download")
    public String downloadBatchDeleteExcel(){
        return "forward:/static/excel/DearanceProductDeleteTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        importParams.setTitleRows(0);
        String[] arr = {"平台","站点", "展示码"};
        importParams.setImportFields(arr);
        List<McDearanceProductExVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McDearanceProductExVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String errorMsg = dearanceProductService.importExcel(list, tokenUser);
        if (StrUtil.isNotEmpty(errorMsg)) {
            throw new ValidateException(errorMsg);
        }
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "批量删除")
    @PostMapping(value = "/batch-delete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDelete(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        importParams.setTitleRows(0);
        importParams.setHeadRows(1);
        String[] arr = {"平台","站点", "展示码"};
        importParams.setImportFields(arr);
        List<McDearanceProductExVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McDearanceProductExVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        dearanceProductService.batchDelete(list, tokenUser);
        return ResultVo.ofSuccess();
    }
}
