package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomInventoryChannelConfigService;
import com.zielsmart.mc.vo.SomInventoryChannelConfigPageSearchVo;
import com.zielsmart.mc.vo.SomInventoryChannelConfigVo;
import com.zielsmart.mc.vo.SomInventoryChannelImportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomInventoryChannelConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somInventoryChannelConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "库存渠道配置管理")
public class SomInventoryChannelConfigController extends BasicController{

    @Resource
    SomInventoryChannelConfigService somInventoryChannelConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomInventoryChannelConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomInventoryChannelConfigVo>> queryByPage(@RequestBody SomInventoryChannelConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somInventoryChannelConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somInventoryChannelConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomInventoryChannelConfigVo somInventoryChannelConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somInventoryChannelConfigService.save(somInventoryChannelConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somInventoryChannelConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomInventoryChannelConfigVo somInventoryChannelConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somInventoryChannelConfigService.update(somInventoryChannelConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somInventoryChannelConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomInventoryChannelConfigVo somInventoryChannelConfigVo) throws ValidateException {
        somInventoryChannelConfigService.delete(somInventoryChannelConfigVo);
        return ResultVo.ofSuccess(null);
    }
    /**
     * downloadExcel
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/InventoryChannelConfigTemplate.xlsx";
    }

    /**
     * importExcel
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台","站点", "寄售标识","库存市场","库存渠道"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomInventoryChannelImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomInventoryChannelImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somInventoryChannelConfigService.importExcel(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

}
