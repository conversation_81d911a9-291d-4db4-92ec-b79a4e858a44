package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomTemuCrossPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossPromotionVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-03-12 09:10:57
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuCrossPromotion")
public interface SomTemuCrossPromotionMapper extends BaseMapper<SomTemuCrossPromotion> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult< SomTemuCrossPromotionVo >}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuCrossPromotionVo> queryByPage(@Param("searchVo") SomTemuCrossPromotionPageSearchVo searchVo, PageRequest pageRequest);
}
