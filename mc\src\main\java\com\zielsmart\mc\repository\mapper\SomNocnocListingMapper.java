package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomNocnocListingPageSearchVo;
import com.zielsmart.mc.vo.SomNocnocListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-12-25
*/

@SqlResource("somNocnocListing")
public interface SomNocnocListingMapper extends BaseMapper<SomNocnocListing> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomNocnocListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomNocnocListingVo> queryByPage(@Param("searchVo")SomNocnocListingPageSearchVo searchVo, PageRequest pageRequest);
}
