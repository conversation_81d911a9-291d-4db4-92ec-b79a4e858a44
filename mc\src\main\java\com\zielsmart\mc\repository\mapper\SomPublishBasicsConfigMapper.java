package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomPublishBasicsConfigPageSearchVo;
import com.zielsmart.mc.vo.SomPublishBasicsConfigVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-07-28
*/

@SqlResource("somPublishBasicsConfig")
public interface SomPublishBasicsConfigMapper extends BaseMapper<SomPublishBasicsConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPublishBasicsConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPublishBasicsConfigVo> queryByPage(@Param("searchVo")SomPublishBasicsConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<SomPublishBasicsConfigVo> queryByExcel(@Param("searchVo")SomPublishBasicsConfigPageSearchVo searchVo);

    default void updateBatch(@Param("updateList") List<SomPublishBasicsConfig> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somPublishBasicsConfig.updateBatch"), updateList);
    }

}
