package com.zielsmart.mc.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;

import java.util.Date;

@Data
public class ZlccAmazonListingHistoryVo{

    @Schema(description = "发布状态", name = "publishStatusStr")
    private String publishStatusStr;
    /**
     * 主键
     */

    private String aid ;

    private String mtAid ;
    /**
     * 站点
     */

    private String site ;
    /**
     * 市场ID
     */

    private String marketplaceId ;
    /**
     * 展示码
     */

    private String sellerSku ;
    /**
     * Summaries
     */

    private String summaries ;
    /**
     * Attributes
     */

    private String attributes ;
    /**
     * Offers
     */

    private String offers ;
    /**
     * Fulfillment
     */

    private String fulfillmentAvailability ;
    /**
     * 删除状态 10正常  99删除
     */

    private Integer deleteStatus ;
    /**
     * 删除人工号
     */

    private String deleteNum ;
    /**
     * 创建人工号
     */

    private String createNum ;
    /**
     * 创建人姓名
     */

    private String createName ;
    /**
     * 创建时间
     */

    private Date createTime ;
    /**
     * 最后修改人工号
     */
    @Schema(description = "修改人工号", name = "lastModifyNum")
    private String lastModifyNum ;
    /**
     * 最后修改人姓名
     */
    @Schema(description = "修改人姓名", name = "lastModifyName")
    private String lastModifyName ;
    /**
     * 最后修改人时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "最后修改时间", name = "lastModifyTime")
    private Date lastModifyTime ;
    /**
     * 从平台更新至系统的时间
     */

    private Date getSyncTime ;
    /**
     * 前端数据json
     */

    private String frontJson ;

    @Schema(description = "插入时数据来源 不包含更新 1.平台下载  2.简单上货  3.推荐上货清单", name = "dataFrom")
    private Integer dataFrom;
    /**
     * 类目
     */

    private String productType ;
    /**
     * 发布状态 0无需发布  1待发布  20发布成功 30 发布中
     */

    private Integer publishStatus ;
    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "发布时间", name = "publishTime")
    private Date publishTime ;
    /**
     * 推送完成后 标记更新数据 0不更新 1更新
     */

    private Integer updateData ;
    /**
     * 插入记录表的创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate ;
}
