package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomFreeAddDeal;
import com.zielsmart.mc.vo.SomFreeAddDealExportVo;
import com.zielsmart.mc.vo.SomFreeAddDealExtVo;
import com.zielsmart.mc.vo.SomFreeAddDealPageSearchVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.Date;
import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-08-11
 */

@SqlResource("somFreeAddDeal")
public interface SomFreeAddDealMapper extends BaseMapper<SomFreeAddDeal> {
    /**
     * checkInternalDescription
     * 校验活动命名是否重复
     *
     * @param checkVo
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @history
     */
    Integer checkInternalDescription(@Param("checkVo") SomFreeAddDealExtVo checkVo);

    /**
     * checkRepeat
     * 校验重复
     *
     * @param childAsin
     * @param beginDate
     * @param endDate
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @history
     */
    Integer checkRepeat(@Param("site") String site,@Param("childAsin") String childAsin, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate);

    /**
     * checkRepeatDeal
     * 校验「站点」+「ASIN」+「提报活动起止时间」是否重复
     *
     * @param asinList
     * @param planStartDate
     * @param planEndDate
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @history
     */
    Integer checkRepeatDeal(@Param("aid") String aid,@Param("site") String site,@Param("asinList") List<String> asinList, @Param("planStartDate") Date planStartDate, @Param("planEndDate") Date planEndDate);


    /**
     * checkLDActivity
     * LD 新增/编辑校验
     *
     * @param asinList
     * @param planStartDate
     * @param planEndDate
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    Integer checkLDActivity(@Param("aid") String aid,@Param("site") String site,@Param("asinList") List<String> asinList, @Param("planStartDate") Date planStartDate, @Param("planEndDate") Date planEndDate);

    /**
     * check7DDActivity
     * 7DD 新增/编辑校验
     *
     * @param asinList
     * @param planStartDate
     * @param planEndDate
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    Integer check7DDActivity(@Param("aid") String aid,@Param("site") String site,@Param("asinList") List<String> asinList, @Param("planStartDate") Date planStartDate, @Param("planEndDate") Date planEndDate);

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomFreeAddDealVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<SomFreeAddDealExtVo> queryByPage(@Param("searchVo") SomFreeAddDealPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * querytotalCount
     * 查询总数
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomFreeAddDealExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomFreeAddDealExtVo> querytotalCount(@Param("searchVo")SomFreeAddDealPageSearchVo searchVo);

    /**
     * check7DDUnique
     *
     * @param asinList
     * @param planStartDate
     * @param planEndDate
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    List<Integer> check7DDRepeat(@Param("aid") String aid,@Param("site") String site,@Param("asinList") List<String> asinList, @Param("planStartDate") Date planStartDate, @Param("planEndDate") Date planEndDate);

    /**
     * exportExcel
     * 导出
     * @param exportVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomFreeAddDealExportVo>}
     * <AUTHOR>
     * @history
     */
    List<SomFreeAddDealExportVo> exportExcel(@Param("exportVo")SomFreeAddDealPageSearchVo exportVo);

    /**
     * batchCancel
     * 批量取消
     * <AUTHOR>
     */
    default void batchCancel(@Param("updateList")List<SomFreeAddDeal> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDeal.batchCancel"), updateList);
    }

    /**
     * batchFeedbackCancelResult
     * 批量反馈取消结果
     * <AUTHOR>
     */
    default void batchFeedbackCancelResult(@Param("updateList")List<SomFreeAddDeal> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDeal.batchFeedbackCancelResult"), updateList);
    }

    /**
     * 批量导入编辑
     *
     * @param updateSomFreeAddDeals 需要更新的数据
     */
    default void batchImportEdit(@Param("updateSomPddAndOds") List<SomFreeAddDeal> updateSomFreeAddDeals) {
        if (CollUtil.isEmpty(updateSomFreeAddDeals)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDeal.batchImportEdit"), updateSomFreeAddDeals);
    }

    /**
     * 批量导入反馈修改结果
     *
     * @param updateSomFreeAddDeals 需要更新的数据
     */
    default void batchImportFeedbackEditResult(@Param("updateSomFreeAddDeals") List<SomFreeAddDeal> updateSomFreeAddDeals) {
        if (CollUtil.isEmpty(updateSomFreeAddDeals)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somFreeAddDeal.batchImportFeedbackEditResult"), updateSomFreeAddDeals);
    }
}
