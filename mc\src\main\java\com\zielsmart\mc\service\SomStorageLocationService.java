package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.mapper.McWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.McWarehouseConfigVo;
import com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo;
import com.zielsmart.web.basic.exception.ValidateException;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomStorageLocationService {

    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * querySLTree
     * 获取库区树节点
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public List<SomStorageLocationTreeVo> querySLTree(McWarehouseConfigVo searchVo) throws ValidateException {
//        if (ObjectUtil.isNull(searchVo) || StrUtil.isBlank(searchVo.getPlatform()) || StrUtil.isBlank(searchVo.getSite())) {
//            throw new ValidateException("平台站点不能为空");
//        }
        // 最顶层节点
        List<SomStorageLocationTreeVo> rootList = new ArrayList<>();
        List<McWarehouseConfigVo> warehouseList = dynamicSqlManager.getMapper(McWarehouseConfigMapper.class).findWarehouseByPlatformSite(searchVo);
        List<String> whcodeList = warehouseList.stream().map(m -> m.getUsableWarehouse()).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(whcodeList)){
            List<SomStorageLocationTreeVo> leafList = somStorageLocationMapper.queryByWhcode(whcodeList);
            for (McWarehouseConfigVo warehouseVo : warehouseList) {
                SomStorageLocationTreeVo root = new SomStorageLocationTreeVo();
                root.setKey(warehouseVo.getUsableWarehouse());
                root.setValue(warehouseVo.getUsableWarehouse());
                root.setTitle(warehouseVo.getWarehouseName());
                root.setSelectable(false);
                List<SomStorageLocationTreeVo> tempList = leafList.stream().filter(f -> StrUtil.equals(warehouseVo.getUsableWarehouse(), f.getWhCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(tempList)) {
                    root.setChildren(tempList);
                }
                rootList.add(root);
            }
        }
        return rootList;
    }
}
