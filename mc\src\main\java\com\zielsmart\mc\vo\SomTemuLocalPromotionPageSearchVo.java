package com.zielsmart.mc.vo;

import com.zielsmart.web.basic.vo.PageSearchVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
 * Temu本本营销活动的VO分页查询实体
 */
@Data
@Schema(title = "Temu本本营销活动分页查询实体", name = "SomTemuLocalPromotionPageSearchVo")
public class SomTemuLocalPromotionPageSearchVo extends PageSearchVo {

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "活动状态：1. 未开始 2. 运行中 3. 已结束", name = "activityStatus")
    private Integer activityStatus;

    @Schema(description = "活动类型 2 Lightning deals 13 Advanced big sale 27 Clearance deals 100 Official big sale", name = "activityType")
    private Integer activityType;

    @Schema(description = "是否参与活动 0. 未参与 1. 已参与", name = "isJoinedActivity")
    private String isJoinedActivity;

    @Schema(description = "导出类型 1. 导出全部 2. 导出以报名产品 3. 导出推荐活动", name = "exportType")
    private int exportType;

}
