package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomButLengow;
import com.zielsmart.mc.vo.SomButLengowPageSearchVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.HashMap;
import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-07-25
*/

@SqlResource("somButLengow")
public interface SomButLengowMapper extends BaseMapper<SomButLengow> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomButLengowVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<HashMap<String, Object>> queryByPage(@Param("searchVo")SomButLengowPageSearchVo searchVo, PageRequest pageRequest);

    default void batchUpdate(List<SomButLengow> updateList){
        this.getSQLManager().updateBatch(SqlId.of("somButLengow.batchUdate"), updateList);
    }
}
