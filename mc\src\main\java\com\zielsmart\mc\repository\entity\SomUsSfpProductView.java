package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
 *
 * gen by 代码生成器 2023-01-13
 */

@Table(name = "mc.som_us_sfp_product_view")
public class SomUsSfpProductView implements java.io.Serializable {
    @Column("display_product_code")
    private String displayProductCode;
    @Column("product_main_code")
    private String productMainCode;
    @Column("site")
    private String site;
    @Column("west")
    private Long west;
    @Column("slm_ga")
    private Long slmGa;
    @Column("eastnorth")
    private Long eastnorth;
    @Column("east")
    private Long east;
    @Column("storage_standard")
    private String storageStandard;
    @Column("central")
    private Long central;

    public SomUsSfpProductView() {
    }

    public String getDisplayProductCode() {
        return displayProductCode;
    }

    public void setDisplayProductCode(String displayProductCode) {
        this.displayProductCode = displayProductCode;
    }

    public String getProductMainCode() {
        return productMainCode;
    }

    public void setProductMainCode(String productMainCode) {
        this.productMainCode = productMainCode;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public Long getWest() {
        return west;
    }

    public void setWest(Long west) {
        this.west = west;
    }

    public Long getSlmGa() {
        return slmGa;
    }

    public void setSlmGa(Long slmGa) {
        this.slmGa = slmGa;
    }

    public Long getEastnorth() {
        return eastnorth;
    }

    public void setEastnorth(Long eastnorth) {
        this.eastnorth = eastnorth;
    }

    public Long getEast() {
        return east;
    }

    public void setEast(Long east) {
        this.east = east;
    }

    public String getStorageStandard() {
        return storageStandard;
    }

    public void setStorageStandard(String storageStandard) {
        this.storageStandard = storageStandard;
    }

    public Long getCentral() {
        return central;
    }

    public void setCentral(Long central) {
        this.central = central;
    }
}
