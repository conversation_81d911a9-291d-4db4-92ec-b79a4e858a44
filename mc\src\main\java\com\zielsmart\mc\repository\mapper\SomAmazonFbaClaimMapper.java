package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonFbaClaimVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-03-16
*/

@SqlResource("somAmazonFbaClaim")
public interface SomAmazonFbaClaimMapper extends BaseMapper<SomAmazonFbaClaim> {

    /**
     * 索赔明细
     *
     * @param site      站点
     * @param sellerSku 展示码
     * @return @return {@link List }<{@link SomAmazonFbaClaimVo }>
     * @author: 王帅杰
     * @date: 2023-03-16 04:56:39
     */
    List<SomAmazonFbaClaimVo> claimDetail(@Param("site")String site, @Param("sellerSku")String sellerSku);
}
