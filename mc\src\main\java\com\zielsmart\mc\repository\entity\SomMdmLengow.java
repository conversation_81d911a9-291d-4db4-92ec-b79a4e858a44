package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 乐售MaisonsduMonde.fr站点listing管理
* gen by 代码生成器 2024-09-09
*/

@Table(name="mc.som_mdm_lengow")
public class SomMdmLengow implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * SKU
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 表格数据
	 */
	@Column("excel_json")
	private String excelJson ;
	/**
	 * 创建人
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomMdmLengow() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* SKU
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* SKU
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 表格数据
	*@return
	*/
	public String getExcelJson(){
		return  excelJson;
	}
	/**
	* 表格数据
	*@param  excelJson
	*/
	public void setExcelJson(String excelJson ){
		this.excelJson = excelJson;
	}
	/**
	* 创建人
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
