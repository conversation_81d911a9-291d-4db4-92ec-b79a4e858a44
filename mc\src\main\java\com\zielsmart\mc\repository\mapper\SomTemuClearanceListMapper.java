package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuClearanceList;
import com.zielsmart.mc.vo.SomTemuClearanceListPageSearchVo;
import com.zielsmart.mc.vo.SomTemuClearanceListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTemuClearanceListMapper
 * @description
 * @date 2024-12-26 08:52:17
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somTemuClearanceList")
public interface SomTemuClearanceListMapper extends BaseMapper<SomTemuClearanceList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    查询参数
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuClearanceListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuClearanceListVo> queryByPage(@Param("searchVo") SomTemuClearanceListPageSearchVo searchVo, PageRequest pageRequest);
}
