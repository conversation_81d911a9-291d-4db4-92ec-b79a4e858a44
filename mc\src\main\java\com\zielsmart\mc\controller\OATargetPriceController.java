package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.OATargetPriceService;
import com.zielsmart.mc.vo.OATargetPriceSearchVo;
import com.zielsmart.mc.vo.OATargetPriceSellerSKUVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title OATargetPriceController
 * @description OA目标价管理Controller
 * @date 2021-11-03 16:42:49
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/oa-targetPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "OA目标价管理")
public class OATargetPriceController extends BasicController {

    @Resource
    private OATargetPriceService service;

    @Operation(summary = "获取展示码")
    @PostMapping(value = "/getsellerSku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<OATargetPriceSellerSKUVo>> getsellerSku(@RequestBody OATargetPriceSearchVo searchVo) {
        return ResultVo.ofSuccess(service.getsellerSku(searchVo));
    }

    @Operation(summary = "校验展示码")
    @PostMapping(value = "/checksellerSku")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> checksellerSku(@RequestBody List<OATargetPriceSellerSKUVo> skuVoList) {
        String result = service.checksellerSku(skuVoList);
        if (result.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(result);
        }
    }

    @Operation(summary = "修改目标价")
    @PostMapping(value = "/modifyTargetPrice")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> modifyTargetPrice(@RequestBody List<OATargetPriceSellerSKUVo> skuVoList) {
        if(!skuVoList.isEmpty()){
            service.modifyTargetPrice(skuVoList);
            return ResultVo.ofSuccess();
        }else {
            return ResultVo.ofFail("数据为空");
        }
    }
}
