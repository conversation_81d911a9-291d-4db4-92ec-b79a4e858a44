package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomSupplySourceStore;
import com.zielsmart.mc.repository.entity.SomSupplySourceStoreConfig;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomSupplySourceStoreConfigMapper;
import com.zielsmart.mc.repository.mapper.SomSupplySourceStoreMapper;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigExtVo;
import com.zielsmart.mc.vo.SomSupplySourceStoreConfigSearchVo;
import com.zielsmart.mc.vo.supply.Capabilities;
import com.zielsmart.mc.vo.supply.Configuration;
import com.zielsmart.mc.vo.supply.HeadRoot;
import com.zielsmart.mc.vo.supply.Root;
import com.zielsmart.mc.vo.supply.ext.CapabilitiesExt;
import com.zielsmart.mc.vo.supply.ext.ConfigurationExt;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomSupplySourceStoreConfigService {

    @Resource
    private SomSupplySourceStoreConfigMapper somSupplySourceStoreConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private IMagicService magicService;
    @Value("${x.authorization.token}")
    private String token;
    /**
     * queryDetail
     * 查看
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomSupplySourceStoreConfigExtVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomSupplySourceStoreConfigExtVo queryDetail(SomSupplySourceStoreConfigSearchVo searchVo) throws ValidateException {
        if (StrUtil.isBlank(searchVo.getStoreId())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStoreConfigExtVo resultVo = new SomSupplySourceStoreConfigExtVo();
        List<SomSupplySourceStoreConfig> resultList = new ArrayList<>();
        if (StrUtil.isNotBlank(searchVo.getConfigVersion())) {
            resultList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", searchVo.getStoreId()).andEq("config_version", searchVo.getConfigVersion()).select();
        } else {
            resultList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", searchVo.getStoreId()).desc("config_version").select();
        }
        if (CollectionUtil.isNotEmpty(resultList)) {
            List<McDictionaryInfo> mcDictionaryInfos = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "SupplySourcePushStatus").select();
            List<SomSupplySourceStoreConfig> configVersionList = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", searchVo.getStoreId()).desc("config_version").select();
            List<String> versionList = configVersionList.stream().map(m -> m.getConfigVersion()).collect(Collectors.toList());
            resultVo.setConfigVersionList(versionList);
            SomSupplySourceStoreConfig temp = resultList.get(0);
            resultVo.setConfigVersion(temp.getConfigVersion());
            resultVo.setCreateName(temp.getCreateName());
            resultVo.setCreateTime(temp.getCreateTime());
            resultVo.setPushName(temp.getPushName());
            resultVo.setPushTime(temp.getPushTime());
            resultVo.setPushStatus(temp.getPushStatus());
            if (ObjectUtil.isNotEmpty(temp.getPushStatus())) {
                mcDictionaryInfos.stream().filter(f -> StrUtil.equals(temp.getPushStatus().toString(), f.getItemValue())).findFirst().ifPresent(ps -> {
                    resultVo.setPushStatusName(ps.getItemLable());
                });
            }
            Configuration configuration = JSONUtil.toBean(temp.getConfigurationJsonString(), Configuration.class);
            Capabilities capabilities = JSONUtil.toBean(temp.getCapabilitiesJsonString(), Capabilities.class);
            ConfigurationExt configExt = new ConfigurationExt();
            configExt.setEmail(configuration.getOperationalConfiguration().getContactDetails().getPrimary().getEmail());
            configExt.setPhone(configuration.getOperationalConfiguration().getContactDetails().getPrimary().getPhone());
            configExt.setThroughputCapValue(configuration.getThroughputConfig().getThroughputCap().getValue().toString());
            configExt.setThroughputCapTimeUnit(configuration.getThroughputConfig().getThroughputCap().getTimeUnit());
            configExt.setHandlingTimeValue(configuration.getOperationalConfiguration().getHandlingTime().getValue());
            configExt.setHandlingTimeUnit(configuration.getOperationalConfiguration().getHandlingTime().getTimeUnit());
            configExt.setTimezone(configuration.getTimezone());
            configExt.setDeliveryIsSupported(capabilities.getOutbound().getDeliveryChannel().isSupported());
            configExt.setPickupIsSupported(capabilities.getOutbound().getPickupChannel().isSupported());
            configExt.setInventoryHoldPeriodValue(capabilities.getOutbound().getPickupChannel().getInventoryHoldPeriod().getValue());
            configExt.setInventoryHoldPeriodUnit(capabilities.getOutbound().getPickupChannel().getInventoryHoldPeriod().getTimeUnit());
            resultVo.setConfiguration(configExt);
            CapabilitiesExt capabilitiesExt = new CapabilitiesExt();
            capabilitiesExt.setMonday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getMonday());
            capabilitiesExt.setTuesday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getTuesday());
            capabilitiesExt.setWednesday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getWednesday());
            capabilitiesExt.setThursday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getThursday());
            capabilitiesExt.setFriday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getFriday());
            capabilitiesExt.setSaturday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getSaturday());
            capabilitiesExt.setSunday(capabilities.getOutbound().getOperationalConfiguration().getOperatingHoursByDay().getSunday());
            resultVo.setCapabilities(capabilitiesExt);
        }
        return resultVo;
    }

    /**
     * pushSupplyConfig
     * 推送提货仓配置
     *
     * @param pushVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void pushSupplyConfig(SomSupplySourceStoreConfigSearchVo pushVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(pushVo) || StrUtil.isEmpty(pushVo.getStoreId())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomSupplySourceStore store = dynamicSqlManager.getMapper(SomSupplySourceStoreMapper.class).createLambdaQuery().andEq("aid", pushVo.getStoreId()).single();
        SomSupplySourceStoreConfig config = somSupplySourceStoreConfigMapper.createLambdaQuery().andEq("store_id", pushVo.getStoreId()).andEq("config_version", pushVo.getConfigVersion()).andNotEq("push_status", 20).single();
        if (ObjectUtil.isNotEmpty(config)) {
            if(StrUtil.isBlank(store.getSupplySourceId())){
                throw new ValidateException("当前数据不允许推送提货仓配置");
            }
            config.setPushNum(tokenUser.getJobNumber());
            config.setPushName(tokenUser.getUserName());
            config.setPushTime(DateTime.now().toJdkDate());
            Root root = generRoot(store, config);
            HeadRoot headRoot = new HeadRoot();
            headRoot.setUpdateData(root);
            headRoot.setSupplySourceId(store.getSupplySourceId());
            try{
                ResultVo resultVo = magicService.updateSupplySource(token, headRoot);
                if (resultVo.isSuccess()) {
                    config.setPushStatus(20);
                    somSupplySourceStoreConfigMapper.updateById(config);
                } else {
                    config.setPushStatus(99);
                    somSupplySourceStoreConfigMapper.updateById(config);
                    throw new ValidateException("调用平台接口出错,错误信息:"+resultVo.getMessage());
                }
            }catch (Exception e){
                throw new ValidateException(e.getMessage());
            }
        } else {
            throw new ValidateException("查询不到需推送提货仓配置的数据");
        }
    }

    /**
     * generRoot
     * 组织数据
     *
     * @param store
     * @param config
     * @return {@link com.zielsmart.mc.vo.supply.Root}
     * <AUTHOR>
     * @history
     */
    private Root generRoot(SomSupplySourceStore store, SomSupplySourceStoreConfig config) {
        Configuration configuration = JSONUtil.toBean(config.getConfigurationJsonString(), Configuration.class);
        Capabilities capabilities = JSONUtil.toBean(config.getCapabilitiesJsonString(), Capabilities.class);
        // 组织格式
        Root root = new Root();
        root.setAlias(store.getAlias());
        root.setConfiguration(configuration);
        root.setCapabilities(capabilities);
        return root;
    }
}
