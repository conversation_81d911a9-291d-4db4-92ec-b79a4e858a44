package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McPushStockWhiteList;
import com.zielsmart.mc.repository.entity.SomAmazonVcWhiteList;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.McPushStockWhiteListMapper;
import com.zielsmart.mc.repository.mapper.SomAmazonVcWhiteListMapper;
import com.zielsmart.mc.vo.dict.McDictionaryInfoVo;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchExtVo;
import com.zielsmart.web.basic.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZBPMStockWhiteListExtService {

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private McPushStockWhiteListMapper whiteListMapper;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private SomAmazonVcWhiteListMapper somAmazonVcWhiteListMapper;

    /**
     * getMarketsPlatforms
     * 获取市场和平台
     *
     * @return {@link com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo}
     * <AUTHOR>
     * @history
     */
    public ZBPMStockWhiteListExtVo getMarketsPlatforms() {
        ZBPMStockWhiteListExtVo whiteListVo = new ZBPMStockWhiteListExtVo();
        List<McDictionaryInfoVo> marketList = mcDictionaryInfoMapper.getAllMarkets();
        whiteListVo.setMarketList(marketList);
        List<McDictionaryInfoVo> platforms = mcDictionaryInfoMapper.getAllPlatforms();
        List<String> platformList = platforms.stream().map(m -> m.getItemLable()).collect(Collectors.toList());
        whiteListVo.setPlatformList(platformList);
        return whiteListVo;
    }

    /**
     * queryListings
     * 根据市场、平台和产品编码查询Listing
     *
     * @param searchVoList
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public List<ZBPMStockWhiteListExtVo> queryListings(List<ZBPMWhiteListSearchExtVo> searchVoList) throws ValidateException {
        if (CollectionUtil.isEmpty(searchVoList) || searchVoList.stream().anyMatch(s->StrUtil.isEmpty(s.getMarketCode())) || searchVoList.stream().anyMatch(s->StrUtil.isEmpty(s.getPlatform()))) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<ZBPMStockWhiteListExtVo> resultList=new ArrayList<>();
        for (ZBPMWhiteListSearchExtVo zbpmWhiteListSearchExtVo : searchVoList) {
            List<McDictionaryInfo> dictionaryInfos = mcDictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", zbpmWhiteListSearchExtVo.getPlatform()).andEq("item_value2", zbpmWhiteListSearchExtVo.getMarketCode()).select();
            List<String> sitelist = dictionaryInfos.stream().map(m -> m.getItemValue()).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(sitelist)) {
                throw new ValidateException("该市场不存在有效站点,请检查");
            }
            zbpmWhiteListSearchExtVo.setSiteList(sitelist);
            if (StrUtil.equals(zbpmWhiteListSearchExtVo.getPlatform(),"VC")){
                if (zbpmWhiteListSearchExtVo.getDelFlag()) {
                    resultList.addAll(productSalesMapper.queryExistsVcListing(zbpmWhiteListSearchExtVo));
                } else {
                    resultList.addAll(productSalesMapper.queryNotExistsVcListing(zbpmWhiteListSearchExtVo));
                }
            }else {
                if (zbpmWhiteListSearchExtVo.getDelFlag()) {
                    resultList.addAll(productSalesMapper.queryExistsListing(zbpmWhiteListSearchExtVo));
                } else {
                    resultList.addAll(productSalesMapper.queryNotExistsListing(zbpmWhiteListSearchExtVo));
                }
            }
        }
        return resultList;
    }

    /**
     * addOrDelStockWhiteList
     * 新增或删除库存白名单
     *
     * @param addOrDelList
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void addOrDelStockWhiteList(List<ZBPMStockWhiteListExtVo> addOrDelList) throws ValidateException {
        if (CollectionUtil.isEmpty(addOrDelList)) {
            throw new ValidateException("数据为空,请检查");
        }
        //筛选出非VC平台的数据
        List<ZBPMStockWhiteListExtVo>  noVcList=addOrDelList.stream().filter(m->!StrUtil.equals(m.getPlatform(),"VC")).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noVcList)){
            List<String> platforms = noVcList.stream().filter(m->!StrUtil.equals(m.getPlatform(),"VC")).map(m -> m.getPlatform()).distinct().collect(Collectors.toList());
            List<String> sites = noVcList.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
            List<String> sellerskus = noVcList.stream().map(m -> m.getDisplayProductCode()).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(noVcList.get(0).getDelFlag()) && noVcList.get(0).getDelFlag()) {
                // 删除
                whiteListMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerskus).delete();
            } else {
                // 新增
                List<McPushStockWhiteList> existList = whiteListMapper.createLambdaQuery().andIn("platform", platforms).andIn("site", sites).andIn("seller_sku", sellerskus).select();
                List<McPushStockWhiteList> insertList = new ArrayList<>();
                for (ZBPMStockWhiteListExtVo vo : noVcList) {
                    long count = existList.stream().filter(f -> StrUtil.equalsIgnoreCase(vo.getPlatform(), f.getPlatform()) && StrUtil.equalsIgnoreCase(vo.getSite(), f.getSite()) && StrUtil.equals(vo.getDisplayProductCode(), f.getSellerSku())).count();
                    if (count > 0) {
                        continue;
                    }
                    McPushStockWhiteList insertObj = new McPushStockWhiteList();
                    insertObj.setAid(IdUtil.fastSimpleUUID());
                    insertObj.setPlatform(vo.getPlatform());
                    insertObj.setSite(vo.getSite());
                    insertObj.setSellerSku(vo.getDisplayProductCode());
                    insertObj.setCreateName(vo.getPromoterName());
                    insertObj.setCreateNum(vo.getPromoter());
                    insertObj.setCreateTime(DateTime.now().toJdkDate());
                    insertList.add(insertObj);
                }
                List<McPushStockWhiteList> distinctInsertList = insertList.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        item -> item.getSite() + "_" + item.getSellerSku(), // 组合唯一键
                                        item -> item,
                                        (oldValue, newValue) -> oldValue  // 保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
                if (CollectionUtil.isNotEmpty(distinctInsertList)) {
                    whiteListMapper.insertBatch(distinctInsertList);
                }
            }
        }
        //筛选出VC平台的数据
        List<ZBPMStockWhiteListExtVo>  vcList=addOrDelList.stream().filter(m->StrUtil.equals(m.getPlatform(),"VC")).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(vcList)){
            List<String> vendorCodes = vcList.stream().map(m -> m.getVendorCode()).distinct().collect(Collectors.toList());
            List<String> sellerskus = vcList.stream().map(m -> m.getDisplayProductCode()).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(vcList.get(0).getDelFlag()) && vcList.get(0).getDelFlag()) {
                // 删除
                somAmazonVcWhiteListMapper.createLambdaQuery().andIn("vendor_code", vendorCodes).andIn("seller_sku", sellerskus).delete();
            } else {
                // 新增
                List<SomAmazonVcWhiteList> existList = somAmazonVcWhiteListMapper.createLambdaQuery().andIn("vendor_code", vendorCodes).andIn("seller_sku", sellerskus).select();
                List<SomAmazonVcWhiteList> insertList = new ArrayList<>();
                for (ZBPMStockWhiteListExtVo vo : vcList) {
                    long count = existList.stream().filter(f -> StrUtil.equalsIgnoreCase(vo.getVendorCode(), f.getVendorCode()) && StrUtil.equals(vo.getDisplayProductCode(), f.getSellerSku())).count();
                    if (count > 0) {
                        continue;
                    }
                    SomAmazonVcWhiteList insertObj = new SomAmazonVcWhiteList();
                    insertObj.setAid(IdUtil.fastSimpleUUID());
                    insertObj.setVendorCode(vo.getVendorCode());
                    insertObj.setSellerSku(vo.getDisplayProductCode());
                    insertObj.setCreateName(vo.getPromoterName());
                    insertObj.setCreateNum(vo.getPromoter());
                    insertObj.setCreateTime(DateTime.now().toJdkDate());
                    insertList.add(insertObj);
                }
                List<SomAmazonVcWhiteList> distinctInsertList = insertList.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        item -> item.getVendorCode() + "_" + item.getSellerSku(), // 组合唯一键
                                        item -> item,
                                        (oldValue, newValue) -> oldValue  // 保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
                if (CollectionUtil.isNotEmpty(distinctInsertList)) {
                    somAmazonVcWhiteListMapper.insertBatch(distinctInsertList);
                }
            }
        }
    }
}