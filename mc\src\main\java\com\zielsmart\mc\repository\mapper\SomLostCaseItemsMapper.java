package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomLostCaseItemsVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-04-03
*/

@SqlResource("somLostCaseItems")
public interface SomLostCaseItemsMapper extends BaseMapper<SomLostCaseItems> {

    List<SomLostCaseItemsVo> compensationDetail(@Param("id")String id);
}
