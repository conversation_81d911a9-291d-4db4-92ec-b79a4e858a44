package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.repository.entity.SomAliExpressSkuList;
import com.zielsmart.mc.vo.SomAliExpressSkuListExportVo;
import com.zielsmart.mc.vo.SomAliExpressSkuListPageSearchVo;
import com.zielsmart.mc.vo.SomAliExpressSkuListVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-11-05
*/

@SqlResource("somAliExpressSkuList")
public interface SomAliExpressSkuListMapper extends BaseMapper<SomAliExpressSkuList> {


    /**
     * 根据查询参数查询sku信息
     *  注：返回的是 List，非 Page
     *
     * @param searchVo 查询参数
     * @return List<SomAliExpressSkuListVo>
     */
    List<SomAliExpressSkuListVo> queryByParam(@Param("searchVo") SomAliExpressSkuListPageSearchVo searchVo);

    /**
     * 查询导出列表数据，需要关联主表导出
     *
     * @param searchVo 查询参数
     * @return List<SomAliExpressSkuListExportVo>
     */
    List<SomAliExpressSkuListExportVo> querySkuExportList(@Param("searchVo") SomAliExpressSkuListPageSearchVo searchVo);
}
