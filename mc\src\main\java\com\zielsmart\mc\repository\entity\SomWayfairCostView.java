package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * Wayfair平台费用表
 * gen by 代码生成器 2024-02-28
 */

@Table(name = "mc.som_wayfair_cost_view")
public class SomWayfairCostView implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * 产品类型
     */
    @Column("product_type")
    private String productType;
    /**
     * 四级分类编码
     */
    @Column("category_code")
    private String categoryCode;
    /**
     * 四级分类名称
     */
    @Column("category_name")
    private String categoryName;
    /**
     * BU组
     */
    @Column("bu_name")
    private String buName;
    /**
     * 毛重，KG
     */
    @Column("weight")
    private BigDecimal weight;
    /**
     * 第一长，CM
     */
    @Column("length")
    private BigDecimal length;
    /**
     * 第二长，CM
     */
    @Column("width")
    private BigDecimal width;
    /**
     * 第三长，CM
     */
    @Column("height")
    private BigDecimal height;
    /**
     * 体积，m³
     */
    @Column("volume")
    private BigDecimal volume;
    /**
     * 业务负责人工号
     */
    @Column("business_owner_code")
    private String businessOwnerCode;
    /**
     * 业务负责人姓名
     */
    @Column("business_owner_name")
    private String businessOwnerName;
    /**
     * 配送费/件
     */
    @Column("delivery_cost")
    private BigDecimal deliveryCost;
    /**
     * 仓储费/天/m³
     */
    @Column("storage_charge")
    private BigDecimal storageCharge;
    /**
     * RTV召回费用
     */
    @Column("rtv_cost")
    private BigDecimal rtvCost;
    /**
     * VAS费用
     */
    @Column("vas_cost")
    private BigDecimal vasCost;
    /**
     * 币种：USD、EUR
     */
    @Column("currency")
    private String currency;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 创建时间
     */
    @Column("category_code_three_level")
    private String categoryCodeThreeLevel;
    /**
     * 跨渠道配送费/件
     */
    @Column("cross_channel_delivery_cost")
    private BigDecimal crossChannelDeliveryCost;
    /**
     * 跨渠道配送费/2件
     */
    @Column("cross_channel_delivery_cost_2_unit")
    private BigDecimal crossChannelDeliveryCost2Unit;
    /**
     * 跨渠道配送费/3件
     */
    @Column("cross_channel_delivery_cost_3_unit")
    private BigDecimal crossChannelDeliveryCost3Unit;
    /**
     * 跨渠道配送费/4件
     */
    @Column("cross_channel_delivery_cost_4_unit")
    private BigDecimal crossChannelDeliveryCost4Unit;
    /**
     * 跨渠道配送费/5件
     */
    @Column("cross_channel_delivery_cost_5_unit")
    private BigDecimal crossChannelDeliveryCost5Unit;


    public SomWayfairCostView() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * SKU
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 产品类型
     *
     * @return
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 产品类型
     *
     * @param productType
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * 四级分类编码
     *
     * @return
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 四级分类编码
     *
     * @param categoryCode
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    /**
     * 四级分类名称
     *
     * @return
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 四级分类名称
     *
     * @param categoryName
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * BU组
     *
     * @return
     */
    public String getbuName() {
        return buName;
    }

    /**
     * BU组
     *
     * @param buName
     */
    public void setbuName(String buName) {
        this.buName = buName;
    }

    /**
     * 毛重，KG
     *
     * @return
     */
    public BigDecimal getWeight() {
        return weight;
    }

    /**
     * 毛重，KG
     *
     * @param weight
     */
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    /**
     * 第一长，CM
     *
     * @return
     */
    public BigDecimal getLength() {
        return length;
    }

    /**
     * 第一长，CM
     *
     * @param length
     */
    public void setLength(BigDecimal length) {
        this.length = length;
    }

    /**
     * 第二长，CM
     *
     * @return
     */
    public BigDecimal getWidth() {
        return width;
    }

    /**
     * 第二长，CM
     *
     * @param width
     */
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    /**
     * 第三长，CM
     *
     * @return
     */
    public BigDecimal getHeight() {
        return height;
    }

    /**
     * 第三长，CM
     *
     * @param height
     */
    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    /**
     * 体积，m³
     *
     * @return
     */
    public BigDecimal getVolume() {
        return volume;
    }

    /**
     * 体积，m³
     *
     * @param volume
     */
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    /**
     * 业务负责人工号
     *
     * @return
     */
    public String getBusinessOwnerCode() {
        return businessOwnerCode;
    }

    /**
     * 业务负责人工号
     *
     * @param businessOwnerCode
     */
    public void setBusinessOwnerCode(String businessOwnerCode) {
        this.businessOwnerCode = businessOwnerCode;
    }

    /**
     * 业务负责人姓名
     *
     * @return
     */
    public String getBusinessOwnerName() {
        return businessOwnerName;
    }

    /**
     * 业务负责人姓名
     *
     * @param businessOwnerName
     */
    public void setBusinessOwnerName(String businessOwnerName) {
        this.businessOwnerName = businessOwnerName;
    }

    /**
     * 配送费/件
     *
     * @return
     */
    public BigDecimal getDeliveryCost() {
        return deliveryCost;
    }

    /**
     * 配送费/件
     *
     * @param deliveryCost
     */
    public void setDeliveryCost(BigDecimal deliveryCost) {
        this.deliveryCost = deliveryCost;
    }

    /**
     * 仓储费/天/m³
     *
     * @return
     */
    public BigDecimal getStorageCharge() {
        return storageCharge;
    }

    /**
     * 仓储费/天/m³
     *
     * @param storageCharge
     */
    public void setStorageCharge(BigDecimal storageCharge) {
        this.storageCharge = storageCharge;
    }

    /**
     * RTV召回费用
     *
     * @return
     */
    public BigDecimal getRtvCost() {
        return rtvCost;
    }

    /**
     * RTV召回费用
     *
     * @param rtvCost
     */
    public void setRtvCost(BigDecimal rtvCost) {
        this.rtvCost = rtvCost;
    }

    /**
     * VAS费用
     *
     * @return
     */
    public BigDecimal getVasCost() {
        return vasCost;
    }

    /**
     * VAS费用
     *
     * @param vasCost
     */
    public void setVasCost(BigDecimal vasCost) {
        this.vasCost = vasCost;
    }

    /**
     * 币种：USD、EUR
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种：USD、EUR
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCategoryCodeThreeLevel() {
        return categoryCodeThreeLevel;
    }

    public void setCategoryCodeThreeLevel(String categoryCodeThreeLevel) {
        this.categoryCodeThreeLevel = categoryCodeThreeLevel;
    }

    public BigDecimal getCrossChannelDeliveryCost() {
        return crossChannelDeliveryCost;
    }

    public void setCrossChannelDeliveryCost(BigDecimal crossChannelDeliveryCost) {
        this.crossChannelDeliveryCost = crossChannelDeliveryCost;
    }

    public BigDecimal getCrossChannelDeliveryCost2Unit() {
        return crossChannelDeliveryCost2Unit;
    }

    public void setCrossChannelDeliveryCost2Unit(BigDecimal crossChannelDeliveryCost2Unit) {
        this.crossChannelDeliveryCost2Unit = crossChannelDeliveryCost2Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost3Unit() {
        return crossChannelDeliveryCost3Unit;
    }

    public void setCrossChannelDeliveryCost3Unit(BigDecimal crossChannelDeliveryCost3Unit) {
        this.crossChannelDeliveryCost3Unit = crossChannelDeliveryCost3Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost4Unit() {
        return crossChannelDeliveryCost4Unit;
    }

    public void setCrossChannelDeliveryCost4Unit(BigDecimal crossChannelDeliveryCost4Unit) {
        this.crossChannelDeliveryCost4Unit = crossChannelDeliveryCost4Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost5Unit() {
        return crossChannelDeliveryCost5Unit;
    }

    public void setCrossChannelDeliveryCost5Unit(BigDecimal crossChannelDeliveryCost5Unit) {
        this.crossChannelDeliveryCost5Unit = crossChannelDeliveryCost5Unit;
    }
}
