package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomPricingHealthRecordWhiteService;
import com.zielsmart.mc.vo.SomPricingHealthRecordWhitePageSearchVo;
import com.zielsmart.mc.vo.SomPricingHealthRecordWhiteVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPricingHealthRecordWhiteController
 * @description
 * @date 2024-11-25 17:57:12
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somPricingHealthRecordWhite", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "丢购白名单管理")
public class SomPricingHealthRecordWhiteController extends BasicController{

    @Resource
    SomPricingHealthRecordWhiteService somPricingHealthRecordWhiteService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPricingHealthRecordWhiteVo>> queryByPage(@RequestBody SomPricingHealthRecordWhitePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somPricingHealthRecordWhiteService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomPricingHealthRecordWhiteVo somPricingHealthRecordWhiteVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPricingHealthRecordWhiteService.save(somPricingHealthRecordWhiteVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomPricingHealthRecordWhiteVo somPricingHealthRecordWhiteVo) throws ValidateException {
        somPricingHealthRecordWhiteService.delete(somPricingHealthRecordWhiteVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomPricingHealthRecordWhitePageSearchVo searchVo){
        String data = somPricingHealthRecordWhiteService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * 下载导入模板
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-import-template")
    public String downloadImportTemplate() {
        return "forward:/static/excel/PricingHealthRecordWhiteImportTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        importParams.setTitleRows(0);
        importParams.setHeadRows(1);
        String[] arr = {"站点", "展示码"};
        importParams.setImportFields(arr);
        List<SomPricingHealthRecordWhiteVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomPricingHealthRecordWhiteVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somPricingHealthRecordWhiteService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }
}
