package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomTemuLocalSkuService;
import com.zielsmart.mc.vo.SomTemuListingReport;
import com.zielsmart.mc.vo.SomTemuLocalSkuPageSearchVo;
import com.zielsmart.mc.vo.SomTemuLocalSkuVo;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuLocalListingController
 * @description Temu Local Listing 管理
 * @date 2025-06-26 15:51:35
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuLocalListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu Local Listing 管理")
public class SomTemuLocalListingController {

    @Resource
    private SomTemuLocalSkuService somTemuLocalSkuService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuLocalSkuVo>> queryByPage(@RequestBody SomTemuLocalSkuPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuLocalSkuService.queryByPage(searchVo));
    }

    @Operation(summary = "URL链接下载")
    @PostMapping(value = "/url/export")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> exportUrl(@RequestBody SomTemuLocalSkuPageSearchVo searchVo) {
        String data = somTemuLocalSkuService.exportUrl(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "本本库存报表")
    @PostMapping(value = "/stock-report")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuListingReport>> stockReport(@RequestBody SomTemuLocalSkuPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somTemuLocalSkuService.stockReport(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomTemuLocalSkuPageSearchVo searchVo){
        String data = somTemuLocalSkuService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
