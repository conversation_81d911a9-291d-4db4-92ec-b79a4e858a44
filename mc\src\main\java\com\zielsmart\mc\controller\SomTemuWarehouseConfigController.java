package com.zielsmart.mc.controller;

import com.zielsmart.mc.repository.entity.SomTemuWarehouse;
import com.zielsmart.mc.service.SomTemuWarehouseConfigService;
import com.zielsmart.mc.vo.SomTemuWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuWarehouseConfigParamVo;
import com.zielsmart.mc.vo.SomTemuWarehouseConfigVo;
import com.zielsmart.mc.vo.SomTemuWarehouseVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somTemuWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "拼多多可售仓库配置管理")
public class SomTemuWarehouseConfigController extends BasicController{

    @Resource
    SomTemuWarehouseConfigService somTemuWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomTemuWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomTemuWarehouseConfigVo>> queryByPage(@RequestBody SomTemuWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuWarehouseConfigService.queryByPage(searchVo));
    }

    @Operation(summary = "Temu仓库列表")
    @PostMapping(value = "/warehouse-list")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomTemuWarehouseVo>> warehouseList(@RequestBody SomTemuWarehouseConfigPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somTemuWarehouseConfigService.warehouseList(searchVo));
    }
    /**
     * save
     *
     * @param somTemuWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomTemuWarehouseConfigVo somTemuWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuWarehouseConfigService.save(somTemuWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somTemuWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomTemuWarehouseConfigVo somTemuWarehouseConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somTemuWarehouseConfigService.update(somTemuWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somTemuWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomTemuWarehouseConfigVo somTemuWarehouseConfigVo) throws ValidateException {
        somTemuWarehouseConfigService.delete(somTemuWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 格式化数据 店铺ID
     * @param somTemuWarehouseConfigParamVo
     * @return void
     * <AUTHOR>
     */
    @Operation(summary = "格式化数据")
    @PostMapping(value = "/format-data")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> formatData(@RequestBody SomTemuWarehouseConfigParamVo somTemuWarehouseConfigParamVo) throws ValidateException {
        somTemuWarehouseConfigService.formatData(somTemuWarehouseConfigParamVo);
        return ResultVo.ofSuccess(null);
    }
}
