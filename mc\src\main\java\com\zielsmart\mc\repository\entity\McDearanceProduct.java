package com.zielsmart.mc.repository.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 清仓产品表
* gen by 代码生成器 2021-08-09
*/

@Table(name="mc.mc_dearance_product")
public class McDearanceProduct implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("sell_sku")
	private String sellSku ;
	/**
	 * sku
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 状态  0 未生效  1 生效   99 删除
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 创建人
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	/**
	 * 状态
	 */
	@Column("total_stock")
	private Integer totalStock ;

	/**
	 * 最后修改时间
	 */
	@Column("whcode_update_time")
	private Date whcodeUpdateTime ;


	/**
	 * 来源
	 */
	@Column("robotization_flag")
	private Integer robotizationFlag ;

	public McDearanceProduct() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellSku(){
		return  sellSku;
	}
	/**
	* 展示码
	*@param  sellSku
	*/
	public void setSellSku(String sellSku ){
		this.sellSku = sellSku;
	}
	/**
	* sku
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* sku
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 状态
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 创建人
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

	public Integer getTotalStock() {
		return totalStock;
	}

	public void setTotalStock(Integer totalStock) {
		this.totalStock = totalStock;
	}

	public Date getWhcodeUpdateTime() {
		return whcodeUpdateTime;
	}

	public void setWhcodeUpdateTime(Date whcodeUpdateTime) {
		this.whcodeUpdateTime = whcodeUpdateTime;
	}

	/**
	 * 来源
	 *@return
	 */
	public Integer getRobotizationFlag(){
		return  robotizationFlag;
	}
	/**
	 * 来源
	 *@param  robotizationFlag
	 */
	public void setRobotizationFlag(Integer robotizationFlag ){
		this.robotizationFlag = robotizationFlag;
	}
}
