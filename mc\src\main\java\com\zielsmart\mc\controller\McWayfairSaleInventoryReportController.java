package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McWayfairSaleInventoryReportService;
import com.zielsmart.mc.vo.McWayfairSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.McWayfairSaleInventoryReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/wayfair-sale-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Wayfair可售库存报表")
public class McWayfairSaleInventoryReportController extends BasicController {

    @Resource
    private McWayfairSaleInventoryReportService service;

    /**
     * queryByPage
     * 查询wayfair可售库存报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWayfairSaleInventoryReportVo>> queryByPage(@RequestBody McWayfairSaleInventoryReportSearchVo searchVo) {
        return ResultVo.ofSuccess(service.queryWayfairSaleInventoryReport(searchVo));
    }

    /**
     * export
     * 导出wayfair可售库存报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody McWayfairSaleInventoryReportSearchVo searchVo) throws ValidateException {
        String data = service.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
