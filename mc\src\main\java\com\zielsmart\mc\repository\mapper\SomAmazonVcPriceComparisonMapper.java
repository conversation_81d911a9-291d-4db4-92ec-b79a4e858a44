package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomAmazonVcPriceComparisonPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceComparisonVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-08-26
 */

@SqlResource("somAmazonVcPriceComparison")
public interface SomAmazonVcPriceComparisonMapper extends BaseMapper<SomAmazonVcPriceComparison> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomAmazonVcPriceComparisonVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonVcPriceComparisonVo> queryByPage(@Param("searchVo") SomAmazonVcPriceComparisonPageSearchVo searchVo, PageRequest pageRequest);
}
