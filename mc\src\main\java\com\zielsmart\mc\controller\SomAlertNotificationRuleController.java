package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomAlertNotificationRuleService;
import com.zielsmart.mc.vo.SomAlertNotificationRuleAdd;
import com.zielsmart.mc.vo.SomAlertNotificationRulePageSearchVo;
import com.zielsmart.mc.vo.SomAlertNotificationRuleVo;
import com.zielsmart.mc.vo.SomBatchDeleteVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAlertNotificationRuleController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAlertNotificationRule", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "预警通知规则列表管理")
public class SomAlertNotificationRuleController extends BasicController{

    @Resource
    SomAlertNotificationRuleService somAlertNotificationRuleService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAlertNotificationRuleVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAlertNotificationRuleVo>> queryByPage(@RequestBody SomAlertNotificationRulePageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somAlertNotificationRuleService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somAlertNotificationRuleAdd
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomAlertNotificationRuleAdd somAlertNotificationRuleAdd, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAlertNotificationRuleService.save(somAlertNotificationRuleAdd,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somAlertNotificationRuleVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomAlertNotificationRuleVo somAlertNotificationRuleVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAlertNotificationRuleService.update(somAlertNotificationRuleVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somBatchDeleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomBatchDeleteVo somBatchDeleteVo) throws ValidateException {
        somAlertNotificationRuleService.delete(somBatchDeleteVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "查询全部预警通知规则名称")
    @PostMapping(value = "/findAllAlertNotificationRuleName")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<String>> findAllAlertNotificationRuleName(@RequestBody SomAlertNotificationRulePageSearchVo somAlertNotificationRulePageSearchVo) throws ValidateException {
        return ResultVo.ofSuccess(somAlertNotificationRuleService.findAllAlertNotificationRuleName(somAlertNotificationRulePageSearchVo));
    }
}
