package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
 * Wayfair站点配置
 * gen by 代码生成器 2024-02-28
 */

@Table(name = "mc.som_wayfair_cost_config_site")
public class SomWayfairCostConfigSite implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * som_wayfair_cost_config表的aid
     */
    @Column("cost_config_id")
    private String costConfigId;

    public SomWayfairCostConfigSite() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * som_wayfair_cost_config表的aid
     *
     * @return
     */
    public String getCostConfigId() {
        return costConfigId;
    }

    /**
     * som_wayfair_cost_config表的aid
     *
     * @param costConfigId
     */
    public void setCostConfigId(String costConfigId) {
        this.costConfigId = costConfigId;
    }

}
