package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomDealSearchVo;
import com.zielsmart.mc.vo.SomDealVo;
import com.zielsmart.mc.vo.ValidateDealVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

@SqlResource("somDeal")
public interface SomDealMapper extends BaseMapper {

    List<SomDealVo> searchDealList(@Param("searchVo") SomDealSearchVo searchVo);

    /**
     * checkActivityDeal
     * 校验营销活动
     * @param validateVo
     * @return {@link java.util.List<java.lang.Integer>}
     * <AUTHOR>
     * @history
     */
    List<ValidateDealVo> checkCouponAndDeal(@Param("validateVo") ValidateDealVo validateVo);
}
