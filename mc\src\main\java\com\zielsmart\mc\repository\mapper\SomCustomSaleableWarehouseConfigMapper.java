package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
*
* gen by 代码生成器 mapper 2025-06-10
*/

@SqlResource("somCustomSaleableWarehouseConfig")
public interface SomCustomSaleableWarehouseConfigMapper extends BaseMapper<SomCustomSaleableWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomCustomSaleableWarehouseConfigVo> queryByPage(@Param("searchVo")SomCustomSaleableWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<SomCustomSaleableWarehouseConfigVo> export(SomCustomSaleableWarehouseConfigPageSearchVo searchVo);
}
