package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* 预警通知规则列表
* gen by 代码生成器 2023-02-15
*/

@Table(name="mc.som_alert_notification_rule")
public class SomAlertNotificationRule implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private String aid;
	/**
	 * 预警规则名称
	 */
	@Column("alert_rule_name")
	private String alertRuleName;
	/**
	 * 规则类型1邮件,2订单
	 */
	@Column("rule_type")
	private String ruleType;
	/**
	 * 预警通知时间段
	 */
	@Column("alert_notification_time")
	private String alertNotificationTime;
	/**
	 * 预警通知类型1指定人员通知,2飞书群组通知
	 */
	@Column("alert_notification_type")
	private String alertNotificationType;
	/**
	 * 预警通知人员/群组名称
	 */
	@Column("alert_notification_object")
	private String alertNotificationObject;
	/**
	 * 预警通知形式
	 */
	@Column("alert_notification_modality")
	private String alertNotificationModality;
	/**
	 * 飞书应用内预警提醒内容
1飞书应用内通知,3 飞书应用内通知+短信,5 飞书应用内通知+电话,7 飞书应用内通知+短信+电话
	 */
	@Column("alert_notification_content")
	private String alertNotificationContent;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAlertNotificationRule() {
	}

	/**
	* 主键id
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键id
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 预警规则名称
	*@return
	*/
	public String getAlertRuleName(){
		return  alertRuleName;
	}
	/**
	* 预警规则名称
	*@param  alertRuleName
	*/
	public void setAlertRuleName(String alertRuleName ){
		this.alertRuleName = alertRuleName;
	}
	/**
	* 规则类型1邮件,2订单
	*@return
	*/
	public String getRuleType(){
		return  ruleType;
	}
	/**
	* 规则类型1邮件,2订单
	*@param  ruleType
	*/
	public void setRuleType(String ruleType){
		this.ruleType = ruleType;
	}
	/**
	* 预警通知时间段
	*@return
	*/
	public String getAlertNotificationTime(){
		return  alertNotificationTime;
	}
	/**
	* 预警通知时间段
	*@param  alertNotificationTime
	*/
	public void setAlertNotificationTime(String alertNotificationTime ){
		this.alertNotificationTime = alertNotificationTime;
	}
	/**
	* 预警通知类型1指定人员通知,2飞书群组通知
	*@return
	*/
	public String getAlertNotificationType(){
		return  alertNotificationType;
	}
	/**
	* 预警通知类型1指定人员通知,2飞书群组通知
	*@param  alertNotificationType
	*/
	public void setAlertNotificationType(String alertNotificationType ){
		this.alertNotificationType = alertNotificationType;
	}
	/**
	* 预警通知人员/群组名称
	*@return
	*/
	public String getAlertNotificationObject(){
		return  alertNotificationObject;
	}
	/**
	* 预警通知人员/群组名称
	*@param  alertNotificationObject
	*/
	public void setAlertNotificationObject(String alertNotificationObject ){
		this.alertNotificationObject = alertNotificationObject;
	}
	/**
	* 预警通知形式
	*@return
	*/
	public String getAlertNotificationModality(){
		return  alertNotificationModality;
	}
	/**
	* 预警通知形式
	*@param  alertNotificationModality
	*/
	public void setAlertNotificationModality(String alertNotificationModality ){
		this.alertNotificationModality = alertNotificationModality;
	}
	/**
	* 飞书应用内预警提醒内容
1飞书应用内通知,3 飞书应用内通知+短信,5 飞书应用内通知+电话,7 飞书应用内通知+短信+电话
	*@return
	*/
	public String getAlertNotificationContent(){
		return  alertNotificationContent;
	}
	/**
	* 飞书应用内预警提醒内容
1飞书应用内通知,3 飞书应用内通知+短信,5 飞书应用内通知+电话,7 飞书应用内通知+短信+电话
	*@param  alertNotificationContent
	*/
	public void setAlertNotificationContent(String alertNotificationContent ){
		this.alertNotificationContent = alertNotificationContent;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
