package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomRecommendPublishListService;
import com.zielsmart.mc.vo.SomRecommendPublishListImportVo;
import com.zielsmart.mc.vo.SomRecommendPublishListPageSearchVo;
import com.zielsmart.mc.vo.SomRecommendPublishListPublishVo;
import com.zielsmart.mc.vo.SomRecommendPublishListVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomRecommendPublishListController
 * @description 推荐上货清单管理
 * @date 2025-07-21 10:17:48
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somRecommendPublishList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "推荐上货清单管理")
public class SomRecommendPublishListController extends BasicController{

    @Resource
    SomRecommendPublishListService somRecommendPublishListService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomRecommendPublishListVo>> queryByPage(@RequestBody SomRecommendPublishListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somRecommendPublishListService.queryByPage(searchVo));
    }

    @Operation(summary = "客户简称列表")
    @PostMapping(value = "/customerShortName/list")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<String>> queryCustomerShortName(@RequestBody SomRecommendPublishListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somRecommendPublishListService.queryCustomerShortName(searchVo));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/SomRecommendPublishListTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"客户简称*","SKU*", "是否寄售*", "平台类目ID*", "EAN", "VC合作标识", "VC合作模式", "不可售处理", "综合单标识", "Item Package Quantity*", "业务组*", "业务负责人*", "业务助理*"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomRecommendPublishListImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomRecommendPublishListImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somRecommendPublishListService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomRecommendPublishListPageSearchVo searchVo) {
        String data = somRecommendPublishListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "批量简单上货")
    @PostMapping(value = "/batchSimplePublish")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchSimplePublish(@RequestBody SomRecommendPublishListPublishVo somRecommendPublishListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somRecommendPublishListService.batchSimplePublish(somRecommendPublishListVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "批量详细上货")
    @PostMapping(value = "/batchDetailPublish")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDetailPublish(@RequestBody SomRecommendPublishListPublishVo somRecommendPublishListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somRecommendPublishListService.batchDetailPublish(somRecommendPublishListVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "查看电子物料")
    @PostMapping(value = "/material/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<Map<String, Object>> materialQuery(@RequestBody SomRecommendPublishListVo somRecommendPublishListVo) throws ValidateException {
        return ResultVo.ofSuccess(somRecommendPublishListService.materialQuery(somRecommendPublishListVo));
    }
}
