package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.role.McRoleResourcePageSearchVo;
import com.zielsmart.mc.vo.role.McRoleResourceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2021-07-16
*/

@SqlResource("mcRoleResource")
public interface McRoleResourceMapper extends BaseMapper<McRoleResource> {

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult< McRoleResourceVo >}
     * <AUTHOR>
     * @history
     */
    PageResult<McRoleResourceVo> queryByPage(@Param("search")McRoleResourcePageSearchVo searchVo, PageRequest pageRequest);

    List<McRoleResource> getPermissionListByJobNumber(@Param("jobNumber")String jobNumber);
}
