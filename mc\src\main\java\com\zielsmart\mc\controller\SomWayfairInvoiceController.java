package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.csv.CsvImportUtil;
import cn.afterturn.easypoi.csv.entity.CsvImportParams;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.zielsmart.mc.service.SomWayfairInvoiceService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.excel.SomWayfairInvoiceExcel;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWayfairInvoiceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somWayfairInvoice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Wayfair账单发票索赔表管理")
public class SomWayfairInvoiceController extends BasicController{

    @Resource
    SomWayfairInvoiceService somWayfairInvoiceService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomWayfairInvoiceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWayfairInvoiceExtVo>> queryByPage(@RequestBody SomWayfairInvoicePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWayfairInvoiceService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somWayfairInvoiceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWayfairInvoiceVo somWayfairInvoiceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWayfairInvoiceService.save(somWayfairInvoiceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somWayfairInvoiceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWayfairInvoiceService.update(somWayfairInvoiceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somWayfairInvoiceVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        somWayfairInvoiceService.delete(somWayfairInvoiceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomWayfairInvoicePageSearchVo searchVo){
        String data = somWayfairInvoiceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("site") String site, @RequestParam("importDate") String importDate, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, ParseException, CloneNotSupportedException {
        CsvImportParams importParams = new CsvImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        importParams.setStartRows(1);

        List<SomWayfairInvoiceExcel> result = null;
        try {
            result = CsvImportUtil.importCsv(file.getInputStream(), SomWayfairInvoiceExcel.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result == null || result.isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }

        int size = somWayfairInvoiceService.importExcel(result, tokenUser, site, importDate);
        return ResultVo.ofSuccess(null,"导入成功"+size+"条数据");
    }

    @Operation(summary = "反馈索赔结果")
    @PostMapping(value = "/feedbackClaimResult")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackClaimResult(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        somWayfairInvoiceService.feedbackClaimResult(somWayfairInvoiceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "索赔")
    @PostMapping(value = "/claim")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> claim(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        somWayfairInvoiceService.claim(somWayfairInvoiceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "免二次包装标记")
    @PostMapping(value = "/updatePackagingMark")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> updatePackagingMark(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        somWayfairInvoiceService.updatePackagingMark(somWayfairInvoiceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "编辑RTV明细")
    @PostMapping(value = "/editRtvDetails")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> editRtvDetails(@RequestBody SomWayfairInvoiceVo somWayfairInvoiceVo) throws ValidateException {
        somWayfairInvoiceService.editRtvDetails(somWayfairInvoiceVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "查询RTV产品费用")
    @PostMapping(value = "/queryRtvCost")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomWayfairRtvDetailVo> queryRtvCost(@RequestBody SomWayfairRtvDetailQueryVo queryVo) throws ValidateException {
        return ResultVo.ofSuccess(somWayfairInvoiceService.queryRtvCost(queryVo));
    }
}
