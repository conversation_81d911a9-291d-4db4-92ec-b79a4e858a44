package com.zielsmart.mc.repository.entity;

import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;

@Data
@Table(name="mc.som_chewy_warehouse_info")
public class SomChewyWarehouseInfo {
    @AssignID
    private String aid;

    /**
     * 平台仓库Code
     */
    @Column("warehouse_code")
    private String warehouseCode;

    /**
     * 平台仓库名称
     */
    @Column("warehouse_name")
    private String warehouseName;

    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum ;

    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName ;

    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
}
