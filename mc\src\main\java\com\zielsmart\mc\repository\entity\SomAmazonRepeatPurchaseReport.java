package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* 品牌复购率表
* gen by 代码生成器 2023-08-04
*/

@Table(name="mc.som_amazon_repeat_purchase_report")
public class SomAmazonRepeatPurchaseReport implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 起止时间 yyyy-MM-dd
	 */
	@Column("start_date")
	private Date startDate ;
	/**
	 * 截止时间 yyyy-MM-dd
	 */
	@Column("end_date")
	private Date endDate ;
	/**
	 * 订单数量
	 */
	@Column("orders")
	private Integer orders ;
	/**
	 * 单次购买的客户数量
	 */
	@Column("unique_customers")
	private Integer uniqueCustomers ;
	/**
	 * 买家数份额
	 */
	@Column("repeat_customers_pct_total")
	private BigDecimal repeatCustomersPctTotal ;
	/**
	 * 复购商品销售额
	 */
	@Column("repeat_purchase_revenue_amount")
	private BigDecimal repeatPurchaseRevenueAmount ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 复购商品销售额份额
	 */
	@Column("repeat_purchase_revenue_pct_total")
	private BigDecimal repeatPurchaseRevenuePctTotal ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomAmazonRepeatPurchaseReport() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 起止时间 yyyy-MM-dd
	*@return
	*/
	public Date getStartDate(){
		return  startDate;
	}
	/**
	* 起止时间 yyyy-MM-dd
	*@param  startDate
	*/
	public void setStartDate(Date startDate ){
		this.startDate = startDate;
	}
	/**
	* 截止时间 yyyy-MM-dd
	*@return
	*/
	public Date getEndDate(){
		return  endDate;
	}
	/**
	* 截止时间 yyyy-MM-dd
	*@param  endDate
	*/
	public void setEndDate(Date endDate ){
		this.endDate = endDate;
	}
	/**
	* 订单数量
	*@return
	*/
	public Integer getOrders(){
		return  orders;
	}
	/**
	* 订单数量
	*@param  orders
	*/
	public void setOrders(Integer orders ){
		this.orders = orders;
	}
	/**
	* 单次购买的客户数量
	*@return
	*/
	public Integer getUniqueCustomers(){
		return  uniqueCustomers;
	}
	/**
	* 单次购买的客户数量
	*@param  uniqueCustomers
	*/
	public void setUniqueCustomers(Integer uniqueCustomers ){
		this.uniqueCustomers = uniqueCustomers;
	}
	/**
	* 买家数份额
	*@return
	*/
	public BigDecimal getRepeatCustomersPctTotal(){
		return  repeatCustomersPctTotal;
	}
	/**
	* 买家数份额
	*@param  repeatCustomersPctTotal
	*/
	public void setRepeatCustomersPctTotal(BigDecimal repeatCustomersPctTotal ){
		this.repeatCustomersPctTotal = repeatCustomersPctTotal;
	}
	/**
	* 复购商品销售额
	*@return
	*/
	public BigDecimal getRepeatPurchaseRevenueAmount(){
		return  repeatPurchaseRevenueAmount;
	}
	/**
	* 复购商品销售额
	*@param  repeatPurchaseRevenueAmount
	*/
	public void setRepeatPurchaseRevenueAmount(BigDecimal repeatPurchaseRevenueAmount ){
		this.repeatPurchaseRevenueAmount = repeatPurchaseRevenueAmount;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 复购商品销售额份额
	*@return
	*/
	public BigDecimal getRepeatPurchaseRevenuePctTotal(){
		return  repeatPurchaseRevenuePctTotal;
	}
	/**
	* 复购商品销售额份额
	*@param  repeatPurchaseRevenuePctTotal
	*/
	public void setRepeatPurchaseRevenuePctTotal(BigDecimal repeatPurchaseRevenuePctTotal ){
		this.repeatPurchaseRevenuePctTotal = repeatPurchaseRevenuePctTotal;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
