package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McSaleInventoryReportService;
import com.zielsmart.mc.vo.McDeptVo;
import com.zielsmart.mc.vo.McSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.McSaleInventoryReportVo;
import com.zielsmart.mc.vo.role.McRoleResourceVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McSaleInventoryReportController
 * @description
 * @date 2021-08-18 16:22:05
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/sale-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "可售库存报表")
public class McSaleInventoryReportController extends BasicController {

    @Resource
    private McSaleInventoryReportService mcSaleInventoryReportService;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link ResultVo< PageVo< McRoleResourceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McSaleInventoryReportVo>> queryByPage(@RequestBody McSaleInventoryReportSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcSaleInventoryReportService.queryByPage(searchVo));
    }

    /**
     * batchPutUp
     * 批量上架
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量上架")
    @PostMapping(value = "/batch-put-up")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutUp(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcSaleInventoryReportService.batchPutUp(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * batchPutDown
     * 批量下架
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量下架")
    @PostMapping(value = "/batch-put-down")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutDown(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcSaleInventoryReportService.batchPutDown(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }


    /**
     * getGroupList
     * 获取业务组列表
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<List<McDeptVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取业务组列表")
    @PostMapping(value = "/get-group-list")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McDeptVo>> getGroupList() throws ValidateException {
        return ResultVo.ofSuccess(mcSaleInventoryReportService.getGroupList());
    }


    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportDearanceProduct(@RequestBody McSaleInventoryReportSearchVo searchVo) throws ValidateException{
        String data = mcSaleInventoryReportService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
