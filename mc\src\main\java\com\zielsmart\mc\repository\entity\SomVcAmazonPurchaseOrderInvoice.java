package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;
/*
* 短装发票表
* gen by 代码生成器 2023-08-17
*/

@Table(name="mc.som_vc_amazon_purchase_order_invoice")
public class SomVcAmazonPurchaseOrderInvoice implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * PO表ID
	 */
	@Column("purchase_id")
	private String purchaseId ;
	/**
	 * 发票编号
	 */
	@Column("invoice_number")
	private String invoiceNumber ;
	/**
	 * 付款截止日期
	 */
	@Column("payment_due_date")
	private Date paymentDueDate ;
	/**
	 * 发票日期
	 */
	@Column("invoice_date")
	private Date invoiceDate ;
	/**
	 * 发票创建日期
	 */
	@Column("ceration_date")
	private Date cerationDate ;
	/**
	 * 发票总金额
	 */
	@Column("invoice_amount")
	private BigDecimal invoiceAmount ;
	/**
	 * 币种符号，例如：€
	 */
	@Column("currency_symbol")
	private String currencySymbol ;
	/**
	 * 币种，例如：EUR
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomVcAmazonPurchaseOrderInvoice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* PO表ID
	*@return
	*/
	public String getPurchaseId(){
		return  purchaseId;
	}
	/**
	* PO表ID
	*@param  purchaseId
	*/
	public void setPurchaseId(String purchaseId ){
		this.purchaseId = purchaseId;
	}
	/**
	* 发票编号
	*@return
	*/
	public String getInvoiceNumber(){
		return  invoiceNumber;
	}
	/**
	* 发票编号
	*@param  invoiceNumber
	*/
	public void setInvoiceNumber(String invoiceNumber ){
		this.invoiceNumber = invoiceNumber;
	}
	/**
	* 付款截止日期
	*@return
	*/
	public Date getPaymentDueDate(){
		return  paymentDueDate;
	}
	/**
	* 付款截止日期
	*@param  paymentDueDate
	*/
	public void setPaymentDueDate(Date paymentDueDate ){
		this.paymentDueDate = paymentDueDate;
	}
	/**
	* 发票日期
	*@return
	*/
	public Date getInvoiceDate(){
		return  invoiceDate;
	}
	/**
	* 发票日期
	*@param  invoiceDate
	*/
	public void setInvoiceDate(Date invoiceDate ){
		this.invoiceDate = invoiceDate;
	}
	/**
	* 发票创建日期
	*@return
	*/
	public Date getCerationDate(){
		return  cerationDate;
	}
	/**
	* 发票创建日期
	*@param  cerationDate
	*/
	public void setCerationDate(Date cerationDate ){
		this.cerationDate = cerationDate;
	}
	/**
	* 发票总金额
	*@return
	*/
	public BigDecimal getInvoiceAmount(){
		return  invoiceAmount;
	}
	/**
	* 发票总金额
	*@param  invoiceAmount
	*/
	public void setInvoiceAmount(BigDecimal invoiceAmount ){
		this.invoiceAmount = invoiceAmount;
	}
	/**
	* 币种符号，例如：€
	*@return
	*/
	public String getCurrencySymbol(){
		return  currencySymbol;
	}
	/**
	* 币种符号，例如：€
	*@param  currencySymbol
	*/
	public void setCurrencySymbol(String currencySymbol ){
		this.currencySymbol = currencySymbol;
	}
	/**
	* 币种，例如：EUR
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种，例如：EUR
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
