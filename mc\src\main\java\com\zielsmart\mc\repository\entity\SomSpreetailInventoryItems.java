package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* Spreetail库存信息表
* gen by 代码生成器 2024-07-25
*/

@Table(name="mc.som_spreetail_inventory_items")
public class SomSpreetailInventoryItems implements java.io.Serializable {
	/**
	 * 主键aid
	 */
	@AssignID
	private String aid ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 库存信息（json格式）
	 */
	@Column("stock_info")
	private String stockInfo ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomSpreetailInventoryItems() {
	}

	/**
	* 主键aid
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键aid
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 库存信息（json格式）
	*@return
	*/
	public String getStockInfo(){
		return  stockInfo;
	}
	/**
	* 库存信息（json格式）
	*@param  stockInfo
	*/
	public void setStockInfo(String stockInfo ){
		this.stockInfo = stockInfo;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
