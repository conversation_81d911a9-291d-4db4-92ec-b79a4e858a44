package com.zielsmart.mc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/*
 * Temu跨境goods的VO实体对象
 * gen by 代码生成器 2025-06-26
 */

@Data
@Schema(title = "Temu跨境goods", name = "SomTemuCrossGoodsVo")
public class SomTemuCrossGoodsVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "店铺名称", name = "accountTag")
    private String accountTag;

    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;

    @Schema(description = "商品ID", name = "productId")
    private Long productId;

    @Schema(description = "商品名称", name = "productName")
    private String productName;

    @Schema(description = "站点集合", name = "sites")
    private String sites;

    @Schema(description = "类目ID", name = "catId")
    private Long catId;

    @Schema(description = "类目名称", name = "catName")
    private String catName;

    @Schema(description = "SKC ID", name = "productSkcId")
    private Long productSkcId;

    @Schema(description = "创建时间", name = "createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @Schema(description = "skus", name = "skus")
    private List<SomTemuCrossSkuVo> skus;
}
