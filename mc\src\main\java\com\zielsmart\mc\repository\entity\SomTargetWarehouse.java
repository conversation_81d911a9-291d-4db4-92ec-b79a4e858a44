package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2023-12-06
 */

@Table(name = "mc.som_target_warehouse")
public class SomTargetWarehouse implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台仓库ID
     */
    @Column("warehouse_code")
    private String warehouseCode;
    /**
     * 平台仓库名称
     */
    @Column("warehouse_name")
    private String warehouseName;
    /**
     * 仓库类型
     */
    @Column("warehouse_type")
    private String warehouseType;
    /**
     * 下载时间
     */
    @Column("create_time")
    private Date createTime;

    public SomTargetWarehouse() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 平台仓库ID
     *
     * @return
     */
    public String getWarehouseCode() {
        return warehouseCode;
    }

    /**
     * 平台仓库ID
     *
     * @param warehouseCode
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * 平台仓库名称
     *
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 平台仓库名称
     *
     * @param warehouseName
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 仓库类型
     *
     * @return
     */
    public String getWarehouseType() {
        return warehouseType;
    }

    /**
     * 仓库类型
     *
     * @param warehouseType
     */
    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 下载时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
