package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAliExpressListingService;
import com.zielsmart.mc.vo.SomAliExpressListingPageSearchVo;
import com.zielsmart.mc.vo.SomAliExpressListingReport;
import com.zielsmart.mc.vo.SomAliExpressListingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAliExpressListingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAliExpressListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "速卖通Listing管理")
public class SomAliExpressListingController extends BasicController {

    @Resource
    SomAliExpressListingService somAliExpressListingService;

    /**
     * queryByPage
     *
     * @param searchVo 查询参数
     * @return {@link ResultVo<PageVo<SomAliExpressListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAliExpressListingVo>> queryByPage(@RequestBody SomAliExpressListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAliExpressListingService.queryByPage(searchVo));
    }


    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomAliExpressListingPageSearchVo searchVo) {
        String data = somAliExpressListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "可售库存报表")
    @PostMapping(value = "/stock-report")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAliExpressListingReport>> stockReport(@RequestBody SomAliExpressListingPageSearchVo searchVo) throws Exception {
        return ResultVo.ofSuccess(somAliExpressListingService.stockReport(searchVo));
    }

    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export-stock-report", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportStockReport(@RequestBody SomAliExpressListingPageSearchVo searchVo) throws Exception {
        String data = somAliExpressListingService.exportStockReport(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
