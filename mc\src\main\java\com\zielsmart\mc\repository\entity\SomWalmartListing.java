package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * Walmart Listing信息表
 * gen by 代码生成器 2023-02-08
 */

@Table(name = "mc.som_walmart_listing")
public class SomWalmartListing implements java.io.Serializable {
    /**
     * 主键id
     */
    @AssignID
    private String aid;
    /**
     * 市场："WALMART_US" "WALMART_CA" "ASDA_GM" "WALMART_MEXICO"
     */
    @Column("mart")
    private String mart;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * Walmart平台分配的产品唯一ID
     */
    @Column("wpid")
    private String wpid;
    /**
     * UPC编码
     */
    @Column("upc")
    private String upc;
    /**
     * UPC编码或EAN编码
     */
    @Column("gtin")
    private String gtin;
    /**
     * 品名
     */
    @Column("product_name")
    private String productName;
    /**
     * 商品货架名称
     */
    @Column("shelf")
    private String shelf;
    /**
     * 卖方指定的、唯一标识产品类型的字母数字字符串。例如:“钻石”
     */
    @Column("product_type")
    private String productType;
    /**
     * 售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 产品发布状态。状态可以是以下状态之一:PUBLISHED、READY_TO_PUBLISH、IN_PROGRESS、UNPUBLISHED、STAGE或SYSTEM_PROBLEM。
     */
    @Column("published_status")
    private String publishedStatus;
    /**
     * 附加属性包
     */
    @Column("additional_attributes")
    private String additionalAttributes;
    /**
     * 未发布项的原因，即当' publhedstatus '被设置为' unpublished '时
     */
    @Column("unpublished_reasons")
    private String unpublishedReasons;
    /**
     * 产品状态，ACTIVE , ARCHIVED, RETIRED.
     */
    @Column("lifecycle_status")
    private String lifecycleStatus;
    /**
     * 如果当前产品是变体，变体ID
     */
    @Column("variant_group_id")
    private String variantGroupId;
    /**
     * 变体详情
     */
    @Column("variant_group_info")
    private String variantGroupInfo;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomWalmartListing() {
    }

    /**
     * 主键id
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键id
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 市场："WALMART_US" "WALMART_CA" "ASDA_GM" "WALMART_MEXICO"
     *
     * @return
     */
    public String getMart() {
        return mart;
    }

    /**
     * 市场："WALMART_US" "WALMART_CA" "ASDA_GM" "WALMART_MEXICO"
     *
     * @param mart
     */
    public void setMart(String mart) {
        this.mart = mart;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * Walmart平台分配的产品唯一ID
     *
     * @return
     */
    public String getWpid() {
        return wpid;
    }

    /**
     * Walmart平台分配的产品唯一ID
     *
     * @param wpid
     */
    public void setWpid(String wpid) {
        this.wpid = wpid;
    }

    /**
     * UPC编码
     *
     * @return
     */
    public String getUpc() {
        return upc;
    }

    /**
     * UPC编码
     *
     * @param upc
     */
    public void setUpc(String upc) {
        this.upc = upc;
    }

    /**
     * UPC编码或EAN编码
     *
     * @return
     */
    public String getGtin() {
        return gtin;
    }

    /**
     * UPC编码或EAN编码
     *
     * @param gtin
     */
    public void setGtin(String gtin) {
        this.gtin = gtin;
    }

    /**
     * 品名
     *
     * @return
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 品名
     *
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 商品货架名称
     *
     * @return
     */
    public String getShelf() {
        return shelf;
    }

    /**
     * 商品货架名称
     *
     * @param shelf
     */
    public void setShelf(String shelf) {
        this.shelf = shelf;
    }

    /**
     * 卖方指定的、唯一标识产品类型的字母数字字符串。例如:“钻石”
     *
     * @return
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 卖方指定的、唯一标识产品类型的字母数字字符串。例如:“钻石”
     *
     * @param productType
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * 售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 产品发布状态。状态可以是以下状态之一:PUBLISHED、READY_TO_PUBLISH、IN_PROGRESS、UNPUBLISHED、STAGE或SYSTEM_PROBLEM。
     *
     * @return
     */
    public String getPublishedStatus() {
        return publishedStatus;
    }

    /**
     * 产品发布状态。状态可以是以下状态之一:PUBLISHED、READY_TO_PUBLISH、IN_PROGRESS、UNPUBLISHED、STAGE或SYSTEM_PROBLEM。
     *
     * @param publishedStatus
     */
    public void setPublishedStatus(String publishedStatus) {
        this.publishedStatus = publishedStatus;
    }

    /**
     * 附加属性包
     *
     * @return
     */
    public String getAdditionalAttributes() {
        return additionalAttributes;
    }

    /**
     * 附加属性包
     *
     * @param additionalAttributes
     */
    public void setAdditionalAttributes(String additionalAttributes) {
        this.additionalAttributes = additionalAttributes;
    }

    /**
     * 未发布项的原因，即当' publhedstatus '被设置为' unpublished '时
     *
     * @return
     */
    public String getUnpublishedReasons() {
        return unpublishedReasons;
    }

    /**
     * 未发布项的原因，即当' publhedstatus '被设置为' unpublished '时
     *
     * @param unpublishedReasons
     */
    public void setUnpublishedReasons(String unpublishedReasons) {
        this.unpublishedReasons = unpublishedReasons;
    }

    /**
     * 产品状态，ACTIVE , ARCHIVED, RETIRED.
     *
     * @return
     */
    public String getLifecycleStatus() {
        return lifecycleStatus;
    }

    /**
     * 产品状态，ACTIVE , ARCHIVED, RETIRED.
     *
     * @param lifecycleStatus
     */
    public void setLifecycleStatus(String lifecycleStatus) {
        this.lifecycleStatus = lifecycleStatus;
    }

    /**
     * 如果当前产品是变体，变体ID
     *
     * @return
     */
    public String getVariantGroupId() {
        return variantGroupId;
    }

    /**
     * 如果当前产品是变体，变体ID
     *
     * @param variantGroupId
     */
    public void setVariantGroupId(String variantGroupId) {
        this.variantGroupId = variantGroupId;
    }

    /**
     * 变体详情
     *
     * @return
     */
    public String getVariantGroupInfo() {
        return variantGroupInfo;
    }

    /**
     * 变体详情
     *
     * @param variantGroupInfo
     */
    public void setVariantGroupInfo(String variantGroupInfo) {
        this.variantGroupInfo = variantGroupInfo;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
