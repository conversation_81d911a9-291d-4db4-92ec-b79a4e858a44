package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomMerchantShippingFeeService;
import com.zielsmart.mc.vo.SomMerchantShippingFeePageSearchVo;
import com.zielsmart.mc.vo.SomMerchantShippingFeeVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomMerchantShippingFeeController
 * @description
 * @date 2024-11-13 11:17:35
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somMerchantShippingFee", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "亚马逊运费价格表管理")
public class SomMerchantShippingFeeController extends BasicController {

    @Resource
    SomMerchantShippingFeeService somMerchantShippingFeeService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomMerchantShippingFeeVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomMerchantShippingFeeVo>> queryByPage(@RequestBody SomMerchantShippingFeePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somMerchantShippingFeeService.queryByPage(searchVo));
    }


    /**
     * update
     *
     * @param somMerchantShippingFeeVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomMerchantShippingFeeVo somMerchantShippingFeeVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somMerchantShippingFeeService.update(somMerchantShippingFeeVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
