package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderCaseItem;
import com.zielsmart.mc.vo.SomVcAmazonPurchaseOrderCaseItemVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-08-18
*/

@SqlResource("somVcAmazonPurchaseOrderCaseItem")
public interface SomVcAmazonPurchaseOrderCaseItemMapper extends BaseMapper<SomVcAmazonPurchaseOrderCaseItem> {

    List<SomVcAmazonPurchaseOrderCaseItemVo> queryByPoAids(@Param("aids")List<String> aids);

    default void updateBatch(@Param("updateList") List<SomVcAmazonPurchaseOrderCaseItem> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somVcAmazonPurchaseOrderCaseItem.updateBatch"), updateList);
    }

}
