package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomAmazonEuropeanEligibilityRpaResourcePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonEuropeanEligibilityRpaResourceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-02-21
 */

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomAmazonEuropeanEligibilityRpaResourceMapper
 * @description
 * @date 2024-02-21 15:06:04
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somAmazonEuropeanEligibilityRpaResource")
public interface SomAmazonEuropeanEligibilityRpaResourceMapper extends BaseMapper<SomAmazonEuropeanEligibilityRpaResource> {
    /**
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomAmazonEuropeanEligibilityRpaResourceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonEuropeanEligibilityRpaResourceVo> queryByPage(@Param("searchVo") SomAmazonEuropeanEligibilityRpaResourcePageSearchVo searchVo, PageRequest pageRequest);
}
