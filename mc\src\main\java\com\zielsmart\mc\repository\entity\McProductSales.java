package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* 产品销售视图
* gen by 代码生成器 2021-07-15
*/

@Table(name="mc.mc_product_sales")
public class McProductSales implements java.io.Serializable {
	/**
	 * 主键
	 */
	@Column("aid")
	private String aid ;
	/**
	 * 所属产品主表编码
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 展示码
	 */
	@Column("display_product_code")
	private String displayProductCode ;
	/**
	 * 所属客户编码
	 */
	@Column("customer_code")
	private String customerCode ;
	/**
	 * 客户简称
	 */
	@Column("customer_short_name")
	private String customerShortName ;
	/**
	 * 销售标识
	 */
	@Column("sales_flag")
	private Integer salesFlag ;
	/**
	 * FNSKU
	 */
	@Column("fnsku_code")
	private String fnskuCode ;
	/**
	 * EAN
	 */
	@Column("ean_code")
	private String eanCode ;
	/**
	 * UPC
	 */
	@Column("upc_code")
	private String upcCode ;
	/**
	 * 平台链接
	 */
	@Column("platform_links")
	private String platformLinks ;
	/**
	 * 拿amazon来说，FBA就算是寄售，用于标识该SKU是否存在寄售模式
	 */
	@Column("is_consignment_sales")
	private Integer isConsignmentSales ;
	/**
	 * 标识该SKU是否参与了amazon的VC合作
	 */
	@Column("vc_flag")
	private Integer vcFlag ;
	/**
	 * 展示码状态编码
	 */
	@Column("seller_sku_status_code")
	private String sellerSkuStatusCode ;
	/**
	 * 所属运营/业务组CODE
	 */
	@Column("sales_group_code")
	private String salesGroupCode ;
	/**
	 * 所属销售/业务负责人工号
	 */
	@Column("sales_group_empt_code")
	private String salesGroupEmptCode ;
	/**
	 * 所属运营/业务助理工号
	 */
	@Column("operation_empt_code")
	private String operationEmptCode ;
	/**
	 * 是否有效
	 */
	@Column("is_enabled")
	private Integer isEnabled ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 状态更新日期：手工修改为淘汰/预淘汰时更新该日期，JOB任务检测到新品/试销/正常状态变更时更新该日期；
	 */
	@Column("status_modify_time")
	private Date statusModifyTime ;

	/**
	 * 上下架状态
	 */
	@Column("listing_status")
	private Integer listingStatus ;

	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 三级分类名称
	 */
	@Column("category_name")
	private String categoryName ;

	@Column("product_type_code")
	private String productTypeCode;

	@Column("product_group_code")
	private String productGroupCode;
	@Column("product_group_name")
	private String productGroupName;
	@Column("platform_special_project_code")
	private String  platformSpecialProjectCode;

	//补充字段
	@Column("new_sku_code")
	private String newSkuCode;
	@Column("facelift_exist_flag")
	private Integer faceliftExistFlag;
	@Column("local_sale_flag")
	private Integer localSaleFlag;

	@Column("asin_code")
	private String asinCode;

	public McProductSales() {

	}

	public String getAsinCode() {
		return asinCode;
	}

	public void setAsinCode(String asinCode) {
		this.asinCode = asinCode;
	}

	public String getNewSkuCode() {
		return newSkuCode;
	}

	public void setNewSkuCode(String newSkuCode) {
		this.newSkuCode = newSkuCode;
	}

	public Integer getFaceliftExistFlag() {
		return faceliftExistFlag;
	}

	public void setFaceliftExistFlag(Integer faceliftExistFlag) {
		this.faceliftExistFlag = faceliftExistFlag;
	}

	public Integer getLocalSaleFlag() {
		return localSaleFlag;
	}

	public void setLocalSaleFlag(Integer localSaleFlag) {
		this.localSaleFlag = localSaleFlag;
	}

	public String getPlatformSpecialProjectCode() {
		return platformSpecialProjectCode;
	}

	public void setPlatformSpecialProjectCode(String platformSpecialProjectCode) {
		this.platformSpecialProjectCode = platformSpecialProjectCode;
	}

	public String getProductGroupCode() {
		return productGroupCode;
	}

	public void setProductGroupCode(String productGroupCode) {
		this.productGroupCode = productGroupCode;
	}

	public String getProductGroupName() {
		return productGroupName;
	}

	public void setProductGroupName(String productGroupName) {
		this.productGroupName = productGroupName;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 所属产品主表编码
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* 所属产品主表编码
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 展示码
	*@return
	*/
	public String getDisplayProductCode(){
		return  displayProductCode;
	}
	/**
	* 展示码
	*@param  displayProductCode
	*/
	public void setDisplayProductCode(String displayProductCode ){
		this.displayProductCode = displayProductCode;
	}
	/**
	* 所属客户编码
	*@return
	*/
	public String getCustomerCode(){
		return  customerCode;
	}
	/**
	* 所属客户编码
	*@param  customerCode
	*/
	public void setCustomerCode(String customerCode ){
		this.customerCode = customerCode;
	}
	/**
	* 客户简称
	*@return
	*/
	public String getCustomerShortName(){
		return  customerShortName;
	}
	/**
	* 客户简称
	*@param  customerShortName
	*/
	public void setCustomerShortName(String customerShortName ){
		this.customerShortName = customerShortName;
	}
	/**
	* 销售标识
	*@return
	*/
	public Integer getSalesFlag(){
		return  salesFlag;
	}
	/**
	* 销售标识
	*@param  salesFlag
	*/
	public void setSalesFlag(Integer salesFlag ){
		this.salesFlag = salesFlag;
	}
	/**
	* FNSKU
	*@return
	*/
	public String getFnskuCode(){
		return  fnskuCode;
	}
	/**
	* FNSKU
	*@param  fnskuCode
	*/
	public void setFnskuCode(String fnskuCode ){
		this.fnskuCode = fnskuCode;
	}
	/**
	* EAN
	*@return
	*/
	public String getEanCode(){
		return  eanCode;
	}
	/**
	* EAN
	*@param  eanCode
	*/
	public void setEanCode(String eanCode ){
		this.eanCode = eanCode;
	}
	/**
	* UPC
	*@return
	*/
	public String getUpcCode(){
		return  upcCode;
	}
	/**
	* UPC
	*@param  upcCode
	*/
	public void setUpcCode(String upcCode ){
		this.upcCode = upcCode;
	}
	/**
	* 平台链接
	*@return
	*/
	public String getPlatformLinks(){
		return  platformLinks;
	}
	/**
	* 平台链接
	*@param  platformLinks
	*/
	public void setPlatformLinks(String platformLinks ){
		this.platformLinks = platformLinks;
	}
	/**
	* 拿amazon来说，FBA就算是寄售，用于标识该SKU是否存在寄售模式
	*@return
	*/
	public Integer getisConsignmentSales(){
		return  isConsignmentSales;
	}
	/**
	* 拿amazon来说，FBA就算是寄售，用于标识该SKU是否存在寄售模式
	*@param  isConsignmentSales
	*/
	public void setisConsignmentSales(Integer isConsignmentSales ){
		this.isConsignmentSales = isConsignmentSales;
	}
	/**
	* 标识该SKU是否参与了amazon的VC合作
	*@return
	*/
	public Integer getvcFlag(){
		return  vcFlag;
	}
	/**
	* 标识该SKU是否参与了amazon的VC合作
	*@param  vcFlag
	*/
	public void setvcFlag(Integer vcFlag ){
		this.vcFlag = vcFlag;
	}
	/**
	* 展示码状态编码
	*@return
	*/
	public String getSellerSkuStatusCode(){
		return  sellerSkuStatusCode;
	}
	/**
	* 展示码状态编码
	*@param  sellerSkuStatusCode
	*/
	public void setSellerSkuStatusCode(String sellerSkuStatusCode ){
		this.sellerSkuStatusCode = sellerSkuStatusCode;
	}
	/**
	* 所属运营/业务组CODE
	*@return
	*/
	public String getSalesGroupCode(){
		return  salesGroupCode;
	}
	/**
	* 所属运营/业务组CODE
	*@param  salesGroupCode
	*/
	public void setSalesGroupCode(String salesGroupCode ){
		this.salesGroupCode = salesGroupCode;
	}
	/**
	* 所属销售/业务负责人工号
	*@return
	*/
	public String getSalesGroupEmptCode(){
		return  salesGroupEmptCode;
	}
	/**
	* 所属销售/业务负责人工号
	*@param  salesGroupEmptCode
	*/
	public void setSalesGroupEmptCode(String salesGroupEmptCode ){
		this.salesGroupEmptCode = salesGroupEmptCode;
	}
	/**
	* 所属运营/业务助理工号
	*@return
	*/
	public String getOperationEmptCode(){
		return  operationEmptCode;
	}
	/**
	* 所属运营/业务助理工号
	*@param  operationEmptCode
	*/
	public void setOperationEmptCode(String operationEmptCode ){
		this.operationEmptCode = operationEmptCode;
	}
	/**
	* 是否有效
	*@return
	*/
	public Integer getisEnabled(){
		return  isEnabled;
	}
	/**
	* 是否有效
	*@param  isEnabled
	*/
	public void setisEnabled(Integer isEnabled ){
		this.isEnabled = isEnabled;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}
	/**
	* 状态更新日期：手工修改为淘汰/预淘汰时更新该日期，JOB任务检测到新品/试销/正常状态变更时更新该日期；
	*@return
	*/
	public Date getStatusModifyTime(){
		return  statusModifyTime;
	}
	/**
	* 状态更新日期：手工修改为淘汰/预淘汰时更新该日期，JOB任务检测到新品/试销/正常状态变更时更新该日期；
	*@param  statusModifyTime
	*/
	public void setStatusModifyTime(Date statusModifyTime ){
		this.statusModifyTime = statusModifyTime;
	}

	public Integer getIsConsignmentSales() {
		return isConsignmentSales;
	}

	public void setIsConsignmentSales(Integer isConsignmentSales) {
		this.isConsignmentSales = isConsignmentSales;
	}

	public Integer getVcFlag() {
		return vcFlag;
	}

	public void setVcFlag(Integer vcFlag) {
		this.vcFlag = vcFlag;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}

	public Integer getListingStatus() {
		return listingStatus;
	}

	public void setListingStatus(Integer listingStatus) {
		this.listingStatus = listingStatus;
	}

	public String getProductTypeCode() {
		return productTypeCode;
	}

	public void setProductTypeCode(String productTypeCode) {
		this.productTypeCode = productTypeCode;
	}
}
