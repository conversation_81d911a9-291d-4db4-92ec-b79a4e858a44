package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 
* gen by 代码生成器 2025-05-16
*/

@Table(name="mc.som_temu_warehouse")
public class SomTemuWarehouse implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 站点id
	 */
	@Column("site_id")
	private Integer siteId ;
	/**
	 * 站点名称
	 */
	@Column("site_name")
	private String siteName ;
	/**
	 * 是否禁用
	 */
	@Column("warehouse_disable")
	private Integer warehouseDisable ;
	/**
	 * 仓库id
	 */
	@Column("warehouse_id")
	private String warehouseId ;
	/**
	 * 仓库名称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 管理类型
	 */
	@Column("management_type")
	private Integer managementType ;

	@Column("site")
	private String site;

	public SomTemuWarehouse() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 站点id
	*@return
	*/
	public Integer getSiteId(){
		return  siteId;
	}
	/**
	* 站点id
	*@param  siteId
	*/
	public void setSiteId(Integer siteId ){
		this.siteId = siteId;
	}
	/**
	* 站点名称
	*@return
	*/
	public String getSiteName(){
		return  siteName;
	}
	/**
	* 站点名称
	*@param  siteName
	*/
	public void setSiteName(String siteName ){
		this.siteName = siteName;
	}
	/**
	* 是否禁用
	*@return
	*/
	public Integer getWarehouseDisable(){
		return  warehouseDisable;
	}
	/**
	* 是否禁用
	*@param  warehouseDisable
	*/
	public void setWarehouseDisable(Integer warehouseDisable ){
		this.warehouseDisable = warehouseDisable;
	}
	/**
	* 仓库id
	*@return
	*/
	public String getWarehouseId(){
		return  warehouseId;
	}
	/**
	* 仓库id
	*@param  warehouseId
	*/
	public void setWarehouseId(String warehouseId ){
		this.warehouseId = warehouseId;
	}
	/**
	* 仓库名称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 仓库名称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 管理类型
	*@return
	*/
	public Integer getManagementType(){
		return  managementType;
	}
	/**
	* 管理类型
	*@param  managementType
	*/
	public void setManagementType(Integer managementType ){
		this.managementType = managementType;
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}
}
