package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonLostService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Controller
@RequestMapping(value = "/amazon-lost", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Lost索赔管理")
public class SomLostCaseController extends BasicController {

    @Resource
    private SomAmazonLostService somAmazonLostService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomLostVo>> queryByPage(@RequestBody SomAmazonLostSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonLostService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonLostSearchVo searchVo) throws ValidateException {
        String data = somAmazonLostService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "索赔")
    @PostMapping(value = "/save-claim")
    @ResponseBody
    public ResultVo<String> saveClaim(@RequestBody SomLostClaimVo claim,@Parameter(hidden = true) @TokenUser TokenUserInfo user) throws ValidateException {
        somAmazonLostService.saveClaim(claim,user);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "查看索赔结果")
    @PostMapping(value = "/claim-query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomLostCaseVo>> claimQueryByPage(@RequestBody SomAmazonLostSearchVo searchVo){
        return ResultVo.ofSuccess(somAmazonLostService.claimQueryByPage(searchVo));
    }

    @Operation(summary = "查看索赔明细")
    @PostMapping(value = "/claim-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomLostCaseItemsVo>> claimDetail(@RequestBody Map map) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonLostService.claimDetail(map));
    }
    @Operation(summary = "查看赔付明细")
    @PostMapping(value = "/compensation-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<Map> compensationDetail(@RequestBody Map map) throws ValidateException {
        return ResultVo.ofSuccess(somAmazonLostService.compensationDetail(map));
    }


    @Operation(summary = "批量关闭索赔")
    @PostMapping(value = "/close-claim")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> closeClaim(@RequestBody Map map) throws ValidateException {
        somAmazonLostService.closeClaim(map);
        return ResultVo.ofSuccess();
    }
}
