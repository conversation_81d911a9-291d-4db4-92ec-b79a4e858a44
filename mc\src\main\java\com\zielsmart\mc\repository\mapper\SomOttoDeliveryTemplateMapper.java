package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplatePageSearchVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplateVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-09-20
*/

@SqlResource("somOttoDeliveryTemplate")
public interface SomOttoDeliveryTemplateMapper extends BaseMapper<SomOttoDeliveryTemplate> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOttoDeliveryTemplateVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomOttoDeliveryTemplateVo> queryByPage(@Param("searchVo")SomOttoDeliveryTemplatePageSearchVo searchVo, PageRequest pageRequest);

    List<SomOttoDeliveryTemplateVo> queryAllDataWithName();
}
