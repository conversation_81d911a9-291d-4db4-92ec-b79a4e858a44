package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 部门表
* gen by 代码生成器 2021-08-24
*/

@Table(name="mc.mc_dept")
public class McDept implements java.io.Serializable {
	/**
	 * 部门主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 部门名称
	 */
	@Column("dept_name")
	private String deptName ;
	/**
	 * 部门名称_拼音
	 */
	@Column("dept_name_pinyin")
	private String deptNamePinyin ;
	/**
	 * 部门编号
	 */
	@Column("dept_no")
	private String deptNo ;
	/**
	 * 部门领导
	 */
	@Column("dept_leader")
	private String deptLeader ;
	/**
	 * 上级部门Id
	 */
	@Column("dept_parent_id")
	private String deptParentId ;
	/**
	 * 上级领导
	 */
	@Column("dept_parent_leader")
	private String deptParentLeader ;
	/**
	 * 所属机构
	 */
	@Column("dept_org_id")
	private String deptOrgId ;
	/**
	 * 部门创建时间
	 */
	@Column("dept_add_time")
	private Date deptAddTime ;
	/**
	 * 部门修改时间
	 */
	@Column("dept_update_time")
	private Date deptUpdateTime ;
	/**
	 * 部门状态 0:不可用1可用
	 */
	@Column("dept_status")
	private Integer deptStatus ;
	/**
	 * 所属机构名称
	 */
	@Column("dept_org_name")
	private String deptOrgName ;
	/**
	 * 同步日期
	 */
	@Column("last_sync_time")
	private Date lastSyncTime ;
	/**
	 * 新EYA的部门名称
	 */
	@Column("new_eya_dept_name")
	private String newEyaDeptName ;
	/**
	 * 新EYA的部门编码
	 */
	@Column("new_eya_dept_code")
	private String newEyaDeptCode ;

	public McDept() {
	}

	/**
	* 部门主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 部门主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 部门名称
	*@return
	*/
	public String getDeptName(){
		return  deptName;
	}
	/**
	* 部门名称
	*@param  deptName
	*/
	public void setDeptName(String deptName ){
		this.deptName = deptName;
	}
	/**
	* 部门名称_拼音
	*@return
	*/
	public String getDeptNamePinyin(){
		return  deptNamePinyin;
	}
	/**
	* 部门名称_拼音
	*@param  deptNamePinyin
	*/
	public void setDeptNamePinyin(String deptNamePinyin ){
		this.deptNamePinyin = deptNamePinyin;
	}
	/**
	* 部门编号
	*@return
	*/
	public String getDeptNo(){
		return  deptNo;
	}
	/**
	* 部门编号
	*@param  deptNo
	*/
	public void setDeptNo(String deptNo ){
		this.deptNo = deptNo;
	}
	/**
	* 部门领导
	*@return
	*/
	public String getDeptLeader(){
		return  deptLeader;
	}
	/**
	* 部门领导
	*@param  deptLeader
	*/
	public void setDeptLeader(String deptLeader ){
		this.deptLeader = deptLeader;
	}
	/**
	* 上级部门Id
	*@return
	*/
	public String getDeptParentId(){
		return  deptParentId;
	}
	/**
	* 上级部门Id
	*@param  deptParentId
	*/
	public void setDeptParentId(String deptParentId ){
		this.deptParentId = deptParentId;
	}
	/**
	* 上级领导
	*@return
	*/
	public String getDeptParentLeader(){
		return  deptParentLeader;
	}
	/**
	* 上级领导
	*@param  deptParentLeader
	*/
	public void setDeptParentLeader(String deptParentLeader ){
		this.deptParentLeader = deptParentLeader;
	}
	/**
	* 所属机构
	*@return
	*/
	public String getDeptOrgId(){
		return  deptOrgId;
	}
	/**
	* 所属机构
	*@param  deptOrgId
	*/
	public void setDeptOrgId(String deptOrgId ){
		this.deptOrgId = deptOrgId;
	}
	/**
	* 部门创建时间
	*@return
	*/
	public Date getDeptAddTime(){
		return  deptAddTime;
	}
	/**
	* 部门创建时间
	*@param  deptAddTime
	*/
	public void setDeptAddTime(Date deptAddTime ){
		this.deptAddTime = deptAddTime;
	}
	/**
	* 部门修改时间
	*@return
	*/
	public Date getDeptUpdateTime(){
		return  deptUpdateTime;
	}
	/**
	* 部门修改时间
	*@param  deptUpdateTime
	*/
	public void setDeptUpdateTime(Date deptUpdateTime ){
		this.deptUpdateTime = deptUpdateTime;
	}
	/**
	* 部门状态 0:不可用1可用
	*@return
	*/
	public Integer getDeptStatus(){
		return  deptStatus;
	}
	/**
	* 部门状态 0:不可用1可用
	*@param  deptStatus
	*/
	public void setDeptStatus(Integer deptStatus ){
		this.deptStatus = deptStatus;
	}
	/**
	* 所属机构名称
	*@return
	*/
	public String getDeptOrgName(){
		return  deptOrgName;
	}
	/**
	* 所属机构名称
	*@param  deptOrgName
	*/
	public void setDeptOrgName(String deptOrgName ){
		this.deptOrgName = deptOrgName;
	}
	/**
	* 同步日期
	*@return
	*/
	public Date getLastSyncTime(){
		return  lastSyncTime;
	}
	/**
	* 同步日期
	*@param  lastSyncTime
	*/
	public void setLastSyncTime(Date lastSyncTime ){
		this.lastSyncTime = lastSyncTime;
	}
	/**
	* 新EYA的部门名称
	*@return
	*/
	public String getNewEyaDeptName(){
		return  newEyaDeptName;
	}
	/**
	* 新EYA的部门名称
	*@param  newEyaDeptName
	*/
	public void setNewEyaDeptName(String newEyaDeptName ){
		this.newEyaDeptName = newEyaDeptName;
	}
	/**
	* 新EYA的部门编码
	*@return
	*/
	public String getNewEyaDeptCode(){
		return  newEyaDeptCode;
	}
	/**
	* 新EYA的部门编码
	*@param  newEyaDeptCode
	*/
	public void setNewEyaDeptCode(String newEyaDeptCode ){
		this.newEyaDeptCode = newEyaDeptCode;
	}

}
