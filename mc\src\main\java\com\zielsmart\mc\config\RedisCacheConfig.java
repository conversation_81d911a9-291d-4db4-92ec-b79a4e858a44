package com.zielsmart.mc.config;

import com.zielsmart.eya.redis.starter.IRedisCacheConfig;
import com.zielsmart.mc.McConstants;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.omsb2c.config
 * @title RedisCacheConfig
 * @description
 * @date 2021-04-14 10:14
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@EnableCaching
@Component
public class RedisCacheConfig implements IRedisCacheConfig {
    @Override
    public Map<String, RedisCacheConfiguration> cacheKey() {
        Map<String, RedisCacheConfiguration> redisCacheConfigMap = new HashMap<>(1);
        /**
         * 默认cd 缓存超时时间1小时
         */
        redisCacheConfigMap.put(McConstants.CACHE_AREA_CD, getRedisCacheConfigurationWithTtl(3600));
        /**
         * EBAY token超时时间7200 默认7100失效重新获取
         */
        redisCacheConfigMap.put(McConstants.CACHE_AREA_EBAY, getRedisCacheConfigurationWithTtl(7100));
        /**
         * OTTO token有效时间为1800 默认1700失效重新获取
         */
        redisCacheConfigMap.put(McConstants.CACHE_AREA_OTTO, getRedisCacheConfigurationWithTtl(1700));
        /**
         * 默认yahoo 缓存超时时间1小时
         */
        redisCacheConfigMap.put(McConstants.CACHE_AREA_YAHOO, getRedisCacheConfigurationWithTtl(3600));
        /**
         * 默认wayfair 缓存超时时间1小时
         */
        redisCacheConfigMap.put(McConstants.CACHE_AREA_WAYFAIR, getRedisCacheConfigurationWithTtl(3600));
        return redisCacheConfigMap;
    }

}
