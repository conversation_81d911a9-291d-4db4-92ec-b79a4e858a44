package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomGoogleAdsShoppingReportPageSearchVo;
import com.zielsmart.mc.vo.SomGoogleAdsShoppingReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-02-26
 */

@SqlResource("somGoogleAdsShoppingReport")
public interface SomGoogleAdsShoppingReportMapper extends BaseMapper<SomGoogleAdsShoppingReport> {
    /**
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomGoogleAdsShoppingReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomGoogleAdsShoppingReportVo> queryByPage(@Param("searchVo") SomGoogleAdsShoppingReportPageSearchVo searchVo, PageRequest pageRequest);
}
