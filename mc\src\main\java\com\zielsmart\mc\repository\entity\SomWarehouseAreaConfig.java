package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
 *  仓库区域配置表
 * gen by 代码生成器 2022-02-14
 */

@Table(name="mc.som_warehouse_area_config")
public class SomWarehouseAreaConfig implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 区域
	 */
	@Column("area_code")
	private String areaCode ;
	/**
	 * 仓库编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	@Column("site")
	private String site ;

	@Column("business_type")
	private Integer businessType ;

	public SomWarehouseAreaConfig() {
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}

	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * 区域
	 *@return
	 */
	public String getAreaCode(){
		return  areaCode;
	}
	/**
	 * 区域
	 *@param  areaCode
	 */
	public void setAreaCode(String areaCode ){
		this.areaCode = areaCode;
	}
	/**
	 * 仓库编码
	 *@return
	 */
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	 * 仓库编码
	 *@param  warehouseCode
	 */
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	 * 创建人工号
	 *@return
	 */
	public String getCreateNum(){
		return  createNum;
	}
	/**
	 * 创建人工号
	 *@param  createNum
	 */
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	 * 创建人姓名
	 *@return
	 */
	public String getCreateName(){
		return  createName;
	}
	/**
	 * 创建人姓名
	 *@param  createName
	 */
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	 * 创建时间
	 *@return
	 */
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	 * 创建时间
	 *@param  createTime
	 */
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
