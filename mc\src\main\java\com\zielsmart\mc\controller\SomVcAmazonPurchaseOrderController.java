package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.SomVcAmazonPurchaseOrderService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.excel.PurchaseOrderHandler;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcAmazonPurchaseOrderController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somVcAmazonPurchaseOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC短装索赔管理")
public class SomVcAmazonPurchaseOrderController extends BasicController{

    @Resource
    SomVcAmazonPurchaseOrderService somVcAmazonPurchaseOrderService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomVcAmazonPurchaseOrderVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomVcAmazonPurchaseOrderVo>> queryByPage(@RequestBody SomVcAmazonPurchaseOrderPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcAmazonPurchaseOrderService.queryByPage(searchVo));
    }


    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody JSONObject map) throws ValidateException {
        somVcAmazonPurchaseOrderService.delete(map);
        return ResultVo.ofSuccess(null);
    }


    /**
     * finance-remark
     *
     * @param orderVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "财务备注")
    @PostMapping(value = "/finance-remark")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> financeRemark(@RequestBody SomVcAmazonPurchaseOrderVo orderVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcAmazonPurchaseOrderService.financeRemark(orderVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "关闭索赔")
    @PostMapping(value = "/close")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> close(@RequestBody SomVcAmazonPurchaseOrderVo orderVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcAmazonPurchaseOrderService.close(orderVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "加载索赔数据列表")
    @PostMapping(value = "/load-case")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomVcAmazonPurchaseOrderCaseVo>> loadCase(@RequestBody SomVcAmazonPurchaseOrderVo orderVo) throws ValidateException {
        return ResultVo.ofSuccess(somVcAmazonPurchaseOrderService.loadCase(orderVo));
    }

    @Operation(summary = "加载索赔明细列表")
    @PostMapping(value = "/load-case-detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomVcAmazonPurchaseOrderCaseItemVo>> loadCaseDetail(@RequestBody SomVcAmazonPurchaseOrderCaseVo caseVo) throws ValidateException {
        return ResultVo.ofSuccess(somVcAmazonPurchaseOrderService.loadCaseDetail(caseVo));
    }

    @Operation(summary = "修改TAG标签状态")
    @PostMapping(value = "/edit-tag")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> editTag(@RequestBody SomVcAmazonPurchaseOrderCaseVo caseVo) throws ValidateException {
        somVcAmazonPurchaseOrderService.editTag(caseVo);
        return ResultVo.ofSuccess();
    }


    /**
     * 只更新 明细表赔付金额
     * @param caseVo
     * @param tokenUser
     * @return
     * @throws ValidateException
     */
    @Operation(summary = "反馈索赔结果")
    @PostMapping(value = "/feedback-case")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackCase(@RequestBody SomVcAmazonPurchaseOrderCaseVo caseVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcAmazonPurchaseOrderService.feedbackCase(caseVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "索赔")
    @PostMapping(value = "/open-case")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> openCase(@RequestBody SomVcAmazonPurchaseOrderCaseVo caseVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcAmazonPurchaseOrderService.openCase(caseVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomVcAmazonPurchaseOrderPageSearchVo searchVo){
        String data = somVcAmazonPurchaseOrderService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, ParseException {
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        importParams.setTitleRows(0);
        // 需要特殊处理的美国excel
        String[] arr = {"Payment Due Date","Invoice Creation Date","Invoice Number"};
        PurchaseOrderHandler purchaseOrderHandler = new PurchaseOrderHandler();
        purchaseOrderHandler.setNeedHandlerFields(arr);
        importParams.setDataHandler(purchaseOrderHandler);
        ExcelImportResult<SomVcAmazonPurchaseOrderImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcAmazonPurchaseOrderImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("不是合法的Excel模板,请检查是否有 Purchase Order 这一列");
            }
        }
        if (result==null || result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcAmazonPurchaseOrderService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }
}
;