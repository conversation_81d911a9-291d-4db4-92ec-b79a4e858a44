package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomWayfairInvoiceExtVo;
import com.zielsmart.mc.vo.SomWayfairInvoicePageSearchVo;
import com.zielsmart.mc.vo.SomWayfairInvoiceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-05-20
*/

@SqlResource("somWayfairInvoice")
public interface SomWayfairInvoiceMapper extends BaseMapper<SomWayfairInvoice> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomWayfairInvoiceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWayfairInvoiceExtVo> queryByPage(@Param("searchVo")SomWayfairInvoicePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryExport
     * 导出查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomWayfairInvoiceExtVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomWayfairInvoiceExtVo> queryExport(@Param("searchVo")SomWayfairInvoicePageSearchVo searchVo, PageRequest pageRequest);
}
