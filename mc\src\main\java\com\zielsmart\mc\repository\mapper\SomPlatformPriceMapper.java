package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomPlatformPrice;
import com.zielsmart.mc.vo.SomPlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomPlatformPriceVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2022-02-22
 */

@SqlResource("somPlatformPrice")
public interface SomPlatformPriceMapper extends BaseMapper<SomPlatformPrice> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomPlatformPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomPlatformPriceVo> queryByPage(@Param("searchVo") SomPlatformPricePageSearchVo searchVo, PageRequest pageRequest);
}
