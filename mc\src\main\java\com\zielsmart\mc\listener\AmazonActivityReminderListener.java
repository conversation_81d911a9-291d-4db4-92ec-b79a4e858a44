package com.zielsmart.mc.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.event.AmazonActivityReminderEvent;
import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.web.basic.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableAsync
public class AmazonActivityReminderListener {

    @Async("taskExecutor")
    @EventListener
    public void handler(AmazonActivityReminderEvent event) {
        try {
            log.info("营销活动消息提醒接收参数{}", JSONUtil.toJsonStr(event));
            Assert.notNull(event, "消息不能为空");
            Assert.notNull(event.getList(), "消息内容不能为空");
            //按照创建人工号分组
            Map<String, List<AmazonActivityReminderEvent.Reminder>> map = event.getList().stream().collect(Collectors.groupingBy(x -> x.getCreatNum()));
            for (String creatNum : map.keySet()) {
                List<String> msgList = new ArrayList<>();
                for (AmazonActivityReminderEvent.Reminder reminder : map.get(creatNum)) {
                    String msg = Strings.EMPTY;
                    switch (reminder.getTypeName()) {
                        case "Coupon":
                            msg = StrUtil.format("站点:{},展示码:{},活动类型:{},活动起止日期为:{}~{}的Coupon活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        case "Promotion":
                            msg = StrUtil.format("站点:{},Internal Description:{},活动类型:{},活动起止日期为:{}~{}的Promotion活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getInternalDescription(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        case "LD&7DD":
                            msg = StrUtil.format("站点:{},Internal Description:{},活动类型:{},活动起止日期为:{}~{}的秒杀活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getInternalDescription(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        case "DOTD":
                            msg = StrUtil.format("站点:{},展示码:{},活动起止日期为:{}~{}的DOTD活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        default:
                            msg = StrUtil.format("站点:{},展示码:{},活动类型:{},活动起止日期为:{}~{}的秒杀活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()),
                                    reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                    }
                }
                if (CollectionUtil.isNotEmpty(msgList) && Strings.isNotBlank(creatNum)) {
                    String message = msgList.stream().distinct().collect(Collectors.joining("\n")) + "，请您及时处理";
                    log.info("营销活动提报失败发送消息给创建人工号:{}", creatNum);
                    FeiShuUtils.sendNotice(creatNum, message);
                } else {
                    log.warn("创建人工号或消息为空,工号{},消息{}", creatNum, JSONUtil.toJsonStr(msgList));
                }
            }

            //按照销售负责人分组
            List<String> sellerSkuList = event.getList().stream().map(x -> x.getSellerSku()).collect(Collectors.toList());
            McProductSalesMapper salesMapper = WebUtils.getApplicationContext().getBean(McProductSalesMapper.class);
            //查询销售视图对应的销售负责人
            List<McProductSales> salesList = salesMapper.createLambdaQuery().andIn("display_product_code", sellerSkuList).select();

            Map<String, String> salesMap = salesList.stream().collect(Collectors.toMap(x -> x.getSite()+x.getDisplayProductCode(),v->v.getSalesGroupEmptCode(),(k1,k2)->k1));
            event.getList().stream().forEach(x -> {
                //根据站点 展示码 查找对应的销售负责人
                String salesName = salesMap.getOrDefault(x.getSite() + x.getSellerSku(), Strings.EMPTY);
                x.setSalesGroupEmptCode(salesName);
            });

            Map<String, List<AmazonActivityReminderEvent.Reminder>> saleGroupEmptMap = event.getList().stream().collect(Collectors.groupingBy(x -> x.getSalesGroupEmptCode()));

            for (String saleCode : saleGroupEmptMap.keySet()) {
                List<String> msgList = new ArrayList<>();
                for (AmazonActivityReminderEvent.Reminder reminder : saleGroupEmptMap.get(saleCode)) {
                    String msg = Strings.EMPTY;
                    switch (reminder.getTypeName()) {
                        case "Coupon":
                            msg = StrUtil.format("站点:{},展示码:{},活动类型:{},活动起止日期为:{}~{}的Coupon活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        case "Promotion":
                            //Promotion不发送给销售负责人
                            break;
                        case "LD&7DD":
                            msg = StrUtil.format("站点:{},展示码:{},Internal Description:{},活动类型:{},活动起止日期为:{}~{}的秒杀活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(),reminder.getSellerSku(), reminder.getInternalDescription(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        case "DOTD":
                            msg = StrUtil.format("站点:{},展示码:{},活动起止日期为:{}~{}的DOTD活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()), reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                        default:
                            msg = StrUtil.format("站点:{},展示码:{},活动类型:{},活动起止日期为:{}~{}的秒杀活动在Amazon后台提报失败，失败原因:{}",
                                    reminder.getSite(), reminder.getSellerSku(), reminder.getSortName(), DateUtil.formatDate(reminder.getStartTime()), DateUtil.formatDate(reminder.getEndTime()),
                                    reminder.getFailMsg());
                            msgList.add(msg);
                            break;
                    }
                }
                if (CollectionUtil.isNotEmpty(msgList) && Strings.isNotBlank(saleCode)) {
                    String message = msgList.stream().collect(Collectors.joining("\n")) + "，请您及时处理";
                    log.info("营销活动提报失败发送消息给销售负责人工号:{}", saleCode);
                    FeiShuUtils.sendNotice(saleCode, message);
                } else {
                    log.warn("销售负责人工号或消息为空,工号{},消息{}", saleCode, JSONUtil.toJsonStr(msgList));
                }
            }

        } catch (Exception e) {
            log.error("营销活动消息提醒异常,异常信息为{}", e.getMessage());
        }
        log.info("营销活动消息提醒结束");
    }
}
