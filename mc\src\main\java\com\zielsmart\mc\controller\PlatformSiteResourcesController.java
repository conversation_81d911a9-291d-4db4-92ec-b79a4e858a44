package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.PlatformSiteResourcesService;
import com.zielsmart.mc.vo.role.McPlatformSiteResourcesVo;
import com.zielsmart.mc.vo.role.PlatformSiteResourcesPageSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.xml.bind.ValidationException;
import java.util.List;

/**
 * @version V1.0
 * @title: PlatformSiteResourcesController
 * @package: com.zielsmart.mc.controller
 * @description:
 * @author: lvjishuai
 * @date: 2021-07-14 16:32
 * @Copyright: 2019 www.ziel.cn Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/platform-site-resources", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "运营平台站点配置表")
public class PlatformSiteResourcesController extends BasicController {
    @Resource
    private PlatformSiteResourcesService platformSiteResourcesService;


    /**
     * queryByPage
     * 运营平台站点配置表分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo< McPlatformSiteResourcesVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "运营平台站点配置表分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McPlatformSiteResourcesVo>> queryByPage(@RequestBody PlatformSiteResourcesPageSearchVo searchVo) {
        return ResultVo.ofSuccess(platformSiteResourcesService.queryByPage(searchVo));
    }

    /**
     * saveOrUpdate
     * 保存修改运营平台站点配置
     * @param tokenUser
     * @param platformSiteResourcesVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "保存修改运营平台站点配置")
    @PostMapping(value = "/save-or-update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveOrUpdate(@Parameter(hidden = true)  @TokenUser TokenUserInfo tokenUser, @RequestBody McPlatformSiteResourcesVo platformSiteResourcesVo) throws RuntimeException {
        platformSiteResourcesService.saveOrUpdate(tokenUser, platformSiteResourcesVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除运营平台站点配置
     * @param aidList
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidationException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除运营平台站点配置")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody List<String> aidList) throws ValidationException {
        platformSiteResourcesService.delete(aidList);
        return ResultVo.ofSuccess(null);
    }
}
