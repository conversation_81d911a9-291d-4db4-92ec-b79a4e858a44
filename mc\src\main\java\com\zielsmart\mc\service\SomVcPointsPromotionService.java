package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.enums.VcPromotionStatusEnum;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McPlatformProperties;
import com.zielsmart.mc.repository.entity.SomAmazonVcPrice;
import com.zielsmart.mc.repository.entity.SomVcPointsPromotion;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SomVcPointsPromotionService {
    
    @Resource
    private SomVcPointsPromotionMapper somVcPointsPromotionMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    SomVcPromotionService somVcPromotionService;

    public PageVo<SomVcPointsPromotionVo> queryByPage(SomVcPointsPromotionPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcPointsPromotionVo> pageResult = dynamicSqlManager.getMapper(SomVcPointsPromotionMapper.class).queryByPage(searchVo, pageRequest);

        if (CollUtil.isNotEmpty(pageResult.getList())) {
            // 字典值、其他基础字段转换
            List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
            Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1));
            List<McDictionaryInfo> dictListVcPointsStatus = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPointsStatus").select();
            Map<String, String> dictMapVcPointsStatus = dictListVcPointsStatus.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            List<McDictionaryInfo> dictListVcPromotionType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionType").select();
            Map<String, String> dictMapVcPromotionType = dictListVcPromotionType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            pageResult.getList().forEach(x -> {
                x.setApplyReasonShow(dictMapVcPromotionApplyReason.get(x.getApplyReason().toString()).getItemLable());
                x.setStatusShow(dictMapVcPointsStatus.get(x.getStatus().toString()));
                x.setPromotionTypeShow(dictMapVcPromotionType.get(x.getPromotionType().toString()));
                x.setPerUnitFundingShow(x.getPerUnitFunding() + " pt");
            });
        }

        return ConvertUtils.pageConvert(pageResult, SomVcPointsPromotionVo.class, searchVo);
    }

    public void addOrEdit(SomVcPointsPromotionVo somVcPointsPromotionVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somVcPointsPromotionVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        SomVcPointsPromotion somVcPointsPromotion = ConvertUtils.beanConvert(somVcPointsPromotionVo, SomVcPointsPromotion.class);
        // 唯一性验证
        SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
        BeanUtil.copyProperties(somVcPointsPromotionVo, checkUniqueVo);
        if (StrUtil.isEmpty(somVcPointsPromotionVo.getAid())) {
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                throw new ValidateException("该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增");
            }
            somVcPointsPromotion.setAid(IdUtil.fastSimpleUUID());
            somVcPointsPromotion.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcPointsPromotion.setCreateNum(tokenUser.getJobNumber());
            somVcPointsPromotion.setCreateName(tokenUser.getUserName());
            somVcPointsPromotion.setCreateTime(DateTime.now().toJdkDate());
            somVcPointsPromotion.setPromotionType(40);
            somVcPointsPromotionMapper.insert(somVcPointsPromotion);
        } else {
            checkUniqueVo.setPointsAid(somVcPointsPromotionVo.getAid());
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                throw new ValidateException("该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增");
            }
            // 编辑后 -> 草稿
            somVcPointsPromotion.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcPointsPromotion.setModifyNum(tokenUser.getJobNumber());
            somVcPointsPromotion.setModifyName(tokenUser.getUserName());
            somVcPointsPromotion.setModifyTime(DateTime.now().toJdkDate());
            somVcPointsPromotionMapper.updateById(somVcPointsPromotion);
        }
    }

    public void delete(SomVcPointsPromotionVo somVcPointsPromotionVo) throws ValidateException {
        SomVcPointsPromotion somVcPointsPromotion = somVcPointsPromotionMapper.createLambdaQuery().andEq("aid", somVcPointsPromotionVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcPointsPromotion)) {
            throw new ValidateException("内容不存在");
        }
        // [草稿]状态才可以删除
        if (!ObjectUtil.equal(somVcPointsPromotion.getStatus(), VcPromotionStatusEnum.DRAFT.getStatus())) {
            throw new ValidateException("只能删除状态为草稿的内容");
        }
        somVcPointsPromotionMapper.createLambdaQuery().andEq("aid",somVcPointsPromotionVo.getAid()).delete();
    }

    public void cancel(SomVcPointsPromotionVo somVcPointsPromotionVo) throws ValidateException {
        SomVcPointsPromotion somVcPointsPromotion = somVcPointsPromotionMapper.createLambdaQuery().andEq("aid", somVcPointsPromotionVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcPointsPromotion)) {
            throw new ValidateException("内容不存在");
        }
        // [进行中,需要关注,未开始]的活动允许取消
        List<Integer> allowCancelStatus = VcPromotionStatusEnum.getAllowCancelStatus();
        if (!allowCancelStatus.contains(somVcPointsPromotion.getStatus())) {
            throw new ValidateException("当前数据不允许取消！");
        }
        somVcPointsPromotion.setStatus(VcPromotionStatusEnum.CANCELED.getStatus());
        somVcPointsPromotionMapper.createLambdaQuery().andEq("aid", somVcPointsPromotionVo.getAid()).update(somVcPointsPromotion);
    }

    public String export(SomVcPointsPromotionPageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomVcPointsPromotionVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {

                for (SomVcPointsPromotionVo vo : records) {
                    vo.setSellerSkuAndAsinAndSku(vo.getSellerSku() + "\n" + vo.getAsin() + "\n" + vo.getSku());
                    vo.setRrpAndPrice(vo.getRecommendedRetailPrice() + " " + vo.getCurrency() + "\n" + vo.getPrice() + " " + vo.getCurrency());
                    vo.setPerUnitFundingShow(vo.getPerUnitFunding() + " pt");
                    // 起止时间
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    vo.setStartDateAndEndDate(sdf.format(vo.getStartDate()) + "\n" + sdf.format(vo.getEndDate()));
                }

                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "VC Points promotion");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomVcPointsPromotionVo.class, records);

                    // 自适应高度
                    Sheet sheet = workbook.getSheetAt(0);
                    // 遍历所有行
                    for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                        Row row = sheet.getRow(i);
                        if (row == null) continue;
                        // 遍历每一列，计算最大行高
                        int maxLines = 1;
                        for (int j = 0; j < row.getLastCellNum(); j++) {
                            Cell cell = row.getCell(j);
                            if (cell == null || cell.getStringCellValue() == null) continue;
                            // 根据换行符计算行数
                            String cellValue = cell.getStringCellValue();
                            int lines = cellValue.split("\n").length;
                            if (lines > maxLines) {
                                maxLines = lines;
                            }
                        }
                        // 设置行高 每行高度为15*行数
                        row.setHeight((short) (15 * maxLines * 20));
                    }

                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
    }


    public String importExcel(List<SomVcPointsPromotionImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        // 站点唯一验证
        long siteCount = list.stream().map(SomVcPointsPromotionImportVo::getSite).distinct().count();
        if (siteCount > 1) {
            throw new ValidateException("导入数据存在多个站点，请检查数据！");
        }

        StringBuilder stringBuilder = new StringBuilder();
        List<String> siteList = list.stream().map(SomVcPointsPromotionImportVo::getSite).collect(Collectors.toList());
        List<String> sellerSkuList = list.stream().map(SomVcPointsPromotionImportVo::getSellerSku).collect(Collectors.toList());
        List<String> asinList = list.stream().map(SomVcPointsPromotionImportVo::getAsin).collect(Collectors.toList());

        // 确定使用展示码还是asin
        SomVcPointsPromotionImportVo firstRow = list.get(0);
        String sellerSkuFirst = firstRow.getSellerSku();
        String asinFirst = firstRow.getAsin();
        if (StrUtil.isNotBlank(sellerSkuFirst) && StrUtil.isNotBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN只能使用一项");
        }
        if (StrUtil.isBlank(sellerSkuFirst) && StrUtil.isBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN至少填写一项");
        }
        String useSellerSkuOrAsin = "sellerSku";
        if (StrUtil.isNotEmpty(asinFirst)) {
            useSellerSkuOrAsin = "asin";
        }

        // 必填项
        int i = 2;
        for (SomVcPointsPromotionImportVo vo : list) {
            StringBuilder rowErrors = new StringBuilder();
            if (StrUtil.isEmpty(vo.getSite())) rowErrors.append("站点、");
            if (StrUtil.isEmpty(vo.getAccountName())) rowErrors.append("账号名称、");
            if (StrUtil.isEmpty(vo.getVendorCode())) rowErrors.append("供应商编码、");
            //if (StrUtil.isEmpty(vo.getSellerSku())) rowErrors.append("展示码、");
            if (ObjectUtil.isEmpty(vo.getStartDate())) rowErrors.append("活动起始日期、");
            if (ObjectUtil.isEmpty(vo.getEndDate())) rowErrors.append("活动截止日期、");
            if (ObjectUtil.isEmpty(vo.getDiscount())) rowErrors.append("积分比例、");
            if (StrUtil.isEmpty(vo.getApplyReasonShow())) rowErrors.append("申请原因、");
            if (rowErrors.length() > 0) {
                rowErrors.setLength(rowErrors.length() - 1);
                stringBuilder.append("第").append(i).append("行 - 缺少必填项: ").append(rowErrors).append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 基础字段验证
        i = 2;
        for (SomVcPointsPromotionImportVo vo : list) {
            // 活动时间
            if (vo.getStartDate().after(vo.getEndDate())) {
                stringBuilder.append("第").append(i).append("行 - 活动截止日期必须晚于起始日期").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 数据唯一验证
        i = 2;
        for (SomVcPointsPromotionImportVo vo : list) {
            SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
            BeanUtil.copyProperties(vo, checkUniqueVo);
            if (somVcPromotionService.checkUnique(checkUniqueVo)) {
                stringBuilder.append("第").append(i).append("行 - 该产品已经存在营销活动，为了防止叠加折扣，不允许重复新增").append("\n");
            }
            i++;
        }

        // 字典值、其他基础字段转换
        List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
        Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1));
        // 平台站点属性配置 获取币种
        List<McPlatformProperties> mcPlatformPropertiesList = dynamicSqlManager.getMapper(McPlatformPropertiesMapper.class).createLambdaQuery().andIn("site", siteList).select();
        Map<String, String> mcPlatformPropertiesMap = mcPlatformPropertiesList.stream().collect(Collectors.toMap(McPlatformProperties::getSite, McPlatformProperties::getCurrencyCode, (x1, x2) -> x1));
        for (SomVcPointsPromotionImportVo vo : list) {
            // 字典值转换
            vo.setApplyReason(Integer.valueOf(dictMapVcPromotionApplyReason.get(vo.getApplyReasonShow()).getItemValue())); // 申请原因
            // 币种
            vo.setCurrency(mcPlatformPropertiesMap.get(vo.getSite()));
            // 积分比例 -> 积分比值 与前端保持一致
            vo.setDiscount(vo.getDiscount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        }

        // 获取ASIN、SKU等销售视图相关数据
        Map<String, McProductSalesVo> mcProductSalesVoMap = null;
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setSiteList(siteList);
        if (useSellerSkuOrAsin.equals("sellerSku")) {
            mcProductSalesSearchVo.setDisplayProductCodeList(sellerSkuList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));
        }
        if (useSellerSkuOrAsin.equals("asin")) {
            mcProductSalesSearchVo.setAsinList(asinList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getAsinCode(), Function.identity(), (x1, x2) -> x1));
        }
        i = 2;
        for (SomVcPointsPromotionImportVo vo : list) {
            String key = "";
            if (useSellerSkuOrAsin.equals("sellerSku")) {
                key = vo.getSite() + vo.getSellerSku();
            }
            if (useSellerSkuOrAsin.equals("asin")) {
                key = vo.getSite() + vo.getAsin();
            }
            McProductSalesVo mcProductSalesVo = mcProductSalesVoMap.get(key);
            if (mcProductSalesVo != null) {
                vo.setSellerSku(mcProductSalesVo.getDisplayProductCode());
                vo.setAsin(mcProductSalesVo.getAsinCode());
                vo.setSku(mcProductSalesVo.getProductMainCode());
                vo.setSalesGroupCode(mcProductSalesVo.getSalesGroupCode());
                vo.setSalesGroupName(mcProductSalesVo.getSalesGroupName());
                vo.setSalesGroupEmptCode(mcProductSalesVo.getSalesGroupEmptCode());
                vo.setSalesGroupEmptName(mcProductSalesVo.getSalesGroupEmptName());
                vo.setOperationEmptCode(mcProductSalesVo.getOperationEmptCode());
                vo.setOperationEmptName(mcProductSalesVo.getOperationEmptName());
            } else {
                stringBuilder.append("第").append(i).append("行 - 销售视图查询不存在").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 建议零售价RRP/前台售价/30天DMS/单个产品预估积分
        asinList = list.stream().map(SomVcPointsPromotionImportVo::getAsin).collect(Collectors.toList());
        sellerSkuList = list.stream().map(SomVcPointsPromotionImportVo::getSellerSku).collect(Collectors.toList());
        List<SomAmazonVcPrice> somAmazonVcPriceList = dynamicSqlManager.getMapper(SomAmazonVcPriceMapper.class).createLambdaQuery()
                .andIn("site", siteList)
                .andIn("seller_sku", sellerSkuList)
                .andIn("asin", asinList)
                .select();
        Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = somAmazonVcPriceList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getSellerSku() + x.getAsin(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        i = 2;
        for (SomVcPointsPromotionImportVo vo : list) {
            String key = vo.getSite() + vo.getSellerSku() + vo.getAsin();
            SomAmazonVcPrice somAmazonVcPrice = somAmazonVcPriceMap.get(key);
            if (somAmazonVcPrice != null) {
                // 前台售价
                vo.setPrice(somAmazonVcPrice.getPrice());
                // 30天DMS
                vo.setMonthDms(somAmazonVcPrice.getMonthDms());
                // 建议零售价RRP
                vo.setRecommendedRetailPrice(somAmazonVcPrice.getRecommendedRetailPrice());
                // 单个产品预估积分 RRP * 折扣百分比
                BigDecimal perUnitFunding = BigDecimal.ZERO;
                BigDecimal rrp = somAmazonVcPrice.getRecommendedRetailPrice();
                BigDecimal discount = vo.getDiscount().divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                perUnitFunding = rrp.multiply(discount).setScale(2, RoundingMode.HALF_UP);
                vo.setPerUnitFunding(perUnitFunding);
            } else {
                stringBuilder.append("第").append(i).append("行 - 此产品未维护RRP，请先维护RRP").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 获取库存可销售天数
        List<SomSellerSkuStockSaleDaysQueryVo> queryVos = new ArrayList<>();
        for (SomVcPointsPromotionImportVo vo : list) {
            SomSellerSkuStockSaleDaysQueryVo queryVo = new SomSellerSkuStockSaleDaysQueryVo();
            queryVo.setSite(vo.getSite());
            queryVo.setSellerSku(vo.getSellerSku());
            queryVo.setStartDate(vo.getStartDate());
            queryVo.setEndDate(vo.getEndDate());
            queryVos.add(queryVo);
        }
        Map<String, Object> vcStockSaleDaysMap = somVcPromotionService.queryVcStockSaleDays(queryVos);
        for (SomVcPointsPromotionImportVo vo : list) {
            // 库存可销售天数
            if (vcStockSaleDaysMap.containsKey(vo.getSellerSku())) {
                vo.setStockSaleDays((Integer) vcStockSaleDaysMap.get(vo.getSellerSku()));
            }
        }

        List<SomVcPointsPromotion> insertList = new ArrayList<>();
        for (SomVcPointsPromotionImportVo vo : list) {
            SomVcPointsPromotion somVcPointsPromotion = new SomVcPointsPromotion();
            String couponAid = IdUtil.fastSimpleUUID();
            somVcPointsPromotion.setAid(couponAid);
            somVcPointsPromotion.setSite(vo.getSite());
            somVcPointsPromotion.setAccountName(vo.getAccountName());
            somVcPointsPromotion.setPromotionType(40);
            somVcPointsPromotion.setVendorCode(vo.getVendorCode());
            somVcPointsPromotion.setSellerSku(vo.getSellerSku());
            somVcPointsPromotion.setAsin(vo.getAsin());
            somVcPointsPromotion.setSku(vo.getSku());
            somVcPointsPromotion.setRecommendedRetailPrice(vo.getRecommendedRetailPrice());
            somVcPointsPromotion.setPrice(vo.getPrice());
            somVcPointsPromotion.setDiscount(vo.getDiscount());
            somVcPointsPromotion.setPerUnitFunding(vo.getPerUnitFunding());
            somVcPointsPromotion.setCurrency(vo.getCurrency());
            somVcPointsPromotion.setRating(vo.getRating());
            somVcPointsPromotion.setStockSaleDays(vo.getStockSaleDays());
            somVcPointsPromotion.setMonthDms(vo.getMonthDms());
            somVcPointsPromotion.setStartDate(vo.getStartDate());
            somVcPointsPromotion.setEndDate(vo.getEndDate());
            somVcPointsPromotion.setStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcPointsPromotion.setApplyReason(vo.getApplyReason());
            somVcPointsPromotion.setCustomReason(vo.getCustomReason());
            somVcPointsPromotion.setCreateTime(DateTime.now().toJdkDate());
            somVcPointsPromotion.setCreateName(tokenUser.getUserName());
            somVcPointsPromotion.setCreateNum(tokenUser.getJobNumber());
            somVcPointsPromotion.setSalesGroupCode(vo.getSalesGroupCode());
            somVcPointsPromotion.setSalesGroupName(vo.getSalesGroupName());
            somVcPointsPromotion.setSalesGroupEmptCode(vo.getSalesGroupEmptCode());
            somVcPointsPromotion.setSalesGroupEmptName(vo.getSalesGroupEmptName());
            somVcPointsPromotion.setOperationEmptCode(vo.getOperationEmptCode());
            somVcPointsPromotion.setOperationEmptName(vo.getOperationEmptName());
            insertList.add(somVcPointsPromotion);
        }

        if (CollUtil.isNotEmpty(insertList)) {
            somVcPointsPromotionMapper.insertBatch(insertList);
        }

        return "";
    }
}
