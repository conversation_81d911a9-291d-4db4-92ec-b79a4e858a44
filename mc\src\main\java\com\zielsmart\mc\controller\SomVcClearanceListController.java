package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomVcClearanceListService;
import com.zielsmart.mc.vo.SomVcClearanceListPageSearchVo;
import com.zielsmart.mc.vo.SomVcClearanceListVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomVcClearanceListController
 * @description
 * @date 2025-01-07 09:14:40
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somVcClearanceList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC清仓列表管理")
public class SomVcClearanceListController extends BasicController{

    @Resource
    SomVcClearanceListService somVcClearanceListService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcClearanceListVo>> queryByPage(@RequestBody SomVcClearanceListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcClearanceListService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated SomVcClearanceListVo somVcClearanceListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcClearanceListService.save(somVcClearanceListVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomVcClearanceListVo somVcClearanceListVo) throws ValidateException {
        somVcClearanceListService.delete(somVcClearanceListVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/VcClearanceTemplate.xlsx";
    }

    @Operation(summary = "下载批量删除导入模板")
    @GetMapping(value = "/batch-delete/download")
    public String downloadBatchDeleteExcel() {
        return "forward:/static/excel/VcClearanceDeleteTemplate.xlsx";
    }
    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"站点", "供应商编码", "展示码", "平台仓库编码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomVcClearanceListVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomVcClearanceListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcClearanceListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomVcClearanceListPageSearchVo searchVo){
        String data = somVcClearanceListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "批量删除")
    @PostMapping(value = "/batch-delete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDelete(@RequestParam("file") MultipartFile file) throws Exception {
        String[] importFields = {"站点", "供应商编码", "展示码", "平台仓库编码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomVcClearanceListVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomVcClearanceListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somVcClearanceListService.batchDelete(list);
        return ResultVo.ofSuccess();
    }
}
