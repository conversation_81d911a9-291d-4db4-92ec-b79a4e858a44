package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2024-02-21
 */

@Table(name = "mc.som_amazon_european_eligibility_rpa_resource")
public class SomAmazonEuropeanEligibilityRpaResource implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * FNSKU
     */
    @Column("fnsku")
    private String fnsku;
    /**
     * PAN-EU状态
     */
    @Column("pan_eu_status")
    private String panEuStatus;
    /**
     * 库存数量
     */
    @Column("available")
    private Integer available;
    /**
     * 英国产品状态
     */
    @Column("item_status_uk")
    private String itemStatusUk;
    /**
     * 德国产品状态
     */
    @Column("item_status_de")
    private String itemStatusDe;
    /**
     * 法国产品状态
     */
    @Column("item_status_fr")
    private String itemStatusFr;
    /**
     * 意大利产品状态
     */
    @Column("item_status_it")
    private String itemStatusIt;
    /**
     * 西班牙产品状态
     */
    @Column("item_status_es")
    private String itemStatusEs;
    /**
     * 荷兰产品状态
     */
    @Column("item_status_nl")
    private String itemStatusNl;
    /**
     * 瑞典产品状态
     */
    @Column("item_status_se")
    private String itemStatusSe;
    /**
     * 波兰产品状态
     */
    @Column("item_status_pl")
    private String itemStatusPl;
    /**
     * 比利时产品状态
     */
    @Column("item_status_be")
    private String itemStatusBe;
    /**
     * pan-eu优惠失效时间
     */
    @Column("date_pan_eu_expires")
    private Date datePanEuExpires;
    /**
     * 下载时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 报表日期
     */
    @Column("report_date")
    private Date reportDate;
    /**
     * 爱尔兰产品状态
     */
    @Column("item_status_ie")
    private String itemStatusIe;

    public SomAmazonEuropeanEligibilityRpaResource() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * FNSKU
     *
     * @return
     */
    public String getFnsku() {
        return fnsku;
    }

    /**
     * FNSKU
     *
     * @param fnsku
     */
    public void setFnsku(String fnsku) {
        this.fnsku = fnsku;
    }

    /**
     * PAN-EU状态
     *
     * @return
     */
    public String getPanEuStatus() {
        return panEuStatus;
    }

    /**
     * PAN-EU状态
     *
     * @param panEuStatus
     */
    public void setPanEuStatus(String panEuStatus) {
        this.panEuStatus = panEuStatus;
    }

    /**
     * 库存数量
     *
     * @return
     */
    public Integer getAvailable() {
        return available;
    }

    /**
     * 库存数量
     *
     * @param available
     */
    public void setAvailable(Integer available) {
        this.available = available;
    }

    /**
     * 英国产品状态
     *
     * @return
     */
    public String getItemStatusUk() {
        return itemStatusUk;
    }

    /**
     * 英国产品状态
     *
     * @param itemStatusUk
     */
    public void setItemStatusUk(String itemStatusUk) {
        this.itemStatusUk = itemStatusUk;
    }

    /**
     * 德国产品状态
     *
     * @return
     */
    public String getItemStatusDe() {
        return itemStatusDe;
    }

    /**
     * 德国产品状态
     *
     * @param itemStatusDe
     */
    public void setItemStatusDe(String itemStatusDe) {
        this.itemStatusDe = itemStatusDe;
    }

    /**
     * 法国产品状态
     *
     * @return
     */
    public String getItemStatusFr() {
        return itemStatusFr;
    }

    /**
     * 法国产品状态
     *
     * @param itemStatusFr
     */
    public void setItemStatusFr(String itemStatusFr) {
        this.itemStatusFr = itemStatusFr;
    }

    /**
     * 意大利产品状态
     *
     * @return
     */
    public String getItemStatusIt() {
        return itemStatusIt;
    }

    /**
     * 意大利产品状态
     *
     * @param itemStatusIt
     */
    public void setItemStatusIt(String itemStatusIt) {
        this.itemStatusIt = itemStatusIt;
    }

    /**
     * 西班牙产品状态
     *
     * @return
     */
    public String getItemStatusEs() {
        return itemStatusEs;
    }

    /**
     * 西班牙产品状态
     *
     * @param itemStatusEs
     */
    public void setItemStatusEs(String itemStatusEs) {
        this.itemStatusEs = itemStatusEs;
    }

    /**
     * 荷兰产品状态
     *
     * @return
     */
    public String getItemStatusNl() {
        return itemStatusNl;
    }

    /**
     * 荷兰产品状态
     *
     * @param itemStatusNl
     */
    public void setItemStatusNl(String itemStatusNl) {
        this.itemStatusNl = itemStatusNl;
    }

    /**
     * 瑞典产品状态
     *
     * @return
     */
    public String getItemStatusSe() {
        return itemStatusSe;
    }

    /**
     * 瑞典产品状态
     *
     * @param itemStatusSe
     */
    public void setItemStatusSe(String itemStatusSe) {
        this.itemStatusSe = itemStatusSe;
    }

    /**
     * 波兰产品状态
     *
     * @return
     */
    public String getItemStatusPl() {
        return itemStatusPl;
    }

    /**
     * 波兰产品状态
     *
     * @param itemStatusPl
     */
    public void setItemStatusPl(String itemStatusPl) {
        this.itemStatusPl = itemStatusPl;
    }

    /**
     * 比利时产品状态
     *
     * @return
     */
    public String getItemStatusBe() {
        return itemStatusBe;
    }

    /**
     * 比利时产品状态
     *
     * @param itemStatusBe
     */
    public void setItemStatusBe(String itemStatusBe) {
        this.itemStatusBe = itemStatusBe;
    }

    /**
     * pan-eu优惠失效时间
     *
     * @return
     */
    public Date getDatePanEuExpires() {
        return datePanEuExpires;
    }

    /**
     * pan-eu优惠失效时间
     *
     * @param datePanEuExpires
     */
    public void setDatePanEuExpires(Date datePanEuExpires) {
        this.datePanEuExpires = datePanEuExpires;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 下载时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 报表日期
     *
     * @return
     */
    public Date getReportDate() {
        return reportDate;
    }

    /**
     * 报表日期
     *
     * @param reportDate
     */
    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public String getItemStatusIe() {
        return itemStatusIe;
    }

    public void setItemStatusIe(String itemStatusIe) {
        this.itemStatusIe = itemStatusIe;
    }
}
