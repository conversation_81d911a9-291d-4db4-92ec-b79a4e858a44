package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.mapper.SomTemuCrossGoodsMapper;
import com.zielsmart.mc.repository.mapper.SomTemuCrossSkuMapper;
import com.zielsmart.mc.vo.SomTemuCrossGoodsPageSearchVo;
import com.zielsmart.mc.vo.SomTemuImageUrlReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description SomTemuCrossSkuService
 * @date 2025-06-26 15:57:05
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTemuCrossSkuService {

    @Resource
    private SomTemuCrossSkuMapper somTemuCrossSkuMapper;

    @Resource
    private SomTemuCrossGoodsMapper somTemuCrossGoodsMapper;

    @Resource
    private SomTemuLocalSkuService somTemuLocalSkuService;

    /**
     * URL 链接下载
     *
     * @param searchVo 入参
     * @return String
     */
    public String exportUrl(SomTemuCrossGoodsPageSearchVo searchVo) {
        // 获取URL链接下载需要的sku记录
        String accountId = searchVo.getAccountId();
        String keyWord = searchVo.getKeyWord();
        List<SomTemuImageUrlReport> records = new ArrayList<>();
        List<String> goodsIds = new ArrayList<>();
        if (StrUtil.isAllEmpty(accountId, keyWord)) {
            records = somTemuCrossSkuMapper.getUrlExportData(goodsIds);
        } else {
            // 获取URL链接的所有goodsId
            goodsIds = somTemuCrossGoodsMapper.getUrlExportGoodsIds(searchVo);
            if (CollUtil.isNotEmpty(goodsIds)) {
                records = somTemuCrossSkuMapper.getUrlExportData(goodsIds);
            }
        }
        return somTemuLocalSkuService.exportUrl(records);
    }
}
