package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.zielsmart.mc.service.McPushOttoDeliveryService;
import com.zielsmart.mc.vo.McPushOttoDeliveryPageSearchVo;
import com.zielsmart.mc.vo.McPushOttoDeliveryVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McPushOttoDeliveryController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcPushOttoDelivery", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Otto推送配送数据管理")
public class McPushOttoDeliveryController extends BasicController {

    @Resource
    McPushOttoDeliveryService mcPushOttoDeliveryService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McPushOttoDeliveryVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McPushOttoDeliveryVo>> queryByPage(@RequestBody McPushOttoDeliveryPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcPushOttoDeliveryService.queryByPage(searchVo));
    }

    @Operation(summary = "推送配送时效")
    @GetMapping(value="/push-delivery-time")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> pushDeliveryTime() throws Exception {
        mcPushOttoDeliveryService.pushDeliveryTime();
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="download")
    public String downloadExcel(){
       return "forward:/static/excel/OttoTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"展示码", "SKU", "配送方式", "配送时间"};
        importParams.setImportFields(arr);
        ExcelImportResult<McPushOttoDeliveryVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), McPushOttoDeliveryVo.class,importParams);
        } catch (Exception e) {
                throw new ValidateException("导入模板有误,请检查模板");
        }
        List<McPushOttoDeliveryVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        mcPushOttoDeliveryService.save(list,tokenUser);
        return ResultVo.ofSuccess();
    }
}
