package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonRepeatPurchaseReport;
import com.zielsmart.mc.vo.SomAmazonRepeatPurchaseReportPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonRepeatPurchaseReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-08-04
*/

@SqlResource("somAmazonRepeatPurchaseReport")
public interface SomAmazonRepeatPurchaseReportMapper extends BaseMapper<SomAmazonRepeatPurchaseReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonRepeatPurchaseReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonRepeatPurchaseReportVo> queryByPage(@Param("searchVo")SomAmazonRepeatPurchaseReportPageSearchVo searchVo, PageRequest<SomAmazonRepeatPurchaseReportVo> pageRequest);
}
