package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2025-07-21
 */

@Table(name = "mc.som_recommend_publish_list")
public class SomRecommendPublishList implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 客户编码
     */
    @Column("customer_code")
    private String customerCode;
    /**
     * 客户简称
     */
    @Column("customer_short_name")
    private String customerShortName;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * SPU编码
     */
    @Column("spu")
    private String spu;
    /**
     * 产品编码
     */
    @Column("sku")
    private String sku;
    /**
     * 三级类目编码
     */
    @Column("third_level_category_code")
    private String thirdLevelCategoryCode;
    /**
     * 三级类目名称
     */
    @Column("third_level_category_name")
    private String thirdLevelCategoryName;
    /**
     * 四级类目编码
     */
    @Column("forth_level_category_code")
    private String forthLevelCategoryCode;
    /**
     * 四级类目名称
     */
    @Column("forth_level_category_name")
    private String forthLevelCategoryName;
    /**
     * 品牌
     */
    @Column("brand")
    private String brand;
    /**
     * PCS
     */
    @Column("pcs")
    private Integer pcs;
    /**
     * 产品首图
     */
    @Column("main_image")
    private String mainImage;
    /**
     * 产品状态
     */
    @Column("product_status")
    private String productStatus;
    /**
     * 0.非寄售  1.寄售
     */
    @Column("consignment_sales")
    private Integer consignmentSales;
    /**
     * 0.不需要简单上货 1.需要简单上货
     */
    @Column("simple_publish_tag")
    private Integer simplePublishTag;
    /**
     * 预计到港时间
     */
    @Column("estimate_arrival_port_date")
    private Date estimateArrivalPortDate;
    /**
     * 预计到仓时间
     */
    @Column("estimate_arrival_warehouse_date")
    private Date estimateArrivalWarehouseDate;
    /**
     * 是否已上货。 0.否 1.是
     */
    @Column("is_publish")
    private Integer isPublish;
    /**
     * 平台类目ID
     */
    @Column("platform_category_id")
    private String platformCategoryId;
    /**
     * EAN编码
     */
    @Column("ean")
    private String ean;
    /**
     * 不可售处理，枚举值：
     * 10 召回
     * 20 销毁
     */
    @Column("consignment_stock_processing")
    private Integer consignmentStockProcessing;
    /**
     * VC合作标识，0.否 1.是
     */
    @Column("vc_cooperation")
    private Integer vcCooperation;
    /**
     * VC合作模式，枚举值：
     * DI        10
     * AOC        20
     * SC-VOC        30
     * VC-VOC        40
     * DDP        50
     * DF        60
     */
    @Column("vc_cooperation_model")
    private Integer vcCooperationModel;
    /**
     * 综合单标识
     */
    @Column("variation_identifications")
    private String variationIdentifications;
    /**
     * IPQ   包装数量
     */
    @Column("item_package_quantity")
    private Integer itemPackageQuantity;
    /**
     * 业务组编码
     */
    @Column("sales_group_code")
    private String salesGroupCode;
    /**
     * 电子物料是否完备：
     * 0 否
     * 1 是
     */
    @Column("material_is_complete")
    private Integer materialIsComplete;
    /**
     * 销售负责人工号
     */
    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;
    /**
     * 销售助理工号
     */
    @Column("operation_empt_code")
    private String operationEmptCode;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;

    /**
     * 修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;

    /**
     * 平台类目关键字
     */
    @Column("platform_category_type_keyword")
    private String platformCategoryTypeKeyword;

    public SomRecommendPublishList() {
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerShortName() {
        return customerShortName;
    }

    public void setCustomerShortName(String customerShortName) {
        this.customerShortName = customerShortName;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getThirdLevelCategoryCode() {
        return thirdLevelCategoryCode;
    }

    public void setThirdLevelCategoryCode(String thirdLevelCategoryCode) {
        this.thirdLevelCategoryCode = thirdLevelCategoryCode;
    }

    public String getThirdLevelCategoryName() {
        return thirdLevelCategoryName;
    }

    public void setThirdLevelCategoryName(String thirdLevelCategoryName) {
        this.thirdLevelCategoryName = thirdLevelCategoryName;
    }

    public String getForthLevelCategoryCode() {
        return forthLevelCategoryCode;
    }

    public void setForthLevelCategoryCode(String forthLevelCategoryCode) {
        this.forthLevelCategoryCode = forthLevelCategoryCode;
    }

    public String getForthLevelCategoryName() {
        return forthLevelCategoryName;
    }

    public void setForthLevelCategoryName(String forthLevelCategoryName) {
        this.forthLevelCategoryName = forthLevelCategoryName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getPcs() {
        return pcs;
    }

    public void setPcs(Integer pcs) {
        this.pcs = pcs;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public String getProductStatus() {
        return productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    public Integer getConsignmentSales() {
        return consignmentSales;
    }

    public void setConsignmentSales(Integer consignmentSales) {
        this.consignmentSales = consignmentSales;
    }

    public Integer getSimplePublishTag() {
        return simplePublishTag;
    }

    public void setSimplePublishTag(Integer simplePublishTag) {
        this.simplePublishTag = simplePublishTag;
    }

    public Date getEstimateArrivalPortDate() {
        return estimateArrivalPortDate;
    }

    public void setEstimateArrivalPortDate(Date estimateArrivalPortDate) {
        this.estimateArrivalPortDate = estimateArrivalPortDate;
    }

    public Date getEstimateArrivalWarehouseDate() {
        return estimateArrivalWarehouseDate;
    }

    public void setEstimateArrivalWarehouseDate(Date estimateArrivalWarehouseDate) {
        this.estimateArrivalWarehouseDate = estimateArrivalWarehouseDate;
    }

    public Integer getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(Integer isPublish) {
        this.isPublish = isPublish;
    }

    public String getPlatformCategoryId() {
        return platformCategoryId;
    }

    public void setPlatformCategoryId(String platformCategoryId) {
        this.platformCategoryId = platformCategoryId;
    }

    public String getEan() {
        return ean;
    }

    public void setEan(String ean) {
        this.ean = ean;
    }

    public Integer getConsignmentStockProcessing() {
        return consignmentStockProcessing;
    }

    public void setConsignmentStockProcessing(Integer consignmentStockProcessing) {
        this.consignmentStockProcessing = consignmentStockProcessing;
    }

    public Integer getVcCooperation() {
        return vcCooperation;
    }

    public void setVcCooperation(Integer vcCooperation) {
        this.vcCooperation = vcCooperation;
    }

    public Integer getVcCooperationModel() {
        return vcCooperationModel;
    }

    public void setVcCooperationModel(Integer vcCooperationModel) {
        this.vcCooperationModel = vcCooperationModel;
    }

    public String getVariationIdentifications() {
        return variationIdentifications;
    }

    public void setVariationIdentifications(String variationIdentifications) {
        this.variationIdentifications = variationIdentifications;
    }

    public Integer getItemPackageQuantity() {
        return itemPackageQuantity;
    }

    public void setItemPackageQuantity(Integer itemPackageQuantity) {
        this.itemPackageQuantity = itemPackageQuantity;
    }

    public String getSalesGroupCode() {
        return salesGroupCode;
    }

    public void setSalesGroupCode(String salesGroupCode) {
        this.salesGroupCode = salesGroupCode;
    }

    public Integer getMaterialIsComplete() {
        return materialIsComplete;
    }

    public void setMaterialIsComplete(Integer materialIsComplete) {
        this.materialIsComplete = materialIsComplete;
    }

    public String getSalesGroupEmptCode() {
        return salesGroupEmptCode;
    }

    public void setSalesGroupEmptCode(String salesGroupEmptCode) {
        this.salesGroupEmptCode = salesGroupEmptCode;
    }

    public String getOperationEmptCode() {
        return operationEmptCode;
    }

    public void setOperationEmptCode(String operationEmptCode) {
        this.operationEmptCode = operationEmptCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateNum() {
        return createNum;
    }

    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    public String getModifyNum() {
        return modifyNum;
    }

    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getPlatformCategoryTypeKeyword() {
        return platformCategoryTypeKeyword;
    }

    public void setPlatformCategoryTypeKeyword(String platformCategoryTypeKeyword) {
        this.platformCategoryTypeKeyword = platformCategoryTypeKeyword;
    }
}
