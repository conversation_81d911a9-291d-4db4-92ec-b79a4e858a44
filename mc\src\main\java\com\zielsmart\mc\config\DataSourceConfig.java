package com.zielsmart.mc.config;

import com.zaxxer.hikari.HikariDataSource;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.SQLManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.util.Map;

/**
 * @version V2.0
 * @title: DataSourceConfig
 * @package: com.zielsmart.mc.config
 * @description: 数据源配置
 * @author: 李耀华
 * @date: 2021-04-2210:04
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
public class DataSourceConfig {

    /**
     * dataSource
     * 配置数据源
     * @param env
     * @return {@link javax.sql.DataSource}
     * <AUTHOR>
     * @history
     */
    @Bean(name = "dataSource")
    public DataSource dataSource(Environment env){
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(env.getProperty("spring.datasource.url"));
        ds.setUsername(env.getProperty("spring.datasource.username"));
        ds.setPassword(env.getProperty("spring.datasource.password"));
        ds.setDriverClassName(env.getProperty("spring.datasource.driver-class-name"));
        ds.setMaximumPoolSize(30);
        ds.setIdleTimeout(20000);
        ds.setMaxLifetime(60000);
        ds.setMinimumIdle(5);
        ds.setConnectionTimeout(60000);
        ds.setValidationTimeout(3000);
        ds.setPoolName("HiKariDbPool");
        //设置默认的Schema
        ds.setSchema("mc");
        ds.setConnectionTestQuery("select 1 ");
        return ds;
    }

    @Bean
    public DynamicSqlManager dynamicSqlManager(ApplicationContext act) {
        Map<String, SQLManager> sqlManagerList = act.getBeansOfType(SQLManager.class);
        DynamicSqlManager conditionalSQLManager = new DynamicSqlManager(sqlManagerList.get("sqlManager"), sqlManagerList);
        return conditionalSQLManager;
    }

}
