package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomLeroymerlinAutoPushStockRecordService;
import com.zielsmart.mc.vo.SomLeroymerlinAutoPushStockRecordPageSearchVo;
import com.zielsmart.mc.vo.SomLeroymerlinAutoPushStockRecordVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomLeroymerlinAutoPushStockRecordController
 * @description
 * @date 2025-04-09 16:47:11
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somLeroymerlinAutoPushStockRecord", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Leroymerlin offers 历史记录管理")
public class SomLeroymerlinAutoPushStockRecordController extends BasicController{

    @Resource
    SomLeroymerlinAutoPushStockRecordService somLeroymerlinAutoPushStockRecordService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomLeroymerlinAutoPushStockRecordVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomLeroymerlinAutoPushStockRecordVo>> queryByPage(@RequestBody SomLeroymerlinAutoPushStockRecordPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somLeroymerlinAutoPushStockRecordService.queryByPage(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomLeroymerlinAutoPushStockRecordPageSearchVo searchVo){
        String data = somLeroymerlinAutoPushStockRecordService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
