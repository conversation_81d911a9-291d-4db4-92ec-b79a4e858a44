package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * Wayfair费用指标配置表
 * gen by 代码生成器 2024-02-27
 */

@Table(name = "mc.som_wayfair_cost_config")
public class SomWayfairCostConfig implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 名称
     */
    @Column("name")
    private String name;
    /**
     * 运算符号：≤、≥
     */
    @Column("operator")
    private String operator;
    /**
     * 毛重范围（起），KG
     */
    @Column("weight_ranges_from")
    private BigDecimal weightRangesFrom;
    /**
     * 毛重范围（止），KG
     */
    @Column("weight_ranges_to")
    private BigDecimal weightRangesTo;
    /**
     * 运输周长，CM
     */
    @Column("transport_girth")
    private BigDecimal transportGirth;
    /**
     * 第一长，CM
     */
    @Column("length")
    private BigDecimal length;
    /**
     * 第二长，CM
     */
    @Column("width")
    private BigDecimal width;
    /**
     * 第三长，CM
     */
    @Column("height")
    private BigDecimal height;
    /**
     * 配送费/件
     */
    @Column("delivery_cost")
    private BigDecimal deliveryCost;
    /**
     * 仓储费/天/m³
     */
    @Column("storage_charge")
    private BigDecimal storageCharge;
    /**
     * RTV召回费用
     */
    @Column("rtv_cost")
    private BigDecimal rtvCost;
    /**
     * VAS费用
     */
    @Column("vas_cost")
    private BigDecimal vasCost;
    /**
     * 币种：USD、EUR
     */
    @Column("currency")
    private String currency;
    /**
     * 优先级
     */
    @Column("priority")
    private Integer priority;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 最后修改人工号
     */
    @Column("last_modify_num")
    private String lastModifyNum;
    /**
     * 最后修改人姓名
     */
    @Column("last_modify_name")
    private String lastModifyName;
    /**
     * 最后修改时间
     */
    @Column("last_modify_time")
    private Date lastModifyTime;
    /**
     * 跨渠道配送费/件
     */
    @Column("cross_channel_delivery_cost")
    private BigDecimal crossChannelDeliveryCost;
    /**
     * 跨渠道配送费/2件
     */
    @Column("cross_channel_delivery_cost_2_unit")
    private BigDecimal crossChannelDeliveryCost2Unit;
    /**
     * 跨渠道配送费/3件
     */
    @Column("cross_channel_delivery_cost_3_unit")
    private BigDecimal crossChannelDeliveryCost3Unit;
    /**
     * 跨渠道配送费/4件
     */
    @Column("cross_channel_delivery_cost_4_unit")
    private BigDecimal crossChannelDeliveryCost4Unit;
    /**
     * 跨渠道配送费/4件
     */
    @Column("cross_channel_delivery_cost_5_unit")
    private BigDecimal crossChannelDeliveryCost5Unit;
    /**
     * 站点
     */
    @Column("site")
    private String site;

    public SomWayfairCostConfig() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 名称
     *
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     *
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 运算符号：≤、≥
     *
     * @return
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 运算符号：≤、≥
     *
     * @param operator
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 毛重范围（起），KG
     *
     * @return
     */
    public BigDecimal getWeightRangesFrom() {
        return weightRangesFrom;
    }

    /**
     * 毛重范围（起），KG
     *
     * @param weightRangesFrom
     */
    public void setWeightRangesFrom(BigDecimal weightRangesFrom) {
        this.weightRangesFrom = weightRangesFrom;
    }

    /**
     * 毛重范围（止），KG
     *
     * @return
     */
    public BigDecimal getWeightRangesTo() {
        return weightRangesTo;
    }

    /**
     * 毛重范围（止），KG
     *
     * @param weightRangesTo
     */
    public void setWeightRangesTo(BigDecimal weightRangesTo) {
        this.weightRangesTo = weightRangesTo;
    }

    /**
     * 运输周长，CM
     *
     * @return
     */
    public BigDecimal getTransportGirth() {
        return transportGirth;
    }

    /**
     * 运输周长，CM
     *
     * @param transportGirth
     */
    public void setTransportGirth(BigDecimal transportGirth) {
        this.transportGirth = transportGirth;
    }

    /**
     * 第一长，CM
     *
     * @return
     */
    public BigDecimal getLength() {
        return length;
    }

    /**
     * 第一长，CM
     *
     * @param length
     */
    public void setLength(BigDecimal length) {
        this.length = length;
    }

    /**
     * 第二长，CM
     *
     * @return
     */
    public BigDecimal getWidth() {
        return width;
    }

    /**
     * 第二长，CM
     *
     * @param width
     */
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    /**
     * 第三长，CM
     *
     * @return
     */
    public BigDecimal getHeight() {
        return height;
    }

    /**
     * 第三长，CM
     *
     * @param height
     */
    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    /**
     * 配送费/件
     *
     * @return
     */
    public BigDecimal getDeliveryCost() {
        return deliveryCost;
    }

    /**
     * 配送费/件
     *
     * @param deliveryCost
     */
    public void setDeliveryCost(BigDecimal deliveryCost) {
        this.deliveryCost = deliveryCost;
    }

    /**
     * 仓储费/天/m³
     *
     * @return
     */
    public BigDecimal getStorageCharge() {
        return storageCharge;
    }

    /**
     * 仓储费/天/m³
     *
     * @param storageCharge
     */
    public void setStorageCharge(BigDecimal storageCharge) {
        this.storageCharge = storageCharge;
    }

    /**
     * RTV召回费用
     *
     * @return
     */
    public BigDecimal getRtvCost() {
        return rtvCost;
    }

    /**
     * RTV召回费用
     *
     * @param rtvCost
     */
    public void setRtvCost(BigDecimal rtvCost) {
        this.rtvCost = rtvCost;
    }

    /**
     * VAS费用
     *
     * @return
     */
    public BigDecimal getVasCost() {
        return vasCost;
    }

    /**
     * VAS费用
     *
     * @param vasCost
     */
    public void setVasCost(BigDecimal vasCost) {
        this.vasCost = vasCost;
    }

    /**
     * 币种：USD、EUR
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种：USD、EUR
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 优先级
     *
     * @return
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * 优先级
     *
     * @param priority
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改人工号
     *
     * @return
     */
    public String getLastModifyNum() {
        return lastModifyNum;
    }

    /**
     * 最后修改人工号
     *
     * @param lastModifyNum
     */
    public void setLastModifyNum(String lastModifyNum) {
        this.lastModifyNum = lastModifyNum;
    }

    /**
     * 最后修改人姓名
     *
     * @return
     */
    public String getLastModifyName() {
        return lastModifyName;
    }

    /**
     * 最后修改人姓名
     *
     * @param lastModifyName
     */
    public void setLastModifyName(String lastModifyName) {
        this.lastModifyName = lastModifyName;
    }

    /**
     * 最后修改时间
     *
     * @return
     */
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * 最后修改时间
     *
     * @param lastModifyTime
     */
    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public BigDecimal getCrossChannelDeliveryCost() {
        return crossChannelDeliveryCost;
    }

    public void setCrossChannelDeliveryCost(BigDecimal crossChannelDeliveryCost) {
        this.crossChannelDeliveryCost = crossChannelDeliveryCost;
    }

    public BigDecimal getCrossChannelDeliveryCost2Unit() {
        return crossChannelDeliveryCost2Unit;
    }

    public void setCrossChannelDeliveryCost2Unit(BigDecimal crossChannelDeliveryCost2Unit) {
        this.crossChannelDeliveryCost2Unit = crossChannelDeliveryCost2Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost3Unit() {
        return crossChannelDeliveryCost3Unit;
    }

    public void setCrossChannelDeliveryCost3Unit(BigDecimal crossChannelDeliveryCost3Unit) {
        this.crossChannelDeliveryCost3Unit = crossChannelDeliveryCost3Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost4Unit() {
        return crossChannelDeliveryCost4Unit;
    }

    public void setCrossChannelDeliveryCost4Unit(BigDecimal crossChannelDeliveryCost4Unit) {
        this.crossChannelDeliveryCost4Unit = crossChannelDeliveryCost4Unit;
    }

    public BigDecimal getCrossChannelDeliveryCost5Unit() {
        return crossChannelDeliveryCost5Unit;
    }

    public void setCrossChannelDeliveryCost5Unit(BigDecimal crossChannelDeliveryCost5Unit) {
        this.crossChannelDeliveryCost5Unit = crossChannelDeliveryCost5Unit;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }
}
