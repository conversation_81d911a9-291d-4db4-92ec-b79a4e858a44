package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomDealRuleConfigService;
import com.zielsmart.mc.vo.SomDealRuleConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomDealRuleConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somDealRuleConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "秒杀规则配置表管理")
public class SomDealRuleConfigController extends BasicController {

    @Resource
    SomDealRuleConfigService somDealRuleConfigService;

    /**
     * addOrEdit
     * 新增或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR> @history
     */
    @Operation(summary = "新增或编辑")
    @PostMapping(value = "/addOrEdit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomDealRuleConfigVo addOrEditVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somDealRuleConfigService.addOrEdit(addOrEditVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @return {@link ResultVo< PageVo< SomDealRuleConfigVo>>}
     * <AUTHOR> @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomDealRuleConfigVo>> queryByPage(@RequestBody SomDealRuleConfigPageSearchVo pageSearchVo) {
        return ResultVo.ofSuccess(somDealRuleConfigService.queryByPage(pageSearchVo));
    }

    /**
     * queryByAid
     * 查看
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomDealRuleConfigVo>>}
     * <AUTHOR> @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomDealRuleConfigVo> queryByAid(@RequestBody SomDealRuleConfigVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somDealRuleConfigService.queryByAid(searchVo));
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR> @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomDealRuleConfigVo deleteVo) throws ValidateException {
        somDealRuleConfigService.delete(deleteVo);
        return ResultVo.ofSuccess(null);
    }
}
