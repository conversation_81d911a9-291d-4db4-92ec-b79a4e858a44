package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomAreaInfoService;
import com.zielsmart.mc.vo.SomAreaInfoPageSearchVo;
import com.zielsmart.mc.vo.SomAreaInfoVo;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAreaInfoController
 * @description
 * @date 2025-07-29 12:09:41
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAreaInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "区域表管理")
public class SomAreaInfoController extends BasicController{

    @Resource
    SomAreaInfoService somAreaInfoService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAreaInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据等级查询")
    @PostMapping(value = "/query-by-level")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomAreaInfoVo>> queryByLevel(@RequestBody SomAreaInfoPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAreaInfoService.queryByLevel(searchVo));
    }

}
