package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomTemuWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTemuWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-04-03
*/

@SqlResource("somTemuWarehouseConfig")
public interface SomTemuWarehouseConfigMapper extends BaseMapper<SomTemuWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuWarehouseConfigVo> queryByPage(@Param("searchVo")SomTemuWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
