package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.util.Date;
/*
* 拼多多平台Listing
* gen by 代码生成器 2024-04-03
*/

@Table(name="mc.som_temu_listing")
public class SomTemuListing implements java.io.Serializable {

	@Column("skc_site_status")
	private Integer skcSiteStatus;

	@Column("status")
	private Integer status;

	@Column("sub_status")
	private Integer subStatus;

	//类目ID
	@Column("cat_id")
	private Integer catId;

	//类目名称
	@Column("cat_name")
	private String catName;

	@Column("color_name")
	private String colorName;






	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 库存数量
	 */
	@Column("virtual_stock")
	private Integer virtualStock ;
	/**
	 * 产品ID
	 */
	@Column("product_id")
	private String productId ;
	/**
	 * 平台SKU唯一编码
	 */
	@Column("product_sku_id")
	private String productSkuId ;
	/**
	 * Skc id
	 */
	@Column("product_skc_id")
	private String productSkcId ;
	/**
	 * 产品名称
	 */
	@Column("product_name")
	private String productName ;
	/**
	 * 主图地址
	 */
	@Column("main_image_url")
	private String mainImageUrl ;
	/**
	 * 下载时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	@Column("account_id")
	private String accountId ;

	@Column("account_tag")
	private String accountTag ;

	@Column("price")
	private BigDecimal price ;

	@Column("goods_type")
	private Integer goodsType ;

	/**
	 * Temu平台下载的站点列表
	 */
	@Column("sites")
	private PGobject sites;

	public SomTemuListing() {
	}

	public PGobject getSites() {
		return sites;
	}

	public void setSites(PGobject sites) {
		this.sites = sites;
	}

	public Integer getSkcSiteStatus() {
		return skcSiteStatus;
	}

	public void setSkcSiteStatus(Integer skcSiteStatus) {
		this.skcSiteStatus = skcSiteStatus;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSubStatus() {
		return subStatus;
	}

	public void setSubStatus(Integer subStatus) {
		this.subStatus = subStatus;
	}

	public Integer getCatId() {
		return catId;
	}

	public void setCatId(Integer catId) {
		this.catId = catId;
	}

	public String getCatName() {
		return catName;
	}

	public void setCatName(String catName) {
		this.catName = catName;
	}

	public String getColorName() {
		return colorName;
	}

	public void setColorName(String colorName) {
		this.colorName = colorName;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getGoodsType() {
		return goodsType;
	}

	public void setGoodsType(Integer goodsType) {
		this.goodsType = goodsType;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getAccountTag() {
		return accountTag;
	}

	public void setAccountTag(String accountTag) {
		this.accountTag = accountTag;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 库存数量
	*@return
	*/
	public Integer getVirtualStock(){
		return  virtualStock;
	}
	/**
	* 库存数量
	*@param  virtualStock
	*/
	public void setVirtualStock(Integer virtualStock ){
		this.virtualStock = virtualStock;
	}
	/**
	* 产品ID
	*@return
	*/
	public String getProductId(){
		return  productId;
	}
	/**
	* 产品ID
	*@param  productId
	*/
	public void setProductId(String productId ){
		this.productId = productId;
	}
	/**
	* 平台SKU唯一编码
	*@return
	*/
	public String getProductSkuId(){
		return  productSkuId;
	}
	/**
	* 平台SKU唯一编码
	*@param  productSkuId
	*/
	public void setProductSkuId(String productSkuId ){
		this.productSkuId = productSkuId;
	}
	/**
	* Skc id
	*@return
	*/
	public String getProductSkcId(){
		return  productSkcId;
	}
	/**
	* Skc id
	*@param  productSkcId
	*/
	public void setProductSkcId(String productSkcId ){
		this.productSkcId = productSkcId;
	}
	/**
	* 产品名称
	*@return
	*/
	public String getProductName(){
		return  productName;
	}
	/**
	* 产品名称
	*@param  productName
	*/
	public void setProductName(String productName ){
		this.productName = productName;
	}
	/**
	* 主图地址
	*@return
	*/
	public String getMainImageUrl(){
		return  mainImageUrl;
	}
	/**
	* 主图地址
	*@param  mainImageUrl
	*/
	public void setMainImageUrl(String mainImageUrl ){
		this.mainImageUrl = mainImageUrl;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 下载时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
