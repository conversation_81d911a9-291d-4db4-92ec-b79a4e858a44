package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 产品系列配置表
 * gen by 代码生成器 2024-04-11
 */

@Table(name = "mc.som_product_series_configuration")
public class SomProductSeriesConfiguration implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 风格英文名称
     */
    @Column("style_name_en")
    private String styleNameEn;
    /**
     * 系列英文名称
     */
    @Column("series_name_en")
    private String seriesNameEn;
    /**
     * 系列封面图URL
     */
    @Column("series_cover_image_url")
    private String seriesCoverImageUrl;
    /**
     * 0.禁用 1.启用
     */
    @Column("is_enabled")
    private Integer isEnabled;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    @Column("series_banner_image_url")
    private String seriesBannerImageUrl;
    @Column("series_definition")
    private String seriesDefinition;
    @Column("sort")
    private Integer sort;

    public SomProductSeriesConfiguration() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 风格英文名称
     *
     * @return
     */
    public String getStyleNameEn() {
        return styleNameEn;
    }

    /**
     * 风格英文名称
     *
     * @param styleNameEn
     */
    public void setStyleNameEn(String styleNameEn) {
        this.styleNameEn = styleNameEn;
    }

    /**
     * 系列英文名称
     *
     * @return
     */
    public String getSeriesNameEn() {
        return seriesNameEn;
    }

    /**
     * 系列英文名称
     *
     * @param seriesNameEn
     */
    public void setSeriesNameEn(String seriesNameEn) {
        this.seriesNameEn = seriesNameEn;
    }

    /**
     * 系列封面图URL
     *
     * @return
     */
    public String getSeriesCoverImageUrl() {
        return seriesCoverImageUrl;
    }

    /**
     * 系列封面图URL
     *
     * @param seriesCoverImageUrl
     */
    public void setSeriesCoverImageUrl(String seriesCoverImageUrl) {
        this.seriesCoverImageUrl = seriesCoverImageUrl;
    }

    /**
     * 0.禁用 1.启用
     *
     * @return
     */
    public Integer getisEnabled() {
        return isEnabled;
    }

    /**
     * 0.禁用 1.启用
     *
     * @param isEnabled
     */
    public void setisEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSeriesBannerImageUrl() {
        return seriesBannerImageUrl;
    }

    public void setSeriesBannerImageUrl(String seriesBannerImageUrl) {
        this.seriesBannerImageUrl = seriesBannerImageUrl;
    }

    public String getSeriesDefinition() {
        return seriesDefinition;
    }

    public void setSeriesDefinition(String seriesDefinition) {
        this.seriesDefinition = seriesDefinition;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
