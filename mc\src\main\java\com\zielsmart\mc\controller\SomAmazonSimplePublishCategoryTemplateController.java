package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonSimplePublishCategoryTemplateService;
import com.zielsmart.mc.vo.SomAmazonSimplePublishCategoryTemplatePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishCategoryTemplateVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonSimplePublishCategoryTemplateController
 * @description Amazon简单上货类目模板配置管理
 * @date 2025-07-17 10:24:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonSimplePublishCategoryTemplate", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon简单上货类目模板配置管理")
public class SomAmazonSimplePublishCategoryTemplateController extends BasicController {

    @Resource
    SomAmazonSimplePublishCategoryTemplateService somAmazonSimplePublishCategoryTemplateService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomAmazonSimplePublishCategoryTemplateVo>> queryByPage(@RequestBody SomAmazonSimplePublishCategoryTemplatePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonSimplePublishCategoryTemplateService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestParam("file") MultipartFile file, String site, String platformCategoryId,
                                 String platformCategoryName, String categoryCode, Integer sheetNum,
                                 @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        somAmazonSimplePublishCategoryTemplateService.save(file, site, platformCategoryId, platformCategoryName, categoryCode, sheetNum, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomAmazonSimplePublishCategoryTemplateVo somAmazonSimplePublishCategoryTemplateVo) throws ValidateException {
        somAmazonSimplePublishCategoryTemplateService.delete(somAmazonSimplePublishCategoryTemplateVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出模版")
    @PostMapping(value = "/template/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> exportTemplate(@RequestBody SomAmazonSimplePublishCategoryTemplateVo templateVo) throws Exception {
        String data = somAmazonSimplePublishCategoryTemplateService.exportTemplate(templateVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
