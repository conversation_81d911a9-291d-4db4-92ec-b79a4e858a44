package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;

import java.util.Date;
/*
 * 亚马逊类目可批量修改的字段
 * gen by 代码生成器 2025-07-28
 */

@Table(name = "mc.som_category_batch_modify_properties")
public class SomCategoryBatchModifyProperties implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 类目所属产品类型
     */
    @Column("product_type")
    private String productType;
    /**
     * 字段路径
     */
    @Column("properties_path")
    private String propertiesPath;
    /**
     * 描述
     */
    @Column("properties_description")
    private String propertiesDescription;
    /**
     * 字段名称
     */
    @Column("properties_name")
    private String propertiesName;
    /**
     * 字段类型
     */
    @Column("properties_type")
    private String propertiesType;
    /**
     * 枚举名称
     */
    @Column("properties_enum_names")
    private PGobject propertiesEnumNames;
    /**
     * 枚举值
     */
    @Column("properties_enum")
    private PGobject propertiesEnum;
    /**
     * 示例数据
     */
    @Column("examples")
    private PGobject examples;
    /**
     * 类目ID
     */
    @Column("category_id")
    private String categoryId;

    @Column("create_time")
    private Date createTime;


    public SomCategoryBatchModifyProperties() {
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 类目所属产品类型
     *
     * @return
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 类目所属产品类型
     *
     * @param productType
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * 字段路径
     *
     * @return
     */
    public String getPropertiesPath() {
        return propertiesPath;
    }

    /**
     * 字段路径
     *
     * @param propertiesPath
     */
    public void setPropertiesPath(String propertiesPath) {
        this.propertiesPath = propertiesPath;
    }

    /**
     * 描述
     *
     * @return
     */
    public String getPropertiesDescription() {
        return propertiesDescription;
    }

    /**
     * 描述
     *
     * @param propertiesDescription
     */
    public void setPropertiesDescription(String propertiesDescription) {
        this.propertiesDescription = propertiesDescription;
    }

    /**
     * 字段名称
     *
     * @return
     */
    public String getPropertiesName() {
        return propertiesName;
    }

    /**
     * 字段名称
     *
     * @param propertiesName
     */
    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    /**
     * 字段类型
     *
     * @return
     */
    public String getPropertiesType() {
        return propertiesType;
    }

    /**
     * 字段类型
     *
     * @param propertiesType
     */
    public void setPropertiesType(String propertiesType) {
        this.propertiesType = propertiesType;
    }

    /**
     * 枚举名称
     *
     * @return
     */
    public PGobject getPropertiesEnumNames() {
        return propertiesEnumNames;
    }

    /**
     * 枚举名称
     *
     * @param propertiesEnumNames
     */
    public void setPropertiesEnumNames(PGobject propertiesEnumNames) {
        this.propertiesEnumNames = propertiesEnumNames;
    }

    /**
     * 枚举值
     *
     * @return
     */
    public PGobject getPropertiesEnum() {
        return propertiesEnum;
    }

    /**
     * 枚举值
     *
     * @param propertiesEnum
     */
    public void setPropertiesEnum(PGobject propertiesEnum) {
        this.propertiesEnum = propertiesEnum;
    }

    /**
     * 示例数据
     *
     * @return
     */
    public PGobject getExamples() {
        return examples;
    }

    /**
     * 示例数据
     *
     * @param examples
     */
    public void setExamples(PGobject examples) {
        this.examples = examples;
    }

    /**
     * 类目ID
     *
     * @return
     */
    public String getCategoryId() {
        return categoryId;
    }

    /**
     * 类目ID
     *
     * @param categoryId
     */
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

}
