package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.zielsmart.mc.event.AmazonActivityReminderEvent;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.util.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.zbpm.ProcessDefinitionVo;
import com.zielsmart.mc.vo.zbpm.ZBPMCouponSubmitVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.listener.EventBusTemplate;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomCouponService {

    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SysDeptNeweyaMapper deptMapper;
    @Resource
    private McUserMapper userMapper;
    @Resource
    private SomCouponMapper somCouponMapper;
    @Resource
    private SomCouponItemsMapper somCouponItemsMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;
    @Resource
    private McListingInfoAmazonMapper listingInfoAmazonMapper;
    @Resource
    private McPlatformPropertiesMapper platformPropertiesMapper;
    @Resource
    private AmazonService amazonService;
    @Resource
    private EventBusTemplate eventBusTemplate;
    @Resource
    private McStockInfoMapper stockInfoMapper;
    @Resource
    private McSellerskuMappingMapper skuMappingMapper;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private RestTemplate restTemplate;
    @Value("${remote.oa.remote.url}")
    private String oaUrl;


    /**
     * addOrClone
     * 新增
     *
     * @param addVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrClone(SomCouponExVo addVo, TokenUserInfo tokenUser) throws ValidateException {
        SomCoupon coupon = new SomCoupon();
        List<SomCouponItems> itemsList = CollectionUtil.newArrayList();
        if (StrUtil.isEmpty(addVo.getAid())) {
            validate(addVo);
            // 组织数据主表
            String uuid = IdUtil.fastSimpleUUID();
            coupon = ConvertUtils.beanConvert(addVo, SomCoupon.class);
            // 获取币种
            McPlatformProperties pl = platformPropertiesMapper.createLambdaQuery().andEq("platform", coupon.getPlatform()).andEq("site", coupon.getSite()).single();
            coupon.setAid(uuid);
            coupon.setStatus(10);
            if (ObjectUtil.isNotNull(pl) && StrUtil.isNotBlank(pl.getCurrencyCode())) {
                coupon.setCurrency(pl.getCurrencyCode());
            }
            coupon.setCreateNum(tokenUser.getJobNumber());
            coupon.setCreateTime(DateTime.now().toJdkDate());
            // 组织从表数据
            List<SomCouponItemsVo> itemsVoList = somCouponItemsMapper.checkUnique(addVo);
            for (SomCouponItemsVo f : addVo.getItemsVoList()) {
                // 唯一性校验
                boolean match = itemsVoList.stream().anyMatch(pc -> StrUtil.equals(f.getSellerSku(), pc.getSellerSku()));
                if (match) {
                    throw new ValidateException("活动起止日期内存在该产品的Coupon活动，不允许重复提报");
                }
                SomCouponItems items = ConvertUtils.beanConvert(f, SomCouponItems.class);
                items.setAid(IdUtil.fastSimpleUUID());
                items.setCouponId(uuid);
                itemsList.add(items);
            }
            coupon.setCurrencySymbol(itemsList.get(0).getCurrencySymbol());
        } else {
            SomCoupon couponTemp = somCouponMapper.createLambdaQuery().andEq("aid", addVo.getAid()).single();
            String aid = IdUtil.fastUUID();
            couponTemp.setAid(aid);
            if (addVo.getTitle().contains("on")) {
                String title = StrUtil.subAfter(addVo.getTitle(), "on", true).trim();
                couponTemp.setTitle(title);
            } else {
                couponTemp.setTitle(addVo.getTitle());
            }
            couponTemp.setBeginDate(addVo.getBeginDate());
            couponTemp.setEndDate(addVo.getEndDate());
            couponTemp.setStatus(10);
            couponTemp.setCreateNum(tokenUser.getJobNumber());
            couponTemp.setCreateTime(DateTime.now().toJdkDate());
            couponTemp.setBeginDateReal(null);
            couponTemp.setEndDateReal(null);
            couponTemp.setFinishType(null);
            couponTemp.setFinishReason(null);
            couponTemp.setCancelReason(null);
            couponTemp.setFinishDate(null);
            couponTemp.setCancelDate(null);
            List<SomCouponItems> couponItemsList = somCouponItemsMapper.createLambdaQuery().andEq("coupon_id", addVo.getAid()).select();
            SomDealSearchVo dealSearchVo = new SomDealSearchVo();
            dealSearchVo.setSites(Collections.singletonList(couponTemp.getSite()));
            dealSearchVo.setSellerSkus(couponItemsList.stream().map(m -> m.getSellerSku()).collect(Collectors.toList()));
            List<SomDealVo> dealVoList = dynamicSqlManager.getMapper(SomDealMapper.class).searchDealList(dealSearchVo);
            addVo.setSite(couponTemp.getSite());
            List<SomCouponItemsVo> itemsVoList = somCouponItemsMapper.checkUnique(addVo);
            for (SomCouponItems items : couponItemsList) {
                boolean match = itemsVoList.stream().anyMatch(pc -> StrUtil.equals(items.getSellerSku(), pc.getSellerSku()));
                if (match) {
                    throw new ValidateException("活动起止日期内存在该产品的Coupon活动，不允许重复提报");
                }
                items.setAid(IdUtil.fastUUID());
                items.setCouponId(aid);
                McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
                searchExVo.setSite(couponTemp.getSite());
                searchExVo.setKeyWord(items.getSellerSku());
                searchExVo.setBeginDate(couponTemp.getBeginDate());
                searchExVo.setEndDate(couponTemp.getEndDate());
                BigDecimal promotionDiscount = BigDecimal.ZERO;
                try {
                    promotionDiscount = amazonService.queryPromotionDiscount(searchExVo);
                } catch (Exception e) {
                    throw new ValidateException("获取promotion折扣信息出错" + e.getMessage());
                }
                items.setPromotionDiscount(promotionDiscount);
                SomDealVo dealVo = dealVoList.stream().filter(t -> StrUtil.equals(items.getSellerSku(), t.getSellerSku()) && ((couponTemp.getBeginDate().compareTo(t.getPlanStartDate()) == -1)
                        || (couponTemp.getBeginDate().compareTo(t.getPlanStartDate()) == 0)) && (couponTemp.getEndDate().after(t.getPlanEndDate()))).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(dealVo) && ObjectUtil.isNotEmpty(dealVo.getDealDiscount())) {
                    items.setDealDiscount(dealVo.getDealDiscount());
                } else {
                    items.setDealDiscount(BigDecimal.ZERO);
                }
                // 总折扣
                items.setTotalDiscount(items.getCouponDiscount().add(items.getDealDiscount()).add(items.getPromotionDiscount()));
                // 总折扣金额
                items.setTotalDiscountAmount(items.getTotalDiscount().multiply(items.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
                // 预估成交价
                items.setTransactionPrice(items.getPrice().subtract(items.getTotalDiscountAmount()));
                itemsList.add(items);
            }
            coupon = couponTemp;
        }
        somCouponMapper.insert(coupon);

        if (CollectionUtil.isNotEmpty(itemsList)) {
            // 计算库存可售天数
            /**
             List<McStockInfoExtVo> stockInfoList = stockInfoMapper.queryBySitesAndSkus(Collections.singletonList(addVo.getSite()), itemsList.stream().map(m -> m.getSku()).collect(Collectors.toList()));
             for(SomCouponItems items :itemsList){
             List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f ->StrUtil.equalsIgnoreCase(addVo.getSite(), f.getSite()) &&
             StrUtil.equalsIgnoreCase(items.getSku(), f.getProductMainCode())).collect(Collectors.toList());
             if(CollectionUtil.isNotEmpty(stockInfos)){
             long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
             BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
             if(BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0){
             items.setStockSaleDays(0);
             }else {
             // 库存可销售天数 库存/7天发货平均值 只取整数
             int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber,0,BigDecimal.ROUND_DOWN).intValue();
             items.setStockSaleDays(stockSaleDays);
             }
             }else {
             items.setStockSaleDays(0);
             }
             }
             */
            List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (SomCouponItems items : itemsList) {
                MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
                activity.setSite(addVo.getSite());
                activity.setSellerSku(items.getSellerSku());
                activity.setStartDate(sdf.format(addVo.getBeginDate()));
                activity.setEndDate(sdf.format(addVo.getEndDate()));
                activity.setType("Coupon");
                body.add(activity);
            }
            MarketActivityUtil activityUtil = new MarketActivityUtil();
            MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, true);
            if (!daysMsgData.isSuccess()) {
                String errorMsg = daysMsgData.getErrorMap().values().stream().collect(Collectors.joining(","));
                throw new ValidateException(errorMsg);
            } else {
                for (SomCouponItems items : itemsList) {
                    items.setStockSaleDays((Integer)daysMsgData.getDaysMap().get(items.getSellerSku()));
                }
            }
            somCouponItemsMapper.insertBatch(itemsList);
        }
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo< SomCouponExVo >}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomCouponExVo> queryByPage(SomCouponPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        // 查询符合条件的主数据
        List<SomCouponExVo> totalList = dynamicSqlManager.getMapper(SomCouponMapper.class).querytotalCount(searchVo);
        DefaultPageResult<SomCouponExVo> pageResult = dynamicSqlManager.getMapper(SomCouponMapper.class).queryByPage(searchVo, pageRequest);
        pageResult.setTotalRow(totalList.size());
        if (totalList.size() == 0L) {
            pageResult.setTotalPage(1L);
        } else if (totalList.size() % (long) pageRequest.getPageSize() == 0L) {
            pageResult.setTotalPage(totalList.size() / (long) pageRequest.getPageSize());
        } else {
            pageResult.setTotalPage(totalList.size() / (long) pageRequest.getPageSize() + 1L);
        }
        // 转义目标客户/活动类型/活动状态/活动结束类型
        List<String> codeList = new ArrayList<>();
        codeList.add("CouponTargetCustomer");
        codeList.add("CouponType");
        codeList.add("CouponStatus");
        codeList.add("CouponFinishType");
        List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
        pageResult.getList().forEach(f -> {
            infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponTargetCustomer") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getTargetCustomer().toString())).findFirst().ifPresent(pc -> {
                f.setTargetCustomerName(pc.getItemLable());
            });
            infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponType") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getCouponType().toString())).findFirst().ifPresent(pc -> {
                f.setCouponTypeName(pc.getItemLable());
            });
            infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponStatus") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getStatus().toString())).findFirst().ifPresent(pc -> {
                f.setStatusName(pc.getItemLable());
            });
            if (ObjectUtil.isNull(f.getFinishType())) {
                f.setFinishTypeName("-");
            } else {
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponFinishType") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getFinishType().toString())).findFirst().ifPresent(pc -> {
                    f.setFinishTypeName(pc.getItemLable());
                });
            }
            if (ObjectUtil.equal(10, f.getCouponType())) {
                String titleExt = StrUtil.format("Save {}{} on {}", f.getCurrencySymbol(), f.getCouponDiscountAmount(), f.getTitle());
                f.setTitleExt(titleExt);
            } else {
                String titleExt = StrUtil.format("Save {}% on {}", f.getCouponDiscount(), f.getTitle());
                f.setTitleExt(titleExt);
            }
        });
        // 填充从数据
        List<String> aidList = pageResult.getList().stream().map(m -> m.getAid()).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(aidList)) {
            List<String> siteList = pageResult.getList().stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
            List<SysDeptNeweya> deptList = deptMapper.createLambdaQuery().andEq("is_enabled", 1).select();
            List<McUser> userList = userMapper.all();
            List<SomCouponItems> itemsList = somCouponItemsMapper.createLambdaQuery().andIn("coupon_id", aidList).select();
            List<String> sellerSKuList = itemsList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
            List<McProductSales> productSalesList = productSalesMapper.createLambdaQuery().andIn("site", siteList).andIn("display_product_code", sellerSKuList).select();
            pageResult.getList().forEach(f -> {
                List<SomCouponItems> tempList = itemsList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getCouponId(), f.getAid())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(tempList)) {
                    List<SomCouponItemsVo> itemsVoList = ConvertUtils.listConvert(tempList, SomCouponItemsVo.class);
                    itemsVoList.forEach(s -> {
                        McProductSales productSales = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(s.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
                        if (ObjectUtil.isNotEmpty(productSales)) {
                            deptList.stream().filter(d -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupCode(), d.getDeptCode())).findFirst().ifPresent(ps -> {
                                s.setSalesGroupName(ps.getDeptNameCn());
                            });
                            userList.stream().filter(u -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupEmptCode(), u.getJobNumber())).findFirst().ifPresent(ps -> {
                                s.setSalesGroupEmptName(ps.getUserName());
                            });
                            userList.stream().filter(u -> StrUtil.equalsIgnoreCase(productSales.getOperationEmptCode(), u.getJobNumber())).findFirst().ifPresent(ps -> {
                                s.setOperationEmptName(ps.getUserName());
                            });
                        }
                    });
                    f.setItemsVoList(itemsVoList);
                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomCouponExVo.class, searchVo);
    }

    /**
     * queryByAid
     * 查看
     *
     * @param queryVo
     * @return {@link SomCouponExVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomCouponExVo queryByAid(SomCouponVo queryVo) throws ValidateException {
        if (StrUtil.isBlank(queryVo.getAid())) {
            throw new ValidateException("请选择要查看的数据");
        }
        SomCouponExVo exVo = new SomCouponExVo();
        SomCoupon vo = somCouponMapper.createLambdaQuery().andEq("aid", queryVo.getAid()).single();
        if (ObjectUtil.isNotNull(vo)) {
            exVo = ConvertUtils.beanConvert(vo, SomCouponExVo.class);
            // 转义目标客户/活动类型/活动状态/活动结束类型
            List<String> codeList = new ArrayList<>();
            codeList.add("CouponTargetCustomer");
            codeList.add("CouponType");
            codeList.add("CouponStatus");
            codeList.add("CouponFinishType");
            codeList.add("CouponVoucherType");
            codeList.add("CouponBrand");
            codeList.add("CouponBrandAudience");

            List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
            McDictionaryInfo dictionaryInfo = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponTargetCustomer") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getTargetCustomer().toString())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(dictionaryInfo) && StrUtil.isNotBlank(dictionaryInfo.getItemLable())) {
                exVo.setTargetCustomerName(dictionaryInfo.getItemLable());
            }
            if (vo.getVoucherType() != null) {
                McDictionaryInfo voucherType = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponVoucherType") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getVoucherType().toString())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(voucherType) && StrUtil.isNotBlank(voucherType.getItemLable())) {
                    exVo.setVoucherTypeStr(voucherType.getItemLable());
                }
            }
            if (vo.getBrand() != null) {
                McDictionaryInfo brandType = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponBrand") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getBrand().toString())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(brandType) && StrUtil.isNotBlank(brandType.getItemLable())) {
                    exVo.setBrandStr(brandType.getItemLable());
                }
            }

            if (vo.getBrandAudience() != null) {
                McDictionaryInfo brandAudienceType = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponBrandAudience") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getBrandAudience().toString())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(brandAudienceType) && StrUtil.isNotBlank(brandAudienceType.getItemLable())) {
                    exVo.setBrandAudienceStr(brandAudienceType.getItemLable());
                }
            }

            McDictionaryInfo info = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponType") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getCouponType().toString())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(info) && StrUtil.isNotBlank(info.getItemLable())) {
                exVo.setCouponTypeName(info.getItemLable());
            }

            McDictionaryInfo mcDictionaryInfo = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponStatus") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getStatus().toString())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(mcDictionaryInfo) && StrUtil.isNotBlank(mcDictionaryInfo.getItemLable())) {
                exVo.setStatusName(mcDictionaryInfo.getItemLable());
            }
            if (ObjectUtil.isNull(exVo.getFinishType())) {
                exVo.setFinishTypeName("-");
            } else {
                McDictionaryInfo finishType = infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "CouponFinishType") && StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getFinishType().toString())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(finishType) && StrUtil.isNotBlank(finishType.getItemLable())) {
                    exVo.setFinishTypeName(finishType.getItemLable());
                }
            }
            McUser user = userMapper.createLambdaQuery().andEq("job_number", vo.getCreateNum()).single();
            if (ObjectUtil.isNotNull(user) && StrUtil.isNotBlank(user.getUserName())) {
                exVo.setCreateName(user.getUserName());
            }
            List<SomCouponItems> itemsList = somCouponItemsMapper.createLambdaQuery().andEq("coupon_id", vo.getAid()).select();
            if (CollectionUtil.isNotEmpty(itemsList)) {
                List<SysDeptNeweya> deptList = deptMapper.createLambdaQuery().andEq("is_enabled", 1).select();
                List<McUser> userList = userMapper.all();
                List<String> sellerSKuList = itemsList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
                List<McProductSales> productSalesList = productSalesMapper.createLambdaQuery().andEq("site", vo.getSite()).andIn("display_product_code", sellerSKuList).select();
                List<SomCouponItemsVo> itemsVoList = ConvertUtils.listConvert(itemsList, SomCouponItemsVo.class);
                itemsVoList.forEach(s -> {
                    McProductSales productSales = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(vo.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(s.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(productSales)) {
                        deptList.stream().filter(d -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupCode(), d.getDeptCode())).findFirst().ifPresent(ps -> {
                            s.setSalesGroupName(ps.getDeptNameCn());
                        });
                        userList.stream().filter(u -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupEmptCode(), u.getJobNumber())).findFirst().ifPresent(ps -> {
                            s.setSalesGroupEmptName(ps.getUserName());
                        });
                        userList.stream().filter(u -> StrUtil.equalsIgnoreCase(productSales.getOperationEmptCode(), u.getJobNumber())).findFirst().ifPresent(ps -> {
                            s.setOperationEmptName(ps.getUserName());
                        });
                    }
                });
                exVo.setItemsVoList(itemsVoList);
                exVo.setCurrencySymbol(itemsVoList.get(0).getCurrencySymbol());
            }
            if (ObjectUtil.equal(10, vo.getCouponType())) {
                String titleExt = StrUtil.format("Save {}{} on {}", vo.getCurrencySymbol(), vo.getCouponDiscountAmount(), vo.getTitle());
                exVo.setTitleExt(titleExt);
            } else {
                String titleExt = StrUtil.format("Save {}% on {}", vo.getCouponDiscount(), vo.getTitle());
                exVo.setTitleExt(titleExt);
            }
            return exVo;
        }
        return exVo;
    }

    /**
     * importExcel
     * 导入
     *
     * @param importVoList
     * @param tokenUser
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String importExcel(List<SomCouponImportVo> importVoList, TokenUserInfo tokenUser) throws ValidateException {
        // 校验数据
        long count = importVoList.stream().map(m -> m.getSite()).distinct().count();
        if (count > 1) {
            throw new ValidateException("您的EXCEL中出现了多个站点的活动，不允许一次性导入多站点的活动");
        }
        String errorStr = validate(importVoList);
        if (StrUtil.isNotBlank(errorStr)) {
            return errorStr;
        }
        List<String> errorList = new ArrayList<>();
        List<String> sellerSkuList = importVoList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<String> platformSkuList = importVoList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McSellerskuMapping> mappingList = skuMappingMapper.createLambdaQuery().andEq("site", importVoList.get(0).getSite()).andIn("product_display_code", sellerSkuList).select();
        List<String> skuMappingList = mappingList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        sellerSkuList.addAll(skuMappingList);
        List<String> platformSkuMappingList = mappingList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        platformSkuList.addAll(platformSkuMappingList);
        // 获取sku信息
        List<McListingInfoAmazonExVo> exVoList = listingInfoAmazonMapper.queryBySiteSellerSkus(importVoList.get(0).getSite(), sellerSkuList);
        // 获取listing信息
        List<McListingInfoAmazon> amazonList = listingInfoAmazonMapper.createLambdaQuery().andEq("site", importVoList.get(0).getSite()).andIn("seller_sku", platformSkuList).select();
        // 获取货币信息
        McPlatformProperties pl = platformPropertiesMapper.createLambdaQuery().andEq("platform", "Amazon").andEq("site", importVoList.get(0).getSite()).single();
        // 查询deal折扣
        SomDealSearchVo dealSearchVo = new SomDealSearchVo();
        dealSearchVo.setSites(Collections.singletonList(importVoList.get(0).getSite()));
        ;
        dealSearchVo.setSellerSkus(sellerSkuList);
        List<SomDealVo> dealVoList = dynamicSqlManager.getMapper(SomDealMapper.class).searchDealList(dealSearchVo);
        List<String> codeList = Arrays.asList("CouponTargetCustomer", "CouponType");
        List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
        // 组织数据
        List<SomCoupon> couponList = CollectionUtil.newArrayList();
        List<SomCouponItems> couponItemsList = CollectionUtil.newArrayList();

        List<Map<String, String>> msgList = new ArrayList<>();
        MarketActivityUtil activityUtil = new MarketActivityUtil();

        for (SomCouponImportVo f : importVoList) {
            String aid = IdUtil.fastUUID();
            List<SomCouponItems> tmpItemsList = CollectionUtil.newArrayList();
            List<String> tempList = Arrays.asList(f.getSellerSku().split(","));
            for (int i = 0; i < tempList.size(); i++) {
                SomCouponItems items = new SomCouponItems();
                // 从表
                items.setAid(IdUtil.fastUUID());
                items.setCouponId(aid);
                items.setSellerSku(tempList.get(i));
                McSellerskuMapping mapping = mappingList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getProductDisplayCode(), items.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(mapping)) {
                    items.setSellerSku(mapping.getSellerSku());
                }
                McListingInfoAmazon amazon = amazonList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), items.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(amazon)) {
                    items.setAsin(amazon.getAsinCode());
                    items.setSku(amazon.getSku());
                    items.setPrice(amazon.getPrice());
                    items.setCurrencySymbol(amazon.getCurrencyCode());
                } else {
                    items.setPrice(BigDecimal.ZERO);
                    items.setTargetPrice(BigDecimal.ZERO);
                }
                if (ObjectUtil.isNotEmpty(mapping)) {
                    items.setSellerSku(mapping.getProductDisplayCode());
                }
                McListingInfoAmazonExVo exVo = exVoList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), items.getSellerSku())).findFirst().orElse(null);
                // coupon折扣
                if (StrUtil.equalsIgnoreCase(f.getCouponTypeName(), "Percentage off")) {
                    items.setCouponDiscount(f.getDiscountRatio().multiply(BigDecimal.valueOf(100)));
                } else {
                    BigDecimal divide = (f.getCouponDiscountAmount().divide(items.getPrice(), 2, BigDecimal.ROUND_HALF_UP)).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
                    items.setCouponDiscount(divide);
                }
                // promotion折扣
                McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
                searchExVo.setSite(f.getSite());
                searchExVo.setKeyWord(items.getSellerSku());
                searchExVo.setBeginDate(f.getBeginDate());
                searchExVo.setEndDate(f.getEndDate());
                BigDecimal bigDecimal = BigDecimal.ZERO;
                try {
                    bigDecimal = amazonService.queryPromotionDiscount(searchExVo);
                } catch (ValidateException e) {
                    e.printStackTrace();
                }
                items.setPromotionDiscount(bigDecimal);
                // deal折扣
                if (CollectionUtil.isNotEmpty(dealVoList)) {
                    List<SomDealVo> dealList = dealVoList.stream().filter(t -> StrUtil.equals(f.getSellerSku(), t.getSellerSku()) && ((f.getBeginDate().compareTo(t.getPlanStartDate()) == -1)
                            || (f.getBeginDate().compareTo(t.getPlanStartDate()) == 0)) && (f.getEndDate().after(t.getPlanEndDate()))).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(dealList)) {
                        List<SomDealVo> sortList = dealList.stream().sorted(Comparator.comparing(SomDealVo::getDealDiscount).reversed()).collect(Collectors.toList());
                        items.setDealDiscount(sortList.get(0).getDealDiscount());
                    } else {
                        items.setDealDiscount(BigDecimal.ZERO);
                    }
                } else {
                    items.setDealDiscount(BigDecimal.ZERO);
                }
                // 总折扣
                items.setTotalDiscount(items.getCouponDiscount().add(items.getDealDiscount()).add(items.getPromotionDiscount()));
                // 总折扣金额
                items.setTotalDiscountAmount(items.getTotalDiscount().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).multiply(items.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                // 预估成交价
                items.setTransactionPrice(items.getPrice().subtract(items.getTotalDiscountAmount()));
                items.setBusinessGroupCode(exVo.getBusinessGroupCode());
                items.setBusinessLeaderCode(exVo.getBusinessLeaderCode());
                items.setBusinessOperationCode(exVo.getBusinessOperationCode());
                items.setSkuGrade(exVo.getSkuGrade());
                items.setTargetPrice(exVo.getTargetPrice());
                items.setSku(exVo.getSku());
                tmpItemsList.add(items);
            }
            if (StrUtil.equalsIgnoreCase("Percentage off", f.getCouponTypeName())) {
                BigDecimal couponDiscount = f.getDiscountRatio().multiply(BigDecimal.valueOf(100));
                if (BigDecimal.valueOf(5).compareTo(couponDiscount) == 1 || BigDecimal.valueOf(80).compareTo(couponDiscount) == -1) {
                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", f.getSite(), f.getSellerSku(),
                            DateUtil.formatDate(f.getBeginDate()), DateUtil.formatDate(f.getEndDate()));
                    errorList.add(error);
                }
            }
            if (StrUtil.equalsIgnoreCase("Money off", f.getCouponTypeName())) {
                for (SomCouponItems items : tmpItemsList) {
                    if (BigDecimal.valueOf(5).compareTo(items.getCouponDiscount()) == 1 || BigDecimal.valueOf(80).compareTo(items.getCouponDiscount()) == -1) {
                        String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", f.getSite(), items.getSellerSku(),
                                DateUtil.formatDate(f.getBeginDate()), DateUtil.formatDate(f.getEndDate()));
                        errorList.add(error);
                    }
                }
            }
            // 主表
            SomCoupon coupon = ConvertUtils.beanConvert(f, SomCoupon.class);
            coupon.setAid(aid);
            coupon.setPlatform("Amazon");
            coupon.setSite(f.getSite());
            coupon.setBeginDate(f.getBeginDate());
            coupon.setEndDate(f.getEndDate());
            if (ObjectUtil.isNotNull(pl) && StrUtil.isNotBlank(pl.getCurrencyCode())) {
                coupon.setCurrency(pl.getCurrencyCode());
            }
            if (f.getTitle().contains("on")) {
                String title = StrUtil.subAfter(f.getTitle(), "on", true).trim();
                coupon.setTitle(title);
            } else {
                coupon.setTitle(f.getTitle());
            }
            McDictionaryInfo couponType = infoList.stream().filter(s -> StrUtil.equalsIgnoreCase("CouponType", s.getItemTypeCode()) && StrUtil.equalsIgnoreCase(f.getCouponTypeName().trim(), s.getItemLable().trim()))
                    .findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(couponType) && StrUtil.isNotEmpty(couponType.getItemValue())) {
                coupon.setCouponType(Integer.valueOf(couponType.getItemValue().trim()));
            }
            if (ObjectUtil.isNotNull(f.getDiscountRatio())) {
                coupon.setCouponDiscount(f.getDiscountRatio().multiply(BigDecimal.valueOf(100)));
            }
            coupon.setCouponDiscountAmount(f.getCouponDiscountAmount());
            coupon.setCurrencySymbol(tmpItemsList.get(0).getCurrencySymbol());
            coupon.setBudget(f.getBudget());
            if (StrUtil.equalsIgnoreCase(f.getOnlyExchangeOne(), "Yes")) {
                coupon.setOnlyExchangeOne("1");
            } else {
                coupon.setOnlyExchangeOne("0");
            }
            McDictionaryInfo couponTargetCustomer = infoList.stream().filter(s -> StrUtil.equalsIgnoreCase("CouponTargetCustomer", s.getItemTypeCode()) && StrUtil.equalsIgnoreCase(f.getTargetCustomerName().trim(), s.getItemLable().trim()))
                    .findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(couponTargetCustomer) && StrUtil.isNotEmpty(couponTargetCustomer.getItemValue())) {
                coupon.setTargetCustomer(Integer.valueOf(couponTargetCustomer.getItemValue().trim()));
            }
            coupon.setVoucherType(f.getVoucherType());
            coupon.setBrand(f.getBrand());
            coupon.setBrandAudience(f.getBrandAudience());
            coupon.setGrossMargin(Long.valueOf(f.getGrossMargin().multiply(BigDecimal.valueOf(100)).longValue()));
            coupon.setStatus(10);
            coupon.setReason(f.getReason());
            coupon.setCreateNum(tokenUser.getJobNumber());
            coupon.setCreateTime(DateTime.now().toJdkDate());
            // 计算库存可售天数
            /*List<McStockInfoExtVo> stockInfoList = stockInfoMapper.queryBySitesAndSkus(Collections.singletonList(f.getSite()), couponItemsList.stream().map(m -> m.getSku()).collect(Collectors.toList()));
            for(SomCouponItems items :couponItemsList){
                List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(s ->StrUtil.equalsIgnoreCase(f.getSite(), s.getSite()) &&
                        StrUtil.equalsIgnoreCase(items.getSku(), s.getProductMainCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(stockInfos)){
                    long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                    BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if(BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0){
                        items.setStockSaleDays(0);
                    }else {
                        // 库存可销售天数 库存/7天发货平均值 只取整数
                        int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber,0,BigDecimal.ROUND_DOWN).intValue();
                        items.setStockSaleDays(stockSaleDays);
                    }
                }else {
                    items.setStockSaleDays(0);
                }
            }*/
            List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (SomCouponItems items : tmpItemsList) {
                MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
                activity.setSite(f.getSite());
                activity.setSellerSku(items.getSellerSku());
                activity.setStartDate(sdf.format(f.getBeginDate()));
                activity.setEndDate(sdf.format(f.getEndDate()));
                activity.setType("Coupon");
                body.add(activity);
            }
            MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, true);
            if (!daysMsgData.isSuccess()) {
                for (SomCouponItems item : tmpItemsList) {
                    String tmpMsg = "*标题：%s*\n*站点：%s*\n*展示码：%s*\n**失败原因**：%s";
                    String errorMsg = daysMsgData.getErrorMap().get(f.getSellerSku());
                    errorMsg = errorMsg.replace("%%", "%");
                    tmpMsg = String.format(tmpMsg, f.getTitle(),f.getSite(), f.getSellerSku(), errorMsg);
                    Map<String, String> msg = new HashMap<>();
                    msg.put("tag", "markdown");
                    msg.put("content",tmpMsg);
                    msgList.add(msg);
                }
                continue;
            } else {
                //设置可售库存天数
                for (SomCouponItems items : tmpItemsList) {
                    items.setStockSaleDays((Integer)daysMsgData.getDaysMap().get(items.getSellerSku()));
                }
                couponItemsList.addAll(tmpItemsList);
                couponList.add(coupon);
            }
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            String error = errorList.stream().collect(Collectors.joining("\n")) +
                    "\n折扣比例必须在5%%~80%%之间";
            return error;
        }
        if (!msgList.isEmpty()) {
            String msgBody = "{\"header\":{\"template\":\"red\",\"title\":{\"tag\":\"plain_text\",\"content\":\"%s\"}},\"elements\":[%s]}";
            String msgs = msgList.stream().map(x -> JSONUtil.toJsonStr(x)).collect(Collectors.joining(","));
            msgBody = String.format(msgBody, "Coupon库存可售天数异常提醒", msgs);
            try {
                FeiShuUtils.sendNotice(tokenUser.getJobNumber(), msgBody, MessageType.INTERACTIVE);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (!couponList.isEmpty()) {
            somCouponMapper.insertBatch(couponList);
        }
        if (CollectionUtil.isNotEmpty(couponItemsList)) {
            somCouponItemsMapper.insertBatch(couponItemsList);
        }
        return Strings.EMPTY;
    }

    /**
     * validate
     * 导入校验
     *
     * @param importVoList
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    private String validate(List<SomCouponImportVo> importVoList) {
        String errorStr = Strings.EMPTY;
        List<String> errorList = new ArrayList<>();
        List<String> error1List = new ArrayList<>();
        List<String> error2List = new ArrayList<>();
        List<String> error3List = new ArrayList<>();
        List<String> error4List = new ArrayList<>();
        List<String> error5List = new ArrayList<>();
        List<String> error6List = new ArrayList<>();
        List<String> sellerSkus = CollectionUtil.newArrayList();
        ZoneId zoneId = ZoneId.systemDefault();
        Date date = Date.from(LocalDate.now().atStartOfDay().atZone(zoneId).toInstant());
        for (SomCouponImportVo importVo : importVoList) {
            if (StrUtil.isBlank(importVo.getSite()) || StrUtil.isBlank(importVo.getSellerSku()) || ObjectUtil.isNull(importVo.getBeginDate()) || ObjectUtil.isNull(importVo.getEndDate())
                    || StrUtil.isBlank(importVo.getTitle()) || StrUtil.isBlank(importVo.getOnlyExchangeOne()) | StrUtil.isBlank(importVo.getCouponTypeName()) || ObjectUtil.isNull(importVo.getBudget())
                    || StrUtil.isBlank(importVo.getTargetCustomerName()) || ObjectUtil.isNull(importVo.getExpectedActivityExpense()) || ObjectUtil.isNull(importVo.getGrossMargin()) || StrUtil.isBlank(importVo.getReason())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()));
                errorList.add(error);
            }
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            errorStr = errorList.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动的「站点」、「展示码」、「活动开始时间」、「活动截止时间」、「标题」、「每位顾客只能兑换一份」、「折扣类型」、「预算金额」、「目标客户」、「预计活动费用」、「提报毛利率」、「提报原因」列不允许为空";
            return errorStr;
        }
        for (SomCouponImportVo importVo : importVoList) {
            if (importVo.getTargetCustomerName() != null && importVo.getTargetCustomerName().equals("Brand")) {
                // 目标客户为50时，必须填写品牌 和品牌受众
                if (importVo.getBrand() == null || importVo.getBrandAudience() == null) {
                    String error = StrUtil.format("站点:{},展示码:{},目标客户为Brand时，品牌和品牌受众不能为空", importVo.getSite(), importVo.getSellerSku());
                    error1List.add(error);
                }
            }
            if (StrUtil.equalsIgnoreCase(importVo.getCouponTypeName(), "Percentage off") && ObjectUtil.isNull(importVo.getDiscountRatio())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()), "「折扣比例」不允许为空");
                error1List.add(error);
            }
            if (StrUtil.equalsIgnoreCase(importVo.getCouponTypeName(), "Money off") && ObjectUtil.isNull(importVo.getCouponDiscountAmount())) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()), "「折扣金额」不允许为空");
                error1List.add(error);
            }
            if (importVo.getBeginDate().before(date)) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()));
                error2List.add(error);
            }
            if (importVo.getEndDate().before(date) || importVo.getBeginDate().after((importVo.getEndDate()))) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()));
                error3List.add(error);
            }
            long l = importVo.getEndDate().getTime() - importVo.getBeginDate().getTime();
            int i = 1000 * 24 * 60 * 60;
            long days = l / i;
            if (days < 1 || days > 90) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()));
                error4List.add(error);
            }
            SomCouponExVo exVo = new SomCouponExVo();
            exVo.setSite(importVo.getSite());
            exVo.setBeginDate(importVo.getBeginDate());
            exVo.setEndDate(importVo.getEndDate());
            List<SomCouponItemsVo> itemsVoList = somCouponItemsMapper.checkUnique(exVo);
            boolean match = itemsVoList.stream().anyMatch(pc -> StrUtil.equals(importVo.getSellerSku(), pc.getSellerSku()));
            if (match) {
                String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", importVo.getSite(), importVo.getSellerSku(),
                        DateUtil.formatDate(importVo.getBeginDate()), DateUtil.formatDate(importVo.getEndDate()));
                error5List.add(error);
            }
            List<String> tempList = Arrays.asList(importVo.getSellerSku().split(","));
            sellerSkus.addAll(tempList);
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            errorStr = errorList.stream().collect(Collectors.joining("\n"));
            return errorStr;
        }
        if (CollectionUtil.isNotEmpty(error1List)) {
            errorStr = error1List.stream().collect(Collectors.joining("\n"));
            return errorStr;
        }
        if (CollectionUtil.isNotEmpty(error2List)) {
            errorStr = error2List.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动的「活动开始时间」必须大于当前时间";
            return errorStr;
        }
        if (CollectionUtil.isNotEmpty(error3List)) {
            errorStr = error3List.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动的「活动截止时间」必须大于「活动开始时间」 并且必须大于「活动开始时间」";
            return errorStr;
        }
        if (CollectionUtil.isNotEmpty(error4List)) {
            errorStr = error4List.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动的「活动截止始时间」减去「活动开始时间」必须≥1天并且≤90天";
            return errorStr;
        }
        if (CollectionUtil.isNotEmpty(error5List)) {
            errorStr = error5List.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动活动起止日期内已经存在，不允许重复提报";
            return errorStr;
        }
        List<McSellerskuMapping> mappingList = skuMappingMapper.createLambdaQuery().andEq("site", importVoList.get(0).getSite()).andIn("seller_sku", sellerSkus).select();
        List<String> skuMappingList = mappingList.stream().map(m -> m.getProductDisplayCode()).distinct().collect(Collectors.toList());
        sellerSkus.addAll(skuMappingList);
        List<McListingInfoAmazonExVo> exVoList = listingInfoAmazonMapper.queryBySiteSellerSkus(importVoList.get(0).getSite(), sellerSkus);
        // 获取listing信息
        List<String> platformSkuList = importVoList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McListingInfoAmazon> amazonList = listingInfoAmazonMapper.createLambdaQuery().andEq("site", importVoList.get(0).getSite()).andIn("seller_sku", platformSkuList).select();
        importVoList.forEach(f -> {
            List<String> tempList = Arrays.asList(f.getSellerSku().split(","));
            for (int i = 0; i < tempList.size(); i++) {
                SomCouponItems items = new SomCouponItems();
                items.setSellerSku(tempList.get(i));
                McSellerskuMapping mapping = mappingList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), items.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(mapping)) {
                    items.setSellerSku(mapping.getSellerSku());
                }
                McListingInfoAmazon amazon = amazonList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), items.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(amazon)) {
                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", f.getSite(), f.getSellerSku(),
                            DateUtil.formatDate(f.getBeginDate()), DateUtil.formatDate(f.getEndDate()));
                    error6List.add(error);
                }
                if (ObjectUtil.isNotEmpty(mapping)) {
                    items.setSellerSku(mapping.getProductDisplayCode());
                }
                McListingInfoAmazonExVo exVo = exVoList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getSellerSku(), items.getSellerSku())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(exVo)) {
                    String error = StrUtil.format("站点:{},展示码:{},活动起止日期:{}~{}", f.getSite(), f.getSellerSku(),
                            DateUtil.formatDate(f.getBeginDate()), DateUtil.formatDate(f.getEndDate()));
                    error6List.add(error);
                } else {
                    items.setPrice(exVo.getPrice());
                }
            }
        });
        if (CollectionUtil.isNotEmpty(error6List)) {
            errorStr = error6List.stream().collect(Collectors.joining("\n")) +
                    "\n这批活动展示码在系统中不存在";
            return errorStr;
        }
        return errorStr;
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String exportExcel(SomCouponPageSearchVo exportVo) throws ValidateException {
        if (ObjectUtil.isEmpty(exportVo) || CollectionUtil.isEmpty(exportVo.getSiteList())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<SomCouponExportVo> importVoList = somCouponMapper.queryExportData(exportVo);
        if (CollectionUtil.isNotEmpty(importVoList)) {
            List<String> codeList = Arrays.asList("CouponTargetCustomer", "CouponType");
            List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
            List<String> aidList = importVoList.stream().map(m -> m.getAid()).collect(Collectors.toList());
            List<SomCouponItems> itemsList = somCouponItemsMapper.createLambdaQuery().andIn("coupon_id", aidList).select();
            importVoList.forEach(f -> {
                List<SomCouponItems> list = itemsList.stream().filter(s -> StrUtil.equalsIgnoreCase(s.getCouponId(), f.getAid())).collect(Collectors.toList());
                String asins = list.stream().map(m -> m.getAsin()).collect(Collectors.joining(","));
                f.setAsin(asins);
                // 每位顾客只能兑换一份
                f.setOnlyExchangeOne(StrUtil.equalsAnyIgnoreCase(f.getOnlyExchangeOne(), "1") ? "Yes" : "No");
                // 折扣类型
                f.setCouponTypeName(f.getCouponType() == 10 ? f.getCurrencySymbol() + " Off" : "% Off");
                // 目标客户
                McDictionaryInfo couponTargetCustomer = infoList.stream().filter(s -> StrUtil.equalsIgnoreCase("CouponTargetCustomer", s.getItemTypeCode()) && StrUtil.equalsIgnoreCase(f.getTargetCustomer().toString(), s.getItemValue().trim()))
                        .findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(couponTargetCustomer) && StrUtil.isNotEmpty(couponTargetCustomer.getItemLable())) {
                    f.setTargetCustomerName(couponTargetCustomer.getItemLable());
                }
            });
            TemplateExportParams params = new TemplateExportParams("static/excel/CouponExportTemplate.xlsx", true);
            SomCouponExportVo vo = importVoList.stream().filter(f -> StrUtil.isNotEmpty(f.getSite()) && StrUtil.isNotEmpty(f.getCurrencySymbol())).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(vo)) {
                throw new ValidateException("当前站点货币符号为空,无法动态生成列");
            }
            String currencySymbol = vo.getCurrencySymbol();
            if (StrUtil.equalsIgnoreCase("Amazon.ca", exportVo.getSite())) {
                currencySymbol = currencySymbol.substring(0, 1) + currencySymbol.substring(3, 4);
            }
            try {
                Map<String, Object> map = new HashMap<>();
                List<Map<String, Object>> mapList = new ArrayList<>();
                for (SomCouponExportVo temp : importVoList) {
                    Map<String, Object> tempmap = new HashMap<>();
                    tempmap.put("asin", temp.getAsin());
                    tempmap.put("couponTypeName", temp.getCouponTypeName());
                    if (ObjectUtil.isNotEmpty(temp.getCouponDiscount())) {
                        tempmap.put("discountRatio", temp.getCouponDiscount() + "%");
                    }
                    if (ObjectUtil.isNotEmpty(temp.getCouponDiscountAmount())) {
                        tempmap.put("couponDiscountAmount", currencySymbol + temp.getCouponDiscountAmount());
                    }
                    tempmap.put("title", temp.getTitle());
                    if (!StrUtil.equalsIgnoreCase("Amazon.ca", exportVo.getSite())) {
                        tempmap.put("budget", currencySymbol + temp.getBudget());
                    } else {
                        tempmap.put("budget", temp.getBudget());
                    }
                    tempmap.put("beginDate", DateUtil.formatDate(temp.getBeginDate()));
                    tempmap.put("endDate", DateUtil.formatDate(temp.getEndDate()));
                    tempmap.put("onlyExchangeOne", temp.getOnlyExchangeOne());
                    tempmap.put("targetCustomerName", temp.getTargetCustomerName());
                    mapList.add(tempmap);
                }
                map.put("mapList", mapList);
                Workbook workbook = ExcelExportUtil.exportExcel(params, map);
                // 获取sheet页
                Sheet sheetAt = workbook.getSheetAt(0);
                // 根据行号获取对应的行
                Row titleRow = sheetAt.getRow(6);
                // 根据索引获取对应的列
                Cell couponTypeCell = titleRow.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                Cell amountCell = titleRow.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                couponTypeCell.setCellValue("Discount type (" + currencySymbol + " off or % off)");
                amountCell.setCellValue("Voucher discount '" + currencySymbol + " Off' value");
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出失败" + e.getStackTrace());
            }
        }
        return null;
    }

    /**
     * dynamicColumn
     * 动态生成列
     *
     * @param exportVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    private void dynamicColumn(SomCouponExportVo exportVo) throws ValidateException {
        try {
            Class clazz = SomCouponExportVo.class;
            Field[] fields = clazz.getDeclaredFields();
            List<Field> list = Arrays.stream(fields).filter(f -> StrUtil.equalsIgnoreCase(f.getName(), "couponTypeName") || StrUtil.equalsIgnoreCase(f.getName(), "couponDiscountAmount"))
                    .collect(Collectors.toList());
            for (Field field : list) {
                Excel excel = field.getAnnotation(Excel.class);
                InvocationHandler excelHandler = Proxy.getInvocationHandler(excel);
                Field excelFiled = excelHandler.getClass().getDeclaredField("memberValues");
                excelFiled.setAccessible(true);
                Map excelValues = (Map) excelFiled.get(excelHandler);
                String currencySymbol = exportVo.getCurrencySymbol();
                if (StrUtil.equalsIgnoreCase("Amazon.ca", exportVo.getSite())) {
                    currencySymbol = currencySymbol.substring(0, 3);
                }
                if (StrUtil.equals("couponTypeName", field.getName())) {
                    excelValues.put("name", "Discount type (" + currencySymbol + " off or % off)");
                } else {
                    excelValues.put("name", "Voucher discount " + currencySymbol + " Off");
                }
            }
        } catch (Exception e) {
            throw new ValidateException("动态生成列名出错" + e.getMessage());
        }
    }

    /**
     * edit
     * 修改
     *
     * @param editVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void edit(SomCouponExVo editVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(editVo) || StrUtil.isEmpty(editVo.getAid())) {
            throw new ValidateException("请选择要编辑的数据");
        }
        validate(editVo);
        SomCoupon temp = somCouponMapper.createLambdaQuery().andEq("aid", editVo.getAid()).single();
        if (ObjectUtil.isNull(temp) || temp.getStatus() != 10) {
            throw new ValidateException("当前数据不允许编辑");
        }
        SomCoupon coupon = ConvertUtils.beanConvert(temp, SomCoupon.class);
        coupon.setPlatform(editVo.getPlatform());
        coupon.setSite(editVo.getSite());
        coupon.setBeginDate(editVo.getBeginDate());
        coupon.setEndDate(editVo.getEndDate());
        coupon.setCouponType(editVo.getCouponType());
        coupon.setCouponDiscount(editVo.getCouponDiscount());
        coupon.setCouponDiscountAmount(editVo.getCouponDiscountAmount());
        coupon.setOnlyExchangeOne(editVo.getOnlyExchangeOne());
        coupon.setBudget(editVo.getBudget());
        coupon.setTitle(editVo.getTitle());
        coupon.setTargetCustomer(editVo.getTargetCustomer());
        coupon.setGrossMargin(editVo.getGrossMargin());
        coupon.setReason(editVo.getReason());
        coupon.setVoucherType(editVo.getVoucherType());
        coupon.setBrand(editVo.getBrand());
        coupon.setBrandAudience(editVo.getBrandAudience());
        coupon.setExpectedActivityExpense(editVo.getExpectedActivityExpense());
        // 获取币种
        McPlatformProperties properties = platformPropertiesMapper.createLambdaQuery().andEq("platform", coupon.getPlatform()).andEq("site", coupon.getSite()).single();
        if (ObjectUtil.isNotNull(properties) && StrUtil.isNotBlank(properties.getCurrencyCode())) {
            coupon.setCurrency(properties.getCurrencyCode());
        }
        // 组织从表数据
        List<SomCouponItemsVo> itemsVoList = somCouponItemsMapper.checkUnique(editVo);
        for (SomCouponItemsVo f : editVo.getItemsVoList()) {
            // 唯一性校验
            boolean match = itemsVoList.stream().anyMatch(pc -> StrUtil.equals(f.getSellerSku(), pc.getSellerSku()));
            if (match) {
                throw new ValidateException("活动起止日期内存在该产品的Coupon活动，不允许重复提报");
            }
            f.setAid(IdUtil.fastUUID());
            f.setCouponId(editVo.getAid());
        }
        somCouponMapper.createLambdaQuery().andEq("aid", editVo.getAid()).update(coupon);
        somCouponItemsMapper.createLambdaQuery().andEq("coupon_id", editVo.getAid()).delete();
        if (CollectionUtil.isNotEmpty(editVo.getItemsVoList())) {
            // 计算库存可售天数

            List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (SomCouponItemsVo items : editVo.getItemsVoList()) {
                MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
                activity.setSite(coupon.getSite());
                activity.setSellerSku(items.getSellerSku());
                activity.setStartDate(sdf.format(coupon.getBeginDate()));
                activity.setEndDate(sdf.format(coupon.getEndDate()));
                activity.setType("Coupon");
                body.add(activity);
            }
            MarketActivityUtil activityUtil = new MarketActivityUtil();
            MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getStockSaleDaysFromBi(body, true);
            if (!daysMsgData.isSuccess()) {
                String errorMsg = daysMsgData.getErrorMap().values().stream().collect(Collectors.joining(","));
                throw new ValidateException(errorMsg);
            } else {
                for (SomCouponItemsVo items : editVo.getItemsVoList()) {
                    items.setStockSaleDays((Integer)daysMsgData.getDaysMap().get(items.getSellerSku()));
                }
            }
            /*List<McStockInfoExtVo> stockInfoList = stockInfoMapper.queryBySitesAndSkus(Collections.singletonList(editVo.getSite()), editVo.getItemsVoList().stream().map(m -> m.getSku()).collect(Collectors.toList()));
            for (SomCouponItemsVo items : editVo.getItemsVoList()) {
                List<McStockInfoExtVo> stockInfos = stockInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(editVo.getSite(), f.getSite()) &&
                        StrUtil.equalsIgnoreCase(items.getSku(), f.getProductMainCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(stockInfos)) {
                    long totalStock = stockInfos.stream().mapToInt(m -> m.getTotalStock()).sum();
                    BigDecimal sevenDayNumber = stockInfos.stream().map(m -> m.getSevenDayNumber()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.valueOf(0).compareTo(sevenDayNumber) == 0) {
                        items.setStockSaleDays(0);
                    } else {
                        // 库存可销售天数 库存/7天发货平均值 只取整数
                        int stockSaleDays = BigDecimal.valueOf(totalStock).divide(sevenDayNumber, 0, BigDecimal.ROUND_DOWN).intValue();
                        items.setStockSaleDays(stockSaleDays);
                    }
                } else {
                    items.setStockSaleDays(0);
                }
            }*/
            somCouponItemsMapper.insertBatch(ConvertUtils.listConvert(editVo.getItemsVoList(), SomCouponItems.class));
        }
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomCouponVo deleteVo) throws ValidateException {
        if (ObjectUtil.isNull(deleteVo) || StrUtil.isEmpty(deleteVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        SomCoupon coupon = somCouponMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).single();
        if (ObjectUtil.isNotNull(coupon) && coupon.getStatus() != 10) {
            throw new ValidateException("当前数据不允许删除");
        }
        somCouponMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).delete();
    }

    /**
     * validate
     * 新增/编辑校验
     *
     * @param addVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    private void validate(SomCouponExVo addVo) throws ValidateException {
        // 非空校验
        if (ObjectUtil.isEmpty(addVo) || CollectionUtil.isEmpty(addVo.getItemsVoList())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (ObjectUtil.isEmpty(addVo.getBeginDate()) || ObjectUtil.isEmpty(addVo.getEndDate())) {
            throw new ValidateException("活动起止日期不允许为空");
        }
        if (addVo.getTargetCustomer() != null && addVo.getTargetCustomer() == 50) {
            // 目标客户为50时，必须填写品牌 和品牌受众
            if (addVo.getBrand() == null || addVo.getBrandAudience() == null) {
                throw new ValidateException("目标客户为Brand时，品牌和品牌受众不能为空");
            }
        }
        ZoneId zoneId = ZoneId.systemDefault();
        Date date = Date.from(LocalDate.now().atStartOfDay().atZone(zoneId).toInstant());
        if (addVo.getBeginDate().before(date)) {
            throw new ValidateException("开始日期不允许选择当天之前的日期");
        }
        if (addVo.getBeginDate().after((addVo.getEndDate()))) {
            throw new ValidateException("截止日期不允许在开始日期前");
        }
        if (10 == addVo.getCouponType()) {
            if (ObjectUtil.isNull(addVo.getCouponDiscountAmount())) {
                throw new ValidateException("折扣额不允许为空");
            }
            for (SomCouponItemsVo itemsVo : addVo.getItemsVoList()) {
                if (ObjectUtil.isEmpty(itemsVo.getCouponDiscount())) {
                    throw new ValidateException("折扣比例不能为空");
                }
                if (BigDecimal.valueOf(5).compareTo(itemsVo.getCouponDiscount()) == 1 || BigDecimal.valueOf(80).compareTo(itemsVo.getCouponDiscount()) == -1) {
                    throw new ValidateException("折扣比例必须在5%~80%之间");
                }
            }
        }
        if (20 == addVo.getCouponType()) {
            if (ObjectUtil.isNull(addVo.getCouponDiscount())) {
                throw new ValidateException("折扣不允许为空");
            }
            if (BigDecimal.valueOf(5).compareTo(addVo.getCouponDiscount()) == 1 || BigDecimal.valueOf(80).compareTo(addVo.getCouponDiscount()) == -1) {
                throw new ValidateException("折扣比例必须在5%~80%之间");
            }
        }
        if (ObjectUtil.isNull(addVo.getBudget())) {
            throw new ValidateException("预算金额不允许为空");
        }
        if (ObjectUtil.isNull(addVo.getGrossMargin())) {
            throw new ValidateException("提报毛利率不允许为空");
        }
        if (StrUtil.isBlank(addVo.getReason())) {
            throw new ValidateException("提报原因不允许为空");
        }
        if (StrUtil.isBlank(addVo.getSite())) {
            throw new ValidateException("站点不允许为空");
        }
        if (CollectionUtil.isEmpty(addVo.getItemsVoList().stream().map(m -> m.getSku()).collect(Collectors.toList()))) {
            throw new ValidateException("SKU不允许为空");
        }
        if (ObjectUtil.isEmpty(addVo.getExpectedActivityExpense())) {
            throw new ValidateException("预计活动费用不允许为空");
        }
    }

    /**
     * submit
     * 提报&批量提交
     *
     * @param submitVo
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public String submit(SomCouponExVo submitVo, TokenUserInfo tokenUser) throws ValidateException {
        StringBuilder result = new StringBuilder();
        if (ObjectUtil.isNull(submitVo) || CollectionUtil.isEmpty(submitVo.getAidList())) {
            throw new ValidateException("请选择要提报的数据");
        }
        List<ZBPMCouponSubmitVo> detailList = somCouponMapper.queryByAids(submitVo.getAidList());
        List<String> sellerSkuList = detailList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<SomCouponImportVo> couponVoList = somCouponMapper.queryBySiteAndSellerSkus(detailList.get(0).getSite(), sellerSkuList);
        detailList.forEach(f -> {
            if (CollectionUtil.isNotEmpty(couponVoList)) {
                boolean match1 = couponVoList.stream().anyMatch(c -> StrUtil.equalsIgnoreCase(f.getSellerSku(), c.getSellerSku())
                        && (((f.getBeginDate().before(c.getBeginDate()) || f.getBeginDate() == c.getBeginDate()) && (f.getEndDate().after(c.getEndDate()) || f.getEndDate() == c.getEndDate()))));
                boolean match2 = couponVoList.stream().anyMatch(c -> StrUtil.equalsIgnoreCase(f.getSellerSku(), c.getSellerSku())
                        && ((f.getBeginDate().before(c.getBeginDateReal()) || f.getBeginDate() == c.getBeginDateReal()) && (f.getEndDate().after(c.getEndDateReal()) || f.getEndDate() == c.getEndDateReal())));
                if (match1 || match2) {
                    result.append(f.getSellerSku()).append("\n");
                }
            }
        });
        if (!result.toString().isEmpty()) {
            result.append("活动起止日期内存在该产品的Coupon活动，不允许重复提报");
            return result.toString();
        }
        List<SomCoupon> couponList = new ArrayList<>();
        List<SomCouponExVo> submitList = new ArrayList<>();
        // 部门
        List<SysDeptNeweya> deptList = deptMapper.createLambdaQuery().andEq("is_enabled", 1).select();
        // 查询产品销售视图
        List<String> sellerSkus = detailList.stream().map(m -> m.getSellerSku()).distinct().collect(Collectors.toList());
        List<McProductSales> productSalesList = productSalesMapper.createLambdaQuery().andEq("site", detailList.get(0).getSite()).andIn("display_product_code", sellerSkus).select();
        Map<String, List<ZBPMCouponSubmitVo>> map = detailList.stream().collect(Collectors.groupingBy(p -> p.getAid()));
        for (String key : map.keySet()) {
            ZBPMCouponSubmitVo vo = detailList.stream().filter(t -> StrUtil.equals(key, t.getAid())).findFirst().orElse(null);
            SomCouponExVo exVo = ConvertUtils.beanConvert(vo, SomCouponExVo.class);
            List<ZBPMCouponSubmitVo> voList = map.get(key);
            SomCoupon coupon = new SomCoupon();
            coupon.setAid(key);
            coupon.setSubmitTime(DateTime.now().toJdkDate());
            BigDecimal totalDiscountAmountMax = voList.stream().sorted(Comparator.comparing(ZBPMCouponSubmitVo::getTotalDiscountAmount).reversed()).collect(Collectors.toList()).get(0).getTotalDiscountAmount();
            BigDecimal totalDiscountMax = voList.stream().sorted(Comparator.comparing(ZBPMCouponSubmitVo::getTotalDiscount).reversed()).collect(Collectors.toList()).get(0).getTotalDiscount();
            voList = voList.stream().sorted(Comparator.comparing(ZBPMCouponSubmitVo::getTotalDiscount).reversed()).collect(Collectors.toList());
            if (BigDecimal.valueOf(3).compareTo(totalDiscountAmountMax) == 0 || BigDecimal.valueOf(3).compareTo(totalDiscountAmountMax) == 1) {
                coupon.setStatus(90);
            } else if (BigDecimal.valueOf(15).compareTo(totalDiscountMax) == 0 || BigDecimal.valueOf(15).compareTo(totalDiscountMax) == 1) {
                coupon.setStatus(90);
            } else {
                coupon.setStatus(20);
                exVo.setItemsVoList(ConvertUtils.listConvert(voList, SomCouponItemsVo.class));
                for (SomCouponItemsVo itemsVo : exVo.getItemsVoList()) {
                    McProductSales productSales = productSalesList.stream().filter(p -> StrUtil.equalsIgnoreCase(detailList.get(0).getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(itemsVo.getSellerSku(), p.getDisplayProductCode())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(productSales)) {
                        deptList.stream().filter(d -> StrUtil.equalsIgnoreCase(productSales.getSalesGroupCode(), d.getDeptCode())).findFirst().ifPresent(ps -> {
                            itemsVo.setSalesGroupName(ps.getDeptNameCn());
                        });
                    }
                }
                submitList.add(exVo);
            }
            couponList.add(coupon);
        }
        somCouponMapper.batchSubmit(couponList);
        if (CollectionUtil.isNotEmpty(submitList)) {
            couponList = couponList.stream().filter(f -> ObjectUtil.equal(20, f.getStatus())).collect(Collectors.toList());
            // 查询流程信息
            ProcessDefinitionVo vo = new ProcessDefinitionVo();
            vo.setKey("AmazonCoupons");
            try {
//                ResultVo<ZBPMProcessVo> queryResultVo = zbpmService.getProcessByProcessKey(vo);
//                if (!queryResultVo.isSuccess()) {
//                    couponList.forEach(f -> {
//                        f.setStatus(10);
//                        f.setSubmitTime(null);
//                    });
//                    somCouponMapper.batchSubmit(couponList);
//                    throw new ValidateException("根据流程关键字(AmazonCoupons)获取流程信息出错{}", queryResultVo.getMessage());
//                }
//                if (ObjectUtil.isEmpty(queryResultVo.getData())) {
//                    couponList.forEach(f -> {
//                        f.setStatus(10);
//                        f.setSubmitTime(null);
//                    });
//                    somCouponMapper.batchSubmit(couponList);
//                    throw new ValidateException("根据流程关键字(AmazonCoupons)获取流程信息为空");
//                }
//                // 发起流程
//                ProcessVo processVo = new ProcessVo();
//                processVo.setFlowKey("AmazonCoupons");
//                processVo.setData(JSONUtil.toJsonStr(submitList));
//                processVo.setVersion(queryResultVo.getData().getVersion());
//                processVo.setSubject(queryResultVo.getData().getProcessName());
//                ResultVo<String> postResultVo = zbpmService.startProcess(processVo);
                McDictionaryInfo couponInfo = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "OATemplateId")
                        .andEq("item_lable", "Coupon")
                        .single();
                String formValue = beanToOaJson(submitList);
                MultiValueMap<String, Object> wholeForm = new LinkedMultiValueMap<>();
                //文档标题
                wholeForm.add("docSubject", "亚马逊Coupons活动申请");
                //流程发起人
                wholeForm.add("docCreator", String.format("{\"LoginName\": \"%s\"}", tokenUser.getJobNumber()));
                //文档状态，可以为草稿（"10"）或者待审（"20"）两种状态，默认为待审
                wholeForm.add("docStatus", "20");
                //文档模板id，不允许为空
                wholeForm.add("fdTemplateId", couponInfo.getItemValue());
                String formValues = null;
                //流程表单数据，允许为空
                wholeForm.add("formValues", formValue);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(wholeForm, headers);
                //有返回值的情况 VO可以替换成具体的JavaBean
                ResponseEntity<String> obj = null;
                try {
                    log.info("OA新增流程传参,url:{},param:{}", oaUrl, JSONUtil.toJsonStr(entity));
                    obj = restTemplate.exchange(oaUrl + "/api/km-review/kmReviewRestService/addReview", HttpMethod.POST, entity, String.class);
                } catch (RestClientException e) {
                    log.error("调用OA失败：" + e.getMessage());
                }
                String body = obj.getBody();
                log.info("返回信息：", body);
                if (obj.getStatusCode() != HttpStatus.OK) {
                    couponList.forEach(f -> {
                        f.setStatus(10);
                        f.setSubmitTime(null);
                    });
                    somCouponMapper.batchSubmit(couponList);
                    throw new ValidateException((body));
                }
            } catch (Exception e) {
                String msg = ObjectUtil.isNull(e.getMessage()) ? (ObjectUtil.isNull(e.getCause()) ? e.toString() : e.getCause().getMessage()) : e.getMessage();
                couponList.forEach(f -> {
                    f.setStatus(10);
                    f.setSubmitTime(null);
                });
                somCouponMapper.batchSubmit(couponList);
                throw new ValidateException(msg);
            }
        }
        return result.toString();
    }

    private String beanToOaJson(List<SomCouponExVo> submitList) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建要生成的JSON对象
        ObjectNode fdListObject = objectMapper.createObjectNode();
        ArrayNode idArray = objectMapper.createArrayNode();
        ArrayNode platformArray = objectMapper.createArrayNode();
        ArrayNode siteArray = objectMapper.createArrayNode();
        ArrayNode skuArray = objectMapper.createArrayNode();
        ArrayNode sellerSkuArray = objectMapper.createArrayNode();
        ArrayNode asinArray = objectMapper.createArrayNode();
        ArrayNode priceArray = objectMapper.createArrayNode();
        ArrayNode couponDiscountArray = objectMapper.createArrayNode();
        ArrayNode promotionDiscountArray = objectMapper.createArrayNode();
        ArrayNode dealDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalDiscountArray = objectMapper.createArrayNode();
        ArrayNode totalAmountArray = objectMapper.createArrayNode();
        ArrayNode transactionPriceArray = objectMapper.createArrayNode();
        ArrayNode salesGroupNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptNameArray = objectMapper.createArrayNode();
        ArrayNode salesGroupEmptCodeArray = objectMapper.createArrayNode();
        ArrayNode operationEmptNameArray = objectMapper.createArrayNode();
        ArrayNode operationEmpCodeArray = objectMapper.createArrayNode();
        ArrayNode applyReasonNameArray = objectMapper.createArrayNode();
        ArrayNode startTimeArray = objectMapper.createArrayNode();
        ArrayNode endTimeArray = objectMapper.createArrayNode();
        ArrayNode grossMarinArray = objectMapper.createArrayNode();
        ArrayNode stockSaleDaysArray = objectMapper.createArrayNode();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (SomCouponExVo vo : submitList) {
            for (int i = 0; i < vo.getItemsVoList().size(); i++) {
                if (i == 0) {
                    idArray.add(nullToString(vo.getAid()));
                    platformArray.add(nullToString(vo.getPlatform()));
                    siteArray.add(nullToString(vo.getSite()));
                    grossMarinArray.add(nullToString(vo.getGrossMargin()));
                    applyReasonNameArray.add(nullToString(vo.getReason()));
                    startTimeArray.add(sf.format(vo.getBeginDate()));
                    endTimeArray.add(sf.format(vo.getEndDate()));
                } else {
                    idArray.add("");
                    platformArray.add("");
                    siteArray.add("");
                    grossMarinArray.add("");
                    applyReasonNameArray.add("");
                    startTimeArray.add("");
                    endTimeArray.add("");
                }
                SomCouponItemsVo item = vo.getItemsVoList().get(i);
                skuArray.add(nullToString(item.getSku()));
                sellerSkuArray.add(nullToString(item.getSellerSku()));
                asinArray.add(nullToString(item.getAsin()));
                priceArray.add(ObjectUtil.isNull(item.getPrice()) ? "" : item.getCurrencySymbol() + item.getPrice());
                stockSaleDaysArray.add(nullToString(item.getStockSaleDays()));
                couponDiscountArray.add(nullToString(item.getCouponDiscount()));
                promotionDiscountArray.add(nullToString(item.getPromotionDiscount()));
                dealDiscountArray.add(nullToString(item.getDealDiscount()));
                totalDiscountArray.add(nullToString(item.getTotalDiscount()));
                totalAmountArray.add(ObjectUtil.isNull(item.getTotalDiscountAmount()) ? "" : item.getCurrencySymbol() + item.getTotalDiscountAmount());
                transactionPriceArray.add(ObjectUtil.isNull(item.getTransactionPrice()) ? "" : item.getCurrencySymbol() + item.getTransactionPrice());
                salesGroupNameArray.add(nullToString(item.getSalesGroupName()));
                salesGroupEmptNameArray.add(nullToString(item.getSalesGroupEmptName()));
                salesGroupEmptCodeArray.add(nullToString(item.getBusinessLeaderCode()));
                operationEmptNameArray.add(nullToString(item.getOperationEmptName()));
                operationEmpCodeArray.add(nullToString(item.getBusinessOperationCode()));
            }
        }
        fdListObject.set("fd_list.fd_id", idArray);
        fdListObject.set("fd_list.fd_platform", platformArray);
        fdListObject.set("fd_list.fd_site", siteArray);
        fdListObject.set("fd_list.fd_sku", skuArray);
        fdListObject.set("fd_list.fd_sellerSku", sellerSkuArray);
        fdListObject.set("fd_list.fd_asin", asinArray);
        fdListObject.set("fd_list.fd_price", priceArray);
        fdListObject.set("fd_list.fd_grossMargin", grossMarinArray);
        fdListObject.set("fd_list.fd_stockSaleDays", stockSaleDaysArray);
        fdListObject.set("fd_list.fd_couponDiscount", couponDiscountArray);
        fdListObject.set("fd_list.fd_promotionDiscount", promotionDiscountArray);
        fdListObject.set("fd_list.fd_dealDiscount", dealDiscountArray);
        fdListObject.set("fd_list.fd_totalDiscount", totalDiscountArray);
        fdListObject.set("fd_list.fd_totalAmount", totalAmountArray);
        fdListObject.set("fd_list.fd_transactionPrice", transactionPriceArray);
        fdListObject.set("fd_list.fd_salesGroupName", salesGroupNameArray);
        fdListObject.set("fd_list.fd_salesGroupEmptName", salesGroupEmptNameArray);
        fdListObject.set("fd_list.fd_salesGroupEmptCode", salesGroupEmptCodeArray);
        fdListObject.set("fd_list.fd_operationEmptName", operationEmptNameArray);
        fdListObject.set("fd_list.fd_operationEmpCode", operationEmpCodeArray);
        fdListObject.set("fd_list.fd_applyReasonName", applyReasonNameArray);
        fdListObject.set("fd_list.fd_startTime", startTimeArray);
        fdListObject.set("fd_list.fd_endTime", endTimeArray);
        // 将生成的JSON对象转换为字符串
        String json = objectMapper.writeValueAsString(fdListObject);
        return json;
    }

    private String nullToString(Object obj) {
        if (ObjectUtil.isNull(obj)) {
            return "";
        }
        return obj.toString();
    }

    /**
     * cancel
     * 取消
     *
     * @param cancelVo
     * <AUTHOR>
     * @history
     */
    public void cancel(SomCouponImportVo cancelVo) throws ValidateException {
        if (ObjectUtil.isNull(cancelVo) || StrUtil.isBlank(cancelVo.getAid()) || StrUtil.isBlank(cancelVo.getCancelReason())) {
            throw new ValidateException("数据存在空值,请检查");
        }
        SomCoupon coupon = somCouponMapper.createLambdaQuery().andEq("aid", cancelVo.getAid()).single();
        if (coupon.getStatus() != 50) {
            throw new ValidateException("当前状态不允许取消");
        }
        coupon.setStatus(80);
        coupon.setCancelReason(cancelVo.getCancelReason());
        somCouponMapper.updateById(coupon);
    }

    /**
     * finish
     * 结束活动
     *
     * @param finishVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void finish(SomCouponImportVo finishVo) throws ValidateException {
        if (ObjectUtil.isNull(finishVo) || StrUtil.isBlank(finishVo.getAid()) || ObjectUtil.isNull(finishVo.getFinishType()) || StrUtil.isBlank(finishVo.getFinishReason())) {
            throw new ValidateException("数据存在空值,请检查");
        }
        SomCoupon coupon = somCouponMapper.createLambdaQuery().andEq("aid", finishVo.getAid()).single();
        if (coupon.getStatus() != 60) {
            throw new ValidateException("当前状态不允许取消");
        }
        coupon.setStatus(70);
        coupon.setFinishType(finishVo.getFinishType());
        coupon.setFinishReason(finishVo.getFinishReason());
        somCouponMapper.updateById(coupon);
    }

    /**
     * feedbackResult
     * 反馈提报结果
     *
     * @param feedbackVo
     * <AUTHOR>
     * @history
     */
    public void feedbackResult(SomCouponVo feedbackVo) throws ValidateException {
        if (ObjectUtil.isEmpty(feedbackVo) || StrUtil.isBlank(feedbackVo.getAid()) || ObjectUtil.isEmpty(feedbackVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<Integer> statusList = Arrays.asList(90, 110);
        SomCoupon coupon = somCouponMapper.createLambdaQuery().andEq("aid", feedbackVo.getAid()).andIn("status", statusList).single();
        if (ObjectUtil.isEmpty(coupon)) {
            throw new ValidateException("该数据状态不支持当前操作,请检查");
        }
        if (ObjectUtil.equal(40, feedbackVo.getStatus()) && StrUtil.isBlank(feedbackVo.getFailureReason())) {
            throw new ValidateException("提报失败时，失败原因不能为空");
        }
        if (ObjectUtil.equal(100, feedbackVo.getStatus()) && (ObjectUtil.isEmpty(feedbackVo.getBeginDateReal()) || ObjectUtil.isEmpty(feedbackVo.getEndDateReal()))) {
            throw new ValidateException("提报成功时，实际活动起止时间不能为空");
        }
        coupon.setStatus(feedbackVo.getStatus());
        if (feedbackVo.getStatus().equals(100)) {
            coupon.setStatus(50);
        }
        coupon.setFailureReason(feedbackVo.getFailureReason());
        coupon.setBeginDateReal(feedbackVo.getBeginDateReal());
        coupon.setEndDateReal(feedbackVo.getEndDateReal());
        somCouponMapper.updateById(coupon);
        if (ObjectUtil.equal(40, feedbackVo.getStatus())) {
            try {
                List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "CouponType").select();
                McDictionaryInfo info = infoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemValue(), coupon.getCouponType().toString())).findFirst().orElse(null);
//                SomCouponItems couponItems = somCouponItemsMapper.createLambdaQuery().andEq("coupon_id", feedbackVo.getAid()).single();
                List<SomCouponItems> itemsList = somCouponItemsMapper.createLambdaQuery().andEq("coupon_id", feedbackVo.getAid()).select();
                List<AmazonActivityReminderEvent.Reminder> list = new ArrayList<>();
                for (SomCouponItems somCouponItems : itemsList) {
                    AmazonActivityReminderEvent.Reminder reminder = new AmazonActivityReminderEvent.Reminder(coupon.getSite(), somCouponItems.getSellerSku(), Strings.EMPTY,
                            info.getItemLable(), coupon.getBeginDate(), coupon.getEndDate(), "Coupon", feedbackVo.getFailureReason(), coupon.getCreateNum());
                    list.add(reminder);
                }

                AmazonActivityReminderEvent event = new AmazonActivityReminderEvent(list);
                eventBusTemplate.publish(event);
            } catch (Exception e) {
                throw new ValidateException("放入消息队列出错" + e.getMessage());
            }
        }
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param list
     * <AUTHOR>
     * @history
     */
    public String batchFeedbackResult(List<SomCouponFeedBackVo> list) throws ValidateException {
        StringBuilder resultStr1 = new StringBuilder();
        StringBuilder resultStr2 = new StringBuilder();
        StringBuilder resultStr3 = new StringBuilder();
        for (SomCouponFeedBackVo importVo : list) {
            if (importVo.getTitle().contains("on")) {
                String title = StrUtil.subAfter(importVo.getTitle(), "on", true).trim();
                importVo.setTitle(title);
            }
        }
        List<String> sites = list.stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
        List<String> titles = list.stream().map(m -> m.getTitle()).distinct().collect(Collectors.toList());
        List<Integer> statusList = Arrays.asList(90, 110);
        List<SomCoupon> couponList = somCouponMapper.createLambdaQuery().andIn("site", sites).andIn("title", titles).andIn("status", statusList).select();
        for (SomCouponFeedBackVo importVo : list) {
            if (StrUtil.isBlank(importVo.getSite()) || StrUtil.isBlank(importVo.getTitle())) {
                throw new ValidateException("站点和Coupon Title不允许为空");
            }
            if (StrUtil.equalsIgnoreCase("提报成功", importVo.getStatusName()) && (ObjectUtil.isEmpty(importVo.getBeginDateReal()) || (ObjectUtil.isEmpty(importVo.getEndDateReal())))) {
                resultStr1.append(importVo.getSite()).append("\n").append(importVo.getTitle()).append("\n");
            }
            if (StrUtil.equalsIgnoreCase("提报失败", importVo.getStatusName()) && StrUtil.isBlank(importVo.getFailureReason())) {
                resultStr2.append(importVo.getSite()).append("\n").append(importVo.getTitle()).append("\n");
            }
            long count = couponList.stream().filter(f -> StrUtil.equals(f.getSite(), importVo.getSite()) && StrUtil.equals(f.getTitle(), importVo.getTitle())).count();
            if (count == 0) {
                resultStr3.append("站点:").append(importVo.getSite()).append("\n").append("Coupon Title:").append(importVo.getTitle()).append("\n");
            }
        }
        if (StrUtil.isNotBlank(resultStr1)) {
            resultStr1.append("实际起止时间不允许为空");
            return resultStr1.toString();
        }
        if (StrUtil.isNotBlank(resultStr2)) {
            resultStr2.append("提报失败原因不允许为空");
            return resultStr2.toString();
        }
        if (StrUtil.isNotBlank(resultStr3)) {
            resultStr3.append("的秒杀活动不存在");
            return resultStr3.toString();
        }
        List<SomCoupon> addList = new ArrayList<>();
        list.forEach(obj -> {
            couponList.stream().filter(f -> StrUtil.equalsIgnoreCase(obj.getSite(), f.getSite()) && StrUtil.equalsIgnoreCase(obj.getTitle(), f.getTitle())).findFirst().ifPresent(ps -> {
                if (StrUtil.equalsIgnoreCase("提报成功", obj.getStatusName())) {
                    ps.setStatus(50);
                } else if (StrUtil.equalsIgnoreCase("提报失败", obj.getStatusName())) {
                    ps.setStatus(40);
                }
                ps.setBeginDateReal(obj.getBeginDateReal());
                ps.setEndDateReal(obj.getEndDateReal());
                ps.setFailureReason(obj.getFailureReason());
                addList.add(ps);
            });
        });
        if (CollectionUtil.isNotEmpty(addList)) {
            somCouponMapper.batchFeedbackResult(addList);
        }
        List<SomCoupon> eventList = couponList.stream().filter(f -> ObjectUtil.equal(40, f.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(eventList)) {
            try {
                List<AmazonActivityReminderEvent.Reminder> reminderList = new ArrayList<>();
                List<SomCouponItems> couponItemsList = somCouponItemsMapper.createLambdaQuery().andIn("coupon_id", eventList.stream().map(m -> m.getAid())
                        .collect(Collectors.toList())).select();
                List<McDictionaryInfo> infoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "CouponType").select();
                for (SomCoupon coupon : eventList) {
                    McDictionaryInfo info = infoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getItemValue(), coupon.getCouponType().toString())).findFirst().orElse(null);
                    SomCouponItems couponItems = couponItemsList.stream().filter(f -> StrUtil.equalsIgnoreCase(coupon.getAid(), f.getCouponId())).findFirst().orElse(null);
                    SomCouponFeedBackVo feedbackVo = list.stream().filter(f -> StrUtil.equalsIgnoreCase(coupon.getSite(), f.getSite()) && StrUtil.equalsIgnoreCase(coupon.getTitle(), f.getTitle()))
                            .findFirst().orElse(null);
                    AmazonActivityReminderEvent.Reminder reminder = new AmazonActivityReminderEvent.Reminder(coupon.getSite(), couponItems.getSellerSku(), Strings.EMPTY,
                            info.getItemLable(), coupon.getBeginDate(), coupon.getEndDate(), "Coupon", feedbackVo.getFailureReason(), coupon.getCreateNum());
                    reminderList.add(reminder);
                }
                AmazonActivityReminderEvent event = new AmazonActivityReminderEvent(reminderList);
                eventBusTemplate.publish(event);
            } catch (Exception e) {
                throw new ValidateException("放入消息队列出错" + e.getMessage());
            }
        }
        return Strings.EMPTY;
    }

    /**
     * mark
     * 标记活动状态
     *
     * @param markVo
     * <AUTHOR>
     * @history
     */
    public void mark(SomCouponVo markVo) throws ValidateException {
        if (ObjectUtil.isEmpty(markVo) || StrUtil.isBlank(markVo.getAid()) || ObjectUtil.isEmpty(markVo.getStatus())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<Integer> statusList = Arrays.asList(70, 80);
        SomCoupon coupon = somCouponMapper.createLambdaQuery().andEq("aid", markVo.getAid()).andIn("status", statusList).single();
        if (ObjectUtil.isEmpty(coupon)) {
            throw new ValidateException("该数据状态不支持当前操作,请检查");
        }
        if ((ObjectUtil.equal(71, markVo.getStatus()) || ObjectUtil.equal(81, markVo.getStatus())) && StrUtil.isBlank(markVo.getFailureReason())) {
            throw new ValidateException("结束失败/取消失败时,失败原因不能为空");
        }
        if ((ObjectUtil.equal(72, markVo.getStatus()) || ObjectUtil.equal(82, markVo.getStatus())) && (ObjectUtil.isEmpty(markVo.getCancelDate()) && ObjectUtil.isEmpty(markVo.getFinishDate()))) {
            throw new ValidateException("已结束/已取消时,结束时间或取消时间不能同时为空");
        }
        coupon.setStatus(markVo.getStatus());
        coupon.setCancelDate(markVo.getCancelDate());
        coupon.setFinishDate(markVo.getFinishDate());
        coupon.setFailureReason(markVo.getFailureReason());
        if (ObjectUtil.equal(82, markVo.getStatus())) {
            coupon.setBeginDateReal(markVo.getBeginDateReal());
            coupon.setEndDateReal(markVo.getEndDateReal());
        }
        somCouponMapper.updateById(coupon);
    }

    /**
     * summited
     * 标记为已提报
     *
     * @param summitedVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void summited(SomCouponExVo summitedVo) throws ValidateException {
        if (ObjectUtil.isEmpty(summitedVo) || CollectionUtil.isEmpty(summitedVo.getAidList())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<SomCoupon> couponList = somCouponMapper.createLambdaQuery().andIn("aid", summitedVo.getAidList()).andEq("status", 90).select();
        if (summitedVo.getAidList().size() != couponList.size()) {
            throw new ValidateException("存在不同状态的数据,请检查");
        }
        for (SomCoupon coupon : couponList) {
            coupon.setStatus(110);
        }
        // 运营已提报
        somCouponMapper.batchUpdate(couponList);
    }
}
