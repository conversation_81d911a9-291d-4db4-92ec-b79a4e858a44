package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McShippingGroupInfoService;
import com.zielsmart.mc.vo.McShippingGroupInfoPageSearchVo;
import com.zielsmart.mc.vo.McShippingGroupInfoSearchVo;
import com.zielsmart.mc.vo.McShippingGroupInfoVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McShippingGroupInfoController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcShippingGroupInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "运费模板配置管理")
public class McShippingGroupInfoController extends BasicController{

    @Resource
    McShippingGroupInfoService mcShippingGroupInfoService;


    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McShippingGroupInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McShippingGroupInfoVo>> queryByPage(@RequestBody McShippingGroupInfoPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcShippingGroupInfoService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param McShippingGroupInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McShippingGroupInfoVo McShippingGroupInfoVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcShippingGroupInfoService.save(McShippingGroupInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param McShippingGroupInfoVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McShippingGroupInfoVo McShippingGroupInfoVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcShippingGroupInfoService.update(McShippingGroupInfoVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param McShippingGroupInfoVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McShippingGroupInfoVo McShippingGroupInfoVo) throws ValidateException {
        mcShippingGroupInfoService.delete(McShippingGroupInfoVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryShippingGroupInfos
     * 根据平台站点查询运费模板
     * @param searchVo
     * @return {@link ResultVo< PageVo< McShippingGroupInfoVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点查询运费模板")
    @PostMapping(value = "/queryShippingGroupInfos")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McShippingGroupInfoVo>> queryShippingGroupInfos(@RequestBody McShippingGroupInfoSearchVo searchVo) {
        return ResultVo.ofSuccess(mcShippingGroupInfoService.queryShippingGroupInfos(searchVo));
    }

    /**
     * appointArea
     * 指定区域
     * @param McShippingGroupInfoVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "指定区域")
    @PostMapping(value = "/appointArea")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> appointArea(@RequestBody McShippingGroupInfoVo McShippingGroupInfoVo) throws ValidateException {
        mcShippingGroupInfoService.appointArea(McShippingGroupInfoVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "根据站点查询区域")
    @PostMapping(value = "/getAreaBySite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<Map<String, String>>> getAreaBySite(@RequestBody McShippingGroupInfoVo McShippingGroupInfoVo) throws ValidateException {
        return ResultVo.ofSuccess(mcShippingGroupInfoService.getAreaBySite(McShippingGroupInfoVo));
    }
}
