package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McSellerskuMappingService;
import com.zielsmart.mc.vo.McSellerskuMappingPageSearchVo;
import com.zielsmart.mc.vo.McSellerskuMappingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McSellerskuMappingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcSellerskuMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "展示码映射表管理")
public class McSellerskuMappingController extends BasicController {

    @Resource
    McSellerskuMappingService mcSellerskuMappingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McSellerskuMappingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McSellerskuMappingVo>> queryByPage(@RequestBody McSellerskuMappingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcSellerskuMappingService.queryByPage(searchVo));
    }


    /**
     * delete
     *
     * @param mcSellerskuMappingVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McSellerskuMappingVo mcSellerskuMappingVo) throws ValidateException {
        mcSellerskuMappingService.delete(mcSellerskuMappingVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * importExcel
     * 模板导入数据
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台", "站点", "展示码", "Seller SKU"};
        importParams.setImportFields(arr);
        ExcelImportResult<McSellerskuMappingVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), McSellerskuMappingVo.class,importParams);
        } catch (Exception e) {
            if(StrUtil.equalsIgnoreCase(e.getMessage(),"不是合法的Excel模板")){
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        List<McSellerskuMappingVo> list = result.getList();
        if(list.isEmpty()){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        mcSellerskuMappingService.importExcel(list,tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * downloadTemplate
     * 下载导入模板
     * @param response
     * @throws {@link IOException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping("/download-template")
    public String downloadTemplate(HttpServletResponse response) throws IOException {
        return "forward:/static/excel/SkuMappingTemplate.xlsx";
    }

    /**
     * exportDearanceProduct
     * 导出展示码映射关系报表  全量导出 可按条件查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出展示码映射关系报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody McSellerskuMappingPageSearchVo searchVo) throws ValidateException{
        String data = mcSellerskuMappingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }


    /**
     * add
     *
     * @param addVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> add(@RequestBody McSellerskuMappingVo addVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcSellerskuMappingService.add(addVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * edit
     *
     * @param editVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/edit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody McSellerskuMappingVo editVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcSellerskuMappingService.edit(editVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
