package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 亚马逊价格前置管理
* gen by 代码生成器 2025-02-17
*/

@Table(name="mc.som_amazon_price_pre_manage")
public class SomAmazonPricePreManage implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 产品编码
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 是否寄售
	 */
	@Column("is_consignment_sales")
	private Integer isConsignmentSales ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 失效时间
	 */
	@Column("failure_time")
	private Date failureTime ;
	/**
	 * 调价原因
	 */
	@Column("adjust_price_reason")
	private String adjustPriceReason ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomAmazonPricePreManage() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 产品编码
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* 产品编码
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 是否寄售
	*@return
	*/
	public Integer getisConsignmentSales(){
		return  isConsignmentSales;
	}
	/**
	* 是否寄售
	*@param  isConsignmentSales
	*/
	public void setisConsignmentSales(Integer isConsignmentSales ){
		this.isConsignmentSales = isConsignmentSales;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 失效时间
	*@return
	*/
	public Date getFailureTime(){
		return  failureTime;
	}
	/**
	* 失效时间
	*@param  failureTime
	*/
	public void setFailureTime(Date failureTime ){
		this.failureTime = failureTime;
	}
	/**
	* 调价原因
	*@return
	*/
	public String getAdjustPriceReason(){
		return  adjustPriceReason;
	}
	/**
	* 调价原因
	*@param  adjustPriceReason
	*/
	public void setAdjustPriceReason(String adjustPriceReason ){
		this.adjustPriceReason = adjustPriceReason;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 最后修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 最后修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
