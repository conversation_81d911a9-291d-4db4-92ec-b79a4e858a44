package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.repository.entity.McSellerskuMapping;
import com.zielsmart.mc.repository.entity.SysUserNeweya;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.SomOfferExportVo;
import com.zielsmart.mc.vo.SomOfferExtVo;
import com.zielsmart.mc.vo.SomOfferHistoryVo;
import com.zielsmart.mc.vo.SomOfferPageSearchVo;
import com.zielsmart.mc.vo.offer.BuyBoxPrice;
import com.zielsmart.mc.vo.offer.LowestPrice;
import com.zielsmart.mc.vo.offer.SellerFeedbackRating;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomOfferService {

    @Resource
    private SomOfferMapper somOfferMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOfferVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomOfferExtVo> queryByPage(SomOfferPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomOfferExtVo> pageResult = somOfferMapper.queryByPage(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<String> codeList = Arrays.asList("IsFulfilledByAmazon", "OffersInfoStatus");
            List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", codeList).select();
            List<McSellerskuMapping> skuMappingList = dynamicSqlManager.getMapper(McSellerskuMappingMapper.class).all();
            List<McProductSales> productList = dynamicSqlManager.getMapper(McProductSalesMapper.class).createLambdaQuery().andIn("display_product_code", skuMappingList.stream().map(m -> m.getProductDisplayCode()).collect(Collectors.toList())).select();
            List<SysUserNeweya> userList = dynamicSqlManager.getMapper(SysUserNeweyaMapper.class).all();
            pageResult.getList().forEach(f -> {
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "IsFulfilledByAmazon") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getIsFulfilledByAmazon().toString())).findFirst().ifPresent(pc -> {
                    f.setIsFulfilledByAmazonName(pc.getItemLable());
                });
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "OffersInfoStatus") && StrUtil.equalsIgnoreCase(t.getItemValue(), f.getStatus().toString())).findFirst().ifPresent(pc -> {
                    f.setStatusName(pc.getItemLable());
                });
                if (StrUtil.isNotEmpty(f.getBuyBoxPrices())) {
                    JSONArray array = JSONUtil.parseArray(f.getBuyBoxPrices());
                    List<BuyBoxPrice> buyBoxPriceList = JSONUtil.toList(array, BuyBoxPrice.class);
                    buyBoxPriceList.stream().filter(s-> StrUtil.equalsIgnoreCase("new",s.getCondition())).findFirst().ifPresent(ps->{
                            f.setBuyBoxPrice(ps);
                    });
                }
                if (StrUtil.isNotEmpty(f.getLowestPrices())) {
                    JSONArray array = JSONUtil.parseArray(f.getLowestPrices());
                    List<LowestPrice> lowestPriceList = JSONUtil.toList(array, LowestPrice.class);
                    lowestPriceList.stream().filter(s-> StrUtil.equalsIgnoreCase("new",s.getCondition())).findFirst().ifPresent(ps->{
                        f.setLowestPrice(ps);
                    });
                }
                if(StrUtil.isNotEmpty(f.getSite())){
                    String url = StrUtil.format("https://www.amazon.{}/sp?ie=UTF8&seller={}", f.getSite().substring(7), f.getSellerId());
                    f.setDirectUrl(url);
                }
                if(StrUtil.isEmpty(f.getSku())){
                    McSellerskuMapping mapping = skuMappingList.stream().filter(p -> StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(f.getSellerSku(), p.getSellerSku())).findFirst().orElse(null);
                    if(ObjectUtil.isNotEmpty(mapping)){
                        productList.stream().filter(p-> StrUtil.equalsIgnoreCase(f.getSite(), p.getSite()) && StrUtil.equalsIgnoreCase(mapping.getProductDisplayCode(), p.getDisplayProductCode())).findFirst().ifPresent(ps->{
                            f.setSku(ps.getProductMainCode());
                            userList.stream().filter(p->StrUtil.equalsIgnoreCase(ps.getSalesGroupEmptCode(), p.getEmployeeNumber())).findFirst().ifPresent(pf->{
                            f.setSalesGroupEmptName(pf.getemNameCn());
                            });
                        });
                    }
                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomOfferExtVo.class, searchVo);
    }

    /**
     * queryHistory
     * 查询历史记录
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOfferHistoryVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomOfferHistoryVo> queryHistory(SomOfferPageSearchVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isEmpty(searchVo.getKeyWord()) || StrUtil.isEmpty(searchVo.getMarketplaceName()) || ObjectUtil.isEmpty(searchVo.getIsFulfilledByAmazon())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        List<SomOfferHistoryVo> historyVoList = somOfferMapper.queryHistory(searchVo);
        if (CollectionUtil.isNotEmpty(historyVoList)) {
            List<String> codeList = Arrays.asList("IsFulfilledByAmazon", "OffersInfoStatus");
            List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", codeList).select();
            for (SomOfferHistoryVo historyVo : historyVoList) {
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "IsFulfilledByAmazon") && StrUtil.equalsIgnoreCase(t.getItemValue(), historyVo.getIsFulfilledByAmazon().toString())).findFirst().ifPresent(pc -> {
                    historyVo.setIsFulfilledByAmazonName(pc.getItemLable());
                });
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "OffersInfoStatus") && StrUtil.equalsIgnoreCase(t.getItemValue(), historyVo.getStatus().toString())).findFirst().ifPresent(pc -> {
                    historyVo.setStatusName(pc.getItemLable());
                });
                historyVo.setTotalCount(historyVoList.size());
                BigDecimal totalAmaont = historyVo.getListingPrice().subtract(historyVo.getShipping()).subtract(ObjectUtil.isEmpty(historyVo.getPoints()) ? BigDecimal.ZERO : BigDecimal.valueOf(historyVo.getPoints()));
                historyVo.setTotalAmaont(totalAmaont);
                if (StrUtil.isNotEmpty(historyVo.getSellerFeedbackRating())) {
                    SellerFeedbackRating sellerFeedbackRating = JSONUtil.toBean(historyVo.getSellerFeedbackRating(), SellerFeedbackRating.class);
                    if (ObjectUtil.isNotEmpty(sellerFeedbackRating)) {
                        historyVo.setFeedbackRating(sellerFeedbackRating);
                    }
                }
                String url = StrUtil.format("https://www.amazon.{}/sp?ie=UTF8&seller={}", historyVo.getSite().substring(7), historyVo.getSellerId());
                historyVo.setDirectUrl(url);
            }
        }
        return historyVoList;
    }

    /**
     * export
     * 导出
     * @param searchVo
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public String export(SomOfferPageSearchVo searchVo) throws ValidateException{
        List<SomOfferExportVo> records = somOfferMapper.queryExportData(searchVo);
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> codeList = Arrays.asList("IsFulfilledByAmazon", "OffersInfoStatus");
            List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andIn("item_type_code", codeList).select();
            for(SomOfferExportVo exportVo :records){
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "IsFulfilledByAmazon") && StrUtil.equalsIgnoreCase(t.getItemValue(), exportVo.getIsFulfilledByAmazon().toString())).findFirst().ifPresent(pc -> {
                    exportVo.setIsFulfilledByAmazonName(pc.getItemLable());
                });
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemTypeCode(), "OffersInfoStatus") && StrUtil.equalsIgnoreCase(t.getItemValue(), exportVo.getStatus().toString())).findFirst().ifPresent(pc -> {
                    exportVo.setStatusName(pc.getItemLable());
                });
                if (StrUtil.isNotEmpty(exportVo.getBuyBoxPrices())) {
                    JSONArray array = JSONUtil.parseArray(exportVo.getBuyBoxPrices());
                    List<BuyBoxPrice> buyBoxPriceList = JSONUtil.toList(array, BuyBoxPrice.class);
                    buyBoxPriceList.stream().filter(s-> StrUtil.equalsIgnoreCase("new",s.getCondition())).findFirst().ifPresent(ps->{
                        exportVo.setBuyBoxPrice(ps);
                        exportVo.setBuyBoxPriceExt(ps.getLandedPrice().getAmount());
                    });
                }
                if (StrUtil.isNotEmpty(exportVo.getLowestPrices())) {
                    JSONArray array = JSONUtil.parseArray(exportVo.getLowestPrices());
                    List<LowestPrice> lowestPriceList = JSONUtil.toList(array, LowestPrice.class);
                    lowestPriceList.stream().filter(s-> StrUtil.equalsIgnoreCase("new",s.getCondition())).findFirst().ifPresent(ps->{
                        exportVo.setLowestPrice(ps);
                        exportVo.setLowestPriceExt(ps.getLandedPrice().getAmount());
                    });
                }
                String buyCarOwner = Strings.EMPTY;
                if(StrUtil.isNotEmpty(exportVo.getSellerName())){
                    buyCarOwner = exportVo.getSellerId() + "," +exportVo.getSellerName();
                }else {
                    buyCarOwner = exportVo.getSellerId();
                }
                exportVo.setBuyCarOwner(buyCarOwner);
                if(StrUtil.isEmpty(exportVo.getSellerName()) && StrUtil.isNotEmpty(exportVo.getSellerId())){
                    exportVo.setSellerName(exportVo.getSellerId());
                }
                BigDecimal totalAmaont = exportVo.getListingPrice().subtract(exportVo.getShipping()).subtract(ObjectUtil.isEmpty(exportVo.getPoints()) ? BigDecimal.ZERO : BigDecimal.valueOf(exportVo.getPoints()));
                String  shipping = Strings.EMPTY;
                if(ObjectUtil.isEmpty(exportVo.getShipping()) || BigDecimal.valueOf(0).compareTo(exportVo.getShipping()) == 0) {
                    shipping = "-";
                }
                String  points = Strings.EMPTY;
                if(ObjectUtil.isEmpty(exportVo.getPoints()) || Integer.valueOf(0).compareTo(exportVo.getPoints()) == 0) {
                    points = "-";
                }
                exportVo.setToSellerPrice("总计:" + totalAmaont + "\n售价:" + exportVo.getListingPrice() + "\n运费:" +shipping + "\n积分:" +points);
            }
            try {
                ExportParams params = new ExportParams(null, "跟卖监控");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                Workbook workbook = ExcelExportUtil.exportExcel(params, SomOfferExportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出跟卖监控失败：" + e.getMessage());
            }
        }
        return null;
    }
}
