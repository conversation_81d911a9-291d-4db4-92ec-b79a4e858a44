package com.zielsmart.mc.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/*
 * FBA产品黑名单表的VO实体对象
 * gen by 代码生成器 2024-07-04
 */

@Data
@Schema(title = "FBA产品黑名单表", name = "SomTemuFbaBlackListVo")
public class SomTemuFbaBlackListVo implements java.io.Serializable {
    /**
     * 主键aid
     */
    @Schema(description = "主键aid", name = "aid")
    private String aid;

    @Schema(description = "主键aid集合", name = "aidList")
    private List<String> aidList;
    /**
     * 平台
     */
    @Schema(description = "平台", name = "platform")
    @Excel(name = "平台", orderNum = "3")
    private String platform;
    /**
     * 站点
     */
    @Schema(description = "站点", name = "site")
    @Excel(name = "站点", orderNum = "4")
    private String site;
    /**
     * 展示码
     */
    @Schema(description = "展示码", name = "sellerSku")
    @Excel(name = "展示码", orderNum = "5")
    private String sellerSku;
    /**
     * 删除标识，10.正常 99.删除
     */
    @Schema(description = "删除标识，10.正常 99.删除", name = "deleteFlag")
    private Integer deleteFlag;
    /**
     * 创建人工号
     */
    @Schema(description = "创建人工号", name = "createNum")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", name = "createName")
    @Excel(name = "创建人", orderNum = "6")
    private String createName;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间", name = "createTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", exportFormat = "yyyy-MM-dd HH:mm:ss", orderNum = "7")
    private Date createTime;

    @Schema(description = "发货方式", name = "fulfillmentChannel")
    @Excel(name = "发货方式(使用逗号分隔)", orderNum = "8")
    private String fulfillmentChannel;

    @Schema(description = "发货仓库&发货库区(json格式)", name = "useableWarehouseStorage")
    private String useableWarehouseStorage;

    @Schema(description = "店铺ID", name = "accountId")
    @Excel(name = "店铺ID", orderNum = "2")
    private String accountId;

    @Schema(description = "店铺名称", name = "accountName")
    @Excel(name = "店铺名称", orderNum = "1")
    private String accountName;

    private List<UseableWarehouse> list;

    private List<String> nameList;

    @Schema(description = "发货仓库&发货库区名称(逗号分隔)", name = "useableWarehouseStorage")
    @Excel(name = "发货仓库&发货库区", orderNum = "7")
    private String warehouseStorageName;

    @Data
    public static class UseableWarehouse implements Serializable {
        @Schema(description = "发货仓库编码", name = "warehouseCode")
        private String warehouseCode;

        @Schema(description = "发货库区编码",name="slCode")
        private String slCode;

    }
}
