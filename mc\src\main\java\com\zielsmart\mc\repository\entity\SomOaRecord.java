package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* OA流程记录
* gen by 代码生成器 2024-06-24
*/

@Table(name="mc.som_oa_record")
public class SomOaRecord implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 流程ID
	 */
	@Column("process_id")
	private String processId ;
	/**
	 * 表单json
	 */
	@Column("form_value")
	private String formValue ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 流程标题
	 */
	@Column("doc_subject")
	private String docSubject ;
	/**
	 * 发起提交流程时间
	 */
	@Column("submit_time")
	private Date submitTime ;
	/**
	 * 状态 0未发起  1成功   2失败
	 */
	@Column("status")
	private Integer status ;

	public SomOaRecord() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 流程ID
	*@return
	*/
	public String getProcessId(){
		return  processId;
	}
	/**
	* 流程ID
	*@param  processId
	*/
	public void setProcessId(String processId ){
		this.processId = processId;
	}
	/**
	* 表单json
	*@return
	*/
	public String getFormValue(){
		return  formValue;
	}
	/**
	* 表单json
	*@param  formValue
	*/
	public void setFormValue(String formValue ){
		this.formValue = formValue;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 流程标题
	*@return
	*/
	public String getDocSubject(){
		return  docSubject;
	}
	/**
	* 流程标题
	*@param  docSubject
	*/
	public void setDocSubject(String docSubject ){
		this.docSubject = docSubject;
	}
	/**
	* 发起提交流程时间
	*@return
	*/
	public Date getSubmitTime(){
		return  submitTime;
	}
	/**
	* 发起提交流程时间
	*@param  submitTime
	*/
	public void setSubmitTime(Date submitTime ){
		this.submitTime = submitTime;
	}
	/**
	* 状态 0未发起  1成功   2失败
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态 0未发起  1成功   2失败
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}

}
