package com.zielsmart.mc.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.listener
 * @title PlatformQuantityListener
 * @description
 * @date 2021-04-27 15:34
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component
@EnableAsync
public class EbayPlatformQuantityListener {
    @Async("taskExecutor")
    @EventListener
    public void handler(EbayPlatformQuantityListener ebayPlatformQuantityListener) {

    }
}
