package com.zielsmart.mc.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2024-12-12
*/

@Table(name="mc.som_walmart_advertising_performance")
public class SomWalmartAdvertisingPerformance implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 广告类型
	 */
	@Column("advertising_type")
	private String advertisingType ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 广告归因日期,格式："yyyy-MM-dd"
	 */
	@Column("advertising_date")
	private Date advertisingDate ;
	/**
	 * 广告分组名称
	 */
	@Column("campaign_name")
	private String campaignName ;
	/**
	 * 产品唯一ID
	 */
	@Column("item_id")
	private String itemId ;
	/**
	 * 产品名称
	 */
	@Column("item_name")
	private String itemName ;
	/**
	 * 浏览量
	 */
	@Column("impressions")
	private Long impressions ;
	/**
	 * 点击次数
	 */
	@Column("clicks")
	private Integer clicks ;
	/**
	 * CTR
	 */
	@Column("ctr")
	private BigDecimal ctr ;
	/**
	 * 广告花费
	 */
	@Column("ad_spend")
	private BigDecimal adSpend ;
	/**
	 * 订单数量
	 */
	@Column("orders")
	private Long orders ;
	/**
	 * 转化率
	 */
	@Column("conversion_rate")
	private BigDecimal conversionRate ;
	/**
	 * 总销售额
	 */
	@Column("total_sales_amount")
	private BigDecimal totalSalesAmount ;
	/**
	 * 广告销售额
	 */
	@Column("advertised_sku_sales")
	private BigDecimal advertisedSkuSales ;
	/**
	 * 总销量
	 */
	@Column("unit_sold")
	private Long unitSold ;
	/**
	 * 广告销量
	 */
	@Column("advertised_sku_unit")
	private Integer advertisedSkuUnit ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomWalmartAdvertisingPerformance() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 广告类型
	*@return
	*/
	public String getAdvertisingType(){
		return  advertisingType;
	}
	/**
	* 广告类型
	*@param  advertisingType
	*/
	public void setAdvertisingType(String advertisingType ){
		this.advertisingType = advertisingType;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 广告归因日期,格式："yyyy-MM-dd"
	*@return
	*/
	public Date getAdvertisingDate(){
		return  advertisingDate;
	}
	/**
	* 广告归因日期,格式："yyyy-MM-dd"
	*@param  advertisingDate
	*/
	public void setAdvertisingDate(Date advertisingDate ){
		this.advertisingDate = advertisingDate;
	}
	/**
	* 广告分组名称
	*@return
	*/
	public String getCampaignName(){
		return  campaignName;
	}
	/**
	* 广告分组名称
	*@param  campaignName
	*/
	public void setCampaignName(String campaignName ){
		this.campaignName = campaignName;
	}
	/**
	* 产品唯一ID
	*@return
	*/
	public String getItemId(){
		return  itemId;
	}
	/**
	* 产品唯一ID
	*@param  itemId
	*/
	public void setItemId(String itemId ){
		this.itemId = itemId;
	}
	/**
	* 产品名称
	*@return
	*/
	public String getItemName(){
		return  itemName;
	}
	/**
	* 产品名称
	*@param  itemName
	*/
	public void setItemName(String itemName ){
		this.itemName = itemName;
	}
	/**
	* 浏览量
	*@return
	*/
	public Long getImpressions(){
		return  impressions;
	}
	/**
	* 浏览量
	*@param  impressions
	*/
	public void setImpressions(Long impressions ){
		this.impressions = impressions;
	}
	/**
	* 点击次数
	*@return
	*/
	public Integer getClicks(){
		return  clicks;
	}
	/**
	* 点击次数
	*@param  clicks
	*/
	public void setClicks(Integer clicks ){
		this.clicks = clicks;
	}
	/**
	* CTR
	*@return
	*/
	public BigDecimal getCtr(){
		return  ctr;
	}
	/**
	* CTR
	*@param  ctr
	*/
	public void setCtr(BigDecimal ctr ){
		this.ctr = ctr;
	}
	/**
	* 广告花费
	*@return
	*/
	public BigDecimal getAdSpend(){
		return  adSpend;
	}
	/**
	* 广告花费
	*@param  adSpend
	*/
	public void setAdSpend(BigDecimal adSpend ){
		this.adSpend = adSpend;
	}
	/**
	* 订单数量
	*@return
	*/
	public Long getOrders(){
		return  orders;
	}
	/**
	* 订单数量
	*@param  orders
	*/
	public void setOrders(Long orders ){
		this.orders = orders;
	}
	/**
	* 转化率
	*@return
	*/
	public BigDecimal getConversionRate(){
		return  conversionRate;
	}
	/**
	* 转化率
	*@param  conversionRate
	*/
	public void setConversionRate(BigDecimal conversionRate ){
		this.conversionRate = conversionRate;
	}
	/**
	* 总销售额
	*@return
	*/
	public BigDecimal getTotalSalesAmount(){
		return  totalSalesAmount;
	}
	/**
	* 总销售额
	*@param  totalSalesAmount
	*/
	public void setTotalSalesAmount(BigDecimal totalSalesAmount ){
		this.totalSalesAmount = totalSalesAmount;
	}
	/**
	* 广告销售额
	*@return
	*/
	public BigDecimal getAdvertisedSkuSales(){
		return  advertisedSkuSales;
	}
	/**
	* 广告销售额
	*@param  advertisedSkuSales
	*/
	public void setAdvertisedSkuSales(BigDecimal advertisedSkuSales ){
		this.advertisedSkuSales = advertisedSkuSales;
	}
	/**
	* 总销量
	*@return
	*/
	public Long getUnitSold(){
		return  unitSold;
	}
	/**
	* 总销量
	*@param  unitSold
	*/
	public void setUnitSold(Long unitSold ){
		this.unitSold = unitSold;
	}
	/**
	* 广告销量
	*@return
	*/
	public Integer getAdvertisedSkuUnit(){
		return  advertisedSkuUnit;
	}
	/**
	* 广告销量
	*@param  advertisedSkuUnit
	*/
	public void setAdvertisedSkuUnit(Integer advertisedSkuUnit ){
		this.advertisedSkuUnit = advertisedSkuUnit;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
