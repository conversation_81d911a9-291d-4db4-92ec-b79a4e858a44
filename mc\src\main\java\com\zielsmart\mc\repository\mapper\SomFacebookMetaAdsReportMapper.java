package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomFacebookMetaAdsReport;
import com.zielsmart.mc.vo.SomFacebookMetaAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomFacebookMetaAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-05-19
*/

@SqlResource("somFacebookMetaAdsReport")
public interface SomFacebookMetaAdsReportMapper extends BaseMapper<SomFacebookMetaAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomFacebookMetaAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomFacebookMetaAdsReportVo> queryByPage(@Param("searchVo")SomFacebookMetaAdsReportPageSearchVo searchVo, PageRequest pageRequest);
}
