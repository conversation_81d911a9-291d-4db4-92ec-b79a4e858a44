package com.zielsmart.mc.controller;

import com.zielsmart.web.basic.annotation.LoginIgnore;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 默认一些请求返回
 * 1.处理/异常的
 * 2.处理没有favicon.ico图标异常的问题
 */
@RestController
public class SomDefaultController {
    @LoginIgnore(value = true)
    @GetMapping(value = "/")
    public String ping(){
        return "pong";
    }

    @LoginIgnore(value = true)
    @GetMapping("/favicon.ico")
    @ResponseBody
    public void favicon() {
        System.out.println("================输出favicon.ico");
    }
}
