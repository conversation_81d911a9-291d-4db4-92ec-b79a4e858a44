package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonRepeatPurchaseReportService;
import com.zielsmart.mc.vo.SomAmazonRepeatPurchaseReportPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonRepeatPurchaseReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonRepeatPurchaseReportController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somAmazonRepeatPurchaseReport", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "品牌复购率表管理")
public class SomAmazonRepeatPurchaseReportController extends BasicController{

    @Resource
    SomAmazonRepeatPurchaseReportService somAmazonRepeatPurchaseReportService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonRepeatPurchaseReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonRepeatPurchaseReportVo>> queryByPage(@RequestBody SomAmazonRepeatPurchaseReportPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonRepeatPurchaseReportService.queryByPage(searchVo));
    }

    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomAmazonRepeatPurchaseReportPageSearchVo searchVo) {
        String data = somAmazonRepeatPurchaseReportService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
