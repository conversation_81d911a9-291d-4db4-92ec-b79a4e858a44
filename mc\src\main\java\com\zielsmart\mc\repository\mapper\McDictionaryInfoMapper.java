package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.vo.dict.McDictPageSearchVo;
import com.zielsmart.mc.vo.dict.McDictionaryInfoVo;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-08-02
 */

@SqlResource("mcDictionaryInfo")
public interface McDictionaryInfoMapper extends BaseMapper<McDictionaryInfo> {

    PageResult<McDictionaryInfoVo> queryByPage(@Param("searchVo") McDictPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryAllInfos
     * 获取所有的平台、站点
     *
     * @return {@link java.util.List<com.zielsmart.mc.repository.entity.McDictionaryInfo>}
     * <AUTHOR>
     * @history
     */
    List<McDictionaryInfo> queryAllPlatformAndSites();

    /**
     * getMarket
     * 获取市场
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListVo> getMarket();

    /**
     * getPlatforms
     * 获取平台
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListVo> getPlatforms(@Param("searchVo") ZBPMWhiteListSearchVo searchVo);

    /**
     * getAllMarkets
     * 获取所有市场
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.dict.McDictionaryInfoVo>}
     * <AUTHOR>
     * @history
     */
    List<McDictionaryInfoVo> getAllMarkets();

    /**
     * getAllPlatforms
     * 获取所有平台
     *
     * @return {@link java.util.List<com.zielsmart.mc.vo.dict.McDictionaryInfoVo>}
     * <AUTHOR>
     * @history
     */
    List<McDictionaryInfoVo> getAllPlatforms();
}
