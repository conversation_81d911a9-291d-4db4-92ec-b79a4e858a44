package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomMdmOffers;
import com.zielsmart.mc.vo.SomMdmOffersPageSearchVo;
import com.zielsmart.mc.vo.SomMdmOffersVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-08
*/

@SqlResource("somMdmOffers")
public interface SomMdmOffersMapper extends BaseMapper<SomMdmOffers> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomMdmOffersVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomMdmOffersVo> queryByPage(@Param("searchVo")SomMdmOffersPageSearchVo searchVo, PageRequest pageRequest);

    List<SomMdmOffersVo> exportExcel(@Param("searchVo") SomMdmOffersPageSearchVo searchVo);

    List<SomMdmOffers> allMdmListing();

    default void updateBatch(@Param("updateList") List<SomMdmOffers> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somMdmOffers.updateBatch"), updateList);
    }
}
