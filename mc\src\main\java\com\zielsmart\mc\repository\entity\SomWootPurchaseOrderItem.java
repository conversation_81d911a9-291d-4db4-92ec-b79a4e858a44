package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * woot反馈的采购订单明细表
 * gen by 代码生成器 2024-06-04
 */

@Table(name = "mc.som_woot_purchase_order_item")
public class SomWootPurchaseOrderItem implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 主表aid
     */
    @Column("m_aid")
    private String mAid;
    /**
     * Woot产品编码
     */
    @Column("win")
    private String win;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 数量
     */
    @Column("quantity")
    private Integer quantity;
    /**
     * 退回的产品数量
     */
    @Column("return_purchase_order_quantity")
    private Integer returnPurchaseOrderQuantity;
    /**
     * 销售数量
     */
    @Column("sales_volume")
    private Integer salesVolume;
    /**
     * 活动单价
     */
    @Column("woot_price")
    private BigDecimal wootPrice;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 数据创建日期
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 客户经理反馈的PO号
     */
    @Column("purchase_order")
    private String purchaseOrder;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;

    public SomWootPurchaseOrderItem() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 主表aid
     *
     * @return
     */
    public String getMAid() {
        return mAid;
    }

    /**
     * 主表aid
     *
     * @param mAid
     */
    public void setMAid(String mAid) {
        this.mAid = mAid;
    }

    /**
     * Woot产品编码
     *
     * @return
     */
    public String getWin() {
        return win;
    }

    /**
     * Woot产品编码
     *
     * @param win
     */
    public void setWin(String win) {
        this.win = win;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 数量
     *
     * @return
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 数量
     *
     * @param quantity
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 退回的产品数量
     *
     * @return
     */
    public Integer getReturnPurchaseOrderQuantity() {
        return returnPurchaseOrderQuantity;
    }

    /**
     * 退回的产品数量
     *
     * @param returnPurchaseOrderQuantity
     */
    public void setReturnPurchaseOrderQuantity(Integer returnPurchaseOrderQuantity) {
        this.returnPurchaseOrderQuantity = returnPurchaseOrderQuantity;
    }

    /**
     * 销售数量
     *
     * @return
     */
    public Integer getSalesVolume() {
        return salesVolume;
    }

    /**
     * 销售数量
     *
     * @param salesVolume
     */
    public void setSalesVolume(Integer salesVolume) {
        this.salesVolume = salesVolume;
    }

    /**
     * 活动单价
     *
     * @return
     */
    public BigDecimal getWootPrice() {
        return wootPrice;
    }

    /**
     * 活动单价
     *
     * @param wootPrice
     */
    public void setWootPrice(BigDecimal wootPrice) {
        this.wootPrice = wootPrice;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 数据创建日期
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 数据创建日期
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 客户经理反馈的PO号
     *
     * @return
     */
    public String getPurchaseOrder() {
        return purchaseOrder;
    }

    /**
     * 客户经理反馈的PO号
     *
     * @param purchaseOrder
     */
    public void setPurchaseOrder(String purchaseOrder) {
        this.purchaseOrder = purchaseOrder;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }
}
