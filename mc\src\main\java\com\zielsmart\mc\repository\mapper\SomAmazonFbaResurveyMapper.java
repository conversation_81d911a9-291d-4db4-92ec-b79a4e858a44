package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonFbaResurveyVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-03-16
*/

@SqlResource("somAmazonFbaResurvey")
public interface SomAmazonFbaResurveyMapper extends BaseMapper<SomAmazonFbaResurvey> {

    /**
     * 重测明细
     *
     * @param site      站点
     * @param sellerSku 展示码
     * @return @return {@link List }<{@link SomAmazonFbaResurveyVo }>
     * @author: 王帅杰
     * @date: 2023-03-16 04:57:03
     */
    List<SomAmazonFbaResurveyVo> resurveyDetail(@Param("site")String site, @Param("sellerSku")String sellerSku);
}
