package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomOffersInfo;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomOffersInfoMapper;
import com.zielsmart.mc.vo.SomOffersInfoExtVo;
import com.zielsmart.mc.vo.SomOffersInfoPageSearchVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomOffersInfoService {

    @Resource
    private SomOffersInfoMapper somOffersInfoMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomOffersInfoVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomOffersInfoExtVo> queryByPage(SomOffersInfoPageSearchVo searchVo) {
        List<SomOffersInfoExtVo> list1 = somOffersInfoMapper.querytotalCount(searchVo);
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        DefaultPageResult<SomOffersInfoExtVo> pageResult = somOffersInfoMapper.queryByPage(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<McDictionaryInfo> infoList = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "OffersInfoStatus").select();
            List<String> sellerIdList = pageResult.getList().stream().map(m -> m.getSellerId()).distinct().collect(Collectors.toList());
            List<String> marketplaceNameList = pageResult.getList().stream().map(m -> m.getMarketplaceName()).distinct().collect(Collectors.toList());
//            List<SomOffersInfoExtVo> produtNumList = somOffersInfoMapper.queryProdutNum(sellerIdList, marketplaceNameList);
            List<SomOffersInfoExtVo> produtNumList = somOffersInfoMapper.queryProdutNum(sellerIdList);
            List<SomOffersInfo> offersInfoList = dynamicSqlManager.getMapper(SomOffersInfoMapper.class).createLambdaQuery().andIn("seller_id", sellerIdList).select();
            for (SomOffersInfoExtVo vo : pageResult.getList()) {
                infoList.stream().filter(t -> StrUtil.equalsIgnoreCase(t.getItemValue(), vo.getStatus().toString())).findFirst().ifPresent(pc -> {
                    vo.setStatusName(pc.getItemLable());
                });
                // 跟卖产品次数
                Long totalCount = offersInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getSellerId(), vo.getSellerId())).count();
                vo.setTotalCount(totalCount);
                // 第一次跟卖时间
                offersInfoList.stream().filter(f -> StrUtil.equalsIgnoreCase(f.getSellerId(), vo.getSellerId())).collect(Collectors.toList()).sort(Comparator.comparing(SomOffersInfo::getBeginSellDate));
                if (CollectionUtil.isNotEmpty(offersInfoList)) {
                    vo.setFirstSellDate(offersInfoList.get(0).getBeginSellDate());
                }
            }
            Map<String, List<SomOffersInfoExtVo>> map = pageResult.getList().stream().collect(Collectors.groupingBy(p -> String.format("%s,%s,%s,%s,%s",p.getSellerId(),p.getSellerStoreName(),p.getSellerName(),p.getMarketplaceName(),p.getStatusName())));
            pageResult.getList().clear();
            for(String key : map.keySet()){
                List<String> keyList = Arrays.asList(key.split(","));
                SomOffersInfoExtVo vo = new SomOffersInfoExtVo();
                vo.setSellerStoreName(keyList.get(1));
                vo.setSellerName(keyList.get(2));
                vo.setMarketplaceName(keyList.get(3));
                vo.setStatusName(keyList.get(4));
                // 获取该店铺下的所有asin
                // 2022.12.16与产品确认,跟卖产品数量与跟卖产品次数不按照国家维度统计
//                List<SomOffersInfoExtVo> offerList = produtNumList.stream().filter(f ->  StrUtil.equals(keyList.get(3), f.getMarketplaceName())  && StrUtil.equals(keyList.get(0), f.getSellerId())).collect(Collectors.toList());
                List<SomOffersInfoExtVo> offerList = produtNumList.stream().filter(f -> StrUtil.equals(keyList.get(0), f.getSellerId())).collect(Collectors.toList());
                List<String> asins = offerList.stream().map(m -> m.getAsin()).collect(Collectors.toList());
                int i = 0;
                for(String asin :asins){
                    long count = offerList.stream().filter(f ->StrUtil.equals(f.getAsin(), asin)).count();
                    i += count;
                }
                vo.setSellerId(keyList.get(0));
                vo.setTotalNum(Long.valueOf(i));
                vo.setFirstSellDate(map.get(key).get(0).getFirstSellDate());
                vo.setTotalCount(map.get(key).get(0).getTotalCount());
                vo.setTimeOfOfferChange(map.get(key).get(0).getTimeOfOfferChange());
                pageResult.getList().add(vo);
            }
        }
        pageResult.setTotalRow(list1.size());
        if (list1.size() == 0L) {
            pageResult.setTotalPage(1L);
        } else if (list1.size() % (long)pageRequest.getPageSize() == 0L) {
            pageResult.setTotalPage(list1.size() / (long)pageRequest.getPageSize());
        } else {
            pageResult.setTotalPage( list1.size() / (long)pageRequest.getPageSize() + 1L);
        }
        return ConvertUtils.pageConvert(pageResult, SomOffersInfoExtVo.class, searchVo);
    }

    /**
     * export
     * 导出
     * @param searchVo
     * @return {@link java.lang.String}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public String export(SomOffersInfoPageSearchVo searchVo) throws ValidateException{
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomOffersInfoExtVo> records = queryByPage(searchVo).getRecords();
        try {
            ExportParams params = new ExportParams(null, "跟卖监控");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, SomOffersInfoExtVo.class, records);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(barray);
        } catch (Exception e) {
            throw new ValidateException("导出跟卖者分析失败：" + e.getMessage());
        }
    }
}
