package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2024-11-05
 */

@Table(name = "mc.som_ali_express_sku_list")
public class SomAliExpressSkuList implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 商品ID，关联Listing表
     */
    @Column("product_id")
    private String productId;
    /**
     * SKU供货价
     */
    @Column("supply_price")
    private BigDecimal supplyPrice;
    /**
     * SKU审核状态。若当前字段为1或者为空：表示审核通过；否则，都表示审核未通过；当主表的商品供货价审核不通过的时候才会存在，用来区分哪个sku的供货价审核不通过，与 suggest_price 和suggest_note 配合使用。
     */
    @Column("sku_audit_status")
    private Integer skuAuditStatus;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * SKU ID
     */
    @Column("sku_id")
    private String skuId;
    /**
     * 属性
     */
    @Column("variation")
    private String variation;
    /**
     * 若审核未通过的情况下，平台给的“建议供货价”
     */
    @Column("suggest_price")
    private BigDecimal suggestPrice;
    /**
     * 若审核未通过的情况下，平台给的“建议标签”
     */
    @Column("suggest_note")
    private String suggestNote;
    /**
     * SKU是否在售，active在售 inactive不在售
     */
    @Column("status")
    private String status;
    /**
     * 当前生效的供货价
     */
    @Column("effective_supply_price")
    private BigDecimal effectiveSupplyPrice;
    /**
     * SKU总库存
     */
    @Column("sku_stock")
    private Integer skuStock;
    /**
     * 仓库维度的库存，JSON字符串
     */
    @Column("sku_warehouse_stock_list")
    private Object skuWarehouseStockList;
    /**
     * 下载时间
     */
    @Column("download_time")
    private Date downloadTime;

    public SomAliExpressSkuList() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 商品ID，关联Listing表
     *
     * @return
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 商品ID，关联Listing表
     *
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * SKU供货价
     *
     * @return
     */
    public BigDecimal getSupplyPrice() {
        return supplyPrice;
    }

    /**
     * SKU供货价
     *
     * @param supplyPrice
     */
    public void setSupplyPrice(BigDecimal supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    /**
     * SKU审核状态。若当前字段为1或者为空：表示审核通过；否则，都表示审核未通过；当主表的商品供货价审核不通过的时候才会存在，用来区分哪个sku的供货价审核不通过，与 suggest_price 和suggest_note 配合使用。
     *
     * @return
     */
    public Integer getSkuAuditStatus() {
        return skuAuditStatus;
    }

    /**
     * SKU审核状态。若当前字段为1或者为空：表示审核通过；否则，都表示审核未通过；当主表的商品供货价审核不通过的时候才会存在，用来区分哪个sku的供货价审核不通过，与 suggest_price 和suggest_note 配合使用。
     *
     * @param skuAuditStatus
     */
    public void setSkuAuditStatus(Integer skuAuditStatus) {
        this.skuAuditStatus = skuAuditStatus;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * SKU ID
     *
     * @return
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * SKU ID
     *
     * @param skuId
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    /**
     * 属性
     *
     * @return
     */
    public String getVariation() {
        return variation;
    }

    /**
     * 属性
     *
     * @param variation
     */
    public void setVariation(String variation) {
        this.variation = variation;
    }

    /**
     * 若审核未通过的情况下，平台给的“建议供货价”
     *
     * @return
     */
    public BigDecimal getSuggestPrice() {
        return suggestPrice;
    }

    /**
     * 若审核未通过的情况下，平台给的“建议供货价”
     *
     * @param suggestPrice
     */
    public void setSuggestPrice(BigDecimal suggestPrice) {
        this.suggestPrice = suggestPrice;
    }

    /**
     * 若审核未通过的情况下，平台给的“建议标签”
     *
     * @return
     */
    public String getSuggestNote() {
        return suggestNote;
    }

    /**
     * 若审核未通过的情况下，平台给的“建议标签”
     *
     * @param suggestNote
     */
    public void setSuggestNote(String suggestNote) {
        this.suggestNote = suggestNote;
    }

    /**
     * SKU是否在售，active在售 inactive不在售
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * SKU是否在售，active在售 inactive不在售
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 当前生效的供货价
     *
     * @return
     */
    public BigDecimal getEffectiveSupplyPrice() {
        return effectiveSupplyPrice;
    }

    /**
     * 当前生效的供货价
     *
     * @param effectiveSupplyPrice
     */
    public void setEffectiveSupplyPrice(BigDecimal effectiveSupplyPrice) {
        this.effectiveSupplyPrice = effectiveSupplyPrice;
    }

    /**
     * SKU总库存
     *
     * @return
     */
    public Integer getSkuStock() {
        return skuStock;
    }

    /**
     * SKU总库存
     *
     * @param skuStock
     */
    public void setSkuStock(Integer skuStock) {
        this.skuStock = skuStock;
    }

    /**
     * 仓库维度的库存，JSON字符串
     *
     * @return
     */
    public Object getSkuWarehouseStockList() {
        return skuWarehouseStockList;
    }

    /**
     * 仓库维度的库存，JSON字符串
     *
     * @param skuWarehouseStockList
     */
    public void setSkuWarehouseStockList(Object skuWarehouseStockList) {
        this.skuWarehouseStockList = skuWarehouseStockList;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getDownloadTime() {
        return downloadTime;
    }

    /**
     * 下载时间
     *
     * @param downloadTime
     */
    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

}
