package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomButOffers;
import com.zielsmart.mc.repository.mapper.SomButOffersMapper;
import com.zielsmart.mc.util.InventoryCalcUtil;
import com.zielsmart.mc.vo.SomButOffersPageSearchVo;
import com.zielsmart.mc.vo.SomButOffersVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-04-01 16:39:51
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomButOffersService {
    
    @Resource
    private SomButOffersMapper somButOffersMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomButOffersVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomButOffersVo> queryByPage(SomButOffersPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomButOffersVo> pageResult = somButOffersMapper.queryByPage(searchVo, pageRequest);
        if(!pageResult.getList().isEmpty()){
            List<String> skuList = null;
            if(pageResult.getList().size()<200){
                skuList = pageResult.getList().stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("BUT.fr",skuList);
            calcInventory(pageResult.getList(), inventoryMap);
        }

        return ConvertUtils.pageConvert(pageResult, SomButOffersVo.class, searchVo);
    }

    private static void calcInventory(List<SomButOffersVo> list, Map<String, BigDecimal> inventoryMap) {
        for (SomButOffersVo somButOffersVo : list) {
            BigDecimal inventory = inventoryMap.getOrDefault(somButOffersVo.getSku()+"INVENTORY", BigDecimal.ZERO);
            BigDecimal dms = inventoryMap.getOrDefault(somButOffersVo.getSku()+"DMS", BigDecimal.ZERO);
            //减去安全库存
            int stock = inventory.intValue() - Math.max(dms.divide(BigDecimal.valueOf(3),2,  RoundingMode.DOWN).intValue(), somButOffersVo.getSafetyStock());
            somButOffersVo.setStock(Math.max(stock, 0));
        }
    }

    /**
     * save
     * 添加
     * @param somButOffersVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomButOffersVo somButOffersVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somButOffersVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if(StrUtil.isBlank(somButOffersVo.getSellerSku()) || somButOffersVo.getPrice()==null || StrUtil.isBlank(somButOffersVo.getLogistic()) || somButOffersVo.getLeadtime()==null){
            throw new ValidateException("必填项未填写，请检查数据");
        }
        somButOffersVo.setModifyNum(tokenUser.getJobNumber());
        somButOffersVo.setModifyName(tokenUser.getUserName());
        somButOffersVo.setModifyTime(DateTime.now().toJdkDate());

        if (somButOffersVo.getDiscountPrice() != null) {
            if(somButOffersVo.getDiscountStartDate()==null || somButOffersVo.getDiscountEndDate()==null){
                throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
            }

            if(somButOffersVo.getDiscountStartDate().after(somButOffersVo.getDiscountEndDate())){
                throw new ValidateException("展示码"+somButOffersVo.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
            }
        }

        if (StrUtil.isBlank(somButOffersVo.getAid())) {
            //插入
            //判断展示码是否存在
            long count = somButOffersMapper.createLambdaQuery().andEq("seller_sku", somButOffersVo.getSellerSku()).count();
            if (count > 0) {
                throw new ValidateException("展示码已存在，请刷新界面检查数据");
            }
            somButOffersVo.setAid(IdUtil.fastSimpleUUID());
            somButOffersMapper.insert(ConvertUtils.beanConvert(somButOffersVo, SomButOffers.class));
        }else {
            //更新
            somButOffersMapper.updateById(ConvertUtils.beanConvert(somButOffersVo, SomButOffers.class));
        }

    }


    /**
     * 将查询结果导出为Excel文件，并返回文件的Base64编码字符串
     *
     * @param searchVo 查询条件对象
     * @return 导出的Excel文件的Base64编码字符串，如果没有数据或导出失败则返回null
     */
    public String export(SomButOffersPageSearchVo searchVo) {
        List<SomButOffersVo> records = somButOffersMapper.exportExcel(searchVo);
        if (!records.isEmpty()) {
            //计算库存
            List<String> skuList = null;
            if(records.size()<200){
                skuList = records.stream().map(x -> x.getSku()).distinct().collect(Collectors.toList());
            }
            Map<String, BigDecimal> inventoryMap = InventoryCalcUtil.calcInventory("BUT.fr",skuList);
            calcInventory(records, inventoryMap);

            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "But fr Offers管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomButOffersVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomButOffersVo> list, TokenUserInfo tokenUser) throws ValidateException {
        //校验"展示码","划线价格", "折扣价格","折扣开始时间","折扣结束时间","物流方式", "备货时间","备注" 是否为空
        HashSet<String> sellerSet = new HashSet<>();
        List<SomButOffers> existSellerSku = somButOffersMapper.createLambdaQuery().select("seller_sku","aid");
        Map<String, String> existSellerSkuMap = existSellerSku.stream().collect(Collectors.toMap(SomButOffers::getSellerSku, x -> x.getAid()));
        List<SomButOffers> allButListing = somButOffersMapper.allButListing();
        //转map
        Map<String, SomButOffers> allButListingMap = allButListing.stream().collect(Collectors.toMap(SomButOffers::getSellerSku, x -> x));
        //校验展示码是否存在
        String notExistSellerSkus = list.stream().map(SomButOffersVo::getSellerSku).filter(sellerSku -> !allButListingMap.containsKey(sellerSku)).collect(Collectors.joining(","));
        if(StrUtil.isNotBlank(notExistSellerSkus)){
            throw new ValidateException("展示码"+notExistSellerSkus+"不存在，批量导入失败!");
        }

        List<SomButOffersVo> insertList = new ArrayList<>();
        List<SomButOffersVo> updateList = new ArrayList<>();

        for (SomButOffersVo item : list) {
            if(!sellerSet.add(item.getSellerSku())){
                throw new ValidateException(" 展示码"+item.getSellerSku()+"重复，批量导入失败");
            }
            if(StrUtil.isBlank(item.getSellerSku()) || item.getPrice()==null || StrUtil.isBlank(item.getLogistic()) || item.getLeadtime()==null){
                throw new ValidateException("必填项未填写，批量导入失败");
            }
            if (item.getDiscountPrice() != null) {
                if(item.getDiscountStartDate()==null || item.getDiscountEndDate()==null){
                    throw new ValidateException("折扣价格已填写，折扣开始时间和结束时间必须填写");
                }
                if(item.getPrice().compareTo(item.getDiscountPrice())<=0){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“划线价格>折扣价格”规则，批量导入失败！");
                }
                if(item.getDiscountStartDate().after(item.getDiscountEndDate())){
                    throw new ValidateException("展示码"+item.getSellerSku()+" 不符合“折扣开始时间≤折扣结束时间“规则，批量导入失败！");
                }
            }
            if(item.getDiscountStartDate()!=null || item.getDiscountEndDate()!=null){
                if (item.getDiscountPrice() == null) {
                    throw new ValidateException("折扣开始时间和结束时间已填写，折扣价格必须填写");
                }
            }

            if (existSellerSkuMap.containsKey(item.getSellerSku())) {
                //执行更新操作
                updateList.add(item);
            }else {
                //执行插入操作
                item.setAid(IdUtil.fastSimpleUUID());
                insertList.add(item);
            }
            item.setModifyNum(tokenUser.getJobNumber());
            item.setModifyName(tokenUser.getUserName());
            item.setModifyTime(DateTime.now().toJdkDate());
        }
        if(!insertList.isEmpty()){
            somButOffersMapper.insertBatch(ConvertUtils.listConvert(insertList, SomButOffers.class));
        }
        if(!updateList.isEmpty()){
            somButOffersMapper.updateBatch(ConvertUtils.listConvert(updateList, SomButOffers.class));
        }
    }

}
