package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 * VC DOTO表
 * gen by 代码生成器 2025-05-07
 */

@Table(name = "mc.som_vc_dotd")
public class SomVcDotd implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 账号名称/大分类
     */
    @Column("account_name")
    private String accountName;
    /**
     * 供应商编码
     */
    @Column("vendor_code")
    private String vendorCode;
    /**
     * 与亚马逊的合作模式 10 DDP 20 DI 30 DF
     */
    @Column("cooperation_model")
    private Integer cooperationModel;
    /**
     * 自发类型：10 AOC 20 VOC
     */
    @Column("dropship_type")
    private Integer dropshipType;
    /**
     * 营销活动类型：10 Price Discount 20 Lightning Deal 30 Best Deal 40 Points promotion 50 DOTD
     */
    @Column("promotion_type")
    private Integer promotionType;
    /**
     * 大促类型
     */
    @Column("campaign_type")
    private Integer campaignType;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 产品SKU编码
     */
    @Column("sku")
    private String sku;
    /**
     * 建议零售价，RRP
     */
    @Column("recommended_retail_price")
    private BigDecimal recommendedRetailPrice;
    /**
     *
     */
    @Column("front_sell_price")
    private BigDecimal frontSellPrice;
    /**
     * 供货价，预留
     */
    @Column("cost_price")
    private BigDecimal costPrice;
    /**
     * 折扣百分比
     */
    @Column("discount")
    private BigDecimal discount;
    /**
     * 秒杀价
     */
    @Column("deal_price")
    private BigDecimal dealPrice;
    /**
     * Funding
     */
    @Column("per_unit_funding")
    private BigDecimal perUnitFunding;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 评分
     */
    @Column("score")
    private BigDecimal score;
    /**
     * 库存可销售天数
     */
    @Column("stock_sale_days")
    private Integer stockSaleDays;
    /**
     * 30天dms
     */
    @Column("dms_last_30day")
    private BigDecimal dmsLast30day;
    /**
     * 起始日期
     */
    @Column("start_date")
    private Date startDate;
    /**
     * 截止日期
     */
    @Column("end_date")
    private Date endDate;
    /**
     * 状态 10 草稿 11 提报中 19 提报失败 20 即将开始 30 进行中 39 需要关注 40 已结束 99 已取消
     */
    @Column("status")
    private Integer status;
    /**
     * 需要关注时，错误提示信息
     */
    @Column("need_attention_reason")
    private String needAttentionReason;
    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     */
    @Column("apply_reason")
    private Integer applyReason;
    /**
     * 自定义申请原因
     */
    @Column("custom_reason")
    private String customReason;
    /**
     * 提交失败原因
     */
    @Column("submission_failure_remark")
    private String submissionFailureRemark;
    /**
     * 业务组
     */
    @Column("sales_group_code")
    private String salesGroupCode;
    /**
     * 业务组名称
     */
    @Column("sales_group_name")
    private String salesGroupName;
    /**
     * 销售负责人工号
     */
    @Column("sales_group_empt_code")
    private String salesGroupEmptCode;
    /**
     * 销售负责人姓名
     */
    @Column("sales_group_empt_name")
    private String salesGroupEmptName;
    /**
     * 业务助理工号
     */
    @Column("operation_empt_code")
    private String operationEmptCode;
    /**
     * 业务助理姓名
     */
    @Column("operation_empt_name")
    private String operationEmptName;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 最后修改人工号
     */
    @Column("modify_num")
    private String modifyNum;
    /**
     * 最后修改人姓名
     */
    @Column("modify_name")
    private String modifyName;
    /**
     * 修改时间
     */
    @Column("modify_time")
    private Date modifyTime;

    public SomVcDotd() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 账号名称/大分类
     *
     * @return
     */
    public String getAccountName() {
        return accountName;
    }

    /**
     * 账号名称/大分类
     *
     * @param accountName
     */
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    /**
     * 供应商编码
     *
     * @return
     */
    public String getVendorCode() {
        return vendorCode;
    }

    /**
     * 供应商编码
     *
     * @param vendorCode
     */
    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    /**
     * 与亚马逊的合作模式 10 DDP 20 DI 30 DF
     *
     * @return
     */
    public Integer getCooperationModel() {
        return cooperationModel;
    }

    /**
     * 与亚马逊的合作模式 10 DDP 20 DI 30 DF
     *
     * @param cooperationModel
     */
    public void setCooperationModel(Integer cooperationModel) {
        this.cooperationModel = cooperationModel;
    }

    /**
     * 自发类型：10 AOC 20 VOC
     *
     * @return
     */
    public Integer getDropshipType() {
        return dropshipType;
    }

    /**
     * 自发类型：10 AOC 20 VOC
     *
     * @param dropshipType
     */
    public void setDropshipType(Integer dropshipType) {
        this.dropshipType = dropshipType;
    }

    /**
     * 营销活动类型：10 Price Discount 20 Lightning Deal 30 Best Deal 40 Points promotion 50 DOTD
     *
     * @return
     */
    public Integer getPromotionType() {
        return promotionType;
    }

    /**
     * 营销活动类型：10 Price Discount 20 Lightning Deal 30 Best Deal 40 Points promotion 50 DOTD
     *
     * @param promotionType
     */
    public void setPromotionType(Integer promotionType) {
        this.promotionType = promotionType;
    }

    /**
     * 大促类型
     *
     * @return
     */
    public Integer getCampaignType() {
        return campaignType;
    }

    /**
     * 大促类型
     *
     * @param campaignType
     */
    public void setCampaignType(Integer campaignType) {
        this.campaignType = campaignType;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 产品SKU编码
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * 产品SKU编码
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 建议零售价，RRP
     *
     * @return
     */
    public BigDecimal getRecommendedRetailPrice() {
        return recommendedRetailPrice;
    }

    /**
     * 建议零售价，RRP
     *
     * @param recommendedRetailPrice
     */
    public void setRecommendedRetailPrice(BigDecimal recommendedRetailPrice) {
        this.recommendedRetailPrice = recommendedRetailPrice;
    }

    /**
     * 供货价，预留
     *
     * @return
     */
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    /**
     * 供货价，预留
     *
     * @param costPrice
     */
    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    /**
     * 折扣百分比
     *
     * @return
     */
    public BigDecimal getDiscount() {
        return discount;
    }

    /**
     * 折扣百分比
     *
     * @param discount
     */
    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    /**
     * 秒杀价
     *
     * @return
     */
    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    /**
     * 秒杀价
     *
     * @param dealPrice
     */
    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    /**
     * Funding
     *
     * @return
     */
    public BigDecimal getPerUnitFunding() {
        return perUnitFunding;
    }

    /**
     * Funding
     *
     * @param perUnitFunding
     */
    public void setPerUnitFunding(BigDecimal perUnitFunding) {
        this.perUnitFunding = perUnitFunding;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 评分
     *
     * @return
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 评分
     *
     * @param score
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 库存可销售天数
     *
     * @return
     */
    public Integer getStockSaleDays() {
        return stockSaleDays;
    }

    /**
     * 库存可销售天数
     *
     * @param stockSaleDays
     */
    public void setStockSaleDays(Integer stockSaleDays) {
        this.stockSaleDays = stockSaleDays;
    }

    /**
     * 30天dms
     *
     * @return
     */
    public BigDecimal getDmsLast30day() {
        return dmsLast30day;
    }

    /**
     * 30天dms
     *
     * @param dmsLast30day
     */
    public void setDmsLast30day(BigDecimal dmsLast30day) {
        this.dmsLast30day = dmsLast30day;
    }

    /**
     * 起始日期
     *
     * @return
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 起始日期
     *
     * @param startDate
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 截止日期
     *
     * @return
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 截止日期
     *
     * @param endDate
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 状态 10 草稿 11 提报中 19 提报失败 20 即将开始 30 进行中 39 需要关注 40 已结束 99 已取消
     *
     * @return
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态 10 草稿 11 提报中 19 提报失败 20 即将开始 30 进行中 39 需要关注 40 已结束 99 已取消
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 需要关注时，错误提示信息
     *
     * @return
     */
    public String getNeedAttentionReason() {
        return needAttentionReason;
    }

    /**
     * 需要关注时，错误提示信息
     *
     * @param needAttentionReason
     */
    public void setNeedAttentionReason(String needAttentionReason) {
        this.needAttentionReason = needAttentionReason;
    }

    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     *
     * @return
     */
    public Integer getApplyReason() {
        return applyReason;
    }

    /**
     * 申请原因，枚举： 10 提升排名 20 清货 30 稳排名 99 自定义
     *
     * @param applyReason
     */
    public void setApplyReason(Integer applyReason) {
        this.applyReason = applyReason;
    }

    /**
     * 自定义申请原因
     *
     * @return
     */
    public String getCustomReason() {
        return customReason;
    }

    /**
     * 自定义申请原因
     *
     * @param customReason
     */
    public void setCustomReason(String customReason) {
        this.customReason = customReason;
    }

    /**
     * 业务组
     *
     * @return
     */
    public String getSalesGroupCode() {
        return salesGroupCode;
    }

    /**
     * 业务组
     *
     * @param salesGroupCode
     */
    public void setSalesGroupCode(String salesGroupCode) {
        this.salesGroupCode = salesGroupCode;
    }

    /**
     * 业务组名称
     *
     * @return
     */
    public String getSalesGroupName() {
        return salesGroupName;
    }

    /**
     * 业务组名称
     *
     * @param salesGroupName
     */
    public void setSalesGroupName(String salesGroupName) {
        this.salesGroupName = salesGroupName;
    }

    /**
     * 销售负责人工号
     *
     * @return
     */
    public String getSalesGroupEmptCode() {
        return salesGroupEmptCode;
    }

    /**
     * 销售负责人工号
     *
     * @param salesGroupEmptCode
     */
    public void setSalesGroupEmptCode(String salesGroupEmptCode) {
        this.salesGroupEmptCode = salesGroupEmptCode;
    }

    /**
     * 销售负责人姓名
     *
     * @return
     */
    public String getSalesGroupEmptName() {
        return salesGroupEmptName;
    }

    /**
     * 销售负责人姓名
     *
     * @param salesGroupEmptName
     */
    public void setSalesGroupEmptName(String salesGroupEmptName) {
        this.salesGroupEmptName = salesGroupEmptName;
    }

    /**
     * 业务助理工号
     *
     * @return
     */
    public String getOperationEmptCode() {
        return operationEmptCode;
    }

    /**
     * 业务助理工号
     *
     * @param operationEmptCode
     */
    public void setOperationEmptCode(String operationEmptCode) {
        this.operationEmptCode = operationEmptCode;
    }

    /**
     * 业务助理姓名
     *
     * @return
     */
    public String getOperationEmptName() {
        return operationEmptName;
    }

    /**
     * 业务助理姓名
     *
     * @param operationEmptName
     */
    public void setOperationEmptName(String operationEmptName) {
        this.operationEmptName = operationEmptName;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改人工号
     *
     * @return
     */
    public String getModifyNum() {
        return modifyNum;
    }

    /**
     * 最后修改人工号
     *
     * @param modifyNum
     */
    public void setModifyNum(String modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 最后修改人姓名
     *
     * @return
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 最后修改人姓名
     *
     * @param modifyName
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    /**
     * 修改时间
     *
     * @return
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getSubmissionFailureRemark() {
        return submissionFailureRemark;
    }

    public void setSubmissionFailureRemark(String submissionFailureRemark) {
        this.submissionFailureRemark = submissionFailureRemark;
    }

    public BigDecimal getFrontSellPrice() {
        return frontSellPrice;
    }

    public void setFrontSellPrice(BigDecimal frontSellPrice) {
        this.frontSellPrice = frontSellPrice;
    }
}
