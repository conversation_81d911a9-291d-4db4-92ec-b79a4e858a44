package com.zielsmart.mc.controller;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.service.SomVcLdAndBdService;
import com.zielsmart.mc.vo.SomVcLdAndBdImportVo;
import com.zielsmart.mc.vo.SomVcLdAndBdPageSearchVo;
import com.zielsmart.mc.vo.SomVcLdAndBdVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
@RequestMapping(value = "/somVcLdAndBd", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC LD&BD")
public class SomVcLdAndBdController extends BasicController{

    @Resource
    SomVcLdAndBdService somVcLdAndBdService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomVcLdAndBdVo>> queryByPage(@RequestBody SomVcLdAndBdPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somVcLdAndBdService.queryByPage(searchVo));
    }

    @Operation(summary = "新增或编辑")
    @PostMapping(value = "/addOrEdit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomVcLdAndBdVo somVcLdAndBdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcLdAndBdService.addOrEdit(somVcLdAndBdVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomVcLdAndBdVo somVcLdAndBdVo) throws ValidateException {
        somVcLdAndBdService.delete(somVcLdAndBdVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> cancel(@RequestBody SomVcLdAndBdVo somVcLdAndBdVo) throws ValidateException {
        somVcLdAndBdService.cancel(somVcLdAndBdVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomVcLdAndBdPageSearchVo searchVo){
        String data = somVcLdAndBdService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/VCLD&BDImportTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // sheet页
        importParams.setSheetNum(0);
        // 导入字段
        String[] arr = {"站点", "账号名称", "供应商编码", "活动名称", "秒杀类型", "大促类型",
                "提报方式", "活动起始日期", "活动截止日期", "展示码", "ASIN", "折扣比例", "活动价格",
                "承诺商品数量", "评分", "申请原因", "自定义原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcLdAndBdImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcLdAndBdImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somVcLdAndBdService.importExcel(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    @Operation(summary = "反馈提报结果")
    @PostMapping(value = "/feedbackSubmitResult")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> feedbackSubmitResult(@RequestBody SomVcLdAndBdVo somVcLdAndBdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcLdAndBdService.feedbackSubmitResult(somVcLdAndBdVo, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "下载批量反馈提报结果导入模板")
    @GetMapping(value = "/downloadBatchFeedbackSubmitResult")
    public String downloadBatchFeedbackSubmitResult() {
        return "forward:/static/excel/VCLD&BDBatchFeedbackImportTemplate.xlsx";
    }

    @Operation(summary = "批量反馈提报结果")
    @PostMapping(value = "/batchFeedbackSubmitResult", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchFeedbackSubmitResult(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"活动名称", "提报结果", "失败原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomVcLdAndBdImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomVcLdAndBdImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somVcLdAndBdService.batchFeedbackSubmitResult(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    @Operation(summary = "补充大促起止日期")
    @PostMapping(value = "/supplyPromotionDate")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> supplyPromotionDate(@RequestBody SomVcLdAndBdVo somVcLdAndBdVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somVcLdAndBdService.supplyPromotionDate(somVcLdAndBdVo, tokenUser);
        return ResultVo.ofSuccess();
    }
}
