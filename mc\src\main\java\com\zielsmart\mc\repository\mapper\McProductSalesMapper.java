package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo;
import com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchExtVo;
import com.zielsmart.mc.vo.zbpm.ZBPMWhiteListSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2021-07-15
 */

@SqlResource("mcProductSales")
public interface McProductSalesMapper extends BaseMapper<McProductSales> {

    /**
     * queryBySiteAndSku
     * 根据站点、sku模糊查询产品销售视图
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McProductSalesVo>}
     * <AUTHOR>
     * @history
     */
    List<McProductSalesVo> queryBySiteAndSku(@Param("searchVo") McProductSalesSearchVo searchVo);

    /**
     * queryByPage
     * 查询可售库存报表
     *
     * @param searchVo
     * @param isEu
     * @return {@link java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McSaleInventoryReportVo> queryByPage(@Param("searchVo") McSaleInventoryReportSearchVo searchVo, @Param("isEu") String isEu);

    List<McSaleInventoryReportVo> queryPanEuByPage(@Param("searchVo") McSaleInventoryReportSearchVo searchVo, @Param("isEu") String isEu);

    /**
     * queryNaSFPProduct
     * 查询美线SFP可售库存报表
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McSaleInventoryReportVo> queryNaSFPProduct(@Param("searchVo") McSaleInventoryReportSearchVo searchVo);


    /**
     * queryNotNaSFPProduct
     * 查询非美线SFP可售库存报表
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McSaleInventoryReportVo> queryNotNaSFPProduct(@Param("searchVo") McSaleInventoryReportSearchVo searchVo);

    /**
     * 根据平台站点sku查询需要清仓的数据，再差集已清仓的数据
     *
     * @param platform
     * @param sku
     * @param sites
     * @return
     */
    List<McProductSales> findClearProduct(@Param("platform") String platform, @Param("sku") String sku, @Param("sites") List<String> sites);

    /**
     * querySaleableStockReport
     * 查询可售库存报表(多仓)
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McSaleInventoryReportMultipleVo> querySaleableStockReport(@Param("searchVo") McSaleInventoryReportMultipleSearchVo searchVo);

    /**
     * queryWayfairSaleInventoryReport
     * 查询wayfair可售库存报表
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McSaleInventoryReportMultipleVo>}
     * <AUTHOR>
     * @history
     */
    List<McWayfairSaleInventoryReportVo> queryWayfairSaleInventoryReport(@Param("searchVo") McWayfairSaleInventoryReportSearchVo searchVo);

    /**
     * queryVCSaleInventoryReport
     * 查询VC可售库存报表
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWayfairSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McVCSaleInventoryReportVo> queryVCSaleInventoryReport(@Param("searchVo") McVCSaleInventoryReportSearchVo searchVo);

    /**
     * queryWalmartSaleInventoryReport
     * 查询walmart可售库存报表
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McWalmartSaleInventoryReportVo>}
     * <AUTHOR>
     * @history
     */
    List<McWalmartSaleInventoryReportVo> queryWalmartSaleInventoryReport(@Param("searchVo") McWalmartSaleInventoryReportSearchVo searchVo);

    /**
     * queryNotExists
     * 查询不存在于库存白名单的数据
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListVo> queryNotExistsWhiteList(@Param("searchVo") ZBPMWhiteListSearchVo searchVo);

    /**
     * queryExistsWhiteList
     * 查询存在于库存白名单的数据
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListVo> queryExistsWhiteList(@Param("searchVo") ZBPMWhiteListSearchVo searchVo);

    /**
     * queryByCountryChanel
     * 根据国家发货方式和sku前三位查询产品销售视图
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McProductSalesVo>}
     * <AUTHOR>
     * @history
     */
    List<McProductSalesVo> queryByCountryChanel(@Param("searchVo") McProductSalesSearchVo searchVo);

    /**
     * queryListingPrice
     * 查询平台价
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomListingPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomListingPriceExtVo> pageSearchListingPrice(@Param("searchVo") SomListingPricePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * querySubmitInfo
     * 根据平台站点展示码查询流程信息
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomListingPriceExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomListingPriceExtVo> querySubmitInfo(@Param("searchVo") SomListingPriceVo searchVo);

    /**
     * querySubmitInfo
     * 根据平台站点sku展示码查询流程信息
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomListingPriceExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomListingPriceExtVo> querySubmitInfoList(@Param("searchVo") SomStandardPriceSearchVo searchVo);

    /**
     * queryProductSales
     * 根据站点展示码查询产品销售视图(业务组/业务负责人/业务助理)
     *
     * @param siteList
     * @param sellerSkus
     * @return {@link java.util.List<com.zielsmart.mc.vo.McProductSalesExtVo>}
     * <AUTHOR>
     * @history
     */
    List<McProductSalesExtVo> queryProductSales(@Param("sites") List<String> siteList, @Param("sellerSkus") List<String> sellerSkus);

    /**
     * getProductSales
     * 根据搜索条件查询产品销售视图(业务组/业务负责人/业务助理)
     * 使用新表
     * @param searchVo McProductSalesSearchVo
     * @return
     */
    List<McProductSalesVo> getProductSales(@Param("searchVo") McProductSalesSearchVo searchVo);

    /**
     * queryNotExistsListing
     * 根据市场、平台和产品编码查询不存在于白名单Listing
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListExtVo> queryNotExistsListing(@Param("searchVo") ZBPMWhiteListSearchExtVo searchVo);

    /**
     * queryExistsListing
     * 根据市场、平台和产品编码查询存在于白名单Listing
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.zbpm.ZBPMStockWhiteListExtVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMStockWhiteListExtVo> queryExistsListing(@Param("searchVo") ZBPMWhiteListSearchExtVo searchVo);

    /**
     * querySetProducts
     * 根据平台、站点查询组合组套产品
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.McProductSalesVo>}
     * <AUTHOR>
     * @history
     */
    List<McProductSalesVo> querySetProducts(@Param("searchVo") McProductSalesSearchVo searchVo);

    List<ZBPMStockWhiteListExtVo> queryExistsVcListing(@Param("searchVo") ZBPMWhiteListSearchExtVo searchVo);

    List<ZBPMStockWhiteListExtVo> queryNotExistsVcListing(@Param("searchVo") ZBPMWhiteListSearchExtVo searchVo);

    /**
     * 根据参数查询产品销售视图，注：sql中不写死固定值，一切由前端传参决定
     *
     * @param searchVo 入参
     * @return List<McProductSalesVo>
     */
    List<McProductSalesVo> query(@Param("searchVo") McProductSalesSearchVo searchVo);

    /**
     * 获取展示码列表，会去重
     *
     * @param searchVo 入参
     * @return 展示码集合
     */
    List<String> querySellerSkuCode(@Param("searchVo") McProductSalesSearchVo searchVo);

    /**
     * 获取展示码列表，带分页，去重
     *
     * @param searchVo 入参
     * @return 展示码集合
     */
    PageResult<SomVcPromotionSellerSkuInfoVo> queryByPageSellerSkuCode(@Param("searchVo") SomVcPromotionSearchVo searchVo, PageRequest pageRequest);
}
