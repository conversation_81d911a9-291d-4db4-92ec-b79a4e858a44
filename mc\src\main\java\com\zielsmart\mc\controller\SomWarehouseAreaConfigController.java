package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomWarehouseAreaConfigService;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWarehouseAreaConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somWarehouseAreaConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = " 仓库区域配置管理")
public class SomWarehouseAreaConfigController extends BasicController{

    @Resource
    SomWarehouseAreaConfigService somWarehouseAreaConfigService;

    /**
     * save
     *
     * @param somWarehouseAreaConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/修改")
    @PostMapping(value = "/saveOrUpdate")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWarehouseAreaConfigVo somWarehouseAreaConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWarehouseAreaConfigService.save(somWarehouseAreaConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * query
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomWarehouseAreaConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomWarehouseAreaConfigVo>> query(@RequestBody SomWarehouseAreaConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWarehouseAreaConfigService.query(searchVo));
    }

    /**
     * delete
     *
     * @param vo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWarehouseAreaConfigPageSearchVo vo) throws ValidateException {
        somWarehouseAreaConfigService.delete(vo);
        return ResultVo.ofSuccess(null);
    }
}
