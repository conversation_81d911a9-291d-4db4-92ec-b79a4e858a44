package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
/*
* 亚马逊运费价格表
* gen by 代码生成器 2024-11-13
*/

@Table(name="mc.som_merchant_shipping_fee")
public class SomMerchantShippingFee implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 亚马逊运费
	 */
	@Column("amazon_fee")
	private BigDecimal amazonFee ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 运费模板名称
	 */
	@Column("merchant_shipping_group")
	private String merchantShippingGroup ;
	/**
	 * 最后修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomMerchantShippingFee() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 亚马逊运费
	*@return
	*/
	public BigDecimal getAmazonFee(){
		return  amazonFee;
	}
	/**
	* 亚马逊运费
	*@param  amazonFee
	*/
	public void setAmazonFee(BigDecimal amazonFee ){
		this.amazonFee = amazonFee;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}

	public String getMerchantShippingGroup() {
		return merchantShippingGroup;
	}

	public void setMerchantShippingGroup(String merchantShippingGroup) {
		this.merchantShippingGroup = merchantShippingGroup;
	}

	/**
	* 最后修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 最后修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 最后修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 最后修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
