package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAlertNotificationRulePageSearchVo;
import com.zielsmart.mc.vo.SomAlertNotificationRuleVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-02-15
*/

@SqlResource("somAlertNotificationRule")
public interface SomAlertNotificationRuleMapper extends BaseMapper<SomAlertNotificationRule> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAlertNotificationRuleVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAlertNotificationRuleVo> queryByPage(@Param("searchVo")SomAlertNotificationRulePageSearchVo searchVo, PageRequest pageRequest);

    List<String> findAllAlertNotificationRuleName(@Param("ruleType") String ruleType);
}
