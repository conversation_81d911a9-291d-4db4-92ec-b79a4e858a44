package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomButOffers;
import com.zielsmart.mc.vo.SomButOffersPageSearchVo;
import com.zielsmart.mc.vo.SomButOffersVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-04-01
*/

@SqlResource("somButOffers")
public interface SomButOffersMapper extends BaseMapper<SomButOffers> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomButOffersVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomButOffersVo> queryByPage(@Param("searchVo")SomButOffersPageSearchVo searchVo, PageRequest pageRequest);

    List<SomButOffersVo> exportExcel(@Param("searchVo")SomButOffersPageSearchVo searchVo);

    List<SomButOffers> allButListing();

    default void updateBatch(@Param("updateList") List<SomButOffers> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somButOffers.updateBatch"), updateList);
    }



}
