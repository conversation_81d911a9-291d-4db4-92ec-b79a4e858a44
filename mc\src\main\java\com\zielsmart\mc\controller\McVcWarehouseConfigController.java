package com.zielsmart.mc.controller;

import cn.hutool.json.JSONObject;
import com.zielsmart.mc.service.McVcWarehouseConfigService;
import com.zielsmart.mc.vo.McVcWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McVcWarehouseConfigVo;
import com.zielsmart.mc.vo.McWarehouseConfigVo;
import com.zielsmart.mc.vo.McWayfairWarehouseConfigVo;
import com.zielsmart.mc.vo.tree.SomStorageLocationTreeVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McVcWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcVcWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "vc可售仓库配置")
public class McVcWarehouseConfigController extends BasicController {

    @Resource
    McVcWarehouseConfigService mcVcWarehouseConfigService;

    /**
     * add
     * 新增
     *
     * @param mcVcWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增")
    @PostMapping(value = "/add")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McVcWarehouseConfigVo mcVcWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcVcWarehouseConfigService.add(mcVcWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     * 查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McVcWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McVcWarehouseConfigVo>> queryByPage(@RequestBody McVcWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcVcWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * update
     * 修改
     *
     * @param mcVcWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McVcWarehouseConfigVo mcVcWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcVcWarehouseConfigService.update(mcVcWarehouseConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param mcVcWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McVcWarehouseConfigVo mcVcWarehouseConfigVo) throws ValidateException {
        mcVcWarehouseConfigService.delete(mcVcWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }


    /**
     * getConfigsBySite
     * 根据站点获取可售仓库配置信息
     * @param json
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McVcWarehouseConfigVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据站点获取可售仓库配置信息")
    @PostMapping(value = "/getConfigsBySite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McVcWarehouseConfigVo>> getConfigsBySite(@RequestBody JSONObject json) throws ValidateException {
        return ResultVo.ofSuccess(mcVcWarehouseConfigService.getConfigsBySite(json));
    }
}
