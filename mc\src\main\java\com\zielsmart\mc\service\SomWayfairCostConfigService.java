package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomWayfairCostConfig;
import com.zielsmart.mc.repository.entity.SomWayfairCostConfigSite;
import com.zielsmart.mc.repository.mapper.SomWayfairCostConfigMapper;
import com.zielsmart.mc.vo.SomWayfairCostConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairCostConfigSaveVo;
import com.zielsmart.mc.vo.SomWayfairCostConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomWayfairCostConfigService
 * @description
 * @date 2024-02-27 15:31:01
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomWayfairCostConfigService {

    @Resource
    private SomWayfairCostConfigMapper somWayfairCostConfigMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo<SomWayfairCostConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWayfairCostConfigVo> queryByPage(SomWayfairCostConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomWayfairCostConfigVo> pageResult = dynamicSqlManager.getMapper(SomWayfairCostConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            pageResult.getList().forEach(f -> {
                f.setWeight(f.getWeightRangesFrom() + "~" + f.getWeightRangesTo());
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomWayfairCostConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param saveVo 入参
     * @param tokenUser 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWayfairCostConfigSaveVo saveVo, TokenUserInfo tokenUser) throws ValidateException {
        List<SomWayfairCostConfigVo> configList = saveVo.getConfigList();
        checkWayfairCostConfigParam(saveVo);
        List<SomWayfairCostConfig> costConfigs = new ArrayList<>();
        for (SomWayfairCostConfigVo vo : configList) {
            SomWayfairCostConfig config = trans2CostConfig(vo, tokenUser);
            config.setSite(saveVo.getSite());
            costConfigs.add(config);
        }
        somWayfairCostConfigMapper.insertBatch(costConfigs);
    }

    /**
     * 类型转换
     *
     * @param vo vo
     * @return SomWayfairCostConfig
     */
    private SomWayfairCostConfig trans2CostConfig(SomWayfairCostConfigVo vo, TokenUserInfo tokenUser) {
        SomWayfairCostConfig config = new SomWayfairCostConfig();
        config.setAid(IdUtil.fastSimpleUUID());
        config.setName(vo.getName());
        config.setOperator(vo.getOperator());
        config.setWeightRangesFrom(vo.getWeightRangesFrom());
        config.setWeightRangesTo(vo.getWeightRangesTo());
        config.setTransportGirth(vo.getTransportGirth());
        config.setLength(vo.getLength());
        config.setWidth(vo.getWidth());
        config.setHeight(vo.getHeight());
        config.setDeliveryCost(vo.getDeliveryCost());
        config.setStorageCharge(vo.getStorageCharge());
        config.setRtvCost(vo.getRtvCost());
        config.setVasCost(vo.getVasCost());
        config.setCurrency(vo.getCurrency());
        config.setPriority(vo.getPriority());
        config.setCreateTime(DateTime.now().toJdkDate());
        config.setCrossChannelDeliveryCost(vo.getCrossChannelDeliveryCost());
        config.setCrossChannelDeliveryCost2Unit(vo.getCrossChannelDeliveryCost2Unit());
        config.setCrossChannelDeliveryCost3Unit(vo.getCrossChannelDeliveryCost3Unit());
        config.setCrossChannelDeliveryCost4Unit(vo.getCrossChannelDeliveryCost4Unit());
        config.setCrossChannelDeliveryCost5Unit(vo.getCrossChannelDeliveryCost5Unit());
        config.setSite(vo.getSite());
        config.setCreateNum(tokenUser.getJobNumber());
        config.setCreateName(tokenUser.getUserName());
        return config;
    }

    /**
     * 核验wayfair费用配置入参
     *
     * @param saveVo 入参
     * @throws ValidateException ex
     */
    private void checkWayfairCostConfigParam(SomWayfairCostConfigSaveVo saveVo) throws ValidateException {
        String site = saveVo.getSite();
        List<SomWayfairCostConfigVo> configList = saveVo.getConfigList();
        if (StrUtil.isBlank(site) || CollectionUtil.isEmpty(configList)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        List<String> existNames = somWayfairCostConfigMapper.createLambdaQuery()
                .andEq("site", site)
                .select()
                .stream()
                .map(SomWayfairCostConfig::getName)
                .collect(Collectors.toList());
        Set<String> saveNames = configList.stream().map(SomWayfairCostConfigVo::getName).collect(Collectors.toSet());
        if (saveNames.size() != configList.size()) {
            throw new ValidateException("您添加的" + configList.size() + "组指标配置中存在相同的Name属性，不允许出现相同的Name属性");
        }
        List<String> filterExistNames = saveNames.stream().filter(existNames::contains).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(filterExistNames)) {
            throw new ValidateException(String.join(",", filterExistNames) + "已配置费用指标，不允许重复配置");
        }
    }

    /**
     * update
     * 修改
     *
     * @param configVo 入参
     * @param tokenUser 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomWayfairCostConfigVo configVo, TokenUserInfo tokenUser) throws ValidateException {
        if (StrUtil.isEmpty(configVo.getAid())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 不允许修改 site 和 name 属性
        configVo.setSite(null);
        configVo.setName(null);
        configVo.setLastModifyNum(tokenUser.getJobNumber());
        configVo.setLastModifyName(tokenUser.getUserName());
        configVo.setLastModifyTime(DateTime.now().toJdkDate());
        somWayfairCostConfigMapper.createLambdaQuery()
                .andEq("aid", configVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(configVo, SomWayfairCostConfig.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somWayfairCostConfigVo 入参
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWayfairCostConfigVo somWayfairCostConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somWayfairCostConfigVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        somWayfairCostConfigMapper.createLambdaQuery()
                .andEq("aid", somWayfairCostConfigVo.getAid())
                .delete();
    }

    /**
     * importExcel
     * 导入文件
     *
     * @param configVoList 入参
     * @param tokenUser 当前登陆用户
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomWayfairCostConfigVo> configVoList, TokenUserInfo tokenUser) throws ValidateException {
        for (SomWayfairCostConfigVo vo : configVoList) {
            if (ObjectUtil.hasEmpty(vo.getSite(), vo.getName(), vo.getOperator(),
                    vo.getWeight(), vo.getTransportGirth(), vo.getLength(), vo.getWidth(), vo.getHeight(), vo.getDeliveryCost(),
                    vo.getStorageCharge(), vo.getRtvCost(), vo.getVasCost(), vo.getCurrency(), vo.getPriority(), vo.getCrossChannelDeliveryCost())) {
                throw new ValidateException("列数据不能为空，请检查文件");
            }
        }
        // 核验是否已配置
        List<SomWayfairCostConfig> all = somWayfairCostConfigMapper.all();
        List<String> allConfig = all.stream().map(f -> StrUtil.concat(true, f.getSite(), f.getName())).collect(Collectors.toList());
        List<SomWayfairCostConfigVo> existConfig = configVoList.stream()
                .filter(f -> allConfig.contains(StrUtil.concat(true, f.getSite(),f.getName())))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(existConfig)) {
            List<String> error = existConfig.stream().map(f -> f.getSite() + "站点" + f.getName()).collect(Collectors.toList());
            throw new ValidateException(String.join(",", error) + "已配置费用指标，不允许重复配置");
        }
        List<SomWayfairCostConfig> costConfigs = new ArrayList<>();
        for (SomWayfairCostConfigVo configVo : configVoList) {
            SomWayfairCostConfig config = trans2CostConfig(configVo, tokenUser);
            config.setWeightRangesFrom(BigDecimal.valueOf(Long.parseLong(configVo.getWeight().split("~")[0])));
            config.setWeightRangesTo(BigDecimal.valueOf(Long.parseLong(configVo.getWeight().split("~")[1])));
            costConfigs.add(config);
        }
        somWayfairCostConfigMapper.insertBatch(costConfigs);
    }

    public void fix() {
        List<SomWayfairCostConfigSite> costConfigSites = somWayfairCostConfigMapper.queryAllSite();
        Map<String, List<SomWayfairCostConfigSite>> costConfigMap = costConfigSites.stream().collect(Collectors.groupingBy(SomWayfairCostConfigSite::getCostConfigId));
        costConfigMap.forEach((costConfigId, configSites) -> {
            for (int i=0; i<configSites.size(); i++) {
                if (i==0) {
                    SomWayfairCostConfigSite configSite = configSites.get(0);
                    somWayfairCostConfigMapper.fixSite(costConfigId, configSite.getSite());
                } else {
                    // 查出旧的数据
                    List<SomWayfairCostConfig> costConfigs = somWayfairCostConfigMapper.createLambdaQuery()
                            .andEq("aid", costConfigId)
                            .select();
                    SomWayfairCostConfig config = costConfigs.get(0);
                    // 新增新数据
                    SomWayfairCostConfigSite configSite = configSites.get(i);
                    config.setAid(IdUtil.fastSimpleUUID());
                    config.setSite(configSite.getSite());
                    config.setCreateTime(DateTime.now().toJdkDate());
                    somWayfairCostConfigMapper.insert(config);
                }
            }
        });
    }
}
