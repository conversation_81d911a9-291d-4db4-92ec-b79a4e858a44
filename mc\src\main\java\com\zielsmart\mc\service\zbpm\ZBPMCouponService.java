package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomCoupon;
import com.zielsmart.mc.repository.entity.SomCouponItems;
import com.zielsmart.mc.repository.mapper.SomCouponItemsMapper;
import com.zielsmart.mc.repository.mapper.SomCouponMapper;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.vo.SomCouponExVo;
import com.zielsmart.mc.vo.SomCouponItemsVo;
import com.zielsmart.mc.vo.zbpm.ZBPMCouponVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZBPMCouponService {
    @Resource
    private SomCouponMapper couponMapper;
    @Resource
    private SomCouponItemsMapper couponItemsMapper;

    /**
     * updateCoupon
     * 更新活动状态
     *
     * @param updateVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void updateCoupon(ZBPMCouponVo updateVo) throws ValidateException {
        if (ObjectUtil.isNull(updateVo) || CollectionUtil.isEmpty(updateVo.getCouponId()) || StrUtil.isBlank(updateVo.getCouponStatus())) {
            throw new ValidateException("参数存在空值,请检查数据");
        }
        if (StrUtil.equals("未通过", updateVo.getCouponStatus()) && StrUtil.isBlank(updateVo.getJobNumber())) {
            throw new ValidateException("审核未通过时,发起人不能为空");
        }
        List<SomCoupon> couponList = couponMapper.createLambdaQuery().andIn("aid", updateVo.getCouponId()).select();
        if (CollectionUtil.isEmpty(couponList)) {
            throw new ValidateException("当前数据不存在,请检查");
        }
        Date auditTime = DateTime.now().toJdkDate();
        for (SomCoupon coupon : couponList) {
            if (StrUtil.isNotBlank(updateVo.getFailMsg())) {
                coupon.setFailureReason(updateVo.getFailMsg());
            }
            switch (updateVo.getCouponStatus()) {
                case "未通过":
                    coupon.setStatus(30);
                    break;
                case "审核通过":
                    coupon.setStatus(90);
                    break;
            }
            coupon.setAuditTime(auditTime);
        }
        if (StrUtil.equals("审核通过", updateVo.getCouponStatus())) {
            couponMapper.batchAudit(couponList);
        }else {
            couponMapper.batchAuditWithFailMsg(couponList);
        }
        if(StrUtil.equalsIgnoreCase("未通过",updateVo.getCouponStatus())){
            List<SomCouponItems> couponItemList = couponItemsMapper.createLambdaQuery().andIn("coupon_id", updateVo.getCouponId()).select();
            List<SomCouponExVo> couponExList = new ArrayList<>();
            for (SomCoupon coupon:couponList) {
                SomCouponExVo exVo = ConvertUtils.beanConvert(coupon,SomCouponExVo.class);
                List<SomCouponItems> couponItems = couponItemList.stream().filter(f -> StrUtil.equals(coupon.getAid(), f.getCouponId())).collect(Collectors.toList());
                exVo.setItemsVoList(ConvertUtils.listConvert(couponItems, SomCouponItemsVo.class));
                exVo.setTag(updateVo.getTag());
                exVo.setReason(updateVo.getFailMsg());
                if(ObjectUtil.equal(10,coupon.getCouponType())){
                    String titleExt = StrUtil.format("Save {}{} on {}",coupon.getCurrencySymbol(),coupon.getCouponDiscountAmount(),coupon.getTitle());
                    exVo.setTitleExt(titleExt);
                }else {
                    String titleExt = StrUtil.format("Save {}% on {}",coupon.getCouponDiscount(),coupon.getTitle());
                    exVo.setTitleExt(titleExt);
                }
                couponExList.add(exVo);
            }
            log.info("Coupon活动审批未通过,发送飞书提醒消息.CouponId:{},发起人:{}",updateVo.getCouponId(),updateVo.getJobNumber());
            try {
                FeiShuUtils.sendCouponNotice(updateVo.getJobNumber(),couponExList);
            } catch (Exception e) {
                log.error("Coupon活动审批未通过,发送飞书提醒消息异常{}",e.getMessage());
            }
        }
    }
}