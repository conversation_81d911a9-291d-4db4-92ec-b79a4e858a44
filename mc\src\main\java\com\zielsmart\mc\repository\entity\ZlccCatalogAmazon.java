package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Amazon类目表
 * gen by 代码生成器 2023-12-01
 */

@Table(name = "mc.zlcc_catalog_amazon")
public class ZlccCatalogAmazon implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 类目ID
     */
    @Column("browse_node_id")
    private Long browseNodeId;
    /**
     * 类目名称
     */
    @Column("browse_node_name")
    private String browseNodeName;
    /**
     * 产品类型关键词
     */
    @Column("item_type_keyword")
    private String itemTypeKeyword;
    /**
     * 产品类型定义
     */
    @Column("product_type_definitions")
    private String productTypeDefinitions;
    /**
     * 父级类目ID
     */
    @Column("browse_parent_id")
    private Long browseParentId;
    /**
     * 创建时间
     */
    @Column("create_date")
    private Date createDate;
    /**
     * 根ID
     */
    @Column("root_node_id")
    private Long rootNodeId;

    public ZlccCatalogAmazon() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 类目ID
     *
     * @return
     */
    public Long getBrowseNodeId() {
        return browseNodeId;
    }

    /**
     * 类目ID
     *
     * @param browseNodeId
     */
    public void setBrowseNodeId(Long browseNodeId) {
        this.browseNodeId = browseNodeId;
    }

    /**
     * 类目名称
     *
     * @return
     */
    public String getBrowseNodeName() {
        return browseNodeName;
    }

    /**
     * 类目名称
     *
     * @param browseNodeName
     */
    public void setBrowseNodeName(String browseNodeName) {
        this.browseNodeName = browseNodeName;
    }

    /**
     * 产品类型关键词
     *
     * @return
     */
    public String getItemTypeKeyword() {
        return itemTypeKeyword;
    }

    /**
     * 产品类型关键词
     *
     * @param itemTypeKeyword
     */
    public void setItemTypeKeyword(String itemTypeKeyword) {
        this.itemTypeKeyword = itemTypeKeyword;
    }

    /**
     * 产品类型定义
     *
     * @return
     */
    public String getProductTypeDefinitions() {
        return productTypeDefinitions;
    }

    /**
     * 产品类型定义
     *
     * @param productTypeDefinitions
     */
    public void setProductTypeDefinitions(String productTypeDefinitions) {
        this.productTypeDefinitions = productTypeDefinitions;
    }

    /**
     * 父级类目ID
     *
     * @return
     */
    public Long getBrowseParentId() {
        return browseParentId;
    }

    /**
     * 父级类目ID
     *
     * @param browseParentId
     */
    public void setBrowseParentId(Long browseParentId) {
        this.browseParentId = browseParentId;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     *
     * @param createDate
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getRootNodeId() {
        return rootNodeId;
    }

    public void setRootNodeId(Long rootNodeId) {
        this.rootNodeId = rootNodeId;
    }
}
