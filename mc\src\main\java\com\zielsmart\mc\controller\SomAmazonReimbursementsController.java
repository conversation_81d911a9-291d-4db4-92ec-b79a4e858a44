package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.service.SomAmazonReimbursementsService;
import com.zielsmart.mc.vo.SomAmazonReimbursementsImportVo;
import com.zielsmart.mc.vo.SomAmazonReimbursementsPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonReimbursementsVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonReimbursementsController
 * @description Amazon Reimbursements报表
 * @date 2025-04-14 16:24:02
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonReimbursements", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon Reimbursements报表")
public class SomAmazonReimbursementsController {

    @Resource
    private SomAmazonReimbursementsService somAmazonReimbursementsService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomAmazonReimbursementsVo>> queryByPage(@RequestBody SomAmazonReimbursementsPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonReimbursementsService.queryByPage(searchVo));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/AmazonReimbursementsTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"market", "approval-date", "reimbursement-id", "case-id", "amazon-order-id", "reason", "sku", "fnsku", "asin",
                "product-name", "condition", "currency-unit", "amount-per-unit", "amount-total", "quantity-reimbursed-cash", "quantity-reimbursed-inventory", "quantity-reimbursed-total",
                "original-reimbursement-id", "original-reimbursement-type"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomAmazonReimbursementsImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonReimbursementsImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonReimbursementsService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

}
