package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McWalmartSaleInventoryReportService;
import com.zielsmart.mc.vo.McWalmartSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.McWalmartSaleInventoryReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/walmart-sale-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "walmart可售库存报表")
public class McWalmartSaleInventoryReportController extends BasicController {

    @Resource
    private McWalmartSaleInventoryReportService service;

    /**
     * queryByPage
     * 查询walmart可售库存报表
     *
     * @param searchVo
     * @return {@link ResultVo< List<com.zielsmart.mc.vo.McSaleInventoryReportVo>>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWalmartSaleInventoryReportVo>> query(@RequestBody McWalmartSaleInventoryReportSearchVo searchVo) {
        return ResultVo.ofSuccess(service.queryWalmartSaleInventoryReport(searchVo));
    }

    /**
     * export
     * 导出walmart可售库存报表
     *
     * @param searchVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody McWalmartSaleInventoryReportSearchVo searchVo) throws ValidateException {
        String data = service.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * batchUpdate
     * 批量上/下架
     *
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量上/下架")
    @PostMapping(value = "/batchUpdate")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchUpdate(@RequestBody McWalmartSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        service.batchUpdate(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }
}
