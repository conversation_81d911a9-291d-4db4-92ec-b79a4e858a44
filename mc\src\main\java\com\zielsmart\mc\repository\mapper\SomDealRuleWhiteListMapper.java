package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomDealRuleWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
 *
 * gen by 代码生成器 mapper 2024-03-06
 */

@SqlResource("somDealRuleWhiteList")
public interface SomDealRuleWhiteListMapper extends BaseMapper<SomDealRuleWhiteList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomDealRuleWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomDealRuleWhiteListVo> queryByPage(@Param("searchVo") SomDealRuleWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
