package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.SomMiraviaListingService;
import com.zielsmart.mc.vo.SomMiraviaListingPageSearchVo;
import com.zielsmart.mc.vo.SomMiraviaListingReport;
import com.zielsmart.mc.vo.SomMiraviaListingVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.LoginIgnore;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomMiraviaListingController
 * @description
 * @date 2024-04-22 14:46:29
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somMiraviaListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Miravia Listing信息表管理")
public class SomMiraviaListingController extends BasicController {

    @Resource
    SomMiraviaListingService somMiraviaListingService;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomMiraviaListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomMiraviaListingVo>> queryByPage(@RequestBody SomMiraviaListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somMiraviaListingService.queryByPage(searchVo));
    }

    @Operation(summary = "库存报表")
    @PostMapping(value = "/stock-report")
    @LoginIgnore
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomMiraviaListingReport>> stockReport(@RequestBody SomMiraviaListingPageSearchVo searchVo) throws JsonProcessingException {
        return ResultVo.ofSuccess(somMiraviaListingService.stockReport(searchVo));
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomMiraviaListingPageSearchVo searchVo) throws ValidateException, JsonProcessingException {
        String data = somMiraviaListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
