package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import com.google.common.collect.Lists;
import com.zielsmart.mc.repository.entity.SomAmazonCategoryNodeXml;
import com.zielsmart.mc.repository.entity.ZlccCatalogAmazon;
import com.zielsmart.mc.repository.entity.ZlccCatalogAmazonPreferenceSetting;
import com.zielsmart.mc.repository.mapper.SomAmazonCategoryNodeXmlMapper;
import com.zielsmart.mc.repository.mapper.ZlccCatalogAmazonMapper;
import com.zielsmart.mc.repository.mapper.ZlccCatalogAmazonPreferenceSettingMapper;
import com.zielsmart.mc.vo.ZlccCatalogAmazonVo;
import com.zielsmart.mc.vo.ZlccCatalogTreeVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.SQLXML;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title ZlccCatalogAmazonService
 * @description
 * @date 2023-12-01 15:26:50
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class ZlccCatalogAmazonService {

    @Resource
    private ZlccCatalogAmazonMapper zlccCatalogAmazonMapper;

    @Resource
    private ZlccCatalogAmazonPreferenceSettingMapper catalogSettingMapper;

    @Resource
    private SomAmazonCategoryNodeXmlMapper somAmazonCategoryNodeXmlMapper;

    /**
     * 根据父级类目ID查询子集
     *
     * @param zlccCatalogAmazonVo 入参
     * @return List<ZlccCatalogAmazonVo>
     */
    public List<ZlccCatalogAmazonVo> queryChildCategory(ZlccCatalogAmazonVo zlccCatalogAmazonVo) throws ValidateException {
        String site = zlccCatalogAmazonVo.getSite();
        Long browseParentId = zlccCatalogAmazonVo.getBrowseParentId();
        if (StrUtil.isBlank(site)) {
            throw new ValidateException("站点不能为空！");
        }
        // 父级ID和根ID必传，防止一个分类在两个根下面
        Long rootNodeId = zlccCatalogAmazonVo.getRootNodeId();
        boolean checkNodeParam = (browseParentId != null && rootNodeId == null)
                || (browseParentId == null && rootNodeId != null);
        if (checkNodeParam) {
            throw new ValidateException("父节点与根节点参数缺失！");
        }
        List<ZlccCatalogAmazonVo> catalogAmazonVos = this.zlccCatalogAmazonMapper.queryChildCategory(site, rootNodeId, browseParentId);
        // 首字母排序
        catalogAmazonVos.sort(Comparator.comparing(ZlccCatalogAmazonVo::getBrowseNodeName));
        // 查询是否拥有子集
        if (CollUtil.isNotEmpty(catalogAmazonVos)) {
            List<Long> browseNodeIds = catalogAmazonVos.stream().map(ZlccCatalogAmazonVo::getBrowseNodeId).collect(Collectors.toList());
            List<Long> hasChildNodeIds = this.zlccCatalogAmazonMapper.queryHasChildNodeIds(site, rootNodeId, browseNodeIds);
            catalogAmazonVos.forEach(data -> data.setHasChild(hasChildNodeIds.contains(data.getBrowseNodeId())));
        }
        return catalogAmazonVos;
    }

    public static List<ZlccCatalogTreeVo> convertToTree(List<ZlccCatalogAmazonVo> dataList) {
        List<ZlccCatalogTreeVo> treeList = dataList.stream().filter(x -> StrUtil.isNotBlank(x.getProductTypeDefinitions())).map(x -> {
            String key = x.getBrowseNodeId().toString() + x.getBrowseParentId() + x.getRootNodeId();
            String title = x.getBrowseNodeName();
            String kewWords = x.getProductTypeDefinitions();
            String parentId = x.getBrowseParentId() == null ? null : x.getBrowseParentId().toString();
            boolean favorite = x.isFavorite();
            ZlccCatalogTreeVo node = new ZlccCatalogTreeVo();
            node.setKey(key);
            node.setParentId(parentId);
            node.setChildId(x.getBrowseNodeId().toString());
            node.setValue(kewWords + "&&" + x.getAid());
            node.setCategory(kewWords);
            node.setTitle(title);
            node.setFavorite(favorite);
            node.setIsLeaf(true);
            node.setSelectable(true);
            node.setFavoriteCreateDate(x.getFavoriteCreateDate());
            return node;
        }).collect(Collectors.toList());
        Map<String, ZlccCatalogTreeVo> nodeKeyMap = treeList.stream().collect(Collectors.toMap(ZlccCatalogTreeVo::getKey, Function.identity(), (x1, x2) -> x1));
        Map<String, ZlccCatalogTreeVo> parentMap = treeList.stream().collect(Collectors.toMap(ZlccCatalogTreeVo::getChildId, Function.identity(), (x1, x2) -> x1));

        List<ZlccCatalogTreeVo> tree = new ArrayList<>();

        for (ZlccCatalogAmazonVo catalogVo : dataList) {
            if (catalogVo.getProductTypeDefinitions() == null) {
                continue;
            }
            ZlccCatalogTreeVo treeVo = nodeKeyMap.get(catalogVo.getBrowseNodeId().toString() + catalogVo.getBrowseParentId() + catalogVo.getRootNodeId());
            if (treeVo == null) {
                continue;
            }
            if (ObjectUtil.isEmpty(catalogVo.getBrowseParentId())) {
                treeVo.setCategory(null);
                tree.add(treeVo);
                continue;
            }
            ZlccCatalogTreeVo parentVo = parentMap.get(catalogVo.getBrowseParentId().toString());
            if (parentVo == null) {
                continue;
            }
            parentVo.setCategory(null);
            parentVo.setIsLeaf(false);
            parentVo.setSelectable(false);
            parentVo.getChildren().add(treeVo);
            List<ZlccCatalogTreeVo> sortedList = parentVo.getChildren().stream()
                    .sorted(Comparator.nullsLast(Comparator.comparing(ZlccCatalogTreeVo::getFavoriteCreateDate, Comparator.nullsLast(Comparator.reverseOrder()))))
                    .collect(Collectors.toList());
            parentVo.setChildren(sortedList);
        }
        return tree.stream()
                .sorted(Comparator.nullsLast(Comparator.comparing(ZlccCatalogTreeVo::getFavoriteCreateDate, Comparator.nullsLast(Comparator.reverseOrder()))))
                .collect(Collectors.toList());
    }

    public List<ZlccCatalogTreeVo> queryFavoriteTree(ZlccCatalogAmazonVo zlccCatalogAmazonVo, TokenUserInfo tokenUser) {
        List<ZlccCatalogAmazonVo> voList = zlccCatalogAmazonMapper.queryTreeListWithFavorite(zlccCatalogAmazonVo.getSite(), tokenUser.getJobNumber());
        return convertToTree(voList);
    }

    public void saveFavorite(ZlccCatalogAmazonVo zlccCatalogAmazonVo, TokenUserInfo tokenUser) throws ValidateException {
        //校验数据是否为空
        if (ObjectUtil.isEmpty(zlccCatalogAmazonVo) || StrUtil.isEmpty(zlccCatalogAmazonVo.getSite()) ||
                ObjectUtil.isEmpty(zlccCatalogAmazonVo.getBrowseNodeId()) || StrUtil.isEmpty(zlccCatalogAmazonVo.getBrowseNodeName())) {
            throw new ValidateException("数据不能为空");
        }
        LambdaQuery<ZlccCatalogAmazonPreferenceSetting> lambdaQuery = catalogSettingMapper.createLambdaQuery();
        if (zlccCatalogAmazonVo.getBrowseParentId() != null) {
            lambdaQuery.andEq("browse_parent_id", zlccCatalogAmazonVo.getBrowseParentId());
        }
        ZlccCatalogAmazonPreferenceSetting single = lambdaQuery
                .andEq("site", zlccCatalogAmazonVo.getSite())
                .andEq("browse_node_id", zlccCatalogAmazonVo.getBrowseNodeId())
                .andEq("create_num", tokenUser.getJobNumber())
                .single();
        //没收藏就保存
        if (ObjectUtil.isEmpty(single)) {
            ZlccCatalogAmazonPreferenceSetting amazon = new ZlccCatalogAmazonPreferenceSetting();
            amazon.setSite(zlccCatalogAmazonVo.getSite());
            amazon.setBrowseNodeId(zlccCatalogAmazonVo.getBrowseNodeId());
            amazon.setBrowseNodeName(zlccCatalogAmazonVo.getBrowseNodeName());
            amazon.setBrowseParentId(zlccCatalogAmazonVo.getBrowseParentId());
            amazon.setCreateDate(new Date());
            amazon.setAid(IdUtil.fastSimpleUUID());
            amazon.setCreateNum(tokenUser.getJobNumber());
            amazon.setCreateName(tokenUser.getUserName());
            catalogSettingMapper.insert(amazon);
        } else {
            //已经收藏了就删除
            catalogSettingMapper.createLambdaQuery().andEq("aid", single.getAid()).delete();
        }
    }

    /**
     * 下载子类目
     *
     * @param zlccCatalogAmazonVo 入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void downloadChild(ZlccCatalogAmazonVo zlccCatalogAmazonVo) throws Exception {
        if (ObjectUtil.isEmpty(zlccCatalogAmazonVo)) {
            return;
        }
        // 站点与类目ID必传
        String site = zlccCatalogAmazonVo.getSite();
        Long browseNodeId = zlccCatalogAmazonVo.getBrowseNodeId();
        if (StrUtil.isEmpty(site) || browseNodeId == null) {
            throw new ValidateException("站点ID与节点ID不能为空");
        }
        // 校验类目是否存在
        checkNodeExist(site, browseNodeId);
        // 需要注意，这里面包含自己，
        List<SomAmazonCategoryNodeXml> nodeXmlList = somAmazonCategoryNodeXmlMapper.queryChild(String.valueOf(browseNodeId), site);
        if (CollUtil.isEmpty(nodeXmlList)) {
            return;
        }
        // 删除之前的子节点
        zlccCatalogAmazonMapper.createLambdaQuery().andEq("site", site).andEq("root_node_id", browseNodeId).delete();
        // 新增新数据
        List<ZlccCatalogAmazon> catalogAmazons = new ArrayList<>();
        for (SomAmazonCategoryNodeXml categoryNodeXml : nodeXmlList) {
            ZlccCatalogAmazon catalogAmazon = new ZlccCatalogAmazon();
            catalogAmazon.setAid(IdUtil.fastSimpleUUID());
            catalogAmazon.setSite(site);
            catalogAmazon.setRootNodeId(browseNodeId);
            catalogAmazon.setBrowseNodeId(Long.parseLong(categoryNodeXml.getNodeId()));
            catalogAmazon.setBrowseNodeName(categoryNodeXml.getNodeName());
            SQLXML nodeXml = categoryNodeXml.getNodeXml();
            if (nodeXml == null) {
                continue;
            }
            Map<String, Object> nodeXmlMap = XmlUtil.xmlToMap(nodeXml.getString());
            String browsePathById = MapUtil.getStr(nodeXmlMap, "browsePathById");
            // 由于 nodeXmlList 里面包含了自己，由于自己是根节点，所以无父
            if (String.valueOf(browseNodeId).equals(categoryNodeXml.getNodeId())) {
                catalogAmazon.setBrowseParentId(null);
            } else {
                String browsePathStr = browsePathById.toString();
                String[] split = browsePathStr.split(",");
                Long parentId = null;
                if (split.length > 1) {
                    parentId = Long.parseLong(split[split.length - 2]);
                }
                catalogAmazon.setBrowseParentId(parentId);
            }
            catalogAmazon.setCreateDate(new Date());
            catalogAmazon.setProductTypeDefinitions(MapUtil.getStr(nodeXmlMap, "productTypeDefinitions"));
            Object attributes = nodeXmlMap.get("browseNodeAttributes");
            try {
                if (attributes != null) {
                    catalogAmazon.setItemTypeKeyword((MapUtil.getStr((Map) attributes, "attribute")));
                }
            } catch (Exception e) {
                catalogAmazon.setItemTypeKeyword(null);
            }
            catalogAmazons.add(catalogAmazon);
        }
        // 入库
        if (CollUtil.isNotEmpty(catalogAmazons)) {
            List<List<ZlccCatalogAmazon>> partition = Lists.partition(catalogAmazons, 500);
            partition.forEach(list -> zlccCatalogAmazonMapper.insertBatch(list));
        }
    }

    /**
     * 核验是否是一级目录ID
     *
     * @param site         站点
     * @param browseNodeId 类目ID
     */
    public void checkNodeExist(String site, Long browseNodeId) throws ValidateException {
        List<SomAmazonCategoryNodeXml> nodeXmlList = somAmazonCategoryNodeXmlMapper.createLambdaQuery()
                .andEq("site", site)
                .andEq("node_id", String.valueOf(browseNodeId))
                .select();
        if (CollUtil.isEmpty(nodeXmlList)) {
            throw new ValidateException("Amazon类目节点不存在！");
        }
    }
}

