package com.zielsmart.mc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/*
 * Temu本本goods的VO实体对象
 * gen by 代码生成器 2025-06-26
 */
@Data
@Schema(title = "Temu本本goods", name = "SomTemuLocalGoodsVo")
public class SomTemuLocalGoodsVo implements java.io.Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键", name = "aid")
    private String aid;
    /**
     * 账号
     */
    @Schema(description = "账号", name = "accountTag")
    private String accountTag;
    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID", name = "accountId")
    private String accountId;
    /**
     * 站点
     */
    @Schema(description = "站点", name = "site")
    private String site;
    /**
     * 商品ID
     */
    @Schema(description = "商品ID", name = "goodsId")
    private Long goodsId;
    /**
     * 商品名称
     */
    @Schema(description = "商品名称", name = "goodsName")
    private String goodsName;
    /**
     * 产品的规格或类型
     */
    @Schema(description = "产品的规格或类型", name = "specName")
    private String specName;
    /**
     * 产品缩略图URL
     */
    @Schema(description = "产品缩略图URL", name = "thumbUrl")
    private String thumbUrl;
    /**
     * 产品外部编码
     */
    @Schema(description = "产品外部编码", name = "outGoodsSn")
    private String outGoodsSn;
    /**
     * 产品的状态（例如，1表示销售，4表示未发布，等等）
     */
    @Schema(description = "产品的状态（例如，1表示销售，4表示未发布，等等）", name = "status")
    private Integer status;
    /**
     * 产品的子状态（具体含义取决于业务逻辑）
     */
    @Schema(description = "产品的子状态（具体含义取决于业务逻辑）", name = "subStatus")
    private Integer subStatus;
    /**
     * 币种
     */
    @Schema(description = "币种", name = "currency")
    private String currency;
    /**
     * 产品的市场价格或建议零售价
     */
    @Schema(description = "产品的市场价格或建议零售价", name = "marketPrice")
    private Long marketPrice;
    /**
     * 产品的市场价格或建议零售价
     */
    @Schema(description = "产品的市场价格或建议零售价", name = "listPrice")
    private Object listPrice;
    /**
     * 产品的外部SKU（库存单位）代码或序列号列表
     */
    @Schema(description = "产品的外部SKU（库存单位）代码或序列号列表", name = "outSkuSnList")
    private Object outSkuSnList;
    /**
     * 产品的SKU标识符列表
     */
    @Schema(description = "产品的SKU标识符列表", name = "skuIdList")
    private Object skuIdList;
    /**
     * 产品的销售价格或零售价格
     */
    @Schema(description = "产品的销售价格或零售价格", name = "price")
    private BigDecimal price;
    /**
     * 产品的销售价格或零售价格
     */
    @Schema(description = "产品的销售价格或零售价格", name = "retailPrice")
    private Object retailPrice;
    /**
     * 产品的库存数量或库存水平
     */
    @Schema(description = "产品的库存数量或库存水平", name = "quantity")
    private Integer quantity;
    /**
     * 产品的创建时间
     */
    @Schema(description = "产品的创建时间", name = "crtTime")
    private Date crtTime;
    /**
     * 产品状态变更时间
     */
    @Schema(description = "产品状态变更时间", name = "goodsStatusChangeTime")
    private Date goodsStatusChangeTime;
    /**
     * 类目ID
     */
    @Schema(description = "类目ID", name = "catId")
    private Long catId;
    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID", name = "brandId")
    private Long brandId;
    /**
     * 商标ID
     */
    @Schema(description = "商标ID", name = "trademarkId")
    private Long trademarkId;
    /**
     * 您的产品可用的交付选项的ID，用逗号分隔
     */
    @Schema(description = "您的产品可用的交付选项的ID，用逗号分隔", name = "costTemplateId")
    private String costTemplateId;
    /**
     * 指示从收到商品订单到可以发货之间的时间（以秒为单位）
     */
    @Schema(description = "指示从收到商品订单到可以发货之间的时间（以秒为单位）", name = "shipmentLimitSecond")
    private Long shipmentLimitSecond;
    /**
     * 产品的SKU标识符列表
     */
    @Schema(description = "产品的SKU标识符列表", name = "skuInfoList")
    private Object skuInfoList;
    /**
     * 产品子状态过滤器
     */
    @Schema(description = "产品子状态过滤器", name = "goodsShowSubStatus")
    private Integer goodsShowSubStatus;
    /**
     * 低流量标签：1-低流量，2-不低流量
     */
    @Schema(description = "低流量标签：1-低流量，2-不低流量", name = "lowTrafficTag")
    private Integer lowTrafficTag;
    /**
     * 限制流量标签：1-限制流量，2-非限制流量
     */
    @Schema(description = "限制流量标签：1-限制流量，2-非限制流量", name = "restrictedTrafficTag")
    private Integer restrictedTrafficTag;
    /**
     * 调用接口下载的时间
     */
    @Schema(description = "调用接口下载的时间", name = "downloadTime")
    private Date downloadTime;


}
