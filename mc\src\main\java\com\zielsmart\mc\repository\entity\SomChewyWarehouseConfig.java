package com.zielsmart.mc.repository.entity;

import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;

@Data
@Table(name="mc.som_chewy_warehouse_config")
public class SomChewyWarehouseConfig {
    @AssignID
    private String aid;

    /**
     * 平台仓库Code
     */
    @Column("warehouse_code")
    private String warehouseCode;

    /**
     * 平台仓库名称
     */
    @Column("warehouse_name")
    private String warehouseName;

    /**
     * 可售仓库
     */
    @Column("useable_warehouse_code")
    private String useableWarehouseCode;

    /**
     * 可售库区
     */
    @Column("useable_storage_code")
    private String useableStorageCode;

    /**
     * 站点
     */
    @Column("site")
    private String site;

    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;

    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    /**
     * 修改人工号
     */
    @Column("last_modify_num")
    private String lastModifyNum;

    /**
     * 修改人姓名
     */
    @Column("last_modify_name")
    private String lastModifyName;

    /**
     * 修改时间
     */
    @Column("last_modify_time")
    private Date lastModifyTime;
}
