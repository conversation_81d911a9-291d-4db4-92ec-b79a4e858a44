package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import org.postgresql.util.PGobject;
/*
* AmazonJsonschema原始数据
* gen by 代码生成器 2025-07-24
*/

@Table(name="mc.som_amazon_jsonschema_data")
public class SomAmazonJsonschemaData implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * MarketPlaceId
	 */
	@Column("market_place_id")
	private String marketPlaceId ;
	/**
	 * 类目
	 */
	@Column("category")
	private String category ;
	/**
	 * Json schema
	 */
	@Column("json_schema")
	private PGobject jsonSchema ;
	/**
	 * Property Groups
	 */
	@Column("property_groups")
	private PGobject propertyGroups ;

	public SomAmazonJsonschemaData() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* MarketPlaceId
	*@return
	*/
	public String getMarketPlaceId(){
		return  marketPlaceId;
	}
	/**
	* MarketPlaceId
	*@param  marketPlaceId
	*/
	public void setMarketPlaceId(String marketPlaceId ){
		this.marketPlaceId = marketPlaceId;
	}
	/**
	* 类目
	*@return
	*/
	public String getCategory(){
		return  category;
	}
	/**
	* 类目
	*@param  category
	*/
	public void setCategory(String category ){
		this.category = category;
	}
	/**
	* Json schema
	*@return
	*/
	public PGobject getJsonSchema(){
		return  jsonSchema;
	}
	/**
	* Json schema
	*@param  jsonSchema
	*/
	public void setJsonSchema(PGobject jsonSchema ){
		this.jsonSchema = jsonSchema;
	}
	/**
	* Property Groups
	*@return
	*/
	public PGobject getPropertyGroups(){
		return  propertyGroups;
	}
	/**
	* Property Groups
	*@param  propertyGroups
	*/
	public void setPropertyGroups(PGobject propertyGroups ){
		this.propertyGroups = propertyGroups;
	}

}
