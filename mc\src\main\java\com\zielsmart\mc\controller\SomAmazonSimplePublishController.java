package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonSimplePublishService;
import com.zielsmart.mc.vo.SomAmazonSimplePublishImportVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonSimplePublishVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonSimplePublishController
 * @description Amazon简单上货管理
 * @date 2025-07-18 10:15:03
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonSimplePublish", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon简单上货管理")
public class SomAmazonSimplePublishController extends BasicController{

    @Resource
    SomAmazonSimplePublishService somAmazonSimplePublishService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomAmazonSimplePublishVo>> queryByPage(@RequestBody SomAmazonSimplePublishPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonSimplePublishService.queryByPage(searchVo));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/SomAmazonSimplePublishTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点*", "SKU*", "是否寄售*", "品牌", "展示码", "标题*", "长描述", "运费模板", "原产国", "价格*", "产品尺寸", "内箱第一长", "内箱第一长单位", "内箱第二长", "内箱第二长单位",
                "内箱第三长", "内箱第三长单位", "内箱毛重", "内箱毛重单位", "颜色", "List Price", "是否需要电池", "是否包含电池", "eu_energy_efficiency_rating", "eprel_registration_number"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomAmazonSimplePublishImportVo> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomAmazonSimplePublishImportVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollUtil.isEmpty(result.getList())) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somAmazonSimplePublishService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonSimplePublishPageSearchVo searchVo) throws Exception {
        String data = somAmazonSimplePublishService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
