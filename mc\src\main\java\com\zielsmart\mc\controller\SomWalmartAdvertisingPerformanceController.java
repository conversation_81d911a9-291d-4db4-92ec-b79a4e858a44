package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.zielsmart.mc.service.SomWalmartAdvertisingPerformanceService;
import com.zielsmart.mc.vo.SomPddAndOdExtVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceExtVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformancePageSearchVo;
import com.zielsmart.mc.vo.SomWalmartAdvertisingPerformanceVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWalmartAdvertisingPerformanceController
 * @description
 * @date 2024-12-12 12:04:46
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somWalmartAdvertisingPerformance", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Walmart平台广告报表")
@Slf4j
public class SomWalmartAdvertisingPerformanceController extends BasicController{

    @Resource
    SomWalmartAdvertisingPerformanceService somWalmartAdvertisingPerformanceService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomWalmartAdvertisingPerformanceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWalmartAdvertisingPerformanceVo>> queryByPage(@RequestBody SomWalmartAdvertisingPerformancePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWalmartAdvertisingPerformanceService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somWalmartAdvertisingPerformanceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWalmartAdvertisingPerformanceService.save(somWalmartAdvertisingPerformanceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somWalmartAdvertisingPerformanceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somWalmartAdvertisingPerformanceService.update(somWalmartAdvertisingPerformanceVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somWalmartAdvertisingPerformanceVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomWalmartAdvertisingPerformanceVo somWalmartAdvertisingPerformanceVo) throws ValidateException {
        somWalmartAdvertisingPerformanceService.delete(somWalmartAdvertisingPerformanceVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * export
     * 导出
     * <AUTHOR>
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomWalmartAdvertisingPerformancePageSearchVo searchVo){
        String data = somWalmartAdvertisingPerformanceService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * downloadImportTemplate
     * 下载导入模板
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/downloadImportTemplate")
    public String downloadImportTemplate() {
        return "forward:/static/excel/WalmartImportTemplate.xlsx";
    }

    /**
     * import
     * 导入
     * @param file
     * @param tokenUser
     * <AUTHOR>
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        log.info("开始导入");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "展示码", "SKU", "广告类型", "币种", "日期", "Campaign Name", "平台ID", "产品名称", "展示", "点击", "CTR", "广告花费", "订单量", "转化率", "总销售额", "广告销售额", "总销量", "广告销量"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomWalmartAdvertisingPerformanceExtVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomWalmartAdvertisingPerformanceExtVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somWalmartAdvertisingPerformanceService.importExcel(result.getList(), tokenUser);

        stopWatch.stop();
        log.info("导入完成,总耗时:" + stopWatch.getTotalTimeMillis() + "毫秒");

        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }
}
