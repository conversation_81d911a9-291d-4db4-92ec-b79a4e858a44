package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMDealService;
import com.zielsmart.mc.vo.zbpm.ZBPMDealVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/zbpm-deal", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Deal活动接口")
public class ZBPMDealController extends BasicController {

    @Resource
    private ZBPMDealService dealService;

    /**
     * updatePromotion
     * 更新Deal
     *
     * @param updateVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新Deal")
    @PostMapping(value = "/updateDeal")
    public ResultVo<String> updatePromotion(@RequestBody ZBPMDealVo updateVo) throws ValidateException {
        dealService.updateDeal(updateVo);
        return ResultVo.ofSuccess(null);
    }
}
