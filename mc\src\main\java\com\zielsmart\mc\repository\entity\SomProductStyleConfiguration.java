package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 产品风格配置表
 * gen by 代码生成器 2024-04-11
 */

@Table(name = "mc.som_product_style_configuration")
public class SomProductStyleConfiguration implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 风格英文名称
     */
    @Column("style_name_en")
    private String styleNameEn;
    /**
     * 风格封面图URL
     */
    @Column("style_cover_image_url")
    private String styleCoverImageUrl;
    /**
     * 风格Banner图URL
     */
    @Column("style_banner_image_url")
    private String styleBannerImageUrl;
    /**
     * 0.禁用 1.启用
     */
    @Column("is_enabled")
    private Integer isEnabled;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;
    /**
     * 排序
     */
    @Column("sort")
    private Integer sort;

    public SomProductStyleConfiguration() {
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 风格英文名称
     *
     * @return
     */
    public String getStyleNameEn() {
        return styleNameEn;
    }

    /**
     * 风格英文名称
     *
     * @param styleNameEn
     */
    public void setStyleNameEn(String styleNameEn) {
        this.styleNameEn = styleNameEn;
    }

    /**
     * 风格封面图URL
     *
     * @return
     */
    public String getStyleCoverImageUrl() {
        return styleCoverImageUrl;
    }

    /**
     * 风格封面图URL
     *
     * @param styleCoverImageUrl
     */
    public void setStyleCoverImageUrl(String styleCoverImageUrl) {
        this.styleCoverImageUrl = styleCoverImageUrl;
    }

    /**
     * 风格Banner图URL
     *
     * @return
     */
    public String getStyleBannerImageUrl() {
        return styleBannerImageUrl;
    }

    /**
     * 风格Banner图URL
     *
     * @param styleBannerImageUrl
     */
    public void setStyleBannerImageUrl(String styleBannerImageUrl) {
        this.styleBannerImageUrl = styleBannerImageUrl;
    }

    /**
     * 0.禁用 1.启用
     *
     * @return
     */
    public Integer getisEnabled() {
        return isEnabled;
    }

    /**
     * 0.禁用 1.启用
     *
     * @param isEnabled
     */
    public void setisEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
