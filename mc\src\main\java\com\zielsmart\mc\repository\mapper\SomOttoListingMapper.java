package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomOttoListingPageSearchVo;
import com.zielsmart.mc.vo.SomOttoListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-04-26
*/

@SqlResource("somOttoListing")
public interface SomOttoListingMapper extends BaseMapper<SomOttoListing> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOttoListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomOttoListingVo> queryByPage(@Param("searchVo")SomOttoListingPageSearchVo searchVo, PageRequest pageRequest);
}
