package com.zielsmart.mc.service.mdm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomRecommendPublishList;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.vo.ZlccAmazonMdmVo;
import com.zielsmart.web.basic.exception.BusinessException;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MDM 请求工具类
 */
@Slf4j
@Component
public class MdmRequestService {

    @Value("${magic.head.token}")
    private String token;

    @Resource
    private IMagicService iMagicService;


    /**
     * 构建MDM请求参数
     *
     * @param recommendPublishLists 推荐上货清单
     * @return Mdm请求参数
     */
    public List<ZlccAmazonMdmVo> buildRequestMdmParams(List<SomRecommendPublishList> recommendPublishLists) {
        List<ZlccAmazonMdmVo> amazonMdmVos = new ArrayList<>();
        for (SomRecommendPublishList recommendPublishList : recommendPublishLists) {
            ZlccAmazonMdmVo amazonMdmVo = new ZlccAmazonMdmVo();
            amazonMdmVo.setCustomPlatform(recommendPublishList.getPlatform());
            amazonMdmVo.setProductNum(recommendPublishList.getSku());
            amazonMdmVo.setCustomNum(recommendPublishList.getCustomerCode());
            amazonMdmVos.add(amazonMdmVo);
        }
        return amazonMdmVos;
    }


    /**
     * 获取MDM简单上货信息
     *
     * @param body 入参
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getMdmExhibitGoodsData(List<ZlccAmazonMdmVo> body) throws ValidateException {
        if (CollUtil.isEmpty(body)) {
            throw new BusinessException("参数不能为空");
        }
        // 返回结果
        body = new ArrayList<>();
        ZlccAmazonMdmVo test1 = new ZlccAmazonMdmVo();
        test1.setProductNum("BBC310W01");
        test1.setCustomNum("10000003");
        test1.setCustomPlatform("Amazon");
        body.add(test1);
        try {
            ResultVo resultVo = iMagicService.getMdmExhibitGoodsData(token, body);
            if (!resultVo.isSuccess()) {
                log.error("亚马逊刊登：获取MDM商品数据失败,param:[{}],result:[{}]", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(resultVo));
                throw new BusinessException("获取MDM商品数据失败！" );
            }
            return (List<Map<String, Object>>) resultVo.getData();
        } catch (Exception ex) {
            log.error("亚马逊刊登：获取MDM商品数据异常,param:[{}],ex:[{}]", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(ex));
            throw new BusinessException("获取MDM商品数据异常 " + ex.getMessage());
        }
    }
}
