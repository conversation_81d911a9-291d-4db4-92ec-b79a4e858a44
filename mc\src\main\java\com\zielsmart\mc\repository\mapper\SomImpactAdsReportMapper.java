package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomImpactAdsReportPageSearchVo;
import com.zielsmart.mc.vo.SomImpactAdsReportVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-05-12
*/

@SqlResource("somImpactAdsReport")
public interface SomImpactAdsReportMapper extends BaseMapper<SomImpactAdsReport> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomImpactAdsReportVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomImpactAdsReportVo> queryByPage(@Param("searchVo")SomImpactAdsReportPageSearchVo searchVo, PageRequest pageRequest);
}
