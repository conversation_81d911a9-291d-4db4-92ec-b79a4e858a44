package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
 *
 * gen by 代码生成器 2022-12-26
 */

@Table(name = "mc.som_kpi_gross_profit")
public class SomKpiGrossProfit implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * 28天的平均毛利率
     */
    @Column("one_month_gross_profit_rate")
    private BigDecimal oneMonthGrossProfitRate;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomKpiGrossProfit() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 28天的平均毛利率
     *
     * @return
     */
    public BigDecimal getOneMonthGrossProfitRate() {
        return oneMonthGrossProfitRate;
    }

    /**
     * 28天的平均毛利率
     *
     * @param oneMonthGrossProfitRate
     */
    public void setOneMonthGrossProfitRate(BigDecimal oneMonthGrossProfitRate) {
        this.oneMonthGrossProfitRate = oneMonthGrossProfitRate;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
