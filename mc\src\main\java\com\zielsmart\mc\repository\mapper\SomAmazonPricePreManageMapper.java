package com.zielsmart.mc.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.SomAmazonPricePreManage;
import com.zielsmart.mc.vo.SomAmazonPricePreManagePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonPricePreManageVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomAmazonPricePreManageMapper
 * @description
 * @date 2025-02-18 11:53:04
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somAmazonPricePreManage")
public interface SomAmazonPricePreManageMapper extends BaseMapper<SomAmazonPricePreManage> {

    /**
     * 分页查询
     *
     * @param searchVo 查询参数
     * @param pageRequest 分页参数
     * @return PageResult<SomAmazonPricePreManageVo>
     */
    PageResult<SomAmazonPricePreManageVo> queryByPage(SomAmazonPricePreManagePageSearchVo searchVo, PageRequest pageRequest);


    /**
     * 批量设置失效
     *
     * @param pricePreManages 需要更新的数据
     */
    default void batchUpdateExpire(@Param("updatePricePreManages") List<SomAmazonPricePreManage> pricePreManages) {
        if (CollUtil.isEmpty(pricePreManages)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somAmazonPricePreManage.batchUpdateExpire"), pricePreManages);
    }
}
