package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 
* gen by 代码生成器 2021-09-27
*/

@Table(name="mc.mc_push_otto_delivery")
public class McPushOttoDelivery implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 展示码
	 */
	@Column("listing_code")
	private String listingCode ;
	/**
	 * SKU
	 */
	@Column("product_main_code")
	private String productMainCode ;
	/**
	 * 配送方式
	 */
	@Column("delivery_type")
	private String deliveryType ;
	/**
	 * 配送时间
	 */
	@Column("delivery_time_day")
	private Integer deliveryTimeDay ;
	/**
	 * 同步状态 0未处理 1处理成功 99失败
	 */
	@Column("sync_status")
	private Integer syncStatus ;
	/**
	 * 错误信息
	 */
	@Column("error_msg")
	private String errorMsg ;
	/**
	 * 处理时间
	 */
	@Column("sync_time")
	private Date syncTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public McPushOttoDelivery() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 展示码
	*@return
	*/
	public String getListingCode(){
		return  listingCode;
	}
	/**
	* 展示码
	*@param  listingCode
	*/
	public void setListingCode(String listingCode ){
		this.listingCode = listingCode;
	}
	/**
	* SKU
	*@return
	*/
	public String getProductMainCode(){
		return  productMainCode;
	}
	/**
	* SKU
	*@param  productMainCode
	*/
	public void setProductMainCode(String productMainCode ){
		this.productMainCode = productMainCode;
	}
	/**
	* 配送方式
	*@return
	*/
	public String getDeliveryType(){
		return  deliveryType;
	}
	/**
	* 配送方式
	*@param  deliveryType
	*/
	public void setDeliveryType(String deliveryType ){
		this.deliveryType = deliveryType;
	}
	/**
	* 配送时间
	*@return
	*/
	public Integer getDeliveryTimeDay(){
		return  deliveryTimeDay;
	}
	/**
	* 配送时间
	*@param  deliveryTimeDay
	*/
	public void setDeliveryTimeDay(Integer deliveryTimeDay ){
		this.deliveryTimeDay = deliveryTimeDay;
	}
	/**
	* 同步状态 0未处理 1处理成功 99失败
	*@return
	*/
	public Integer getSyncStatus(){
		return  syncStatus;
	}
	/**
	* 同步状态 0未处理 1处理成功 99失败
	*@param  syncStatus
	*/
	public void setSyncStatus(Integer syncStatus ){
		this.syncStatus = syncStatus;
	}
	/**
	* 错误信息
	*@return
	*/
	public String getErrorMsg(){
		return  errorMsg;
	}
	/**
	* 错误信息
	*@param  errorMsg
	*/
	public void setErrorMsg(String errorMsg ){
		this.errorMsg = errorMsg;
	}
	/**
	* 处理时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 处理时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
