package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTargetListing;
import com.zielsmart.mc.vo.SomTargetInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomTargetInventoryReportVo;
import com.zielsmart.mc.vo.SomTargetListingPageSearchVo;
import com.zielsmart.mc.vo.SomTargetListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2023-12-05
 */

@SqlResource("somTargetListing")
public interface SomTargetListingMapper extends BaseMapper<SomTargetListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomTargetListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTargetListingVo> queryByPage(@Param("searchVo") SomTargetListingPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomTargetInventoryReportVo> queryTargetInventoryReport(@Param("searchVo") SomTargetInventoryReportSearchVo searchVo, PageRequest pageRequest);

    List<SomTargetInventoryReportVo> exportTargetInventoryReport(@Param("searchVo") SomTargetInventoryReportSearchVo searchVo);

}
