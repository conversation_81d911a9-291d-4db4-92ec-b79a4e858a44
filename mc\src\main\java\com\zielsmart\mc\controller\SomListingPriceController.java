package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomListingPriceService;
import com.zielsmart.mc.vo.SomListingPriceExtVo;
import com.zielsmart.mc.vo.SomListingPricePageSearchVo;
import com.zielsmart.mc.vo.SomListingPriceVo;
import com.zielsmart.mc.vo.ValidateDealVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomListingPriceController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somListingPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台价管理")
public class SomListingPriceController extends BasicController {

    @Resource
    SomListingPriceService somListingPriceService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomListingPriceVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomListingPriceExtVo>> queryByPage(@RequestBody SomListingPricePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somListingPriceService.queryByPage(searchVo));
    }

    /**
     * queryListingPrice
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomListingPriceVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "根据平台站点展示码查询平台价信息")
    @PostMapping(value = "/queryListingPrice")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomListingPriceVo> queryListingPrice(@RequestBody SomListingPricePageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somListingPriceService.queryListingPrice(searchVo));
    }

    /**
     * defendListingPrice
     *
     * @param defendPriceVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "调整价格")
    @PostMapping(value = "/defendListingPrice")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> defendListingPrice(@RequestBody SomListingPriceVo defendPriceVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somListingPriceService.defendListingPrice(defendPriceVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载导入模板
     *
     * @param
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/ListingPriceTemplate.xlsx";
    }

    /**
     * import
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台", "站点", "展示码", "Price", "Shipping price", "Minimum price", "Maximum price", "Business price", "Discount type",
                "Quantity Lower Bound 1", "Quantity Price 1", "Quantity Lower Bound 2", "Quantity Price 2", "Quantity Lower Bound 3", "Quantity Price 3",
                "Quantity Lower Bound 4", "Quantity Price 4", "Quantity Lower Bound 5", "Quantity Price 5", "调价原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomListingPriceVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomListingPriceVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somListingPriceService.importExcel(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportExcel(@RequestBody SomListingPricePageSearchVo exportVo) {
        String data = somListingPriceService.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * 批量调整价格
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量调整价格")
    @PostMapping(value = "/batchUpdatePrice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchUpdatePrice(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台", "站点", "展示码", "Price", "Shipping price", "Minimum price", "Maximum price", "Business price", "Discount type",
                "Quantity Lower Bound 1", "Quantity Price 1", "Quantity Lower Bound 2", "Quantity Price 2", "Quantity Lower Bound 3", "Quantity Price 3",
                "Quantity Lower Bound 4", "Quantity Price 4", "Quantity Lower Bound 5", "Quantity Price 5", "调价原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomListingPriceExtVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomListingPriceExtVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somListingPriceService.batchUpdatePrice(result.getList(), tokenUser);
        if (StrUtil.isBlank(str)) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * validateDeal
     *
     * @param validateVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "校验营销活动")
    @PostMapping(value = "/validateMarketing")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> validateMarketing(@RequestBody ValidateDealVo validateVo) throws ValidateException {
        somListingPriceService.validateDeal(validateVo);
        return ResultVo.ofSuccess(null);
    }
}
