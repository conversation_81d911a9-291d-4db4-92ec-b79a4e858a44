package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomTemuCrossSku;
import com.zielsmart.mc.repository.entity.SomTemuLocalSku;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomTemuCrossSkuMapper;
import com.zielsmart.mc.repository.mapper.SomTemuLocalSkuMapper;
import com.zielsmart.mc.vo.SomTemuSkuBasicVo;
import com.zielsmart.web.basic.exception.ValidateException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomTemuAccountService
 * @description Temu 账号管理
 * @date 2025-03-12 09:10:57
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomTemuSkuService {

    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;

    @Resource
    private SomTemuLocalSkuMapper somTemuLocalSkuMapper;

    @Resource
    private SomTemuCrossSkuMapper somTemuCrossSkuMapper;

    /**
     * 获取 Temu Listing
     *
     * @param accountId 账号ID
     * @param site 站点
     * @param sellerSku 展示码
     * @return SomTemuSkuBasicVo
     */
    public SomTemuSkuBasicVo getTemuSku(String accountId, String site, String sellerSku, String productSkuId) throws ValidateException {
        if (isLocalAccount(accountId)) {
            List<SomTemuLocalSku> somTemuLocalSkus = somTemuLocalSkuMapper.createLambdaQuery()
                    .andEq("account_id", accountId)
                    .andEq("seller_sku", sellerSku)
                    .andEq("site", site)
                    .select();
            somTemuLocalSkus = somTemuLocalSkus.stream().filter(localSku -> productSkuId.equals(String.valueOf(localSku.getSkuId()))).collect(Collectors.toList());
            if (CollUtil.isEmpty(somTemuLocalSkus)) {
                return null;
            }
            return buildSomTemuSkuBasicVo(somTemuLocalSkus.get(0));
        } else {
            List<SomTemuCrossSku> temuCrossSkus = somTemuCrossSkuMapper.createLambdaQuery()
                    .andEq("account_id", accountId)
                    .andEq("seller_sku", sellerSku)
                    .andEq("site", site)
                    .select();
            temuCrossSkus = temuCrossSkus.stream().filter(crossSku -> productSkuId.equals(String.valueOf(crossSku.getProductSkuId()))).collect(Collectors.toList());
            if (CollUtil.isEmpty(temuCrossSkus)) {
                return null;
            }
            return buildSomTemuSkuBasicVo(temuCrossSkus.get(0));
        }
    }

    /**
     * 判断是否是本本
     *
     * @param accountId 账号ID
     * @return boolean
     */
    public boolean isLocalAccount(String accountId) throws ValidateException {
        List<McDictionaryInfo> mcDictionaryInfos = mcDictionaryInfoMapper.createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value3", accountId)
                .andEq("item_value4", "1")
                .select();
        if (CollUtil.isEmpty(mcDictionaryInfos)) {
            throw new ValidateException("Temu店铺不存在或已注销！");
        }
        McDictionaryInfo mcDictionaryInfo = mcDictionaryInfos.get(0);
        return "本本".equals(mcDictionaryInfo.getItemValue8());

    }

    /**
     * 根据账号IDS 查询所有的 Temu listing
     *
     * @param accountIds 账号IDs
     * @return  List<SomTemuSkuBasicVo>
     */
    public List<SomTemuSkuBasicVo> getTemuSkuByAccountIds(List<String> accountIds) {
        if (CollUtil.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        List<SomTemuSkuBasicVo> somTemuSkus = new ArrayList<>();
        // 查询跨境
        List<SomTemuCrossSku> somTemuCrossSkus = somTemuCrossSkuMapper.createLambdaQuery().andIn("account_id", accountIds).select();
        for (SomTemuCrossSku somTemuCrossSku : somTemuCrossSkus) {
            SomTemuSkuBasicVo basicVo = buildSomTemuSkuBasicVo(somTemuCrossSku);
            somTemuSkus.add(basicVo);
        }
        // 查询本本
        List<SomTemuLocalSku> somTemuLocalSkus = somTemuLocalSkuMapper.createLambdaQuery().andIn("account_id", accountIds).select();
        for (SomTemuLocalSku somTemuLocalSku : somTemuLocalSkus) {
            SomTemuSkuBasicVo basicVo = buildSomTemuSkuBasicVo(somTemuLocalSku);
            somTemuSkus.add(basicVo);
        }
        return somTemuSkus;
    }

    /**
     * 构建 Temu Sku 基础信息
     *
     * @param somTemuLocalSku 本本 Sku
     * @return SomTemuSkuBasicVo
     */
    private SomTemuSkuBasicVo buildSomTemuSkuBasicVo(SomTemuCrossSku somTemuLocalSku) {
        if (somTemuLocalSku == null) {
            return null;
        }
        SomTemuSkuBasicVo basicVo = new SomTemuSkuBasicVo();
        basicVo.setAccountId(somTemuLocalSku.getAccountId());
        basicVo.setSite(somTemuLocalSku.getSite());
        basicVo.setProductId(somTemuLocalSku.getProductId() == null ? null : String.valueOf(somTemuLocalSku.getProductId()));
        basicVo.setProductSkuId(somTemuLocalSku.getProductSkuId() == null ? null : String.valueOf(somTemuLocalSku.getProductSkuId()));
        basicVo.setProductSkcId(somTemuLocalSku.getProductSkcId() == null ? null : String.valueOf(somTemuLocalSku.getProductSkcId()));
        basicVo.setSellerSku(somTemuLocalSku.getSellerSku());
        return basicVo;
    }

    /**
     * 构建 Temu Sku 基础信息
     *
     * @param somTemuLocalSku 本本 Sku
     * @return SomTemuSkuBasicVo
     */
    private SomTemuSkuBasicVo buildSomTemuSkuBasicVo(SomTemuLocalSku somTemuLocalSku) {
        if (somTemuLocalSku == null) {
            return null;
        }
        SomTemuSkuBasicVo basicVo = new SomTemuSkuBasicVo();
        basicVo.setAccountId(somTemuLocalSku.getAccountId());
        basicVo.setSite(somTemuLocalSku.getSite());
        basicVo.setProductId(somTemuLocalSku.getGoodsId() == null ? null : String.valueOf(somTemuLocalSku.getGoodsId()));
        basicVo.setProductSkcId(somTemuLocalSku.getGoodsId() == null ? null : String.valueOf(somTemuLocalSku.getGoodsId()));
        basicVo.setProductSkuId(somTemuLocalSku.getSkuId() == null ? null : String.valueOf(somTemuLocalSku.getSkuId()));
        basicVo.setSellerSku(somTemuLocalSku.getSellerSku());
        return basicVo;
    }
}
