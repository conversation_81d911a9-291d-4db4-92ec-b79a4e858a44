package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.enums.VcPromotionStatusEnum;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SomVcCouponsService {
    
    @Resource
    private SomVcCouponsMapper somVcCouponsMapper;
    @Resource
    private SomVcCouponsItemMapper somVcCouponsItemMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    public PageVo<SomVcCouponsVo> queryByPage(SomVcCouponsPageSearchVo searchVo) {
        List<String> aidList = new ArrayList<>();
        List<String> itemAidList = new ArrayList<>();
        int searchIdent = 1;
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        aidList = aidList.stream().distinct().collect(Collectors.toList());
        if (searchIdent == 2 && CollUtil.isEmpty(aidList)) {
            // 给一个永远不会出现的值吧 避免逻辑异常
            aidList.add("-_-");
        }
        searchVo.setAidList(aidList);

        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcCouponsVo> pageResult = dynamicSqlManager.getMapper(SomVcCouponsMapper.class).queryByPage(searchVo, pageRequest);

        if (CollUtil.isNotEmpty(pageResult.getList())) {
            List<String> couponAidList = pageResult.getList().stream().map(SomVcCouponsVo::getAid).collect(Collectors.toList());
            LambdaQuery<SomVcCouponsItem> query = dynamicSqlManager.getMapper(SomVcCouponsItemMapper.class).createLambdaQuery();
            if (CollUtil.isNotEmpty(itemAidList)) {
                query.andIn("aid", itemAidList);
            }
            List<SomVcCouponsItem> somVcCouponsItemList = query.andIn("coupon_aid", couponAidList).select();
            pageResult.getList().forEach(somVcCouponsVo -> somVcCouponsVo.setItemList(somVcCouponsItemList.stream().filter(item -> item.getCouponAid().equals(somVcCouponsVo.getAid())).collect(Collectors.toList())));

            // 字典值、其他基础字段转换
            // 客户群体
            List<McDictionaryInfo> dictListVcCouponCustomerSegment = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponCustomerSegment").select();
            Map<String, String> dictMapVcCouponCustomerSegment = dictListVcCouponCustomerSegment.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 优惠券类型
            List<McDictionaryInfo> dictListVcCouponDiscountType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponDiscountType").select();
            Map<String, String> dictMapVcCouponDiscountType = dictListVcCouponDiscountType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 优惠券分布
            List<McDictionaryInfo> dictListVcCouponDistribution = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponDistribution").select();
            Map<String, String> dictMapVcCouponDistribution = dictListVcCouponDistribution.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            // 申请原因
            List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
            Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, Function.identity(), (x1, x2) -> x1));
            // 状态
            List<McDictionaryInfo> dictListVcCouponStatus = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponStatus").select();
            Map<String, String> dictMapVcCouponStatus = dictListVcCouponStatus.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            for (SomVcCouponsVo somVcCouponsVo : pageResult.getList()) {
                somVcCouponsVo.setCustomerSegmentShow(dictMapVcCouponCustomerSegment.get(somVcCouponsVo.getCustomerSegment().toString())); // 客户群体
                somVcCouponsVo.setDiscountTypeShow(dictMapVcCouponDiscountType.get(somVcCouponsVo.getDiscountType().toString())); // 优惠券类型
                if (ObjectUtil.isNotNull(somVcCouponsVo.getCouponDistribution())) {
                    somVcCouponsVo.setCouponDistributionShow(dictMapVcCouponDistribution.get(somVcCouponsVo.getCouponDistribution().toString())); // 优惠券分布
                }
                somVcCouponsVo.setApplyReasonShow(dictMapVcPromotionApplyReason.get(somVcCouponsVo.getApplyReason().toString()).getItemLable()); // 申请原因
                somVcCouponsVo.setCouponStatusShow(dictMapVcCouponStatus.get(somVcCouponsVo.getCouponStatus().toString())); // 状态
            }
        }

        return ConvertUtils.pageConvert(pageResult, SomVcCouponsVo.class, searchVo);
    }

    private void setSearchPreData(SomVcCouponsPageSearchVo searchVo, List<String> aidList, List<String> itemAidList) {
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            List<SomVcCoupons> searchSomVcCouponsList = dynamicSqlManager.getMapper(SomVcCouponsMapper.class).createLambdaQuery().andLike(SomVcCoupons::getCouponName, "%" + searchVo.getKeyWord() + "%").select("aid");
            aidList.addAll(searchSomVcCouponsList.stream().map(SomVcCoupons::getAid).collect(Collectors.toList()));

            List<SomVcCouponsItem> searchSomVcCouponsItemList = dynamicSqlManager.getMapper(SomVcCouponsItemMapper.class).createLambdaQuery()
                    .andEq("seller_sku", searchVo.getKeyWord())
                    .orEq("asin", searchVo.getKeyWord())
                    .orEq("sku", searchVo.getKeyWord())
                    .select("aid", "coupon_aid");
            aidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getCouponAid).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getAid).collect(Collectors.toList()));
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            List<SomVcCouponsItem> searchSomVcCouponsItemList = dynamicSqlManager.getMapper(SomVcCouponsItemMapper.class).createLambdaQuery().andEq("sales_group_code", searchVo.getSalesGroupCode()).select("aid", "coupon_aid");
            aidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getCouponAid).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getAid).collect(Collectors.toList()));
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            List<SomVcCouponsItem> searchSomVcCouponsItemList = dynamicSqlManager.getMapper(SomVcCouponsItemMapper.class).createLambdaQuery().andEq("sales_group_empt_code", searchVo.getSalesGroupEmptCode()).select("aid", "coupon_aid");
            aidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getCouponAid).collect(Collectors.toList()));
            itemAidList.addAll(searchSomVcCouponsItemList.stream().map(SomVcCouponsItem::getAid).collect(Collectors.toList()));
        }
    }

    public void delete(SomVcCouponsVo somVcCouponsVo) throws ValidateException {
        SomVcCoupons somVcCoupons = somVcCouponsMapper.createLambdaQuery().andEq("aid", somVcCouponsVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcCoupons)) {
            throw new ValidateException("内容不存在");
        }
        // [草稿]状态才可以删除
        if (!ObjectUtil.equal(somVcCoupons.getCouponStatus(), VcPromotionStatusEnum.DRAFT.getStatus())) {
            throw new ValidateException("只能删除状态为草稿的内容");
        }
        somVcCouponsMapper.createLambdaQuery().andEq("aid", somVcCouponsVo.getAid()).delete();
        somVcCouponsItemMapper.createLambdaQuery().andEq("coupon_aid", somVcCouponsVo.getAid()).delete();
    }

    public void cancel(SomVcCouponsVo somVcCouponsVo) throws ValidateException {
        SomVcCoupons somVcCoupons = somVcCouponsMapper.createLambdaQuery().andEq("aid", somVcCouponsVo.getAid()).single();
        if (ObjectUtil.isEmpty(somVcCoupons)) {
            throw new ValidateException("内容不存在");
        }
        // [进行中,需要关注,未开始]的活动允许取消
        List<Integer> allowCancelStatus = VcPromotionStatusEnum.getAllowCancelStatus();
        if (!allowCancelStatus.contains(somVcCoupons.getCouponStatus())) {
            throw new ValidateException("当前数据不允许取消！");
        }
        somVcCoupons.setCouponStatus(VcPromotionStatusEnum.CANCELED.getStatus());
        somVcCouponsMapper.createLambdaQuery().andEq("aid", somVcCouponsVo.getAid()).update(somVcCoupons);
    }

    public String export(SomVcCouponsPageSearchVo searchVo) throws Exception {
        List<String> aidList = new ArrayList<>();
        List<String> itemAidList = new ArrayList<>();
        int searchIdent = 1;
        // 关键字搜索
        if (StrUtil.isNotBlank(searchVo.getKeyWord())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务组搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        // 业务负责人搜索
        if (StrUtil.isNotBlank(searchVo.getSalesGroupEmptCode())) {
            setSearchPreData(searchVo, aidList, itemAidList);
            searchIdent = 2;
        }
        aidList = aidList.stream().distinct().collect(Collectors.toList());
        if (searchIdent == 2 && CollUtil.isEmpty(aidList)) {
            // 给一个永远不会出现的值吧 避免逻辑异常
            aidList.add("-_-");
        }
        searchVo.setAidList(aidList);

        List<SomVcCouponsVo> records = somVcCouponsMapper.queryList(searchVo);
        if (CollUtil.isNotEmpty(records)) {
            List<String> couponAidList = records.stream().map(SomVcCouponsVo::getAid).collect(Collectors.toList());
            LambdaQuery<SomVcCouponsItem> query = dynamicSqlManager.getMapper(SomVcCouponsItemMapper.class).createLambdaQuery();
            if (CollUtil.isNotEmpty(itemAidList)) {
                query.andIn("aid", itemAidList);
            }
            List<SomVcCouponsItem> somVcCouponsItemList = query.andIn("coupon_aid", couponAidList).select();
            records.forEach(somVcCouponsVo -> somVcCouponsVo.setItemList(somVcCouponsItemList.stream().filter(item -> item.getCouponAid().equals(somVcCouponsVo.getAid())).collect(Collectors.toList())));

            // 字典值、其他基础字段转换
            List<McDictionaryInfo> dictListVcCouponDistribution = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponDistribution").select();
            Map<String, String> dictMapVcCouponDistribution = dictListVcCouponDistribution.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue, McDictionaryInfo::getItemLable, (x1, x2) -> x1));
            for (SomVcCouponsVo somVcCouponsVo : records) {
                if (ObjectUtil.isNotNull(somVcCouponsVo.getCouponDistribution())) {
                    somVcCouponsVo.setCouponDistributionShow(dictMapVcCouponDistribution.get(somVcCouponsVo.getCouponDistribution().toString()));
                }
            }
        }

        if (CollUtil.isNotEmpty(records)) {
            TemplateExportParams params = new TemplateExportParams("static/excel/SomVcCouponExportTemplate.xlsx", true);
            Map<String, Object> data = getExportData(records);
            try {
                Workbook workbook = ExcelExportUtil.exportExcel(params, data);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] bytes = bos.toByteArray();
                return Base64.getEncoder().encodeToString(bytes);
            } catch (Exception e) {
                log.error("导出失败:{}", e.toString(), e);
                throw new RuntimeException("导出失败" + e.toString());
            }
        }
        return null;
    }

    private Map<String, Object> getExportData(List<SomVcCouponsVo> records) {
        Map<String, Object> data = new HashMap<>();
        data.put("promotionName", records.get(0).getPromotionName());
        List<Map<String, Object>> itemList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (SomVcCouponsVo vo : records) {
            String asins = "";
            if (CollUtil.isNotEmpty(vo.getItemList())) {
                asins = vo.getItemList().stream().map(SomVcCouponsItem::getAsin).collect(Collectors.joining(","));
            }
            String startDate = sdf.format(vo.getStartDate());
            String endDate = sdf.format(vo.getEndDate());
            // 折扣值
            String discountValueRate = "";
            String discountValueMoney = "";
            if (vo.getDiscountType().equals(10)) {
                BigDecimal discountRate = vo.getDiscountValue().multiply(BigDecimal.valueOf(100));
                // discountRate = discountRate.setScale(2, RoundingMode.HALF_UP);
                discountValueRate = discountRate.toString() + "%";
            } else {
                discountValueMoney = "$" + vo.getDiscountValue().toString();
            }
            Map<String, Object> item = new HashMap<>();
            item.put("couponName", vo.getCouponName());
            item.put("websiteDisplayName", vo.getWebsiteDisplayName());
            item.put("vendorInvoiceCode", vo.getVendorInvoiceCode());
            item.put("asins", asins);
            item.put("budget", "$" + vo.getBudget());
            item.put("startDate", startDate);
            item.put("endDate", endDate);
            item.put("discountValueRate", discountValueRate);
            item.put("discountValueMoney", discountValueMoney);
            item.put("oncePerCustomerShow", vo.getOncePerCustomerShow());
            item.put("couponDistributionShow", vo.getCouponDistributionShow());
            itemList.add(item);
        }
        data.put("list", itemList);
        return data;
    }

    public String importExcel(List<SomVcCouponsImportVo> list, TokenUserInfo tokenUser) throws ValidateException {
        // 站点唯一验证
        long siteCount = list.stream().map(SomVcCouponsImportVo::getSite).distinct().count();
        if (siteCount > 1) {
            throw new ValidateException("导入数据存在多个站点，请检查数据！");
        }

        StringBuilder stringBuilder = new StringBuilder();
        List<String> siteList = list.stream().map(SomVcCouponsImportVo::getSite).collect(Collectors.toList());
        List<String> sellerSkuList = list.stream().map(SomVcCouponsImportVo::getSellerSku).collect(Collectors.toList());
        List<String> asinList = list.stream().map(SomVcCouponsImportVo::getAsin).collect(Collectors.toList());

        // 确定使用展示码还是asin
        SomVcCouponsImportVo firstRow = list.get(0);
        String sellerSkuFirst = firstRow.getSellerSku();
        String asinFirst = firstRow.getAsin();
        if (StrUtil.isNotBlank(sellerSkuFirst) && StrUtil.isNotBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN只能使用一项");
        }
        if (StrUtil.isBlank(sellerSkuFirst) && StrUtil.isBlank(asinFirst)) {
            throw new ValidateException("展示码和ASIN至少填写一项");
        }
        String useSellerSkuOrAsin = "sellerSku";
        if (StrUtil.isNotEmpty(asinFirst)) {
            useSellerSkuOrAsin = "asin";
        }

        // 必填项
        int i = 2;
        for (SomVcCouponsImportVo vo : list) {
            StringBuilder rowErrors = new StringBuilder();
            if (StrUtil.isEmpty(vo.getSite())) rowErrors.append("站点、");
            if (StrUtil.isEmpty(vo.getAccountName())) rowErrors.append("账号名称、");
            if (StrUtil.isEmpty(vo.getVendorCode())) rowErrors.append("供应商编码、");
            if (StrUtil.isEmpty(vo.getPromotionName())) rowErrors.append("促销名称、");
            if (StrUtil.isEmpty(vo.getCouponName())) rowErrors.append("优惠券标题、");
            if (StrUtil.isEmpty(vo.getWebsiteDisplayName())) rowErrors.append("网站展示的名称、");
            if (ObjectUtil.isEmpty(vo.getBudget())) rowErrors.append("预算、");
            //if (StrUtil.isEmpty(vo.getSellerSku())) rowErrors.append("展示码、");
            if (ObjectUtil.isEmpty(vo.getStartDate())) rowErrors.append("活动起始日期、");
            if (ObjectUtil.isEmpty(vo.getEndDate())) rowErrors.append("活动截止日期、");
            if (StrUtil.isEmpty(vo.getDiscountTypeShow())) rowErrors.append("类型、");
            if (StrUtil.isEmpty(vo.getOncePerCustomerShow())) rowErrors.append("一个客户是否只能兑换一次、");
            if (StrUtil.isEmpty(vo.getCustomerSegmentShow())) rowErrors.append("客户群体、");
            if (StrUtil.isEmpty(vo.getApplyReasonShow())) rowErrors.append("申请原因、");
            if (rowErrors.length() > 0) {
                rowErrors.setLength(rowErrors.length() - 1);
                stringBuilder.append("第").append(i).append("行 - 缺少必填项: ").append(rowErrors).append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 基础字段验证
        i = 2;
        for (SomVcCouponsImportVo vo : list) {
            if (vo.getDiscountTypeShow().equals("Percent off")) {
                if (ObjectUtil.isEmpty(vo.getDiscountValueRate())) {
                    stringBuilder.append("第").append(i).append("行 - 当类型=Percent off时，折扣比例不能为空").append("\n");
                }
            } else {
                if (ObjectUtil.isEmpty(vo.getDiscountValueMoney())) {
                    stringBuilder.append("第").append(i).append("行 - 当类型=Money off时，折扣金额不能为空").append("\n");
                }
            }
            // 预算必须大于0
            if (vo.getBudget().compareTo(BigDecimal.ZERO) <= 0) {
                stringBuilder.append("第").append(i).append("行 - 预算必须大于0").append("\n");
            }
            // 活动时间
            if (vo.getStartDate().after(vo.getEndDate())) {
                stringBuilder.append("第").append(i).append("行 - 活动截止日期必须晚于起始日期").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 唯一性验证
        i = 2;
        for (SomVcCouponsImportVo vo : list) {
            SomVcCouponsVo somVcCouponsVo = new SomVcCouponsVo();
            BeanUtil.copyProperties(vo, somVcCouponsVo);
            Integer count = somVcCouponsMapper.checkUnique(somVcCouponsVo);
            if (count > 0) {
                stringBuilder.append("第").append(i).append("行 - 该产品已经存在优惠券，不允许重复新增").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 字典值、其他基础字段转换
        List<McDictionaryInfo> dictListVcCouponCustomerSegment = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponCustomerSegment").select();
        Map<String, String> dictMapVcCouponCustomerSegment = dictListVcCouponCustomerSegment.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcCouponDiscountType = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponDiscountType").select();
        Map<String, String> dictMapVcCouponDiscountType = dictListVcCouponDiscountType.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcCouponDistribution = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcCouponDistribution").select();
        Map<String, String> dictMapVcCouponDistribution = dictListVcCouponDistribution.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, McDictionaryInfo::getItemValue, (x1, x2) -> x1));
        List<McDictionaryInfo> dictListVcPromotionApplyReason = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "VcPromotionApplyReason").select();
        Map<String, McDictionaryInfo> dictMapVcPromotionApplyReason = dictListVcPromotionApplyReason.stream().collect(Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1));
        // 平台站点属性配置 获取币种
        List<McPlatformProperties> mcPlatformPropertiesList = dynamicSqlManager.getMapper(McPlatformPropertiesMapper.class).createLambdaQuery().andIn("site", siteList).select();
        Map<String, String> mcPlatformPropertiesMap = mcPlatformPropertiesList.stream().collect(Collectors.toMap(McPlatformProperties::getSite, McPlatformProperties::getCurrencyCode, (x1, x2) -> x1));
        for (SomVcCouponsImportVo vo : list) {
            // 字典值转换
            vo.setCustomerSegment(Integer.valueOf(dictMapVcCouponCustomerSegment.get(vo.getCustomerSegmentShow()))); // 客户群体
            vo.setDiscountType(Integer.valueOf(dictMapVcCouponDiscountType.get(vo.getDiscountTypeShow()))); // 优惠券类型
            vo.setOncePerCustomer(vo.getOncePerCustomerShow().equals("是") ? 1 : 0); // 一个客户是否只能兑换一次
            if (StrUtil.isNotBlank(vo.getCouponDistributionShow())) {
                vo.setCouponDistribution(Integer.valueOf(dictMapVcCouponDistribution.get(vo.getCouponDistributionShow()))); // 优惠券分布
            }
            vo.setApplyReason(Integer.valueOf(dictMapVcPromotionApplyReason.get(vo.getApplyReasonShow()).getItemValue())); // 申请原因
            // 币种
            vo.setCurrency(mcPlatformPropertiesMap.get(vo.getSite()));
            // 折扣比例 -> 折扣比值 与前端保持一致
            if (vo.getDiscountType().equals(10)) {
                vo.setDiscountValue(vo.getDiscountValueRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            } else {
                vo.setDiscountValue(vo.getDiscountValueMoney());
            }
        }

        // 获取ASIN、SKU等销售视图相关数据
        Map<String, McProductSalesVo> mcProductSalesVoMap = null;
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setSiteList(siteList);
        if (useSellerSkuOrAsin.equals("sellerSku")) {
            mcProductSalesSearchVo.setDisplayProductCodeList(sellerSkuList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));
        }
        if (useSellerSkuOrAsin.equals("asin")) {
            mcProductSalesSearchVo.setAsinList(asinList);
            List<McProductSalesVo> mcProductSalesVoList = dynamicSqlManager.getMapper(McProductSalesMapper.class).getProductSales(mcProductSalesSearchVo);
            mcProductSalesVoMap = mcProductSalesVoList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getAsinCode(), Function.identity(), (x1, x2) -> x1));
        }

        i = 2;
        for (SomVcCouponsImportVo vo : list) {
            String key = "";
            if (useSellerSkuOrAsin.equals("sellerSku")) {
                key = vo.getSite() + vo.getSellerSku();
            }
            if (useSellerSkuOrAsin.equals("asin")) {
                key = vo.getSite() + vo.getAsin();
            }
            McProductSalesVo mcProductSalesVo = mcProductSalesVoMap.get(key);
            if (mcProductSalesVo != null) {
                vo.setSellerSku(mcProductSalesVo.getDisplayProductCode());
                vo.setAsin(mcProductSalesVo.getAsinCode());
                vo.setSku(mcProductSalesVo.getProductMainCode());
                vo.setSalesGroupCode(mcProductSalesVo.getSalesGroupCode());
                vo.setSalesGroupName(mcProductSalesVo.getSalesGroupName());
                vo.setSalesGroupEmptCode(mcProductSalesVo.getSalesGroupEmptCode());
                vo.setSalesGroupEmptName(mcProductSalesVo.getSalesGroupEmptName());
                vo.setOperationEmptCode(mcProductSalesVo.getOperationEmptCode());
                vo.setOperationEmptName(mcProductSalesVo.getOperationEmptName());
            } else {
                stringBuilder.append("第").append(i).append("行 - 销售视图查询不存在").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 建议零售价RRP
        asinList = list.stream().map(SomVcCouponsImportVo::getAsin).collect(Collectors.toList());
        sellerSkuList = list.stream().map(SomVcCouponsImportVo::getSellerSku).collect(Collectors.toList());
        List<SomAmazonVcPrice> somAmazonVcPriceList = dynamicSqlManager.getMapper(SomAmazonVcPriceMapper.class).createLambdaQuery()
                .andIn("site", siteList)
                .andIn("seller_sku", sellerSkuList)
                .andIn("asin", asinList)
                .select();
        Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = somAmazonVcPriceList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getSellerSku() + x.getAsin(),
                Function.identity(),
                (x1, x2) -> x1
        ));
        i = 2;
        for (SomVcCouponsImportVo vo : list) {
            String key = vo.getSite() + vo.getSellerSku() + vo.getAsin();
            SomAmazonVcPrice somAmazonVcPrice = somAmazonVcPriceMap.get(key);
            if (somAmazonVcPrice != null) {
                vo.setRecommendedRetailPrice(somAmazonVcPrice.getRecommendedRetailPrice());
            } else {
                stringBuilder.append("第").append(i).append("行 - 此产品未维护RRP，请先维护RRP").append("\n");
            }
            i++;
        }
        if (stringBuilder.length() > 0) {
            return stringBuilder.append("请检查").toString();
        }

        // 根据「优惠券标题」分组，同一组创建一条内容
        Map<String, List<SomVcCouponsImportVo>> somVcCouponsImportVoMap = list.stream().collect(Collectors.groupingBy(
                SomVcCouponsImportVo::getCouponName
        ));

        List<SomVcCoupons> insertList = new ArrayList<>();
        List<SomVcCouponsItem> insertListItem = new ArrayList<>();
        for (Map.Entry<String, List<SomVcCouponsImportVo>> entry : somVcCouponsImportVoMap.entrySet()) {
            String key = entry.getKey();
            List<SomVcCouponsImportVo> voList = entry.getValue();
            SomVcCouponsImportVo vo = voList.get(0);

            // 主数据
            SomVcCoupons somVcCoupons = new SomVcCoupons();
            String couponAid = IdUtil.fastSimpleUUID();
            somVcCoupons.setAid(couponAid);
            somVcCoupons.setSite(vo.getSite());
            somVcCoupons.setAccountName(vo.getAccountName());
            somVcCoupons.setVendorCode(vo.getVendorCode());
            somVcCoupons.setPromotionName(vo.getPromotionName());
            somVcCoupons.setCustomerSegment(vo.getCustomerSegment());
            somVcCoupons.setCouponName(key);
            somVcCoupons.setWebsiteDisplayName(vo.getWebsiteDisplayName());
            somVcCoupons.setVendorInvoiceCode(vo.getVendorInvoiceCode());
            somVcCoupons.setBudget(vo.getBudget());
            somVcCoupons.setCurrency(vo.getCurrency());
            somVcCoupons.setStartDate(vo.getStartDate());
            somVcCoupons.setEndDate(vo.getEndDate());
            somVcCoupons.setDiscountType(vo.getDiscountType());
            somVcCoupons.setDiscountValue(vo.getDiscountValue());
            somVcCoupons.setOncePerCustomer(vo.getOncePerCustomer());
            somVcCoupons.setCouponDistribution(vo.getCouponDistribution());
            somVcCoupons.setCouponStatus(VcPromotionStatusEnum.DRAFT.getStatus());
            somVcCoupons.setApplyReason(vo.getApplyReason());
            somVcCoupons.setCustomReason(vo.getCustomReason());
            somVcCoupons.setCreateTime(DateTime.now().toJdkDate());
            somVcCoupons.setCreateName(tokenUser.getUserName());
            somVcCoupons.setCreateNum(tokenUser.getJobNumber());
            insertList.add(somVcCoupons);

            // 从数据
            for (SomVcCouponsImportVo itemVo : voList) {
                SomVcCouponsItem somVcCouponsItem = new SomVcCouponsItem();
                somVcCouponsItem.setAid(IdUtil.fastSimpleUUID());
                somVcCouponsItem.setCouponAid(couponAid);
                somVcCouponsItem.setSellerSku(itemVo.getSellerSku());
                somVcCouponsItem.setAsin(itemVo.getAsin());
                somVcCouponsItem.setSku(itemVo.getSku());
                somVcCouponsItem.setSalesGroupCode(itemVo.getSalesGroupCode());
                somVcCouponsItem.setSalesGroupName(itemVo.getSalesGroupName());
                somVcCouponsItem.setSalesGroupEmptCode(itemVo.getSalesGroupEmptCode());
                somVcCouponsItem.setSalesGroupEmptName(itemVo.getSalesGroupEmptName());
                somVcCouponsItem.setOperationEmptCode(itemVo.getOperationEmptCode());
                somVcCouponsItem.setOperationEmptName(itemVo.getOperationEmptName());
                somVcCouponsItem.setRecommendedRetailPrice(itemVo.getRecommendedRetailPrice());
                // 折扣金额
                // 主从数据 使用主数据的数据值计算 防止导入数据中出现从数据多条不一致 计算结果异常
                BigDecimal perUnitFunding = BigDecimal.ZERO;
                BigDecimal rrp = itemVo.getRecommendedRetailPrice();
                BigDecimal discountValue = vo.getDiscountValue();
                if (vo.getDiscountType().equals(10)) {
                    // RRP - RRP * 主表的折扣比例
                    discountValue = discountValue.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                    perUnitFunding = rrp.subtract(rrp.multiply(discountValue)).setScale(2, RoundingMode.HALF_UP);
                } else {
                    // RRP - 主表的折扣金额
                    perUnitFunding = rrp.subtract(discountValue).setScale(2, RoundingMode.HALF_UP);
                }
                somVcCouponsItem.setPerUnitFunding(perUnitFunding);
                somVcCouponsItem.setCurrency(itemVo.getCurrency());
                insertListItem.add(somVcCouponsItem);
            }
        }
        if (CollUtil.isNotEmpty(insertList)) {
            somVcCouponsMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(insertListItem)) {
            somVcCouponsItemMapper.insertBatch(insertListItem);
        }

        return "";
    }
}
