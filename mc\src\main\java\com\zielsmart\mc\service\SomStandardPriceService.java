package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomStandardPrice;
import com.zielsmart.mc.repository.entity.SomStandardPriceHistory;
import com.zielsmart.mc.repository.entity.SysUserNeweya;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.SomStandardPriceExtVo;
import com.zielsmart.mc.vo.SomStandardPriceImportVo;
import com.zielsmart.mc.vo.SomStandardPricePageSearchVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomStandardPriceService {

    @Resource
    private SomStandardPriceMapper somStandardPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private SomStandardPriceHistoryMapper somStandardPriceHistoryMapper;
    @Resource
    private McPlatformPropertiesMapper platformPropertiesMapper;
    @Resource
    private SysUserNeweyaMapper userNeweyaMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomStandardPriceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomStandardPriceExtVo> queryByPage(SomStandardPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
//        if (ObjectUtil.isNotNull(searchVo.getBusinessGroupName()) && searchVo.getBusinessGroupName().endsWith("BU")) {
//            searchVo.setBuGroupCode(searchVo.getBusinessGroupCode());
//            searchVo.setBusinessGroupCode(null);
//        }
        PageResult<SomStandardPriceExtVo> pageResult = dynamicSqlManager.getMapper(SomStandardPriceMapper.class).pageSearchStandPrice(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<String> codeList = Arrays.asList(new String[]{"Country", "SalesProductStatus", "IsConsignmentSales", "ProductStatus"});
            List<McDictionaryInfo> list = mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
            Map<String, String> dictMap = list.stream().collect(Collectors.toMap(x -> x.getItemTypeCode() + x.getItemValue(), v -> v.getItemLable(), (x1, x2) -> x1));

            List<SysUserNeweya> userList = userNeweyaMapper.createLambdaQuery().select("employee_number", "em_name_cn");
            Map<String, String> userMap = userList.stream().collect(Collectors.toMap(x -> x.getEmployeeNumber(), v -> v.getemNameCn(), (x1, x2) -> x1));

            pageResult.getList().forEach(f -> {
                // 国家
                f.setCountryName(dictMap.getOrDefault("Country" + f.getCountry(), null));
                // 展示码状态
                f.setSellerSkuStatusName(dictMap.getOrDefault("SalesProductStatus" + f.getSellerSkuStatusCode(), null));
                // 发货方式
                f.setIsConsignmentSalesName(dictMap.getOrDefault("IsConsignmentSales" + f.getIsConsignmentSales(), null));
                //产品状态
                f.setProductStatusCodeName(dictMap.getOrDefault("ProductStatus" + f.getProductStatusCode(), null));
                if (ObjectUtil.isNotNull(f.getSalesGroupEmptCode())) {
                    f.setSalesGroupEmptName(userMap.get(f.getSalesGroupEmptCode()));
                }
                if (ObjectUtil.isNotNull(f.getOperationEmptCode())) {
                    f.setOperationEmptName(userMap.get(f.getOperationEmptCode()));
                }

//                if (ObjectUtil.isNotNull(f.getMinimumGrossProfitMargin())) {
//                    f.setMinimumGrossProfitMarginName(f.getMinimumGrossProfitMargin() + "%");
//                }
//                if (ObjectUtil.isNotNull(f.getHighestGrossProfitMargin())) {
//                    f.setHighestGrossProfitMarginName(f.getHighestGrossProfitMargin() + "%");
//                }
//                if (ObjectUtil.isNotNull(f.getStandardGrossMargin())) {
//                    f.setStandardGrossMarginName(f.getStandardGrossMargin() + "%");
//                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomStandardPriceExtVo.class, searchVo);
    }


    /**
     * 导入
     * @param importList 行数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomStandardPriceImportVo> importList, TokenUserInfo tokenUser) throws ValidateException {
        // 查询可编辑的列表数据
        List<String> siteList = importList.stream().map(SomStandardPriceImportVo::getSite).distinct().collect(Collectors.toList());
        Map<String, SomStandardPriceExtVo> effectiveMap = new HashMap<>();
        if (CollUtil.isNotEmpty(siteList)) {
            // 查询可编辑的列表数据
            List<SomStandardPriceExtVo> effectiveList = somStandardPriceMapper.effectivePriceData(new SomStandardPriceExtVo(), siteList);
            effectiveMap = effectiveList.stream().collect(Collectors.toMap(x -> x.getSite() + x.getProductMainCode() + x.getSellerSku() + x.getIsConsignmentSales(), y -> y, (x1, x2) -> x1));
        }
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importList, effectiveMap);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        Date jdkDate = DateTime.now().toJdkDate();
        // 查询定价表全量数据
        List<SomStandardPrice> standardPrices = somStandardPriceMapper.createLambdaQuery().andIn("site", siteList).select();
        Map<String, SomStandardPrice> standardPriceMap = standardPrices.stream().collect(Collectors.toMap(SomStandardPrice::getAid, Function.identity()));
        List<SomStandardPrice> insertList = new ArrayList<>();
        List<SomStandardPrice> updateList = new ArrayList<>();
        List<SomStandardPriceHistory> insertHistoryList = new ArrayList<>();
        for (SomStandardPriceImportVo importVo : importList) {
            SomStandardPriceExtVo priceVo = effectiveMap.getOrDefault(importVo.getSite() + importVo.getProductMainCode() + importVo.getSellerSku() + importVo.getIsConsignmentSales(), null);
            if (StrUtil.isBlank(priceVo.getAid())) {
                // 新增
                SomStandardPrice somStandardPrice = ConvertUtils.beanConvert(importVo, SomStandardPrice.class);
                somStandardPrice.setPlatform(priceVo.getPlatform());
                somStandardPrice.setCreateName(tokenUser.getUserName());
                somStandardPrice.setCreateTime(jdkDate);
                somStandardPrice.setCreateNum(tokenUser.getJobNumber());
                somStandardPrice.setModifyName(tokenUser.getUserName());
                somStandardPrice.setModifyTime(jdkDate);
                somStandardPrice.setModifyNum(tokenUser.getJobNumber());
                somStandardPrice.setDataSource("导入新增");
                somStandardPrice.setAid(IdUtil.fastSimpleUUID());
                somStandardPrice.setCurrencyCode(priceVo.getCurrencyCode());
                SomStandardPriceHistory somStandardPriceHistory = ConvertUtils.beanConvert(somStandardPrice, SomStandardPriceHistory.class);
                somStandardPriceHistory.setAid(IdUtil.fastSimpleUUID());

                insertHistoryList.add(somStandardPriceHistory);
                insertList.add(somStandardPrice);
            } else {
                // 更新
                SomStandardPrice dbPrice = standardPriceMap.get(priceVo.getAid());
                if (dbPrice != null) {
                    dbPrice.setStandardPrice(importVo.getStandardPrice());
                    dbPrice.setMinimumPrice(importVo.getMinimumPrice());
                    dbPrice.setRecommendedRetailPrice(importVo.getRecommendedRetailPrice());
                    dbPrice.setHighestPrice(importVo.getHighestPrice());
                    dbPrice.setModifyName(tokenUser.getUserName());
                    dbPrice.setModifyTime(jdkDate);
                    dbPrice.setModifyNum(tokenUser.getJobNumber());
                    dbPrice.setDataSource("导入更新");
                    SomStandardPriceHistory somStandardPriceHistory = ConvertUtils.beanConvert(dbPrice, SomStandardPriceHistory.class);
                    somStandardPriceHistory.setAid(IdUtil.fastSimpleUUID());

                    updateList.add(dbPrice);
                    insertHistoryList.add(somStandardPriceHistory);
                }
            }
        }
        if (!insertList.isEmpty()) {
            somStandardPriceMapper.insertBatch(insertList);
        }
        if (!insertHistoryList.isEmpty()) {
            somStandardPriceHistoryMapper.insertBatch(insertHistoryList);
        }
        if (!updateList.isEmpty()) {
            somStandardPriceMapper.updateBatch(updateList);
        }
    }

    /**
     * exportExcel
     * 导出
     *
     * @param searchVo
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    public String exportExcel(SomStandardPricePageSearchVo searchVo) {
        List<SomStandardPriceExtVo> exportVoList = dynamicSqlManager.getMapper(SomStandardPriceMapper.class).excelSearchStandPrice(searchVo);
        if (exportVoList.isEmpty()) {
            return null;
        }
        List<String> codeList = Arrays.asList(new String[]{"Country", "SalesProductStatus", "IsConsignmentSales", "ProductStatus"});
        List<McDictionaryInfo> list = mcDictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", codeList).select();
        Map<String, String> dictMap = list.stream().collect(Collectors.toMap(x -> x.getItemTypeCode() + x.getItemValue(), v -> v.getItemLable(), (x1, x2) -> x1));
        List<SysUserNeweya> userList = userNeweyaMapper.createLambdaQuery().select("employee_number", "em_name_cn");
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(x -> x.getEmployeeNumber(), v -> v.getemNameCn(), (x1, x2) -> x1));

        exportVoList.forEach(f -> {
            // 国家
            f.setCountryName(dictMap.getOrDefault("Country" + f.getCountry(), null));
            // 展示码状态
            f.setSellerSkuStatusName(dictMap.getOrDefault("SalesProductStatus" + f.getSellerSkuStatusCode(), null));
            // 发货方式
            f.setIsConsignmentSalesName(dictMap.getOrDefault("IsConsignmentSales" + f.getIsConsignmentSales(), null));
            //产品状态
            f.setProductStatusCodeName(dictMap.getOrDefault("ProductStatus" + f.getProductStatusCode(), null));
            if (ObjectUtil.isNotNull(f.getSalesGroupEmptCode())) {
                f.setSalesGroupEmptName(userMap.get(f.getSalesGroupEmptCode()));
            }
            if (ObjectUtil.isNotNull(f.getOperationEmptCode())) {
                f.setOperationEmptName(userMap.get(f.getOperationEmptCode()));
            }
        });
        if (CollectionUtil.isNotEmpty(exportVoList)) {
            try {
                Workbook workbook;
                ExportParams params = new ExportParams(null, "定价管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomStandardPriceExtVo.class, exportVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * edit
     * 编辑标准价格信息
     *
     * @param priceExtVo 价格扩展对象
     * @param tokenUser  当前用户信息
     * @throws ValidateException 验证异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(SomStandardPriceExtVo priceExtVo, TokenUserInfo tokenUser) throws ValidateException {
        // 校验价格不能为空
        if (StrUtil.isBlank(priceExtVo.getSite())) {
            throw new ValidateException("站点不能为空");
        }
        if (StrUtil.isBlank(priceExtVo.getProductMainCode())) {
            throw new ValidateException("SKU不能为空");
        }
        if (StrUtil.isBlank(priceExtVo.getSellerSku())) {
            throw new ValidateException("展示码不能为空");
        }
        if (ObjectUtil.isNull(priceExtVo.getIsConsignmentSales())) {
            throw new ValidateException("发货方式不能为空");
        }
        if (ObjectUtil.isNull(priceExtVo.getRecommendedRetailPrice())) {
            throw new ValidateException("RRP不允许为空");
        }
        if (ObjectUtil.isNull(priceExtVo.getMinimumPrice())) {
            throw new ValidateException("最低价不允许为空");
        }
        if (ObjectUtil.isNull(priceExtVo.getStandardPrice())) {
            throw new ValidateException("标准价不允许为空");
        }
        if (ObjectUtil.isNull(priceExtVo.getHighestPrice())) {
            throw new ValidateException("最高价不允许为空");
        }

        // 校验价格区间关系：最低价 < 标准价 < 最高价
        if (!(priceExtVo.getStandardPrice().compareTo(priceExtVo.getMinimumPrice()) > 0
                && priceExtVo.getHighestPrice().compareTo(priceExtVo.getStandardPrice()) > 0)) {
            throw new ValidateException("请按照最低价＜标准价＜最高价 规则维护价格区间！");
        }

        //检测编辑的数据是否能通过列表查询出来  是否符合编辑条件
        List<SomStandardPriceExtVo> effectiveData = somStandardPriceMapper.effectivePriceData(priceExtVo,null);
        if (CollectionUtil.isEmpty(effectiveData)) {
            throw new ValidateException("展示码 " + priceExtVo.getSellerSku() + "不存在");
        }

        priceExtVo.setModifyName(tokenUser.getUserName());
        priceExtVo.setModifyTime(DateTime.now().toJdkDate());
        priceExtVo.setModifyNum(tokenUser.getJobNumber());


        //有aid则根据aid进行更新
        if (ObjectUtil.isNotNull(priceExtVo.getAid())) {
            priceExtVo.setDataSource("编辑更新");
            SomStandardPrice single = somStandardPriceMapper.single(priceExtVo.getAid());
            priceExtVo.setCreateName(single.getCreateName());
            priceExtVo.setCreateTime(single.getCreateTime());
            priceExtVo.setCreateNum(single.getCreateNum());
            somStandardPriceMapper.updateTemplateById(ConvertUtils.beanConvert(priceExtVo, SomStandardPrice.class));
        } else {

            //检测数据库表中是否有这条数据
            long count = somStandardPriceMapper.createLambdaQuery().andEq("site", priceExtVo.getSite())
                    .andEq("product_main_code", priceExtVo.getProductMainCode())
                    .andEq("seller_sku", priceExtVo.getSellerSku())
                    .andEq("is_consignment_sales", priceExtVo.getIsConsignmentSales())
                    .count();
            if (count > 0) {
                throw new ValidateException("展示码" + priceExtVo.getSellerSku() + "已在表中维护");
            }
            //执行插入操作
            priceExtVo.setCreateName(tokenUser.getUserName());
            priceExtVo.setCreateTime(DateTime.now().toJdkDate());
            priceExtVo.setCreateNum(tokenUser.getJobNumber());
            priceExtVo.setAid(IdUtil.fastSimpleUUID());
            priceExtVo.setDataSource("编辑插入");
            SomStandardPrice standardPrice = ConvertUtils.beanConvert(priceExtVo, SomStandardPrice.class);
            somStandardPriceMapper.insert(standardPrice);
        }

        // 保存历史记录
        SomStandardPriceHistory history = ConvertUtils.beanConvert(priceExtVo, SomStandardPriceHistory.class);
        history.setAid(IdUtil.fastSimpleUUID());
        somStandardPriceHistoryMapper.insert(history);
    }

    /**
     * 基础核验导入数据
     *
     * @param imports 导入行数据
     * @param effectiveMap 查询可编辑的列表数据
     * @return 错误信息
     */
    private List<String> checkImportData(List<SomStandardPriceImportVo> imports, Map<String, SomStandardPriceExtVo> effectiveMap) throws ValidateException {
        // 核验必填项
        List<String> errors = checkImportRequired(imports);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 核验重复
        Set<String> repeatCheckSet = new HashSet<>();
        // 报错需要提示出行号
        int index = 2;
        for (SomStandardPriceImportVo importVo : imports) {
            String lineErrorMsg = StrUtil.concat(true, "第", String.valueOf(index), "行：");
            // 校验表格中的数据是否重复（站点+SKU+展示码+发货方式）
            String key = StrUtil.concat(true, importVo.getSite(), importVo.getProductMainCode(), importVo.getSellerSku(), importVo.getIsConsignmentSalesName());
            if (repeatCheckSet.contains(key)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "文件中存在重复数据，导入失败！"));
            }
            repeatCheckSet.add(key);
            // 校验价格
            if (!(importVo.getStandardPrice().compareTo(importVo.getMinimumPrice()) > 0 && importVo.getHighestPrice().compareTo(importVo.getStandardPrice()) > 0)) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "请按照[最低价＜标准价＜最高价]规则维护价格区间，导入失败！"));
            }
            SomStandardPriceExtVo priceVo = effectiveMap.getOrDefault(importVo.getSite() + importVo.getProductMainCode() + importVo.getSellerSku() + importVo.getIsConsignmentSales(), null);
            if (priceVo == null) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "展示码[", importVo.getSellerSku(), "]不存在，导入失败！"));
            }
            index++;
        }
        return errors;
    }

    /**
     * 核验导入必填项
     *
     * @param imports 行数据
     * @return 错误集合
     */
    private List<String> checkImportRequired(List<SomStandardPriceImportVo> imports) {
        List<String> errors = new ArrayList<>();
        // 报错需要提示出行号
        int index = 2;
        for (SomStandardPriceImportVo importVo : imports) {
            // 校验数据 站点 SKU 展示码 发货方式 RRP 最低价 标准价 最高价 不能为空
            String lineErrorMsg = StrUtil.concat(true, "第", String.valueOf(index), "行：");
            if (StrUtil.isBlank(importVo.getSite())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "站点不能为空！"));
            }
            if (StrUtil.isBlank(importVo.getProductMainCode())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "SKU不能为空！"));
            }
            if (StrUtil.isBlank(importVo.getSellerSku())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "展示码不能为空！"));
            }
            if (StrUtil.isBlank(importVo.getIsConsignmentSalesName())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "发货方式不能为空！"));
            }
            if (ObjectUtil.isNull(importVo.getRecommendedRetailPrice())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "RRP不能为空！"));
            }
            if (ObjectUtil.isNull(importVo.getMinimumPrice())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "最低价不能为空！"));
            }
            if (ObjectUtil.isNull(importVo.getStandardPrice())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "标准价不能为空！"));
            }
            if (ObjectUtil.isNull(importVo.getHighestPrice())) {
                errors.add(StrUtil.concat(true, lineErrorMsg, "最高价不能为空！"));
            }
            // 填充发货方式
            if (StrUtil.isNotEmpty(importVo.getIsConsignmentSalesName())) {
                if ("自发".equals(importVo.getIsConsignmentSalesName())) {
                    importVo.setIsConsignmentSales(0);
                } else if ("寄售".equals(importVo.getIsConsignmentSalesName())) {
                    importVo.setIsConsignmentSales(1);
                } else {
                    errors.add(StrUtil.concat(true, lineErrorMsg, "发货方式有误，导入失败！"));
                }
            }
            index++;
        }
        return errors;
    }
}