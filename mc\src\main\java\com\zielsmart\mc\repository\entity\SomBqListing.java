package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* Conforama Listing信息表
* gen by 代码生成器 2024-12-17
*/

@Table(name="mc.som_bq_listing")
public class SomBqListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 0.false 1.true
	 */
	@Column("active")
	private Integer active ;
	/**
	 * 0.false 1.true
	 */
	@Column("allow_quote_requests")
	private Integer allowQuoteRequests ;
	/**
	 * 适用的定价，JSON字符串
	 */
	@Column("applicable_pricing")
	private String applicablePricing ;
	/**
	 * 可用起始日期
	 */
	@Column("available_start_date")
	private Date availableStartDate ;
	/**
	 * 可用截止日期
	 */
	@Column("available_end_date")
	private Date availableEndDate ;
	/**
	 * 类目编码
	 */
	@Column("category_code")
	private String categoryCode ;
	/**
	 * 类目名称
	 */
	@Column("category_label")
	private String categoryLabel ;
	/**
	 * 渠道集合,以逗号分割的字符串
	 */
	@Column("channels")
	private String channels ;
	/**
	 * 币种编码
	 */
	@Column("currency_iso_code")
	private String currencyIsoCode ;
	/**
	 * 产品描述
	 */
	@Column("description")
	private String description ;
	/**
	 * 折扣,JSON字符串
	 */
	@Column("discount")
	private String discount ;
	/**
	 * 排名
	 */
	@Column("favorite_rank")
	private Integer favoriteRank ;
	/**
	 * 交货周期
	 */
	@Column("leadtime_to_ship")
	private Integer leadtimeToShip ;
	/**
	 * 物流类型,JSON字符串
	 */
	@Column("logistic_class")
	private String logisticClass ;
	/**
	 * 最大订购数量
	 */
	@Column("max_order_quantity")
	private Integer maxOrderQuantity ;
	/**
	 * 最小订购数量
	 */
	@Column("min_order_quantity")
	private Integer minOrderQuantity ;
	/**
	 * offer的最小数量
	 */
	@Column("min_quantity_alert")
	private Integer minQuantityAlert ;
	/**
	 * 最小运费
	 */
	@Column("min_shipping_price")
	private BigDecimal minShippingPrice ;
	/**
	 * 运费附加费
	 */
	@Column("min_shipping_price_additional")
	private BigDecimal minShippingPriceAdditional ;
	/**
	 * 运送方式
	 */
	@Column("min_shipping_type")
	private String minShippingType ;
	/**
	 * 航运区
	 */
	@Column("min_shipping_zone")
	private String minShippingZone ;
	/**
	 * offer附加字段,JSON字符串
	 */
	@Column("offer_additional_fields")
	private String offerAdditionalFields ;
	/**
	 * 报价ID
	 */
	@Column("offer_id")
	private Integer offerId ;
	/**
	 * 包裹数量
	 */
	@Column("package_quantity")
	private Integer packageQuantity ;
	/**
	 * 售价
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 附加价格信息
	 */
	@Column("price_additional_info")
	private String priceAdditionalInfo ;
	/**
	 * 价格信息,JSON字符串
	 */
	@Column("all_prices")
	private String allPrices ;
	/**
	 * references
	 */
	@Column("product_references")
	private String productReferences ;
	/**
	 * references type
	 */
	@Column("product_references_type")
	private String productReferencesType ;
	/**
	 * SKU
	 */
	@Column("product_sku")
	private String productSku ;
	/**
	 * 产品税号
	 */
	@Column("product_tax_code")
	private String productTaxCode ;
	/**
	 * 产品标题
	 */
	@Column("product_title")
	private String productTitle ;
	/**
	 * 产品库存数量
	 */
	@Column("quantity")
	private Integer quantity ;
	/**
	 * 售卖SKU
	 */
	@Column("shop_sku")
	private String shopSku ;
	/**
	 * offer状态
	 */
	@Column("state_code")
	private String stateCode ;
	/**
	 * 总售价,总售价 = price - min_shipping_price
	 */
	@Column("total_price")
	private BigDecimal totalPrice ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomBqListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 0.false 1.true
	*@return
	*/
	public Integer getActive(){
		return  active;
	}
	/**
	* 0.false 1.true
	*@param  active
	*/
	public void setActive(Integer active ){
		this.active = active;
	}
	/**
	* 0.false 1.true
	*@return
	*/
	public Integer getAllowQuoteRequests(){
		return  allowQuoteRequests;
	}
	/**
	* 0.false 1.true
	*@param  allowQuoteRequests
	*/
	public void setAllowQuoteRequests(Integer allowQuoteRequests ){
		this.allowQuoteRequests = allowQuoteRequests;
	}
	/**
	* 适用的定价，JSON字符串
	*@return
	*/
	public String getApplicablePricing(){
		return  applicablePricing;
	}
	/**
	* 适用的定价，JSON字符串
	*@param  applicablePricing
	*/
	public void setApplicablePricing(String applicablePricing ){
		this.applicablePricing = applicablePricing;
	}
	/**
	* 可用起始日期
	*@return
	*/
	public Date getAvailableStartDate(){
		return  availableStartDate;
	}
	/**
	* 可用起始日期
	*@param  availableStartDate
	*/
	public void setAvailableStartDate(Date availableStartDate ){
		this.availableStartDate = availableStartDate;
	}
	/**
	* 可用截止日期
	*@return
	*/
	public Date getAvailableEndDate(){
		return  availableEndDate;
	}
	/**
	* 可用截止日期
	*@param  availableEndDate
	*/
	public void setAvailableEndDate(Date availableEndDate ){
		this.availableEndDate = availableEndDate;
	}
	/**
	* 类目编码
	*@return
	*/
	public String getCategoryCode(){
		return  categoryCode;
	}
	/**
	* 类目编码
	*@param  categoryCode
	*/
	public void setCategoryCode(String categoryCode ){
		this.categoryCode = categoryCode;
	}
	/**
	* 类目名称
	*@return
	*/
	public String getCategoryLabel(){
		return  categoryLabel;
	}
	/**
	* 类目名称
	*@param  categoryLabel
	*/
	public void setCategoryLabel(String categoryLabel ){
		this.categoryLabel = categoryLabel;
	}
	/**
	* 渠道集合,以逗号分割的字符串
	*@return
	*/
	public String getChannels(){
		return  channels;
	}
	/**
	* 渠道集合,以逗号分割的字符串
	*@param  channels
	*/
	public void setChannels(String channels ){
		this.channels = channels;
	}
	/**
	* 币种编码
	*@return
	*/
	public String getCurrencyIsoCode(){
		return  currencyIsoCode;
	}
	/**
	* 币种编码
	*@param  currencyIsoCode
	*/
	public void setCurrencyIsoCode(String currencyIsoCode ){
		this.currencyIsoCode = currencyIsoCode;
	}
	/**
	* 产品描述
	*@return
	*/
	public String getDescription(){
		return  description;
	}
	/**
	* 产品描述
	*@param  description
	*/
	public void setDescription(String description ){
		this.description = description;
	}
	/**
	* 折扣,JSON字符串
	*@return
	*/
	public String getDiscount(){
		return  discount;
	}
	/**
	* 折扣,JSON字符串
	*@param  discount
	*/
	public void setDiscount(String discount ){
		this.discount = discount;
	}
	/**
	* 排名
	*@return
	*/
	public Integer getFavoriteRank(){
		return  favoriteRank;
	}
	/**
	* 排名
	*@param  favoriteRank
	*/
	public void setFavoriteRank(Integer favoriteRank ){
		this.favoriteRank = favoriteRank;
	}
	/**
	* 交货周期
	*@return
	*/
	public Integer getLeadtimeToShip(){
		return  leadtimeToShip;
	}
	/**
	* 交货周期
	*@param  leadtimeToShip
	*/
	public void setLeadtimeToShip(Integer leadtimeToShip ){
		this.leadtimeToShip = leadtimeToShip;
	}
	/**
	* 物流类型,JSON字符串
	*@return
	*/
	public String getLogisticClass(){
		return  logisticClass;
	}
	/**
	* 物流类型,JSON字符串
	*@param  logisticClass
	*/
	public void setLogisticClass(String logisticClass ){
		this.logisticClass = logisticClass;
	}
	/**
	* 最大订购数量
	*@return
	*/
	public Integer getMaxOrderQuantity(){
		return  maxOrderQuantity;
	}
	/**
	* 最大订购数量
	*@param  maxOrderQuantity
	*/
	public void setMaxOrderQuantity(Integer maxOrderQuantity ){
		this.maxOrderQuantity = maxOrderQuantity;
	}
	/**
	* 最小订购数量
	*@return
	*/
	public Integer getMinOrderQuantity(){
		return  minOrderQuantity;
	}
	/**
	* 最小订购数量
	*@param  minOrderQuantity
	*/
	public void setMinOrderQuantity(Integer minOrderQuantity ){
		this.minOrderQuantity = minOrderQuantity;
	}
	/**
	* offer的最小数量
	*@return
	*/
	public Integer getMinQuantityAlert(){
		return  minQuantityAlert;
	}
	/**
	* offer的最小数量
	*@param  minQuantityAlert
	*/
	public void setMinQuantityAlert(Integer minQuantityAlert ){
		this.minQuantityAlert = minQuantityAlert;
	}
	/**
	* 最小运费
	*@return
	*/
	public BigDecimal getMinShippingPrice(){
		return  minShippingPrice;
	}
	/**
	* 最小运费
	*@param  minShippingPrice
	*/
	public void setMinShippingPrice(BigDecimal minShippingPrice ){
		this.minShippingPrice = minShippingPrice;
	}
	/**
	* 运费附加费
	*@return
	*/
	public BigDecimal getMinShippingPriceAdditional(){
		return  minShippingPriceAdditional;
	}
	/**
	* 运费附加费
	*@param  minShippingPriceAdditional
	*/
	public void setMinShippingPriceAdditional(BigDecimal minShippingPriceAdditional ){
		this.minShippingPriceAdditional = minShippingPriceAdditional;
	}
	/**
	* 运送方式
	*@return
	*/
	public String getMinShippingType(){
		return  minShippingType;
	}
	/**
	* 运送方式
	*@param  minShippingType
	*/
	public void setMinShippingType(String minShippingType ){
		this.minShippingType = minShippingType;
	}
	/**
	* 航运区
	*@return
	*/
	public String getMinShippingZone(){
		return  minShippingZone;
	}
	/**
	* 航运区
	*@param  minShippingZone
	*/
	public void setMinShippingZone(String minShippingZone ){
		this.minShippingZone = minShippingZone;
	}
	/**
	* offer附加字段,JSON字符串
	*@return
	*/
	public String getOfferAdditionalFields(){
		return  offerAdditionalFields;
	}
	/**
	* offer附加字段,JSON字符串
	*@param  offerAdditionalFields
	*/
	public void setOfferAdditionalFields(String offerAdditionalFields ){
		this.offerAdditionalFields = offerAdditionalFields;
	}
	/**
	* 报价ID
	*@return
	*/
	public Integer getOfferId(){
		return  offerId;
	}
	/**
	* 报价ID
	*@param  offerId
	*/
	public void setOfferId(Integer offerId ){
		this.offerId = offerId;
	}
	/**
	* 包裹数量
	*@return
	*/
	public Integer getPackageQuantity(){
		return  packageQuantity;
	}
	/**
	* 包裹数量
	*@param  packageQuantity
	*/
	public void setPackageQuantity(Integer packageQuantity ){
		this.packageQuantity = packageQuantity;
	}
	/**
	* 售价
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 售价
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 附加价格信息
	*@return
	*/
	public String getPriceAdditionalInfo(){
		return  priceAdditionalInfo;
	}
	/**
	* 附加价格信息
	*@param  priceAdditionalInfo
	*/
	public void setPriceAdditionalInfo(String priceAdditionalInfo ){
		this.priceAdditionalInfo = priceAdditionalInfo;
	}
	/**
	* 价格信息,JSON字符串
	*@return
	*/
	public String getAllPrices(){
		return  allPrices;
	}
	/**
	* 价格信息,JSON字符串
	*@param  allPrices
	*/
	public void setAllPrices(String allPrices ){
		this.allPrices = allPrices;
	}
	/**
	* references
	*@return
	*/
	public String getProductReferences(){
		return  productReferences;
	}
	/**
	* references
	*@param  productReferences
	*/
	public void setProductReferences(String productReferences ){
		this.productReferences = productReferences;
	}
	/**
	* references type
	*@return
	*/
	public String getProductReferencesType(){
		return  productReferencesType;
	}
	/**
	* references type
	*@param  productReferencesType
	*/
	public void setProductReferencesType(String productReferencesType ){
		this.productReferencesType = productReferencesType;
	}
	/**
	* SKU
	*@return
	*/
	public String getProductSku(){
		return  productSku;
	}
	/**
	* SKU
	*@param  productSku
	*/
	public void setProductSku(String productSku ){
		this.productSku = productSku;
	}
	/**
	* 产品税号
	*@return
	*/
	public String getProductTaxCode(){
		return  productTaxCode;
	}
	/**
	* 产品税号
	*@param  productTaxCode
	*/
	public void setProductTaxCode(String productTaxCode ){
		this.productTaxCode = productTaxCode;
	}
	/**
	* 产品标题
	*@return
	*/
	public String getProductTitle(){
		return  productTitle;
	}
	/**
	* 产品标题
	*@param  productTitle
	*/
	public void setProductTitle(String productTitle ){
		this.productTitle = productTitle;
	}
	/**
	* 产品库存数量
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 产品库存数量
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	/**
	* 售卖SKU
	*@return
	*/
	public String getShopSku(){
		return  shopSku;
	}
	/**
	* 售卖SKU
	*@param  shopSku
	*/
	public void setShopSku(String shopSku ){
		this.shopSku = shopSku;
	}
	/**
	* offer状态
	*@return
	*/
	public String getStateCode(){
		return  stateCode;
	}
	/**
	* offer状态
	*@param  stateCode
	*/
	public void setStateCode(String stateCode ){
		this.stateCode = stateCode;
	}
	/**
	* 总售价,总售价 = price - min_shipping_price
	*@return
	*/
	public BigDecimal getTotalPrice(){
		return  totalPrice;
	}
	/**
	* 总售价,总售价 = price - min_shipping_price
	*@param  totalPrice
	*/
	public void setTotalPrice(BigDecimal totalPrice ){
		this.totalPrice = totalPrice;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
