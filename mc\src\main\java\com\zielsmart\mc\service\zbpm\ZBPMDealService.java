package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomDealOfTheDay;
import com.zielsmart.mc.repository.entity.SomFreeAddDeal;
import com.zielsmart.mc.repository.entity.SomFreeAddDealItems;
import com.zielsmart.mc.repository.entity.SomPddAndOd;
import com.zielsmart.mc.repository.mapper.SomDealOfTheDayMapper;
import com.zielsmart.mc.repository.mapper.SomFreeAddDealItemsMapper;
import com.zielsmart.mc.repository.mapper.SomFreeAddDealMapper;
import com.zielsmart.mc.repository.mapper.SomPddAndOdMapper;
import com.zielsmart.mc.util.FeiShuUtils;
import com.zielsmart.mc.vo.zbpm.ZBPMDealDetailVo;
import com.zielsmart.mc.vo.zbpm.ZBPMDealVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZBPMDealService {
    @Resource
    private SomFreeAddDealMapper freeAddDealMapper;
    @Resource
    private SomFreeAddDealItemsMapper freeAddDealItemsMapper;
    @Resource
    private SomDealOfTheDayMapper dealOfTheDayMapper;
    @Resource
    private SomPddAndOdMapper pddAndOdMapper;

    /**
     * updateDeal
     * 更新状态
     *
     * @param dealVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDeal(ZBPMDealVo dealVo) throws ValidateException {
        log.info("Deal活动更新开始,接收参数如下{}", JSONUtil.toJsonStr(dealVo));
        if (ObjectUtil.isEmpty(dealVo) || CollectionUtil.isEmpty(dealVo.getAidList()) || StrUtil.isBlank(dealVo.getDealTypeName()) || StrUtil.isBlank(dealVo.getResultMsg())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        if (StrUtil.equals("未通过", dealVo.getResultMsg()) && StrUtil.isBlank(dealVo.getResultResason())) {
            throw new ValidateException("审核未通过时,审批原因不能为空");
        }
        if (StrUtil.equals("未通过", dealVo.getResultMsg()) && StrUtil.isBlank(dealVo.getJobNumber())) {
            throw new ValidateException("审核未通过时,发起人不能为空");
        }
        int status = StrUtil.equals("未通过",dealVo.getResultMsg()) ? 29 : 21;
        String ids = dealVo.getAidList().stream().collect(Collectors.joining("','", "'", "'"));
        if (StrUtil.equalsIgnoreCase("7 Day Deal", dealVo.getDealTypeName()) || StrUtil.equalsIgnoreCase("Lighting Deal", dealVo.getDealTypeName())) {
            String sql = StrUtil.format("UPDATE mc.som_free_add_deal set status = {},audit_time = '{}' WHERE aid in({})", status, DateTime.now(), ids);
            if (StrUtil.equals("未通过", dealVo.getResultMsg())) {
                sql = StrUtil.format("UPDATE mc.som_free_add_deal set status = {},audit_failure_remark = '{}',audit_time = '{}' WHERE aid in({})", status, dealVo.getResultResason(), DateTime.now(), ids);
            }
            freeAddDealMapper.executeUpdate(sql);
        } else if (StrUtil.equalsIgnoreCase("PDD", dealVo.getDealTypeName()) || StrUtil.equalsIgnoreCase("Outlet Deal", dealVo.getDealTypeName())|| StrUtil.equalsIgnoreCase("Price Discount", dealVo.getDealTypeName())) {
            String sql = StrUtil.format("UPDATE mc.som_pdd_and_od set status = {},audit_time = '{}' WHERE aid in({})", status, DateTime.now(), ids);
            if (StrUtil.equals("未通过", dealVo.getResultMsg())) {
                sql = StrUtil.format("UPDATE mc.som_pdd_and_od set status = {},audit_failure_remark = '{}',audit_time = '{}' WHERE aid in({})", status, dealVo.getResultResason(), DateTime.now(), ids);
            }
            pddAndOdMapper.executeUpdate(sql);
        } else {
            String sql = StrUtil.format("UPDATE mc.som_deal_of_the_day set status = {},audit_time = '{}' WHERE aid in({})", status, DateTime.now(), ids);
            if (StrUtil.equals("未通过", dealVo.getResultMsg())) {
                sql = StrUtil.format("UPDATE mc.som_deal_of_the_day set status = {},audit_failure_remark = '{}',audit_time = '{}' WHERE aid in({})", status, dealVo.getResultResason(), DateTime.now(), ids);
            }
            dealOfTheDayMapper.executeUpdate(sql);
        }
        if (StrUtil.equals("未通过", dealVo.getResultMsg())){
            List<ZBPMDealDetailVo> dealDetailList = new ArrayList<>();
            try {
                if (StrUtil.equalsIgnoreCase("7 Day Deal", dealVo.getDealTypeName()) || StrUtil.equalsIgnoreCase("Lighting Deal", dealVo.getDealTypeName())) {
                    SomFreeAddDeal ld7dd = freeAddDealMapper.createLambdaQuery().andIn("aid", dealVo.getAidList()).single();
                    List<SomFreeAddDealItems> ld7ddItems = freeAddDealItemsMapper.createLambdaQuery().andIn("deal_id", dealVo.getAidList()).select();
                    dealDetailList = ConvertUtils.listConvert(ld7ddItems,ZBPMDealDetailVo.class);
                    dealVo.setSite(ld7dd.getSite());
                    dealVo.setDealDetailList(dealDetailList);
                    dealVo.setInternalDescription(ld7dd.getInternalDescription());
                    dealVo.setPlanStartDate(ld7dd.getPlanStartDate());
                    dealVo.setPlanEndDate(ld7dd.getPlanEndDate());
                    FeiShuUtils.sendFreeAddDealNotice(dealVo);
                }else if (StrUtil.equalsIgnoreCase("PDD", dealVo.getDealTypeName()) || StrUtil.equalsIgnoreCase("Outlet Deal", dealVo.getDealTypeName())|| StrUtil.equalsIgnoreCase("Price Discount", dealVo.getDealTypeName())) {
                    List<SomPddAndOd> pddAndOdList = pddAndOdMapper.createLambdaQuery().andIn("aid", dealVo.getAidList()).select();
                    for (SomPddAndOd pddAndOd : pddAndOdList) {
                        ZBPMDealDetailVo detailVo = new ZBPMDealDetailVo();
                        detailVo.setSite(pddAndOd.getSite());
                        detailVo.setSellerSku(pddAndOd.getSellerSku());
                        detailVo.setPlanStartDate(pddAndOd.getPlanStartDate());
                        detailVo.setPlanEndDate(pddAndOd.getPlanEndDate());
                        dealDetailList.add(detailVo);
                    }
                    dealVo.setDealDetailList(dealDetailList);
                    FeiShuUtils.sendPddAndOdNotice(dealVo);
                }else {
                    List<SomDealOfTheDay> dealOfTheDayList = dealOfTheDayMapper.createLambdaQuery().andIn("aid", dealVo.getAidList()).select();
                    for (SomDealOfTheDay dealOfTheDay : dealOfTheDayList) {
                        ZBPMDealDetailVo detailVo = new ZBPMDealDetailVo();
                        detailVo.setSite(dealOfTheDay.getSite());
                        detailVo.setSellerSku(dealOfTheDay.getSellerSku());
                        detailVo.setPlanStartDate(dealOfTheDay.getPlanStartDate());
                        detailVo.setPlanEndDate(dealOfTheDay.getPlanEndDate());
                        dealDetailList.add(detailVo);
                    }
                    dealVo.setDealDetailList(dealDetailList);
                    FeiShuUtils.sendDealOfTheDayNotice(dealVo);
                }
            } catch (Exception e) {
                log.error("Deal活动审批未通过,发送飞书提醒消息异常{}",e.getMessage());
            }
        }
        log.info("Deal活动更新结束");
    }
}