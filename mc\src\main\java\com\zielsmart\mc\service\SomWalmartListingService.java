package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomWalmartListingMapper;
import com.zielsmart.mc.vo.SomWalmartListingExtVo;
import com.zielsmart.mc.vo.SomWalmartListingPageSearchVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomWalmartListingService {

    @Resource
    private SomWalmartListingMapper somWalmartListingMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomWalmartListingExtVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWalmartListingExtVo> queryByPage(SomWalmartListingPageSearchVo pageSearchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(pageSearchVo.getCurrent(), pageSearchVo.getPageSize());
        PageResult<SomWalmartListingExtVo> pageResult = somWalmartListingMapper.queryByPage(pageSearchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<McDictionaryInfo> dictionaryList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "WalmartMarket").select();
            for (SomWalmartListingExtVo vo : pageResult.getList()) {
                dictionaryList.stream().filter(f -> StrUtil.equals(vo.getMart(), f.getItemValue())).findFirst().ifPresent(fp -> {
                    vo.setSite(fp.getItemLable());
                });
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomWalmartListingExtVo.class, pageSearchVo);
    }
}
