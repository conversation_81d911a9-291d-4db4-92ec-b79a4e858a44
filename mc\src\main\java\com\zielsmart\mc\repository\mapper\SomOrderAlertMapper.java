package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomOrderAlertPageSearchVo;
import com.zielsmart.mc.vo.SomOrderAlertVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-02-15
*/

@SqlResource("somOrderAlert")
public interface SomOrderAlertMapper extends BaseMapper<SomOrderAlert> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOrderAlertVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomOrderAlertVo> queryByPage(@Param("searchVo")SomOrderAlertPageSearchVo searchVo, PageRequest pageRequest);
}
