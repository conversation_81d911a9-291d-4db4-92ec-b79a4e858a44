package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomKauflandListing;
import com.zielsmart.mc.vo.SomKauflandListingExtVo;
import com.zielsmart.mc.vo.SomKauflandListingPageSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2023-01-11
 */

@SqlResource("somKauflandListing")
public interface SomKauflandListingMapper extends BaseMapper<SomKauflandListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomKauflandListingExtVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomKauflandListingExtVo> queryByPage(@Param("searchVo") SomKauflandListingPageSearchVo searchVo, PageRequest pageRequest);
}
