package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomListingPrice;
import com.zielsmart.mc.vo.SomListingPriceExtVo;
import com.zielsmart.mc.vo.SomListingPricePageSearchVo;
import com.zielsmart.mc.vo.SomListingPriceVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-05-25
 */

@SqlResource("somListingPrice")
public interface SomListingPriceMapper extends BaseMapper<SomListingPrice> {

    /**
     * updateBatch
     * 批量更新
     *
     * @param updateList
     * <AUTHOR>
     * @history
     */
    default void updateBatch(@Param("updateList") List<SomListingPrice> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somListingPrice.updateBatch"), updateList);
    }

    /**
     * updateSuccStatus
     * 批量更新审核通过数据
     *
     * @param updateVoList
     * <AUTHOR>
     * @history
     */
    default void updateSuccStatus(@Param("updateList") List<SomListingPriceExtVo> updateVoList) {
        this.getSQLManager().updateBatch(SqlId.of("somListingPrice.updateSuccStatus"), updateVoList);
    }

    /**
     * queryExportData
     * 导出
     *
     * @param exportVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomListingPriceVo>}
     * <AUTHOR>
     * @history
     */
    List<SomListingPriceVo> queryExportData(@Param("exportVo")SomListingPricePageSearchVo exportVo);
}