package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomCustomSaleableWarehouseConfigService;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomCustomSaleableWarehouseConfigController
 * @description
 * @date 2025-06-10 09:47:32
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somCustomSaleableWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon展示码指定仓库库区配置表管理")
public class SomCustomSaleableWarehouseConfigController extends BasicController{

    @Resource
    SomCustomSaleableWarehouseConfigService somCustomSaleableWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomCustomSaleableWarehouseConfigVo>>}
     * <AUTHOR>
     * * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomCustomSaleableWarehouseConfigVo>> queryByPage(@RequestBody SomCustomSaleableWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somCustomSaleableWarehouseConfigService.queryByPage(searchVo));
    }

    @Operation(summary = "批量删除")
    @PostMapping(value = "/batchDelete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchDelete(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点","展示码"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomCustomSaleableWarehouseConfigVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomCustomSaleableWarehouseConfigVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somCustomSaleableWarehouseConfigService.batchDelete(result.getList());
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomCustomSaleableWarehouseConfigPageSearchVo searchVo){
        String data = somCustomSaleableWarehouseConfigService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/SomCustomSaleableWarehouseConfigTemplate.xlsx";
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台","站点", "展示码","可售仓库&库区"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomCustomSaleableWarehouseConfigVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomCustomSaleableWarehouseConfigVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somCustomSaleableWarehouseConfigService.importExcel(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    @Operation(summary = "下载删除模板")
    @GetMapping(value="/downloadDelete")
    public String downloadDelete(){
        return "forward:/static/excel/SomCustomSaleableWarehouseConfigDeleteTemplate.xlsx";
    }
}
