package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2024-12-25
*/

@Table(name="mc.som_nocnoc_listing")
public class SomNocnocListing implements java.io.Serializable {
	/**
	 * 主键id
	 */
	@AssignID
	private String aid ;
	/**
	 * sku
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 创建时间
	 */
	@Column("created_at")
	private Date createdAt ;
	/**
	 * 更新时间
	 */
	@Column("updated_at")
	private Date updatedAt ;
	/**
	 * 国际ID
	 */
	@Column("international_ids")
	private Object internationalIds ;
	/**
	 * 品牌
	 */
	@Column("brand")
	private String brand ;
	/**
	 * 可用数量
	 */
	@Column("available_quantity")
	private Integer availableQuantity ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 货币代码
	 */
	@Column("currency_code")
	private String currencyCode ;
	/**
	 * 状态
	 */
	@Column("status")
	private String status ;
	/**
	 * 包装尺寸
	 */
	@Column("package_dimensions")
	private Object packageDimensions ;
	/**
	 * 产品尺寸
	 */
	@Column("product_dimensions")
	private Object productDimensions ;
	/**
	 * 图片集
	 */
	@Column("images")
	private Object images ;
	/**
	 * 标题
	 */
	@Column("title")
	private String title ;
	/**
	 * 描述语言
	 */
	@Column("description_language")
	private String descriptionLanguage ;
	/**
	 * 描述
	 */
	@Column("description")
	private String description ;
	/**
	 * 描述html
	 */
	@Column("description_html")
	private String descriptionHtml ;
	/**
	 * 属性
	 */
	@Column("attributes")
	private Object attributes ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomNocnocListing() {
	}

	/**
	* 主键id
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键id
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* sku
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* sku
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreatedAt(){
		return  createdAt;
	}
	/**
	* 创建时间
	*@param  createdAt
	*/
	public void setCreatedAt(Date createdAt ){
		this.createdAt = createdAt;
	}
	/**
	* 更新时间
	*@return
	*/
	public Date getUpdatedAt(){
		return  updatedAt;
	}
	/**
	* 更新时间
	*@param  updatedAt
	*/
	public void setUpdatedAt(Date updatedAt ){
		this.updatedAt = updatedAt;
	}
	/**
	* 国际ID
	*@return
	*/
	public Object getInternationalIds(){
		return  internationalIds;
	}
	/**
	* 国际ID
	*@param  internationalIds
	*/
	public void setInternationalIds(Object internationalIds ){
		this.internationalIds = internationalIds;
	}
	/**
	* 品牌
	*@return
	*/
	public String getBrand(){
		return  brand;
	}
	/**
	* 品牌
	*@param  brand
	*/
	public void setBrand(String brand ){
		this.brand = brand;
	}
	/**
	* 可用数量
	*@return
	*/
	public Integer getAvailableQuantity(){
		return  availableQuantity;
	}
	/**
	* 可用数量
	*@param  availableQuantity
	*/
	public void setAvailableQuantity(Integer availableQuantity ){
		this.availableQuantity = availableQuantity;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 货币代码
	*@return
	*/
	public String getCurrencyCode(){
		return  currencyCode;
	}
	/**
	* 货币代码
	*@param  currencyCode
	*/
	public void setCurrencyCode(String currencyCode ){
		this.currencyCode = currencyCode;
	}
	/**
	* 状态
	*@return
	*/
	public String getStatus(){
		return  status;
	}
	/**
	* 状态
	*@param  status
	*/
	public void setStatus(String status ){
		this.status = status;
	}
	/**
	* 包装尺寸
	*@return
	*/
	public Object getPackageDimensions(){
		return  packageDimensions;
	}
	/**
	* 包装尺寸
	*@param  packageDimensions
	*/
	public void setPackageDimensions(Object packageDimensions ){
		this.packageDimensions = packageDimensions;
	}
	/**
	* 产品尺寸
	*@return
	*/
	public Object getProductDimensions(){
		return  productDimensions;
	}
	/**
	* 产品尺寸
	*@param  productDimensions
	*/
	public void setProductDimensions(Object productDimensions ){
		this.productDimensions = productDimensions;
	}
	/**
	* 图片集
	*@return
	*/
	public Object getImages(){
		return  images;
	}
	/**
	* 图片集
	*@param  images
	*/
	public void setImages(Object images ){
		this.images = images;
	}
	/**
	* 标题
	*@return
	*/
	public String getTitle(){
		return  title;
	}
	/**
	* 标题
	*@param  title
	*/
	public void setTitle(String title ){
		this.title = title;
	}
	/**
	* 描述语言
	*@return
	*/
	public String getDescriptionLanguage(){
		return  descriptionLanguage;
	}
	/**
	* 描述语言
	*@param  descriptionLanguage
	*/
	public void setDescriptionLanguage(String descriptionLanguage ){
		this.descriptionLanguage = descriptionLanguage;
	}
	/**
	* 描述
	*@return
	*/
	public String getDescription(){
		return  description;
	}
	/**
	* 描述
	*@param  description
	*/
	public void setDescription(String description ){
		this.description = description;
	}
	/**
	* 描述html
	*@return
	*/
	public String getDescriptionHtml(){
		return  descriptionHtml;
	}
	/**
	* 描述html
	*@param  descriptionHtml
	*/
	public void setDescriptionHtml(String descriptionHtml ){
		this.descriptionHtml = descriptionHtml;
	}
	/**
	* 属性
	*@return
	*/
	public Object getAttributes(){
		return  attributes;
	}
	/**
	* 属性
	*@param  attributes
	*/
	public void setAttributes(Object attributes ){
		this.attributes = attributes;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
