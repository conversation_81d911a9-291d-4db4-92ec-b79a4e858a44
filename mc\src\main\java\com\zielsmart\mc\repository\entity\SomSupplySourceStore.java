package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
/*
* 提货仓表
* gen by 代码生成器 2024-07-23
*/

@Table(name="mc.som_supply_source_store")
public class SomSupplySourceStore implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 提货仓标识符
	 */
	@Column("supply_source_code")
	private String supplySourceCode ;
	/**
	 * 提货仓名称 Store Name - City Name
	 */
	@Column("alias")
	private String alias ;
	/**
	 * Amazon返回给我们的提货仓唯一ID
	 */
	@Column("supply_source_id")
	private String supplySourceId ;
	/**
	 * 地址名称 Store Name - City Name
	 */
	@Column("address_name")
	private String addressName ;
	/**
	 * 地址1
	 */
	@Column("address_line1")
	private String addressLine1 ;
	/**
	 * 地址2
	 */
	@Column("address_line2")
	private String addressLine2 ;
	/**
	 * 地址3
	 */
	@Column("address_line3")
	private String addressLine3 ;
	/**
	 * 地址所在城市
	 */
	@Column("address_city")
	private String addressCity ;
	/**
	 * 地址所在县村
	 */
	@Column("address_country")
	private String addressCountry ;
	/**
	 * 地址所在区
	 */
	@Column("address_district")
	private String addressDistrict ;
	/**
	 * 地址所在的州省
	 */
	@Column("address_state_or_region")
	private String addressStateOrRegion ;
	/**
	 * 地址的邮编
	 */
	@Column("address_postal_code")
	private String addressPostalCode ;
	/**
	 * 地址所在的国家编码
	 */
	@Column("address_country_code")
	private String addressCountryCode ;
	/**
	 * 地址所在的电话
	 */
	@Column("address_phone")
	private String addressPhone ;
	/**
	 * 10.Active 20.Inactive 99.归档
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 10.未推送  20.推送成功  99.推送失败
	 */
	@Column("push_status")
	private Integer pushStatus ;
	/**
	 * 公司自发仓库的编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 公司自发仓库的名称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 公司自发仓库对应的库区编码
	 */
	@Column("sl_code")
	private String slCode ;
	/**
	 * 司自发仓库对应的库区名称
	 */
	@Column("sl_name")
	private String slName ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 推送人工号
	 */
	@Column("push_num")
	private String pushNum ;
	/**
	 * 推送人姓名
	 */
	@Column("push_name")
	private String pushName ;
	/**
	 * 推送时间
	 */
	@Column("push_time")
	private Date pushTime ;
	/**
	 * 自配送仓库编码
	 */
	@Column("mfn_warehouse_code")
	private String mfnWarehouseCode ;
	/**
	 * 自配送仓库名称
	 */
	@Column("mfn_warehouse_name")
	private String mfnWarehouseName ;
	/**
	 * 自配送库区编码
	 */
	@Column("mfn_sl_code")
	private String mfnSlCode ;
	/**
	 * 自配送库区名称
	 */
	@Column("mfn_sl_name")
	private String mfnSlName ;
	/**
	 * MLI运费模板名称
	 */
	@Column("mli_template_name")
	private String mliTemplateName ;
	/**
	 * 是否为Spreetail的虚拟仓库 0.否 1.是
	 */
	@Column("spreetail_flag")
	private Integer spreetailFlag ;
	/**
	 * Spreetail仓库名称
	 */
	@Column("spreetail_warehouse_name")
	private String spreetailWarehouseName ;
	/**
	 * 仓库_库区(自发)json数据
	 */
	@Column("mfn_warehouse_sl")
	private String mfnWarehouseSl ;

	public SomSupplySourceStore() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 提货仓标识符
	*@return
	*/
	public String getSupplySourceCode(){
		return  supplySourceCode;
	}
	/**
	* 提货仓标识符
	*@param  supplySourceCode
	*/
	public void setSupplySourceCode(String supplySourceCode ){
		this.supplySourceCode = supplySourceCode;
	}
	/**
	* 提货仓名称 Store Name - City Name
	*@return
	*/
	public String getAlias(){
		return  alias;
	}
	/**
	* 提货仓名称 Store Name - City Name
	*@param  alias
	*/
	public void setAlias(String alias ){
		this.alias = alias;
	}
	/**
	* Amazon返回给我们的提货仓唯一ID
	*@return
	*/
	public String getSupplySourceId(){
		return  supplySourceId;
	}
	/**
	* Amazon返回给我们的提货仓唯一ID
	*@param  supplySourceId
	*/
	public void setSupplySourceId(String supplySourceId ){
		this.supplySourceId = supplySourceId;
	}
	/**
	* 地址名称 Store Name - City Name
	*@return
	*/
	public String getAddressName(){
		return  addressName;
	}
	/**
	* 地址名称 Store Name - City Name
	*@param  addressName
	*/
	public void setAddressName(String addressName ){
		this.addressName = addressName;
	}
	/**
	* 地址1
	*@return
	*/
	public String getAddressLine1(){
		return  addressLine1;
	}
	/**
	* 地址1
	*@param  addressLine1
	*/
	public void setAddressLine1(String addressLine1 ){
		this.addressLine1 = addressLine1;
	}
	/**
	* 地址2
	*@return
	*/
	public String getAddressLine2(){
		return  addressLine2;
	}
	/**
	* 地址2
	*@param  addressLine2
	*/
	public void setAddressLine2(String addressLine2 ){
		this.addressLine2 = addressLine2;
	}
	/**
	* 地址3
	*@return
	*/
	public String getAddressLine3(){
		return  addressLine3;
	}
	/**
	* 地址3
	*@param  addressLine3
	*/
	public void setAddressLine3(String addressLine3 ){
		this.addressLine3 = addressLine3;
	}
	/**
	* 地址所在城市
	*@return
	*/
	public String getAddressCity(){
		return  addressCity;
	}
	/**
	* 地址所在城市
	*@param  addressCity
	*/
	public void setAddressCity(String addressCity ){
		this.addressCity = addressCity;
	}
	/**
	* 地址所在县村
	*@return
	*/
	public String getAddressCountry(){
		return  addressCountry;
	}
	/**
	* 地址所在县村
	*@param  addressCountry
	*/
	public void setAddressCountry(String addressCountry ){
		this.addressCountry = addressCountry;
	}
	/**
	* 地址所在区
	*@return
	*/
	public String getAddressDistrict(){
		return  addressDistrict;
	}
	/**
	* 地址所在区
	*@param  addressDistrict
	*/
	public void setAddressDistrict(String addressDistrict ){
		this.addressDistrict = addressDistrict;
	}
	/**
	* 地址所在的州省
	*@return
	*/
	public String getAddressStateOrRegion(){
		return  addressStateOrRegion;
	}
	/**
	* 地址所在的州省
	*@param  addressStateOrRegion
	*/
	public void setAddressStateOrRegion(String addressStateOrRegion ){
		this.addressStateOrRegion = addressStateOrRegion;
	}
	/**
	* 地址的邮编
	*@return
	*/
	public String getAddressPostalCode(){
		return  addressPostalCode;
	}
	/**
	* 地址的邮编
	*@param  addressPostalCode
	*/
	public void setAddressPostalCode(String addressPostalCode ){
		this.addressPostalCode = addressPostalCode;
	}
	/**
	* 地址所在的国家编码
	*@return
	*/
	public String getAddressCountryCode(){
		return  addressCountryCode;
	}
	/**
	* 地址所在的国家编码
	*@param  addressCountryCode
	*/
	public void setAddressCountryCode(String addressCountryCode ){
		this.addressCountryCode = addressCountryCode;
	}
	/**
	* 地址所在的电话
	*@return
	*/
	public String getAddressPhone(){
		return  addressPhone;
	}
	/**
	* 地址所在的电话
	*@param  addressPhone
	*/
	public void setAddressPhone(String addressPhone ){
		this.addressPhone = addressPhone;
	}
	/**
	* 10.Active 20.Inactive 99.归档
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 10.Active 20.Inactive 99.归档
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 10.未推送  20.推送成功  99.推送失败
	*@return
	*/
	public Integer getPushStatus(){
		return  pushStatus;
	}
	/**
	* 10.未推送  20.推送成功  99.推送失败
	*@param  pushStatus
	*/
	public void setPushStatus(Integer pushStatus ){
		this.pushStatus = pushStatus;
	}
	/**
	* 公司自发仓库的编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 公司自发仓库的编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 公司自发仓库的名称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 公司自发仓库的名称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 公司自发仓库对应的库区编码
	*@return
	*/
	public String getslCode(){
		return  slCode;
	}
	/**
	* 公司自发仓库对应的库区编码
	*@param  slCode
	*/
	public void setslCode(String slCode ){
		this.slCode = slCode;
	}
	/**
	* 司自发仓库对应的库区名称
	*@return
	*/
	public String getslName(){
		return  slName;
	}
	/**
	* 司自发仓库对应的库区名称
	*@param  slName
	*/
	public void setslName(String slName ){
		this.slName = slName;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 推送人工号
	*@return
	*/
	public String getPushNum(){
		return  pushNum;
	}
	/**
	* 推送人工号
	*@param  pushNum
	*/
	public void setPushNum(String pushNum ){
		this.pushNum = pushNum;
	}
	/**
	* 推送人姓名
	*@return
	*/
	public String getPushName(){
		return  pushName;
	}
	/**
	* 推送人姓名
	*@param  pushName
	*/
	public void setPushName(String pushName ){
		this.pushName = pushName;
	}
	/**
	* 推送时间
	*@return
	*/
	public Date getPushTime(){
		return  pushTime;
	}
	/**
	* 推送时间
	*@param  pushTime
	*/
	public void setPushTime(Date pushTime ){
		this.pushTime = pushTime;
	}
	/**
	* 自配送仓库编码
	*@return
	*/
	public String getMfnWarehouseCode(){
		return  mfnWarehouseCode;
	}
	/**
	* 自配送仓库编码
	*@param  mfnWarehouseCode
	*/
	public void setMfnWarehouseCode(String mfnWarehouseCode ){
		this.mfnWarehouseCode = mfnWarehouseCode;
	}
	/**
	* 自配送仓库名称
	*@return
	*/
	public String getMfnWarehouseName(){
		return  mfnWarehouseName;
	}
	/**
	* 自配送仓库名称
	*@param  mfnWarehouseName
	*/
	public void setMfnWarehouseName(String mfnWarehouseName ){
		this.mfnWarehouseName = mfnWarehouseName;
	}
	/**
	* 自配送库区编码
	*@return
	*/
	public String getMfnSlCode(){
		return  mfnSlCode;
	}
	/**
	* 自配送库区编码
	*@param  mfnSlCode
	*/
	public void setMfnSlCode(String mfnSlCode ){
		this.mfnSlCode = mfnSlCode;
	}
	/**
	* 自配送库区名称
	*@return
	*/
	public String getMfnSlName(){
		return  mfnSlName;
	}
	/**
	* 自配送库区名称
	*@param  mfnSlName
	*/
	public void setMfnSlName(String mfnSlName ){
		this.mfnSlName = mfnSlName;
	}
	/**
	* MLI运费模板名称
	*@return
	*/
	public String getMliTemplateName(){
		return  mliTemplateName;
	}
	/**
	* MLI运费模板名称
	*@param  mliTemplateName
	*/
	public void setMliTemplateName(String mliTemplateName ){
		this.mliTemplateName = mliTemplateName;
	}
	/**
	* 是否为Spreetail的虚拟仓库 0.否 1.是
	*@return
	*/
	public Integer getSpreetailFlag(){
		return  spreetailFlag;
	}
	/**
	* 是否为Spreetail的虚拟仓库 0.否 1.是
	*@param  spreetailFlag
	*/
	public void setSpreetailFlag(Integer spreetailFlag ){
		this.spreetailFlag = spreetailFlag;
	}
	/**
	* Spreetail仓库名称
	*@return
	*/
	public String getSpreetailWarehouseName(){
		return  spreetailWarehouseName;
	}
	/**
	* Spreetail仓库名称
	*@param  spreetailWarehouseName
	*/
	public void setSpreetailWarehouseName(String spreetailWarehouseName ){
		this.spreetailWarehouseName = spreetailWarehouseName;
	}
	/**
	* 仓库_库区(自发)json数据
	*@return
	*/
	public String getMfnWarehouseSl(){
		return  mfnWarehouseSl;
	}
	/**
	* 仓库_库区(自发)json数据
	*@param  mfnWarehouseSl
	*/
	public void setMfnWarehouseSl(String mfnWarehouseSl ){
		this.mfnWarehouseSl = mfnWarehouseSl;
	}

}
