package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.mapper.SomWayfairCostViewMapper;
import com.zielsmart.mc.service.SomWayfairCostViewService;
import com.zielsmart.mc.vo.SomWayfairCostViewPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairCostViewVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomWayfairCostViewController
 * @description
 * @date 2024-02-28 17:16:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somWayfairCostView", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Wayfair平台费用表管理")
public class SomWayfairCostViewController extends BasicController {

    @Resource
    SomWayfairCostViewService somWayfairCostViewService;

    @Resource
    private SomWayfairCostViewMapper somWayfairCostViewMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo<PageVo<SomWayfairCostViewVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomWayfairCostViewVo>> queryByPage(@RequestBody SomWayfairCostViewPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somWayfairCostViewService.queryByPage(searchVo));
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody SomWayfairCostViewPageSearchVo searchVo) {
        String data = somWayfairCostViewService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

}
