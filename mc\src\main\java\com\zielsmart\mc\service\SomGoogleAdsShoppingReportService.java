package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomGoogleAdsShoppingReport;
import com.zielsmart.mc.repository.mapper.SomGoogleAdsShoppingReportMapper;
import com.zielsmart.mc.vo.SomGoogleAdsShoppingReportPageSearchVo;
import com.zielsmart.mc.vo.SomGoogleAdsShoppingReportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.math.BigDecimal;
import java.util.Base64;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomGoogleAdsShoppingReportService
 * @description
 * @date 2024-02-26 12:12:33
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomGoogleAdsShoppingReportService {

    @Resource
    private SomGoogleAdsShoppingReportMapper somGoogleAdsShoppingReportMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomGoogleAdsShoppingReportVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomGoogleAdsShoppingReportVo> queryByPage(SomGoogleAdsShoppingReportPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomGoogleAdsShoppingReportVo> pageResult = dynamicSqlManager.getMapper(SomGoogleAdsShoppingReportMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            for (SomGoogleAdsShoppingReportVo vo : pageResult.getList()) {
                if (ObjectUtil.isNotNull(vo.getCost())) {
                    Long cost = vo.getCost();
                    BigDecimal result = BigDecimal.valueOf(cost).divide(BigDecimal.valueOf(1000000), 2, BigDecimal.ROUND_HALF_UP);
                    vo.setCostRound(result);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomGoogleAdsShoppingReportVo.class, searchVo);
    }

    /**
     * 导出
     *
     * @param searchVo
     * @return {@link String}
     * <AUTHOR>
     * @history
     */
    public String export(SomGoogleAdsShoppingReportPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomGoogleAdsShoppingReportVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "GoogleAds购物广告报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomGoogleAdsShoppingReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
