package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTemuListing;
import com.zielsmart.mc.vo.SomTemuImageUrlReport;
import com.zielsmart.mc.vo.SomTemuListingPageSearchVo;
import com.zielsmart.mc.vo.SomTemuListingReport;
import com.zielsmart.mc.vo.SomTemuListingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2024-04-03
 */

@SqlResource("somTemuListing")
public interface SomTemuListingMapper extends BaseMapper<SomTemuListing> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuListingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTemuListingVo> queryByPage(@Param("searchVo") SomTemuListingPageSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomTemuListingReport> stockReport(@Param("searchVo")SomTemuListingPageSearchVo searchVo, PageRequest pageRequest);

    List<SomTemuImageUrlReport> getTemuImageUrlData(@Param("searchVo")SomTemuListingPageSearchVo searchVo);
}
