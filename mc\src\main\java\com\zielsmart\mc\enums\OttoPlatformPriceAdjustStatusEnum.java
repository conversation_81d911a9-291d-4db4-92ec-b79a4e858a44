package com.zielsmart.mc.enums;

import lombok.AllArgsConstructor;

/**
 * 调价状态枚举类
 */
@AllArgsConstructor
public enum OttoPlatformPriceAdjustStatusEnum {
    DRAFT(1, "草稿"),
    ONGOING(4, "调价中"),
    SUCCESS(20, "调价成功"),
    FAILURE(99, "调价失败"),
    ;

    private final Integer status;
    private final String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 核验是否允许编辑：调价中不可以编辑
     *
     * @param status 当前状态
     * @return boolean
     */
    public static boolean isNotAllowEdit(Integer status) {
        return ONGOING.getStatus().equals(status);
    }
}
