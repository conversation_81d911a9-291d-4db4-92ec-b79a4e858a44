package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.logging.log4j.util.Strings;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomTemuWarehouseConfigService {
    
    @Resource
    private SomTemuWarehouseConfigMapper somTemuWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private McDictionaryInfoMapper mcDictionaryInfoMapper;
    @Resource
    private SomTemuWarehouseMapper somTemuWarehouseMapper;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTemuWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTemuWarehouseConfigVo> queryByPage(SomTemuWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTemuWarehouseConfigVo> pageResult = somTemuWarehouseConfigMapper.queryByPage(searchVo, pageRequest);
        if(CollectionUtil.isNotEmpty(pageResult.getList())){
            List<String> sites = pageResult.getList().stream().map(m -> m.getSite()).distinct().collect(Collectors.toList());
            List<SomTemuWarehouseConfig> mcWarehouseConfigVos = somTemuWarehouseConfigMapper.createLambdaQuery().andIn("site",sites).select();

            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(e -> e.getWarehouseCode(), y -> y.getWarehouseName(), (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(e -> e.getslCode(), y -> y.getslName(), (x1, x2) -> x1));
            // 店铺
            List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
            Map<String, String> accountMap = needShowAccounts.stream().collect(Collectors.toMap(McDictionaryInfo::getItemValue3, McDictionaryInfo::getItemValue2, (x1, x2) -> x1));
            // 转换结果key值
            Map<String, List<SomTemuWarehouseConfig>> allMap = mcWarehouseConfigVos.stream().collect(Collectors.groupingBy(e -> e.getSite() + '_' + e.getAccountId(), Collectors.toList()));

            List<SomTemuWarehouse> temuWarehouseList = somTemuWarehouseMapper.all();
            Map<String, String> temuWarehouseMap = temuWarehouseList.stream().collect(Collectors.toMap(x -> x.getAccountId() + x.getWarehouseId(), v -> v.getWarehouseName(), (x1, x2) -> x1));

            pageResult.getList().stream().forEach(config->{

                config.setTemuWarehouseName(temuWarehouseMap.get(config.getAccountId() + config.getTemuWarehouseId()));

                if (allMap.containsKey(config.getSite() + '_' + config.getAccountId())) {
                    List<SomTemuWarehouseConfig> warehouseConfigs = allMap.get(config.getSite() + '_' + config.getAccountId());
                    List<String> nameList = new ArrayList<>();
                    config.setList(warehouseConfigs.stream().map(x -> {
                        SomTemuWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTemuWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                        useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                        nameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    config.setWarehouseNameList(nameList);
                    SomTemuWarehouseConfig config1 = warehouseConfigs.get(0);
                    config.setCreateName(config1.getCreateName());
                    config.setCreateNum(config1.getCreateNum());
                    config.setCreateTime(config1.getCreateTime());
                    config.setAccountName(accountMap.get(config.getAccountId()));
                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomTemuWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param temuVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTemuWarehouseConfigVo temuVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(temuVo) || temuVo.getList()==null || temuVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String site = temuVo.getSite();
        if (StrUtil.isBlank(site)) {
            throw new ValidateException("站点不能为空");
        }
        // 核验店铺是否已经被注销
        List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
        List<String> accountIds = needShowAccounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        if (!accountIds.contains(temuVo.getAccountId())) {
            throw new ValidateException("店铺已删除或已被注销！");
        }
        List<SomTemuWarehouseConfigVo.UseableWarehouse> list = temuVo.getList();
        List<SomTemuWarehouseConfig> dbList = somTemuWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", temuVo.getSite())
                .andEq("account_id", temuVo.getAccountId())
                .select();
        Map<String, SomTemuWarehouseConfig> dbMap = dbList.stream().collect(Collectors.toMap(x -> x.getUseableWarehouseCode() + x.getUseableStorageCode(), Function.identity(), (x1, x2) -> x1));
        List<SomTemuWarehouseConfig> insertList = new ArrayList<>();
        for (SomTemuWarehouseConfigVo.UseableWarehouse obj : list) {
            if(dbMap.containsKey(obj.getUseableWarehouseCode()+obj.getUseableStorageCode())){
                throw new ValidateException("已存在相同可售仓库&可售库区，请检查数据");
            }
            SomTemuWarehouseConfig config = new SomTemuWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setTemuWarehouseId(temuVo.getTemuWarehouseId());
            config.setSite(temuVo.getSite());
            config.setAccountId(temuVo.getAccountId());
            config.setUseableWarehouseCode(obj.getUseableWarehouseCode());
            config.setUseableStorageCode(obj.getUseableStorageCode());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateTime(DateTime.now().toJdkDate());
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyNum(tokenUser.getJobNumber());
            config.setLastModifyTime(DateTime.now().toJdkDate());
            insertList.add(config);
        }
        somTemuWarehouseConfigMapper.insertBatch(insertList);
    }

    /**
     * update
     * 修改
     * @param temuVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomTemuWarehouseConfigVo temuVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(temuVo) || CollectionUtil.isEmpty(temuVo.getList())) {
            throw new ValidateException("请选择可售仓库&可售库区");
        }
        // 核验店铺是否已经被注销
        List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
        List<String> accountIds = needShowAccounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        if (!accountIds.contains(temuVo.getAccountId())) {
            throw new ValidateException("店铺已删除或已被注销！");
        }
        somTemuWarehouseConfigMapper.createLambdaQuery().andEq("site", temuVo.getSite()).andEq("account_id", temuVo.getAccountId()).delete();
        List<SomTemuWarehouseConfig> insertList = new ArrayList<>();
        temuVo.getList().stream().distinct().forEach(obj->{
            SomTemuWarehouseConfig warehouse = new SomTemuWarehouseConfig();
            warehouse.setAid(IdUtil.fastSimpleUUID());
            warehouse.setSite(temuVo.getSite());
            warehouse.setTemuWarehouseId(temuVo.getTemuWarehouseId());
            warehouse.setAccountId(temuVo.getAccountId());
            warehouse.setUseableWarehouseCode(obj.getUseableWarehouseCode());
            warehouse.setUseableStorageCode(obj.getUseableStorageCode());
            warehouse.setCreateName(tokenUser.getUserName());
            warehouse.setCreateNum(tokenUser.getJobNumber());
            warehouse.setCreateTime(DateTime.now().toJdkDate());
            warehouse.setLastModifyName(tokenUser.getUserName());
            warehouse.setLastModifyNum(tokenUser.getJobNumber());
            warehouse.setLastModifyTime(DateTime.now().toJdkDate());
            insertList.add(warehouse);
        });
        somTemuWarehouseConfigMapper.insertBatch(insertList);
    }

    /**
     * delete
     * 删除
     * @param somTemuWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTemuWarehouseConfigVo somTemuWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTemuWarehouseConfigVo)||somTemuWarehouseConfigVo.getSite()==null) {
            throw new ValidateException("请选择要删除的数据");
        }
        somTemuWarehouseConfigMapper.createLambdaQuery().andEq("site",somTemuWarehouseConfigVo.getSite()).andEq("account_id",somTemuWarehouseConfigVo.getAccountId()).delete();
    }

    /**
     * 查询需要忽略的店铺ID
     *
     * @return List<String>
     */
    private List<McDictionaryInfo> queryNeedShowAccounts() {
        // 获取字典里面的 TemuAccount，需要展示 item_value4 = 1
        return mcDictionaryInfoMapper.createLambdaQuery()
                .andEq("item_type_code", "TemuAccount")
                .andEq("item_value4", "1")
                .select();
    }

    /**
     * 格式化数据 店铺ID
     * @param somTemuWarehouseConfigParamVo
     * @return void
     * <AUTHOR>
     */
    public void formatData(SomTemuWarehouseConfigParamVo somTemuWarehouseConfigParamVo) throws ValidateException {
        List<SomTemuWarehouseConfig> configList = somTemuWarehouseConfigMapper.createLambdaQuery()
                .andEq("site", somTemuWarehouseConfigParamVo.getSite())
                .andIsNull("account_id")
                .select();

        Map<String, List<SomTemuWarehouseConfig>> map = new HashMap<>();

        for (SomTemuWarehouseConfig config: configList) {
            // 以站点为key值，组装二维数组
            String key = config.getSite();
            List<SomTemuWarehouseConfig> voList = map.get(key);
            if (CollUtil.isEmpty(voList)) {
                voList = new ArrayList<>();
                voList.add(config);
                map.put(key, voList);
            } else {
                voList.add(config);
            }
        }

        // 核验店铺是否已经被注销
        List<McDictionaryInfo> needShowAccounts = this.queryNeedShowAccounts();
        List<String> accountIds = needShowAccounts.stream().map(McDictionaryInfo::getItemValue3).collect(Collectors.toList());
        for (String account_id : somTemuWarehouseConfigParamVo.getAccountIds()) {
            if (!accountIds.contains(account_id)) {
                throw new ValidateException(account_id + "店铺ID不存在");
            }
        }

        // 存在相同站点的可执行数据
        List<SomTemuWarehouseConfig> voList = map.get(somTemuWarehouseConfigParamVo.getSite());
        if (!CollUtil.isEmpty(voList)) {
            List<SomTemuWarehouseConfig> insertList = new ArrayList<>();
            for (String account_id : somTemuWarehouseConfigParamVo.getAccountIds()) {
                for (SomTemuWarehouseConfig vo : voList) {
                    SomTemuWarehouseConfig config = new SomTemuWarehouseConfig();
                    config.setAid(IdUtil.fastSimpleUUID());
                    config.setSite(vo.getSite());
                    config.setTemuWarehouseId(vo.getTemuWarehouseId());
                    config.setUseableStorageCode(vo.getUseableStorageCode());
                    config.setUseableWarehouseCode(vo.getUseableWarehouseCode());
                    config.setCreateNum(vo.getCreateNum());
                    config.setCreateName(vo.getCreateName());
                    config.setCreateTime(vo.getCreateTime());
                    config.setLastModifyNum(vo.getLastModifyNum());
                    config.setLastModifyName(vo.getLastModifyName());
                    config.setLastModifyTime(vo.getLastModifyTime());
                    config.setAccountId(account_id);
                    insertList.add(config);
                }
            }
            somTemuWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * 查询仓库列表 封装数据给前端下拉框提供数据
     * @param searchVo
     * @return
     * @throws ValidateException
     */
    public List<SomTemuWarehouseVo> warehouseList(SomTemuWarehouseConfigPageSearchVo searchVo) throws ValidateException {
        if (searchVo.getAccountId() == null) {
            throw new ValidateException("账号ID不能为空");
        }
        List<SomTemuWarehouse> accountIdList = somTemuWarehouseMapper.createLambdaQuery().andEq("account_id", searchVo.getAccountId()).select();
        return accountIdList.stream().map(x->{
            SomTemuWarehouseVo warehouse = new SomTemuWarehouseVo();
            warehouse.setWarehouseId(x.getWarehouseId());
            warehouse.setWarehouseName(x.getWarehouseName());
            return warehouse;
        }).distinct().collect(Collectors.toList());
    }

}
