package com.zielsmart.mc.service.zbpm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zielsmart.mc.repository.entity.SomStandardPriceWhiteList;
import com.zielsmart.mc.repository.mapper.SomListingPriceHistoryMapper;
import com.zielsmart.mc.repository.mapper.SomListingPriceMapper;
import com.zielsmart.mc.repository.mapper.SomStandardPriceWhiteListMapper;
import com.zielsmart.mc.vo.SomListingPriceExtVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZBPMListingPriceService {
    @Resource
    private SomListingPriceMapper listingPriceMapper;
    @Resource
    private SomListingPriceHistoryMapper listingPriceHistoryMapper;
    @Resource
    private SomStandardPriceWhiteListMapper whiteListMapper;

    /**
     * updateListingPrice
     * 更新
     *
     * @param updateVoList
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateListingPrice(List<SomListingPriceExtVo> updateVoList, TokenUserInfo tokenUser) throws ValidateException {
        if (CollectionUtil.isEmpty(updateVoList)) {
            throw new ValidateException("数据存在空值,请检查");
        }
        // 审核未通过
        if (updateVoList.get(0).getStatus().equals(30)) {
            // 历史表
            listingPriceHistoryMapper.updateStatus(updateVoList);
        } else {
            // 审核通过
            // 需要添加到白名单
            List<SomListingPriceExtVo> addList = updateVoList.stream().filter(f -> ObjectUtil.equal(1, f.getAddOrDel())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(addList)) {
                List<String> countrys = addList.stream().map(m -> m.getCountry()).distinct().collect(Collectors.toList());
                List<String> skus = addList.stream().map(m -> m.getSku()).distinct().collect(Collectors.toList());
                List<Integer> fulfillmentChannels = addList.stream().map(m -> m.getFulfillmentChannel()).distinct().collect(Collectors.toList());
                List<SomStandardPriceWhiteList> whiteLists = whiteListMapper.createLambdaQuery().andIn("country", countrys).andIn("sku", skus).andIn("fulfillment_channel", fulfillmentChannels).select();
                List<SomStandardPriceWhiteList> newList = new ArrayList<>();
//                addList.forEach(f -> {
//                    SomStandardPriceWhiteList standardPriceWhiteList = whiteLists.stream().filter(t -> StrUtil.equals(f.getCountry(), t.getCountry()) && StrUtil.equals(f.getSku(), t.getSku()) && ObjectUtil.equal(f.getFulfillmentChannel(), t.getFulfillmentChannel())).findFirst().orElse(null);
//                    if (ObjectUtil.isEmpty(standardPriceWhiteList)) {
//                        SomStandardPriceWhiteList temp = new SomStandardPriceWhiteList();
//                        temp.setAid(IdUtil.fastSimpleUUID());
//                        temp.setCreateTime(DateTime.now().toJdkDate());
//                        temp.setCreateNum(tokenUser.getJobNumber());
//                        temp.setCreateName(tokenUser.getUserName());
//                        temp.setCountry(f.getCountry());
//                        temp.setSku(f.getSku());
//                        temp.setFulfillmentChannel(f.getFulfillmentChannel());
//                        newList.add(temp);
//                    }
//                });
//                whiteListMapper.insertBatch(newList);
            }
            // 需要从白名单删除
            List<SomListingPriceExtVo> delList = updateVoList.stream().filter(f -> ObjectUtil.equal(2, f.getAddOrDel())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(delList)) {
                List<String> aidList = new ArrayList<>();
                List<String> countrys = delList.stream().map(m -> m.getCountry()).distinct().collect(Collectors.toList());
                List<String> skus = delList.stream().map(m -> m.getSku()).distinct().collect(Collectors.toList());
                List<Integer> fulfillmentChannels = delList.stream().map(m -> m.getFulfillmentChannel()).distinct().collect(Collectors.toList());
                List<SomStandardPriceWhiteList> whiteLists = whiteListMapper.createLambdaQuery().andIn("country", countrys).andIn("sku", skus).andIn("fulfillment_channel", fulfillmentChannels).select();
//                delList.forEach(f -> {
//                    SomStandardPriceWhiteList standardPriceWhiteList = whiteLists.stream().filter(t -> StrUtil.equals(f.getCountry(), t.getCountry()) && StrUtil.equals(f.getSku(), t.getSku()) && ObjectUtil.equal(f.getFulfillmentChannel(), t.getFulfillmentChannel())).findFirst().orElse(null);
//                    if (ObjectUtil.isNotEmpty(standardPriceWhiteList)) {
//                        aidList.add(standardPriceWhiteList.getAid());
//                    }
//                });
//                if (CollectionUtil.isNotEmpty(aidList)) {
//                    whiteListMapper.createLambdaQuery().andIn("aid", aidList).delete();
//                }
            }
            //更新主表状态,价格
            listingPriceMapper.updateSuccStatus(updateVoList);
            // 更新历史表状态
            listingPriceHistoryMapper.updateStatus(updateVoList);
        }
    }
}
