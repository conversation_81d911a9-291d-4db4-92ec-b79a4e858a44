package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomReviewRulesConfigService;
import com.zielsmart.mc.vo.SomReviewRulesConfigPageSearchVo;
import com.zielsmart.mc.vo.SomReviewRulesConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomReviewRulesConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somReviewRulesConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "邀评规则配置管理")
public class SomReviewRulesConfigController extends BasicController {

    @Resource
    SomReviewRulesConfigService somReviewRulesConfigService;

    /**
     * save
     *
     * @param saveVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomReviewRulesConfigVo saveVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somReviewRulesConfigService.save(saveVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * query
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomReviewRulesConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomReviewRulesConfigVo>> query(@RequestBody SomReviewRulesConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somReviewRulesConfigService.query(searchVo));
    }

    /**
     * queryByID
     * 查询详情
     *
     * @param aid
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomReviewRulesConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询详情")
    @PostMapping(value = "/queryByID")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomReviewRulesConfigVo> queryByID(@RequestBody SomReviewRulesConfigPageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somReviewRulesConfigService.queryByID(searchVo));
    }

    /**
     * update
     *
     * @param updateVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomReviewRulesConfigVo updateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somReviewRulesConfigService.update(updateVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param deleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomReviewRulesConfigVo deleteVo) throws ValidateException {
        somReviewRulesConfigService.delete(deleteVo);
        return ResultVo.ofSuccess(null);
    }
}
