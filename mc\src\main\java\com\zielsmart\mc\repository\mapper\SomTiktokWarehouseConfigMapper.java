package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomTiktokWarehouseConfig;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Map;
/*
* 
* gen by 代码生成器 mapper 2024-12-13
*/

@SqlResource("somTiktokWarehouseConfig")
public interface SomTiktokWarehouseConfigMapper extends BaseMapper<SomTiktokWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTiktokWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomTiktokWarehouseConfigVo> queryByPage(@Param("searchVo")SomTiktokWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);

    List<String> getShopCipher();
    List<Map> getWarehouseByShopCipher(@Param("shopCipher")String shopCipher);
}
