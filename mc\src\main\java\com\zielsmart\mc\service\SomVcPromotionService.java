package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McPlatformProperties;
import com.zielsmart.mc.repository.entity.McProductSales;
import com.zielsmart.mc.repository.entity.SomAmazonVcPrice;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IMagicService;
import com.zielsmart.mc.util.MarketActivityUtil;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description Vc DOTO表管理
 * @date 2025-05-07 10:09:27
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomVcPromotionService {

    @Resource
    private McProductSalesMapper mcProductSalesMapper;

    @Resource
    private SomAmazonVcPriceMapper somAmazonVcPriceMapper;

    @Resource
    private McProductSalesMapper productSalesMapper;

    @Resource
    private IMagicService iMagicService;

    @Resource
    private SomVcDotdMapper vcDotdMapper;

    @Resource
    private SomAmazonVcPriceMapper amazonVcPriceMapper;

    @Resource
    private McPlatformPropertiesMapper platformPropertiesMapper;

    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Value("${magic.head.token}")
    private String token;

    /**
     * 查询展示码相关信息
     *
     * @param queryVo 查询参数
     * @return 展示码信息
     */
    public List<SomVcPromotionSellerSkuInfoVo> querySellerSkuInfo(SomVcPromotionSellerSkuQueryVo queryVo) {
        // 返回值
        List<SomVcPromotionSellerSkuInfoVo> skuInfoVos = new ArrayList<>();
        String site = queryVo.getSite();
        List<String> sellerSkus = queryVo.getSellerSkus();
        if (CollUtil.isEmpty(sellerSkus) || StrUtil.isEmpty(site)) {
            return skuInfoVos;
        }
        // 查询平台站点属性配置
        McPlatformPropertiesVo searchVo = new McPlatformPropertiesVo();
        searchVo.setPlatform("VC");
        searchVo.setSite(site);
        McPlatformPropertiesVo platformProperties = platformPropertiesMapper.getPlatformProperties(searchVo);
        // 查询销售视图
        List<McProductSales> productSalesList = mcProductSalesMapper.createLambdaQuery().andEq("platform", "VC").andEq("site", site).andIn("display_product_code", sellerSkus).andEq("is_enabled", 1).andEq("sales_flag", 1).select();
        if (CollUtil.isEmpty(productSalesList)) {
            return queryVo.getSellerSkus().stream().map(x -> {
                SomVcPromotionSellerSkuInfoVo skuInfoVo = new SomVcPromotionSellerSkuInfoVo();
                skuInfoVo.setSite(site);
                skuInfoVo.setSellerSku(x);
                skuInfoVo.setCurrency(platformProperties == null ? null : platformProperties.getCurrencyCode());
                return skuInfoVo;
            }).collect(Collectors.toList());
        }
        Map<String, McProductSales> productSalesMap = productSalesList.stream().collect(Collectors.toMap(McProductSales::getDisplayProductCode, Function.identity(), (x1, x2) -> x1));
        // 查询VC定价管理获取 RRP、前台售价
        List<String> asins = productSalesList.stream().map(McProductSales::getAsinCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        Map<String, SomAmazonVcPrice> amazonVcPriceMap = queryAmazonVcPrice(site, sellerSkus, asins);
        // 查询30天dms
        Map<String, MdmAmazonVcDmsResponseVo> dmsMap = queryAmazonVc30Dms(site, asins);
        // 查询库存可销售天数
        List<SomSellerSkuStockSaleDaysQueryVo> stockSaleDaysQueryVos = new ArrayList<>();
        for (McProductSales mcProductSales :  productSalesMap.values()) {
            SomSellerSkuStockSaleDaysQueryVo stockSaleDaysQueryVo = new SomSellerSkuStockSaleDaysQueryVo();
            stockSaleDaysQueryVo.setSite(site);
            stockSaleDaysQueryVo.setSellerSku(mcProductSales.getDisplayProductCode());
            stockSaleDaysQueryVo.setStartDate(queryVo.getStartDate());
            stockSaleDaysQueryVo.setEndDate(queryVo.getEndDate());
            stockSaleDaysQueryVos.add(stockSaleDaysQueryVo);
        }
        Map<String, Object> vcStockSaleDaysMap = queryVcStockSaleDays(stockSaleDaysQueryVos);
        // 整合数据
        for (String sellerSku : sellerSkus) {
            SomVcPromotionSellerSkuInfoVo skuInfoVo = new SomVcPromotionSellerSkuInfoVo();
            skuInfoVo.setSite(site);
            skuInfoVo.setSellerSku(sellerSku);
            skuInfoVo.setCurrency(platformProperties == null ? null : platformProperties.getCurrencyCode());
            McProductSales mcProductSales = productSalesMap.get(sellerSku);
            if (mcProductSales == null) {
                skuInfoVos.add(skuInfoVo);
                continue;
            }
            skuInfoVo.setSku(mcProductSales.getProductMainCode());
            String asin = mcProductSales.getAsinCode();
            skuInfoVo.setAsin(asin);
            if (StrUtil.isNotEmpty(asin)) {
                // 30天dms
                if (dmsMap.containsKey(asin)) {
                    skuInfoVo.setDmsLast30day(dmsMap.get(mcProductSales.getAsinCode()).getDmsLast30day());
                }
                // vc 比价
                if (amazonVcPriceMap.containsKey(sellerSku + asin)) {
                    SomAmazonVcPrice somAmazonVcPrice = amazonVcPriceMap.get(sellerSku + asin);
                    skuInfoVo.setRecommendedRetailPrice(somAmazonVcPrice.getRecommendedRetailPrice());
                    skuInfoVo.setFrontSellPrice(somAmazonVcPrice.getPrice() == null ? BigDecimal.ZERO : somAmazonVcPrice.getPrice());
                }
            }
            // 库存可销售天数
            if (vcStockSaleDaysMap.containsKey(sellerSku)) {
                skuInfoVo.setStockSaleDays((Integer) vcStockSaleDaysMap.get(sellerSku));
            }
            skuInfoVos.add(skuInfoVo);
        }
        return skuInfoVos;
    }

    /**
     * 查询近30天DMS
     * 需要注意，如果发生异常，返回空 => 30天dms为空
     *
     * @param site     站点
     * @param asins asin 集合
     * @return key:asin value:MdmAmazonVcDmsResponseVo
     */
    public Map<String, MdmAmazonVcDmsResponseVo> queryAmazonVc30Dms(String site, List<String> asins) {
        Map<String, MdmAmazonVcDmsResponseVo> dmsMap = new HashMap<>();
        if (CollUtil.isEmpty(asins)) {
            return dmsMap;
        }
        MdmAmazonVcDmsQueryVo mdmAmazonVcDmsQueryVo = new MdmAmazonVcDmsQueryVo();
        mdmAmazonVcDmsQueryVo.setSite(site);
        mdmAmazonVcDmsQueryVo.setAsins(asins);
        // 返回结果
        List<MdmAmazonVcDmsResponseVo> responseVos = new ArrayList<>();
        try {
            ResultVo<List<MdmAmazonVcDmsResponseVo>> resultVo = iMagicService.queryAmazonVc30Dms(token, mdmAmazonVcDmsQueryVo);
            if (!resultVo.isSuccess()) {
                log.info("MdmAmazonVcDms:查询Dms返回失败：{}", JSONUtil.toJsonStr(resultVo));
                return dmsMap;
            }
            responseVos = resultVo.getData();
            responseVos = CollUtil.isEmpty(responseVos) ? Collections.emptyList() : responseVos;
        } catch (Exception ex) {
            log.error("MdmAmazonVcDms:查询Dms异常：{}", ex.getMessage());
        }
        dmsMap = responseVos.stream().collect(Collectors.toMap(MdmAmazonVcDmsResponseVo::getAsin, Function.identity(), (x1, x2) -> x1));
        return dmsMap;
    }

    /**
     * 查询VC定价管理获取 RRP、前台售价
     *
     * @param site 站点
     * @param sellerSkus 展示码
     * @param asins asins
     * @return key:sellerSku+asin value:SomAmazonVcPrice
     */
    public Map<String, SomAmazonVcPrice> queryAmazonVcPrice(String site, List<String> sellerSkus, List<String> asins) {
        Map<String, SomAmazonVcPrice> amazonVcPriceMap = new HashMap<>();
        if (CollUtil.isNotEmpty(asins)) {
            List<SomAmazonVcPrice> somAmazonVcPrices = somAmazonVcPriceMapper.createLambdaQuery().andEq("site", site).andIn("seller_sku", sellerSkus).andIn("asin", asins).select();
            amazonVcPriceMap = somAmazonVcPrices.stream().collect(Collectors.toMap(x -> x.getSellerSku() + x.getAsin(), Function.identity(), (x1, x2) -> x1));
        }
        return amazonVcPriceMap;
    }

    /**
     * 查询供应商编码
     *
     * @param queryVo 入参
     * @return 供应商编码集合
     */
    public List<String> queryVendorCode(SomVcPromotionVendorCodeQueryVo queryVo) throws ValidateException {
        String accountName = queryVo.getAccountName();
        if (StrUtil.isEmpty(accountName)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        List<String> typeCodes = Collections.singletonList("VcPromotionAccount");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream()
                .filter(x -> x.getItemValue().equals(accountName))
                .map(McDictionaryInfo::getItemValue2)
                .flatMap(str -> Arrays.stream(str.split(",")))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * 查询库存可销售天数
     *
     * @param queryVos 入参
     * @return MarketActivityUtil.MarketMsgData
     */
    public Map<String, Object> queryVcStockSaleDays(List<SomSellerSkuStockSaleDaysQueryVo> queryVos) {
        Map<String, Object> result = new HashMap<>();
        if (CollUtil.isEmpty(queryVos)) {
            return result;
        }
        // 构建参数
        List<MarketActivityUtil.MarketActivity> body = new ArrayList<>();
        for (SomSellerSkuStockSaleDaysQueryVo queryVo : queryVos) {
            MarketActivityUtil.MarketActivity activity = new MarketActivityUtil.MarketActivity();
            activity.setSite(queryVo.getSite());
            activity.setSellerSku(queryVo.getSellerSku());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            activity.setStartDate(queryVo.getStartDate() == null ? null : sdf.format(queryVo.getStartDate()));
            activity.setEndDate(queryVo.getEndDate() == null ? null : sdf.format(queryVo.getEndDate()));
            body.add(activity);
        }
        // 请求BI
        try {
            MarketActivityUtil activityUtil = new MarketActivityUtil();
            MarketActivityUtil.MarketMsgData daysMsgData = activityUtil.getVcStockSaleDaysFromBi(body, false);
            result = CollUtil.isEmpty(daysMsgData.getDaysMap()) ? result : daysMsgData.getDaysMap();
            Map<String, String> errorMap = daysMsgData.getErrorMap();
            if (CollUtil.isNotEmpty(errorMap)) {
                log.error("BI MarketActivity：查询BI失败：{}", JSONUtil.toJsonStr(errorMap));
            }
        } catch (Exception ex) {
            log.error("BI MarketActivity：查询BI异常：{}", ex.getMessage());
        }
        return result;
    }

    /**
     * 核验营销活动唯一性
     *
     * @param checkUniqueVo 入参
     * @return 是否唯一
     */
    public boolean checkUnique(SomVcPromotionCheckUniqueVo checkUniqueVo) {
        List<Integer> count = vcDotdMapper.checkUnique(checkUniqueVo);
        int sum = count.stream().mapToInt(m -> m).sum();
        return sum != 0;
    }

    /**
     * 查询导入的 seller_sku 相关信息
     * 包括 销售视图、DMS、库存可销售天数
     *
     * @param importBasicVos 导入核心字段抽取
     */
    public SomVcPromotionImportInfoVo queryImportSellerSkus(List<? extends SomVcPromotionImportBasicVo> importBasicVos) {
        String site = importBasicVos.get(0).getSite();
        List<String> sellerSkus = importBasicVos.stream().map(SomVcPromotionImportBasicVo::getSellerSku).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> asins = importBasicVos.stream().map(SomVcPromotionImportBasicVo::getAsin).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        SomVcPromotionImportInfoVo importInfoVo = new SomVcPromotionImportInfoVo();
        if (StrUtil.isEmpty(site) || (CollUtil.isEmpty(sellerSkus) && CollUtil.isEmpty(asins))) {
            return importInfoVo;
        }
        // 查询销售视图及相关信息，一切以销售视图为基准，销售视图不存在就不需要往下查询了
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setSiteList(Collections.singletonList(site));
        mcProductSalesSearchVo.setDisplayProductCodeList(sellerSkus);
        mcProductSalesSearchVo.setAsinList(asins);
        mcProductSalesSearchVo.setPlatform("VC");
        List<McProductSalesVo> productSales = productSalesMapper.getProductSales(mcProductSalesSearchVo);
        if (CollUtil.isEmpty(productSales)) {
            return importInfoVo;
        }
        // 导入[asin]和[展示码]二选一
        boolean isAsin = CollUtil.isNotEmpty(asins);
        Map<String, McProductSalesVo> productSalesMap;
        if (isAsin) {
            productSalesMap = productSales.stream().collect(Collectors.toMap(McProductSalesVo::getAsinCode, Function.identity(), (x1, x2) -> x1));
        } else {
            productSalesMap = productSales.stream().collect(Collectors.toMap(McProductSalesVo::getDisplayProductCode, Function.identity(), (x1, x2) -> x1));
        }
        for (SomVcPromotionImportBasicVo importBasicVo : importBasicVos) {
            importBasicVo.setProductSalesVo(productSalesMap.get(isAsin ? importBasicVo.getAsin() : importBasicVo.getSellerSku()));
        }
        // 查询VC定价管理获取 asin、sku、RRP、前台售价
        sellerSkus = productSales.stream().map(McProductSalesVo::getDisplayProductCode).collect(Collectors.toList());
        List<String> asinCodes = productSales.stream().map(McProductSalesVo::getAsinCode).distinct().collect(Collectors.toList());
        importInfoVo.setAmazonVcPriceMap(queryAmazonVcPrice(site, sellerSkus, asinCodes));
        // 查询30天DMS
        importInfoVo.setDmsMap(queryAmazonVc30Dms(site, asinCodes));
        // 查询平台属性配置
        McPlatformProperties platformProperties = platformPropertiesMapper.createLambdaQuery().andEq("platform", "VC").andEq("site", site).single();
        importInfoVo.setCurrency(platformProperties == null ? null : platformProperties.getCurrencyCode());
        // 查询库存可销售天数
        List<SomSellerSkuStockSaleDaysQueryVo> queryVos = new ArrayList<>();
        for (SomVcPromotionImportBasicVo importVo : importBasicVos) {
            McProductSalesVo productSalesVo = importVo.getProductSalesVo();
            if (productSalesVo == null) {
                continue;
            }
            SomSellerSkuStockSaleDaysQueryVo queryVo = new SomSellerSkuStockSaleDaysQueryVo();
            queryVo.setSite(importVo.getSite());
            queryVo.setSellerSku(productSalesVo.getDisplayProductCode());
            queryVo.setStartDate(importVo.getStartDate());
            queryVo.setEndDate(importVo.getEndDate());
            queryVos.add(queryVo);
        }
        Map<String, Object> vcStockSaleDays = queryVcStockSaleDays(queryVos);
        importInfoVo.setStockStockSaleDaysMap(vcStockSaleDays);
        return importInfoVo;
    }

    /**
     * 核验导入公有属性
     *
     * @param importVo     公有属性
     * @param importInfoVo 导入信息汇总
     * @param errors       错误信息
     */
    public boolean checkImportBasic(SomVcPromotionImportBasicVo importVo, SomVcPromotionImportInfoVo importInfoVo, List<String> errors) {
        // 核验销售视图是否存在
        McProductSalesVo productSalesVo = importVo.getProductSalesVo();
        String site = importVo.getSite();
        String sellerSku = importVo.getSellerSku();
        String asin = importVo.getAsin();
        String errorMsg = StrUtil.concat(true, "站点[", site, "]", StrUtil.isNotEmpty(sellerSku) ? "展示码[" + sellerSku + "]" : "ASIN[" + asin + "]");
        if (productSalesVo == null) {
            errors.add(StrUtil.concat(true, "错误30：", errorMsg, "该产品不存在销售视图！"));
            return false;
        }
        importVo.setAsin(productSalesVo.getAsinCode());
        importVo.setSellerSku(productSalesVo.getDisplayProductCode());
        importVo.setSku(productSalesVo.getProductMainCode());
        // 核验 VC 前台售价是否存在
        Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = importInfoVo.getAmazonVcPriceMap();
        SomAmazonVcPrice somAmazonVcPrice = somAmazonVcPriceMap.get(importVo.getSellerSku() + importVo.getAsin());
        if (somAmazonVcPrice == null) {
            errors.add(StrUtil.concat(true, "错误31：", errorMsg, "该产品没有维护RRP，请先维护RRP！"));
            return false;
        }
        importVo.setRecommendedRetailPrice(somAmazonVcPrice.getRecommendedRetailPrice());
        importVo.setFrontSellPrice(somAmazonVcPrice.getPrice() == null ? BigDecimal.ZERO : somAmazonVcPrice.getPrice());
        // 核验可销售天数
        Map<String, Object> stockStockSaleDaysMap = importInfoVo.getStockStockSaleDaysMap();
        if (!stockStockSaleDaysMap.containsKey(importVo.getSellerSku())) {
            errors.add(StrUtil.concat(true, "错误32：", errorMsg, "该产品获取可销售天数失败！"));
            return false;
        }
        importVo.setStockSaleDays((Integer) stockStockSaleDaysMap.get(importVo.getSellerSku()));
        // 30天DMS
        if (StrUtil.isNotEmpty(asin)) {
            Map<String, MdmAmazonVcDmsResponseVo> dmsMap = importInfoVo.getDmsMap();
            importVo.setDmsLast30day(dmsMap.get(asin) == null ? null : dmsMap.get(asin).getDmsLast30day());
        }
        return true;
    }

    /**
     * 构建导入的查询活动是否存在参数
     *
     * @param importBasicVo VC 营销活动导入基础核心参数
     * @return SomVcPromotionCheckUniqueVo
     */
    public SomVcPromotionCheckUniqueVo buildImportCheckUniqueVo(SomVcPromotionImportBasicVo importBasicVo) {
        SomVcPromotionCheckUniqueVo checkUniqueVo = new SomVcPromotionCheckUniqueVo();
        checkUniqueVo.setSite(importBasicVo.getSite());
        checkUniqueVo.setVendorCode(importBasicVo.getVendorCode());
        checkUniqueVo.setSellerSku(importBasicVo.getSellerSku());
        checkUniqueVo.setAsin(importBasicVo.getAsin());
        checkUniqueVo.setStartDate(importBasicVo.getStartDate());
        checkUniqueVo.setEndDate(importBasicVo.getEndDate());
        return checkUniqueVo;
    }

    /**
     * 核验活动的起止时间
     * 注： DOTD 补充大促时间，可以允许【开始时间 < 当前时间】
     *
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param isAfterNow 是否大于当前时间
     */
    public void checkPromotionDate(Date startDate, Date endDate, boolean isAfterNow) throws ValidateException {
        if (startDate == null) {
            throw new ValidateException("活动起始日期不能为空！");
        }
        if (endDate == null) {
            throw new ValidateException("活动截止日期不能为空！");
        }
        // 如果都不为空，活动起始日期不能大于截止日期
        if (startDate.after(endDate)) {
            throw new ValidateException("活动起始日期不能大于活动截止日期！");
        }
        if (isAfterNow && startDate.before(new Date())) {
            throw new ValidateException("活动起始日期不能小于当前日期！");
        }
    }

    /**
     * 获取展示码列表，会去重
     *
     * @param searchVo 入参
     * @return 展示码集合
     */
    public List<String> querySellerSkuCode(McProductSalesSearchVo searchVo) {
        return productSalesMapper.querySellerSkuCode(searchVo);
    }

    /**
     * 获取展示码列表，带分页，去重
     *
     * @param searchVo 入参
     * @return 展示码集合
     */
    public PageVo<SomVcPromotionSellerSkuInfoVo> queryByPageSellerSkuCode(SomVcPromotionSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomVcPromotionSellerSkuInfoVo> pageResult = productSalesMapper.queryByPageSellerSkuCode(searchVo, pageRequest);
        if (CollUtil.isNotEmpty(pageResult.getList())) {
            // 获取前台售价
            List<String> sellerSkuList = pageResult.getList().stream().map(SomVcPromotionSellerSkuInfoVo::getSellerSku).distinct().collect(Collectors.toList());
            List<String> asinList = pageResult.getList().stream().map(SomVcPromotionSellerSkuInfoVo::getAsin).distinct().collect(Collectors.toList());
            List<String> siteList = pageResult.getList().stream().map(SomVcPromotionSellerSkuInfoVo::getSite).distinct().collect(Collectors.toList());
            List<SomAmazonVcPrice> somAmazonVcPriceList = dynamicSqlManager.getMapper(SomAmazonVcPriceMapper.class).createLambdaQuery()
                    .andIn("site", siteList)
                    .andIn("seller_sku", sellerSkuList)
                    .andIn("asin", asinList)
                    .select();
            Map<String, SomAmazonVcPrice> somAmazonVcPriceMap = somAmazonVcPriceList.stream().collect(Collectors.toMap(
                    x -> x.getSite() + x.getSellerSku() + x.getAsin(),
                    Function.identity(),
                    (x1, x2) -> x1
            ));
            pageResult.getList().forEach(x -> {
                String key = x.getSite() + x.getSellerSku() + x.getAsin();
                SomAmazonVcPrice somAmazonVcPrice = somAmazonVcPriceMap.get(key);
                if (somAmazonVcPrice != null) {
                    x.setFrontSellPrice(somAmazonVcPrice.getPrice());
                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomVcPromotionSellerSkuInfoVo.class, searchVo);
    }

    /**
     * 核验导入金额
     *
     * @param amountStr 金额字符串
     * @return boolean
     */
    public boolean isInvalidAmount(String amountStr) {
        // 如果没传证明不需要核验
        if (StrUtil.isEmpty(amountStr)) {
            return false;
        }
        try {
            new BigDecimal(amountStr);
            return false;
        } catch (Exception ex) {
            return true;
        }
    }

    /**
     * 导入核心字段核验
     *  1. 导入必须是同一站点
     *  2. 导入ASIN和展示码列，只需要一列有值
     *
     * @param importVos 导入公共属性
     */
    public void checkImportBasic(List<? extends SomVcPromotionImportBasicVo> importVos) throws ValidateException {
        // 导入必须是同一站点
        long count = importVos.stream().map(SomVcPromotionImportBasicVo::getSite).distinct().count();
        if (count > 1) {
            throw new ValidateException("导入数据存在多个站点，请检查数据！");
        }
        // 导入ASIN和展示码列，只需要一列有值
        Set<String> asins = importVos.stream().map(SomVcPromotionImportBasicVo::getAsin).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> sellerSkus = importVos.stream().map(SomVcPromotionImportBasicVo::getSellerSku).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(asins) && CollUtil.isNotEmpty(sellerSkus)) {
            throw new ValidateException("导入数据展示码列和ASIN列只允许一列有值，请检查数据！");
        }
    }

    /**
     * 查询平台为VC的销售视图
     *
     * @param sites      站点集合
     * @param sellerSkus 展示码
     * @return 销售视图集合
     */
    public List<McProductSalesVo> queryVcProductSales(List<String> sites, List<String> sellerSkus) {
        McProductSalesSearchVo mcProductSalesSearchVo = new McProductSalesSearchVo();
        mcProductSalesSearchVo.setPlatform("VC");
        mcProductSalesSearchVo.setSiteList(sites);
        mcProductSalesSearchVo.setDisplayProductCodeList(sellerSkus);
        return productSalesMapper.getProductSales(mcProductSalesSearchVo);
    }

}
