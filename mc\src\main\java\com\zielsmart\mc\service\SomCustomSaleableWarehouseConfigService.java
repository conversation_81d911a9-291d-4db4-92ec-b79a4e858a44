package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomAmazonVcPrice;
import com.zielsmart.mc.repository.entity.SomCustomSaleableWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomCustomSaleableWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.SomAmazonVcPriceVo;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2025-06-10 09:47:32
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomCustomSaleableWarehouseConfigService {

    @Resource
    private SomCustomSaleableWarehouseConfigMapper somCustomSaleableWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomCustomSaleableWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomCustomSaleableWarehouseConfigVo> queryByPage(SomCustomSaleableWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomCustomSaleableWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomCustomSaleableWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<SomCustomSaleableWarehouseConfig> all = somCustomSaleableWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(e -> e.getWarehouseCode(), y -> y.getWarehouseName(), (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(e -> e.getslCode(), y -> y.getslName(), (x1, x2) -> x1));
            Map<String, List<SomCustomSaleableWarehouseConfig>> allMap = all.stream().collect(Collectors.groupingBy(e -> e.getPlatform() + e.getSite() + e.getSellerSku(), Collectors.toList()));
            for (SomCustomSaleableWarehouseConfigVo config : pageResult.getList()) {
                String key = StrUtil.concat(true, config.getPlatform(), config.getSite(), config.getSellerSku());
                if (allMap.containsKey(key)) {
                    List<SomCustomSaleableWarehouseConfig> warehouseConfigs = allMap.get(key);
                    List<String> nameList = new ArrayList<>();
                    config.setList(warehouseConfigs.stream().map(x -> {
                        SomCustomSaleableWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomCustomSaleableWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableStorageCode(x.getUsableStorageLocation());
                        useableWarehouse.setUseableWarehouseCode(x.getUsableWarehouse());
                        nameList.add(warehouseMap.get(x.getUsableWarehouse()) + "-" + storageMap.get(x.getUsableStorageLocation()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    config.setWarehouseNameList(nameList);
                    SomCustomSaleableWarehouseConfig config1 = warehouseConfigs.get(0);
                    config.setCreateName(config1.getCreateName());
                    config.setCreateNum(config1.getCreateNum());
                    config.setCreateTime(config1.getCreateTime());
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomCustomSaleableWarehouseConfigVo.class, searchVo);
    }


    /**
     * delete
     * 删除
     *
     * @param somCustomSaleableWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void batchDelete(List<SomCustomSaleableWarehouseConfigVo> list) throws ValidateException {
        Set<String> sellerSkuSet = list.stream().map(SomCustomSaleableWarehouseConfigVo::getSellerSku).collect(Collectors.toSet());
        List<SomCustomSaleableWarehouseConfig> somCustomSaleableWarehouseConfigs = new ArrayList<>();
        CollUtil.split(sellerSkuSet, 500).forEach(batch -> {
            somCustomSaleableWarehouseConfigs.addAll(somCustomSaleableWarehouseConfigMapper.createLambdaQuery()
                    .andIn("seller_sku", batch)
                    .select("aid", "site", "seller_sku"));
        });

        Map<String, List<SomCustomSaleableWarehouseConfig>> filteredMap = somCustomSaleableWarehouseConfigs.stream()
                .collect(Collectors.groupingBy(
                        x -> x.getSite() + x.getSellerSku(),
                        Collectors.toList()
                ));

        List<String> aidList = new ArrayList<>();
        for (SomCustomSaleableWarehouseConfigVo vo : list) {
            String key = vo.getSite() + vo.getSellerSku();
            if (filteredMap.containsKey(key)) {
                aidList.addAll(filteredMap.get(key).stream()
                        .map(SomCustomSaleableWarehouseConfig::getAid)
                        .collect(Collectors.toList()));
            }
        }
        CollUtil.split(aidList, 500).forEach(batch -> {
            int deleted = somCustomSaleableWarehouseConfigMapper.createLambdaQuery()
                    .andIn("aid", batch)
                    .delete();
            log.info("删除了 {} 条记录", deleted);
        });
    }

    public String export(SomCustomSaleableWarehouseConfigPageSearchVo searchVo) {
            List<SomCustomSaleableWarehouseConfigVo> records =somCustomSaleableWarehouseConfigMapper.export(searchVo);
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "Amazon展示码指定仓库库区配置表管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomCustomSaleableWarehouseConfigVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public String importExcel(List<SomCustomSaleableWarehouseConfigVo> list, TokenUserInfo tokenUser) throws ValidateException {
        String errorMsg = "";
        List<SomCustomSaleableWarehouseConfig> insertList = new ArrayList<>();
        List<String> errorList=new ArrayList<>();
        List<SomCustomSaleableWarehouseConfig> allList = somCustomSaleableWarehouseConfigMapper.all();
        Map<String, List<SomCustomSaleableWarehouseConfig>> allMap = allList.stream().collect(Collectors.groupingBy(e -> e.getPlatform()+e.getSite()+e.getSellerSku()+e.getUsableWarehouse()+e.getUsableStorageLocation(), Collectors.toList()));
        Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(e -> e.getWarehouseName(), y -> y.getWarehouseCode(), (x1, x2) -> x1));
        Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(e -> e.getslName(), y -> y.getslCode(), (x1, x2) -> x1));
        for (SomCustomSaleableWarehouseConfigVo vo : list) {
            String usableWarehouseAndSl = vo.getUsableWarehouseAndSl();
            if (StrUtil.isNotBlank(usableWarehouseAndSl)) {
                String[] warehouseAndSlList = usableWarehouseAndSl.split(",");
                for (String warehouseAndSl : warehouseAndSlList) {
                    String usableWarehouse = StrUtil.subBefore(warehouseAndSl, ":", false);
                    String usableStorageLocation = StrUtil.subAfter(warehouseAndSl, ":", false);
                    String usableWarehouseCode=warehouseMap.get(usableWarehouse);
                    String usableStorageLocationCode=storageMap.get(usableStorageLocation);
                    String key= vo.getPlatform()+vo.getSite()+vo.getSellerSku()+usableWarehouseCode+usableStorageLocationCode;
                    if (allMap.containsKey(key)){
                        errorList.add("平台："+vo.getPlatform()+"，站点："+vo.getSite()+"，展示码："+vo.getSellerSku()+"，可售仓库："+usableWarehouse+"，库区："+usableStorageLocation+"已存在!");
                    }
                    SomCustomSaleableWarehouseConfig config = new SomCustomSaleableWarehouseConfig();
                    config.setAid(IdUtil.fastSimpleUUID());
                    config.setPlatform(vo.getPlatform());
                    config.setSite(vo.getSite());
                    config.setSellerSku(vo.getSellerSku());
                    config.setUsableWarehouse(usableWarehouseCode);
                    config.setUsableStorageLocation(usableStorageLocationCode);
                    config.setCreateName(tokenUser.getUserName());
                    config.setCreateNum(tokenUser.getJobNumber());
                    config.setCreateTime(new Date());
                    insertList.add(config);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(errorList)){
            errorMsg= StrUtil.join("\n",errorList);
            return errorMsg;
        }
        somCustomSaleableWarehouseConfigMapper.insertBatch(insertList);
        return errorMsg;
    }
}
