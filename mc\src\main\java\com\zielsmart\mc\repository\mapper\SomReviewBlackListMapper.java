package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomReviewBlackListPageSearchVo;
import com.zielsmart.mc.vo.SomReviewBlackListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2022-04-12
*/

@SqlResource("somReviewBlackList")
public interface SomReviewBlackListMapper extends BaseMapper<SomReviewBlackList> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomReviewBlackListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomReviewBlackListVo> queryByPage(@Param("searchVo")SomReviewBlackListPageSearchVo searchVo, PageRequest pageRequest);
}
