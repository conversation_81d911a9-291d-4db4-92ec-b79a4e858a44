package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.ZlccCatalogAmazonService;
import com.zielsmart.mc.vo.ZlccCatalogAmazonVo;
import com.zielsmart.mc.vo.ZlccCatalogTreeVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title ZlccCatalogAmazonController
 * @description
 * @date 2023-12-01 15:27:28
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/zlccCatalogAmazon", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Amazon类目管理")
public class ZlccCatalogAmazonController extends BasicController {

    @Resource
    ZlccCatalogAmazonService zlccCatalogAmazonService;

    @Operation(summary = "获取收藏类目数")
    @PostMapping(value = "/queryFavoriteTree")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZlccCatalogTreeVo>> queryFavoriteTree(@RequestBody ZlccCatalogAmazonVo zlccCatalogAmazonVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) {
        return ResultVo.ofSuccess(zlccCatalogAmazonService.queryFavoriteTree(zlccCatalogAmazonVo,tokenUser));
    }

    @Operation(summary = "收藏/取消收藏类目")
    @PostMapping(value = "/saveFavorite")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> saveFavorite(@RequestBody ZlccCatalogAmazonVo zlccCatalogAmazonVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        zlccCatalogAmazonService.saveFavorite(zlccCatalogAmazonVo,tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "查询类目树（只返回子集，如果类目ID不传，则返回第一级）")
    @PostMapping(value = "/queryChild")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<ZlccCatalogAmazonVo>> queryChildCategory(@RequestBody ZlccCatalogAmazonVo zlccCatalogAmazonVo) throws ValidateException {
        return ResultVo.ofSuccess(zlccCatalogAmazonService.queryChildCategory(zlccCatalogAmazonVo));
    }

    @Operation(summary = "下载子集")
    @PostMapping(value = "/downloadChild")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> downloadChild(@RequestBody ZlccCatalogAmazonVo zlccCatalogAmazonVo) throws Exception {
        zlccCatalogAmazonService.downloadChild(zlccCatalogAmazonVo);
        return ResultVo.ofSuccess();
    }
}
