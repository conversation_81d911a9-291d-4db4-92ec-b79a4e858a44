package com.zielsmart.mc.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/*
 * Temu库存推送黑名单的VO实体对象
 * gen by 代码生成器 2024-12-26
 */
@Data
@Schema(title = "Temu清仓列表", name = "SomTemuClearanceListVo")
public class SomTemuClearanceListVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "站点", name = "site")
    @Excel(name = "站点")
    private String site;

    @Schema(description = "店铺ID", name = "accountId")
    @Excel(name = "店铺ID")
    private String accountId;

    @Schema(description = "店铺名称", name = "accountName")
    @Excel(name = "店铺名称")
    private String accountName;

    @Schema(description = "平台SKU唯一编码", name = "productSkuId")
    @Excel(name = "SKU ID")
    private String productSkuId;

    @Schema(description = "展示码", name = "sellerSku")
    @Excel(name = "展示码")
    private String sellerSku;

    @Schema(description = "创建人工号", name = "createNum")
    private String createNum;

    @Schema(description = "创建人工号", name = "createName")
    @Excel(name = "创建人")
    private String createName;

    @Schema(description = "创建时间", name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
