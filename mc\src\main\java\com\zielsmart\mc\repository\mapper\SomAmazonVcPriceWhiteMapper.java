package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonVcPriceWhite;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhiteVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
 *
 * gen by 代码生成器 mapper 2025-01-21
 */

@SqlResource("somAmazonVcPriceWhite")
public interface SomAmazonVcPriceWhiteMapper extends BaseMapper<SomAmazonVcPriceWhite> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAmazonVcPriceWhiteVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonVcPriceWhiteVo> queryByPage(@Param("searchVo") SomAmazonVcPriceWhitePageSearchVo searchVo, PageRequest pageRequest);
}
