package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* 仓库基础信息
* gen by 代码生成器 2021-08-11
*/

@Table(name="mc.mc_warehouse")
public class McWarehouse implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 仓库编码
	 */
	@Column("warehouse_code")
	private String warehouseCode ;
	/**
	 * 仓库简称
	 */
	@Column("warehouse_name")
	private String warehouseName ;
	/**
	 * 库存市场
	 */
	@Column("storage_market_code")
	private String storageMarketCode ;
	/**
	 * 创建人
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;
	/**
	 * 同步时间
	 */
	@Column("sync_time")
	private Date syncTime ;

	public McWarehouse() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 仓库编码
	*@return
	*/
	public String getWarehouseCode(){
		return  warehouseCode;
	}
	/**
	* 仓库编码
	*@param  warehouseCode
	*/
	public void setWarehouseCode(String warehouseCode ){
		this.warehouseCode = warehouseCode;
	}
	/**
	* 仓库简称
	*@return
	*/
	public String getWarehouseName(){
		return  warehouseName;
	}
	/**
	* 仓库简称
	*@param  warehouseName
	*/
	public void setWarehouseName(String warehouseName ){
		this.warehouseName = warehouseName;
	}
	/**
	* 库存市场
	*@return
	*/
	public String getStorageMarketCode(){
		return  storageMarketCode;
	}
	/**
	* 库存市场
	*@param  storageMarketCode
	*/
	public void setStorageMarketCode(String storageMarketCode ){
		this.storageMarketCode = storageMarketCode;
	}
	/**
	* 创建人
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}
	/**
	* 同步时间
	*@return
	*/
	public Date getSyncTime(){
		return  syncTime;
	}
	/**
	* 同步时间
	*@param  syncTime
	*/
	public void setSyncTime(Date syncTime ){
		this.syncTime = syncTime;
	}

}
