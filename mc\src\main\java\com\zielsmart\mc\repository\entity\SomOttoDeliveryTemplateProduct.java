package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
/*
* OTTO展示码绑定运费模版
* gen by 代码生成器 2024-09-20
*/

@Table(name="mc.som_otto_delivery_template_product")
public class SomOttoDeliveryTemplateProduct implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 运费模版ID
	 */
	@Column("shipping_profile_id")
	private String shippingProfileId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 发货时效 天
	 */
	@Column("processing_time")
	private Integer processingTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	public SomOttoDeliveryTemplateProduct() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 运费模版ID
	*@return
	*/
	public String getShippingProfileId(){
		return  shippingProfileId;
	}
	/**
	* 运费模版ID
	*@param  shippingProfileId
	*/
	public void setShippingProfileId(String shippingProfileId ){
		this.shippingProfileId = shippingProfileId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 发货时效 天
	*@return
	*/
	public Integer getProcessingTime(){
		return  processingTime;
	}
	/**
	* 发货时效 天
	*@param  processingTime
	*/
	public void setProcessingTime(Integer processingTime ){
		this.processingTime = processingTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

}
