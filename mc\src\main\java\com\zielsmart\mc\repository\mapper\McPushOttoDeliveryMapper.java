package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McPushOttoDeliveryPageSearchVo;
import com.zielsmart.mc.vo.McPushOttoDeliveryVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-09-27
*/

@SqlResource("mcPushOttoDelivery")
public interface McPushOttoDeliveryMapper extends BaseMapper<McPushOttoDelivery> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McPushOttoDeliveryVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McPushOttoDeliveryVo> queryByPage(@Param("searchVo")McPushOttoDeliveryPageSearchVo searchVo, PageRequest pageRequest);
}
