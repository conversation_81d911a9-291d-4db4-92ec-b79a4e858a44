package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.vo.SomAmazonVcWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcWhiteListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.repository.mapper
 * @title SomAmazonVcWhiteListMapper
 * @description
 * @date 2024-02-01 16:22:25
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somAmazonVcWhiteList")
public interface SomAmazonVcWhiteListMapper extends BaseMapper<SomAmazonVcWhiteList> {
    /**
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link PageResult< SomAmazonVcWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAmazonVcWhiteListVo> queryByPage(@Param("searchVo") SomAmazonVcWhiteListPageSearchVo searchVo, PageRequest pageRequest);
}
