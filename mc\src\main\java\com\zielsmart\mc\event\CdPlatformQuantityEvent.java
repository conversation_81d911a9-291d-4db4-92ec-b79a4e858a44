package com.zielsmart.mc.event;

import lombok.Builder;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * @version V2.0
 * @title: TransferOrderStockMoveEvent
 * @package: com.zielsmart.eya.ims.event
 * @description:
 * @author: 李耀华
 * @date: 2020-09-2316:17
 * @Copyright: 2019 www.zielsmart.com Inc. All rights reserved.
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Builder
@Data
public class CdPlatformQuantityEvent extends ApplicationEvent {

    public CdPlatformQuantityEvent(Object source) {
        super(source);
    }

    private String platform;
    private String sku;
    private Integer quantity;

    public CdPlatformQuantityEvent(String platform, String sku, Integer quantity) {
        super("");
        this.platform = platform;
        this.sku = sku;
        this.quantity = quantity;
    }
}
