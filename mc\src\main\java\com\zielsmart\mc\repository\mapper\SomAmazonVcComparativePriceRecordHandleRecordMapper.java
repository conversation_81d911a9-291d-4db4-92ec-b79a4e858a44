package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonVcComparativePriceRecordHandleRecord;
import com.zielsmart.mc.vo.SomAmazonVcComparativePriceRecordHandleRecordVo;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2025-01-21
*/

@SqlResource("somAmazonVcComparativePriceRecordHandleRecord")
public interface SomAmazonVcComparativePriceRecordHandleRecordMapper extends BaseMapper<SomAmazonVcComparativePriceRecordHandleRecord> {

    /**
     * 根据主表id查询处理日志记录
     *
     * @param cid 主表id
     * @return List<SomAmazonVcComparativePriceRecordHandleRecordVo>
     */
    List<SomAmazonVcComparativePriceRecordHandleRecordVo> queryByCid(@Param("cid") String cid);
}
