package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomSheinWarehouseConfig;
import com.zielsmart.mc.repository.entity.SomSheinWarehouseInfo;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomSheinWarehouseConfigMapper;
import com.zielsmart.mc.repository.mapper.SomSheinWarehouseInfoMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomSheinWarehouseConfigVo;
import com.zielsmart.mc.vo.SomSheinWarehouseInfoVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomSheinWarehouseConfigService {
    @Resource
    private SomSheinWarehouseInfoMapper somSheinWarehouseInfoMapper;


    @Resource
    private SomSheinWarehouseConfigMapper somSheinWarehouseConfigMapper;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomSheinWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomSheinWarehouseConfigVo> queryByPage(SomSheinWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomSheinWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomSheinWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomSheinWarehouseConfig> all = somSheinWarehouseConfigMapper.all();
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(e -> e.getWarehouseCode(), y -> y.getWarehouseName(), (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(e -> e.getslCode(), y -> y.getslName(), (x1, x2) -> x1));
            Map<String, List<SomSheinWarehouseConfig>> allMap = all.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode(), Collectors.toList()));
            for (SomSheinWarehouseConfigVo config : pageResult.getList()) {
                if (allMap.containsKey(config.getWarehouseCode())) {
                    List<SomSheinWarehouseConfig> warehouseConfigs = allMap.get(config.getWarehouseCode());
                    List<String> nameList = new ArrayList<>();
                    config.setList(warehouseConfigs.stream().map(x -> {
                        SomSheinWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomSheinWarehouseConfigVo.UseableWarehouse();
                        useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                        useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                        nameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                        return useableWarehouse;
                    }).collect(Collectors.toList()));
                    config.setWarehouseNameList(nameList);
                    SomSheinWarehouseConfig config1 = warehouseConfigs.get(0);
                    config.setCreateName(config1.getCreateName());
                    config.setCreateNum(config1.getCreateNum());
                    config.setCreateTime(config1.getCreateTime());
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomSheinWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somSheinWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomSheinWarehouseConfigVo somSheinWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somSheinWarehouseConfigVo) || somSheinWarehouseConfigVo.getWarehouseCode() == null || somSheinWarehouseConfigVo.getList() == null || somSheinWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somSheinWarehouseConfigVo.getWarehouseCode();
        if (somSheinWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).count() > 0) {
            throw new ValidateException("您输入的平台仓库在系统中已存在，不允许重复添加");
        }
        List<SomSheinWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomSheinWarehouseConfigVo.UseableWarehouse useableWarehouse : somSheinWarehouseConfigVo.getList()) {
            SomSheinWarehouseConfig config = new SomSheinWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somSheinWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somSheinWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * update
     * 修改
     *
     * @param somSheinWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomSheinWarehouseConfigVo somSheinWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somSheinWarehouseConfigVo) || somSheinWarehouseConfigVo.getWarehouseCode() == null || somSheinWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somSheinWarehouseConfigVo.getWarehouseCode();
        somSheinWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).delete();
        List<SomSheinWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomSheinWarehouseConfigVo.UseableWarehouse useableWarehouse : somSheinWarehouseConfigVo.getList()) {
            SomSheinWarehouseConfig config = new SomSheinWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somSheinWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            config.setLastModifyNum(tokenUser.getJobNumber());
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somSheinWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somSheinWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomSheinWarehouseConfigVo somSheinWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somSheinWarehouseConfigVo) || StrUtil.isEmpty(somSheinWarehouseConfigVo.getWarehouseCode())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somSheinWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", somSheinWarehouseConfigVo.getWarehouseCode()).delete();
    }

    public List<SomSheinWarehouseInfoVo> warehouse() {
        List<SomSheinWarehouseInfo> warehouseConfigList = somSheinWarehouseInfoMapper.createLambdaQuery().select("warehouse_code","warehouse_name");
        List<SomSheinWarehouseInfoVo> warehouseList = ConvertUtils.listConvert(warehouseConfigList, SomSheinWarehouseInfoVo.class);
        for (SomSheinWarehouseInfoVo somSheinWarehouseInfoVo : warehouseList) {
            somSheinWarehouseInfoVo.setWarehouseCodeAndName(somSheinWarehouseInfoVo.getWarehouseCode() + "-" + somSheinWarehouseInfoVo.getWarehouseName());
        }
        return warehouseList;
    }
}
