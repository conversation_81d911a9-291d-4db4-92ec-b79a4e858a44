package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomVcAmazonPurchaseOrderInvoice;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2023-08-17
*/

@SqlResource("somVcAmazonPurchaseOrderInvoice")
public interface SomVcAmazonPurchaseOrderInvoiceMapper extends BaseMapper<SomVcAmazonPurchaseOrderInvoice> {

}
