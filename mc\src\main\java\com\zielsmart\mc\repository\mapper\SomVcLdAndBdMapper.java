package com.zielsmart.mc.repository.mapper;
import cn.hutool.core.collection.CollUtil;
import com.zielsmart.mc.vo.SomVcLdAndBdPageSearchVo;
import com.zielsmart.mc.vo.SomVcLdAndBdVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;

@SqlResource("somVcLdAndBd")
public interface SomVcLdAndBdMapper extends BaseMapper<SomVcLdAndBd> {

    /**
     * 分页查询
     * @param searchVo SomVcLdAndBdPageSearchVo
     * @return PageResult<SomVcLdAndBdVo>
     */
    PageResult<SomVcLdAndBdVo> queryByPage(@Param("searchVo")SomVcLdAndBdPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 查询列表
     * @param searchVo SomVcLdAndBdPageSearchVo
     * @return List<SomVcLdAndBdVo>
     */
    List<SomVcLdAndBdVo> queryList(@Param("searchVo")SomVcLdAndBdPageSearchVo searchVo);

    /**
     * 批量反馈提报结果
     * @param updateList 需要更新的数据
     */
    default void batchFeedbackSubmitResult(@Param("updateSomPddAndOds") List<SomVcLdAndBd> updateList) {
        if (CollUtil.isEmpty(updateList)) {
            return;
        }
        this.getSQLManager().updateBatch(SqlId.of("somVcLdAndBd.batchFeedbackSubmitResult"), updateList);
    }

}
