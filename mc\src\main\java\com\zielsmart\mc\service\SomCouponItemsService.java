package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.*;
import com.zielsmart.mc.repository.mapper.*;
import com.zielsmart.mc.third.IBiService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomCouponItemsService {

    @Resource
    private SomCouponItemsMapper somCouponItemsMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McListingInfoAmazonMapper amazonMapper;
    @Resource
    private AmazonService amazonService;
    @Resource
    private McProductSalesMapper productSalesMapper;
    @Resource
    private McSellerskuMappingMapper skuMappingMapper;
    @Resource
    private SomFreeDealRecommendItemMapper itemMapper;
    @Resource
    private SomFreeDealRecommendMapper somFreeDealRecommendMapper;
    @Resource
    private IBiService biService;
    @Value("${bi.magic.head.token}")
    private String biToken;


    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomCouponItemsVo> queryByPage(SomCouponItemsPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomCouponItemsVo> pageResult = dynamicSqlManager.getMapper(SomCouponItemsMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomCouponItemsVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somCouponItemsVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomCouponItemsVo somCouponItemsVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somCouponItemsVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somCouponItemsVo.setAid(IdUtil.fastSimpleUUID());
        somCouponItemsMapper.insert(ConvertUtils.beanConvert(somCouponItemsVo, SomCouponItems.class));
    }

    /**
     * update
     * 修改
     * @param somCouponItemsVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomCouponItemsVo somCouponItemsVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somCouponItemsVo) || StrUtil.isEmpty(somCouponItemsVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        somCouponItemsMapper.createLambdaQuery()
                .andEq("aid",somCouponItemsVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somCouponItemsVo, SomCouponItems.class));
    }

    /**
     * delete
     * 删除
     * @param somCouponItemsVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomCouponItemsVo somCouponItemsVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somCouponItemsVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        // somCouponItemsMapper.createLambdaQuery().andIn("aid", somCouponItemsVo.getAidList()).delete();
        somCouponItemsMapper.createLambdaQuery().andEq("aid",somCouponItemsVo.getAid()).delete();
    }

    /**
     * queryCouponPromotionDiscount
     * 查询coupon和promotion折扣
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomCouponItemsVo}
     * <AUTHOR>
     * @history
     */
    public SomCouponItemsVo queryCouponPromotionDiscount(SomCouponSearchVo searchVo) throws  ValidateException {
        if(ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getSite()) || ObjectUtil.isEmpty(searchVo.getBeginDate())|| ObjectUtil.isEmpty(searchVo.getEndDate()) || StrUtil.isBlank(searchVo.getSellerSku())){
            throw new ValidateException("参数存在空值,请检查");
        }
        String originalSellerSku = searchVo.getSellerSku();
        SomCouponItemsVo resultVo = new SomCouponItemsVo();
        McSellerskuMapping obj = skuMappingMapper.createLambdaQuery().andEq("site", searchVo.getSite()).andEq("product_display_code", searchVo.getSellerSku()).single();
        if(ObjectUtil.isNotEmpty(obj) && StrUtil.isNotEmpty(obj.getSellerSku())){
            searchVo.setSellerSku(obj.getSellerSku());
        }
        McListingInfoAmazon amazonVo = amazonMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).andEq("seller_sku", searchVo.getSellerSku()).single();
        // 查询现售价
        if(ObjectUtil.isNotEmpty(amazonVo)){
            resultVo.setPrice(null == amazonVo.getPrice()?BigDecimal.ZERO:amazonVo.getPrice());
            resultVo.setAsin(amazonVo.getAsinCode());
            resultVo.setFulfillmentChannel(amazonVo.getFulfillmentChannel());
            resultVo.setProductName(amazonVo.getProductName());
        }

        McProductSales productSales = productSalesMapper.createLambdaQuery().andEq("platform", searchVo.getPlatform()).andEq("site", searchVo.getSite()).andEq("display_product_code", searchVo.getSellerSku()).single();
        if(ObjectUtil.isNotEmpty(productSales) && StrUtil.isNotBlank(productSales.getCategoryName())){
            resultVo.setCategoryName(productSales.getCategoryName());
        }
        // 查询coupon折扣
        List<SomCouponItemsVo> couponItemsVo = somCouponItemsMapper.queryCouponPromotionDiscount(searchVo);
        if(CollectionUtil.isNotEmpty(couponItemsVo)){
            List<SomCouponItemsVo> sortList = couponItemsVo.stream().sorted(Comparator.comparing(SomCouponItemsVo::getCouponDiscount).reversed()).collect(Collectors.toList());
            resultVo.setCouponDiscount(sortList.get(0).getCouponDiscount());
        }
        // 查询promotion折扣
        McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
        searchExVo.setSite(searchVo.getSite());
        searchExVo.setKeyWord(searchVo.getSellerSku());
        searchExVo.setBeginDate(searchVo.getBeginDate());
        searchExVo.setEndDate(searchVo.getEndDate());
        BigDecimal bigDecimal = amazonService.queryPromotionDiscount(searchExVo);
        if(ObjectUtil.isNotEmpty(bigDecimal)){
            resultVo.setPromotionDiscount(bigDecimal);
        }

        // 获取利润计算值Bi 补充所需字段
        McProductSales mcProductSales = productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("display_product_code", originalSellerSku)
                .andEq("site", searchVo.getSite())
                .single();
        List<BiAmazonProfitCalculationVo> biBody = new ArrayList<>();
        BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
        bi.setSite(searchVo.getSite());
        // 美国站点 需传开始结束时间
        if ("Amazon.com".equals(searchVo.getSite())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String date = sdf.format(searchVo.getBeginDate());
            bi.setDate(date);
        }
        bi.setPrice(searchVo.getDealPrice());
        bi.setDisplayProductCode(originalSellerSku);
        bi.setProductMainCode(mcProductSales.getProductMainCode());
        bi.setPromotionType(searchVo.getPromotionType());
        biBody.add(bi);

        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                x -> x.getSite() + x.getDisplayProductCode(),
                Function.identity(),
                (x1, x2) -> x2)
        );
        BigDecimal dealPriceGross = BigDecimal.ZERO; // 秒杀价毛利率
        BigDecimal dealBurstCoefficient = BigDecimal.ZERO; // 活动预计爆发系数
        BigDecimal dmsLast30day = BigDecimal.ZERO; // 近三十天DMS
        BigDecimal categoryGross = BigDecimal.ZERO; // 三级分类近四周毛利率
        String key = searchVo.getSite() + searchVo.getSellerSku();
        if (biMap.containsKey(key)) {
            dealPriceGross = biMap.get(key).getGross();
            if (dealPriceGross != null && dealPriceGross.compareTo(BigDecimal.valueOf(-9999)) != 0) {
                dealPriceGross = dealPriceGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            }
            dealBurstCoefficient = biMap.get(key).getDealBurstCoefficient();
            dealBurstCoefficient = dealBurstCoefficient != null ? dealBurstCoefficient.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            dmsLast30day = biMap.get(key).getDmsLast30day();
            dmsLast30day = dmsLast30day != null ? dmsLast30day.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            categoryGross = biMap.get(key).getCategoryGross();
            categoryGross = categoryGross != null ? categoryGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        }

        resultVo.setDealPriceGross(dealPriceGross);
        resultVo.setDealBurstCoefficient(dealBurstCoefficient);
        resultVo.setDmsLast30day(dmsLast30day);
        resultVo.setCategoryGross(categoryGross);

        return resultVo;
    }

    /**
     * queryDiscountList
     * 查询coupon和promotion折扣集合
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomCouponItemsVo> queryDiscountList(SomCouponSearchVo searchVo) throws ValidateException{
        if(ObjectUtil.isEmpty(searchVo) || StrUtil.isBlank(searchVo.getSite()) || ObjectUtil.isEmpty(searchVo.getBeginDate())|| ObjectUtil.isEmpty(searchVo.getEndDate()) || CollectionUtil.isEmpty(searchVo.getSellerSkuList())){
            throw new ValidateException("参数存在空值,请检查");
        }
        List<McSellerskuMapping> mappingList = skuMappingMapper.createLambdaQuery().andEq("site", searchVo.getSite()).andIn("product_display_code", searchVo.getSellerSkuList()).select();
        List<SomCouponItemsVo> resutList = new ArrayList<>();
        List<String> tempList = new ArrayList<>();
        Map<String, SomFreeDealRecommendItem> itemMap = new HashMap<>();

        if(StrUtil.isNotEmpty(searchVo.getId())){
            SomFreeDealRecommendItem single = itemMapper.single(searchVo.getId());
            List<SomFreeDealRecommendItem> itemList = itemMapper.createLambdaQuery().andEq("rid", single.getRid()).select();
            itemMap = itemList.stream().collect(Collectors.toMap(x -> x.getId(), Function.identity()));
            searchVo.setSellerSkuList(itemList.stream().map(x->x.getSku()).distinct().collect(Collectors.toList()));
            itemList.forEach(f->{
                SomCouponItemsVo itemsVo = new SomCouponItemsVo();
                McSellerskuMapping mapping = mappingList.stream().filter(m -> StrUtil.equals(f.getSku(), m.getProductDisplayCode())).findFirst().orElse(null);
                if(ObjectUtil.isNotEmpty(mapping)){
                    tempList.add(mapping.getSellerSku());
                }else {
                    tempList.add(f.getSku());
                }
                itemsVo.setItemId(f.getId());
                itemsVo.setSellerSku(f.getSku());
                resutList.add(itemsVo);
            });
        }else{
            searchVo.getSellerSkuList().forEach(f->{
                SomCouponItemsVo itemsVo = new SomCouponItemsVo();
                McSellerskuMapping mapping = mappingList.stream().filter(m -> StrUtil.equals(f, m.getProductDisplayCode())).findFirst().orElse(null);
                if(ObjectUtil.isNotEmpty(mapping)){
                    tempList.add(mapping.getSellerSku());
                }else {
                    tempList.add(f);
                }
                itemsVo.setItemId(f);
                itemsVo.setSellerSku(f);
                resutList.add(itemsVo);
            });
        }

        // 查询基础信息
        List<McListingInfoAmazonExVo> amazonVoList = amazonMapper.queryBySiteSellerSkus(searchVo.getSite(),searchVo.getSellerSkuList());
        // 查询listing信息
        List<McListingInfoAmazon> amazonList = amazonMapper.createLambdaQuery().andEq("site", searchVo.getSite()).andIn("seller_sku",tempList).select();
        // 查询coupon折扣
        List<SomCouponItemsExtVo> couponVoList = somCouponItemsMapper.queryDiscountList(searchVo);
        // 查询配置信息
        McDictionaryInfo dictionaryInfo = dynamicSqlManager.getMapper(McDictionaryInfoMapper.class).createLambdaQuery().andEq("item_type_code", "RecommendFlag").single();
        // 查询推荐数据
//        List<SomFreeDealRecommend> dealRecommendList = new ArrayList<>();
//        if( StrUtil.equalsIgnoreCase(String.valueOf(1),dictionaryInfo.getItemValue()) && ObjectUtil.isNotEmpty(searchVo.getDealType())){
//                dealRecommendList = dynamicSqlManager.getMapper(SomFreeDealRecommendMapper.class).createLambdaQuery().andEq("site", searchVo.getSite())
//                        .andEq("nudge_type", searchVo.getDealType().toString()).andIn("seller_sku", searchVo.getSellerSkuList()).select();
//        }
        for(SomCouponItemsVo itemsVo :resutList){
            McListingInfoAmazonExVo product = amazonVoList.stream().filter(f -> StrUtil.equals(itemsVo.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
            if(ObjectUtil.isNotEmpty(product)){
                itemsVo.setAid(product.getAid());
//                itemsVo.setPrice(amazonVo.getPrice());
                itemsVo.setBusinessGroupCode(product.getBusinessGroupCode());
                itemsVo.setSalesGroupName(product.getSalesGroupName());
                itemsVo.setBusinessLeaderCode(product.getBusinessLeaderCode());
                itemsVo.setSalesGroupEmptName(product.getSalesGroupEmptName());
                itemsVo.setBusinessOperationCode(product.getBusinessOperationCode());
                itemsVo.setOperationEmptName(product.getOperationEmptName());
                itemsVo.setCategoryName(product.getCategoryName());
            }
            McListingInfoAmazon amazon = new McListingInfoAmazon();
            McSellerskuMapping mapping = mappingList.stream().filter(f -> StrUtil.equals(itemsVo.getSellerSku(), f.getProductDisplayCode())).findFirst().orElse(null);
            if(ObjectUtil.isNotEmpty(mapping)){
                amazonList.stream().filter(f ->StrUtil.equals(mapping.getSellerSku(),f.getSellerSku())).findFirst().ifPresent(ps->{
                    itemsVo.setSku(ps.getSku());
                    itemsVo.setAsin(ps.getAsinCode());
                    itemsVo.setPrice(ps.getPrice());
                    itemsVo.setCurrencySymbol(ps.getCurrencyCode());
                });
            }else {
                amazonList.stream().filter(f ->StrUtil.equals(itemsVo.getSellerSku(),f.getSellerSku())).findFirst().ifPresent(ps->{
                    itemsVo.setSku(ps.getSku());
                    itemsVo.setAsin(ps.getAsinCode());
                    itemsVo.setPrice(ps.getPrice());
                    itemsVo.setCurrencySymbol(ps.getCurrencyCode());
                });
            }

//            SomFreeDealRecommend dealRecommend = dealRecommendList.stream().filter(f -> StrUtil.equals(itemsVo.getSellerSku(), f.getSellerSku())).findFirst().orElse(null);
            if(itemMap.containsKey(itemsVo.getItemId())){
                SomFreeDealRecommendItem itemData = itemMap.get(itemsVo.getItemId());
                itemsVo.setPrice(itemData.getSellerPrice());
                itemsVo.setDealPrice(itemData.getDealRecommendPrice());
                itemsVo.setDealQuantity(itemData.getDealQuantity());
            }
            List<SomCouponItemsExtVo> couponList = couponVoList.stream().filter(f -> StrUtil.equals(itemsVo.getSellerSku(), f.getSellerSku())
                    && (searchVo.getBeginDate().compareTo(f.getBeginDate()) == 0 || searchVo.getBeginDate().after(f.getBeginDate()))
                    && (searchVo.getEndDate().compareTo(f.getEndDate()) == 0 || searchVo.getEndDate().before(f.getEndDate()))).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(couponList)){
                List<SomCouponItemsExtVo> sortList = couponList.stream().sorted(Comparator.comparing(SomCouponItemsVo::getCouponDiscount).reversed()).collect(Collectors.toList());
                itemsVo.setCouponDiscount(sortList.get(0).getCouponDiscount());
            }else {
                itemsVo.setCouponDiscount(BigDecimal.ZERO);
            }
            // 查询promotion折扣
            McListingInfoAmazonSearchExVo searchExVo = new McListingInfoAmazonSearchExVo();
            searchExVo.setSite(searchVo.getSite());
            searchExVo.setKeyWord(itemsVo.getSellerSku());
            searchExVo.setBeginDate(searchVo.getBeginDate());
            searchExVo.setEndDate(searchVo.getEndDate());
            BigDecimal bigDecimal = amazonService.queryPromotionDiscount(searchExVo);
            if(ObjectUtil.isNotEmpty(bigDecimal)){
                itemsVo.setPromotionDiscount(bigDecimal);
            }else {
                itemsVo.setPromotionDiscount(BigDecimal.ZERO);
            }
        }

        // 获取利润计算值Bi 补充所需字段
        List<String> displayProductCodeList = searchVo.getBiParamList().stream().map(BiAmazonProfitCalculationVo::getDisplayProductCode).collect(Collectors.toList());
        List<McProductSales> mcProductSalesList =  productSalesMapper.createLambdaQuery()
                .andEq("is_enabled", 1)
                .andEq("site", searchVo.getSite())
                .andIn("display_product_code", displayProductCodeList)
                .select();
        Map<String, McProductSales> mcProductSalesMap = mcProductSalesList.stream().collect(Collectors.toMap(
                McProductSales::getDisplayProductCode,
                Function.identity(),
                (x1, x2) -> x2
        ));
        List<BiAmazonProfitCalculationVo> biBody = new ArrayList<>();
        String site = searchVo.getSite();
        String promotionType = searchVo.getPromotionType();
        for (BiAmazonProfitCalculationVo biParam : searchVo.getBiParamList()) {
            BiAmazonProfitCalculationVo bi = new BiAmazonProfitCalculationVo();
            bi.setSite(site);
            // 美国站点 需传开始结束时间
            if ("Amazon.com".equals(searchVo.getSite())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String date = sdf.format(searchVo.getBeginDate());
                bi.setDate(date);
            }
            bi.setPrice(biParam.getDealPrice());
            bi.setDisplayProductCode(biParam.getDisplayProductCode());
            bi.setPromotionType(promotionType);
            McProductSales mcProductSales = mcProductSalesMap.get(biParam.getDisplayProductCode());
            if (mcProductSales != null) {
                bi.setProductMainCode(mcProductSales.getProductMainCode());
            } else {
                bi.setProductMainCode(null);
            }
            biBody.add(bi);
        }
        List<BiAmazonProfitCalculationVo> biList = biService.getAmzProfitCalculation(biToken, biBody).getData();
        // biList 根据站点+展示码转map
        Map<String, BiAmazonProfitCalculationVo> biMap = biList.stream().collect(Collectors.toMap(
                BiAmazonProfitCalculationVo::getDisplayProductCode,
                Function.identity(),
                (x1, x2) -> x2)
        );
        for (SomCouponItemsVo list : resutList) {
            String key = list.getSellerSku();
            BigDecimal dealPriceGross = BigDecimal.ZERO; // 秒杀价毛利率
            BigDecimal dealBurstCoefficient = BigDecimal.ZERO; // 活动预计爆发系数
            BigDecimal dmsLast30day = BigDecimal.ZERO; // 近三十天DMS
            BigDecimal categoryGross = BigDecimal.ZERO; // 三级分类近四周毛利率
            if (biMap.containsKey(key)) {
                dealPriceGross = biMap.get(key).getGross();
                if (dealPriceGross != null && dealPriceGross.compareTo(BigDecimal.valueOf(-9999)) != 0) {
                    dealPriceGross = dealPriceGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                }
                dealBurstCoefficient = biMap.get(key).getDealBurstCoefficient();
                dealBurstCoefficient = dealBurstCoefficient != null ? dealBurstCoefficient.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                dmsLast30day = biMap.get(key).getDmsLast30day();
                dmsLast30day = dmsLast30day != null ? dmsLast30day.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                categoryGross = biMap.get(key).getCategoryGross();
                categoryGross = categoryGross != null ? categoryGross.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            }
            list.setDealPriceGross(dealPriceGross);
            list.setDealBurstCoefficient(dealBurstCoefficient);
            list.setDmsLast30day(dmsLast30day);
            list.setCategoryGross(categoryGross);
        }
        System.out.println(biMap);

        return resutList.stream().filter(f->StrUtil.isNotEmpty(f.getAid())).collect(Collectors.toList());
    }
}
