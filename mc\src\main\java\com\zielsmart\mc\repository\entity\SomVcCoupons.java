package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
/*
* 
* gen by 代码生成器 2025-05-07
*/

@Table(name="mc.som_vc_coupons")
public class SomVcCoupons implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 账号名称/大分类
	 */
	@Column("account_name")
	private String accountName ;
	/**
	 * 供应商编码
	 */
	@Column("vendor_code")
	private String vendorCode ;
	/**
	 * 促销名称
	 */
	@Column("promotion_name")
	private String promotionName ;
	/**
	 * 客户群体，枚举值：10. All (Default) 20. Amazon Prime
	 */
	@Column("customer_segment")
	private Integer customerSegment ;
	/**
	 * 优惠券标题
	 */
	@Column("coupon_name")
	private String couponName ;
	/**
	 * 网站展示的名称
	 */
	@Column("website_display_name")
	private String websiteDisplayName ;
	/**
	 * 供应商发票编码
	 */
	@Column("vendor_invoice_code")
	private String vendorInvoiceCode ;
	/**
	 * 预算
	 */
	@Column("budget")
	private BigDecimal budget ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 起始日期，格式：yyyy-MM-dd
	 */
	@Column("start_date")
	private Date startDate ;
	/**
	 * 截止日期，格式：yyyy-MM-dd
	 */
	@Column("end_date")
	private Date endDate ;
	/**
	 * 优惠券类型：10. Percent off   20.Money off
	 */
	@Column("discount_type")
	private Integer discountType ;
	/**
	 * 若用户选择的是Percent off,此字段存储折扣百分比。若用户选择的是Money off，此字段存储折扣金额
	 */
	@Column("discount_value")
	private BigDecimal discountValue ;
	/**
	 * 一个客户是否只能兑换一次。 1. 是  0.否
	 */
	@Column("once_per_customer")
	private Integer oncePerCustomer ;
	/**
	 * 优惠券分布 枚举 10=Amazon
 20=Fresh 
30=Amazon,Fresh
	 */
	@Column("coupon_distribution")
	private Integer couponDistribution ;
	/**
	 * 活动状态 10=草稿
 20=未开始
 30=进行中
 40=需要关注
 50=已结束
 60=失效 
99=已取消
	 */
	@Column("coupon_status")
	private Integer couponStatus ;
	/**
	 * 需要关注时，错误提示信息
	 */
	@Column("need_attention_reason")
	private String needAttentionReason ;
	/**
	 * 申请原因 10=提升排名
 20=清货
 30=稳排名 
99=自定义
	 */
	@Column("apply_reason")
	private Integer applyReason ;
	/**
	 * 自定义申请原因
	 */
	@Column("custom_reason")
	private String customReason ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 修改人工号
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;

	public SomVcCoupons() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 账号名称/大分类
	*@return
	*/
	public String getAccountName(){
		return  accountName;
	}
	/**
	* 账号名称/大分类
	*@param  accountName
	*/
	public void setAccountName(String accountName ){
		this.accountName = accountName;
	}
	/**
	* 供应商编码
	*@return
	*/
	public String getVendorCode(){
		return  vendorCode;
	}
	/**
	* 供应商编码
	*@param  vendorCode
	*/
	public void setVendorCode(String vendorCode ){
		this.vendorCode = vendorCode;
	}
	/**
	* 促销名称
	*@return
	*/
	public String getPromotionName(){
		return  promotionName;
	}
	/**
	* 促销名称
	*@param  promotionName
	*/
	public void setPromotionName(String promotionName ){
		this.promotionName = promotionName;
	}
	/**
	* 客户群体，枚举值：10. All (Default) 20. Amazon Prime
	*@return
	*/
	public Integer getCustomerSegment(){
		return  customerSegment;
	}
	/**
	* 客户群体，枚举值：10. All (Default) 20. Amazon Prime
	*@param  customerSegment
	*/
	public void setCustomerSegment(Integer customerSegment ){
		this.customerSegment = customerSegment;
	}
	/**
	* 优惠券标题
	*@return
	*/
	public String getCouponName(){
		return  couponName;
	}
	/**
	* 优惠券标题
	*@param  couponName
	*/
	public void setCouponName(String couponName ){
		this.couponName = couponName;
	}
	/**
	* 网站展示的名称
	*@return
	*/
	public String getWebsiteDisplayName(){
		return  websiteDisplayName;
	}
	/**
	* 网站展示的名称
	*@param  websiteDisplayName
	*/
	public void setWebsiteDisplayName(String websiteDisplayName ){
		this.websiteDisplayName = websiteDisplayName;
	}
	/**
	* 供应商发票编码
	*@return
	*/
	public String getVendorInvoiceCode(){
		return  vendorInvoiceCode;
	}
	/**
	* 供应商发票编码
	*@param  vendorInvoiceCode
	*/
	public void setVendorInvoiceCode(String vendorInvoiceCode ){
		this.vendorInvoiceCode = vendorInvoiceCode;
	}
	/**
	* 预算
	*@return
	*/
	public BigDecimal getBudget(){
		return  budget;
	}
	/**
	* 预算
	*@param  budget
	*/
	public void setBudget(BigDecimal budget ){
		this.budget = budget;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 起始日期，格式：yyyy-MM-dd
	*@return
	*/
	public Date getStartDate(){
		return  startDate;
	}
	/**
	* 起始日期，格式：yyyy-MM-dd
	*@param  startDate
	*/
	public void setStartDate(Date startDate ){
		this.startDate = startDate;
	}
	/**
	* 截止日期，格式：yyyy-MM-dd
	*@return
	*/
	public Date getEndDate(){
		return  endDate;
	}
	/**
	* 截止日期，格式：yyyy-MM-dd
	*@param  endDate
	*/
	public void setEndDate(Date endDate ){
		this.endDate = endDate;
	}
	/**
	* 优惠券类型：10. Percent off   20.Money off
	*@return
	*/
	public Integer getDiscountType(){
		return  discountType;
	}
	/**
	* 优惠券类型：10. Percent off   20.Money off
	*@param  discountType
	*/
	public void setDiscountType(Integer discountType ){
		this.discountType = discountType;
	}
	/**
	* 若用户选择的是Percent off,此字段存储折扣百分比。若用户选择的是Money off，此字段存储折扣金额
	*@return
	*/
	public BigDecimal getDiscountValue(){
		return  discountValue;
	}
	/**
	* 若用户选择的是Percent off,此字段存储折扣百分比。若用户选择的是Money off，此字段存储折扣金额
	*@param  discountValue
	*/
	public void setDiscountValue(BigDecimal discountValue ){
		this.discountValue = discountValue;
	}
	/**
	* 一个客户是否只能兑换一次。 1. 是  0.否
	*@return
	*/
	public Integer getOncePerCustomer(){
		return  oncePerCustomer;
	}
	/**
	* 一个客户是否只能兑换一次。 1. 是  0.否
	*@param  oncePerCustomer
	*/
	public void setOncePerCustomer(Integer oncePerCustomer ){
		this.oncePerCustomer = oncePerCustomer;
	}
	/**
	* 优惠券分布 枚举 10=Amazon
 20=Fresh 
30=Amazon,Fresh
	*@return
	*/
	public Integer getCouponDistribution(){
		return  couponDistribution;
	}
	/**
	* 优惠券分布 枚举 10=Amazon
 20=Fresh 
30=Amazon,Fresh
	*@param  couponDistribution
	*/
	public void setCouponDistribution(Integer couponDistribution ){
		this.couponDistribution = couponDistribution;
	}
	/**
	* 活动状态 10=草稿
 20=未开始
 30=进行中
 40=需要关注
 50=已结束
 60=失效 
99=已取消
	*@return
	*/
	public Integer getCouponStatus(){
		return  couponStatus;
	}
	/**
	* 活动状态 10=草稿
 20=未开始
 30=进行中
 40=需要关注
 50=已结束
 60=失效 
99=已取消
	*@param  couponStatus
	*/
	public void setCouponStatus(Integer couponStatus ){
		this.couponStatus = couponStatus;
	}
	/**
	* 需要关注时，错误提示信息
	*@return
	*/
	public String getNeedAttentionReason(){
		return  needAttentionReason;
	}
	/**
	* 需要关注时，错误提示信息
	*@param  needAttentionReason
	*/
	public void setNeedAttentionReason(String needAttentionReason ){
		this.needAttentionReason = needAttentionReason;
	}
	/**
	* 申请原因 10=提升排名
 20=清货
 30=稳排名 
99=自定义
	*@return
	*/
	public Integer getApplyReason(){
		return  applyReason;
	}
	/**
	* 申请原因 10=提升排名
 20=清货
 30=稳排名 
99=自定义
	*@param  applyReason
	*/
	public void setApplyReason(Integer applyReason ){
		this.applyReason = applyReason;
	}
	/**
	* 自定义申请原因
	*@return
	*/
	public String getCustomReason(){
		return  customReason;
	}
	/**
	* 自定义申请原因
	*@param  customReason
	*/
	public void setCustomReason(String customReason ){
		this.customReason = customReason;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 修改人工号
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人工号
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}

}
