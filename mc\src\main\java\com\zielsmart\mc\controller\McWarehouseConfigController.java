package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.McWarehouseConfigService;
import com.zielsmart.mc.vo.McWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.McWarehouseConfigVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWarehouseConfigController
 * @description
 * @date 2021-08-04 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "可售仓库配置管理")
public class McWarehouseConfigController extends BasicController {

    @Resource
    McWarehouseConfigService mcWarehouseConfigService;

    /**
     * 获取可售仓库
     *
     * @param mcWarehouseConfigVo
     * @return {@link ResultVo< McWarehouseConfigVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "获取可售仓库")
    @PostMapping(value = "/get-warehouse")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McWarehouseConfigVo>> getWarehouse(@RequestBody McWarehouseConfigVo mcWarehouseConfigVo) throws ValidateException {
        return ResultVo.ofSuccess(mcWarehouseConfigService.getWarehouse(mcWarehouseConfigVo));
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McWarehouseConfigVo>> queryByPage(@RequestBody McWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McWarehouseConfigVo mcWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWarehouseConfigService.save(mcWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param mcWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McWarehouseConfigVo mcWarehouseConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWarehouseConfigService.update(mcWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McWarehouseConfigVo mcWarehouseConfigVo) throws ValidateException {
        mcWarehouseConfigService.delete(mcWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }
}
