package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAmazonLostSearchVo;
import com.zielsmart.mc.vo.SomLostCaseVo;
import com.zielsmart.mc.vo.SomLostVo;
import com.zielsmart.mc.vo.dict.McDictionaryInfoVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
import org.beetl.sql.mapper.annotation.Update;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2023-04-03
*/

@SqlResource("somLostCase")
public interface SomLostCaseMapper extends BaseMapper<SomLostCase> {

    PageResult<SomLostVo> queryByPage(@Param("searchVo")SomAmazonLostSearchVo searchVo, PageRequest pageRequest);

    PageResult<SomLostCaseVo> itemQueryByPage(@Param("searchVo")SomAmazonLostSearchVo searchVo, PageRequest pageRequest);

    @Update
    void updateCaseStatus(@Param("aids")List<String> aids);
}
