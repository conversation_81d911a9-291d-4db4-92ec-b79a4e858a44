package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomAmazonVcPriceWhiteService;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhitePageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcPriceWhiteVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonVcPriceWhiteController
 * @description
 * @date 2025-01-21 15:49:07
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonVcPriceWhite", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC比价白名单管理")
public class SomAmazonVcPriceWhiteController extends BasicController {

    @Resource
    SomAmazonVcPriceWhiteService somAmazonVcPriceWhiteService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonVcPriceWhiteVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomAmazonVcPriceWhiteVo>> queryByPage(@RequestBody SomAmazonVcPriceWhitePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonVcPriceWhiteService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somAmazonVcPriceWhiteVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated SomAmazonVcPriceWhiteVo somAmazonVcPriceWhiteVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somAmazonVcPriceWhiteService.save(somAmazonVcPriceWhiteVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somAmazonVcPriceWhiteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody SomAmazonVcPriceWhiteVo somAmazonVcPriceWhiteVo) throws ValidateException {
        somAmazonVcPriceWhiteService.delete(somAmazonVcPriceWhiteVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonVcPriceWhitePageSearchVo searchVo) {
        String data = somAmazonVcPriceWhiteService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
