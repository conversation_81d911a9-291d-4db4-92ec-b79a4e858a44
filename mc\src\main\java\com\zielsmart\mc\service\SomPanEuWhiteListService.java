package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomPanEuWhiteList;
import com.zielsmart.mc.repository.mapper.SomPanEuWhiteListMapper;
import com.zielsmart.mc.vo.SomPanEuWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomPanEuWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title SomPanEuWhiteListService
 * @description
 * @date 2023-12-13 14:12:36
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomPanEuWhiteListService {

    @Resource
    private SomPanEuWhiteListMapper somPanEuWhiteListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link PageVo< SomPanEuWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomPanEuWhiteListVo> queryByPage(SomPanEuWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomPanEuWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomPanEuWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomPanEuWhiteListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somPanEuWhiteListVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomPanEuWhiteListVo somPanEuWhiteListVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somPanEuWhiteListVo) || StrUtil.isBlank(somPanEuWhiteListVo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        long count = somPanEuWhiteListMapper.createLambdaQuery().andEq("seller_sku", somPanEuWhiteListVo.getSellerSku()).count();
        if (count > 0) {
            throw new ValidateException("该展示码已经存在白名单，不允许新增");
        }
        somPanEuWhiteListVo.setAid(IdUtil.fastSimpleUUID());
        somPanEuWhiteListVo.setCreateNum(tokenUser.getJobNumber());
        somPanEuWhiteListVo.setCreateName(tokenUser.getUserName());
        somPanEuWhiteListVo.setCreateTime(DateTime.now().toJdkDate());
        somPanEuWhiteListMapper.insert(ConvertUtils.beanConvert(somPanEuWhiteListVo, SomPanEuWhiteList.class));
    }

    /**
     * delete
     * 删除
     *
     * @param somPanEuWhiteListVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomPanEuWhiteListVo somPanEuWhiteListVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somPanEuWhiteListVo)) {
            throw new ValidateException("请选择要删除的数据");
        }

        // somPanEuWhiteListMapper.createLambdaQuery().andIn("aid", somPanEuWhiteListVo.getAidList()).delete();
        somPanEuWhiteListMapper.createLambdaQuery().andEq("aid",somPanEuWhiteListVo.getAid()).delete();
    }

    /**
     * 导入数据
     *
     * @param list
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomPanEuWhiteListVo> list, TokenUserInfo tokenUser) throws ValidateException {
        if (CollectionUtil.isNotEmpty(list)) {
            Set<String> skuSet = new HashSet<>();
            List<String> duplicateList = new ArrayList<>();
            for (SomPanEuWhiteListVo vo : list) {
                if (StrUtil.isBlank(vo.getSellerSku())) {
                    throw new ValidateException("展示码不允许为空，请检查导入的文件");
                }
                if (!skuSet.add(vo.getSellerSku())) {
                    duplicateList.add(vo.getSellerSku());
                }
            }
            if (CollectionUtil.isNotEmpty(duplicateList)) {
                throw new ValidateException("展示码" + duplicateList + "重复，请检查后重新导入");
            }
            List<String> skuList = list.stream().map(SomPanEuWhiteListVo::getSellerSku).distinct().collect(Collectors.toList());
            List<String> existSkuList = new ArrayList<>();
            List<SomPanEuWhiteList> voList = somPanEuWhiteListMapper.all();
            if (ObjectUtil.isNotEmpty(voList)) {
                existSkuList = voList.stream().map(SomPanEuWhiteList::getSellerSku).distinct().collect(Collectors.toList());
            }
            skuList.retainAll(existSkuList);
            if (CollectionUtil.isNotEmpty(skuList)) {
                throw new ValidateException("展示码" + skuList + "已经存在白名单，不允许重复添加");
            }
            list.stream().forEach(f -> {
                f.setAid(IdUtil.fastSimpleUUID());
                f.setCreateNum(tokenUser.getJobNumber());
                f.setCreateName(tokenUser.getUserName());
                f.setCreateTime(DateTime.now().toJdkDate());
            });
            somPanEuWhiteListMapper.insertBatch(ConvertUtils.listConvert(list, SomPanEuWhiteList.class));
        }
    }
}
