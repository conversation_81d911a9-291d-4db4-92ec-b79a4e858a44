package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McPaneuWarehouseConfigService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.mc.vo.role.McRoleResourceVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.zielsmart.web.basic.BasicController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McPaneuWarehouseConfigController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/mcPaneuWarehouseConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "PanEu站点可售仓库映射表管理")
public class McPaneuWarehouseConfigController extends BasicController{

    @Resource
    McPaneuWarehouseConfigService mcPaneuWarehouseConfigService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McPaneuWarehouseConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<McPaneuWarehouseConfigVo>> queryByPage(@RequestBody McPaneuWarehouseConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcPaneuWarehouseConfigService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcPaneuWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated McPaneuWarehouseConfigVo mcPaneuWarehouseConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcPaneuWarehouseConfigService.save(mcPaneuWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param mcPaneuWarehouseConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody McPaneuWarehouseConfigVo mcPaneuWarehouseConfigVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcPaneuWarehouseConfigService.update(mcPaneuWarehouseConfigVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcPaneuWarehouseConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody McPaneuWarehouseConfigVo mcPaneuWarehouseConfigVo) throws ValidateException {
        mcPaneuWarehouseConfigService.delete(mcPaneuWarehouseConfigVo);
        return ResultVo.ofSuccess(null);
    }





    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link ResultVo< PageVo<  McRoleResourceVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "库存查询")
    @PostMapping(value = "/inventory-query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McSaleInventoryReportVo>> inventoryQueryByPage(@RequestBody McSaleInventoryReportSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcPaneuWarehouseConfigService.inventoryQueryByPage(searchVo));
    }

    /**
     * batchPutUp
     * 批量上架
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量上架")
    @PostMapping(value = "/batch-put-up")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutUp(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcPaneuWarehouseConfigService.batchPutUp(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * batchPutDown
     * 批量下架
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量下架")
    @PostMapping(value = "/batch-put-down")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchPutDown(@RequestBody McSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        mcPaneuWarehouseConfigService.batchPutDown(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "导出可售库存报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportDearanceProduct(@RequestBody McSaleInventoryReportSearchVo searchVo) throws ValidateException{
        String data = mcPaneuWarehouseConfigService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
