package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomAliExpressWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomAliExpressWarehouseConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2024-11-05
*/

@SqlResource("somAliExpressWarehouseConfig")
public interface SomAliExpressWarehouseConfigMapper extends BaseMapper<SomAliExpressWarehouseConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomAliExpressWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomAliExpressWarehouseConfigVo> queryByPage(@Param("searchVo")SomAliExpressWarehouseConfigPageSearchVo searchVo, PageRequest pageRequest);
}
