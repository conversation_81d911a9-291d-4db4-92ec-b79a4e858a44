package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.SomThreePlatformPrice;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.SomThreePlatformPriceMapper;
import com.zielsmart.mc.vo.SomAliExpressWarehouseConfigVo;
import com.zielsmart.mc.vo.SomThreePlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomThreePlatformPriceVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.Base64;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-11-13 13:58:16
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomThreePlatformPriceService {
    
    @Resource
    private SomThreePlatformPriceMapper somThreePlatformPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomThreePlatformPriceVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomThreePlatformPriceVo> queryByPage(SomThreePlatformPricePageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //TODO 自行修改SQL条件查询
        PageResult<SomThreePlatformPriceVo> pageResult = dynamicSqlManager.getMapper(SomThreePlatformPriceMapper.class).queryByPage(searchVo, pageRequest);

        List<McDictionaryInfo> dictionaryInfoList = dynamicSqlManager
                .getMapper(McDictionaryInfoMapper.class)
                .createLambdaQuery().andEq("item_type_code", "ThreePlatformPricePushStatus")
                .select();

        for (SomThreePlatformPriceVo config : pageResult.getList()) {
            if (ObjectUtil.isNotEmpty(config.getSyncStatus())){
                dictionaryInfoList.stream()
                        .filter(t -> t.getItemValue().equals(config.getSyncStatus().toString()))
                        .findFirst().ifPresent(ps -> {
                            config.setSyncStatusShow(ps.getItemLable());
                        });
            }
        }

        return ConvertUtils.pageConvert(pageResult, SomThreePlatformPriceVo.class, searchVo);
    }

    /**
     * save
     * 添加
     * @param somThreePlatformPriceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomThreePlatformPriceVo somThreePlatformPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somThreePlatformPriceVo)) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        somThreePlatformPriceVo.setAid(IdUtil.fastSimpleUUID());
        //TODO 根据情况判断是否需要添加创建人信息
        somThreePlatformPriceMapper.insert(ConvertUtils.beanConvert(somThreePlatformPriceVo, SomThreePlatformPrice.class));
    }

    /**
     * update
     * 修改
     * @param somThreePlatformPriceVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void update(SomThreePlatformPriceVo somThreePlatformPriceVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somThreePlatformPriceVo) || StrUtil.isEmpty(somThreePlatformPriceVo.getAid())) {
            throw new ValidateException("Aid不能为空");
        }
        //TODO 根据情况判断是否需要设置修改人信息
        somThreePlatformPriceMapper.createLambdaQuery()
                .andEq("aid",somThreePlatformPriceVo.getAid())
                .updateSelective(ConvertUtils.beanConvert(somThreePlatformPriceVo, SomThreePlatformPrice.class));
    }

    /**
     * delete
     * 删除
     * @param somThreePlatformPriceVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomThreePlatformPriceVo somThreePlatformPriceVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somThreePlatformPriceVo)) {
            throw new ValidateException("请选择要删除的数据");
        }
        //TODO 是否使用批量删除
        // somThreePlatformPriceMapper.createLambdaQuery().andIn("aid", somThreePlatformPriceVo.getAidList()).delete();
        somThreePlatformPriceMapper.createLambdaQuery().andEq("aid",somThreePlatformPriceVo.getAid()).delete();
    }

    public String export(SomThreePlatformPricePageSearchVo searchVo) {
            searchVo.setCurrent(1);
            searchVo.setPageSize(Integer.MAX_VALUE);
            List<SomThreePlatformPriceVo> records = queryByPage(searchVo).getRecords();
            if (!records.isEmpty()) {
                Workbook workbook = null;
                try {
                    ExportParams params = new ExportParams(null, "三方平台价格推送表管理");
                    params.setType(ExcelType.XSSF);
                    params.setAutoSize(true);
                    workbook = ExcelExportUtil.exportExcel(params, SomThreePlatformPriceVo.class, records);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    workbook.write(bos);
                    byte[] barray = bos.toByteArray();
                    return Base64.getEncoder().encodeToString(barray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }
}
