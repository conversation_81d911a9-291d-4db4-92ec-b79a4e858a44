package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomAliExpressWarehouseConfigService;
import com.zielsmart.mc.vo.SomSheinWarehouseInfoVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.Resource;
import java.util.List;

public class SomAliExpressWarehouseConfig {

    @Resource
    SomAliExpressWarehouseConfigService somAliExpressWarehouseConfigService;



}
