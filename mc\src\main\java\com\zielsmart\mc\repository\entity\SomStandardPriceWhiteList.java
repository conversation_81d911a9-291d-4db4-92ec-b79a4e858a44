package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* 定价白名单表
* gen by 代码生成器 2022-05-17
*/

@Table(name="mc.som_standard_price_white_list")
public class SomStandardPriceWhiteList implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;

	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * 发货方式
	 */
	@Column("fulfillment_channel")
	private Integer fulfillmentChannel ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime;

	/**
	 * 平台
	 */
	@Column("platform")
	private String platform;

	/**
	 * 站点
	 */
	@Column("site")
	private String site;

	@Column("seller_sku")
	private String sellerSku;

	/**
	 * 生效开始日期
	 */
	@Column("start_date")
	private Date startDate;

	/**
	 * 生效结束日期
	 */
	@Column("end_date")
	private Date endDate;


	public SomStandardPriceWhiteList() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}

	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* 发货方式
	*@return
	*/
	public Integer getFulfillmentChannel(){
		return  fulfillmentChannel;
	}
	/**
	* 发货方式
	*@param  fulfillmentChannel
	*/
	public void setFulfillmentChannel(Integer fulfillmentChannel ){
		this.fulfillmentChannel = fulfillmentChannel;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

	/**
	 * 平台
	 *
	 * @return
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 平台
	 *
	 * @param platform
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 站点
	 *
	 * @return
	 */
	public String getSite() {
		return site;
	}

	/**
	 * 站点
	 *
	 * @param site
	 */
	public void setSite(String site) {
		this.site = site;
	}

	/**
	 * 卖家SKU
	 *
	 * @return
	 */
	public String getSellerSku() {
		return sellerSku;
	}

	/**
	 * 卖家SKU
	 *
	 * @param sellerSku
	 */
	public void setSellerSku(String sellerSku) {
		this.sellerSku = sellerSku;
	}

	/**
	 * 开始日期
	 *
	 * @return
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * 开始日期
	 *
	 * @param startDate
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * 结束日期
	 *
	 * @return
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * 结束日期
	 *
	 * @param endDate
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
}
