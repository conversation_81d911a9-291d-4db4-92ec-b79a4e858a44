package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomCouponService;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomCouponController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somCoupon", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "coupon活动管理")
public class SomCouponController extends BasicController {

    @Resource
    SomCouponService somCouponService;

    /**
     * addOrClone
     * 新增或克隆
     *
     * @param addVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增或克隆")
    @PostMapping(value = "/addOrClone")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrClone(@RequestBody @Validated SomCouponExVo addVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somCouponService.addOrClone(addVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }


    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.web.basic.vo.PageVo< SomCouponExVo >>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomCouponExVo>> queryByPage(@RequestBody SomCouponPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somCouponService.queryByPage(searchVo));
    }

    /**
     * queryByAid
     * 查看
     *
     * @param queryVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo< SomCouponExVo >}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomCouponExVo> queryByAid(@RequestBody SomCouponVo queryVo) throws ValidateException {
        return ResultVo.ofSuccess(somCouponService.queryByAid(queryVo));
    }

    /**
     * downloadExcel
     * 下载导入模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download")
    public String downloadExcel() {
        return "forward:/static/excel/CouponTemplate.xlsx";
    }

    /**
     * import
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 起始行数
        importParams.setStartRows(1);
        // 导入字段
        String[] arr = {"站点", "展示码", "Coupon Title", "活动开始时间", "活动截止时间", "每位顾客只能兑换一份", "Coupon类型","折扣类型", "折扣比例", "折扣金额", "预算金额", "目标客户","品牌","品牌受众", "预计活动费用", "提报毛利率", "提报原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomCouponImportVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomCouponImportVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somCouponService.importExcel(result.getList(), tokenUser);
        if (str.isEmpty()) {
            return ResultVo.ofSuccess();
        } else {
            return ResultVo.ofFail(str);
        }
    }

    /**
     * exportExcel
     * 导出
     *
     * @param exportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @ResponseBody
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> exportExcel(@RequestBody SomCouponPageSearchVo exportVo) throws ValidateException{
        String data = somCouponService.exportExcel(exportVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * edit
     * 编辑
     *
     * @param editVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> edit(@RequestBody SomCouponExVo editVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somCouponService.edit(editVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomCouponVo deleteVo) throws ValidateException {
        somCouponService.delete(deleteVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * submit
     * 提报&批量提交
     *
     * @param submitVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "提报&批量提交")
    @PostMapping(value = "/submit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> submit(@RequestBody SomCouponExVo submitVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somCouponService.submit(submitVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * cancel
     * 取消活动
     *
     * @param cancelVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "取消")
    @PostMapping(value = "/cancel")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> cancel(@RequestBody SomCouponImportVo cancelVo) throws ValidateException {
        somCouponService.cancel(cancelVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * finish
     * 结束活动
     *
     * @param finishVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "结束")
    @PostMapping(value = "/finish")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> finish(@RequestBody SomCouponImportVo finishVo) throws ValidateException {
        somCouponService.finish(finishVo);
        return ResultVo.ofSuccess();
    }

    /**
     * feedbackResult
     * 反馈提报结果
     *
     * @param feedbackVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "反馈提报结果")
    @PostMapping(value = "/feedbackResult")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> feedbackResult(@RequestBody SomCouponVo feedbackVo) throws ValidateException {
        somCouponService.feedbackResult(feedbackVo);
        return ResultVo.ofSuccess();
    }

    /**
     * downloadExcel
     * 下载批量反馈提报结果模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载批量反馈提报结果模板")
    @GetMapping(value = "/downloadBatchTemplate")
    public String downloadBatchExcel() {
        return "forward:/static/excel/CouponBatchTemplate.xlsx";
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param file
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量反馈提报结果")
    @PostMapping(value = "/batchFeedbackResult", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchFeedbackResult(@RequestParam("file") MultipartFile file) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"站点", "Coupon Title", "提报结果", "实际活动起始时间", "实际活动截止时间", "提报失败原因"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomCouponFeedBackVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomCouponFeedBackVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        String str = somCouponService.batchFeedbackResult(result.getList());
        if(StrUtil.isBlank(str)){
            return ResultVo.ofSuccess();
        }else {
            String s = Strings.EMPTY;
            if(str.contains("%")){
                 s = str.replace("%", "%%");
            }else {
                s = str;
            }
            return ResultVo.ofFail(s);
        }
    }

    /**
     * mark
     * 标记活动状态
     *
     * @param markVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记活动状态")
    @PostMapping(value = "/mark")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> mark(@RequestBody SomCouponVo markVo) throws ValidateException {
        somCouponService.mark(markVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * summited
     * 标记为已提报
     * @param summitedVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "标记为已提报")
    @PostMapping(value = "/submited")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> summited(@RequestBody SomCouponExVo summitedVo) throws ValidateException {
        somCouponService.summited(summitedVo);
        return ResultVo.ofSuccess(null);
    }
}
