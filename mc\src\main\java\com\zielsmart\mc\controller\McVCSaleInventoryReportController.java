package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McVCSaleInventoryReportService;
import com.zielsmart.mc.vo.McVCSaleInventoryReportSearchVo;
import com.zielsmart.mc.vo.McVCSaleInventoryReportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/vc-sale-inventory-report", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC可售库存报表")
public class McVCSaleInventoryReportController extends BasicController {

    @Resource
    private McVCSaleInventoryReportService service;

    /**
     * query
     * 查询VC可售库存报表
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.McWayfairSaleInventoryReportVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/query")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<McVCSaleInventoryReportVo>> query(@RequestBody McVCSaleInventoryReportSearchVo searchVo) {
        return ResultVo.ofSuccess(service.queryVCSaleInventoryReport(searchVo));
    }

    /**
     * export
     * 导出VC可售库存报表
     *
     * @param searchVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody McVCSaleInventoryReportSearchVo searchVo) throws ValidateException {
        String data = service.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    /**
     * batchUpdate
     * 批量上/下架
     *
     * @param saleInventoryReportVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "批量上/下架")
    @PostMapping(value = "/batchUpdate")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> batchUpdate(@RequestBody McVCSaleInventoryReportVo saleInventoryReportVo) throws ValidateException {
        service.batchUpdate(saleInventoryReportVo);
        return ResultVo.ofSuccess(null);
    }
}
