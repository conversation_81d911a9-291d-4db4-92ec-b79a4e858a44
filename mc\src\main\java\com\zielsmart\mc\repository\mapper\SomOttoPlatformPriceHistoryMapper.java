package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomOttoPlatformPriceHistory;
import com.zielsmart.mc.vo.SomOttoPlatformPriceHistoryPageSearchVo;
import com.zielsmart.mc.vo.SomOttoPlatformPriceHistoryVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description OTTO平台价调整历史表
 * @date 2025-04-01 15:50:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somOttoPlatformPriceHistory")
public interface SomOttoPlatformPriceHistoryMapper extends BaseMapper<SomOttoPlatformPriceHistory> {

    /**
     * 分页查询
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return SomOttoPlatformPriceHistoryVo
     */
    PageResult<SomOttoPlatformPriceHistoryVo> queryByPage(SomOttoPlatformPriceHistoryPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 查询最近成功的调价记录
     *
     * @param site 站点
     * @param sellerSku 展示码
     * @param adjustStatus 调价状态
     * @return SomOttoPlatformPriceHistoryVo
     */
    SomOttoPlatformPriceHistoryVo queryLatestSuccess(@Param("site") String site, @Param("sellerSku") String sellerSku, @Param("adjustStatus") Integer adjustStatus);
}
