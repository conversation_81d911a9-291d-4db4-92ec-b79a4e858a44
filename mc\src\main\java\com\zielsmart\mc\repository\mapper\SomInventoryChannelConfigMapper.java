package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomInventoryChannelConfigPageSearchVo;
import com.zielsmart.mc.vo.SomInventoryChannelConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2023-09-01
*/

@SqlResource("somInventoryChannelConfig")
public interface SomInventoryChannelConfigMapper extends BaseMapper<SomInventoryChannelConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomInventoryChannelConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomInventoryChannelConfigVo> queryByPage(@Param("searchVo")SomInventoryChannelConfigPageSearchVo searchVo, PageRequest pageRequest);
}
