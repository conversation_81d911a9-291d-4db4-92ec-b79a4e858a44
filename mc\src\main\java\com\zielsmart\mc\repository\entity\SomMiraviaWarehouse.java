package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * Miravia仓库表
 * gen by 代码生成器 2024-04-29
 */

@Table(name = "mc.som_miravia_warehouse")
public class SomMiraviaWarehouse implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 仓库名称
     */
    @Column("warehouse_name")
    private String warehouseName;
    /**
     * 仓库编码
     */
    @Column("warehouse_code")
    private String warehouseCode;
    /**
     * 状态
     */
    @Column("status")
    private String status;
    /**
     * 下载时间
     */
    @Column("download_time")
    private Date downloadTime;

    public SomMiraviaWarehouse() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 仓库名称
     *
     * @return
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 仓库名称
     *
     * @param warehouseName
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * 仓库编码
     *
     * @return
     */
    public String getWarehouseCode() {
        return warehouseCode;
    }

    /**
     * 仓库编码
     *
     * @param warehouseCode
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * 状态
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 下载时间
     *
     * @return
     */
    public Date getDownloadTime() {
        return downloadTime;
    }

    /**
     * 下载时间
     *
     * @param downloadTime
     */
    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

}
