package com.zielsmart.mc.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTemuWarehouse;
import com.zielsmart.mc.repository.mapper.SomTemuWarehouseMapper;
import com.zielsmart.mc.vo.SomTemuWarehouseQueryVo;
import com.zielsmart.mc.vo.SomTemuWarehouseVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SomTemuWarehouseService {

    @Resource
    private SomTemuWarehouseMapper somTemuWarehouseMapper;

    /**
     * Temu 平台仓库查询
     *
     * @param queryVo 入参
     * @return List<SomTemuWarehouseVo>
     */
    public List<SomTemuWarehouseVo> queryWarehouse(SomTemuWarehouseQueryVo queryVo) throws ValidateException {
        if (ObjectUtil.isEmpty(queryVo) || StrUtil.isEmpty(queryVo.getAccountId()) || StrUtil.isEmpty(queryVo.getSite())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String accountId = queryVo.getAccountId();
        String site = queryVo.getSite();
        List<SomTemuWarehouse> temuWarehouses = somTemuWarehouseMapper.createLambdaQuery()
                .andEq("account_id", accountId)
                .andEq("site", site)
                .select();
        return ConvertUtils.listConvert(temuWarehouses, SomTemuWarehouseVo.class);
    }
}
