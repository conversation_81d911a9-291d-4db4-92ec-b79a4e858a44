package com.zielsmart.mc.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/*
 * Temu本本营销活动明细的VO实体对象
 * gen by 代码生成器 2025-03-12
 */

@Data
@Schema(title = "Temu本本营销活动明细", name = "SomTemuLocalPromotionDetailVo")
public class SomTemuLocalPromotionDetailVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "店铺名称", name = "accountName")
    @Excel(name = "店铺名称")
    private String accountName;

    @Schema(description = "店铺ID", name = "accountId")
    @Excel(name = "店铺ID")
    private String accountId;

    @Schema(description = "站点", name = "site")
    @Excel(name = "站点")
    private String site;

    @Schema(description = "营销活动名称", name = "activityName")
    @Excel(name = "营销活动名称")
    private String activityName;

    @Schema(description = "活动类型描述", name = "activityTypeDesc")
    @Excel(name = "活动类型")
    private String activityTypeDesc;

    @Schema(description = "活动起止时间", name = "activityTime")
    @Excel(name = "活动起止时间")
    private String activityTime;

    @Schema(description = "活动状态描述：1. 未开始 2. 运行中 3. 已结束", name = "activityStatusDesc")
    @Excel(name = "活动状态")
    private String activityStatusDesc;

    @Schema(description = "类型", name = "type")
    @Excel(name = "类型")
    private String type;

    @Schema(description = "营销活动ID", name = "activityId")
    private String activityId;

    @Schema(description = "产品ID", name = "goodsId")
    @Excel(name = "商品ID")
    private String goodsId;

    @Schema(description = "SKU ID", name = "skuId")
    @Excel(name = "SKU ID")
    private String skuId;

    @Schema(description = "展示码", name = "sellerSku")
    @Excel(name = "展示码")
    private String sellerSku;

    @Schema(description = "提报数量/推荐数量", name = "activityQuantity")
    @Excel(name = "提报数量/推荐数量")
    private Integer activityQuantity;

    @Schema(description = "剩余库存", name = "remainingActivityQuantity")
    @Excel(name = "剩余库存")
    private Integer remainingActivityQuantity;

    @Schema(description = "日常供货价", name = "dailySupplyPrice")
    @Excel(name = "日常供货价")
    private BigDecimal dailySupplyPrice;


    @Schema(description = "活动供货价/推荐供货价", name = "activitySupplierPrice")
    @Excel(name = "活动供货价/推荐供货价")
    private BigDecimal activitySupplierPrice;

    @Schema(description = "创建时间", name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;




}
