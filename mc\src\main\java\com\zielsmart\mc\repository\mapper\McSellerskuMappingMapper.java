package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McSellerskuMappingPageSearchVo;
import com.zielsmart.mc.vo.McSellerskuMappingVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-10-26
*/

@SqlResource("mcSellerskuMapping")
public interface McSellerskuMappingMapper extends BaseMapper<McSellerskuMapping> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McSellerskuMappingVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McSellerskuMappingVo> queryByPage(@Param("searchVo")McSellerskuMappingPageSearchVo searchVo, PageRequest pageRequest);
}
