package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* Temu本本sku
* gen by 代码生成器 2025-06-26
*/

@Table(name="mc.som_temu_local_sku")
public class SomTemuLocalSku implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 账号
	 */
	@Column("account_tag")
	private String accountTag ;
	/**
	 * 店铺ID
	 */
	@Column("account_id")
	private String accountId ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 商品ID
	 */
	@Column("goods_id")
	private Long goodsId ;
	/**
	 * 商品名称
	 */
	@Column("goods_name")
	private String goodsName ;
	/**
	 * SKU ID
	 */
	@Column("sku_id")
	private Long skuId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 产品的规格或类型
	 */
	@Column("spec_name")
	private String specName ;
	/**
	 * 产品缩略图URL
	 */
	@Column("thumb_url")
	private String thumbUrl ;
	/**
	 * 产品的状态（例如，1表示销售，4表示未发布，等等）
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 产品的子状态（具体含义取决于业务逻辑）
	 */
	@Column("sub_status")
	private Integer subStatus ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 产品的销售价格或零售价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 产品的销售价格或零售价格
	 */
	@Column("retail_price")
	private Object retailPrice ;
	/**
	 * 产品的库存数量或库存水平
	 */
	@Column("stock")
	private Integer stock ;
	/**
	 * 产品的创建时间
	 */
	@Column("crt_time")
	private Date crtTime ;
	/**
	 * 产品可用性状态
	 */
	@Column("goods_is_on_sale")
	private Integer goodsIsOnSale ;
	/**
	 * SKU状态变更时间
	 */
	@Column("sku_status_change_time")
	private Date skuStatusChangeTime ;
	/**
	 * 产品子状态过滤器
	 */
	@Column("sku_show_sub_status")
	private Integer skuShowSubStatus ;
	/**
	 * 产品体积
	 */
	@Column("volume_info")
	private Object volumeInfo ;
	/**
	 * 产品重量
	 */
	@Column("weight_info")
	private Object weightInfo ;
	/**
	 * 规范列表
	 */
	@Column("spec_list")
	private Object specList ;
	/**
	 * 低流量标签：1-低流量，2-不低流量
	 */
	@Column("low_traffic_tag")
	private Integer lowTrafficTag ;
	/**
	 * 限制流量标签：1-限制流量，2-非限制流量
	 */
	@Column("restricted_traffic_tag")
	private Integer restrictedTrafficTag ;
	/**
	 * 调用接口下载的时间
	 */
	@Column("download_time")
	private Date downloadTime ;

	public SomTemuLocalSku() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 账号
	*@return
	*/
	public String getAccountTag(){
		return  accountTag;
	}
	/**
	* 账号
	*@param  accountTag
	*/
	public void setAccountTag(String accountTag ){
		this.accountTag = accountTag;
	}
	/**
	* 店铺ID
	*@return
	*/
	public String getAccountId(){
		return  accountId;
	}
	/**
	* 店铺ID
	*@param  accountId
	*/
	public void setAccountId(String accountId ){
		this.accountId = accountId;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 商品ID
	*@return
	*/
	public Long getGoodsId(){
		return  goodsId;
	}
	/**
	* 商品ID
	*@param  goodsId
	*/
	public void setGoodsId(Long goodsId ){
		this.goodsId = goodsId;
	}
	/**
	* 商品名称
	*@return
	*/
	public String getGoodsName(){
		return  goodsName;
	}
	/**
	* 商品名称
	*@param  goodsName
	*/
	public void setGoodsName(String goodsName ){
		this.goodsName = goodsName;
	}
	/**
	* SKU ID
	*@return
	*/
	public Long getSkuId(){
		return  skuId;
	}
	/**
	* SKU ID
	*@param  skuId
	*/
	public void setSkuId(Long skuId ){
		this.skuId = skuId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 产品的规格或类型
	*@return
	*/
	public String getSpecName(){
		return  specName;
	}
	/**
	* 产品的规格或类型
	*@param  specName
	*/
	public void setSpecName(String specName ){
		this.specName = specName;
	}
	/**
	* 产品缩略图URL
	*@return
	*/
	public String getThumbUrl(){
		return  thumbUrl;
	}
	/**
	* 产品缩略图URL
	*@param  thumbUrl
	*/
	public void setThumbUrl(String thumbUrl ){
		this.thumbUrl = thumbUrl;
	}
	/**
	* 产品的状态（例如，1表示销售，4表示未发布，等等）
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 产品的状态（例如，1表示销售，4表示未发布，等等）
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 产品的子状态（具体含义取决于业务逻辑）
	*@return
	*/
	public Integer getSubStatus(){
		return  subStatus;
	}
	/**
	* 产品的子状态（具体含义取决于业务逻辑）
	*@param  subStatus
	*/
	public void setSubStatus(Integer subStatus ){
		this.subStatus = subStatus;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 产品的销售价格或零售价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 产品的销售价格或零售价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 产品的销售价格或零售价格
	*@return
	*/
	public Object getRetailPrice(){
		return  retailPrice;
	}
	/**
	* 产品的销售价格或零售价格
	*@param  retailPrice
	*/
	public void setRetailPrice(Object retailPrice ){
		this.retailPrice = retailPrice;
	}
	/**
	* 产品的库存数量或库存水平
	*@return
	*/
	public Integer getStock(){
		return  stock;
	}
	/**
	* 产品的库存数量或库存水平
	*@param  stock
	*/
	public void setStock(Integer stock ){
		this.stock = stock;
	}
	/**
	* 产品的创建时间
	*@return
	*/
	public Date getCrtTime(){
		return  crtTime;
	}
	/**
	* 产品的创建时间
	*@param  crtTime
	*/
	public void setCrtTime(Date crtTime ){
		this.crtTime = crtTime;
	}
	/**
	* 产品可用性状态
	*@return
	*/
	public Integer getGoodsIsOnSale(){
		return  goodsIsOnSale;
	}
	/**
	* 产品可用性状态
	*@param  goodsIsOnSale
	*/
	public void setGoodsIsOnSale(Integer goodsIsOnSale ){
		this.goodsIsOnSale = goodsIsOnSale;
	}
	/**
	* SKU状态变更时间
	*@return
	*/
	public Date getSkuStatusChangeTime(){
		return  skuStatusChangeTime;
	}
	/**
	* SKU状态变更时间
	*@param  skuStatusChangeTime
	*/
	public void setSkuStatusChangeTime(Date skuStatusChangeTime ){
		this.skuStatusChangeTime = skuStatusChangeTime;
	}
	/**
	* 产品子状态过滤器
	*@return
	*/
	public Integer getSkuShowSubStatus(){
		return  skuShowSubStatus;
	}
	/**
	* 产品子状态过滤器
	*@param  skuShowSubStatus
	*/
	public void setSkuShowSubStatus(Integer skuShowSubStatus ){
		this.skuShowSubStatus = skuShowSubStatus;
	}
	/**
	* 产品体积
	*@return
	*/
	public Object getVolumeInfo(){
		return  volumeInfo;
	}
	/**
	* 产品体积
	*@param  volumeInfo
	*/
	public void setVolumeInfo(Object volumeInfo ){
		this.volumeInfo = volumeInfo;
	}
	/**
	* 产品重量
	*@return
	*/
	public Object getWeightInfo(){
		return  weightInfo;
	}
	/**
	* 产品重量
	*@param  weightInfo
	*/
	public void setWeightInfo(Object weightInfo ){
		this.weightInfo = weightInfo;
	}
	/**
	* 规范列表
	*@return
	*/
	public Object getSpecList(){
		return  specList;
	}
	/**
	* 规范列表
	*@param  specList
	*/
	public void setSpecList(Object specList ){
		this.specList = specList;
	}
	/**
	* 低流量标签：1-低流量，2-不低流量
	*@return
	*/
	public Integer getLowTrafficTag(){
		return  lowTrafficTag;
	}
	/**
	* 低流量标签：1-低流量，2-不低流量
	*@param  lowTrafficTag
	*/
	public void setLowTrafficTag(Integer lowTrafficTag ){
		this.lowTrafficTag = lowTrafficTag;
	}
	/**
	* 限制流量标签：1-限制流量，2-非限制流量
	*@return
	*/
	public Integer getRestrictedTrafficTag(){
		return  restrictedTrafficTag;
	}
	/**
	* 限制流量标签：1-限制流量，2-非限制流量
	*@param  restrictedTrafficTag
	*/
	public void setRestrictedTrafficTag(Integer restrictedTrafficTag ){
		this.restrictedTrafficTag = restrictedTrafficTag;
	}
	/**
	* 调用接口下载的时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 调用接口下载的时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}

}
