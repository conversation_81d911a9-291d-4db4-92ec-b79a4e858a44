package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.util.Date;
/*
* 
* gen by 代码生成器 2023-11-06
*/

@Table(name="mc.som_mercadolibre_listing")
public class SomMercadolibreListing implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 产品ID
	 */
	@Column("listing_id")
	private String listingId ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 站点ID
	 */
	@Column("site_id")
	private String siteId ;
	/**
	 * 卖家ID
	 */
	@Column("seller_id")
	private String sellerId ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 币种
	 */
	@Column("currency_id")
	private String currencyId ;
	/**
	 * 可售库存
	 */
	@Column("available_quantity")
	private Integer availableQuantity ;
	/**
	 * 缩略图URL
	 */
	@Column("thumbnail")
	private String thumbnail ;
	/**
	 * 属性
	 */
	@Column("attributes")
	private String attributes ;
	/**
	 * 下载时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 变体Id
	 */
	@Column("variations_id")
	private String variationsId ;

	public SomMercadolibreListing() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 产品ID
	*@return
	*/
	public String getListingId(){
		return  listingId;
	}
	/**
	* 产品ID
	*@param  listingId
	*/
	public void setListingId(String listingId ){
		this.listingId = listingId;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 站点ID
	*@return
	*/
	public String getSiteId(){
		return  siteId;
	}
	/**
	* 站点ID
	*@param  siteId
	*/
	public void setSiteId(String siteId ){
		this.siteId = siteId;
	}
	/**
	* 卖家ID
	*@return
	*/
	public String getSellerId(){
		return  sellerId;
	}
	/**
	* 卖家ID
	*@param  sellerId
	*/
	public void setSellerId(String sellerId ){
		this.sellerId = sellerId;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrencyId(){
		return  currencyId;
	}
	/**
	* 币种
	*@param  currencyId
	*/
	public void setCurrencyId(String currencyId ){
		this.currencyId = currencyId;
	}
	/**
	* 可售库存
	*@return
	*/
	public Integer getAvailableQuantity(){
		return  availableQuantity;
	}
	/**
	* 可售库存
	*@param  availableQuantity
	*/
	public void setAvailableQuantity(Integer availableQuantity ){
		this.availableQuantity = availableQuantity;
	}
	/**
	* 缩略图URL
	*@return
	*/
	public String getThumbnail(){
		return  thumbnail;
	}
	/**
	* 缩略图URL
	*@param  thumbnail
	*/
	public void setThumbnail(String thumbnail ){
		this.thumbnail = thumbnail;
	}
	/**
	* 属性
	*@return
	*/
	public String getAttributes(){
		return  attributes;
	}
	/**
	* 属性
	*@param  attributes
	*/
	public void setAttributes(String attributes ){
		this.attributes = attributes;
	}
	/**
	* 下载时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 下载时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 变体Id
	*@return
	*/
	public String getVariationsId(){
		return  variationsId;
	}
	/**
	* 变体Id
	*@param  variationsId
	*/
	public void setVariationsId(String variationsId ){
		this.variationsId = variationsId;
	}

}
