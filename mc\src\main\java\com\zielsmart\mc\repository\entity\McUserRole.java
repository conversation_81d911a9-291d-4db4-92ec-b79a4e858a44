package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
/*
* 
* gen by 代码生成器 2021-07-16
*/

@Table(name="mc.mc_user_role")
public class McUserRole implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 用户工号
	 */
	@Column("user_code")
	private String userCode ;
	/**
	 * 角色主键
	 */
	@Column("role_id")
	private String roleId ;

	/**
	 * 用户主键
	 */
	@Column("user_aid")
	private String userAid ;

	public McUserRole() {
	}

	public String getUserAid() {
		return userAid;
	}

	public void setUserAid(String userAid) {
		this.userAid = userAid;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 用户工号
	*@return
	*/
	public String getUserCode(){
		return  userCode;
	}
	/**
	* 用户工号
	*@param  userCode
	*/
	public void setUserCode(String userCode ){
		this.userCode = userCode;
	}
	/**
	* 角色主键
	*@return
	*/
	public String getRoleId(){
		return  roleId;
	}
	/**
	* 角色主键
	*@param  roleId
	*/
	public void setRoleId(String roleId ){
		this.roleId = roleId;
	}

}
