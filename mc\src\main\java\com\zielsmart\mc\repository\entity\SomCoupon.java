package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.util.Date;
/*
* coupon活动主表
* gen by 代码生成器 2022-04-21
*/

@Table(name="mc.som_coupon")
public class SomCoupon implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 标题
	 */
	@Column("title")
	private String title ;
	/**
	 * 活动开始时间(提报)
	 */
	@Column("begin_date")
	private Date beginDate ;
	/**
	 * 活动结束时间(提报)
	 */
	@Column("end_date")
	private Date endDate ;
	/**
	 * 活动开始时间(实际)
	 */
	@Column("begin_date_real")
	private Date beginDateReal ;
	/**
	 * 活动结束时间(实际)
	 */
	@Column("end_date_real")
	private Date endDateReal ;
	/**
	 * 折扣类型 10.Money off 20.Percentage off
	 */
	@Column("coupon_type")
	private Integer couponType ;
	/**
	 * 折扣
	 */
	@Column("coupon_discount")
	private BigDecimal couponDiscount ;
	/**
	 * 折扣额
	 */
	@Column("coupon_discount_amount")
	private BigDecimal couponDiscountAmount ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 货币符号
	 */
	@Column("currency_symbol")
	private String currencySymbol ;
	/**
	 * 是否一个客户只能兑换一次 0.否 1.是
	 */
	@Column("only_exchange_one")
	private String onlyExchangeOne ;
	/**
	 * 预算金额
	 */
	@Column("budget")
	private Long budget ;
	/**
	 * 目标客户10.All customers 20.Amazon Prime members 30.Amazon Student members 40.Amazon Family members
	 */
	@Column("target_customer")
	private Integer targetCustomer ;
	/**
	 * 提报毛利率
	 */
	@Column("gross_margin")
	private Long grossMargin ;
	/**
	 * 状态 10.草稿 20.审核中 30.未通过 40.提报失败 50.未开始 60.进行中 70.结束中 71.结束失败 72.已结束 80.取消中 81.取消失败 82.取消成功 90审批通过 110 运营已提报
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 结束类型 10.正常结束 20.主动结束 30.超预算结束
	 */
	@Column("finish_type")
	private Integer finishType ;
	/**
	 * 结束原因
	 */
	@Column("finish_reason")
	private String finishReason ;
	/**
	 * 取消原因
	 */
	@Column("cancel_reason")
	private String cancelReason ;
	/**
	 * 提报原因
	 */
	@Column("reason")
	private String reason ;
	/**
	 * 失败原因
	 */
	@Column("failure_reason")
	private String failureReason ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;

	/**
	 * 结束时间
	 */
	@Column("finish_date")
	private Date finishDate ;
	/**
	 * 取消时间
	 */
	@Column("cancel_date")
	private Date cancelDate ;
	/**
	 * 提交时间
	 */
	@Column("submit_time")
	private Date submitTime;
	/**
	 * 审批时间
	 */
	@Column("audit_time")
	private Date auditTime;

	@Column("voucher_type")
	private Integer voucherType;
	@Column("brand")
	private Integer brand;
	@Column("brand_audience")
	private Integer brandAudience;

	/**
	 * 预计活动费用
	 */
	@Column("expected_activity_expense")
	private BigDecimal expectedActivityExpense;

	public SomCoupon() {
	}

	public BigDecimal getExpectedActivityExpense() {
		return expectedActivityExpense;
	}

	public void setExpectedActivityExpense(BigDecimal expectedActivityExpense) { this.expectedActivityExpense = expectedActivityExpense; }

	public Integer getVoucherType() {
		return voucherType;
	}

	public void setVoucherType(Integer voucherType) {
		this.voucherType = voucherType;
	}

	public Integer getBrand() {
		return brand;
	}

	public void setBrand(Integer brand) {
		this.brand = brand;
	}

	public Integer getBrandAudience() {
		return brandAudience;
	}

	public void setBrandAudience(Integer brandAudience) {
		this.brandAudience = brandAudience;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 平台
	*@return
	*/
	public String getPlatform(){
		return  platform;
	}
	/**
	* 平台
	*@param  platform
	*/
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 标题
	*@return
	*/
	public String getTitle(){
		return  title;
	}
	/**
	* 标题
	*@param  title
	*/
	public void setTitle(String title ){
		this.title = title;
	}
	/**
	* 活动开始时间(提报)
	*@return
	*/
	public Date getBeginDate(){
		return  beginDate;
	}
	/**
	* 活动开始时间(提报)
	*@param  beginDate
	*/
	public void setBeginDate(Date beginDate ){
		this.beginDate = beginDate;
	}
	/**
	* 活动结束时间(提报)
	*@return
	*/
	public Date getEndDate(){
		return  endDate;
	}
	/**
	* 活动结束时间(提报)
	*@param  endDate
	*/
	public void setEndDate(Date endDate ){
		this.endDate = endDate;
	}
	/**
	* 活动开始时间(实际)
	*@return
	*/
	public Date getBeginDateReal(){
		return  beginDateReal;
	}
	/**
	* 活动开始时间(实际)
	*@param  beginDateReal
	*/
	public void setBeginDateReal(Date beginDateReal ){
		this.beginDateReal = beginDateReal;
	}
	/**
	* 活动结束时间(实际)
	*@return
	*/
	public Date getEndDateReal(){
		return  endDateReal;
	}
	/**
	* 活动结束时间(实际)
	*@param  endDateReal
	*/
	public void setEndDateReal(Date endDateReal ){
		this.endDateReal = endDateReal;
	}
	/**
	* 折扣类型 10.Money off 20.Percentage off
	*@return
	*/
	public Integer getCouponType(){
		return  couponType;
	}
	/**
	* 折扣类型 10.Money off 20.Percentage off
	*@param  couponType
	*/
	public void setCouponType(Integer couponType ){
		this.couponType = couponType;
	}
	/**
	* 折扣
	*@return
	*/
	public BigDecimal getCouponDiscount(){
		return  couponDiscount;
	}
	/**
	* 折扣
	*@param  couponDiscount
	*/
	public void setCouponDiscount(BigDecimal couponDiscount ){
		this.couponDiscount = couponDiscount;
	}
	/**
	* 折扣额
	*@return
	*/
	public BigDecimal getCouponDiscountAmount(){
		return  couponDiscountAmount;
	}
	/**
	* 折扣额
	*@param  couponDiscountAmount
	*/
	public void setCouponDiscountAmount(BigDecimal couponDiscountAmount ){
		this.couponDiscountAmount = couponDiscountAmount;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 货币符号
	*@return
	*/
	public String getCurrencySymbol(){
		return  currencySymbol;
	}
	/**
	* 货币符号
	*@param  currencySymbol
	*/
	public void setCurrencySymbol(String currencySymbol ){
		this.currencySymbol = currencySymbol;
	}
	/**
	* 是否一个客户只能兑换一次 0.否 1.是
	*@return
	*/
	public String getOnlyExchangeOne(){
		return  onlyExchangeOne;
	}
	/**
	* 是否一个客户只能兑换一次 0.否 1.是
	*@param  onlyExchangeOne
	*/
	public void setOnlyExchangeOne(String onlyExchangeOne ){
		this.onlyExchangeOne = onlyExchangeOne;
	}
	/**
	* 预算金额
	*@return
	*/
	public Long getBudget(){
		return  budget;
	}
	/**
	* 预算金额
	*@param  budget
	*/
	public void setBudget(Long budget ){
		this.budget = budget;
	}
	/**
	* 目标客户10.All customers 20.Amazon Prime members 30.Amazon Student members 40.Amazon Family members
	*@return
	*/
	public Integer getTargetCustomer(){
		return  targetCustomer;
	}
	/**
	* 目标客户10.All customers 20.Amazon Prime members 30.Amazon Student members 40.Amazon Family members
	*@param  targetCustomer
	*/
	public void setTargetCustomer(Integer targetCustomer ){
		this.targetCustomer = targetCustomer;
	}
	/**
	* 提报毛利率
	*@return
	*/
	public Long getGrossMargin(){
		return  grossMargin;
	}
	/**
	* 提报毛利率
	*@param  grossMargin
	*/
	public void setGrossMargin(Long grossMargin ){
		this.grossMargin = grossMargin;
	}
	/**
	* 状态 10.草稿 20.审核中 30.未通过 40.提报失败 50.未开始 60.进行中 70.结束中 71.结束失败 72.已结束 80.取消中 81.取消失败 82.取消成功
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* 状态 10.草稿 20.审核中 30.未通过 40.提报失败 50.未开始 60.进行中 70.结束中 71.结束失败 72.已结束 80.取消中 81.取消失败 82.取消成功
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 结束类型 10.正常结束 20.主动结束 30.超预算结束
	*@return
	*/
	public Integer getFinishType(){
		return  finishType;
	}
	/**
	* 结束类型 10.正常结束 20.主动结束 30.超预算结束
	*@param  finishType
	*/
	public void setFinishType(Integer finishType ){
		this.finishType = finishType;
	}
	/**
	* 结束原因
	*@return
	*/
	public String getFinishReason(){
		return  finishReason;
	}
	/**
	* 结束原因
	*@param  finishReason
	*/
	public void setFinishReason(String finishReason ){
		this.finishReason = finishReason;
	}
	/**
	* 取消原因
	*@return
	*/
	public String getCancelReason(){
		return  cancelReason;
	}
	/**
	* 取消原因
	*@param  cancelReason
	*/
	public void setCancelReason(String cancelReason ){
		this.cancelReason = cancelReason;
	}
	/**
	* 提报原因
	*@return
	*/
	public String getReason(){
		return  reason;
	}
	/**
	* 提报原因
	*@param  reason
	*/
	public void setReason(String reason ){
		this.reason = reason;
	}
	/**
	* 失败原因
	*@return
	*/
	public String getFailureReason(){
		return  failureReason;
	}
	/**
	* 失败原因
	*@param  failureReason
	*/
	public void setFailureReason(String failureReason ){
		this.failureReason = failureReason;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}

	public Date getFinishDate() {
		return finishDate;
	}

	public void setFinishDate(Date finishDate) {
		this.finishDate = finishDate;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}
}
