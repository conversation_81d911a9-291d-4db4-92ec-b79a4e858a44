package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomStandardPrice;
import com.zielsmart.mc.vo.SomStandardPriceExportVo;
import com.zielsmart.mc.vo.SomStandardPriceExtVo;
import com.zielsmart.mc.vo.SomStandardPricePageSearchVo;
import com.zielsmart.mc.vo.eya.EyaPriceWaringVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-05-16
 */

@SqlResource("somStandardPrice")
public interface SomStandardPriceMapper extends BaseMapper<SomStandardPrice> {

    /**
     * updateByIdBatch
     * 批量更新价格
     *
     * @param priceUpdateList
     * <AUTHOR>
     * @history
     */
    default void updatePriceByAids(@Param("updateList") List<SomStandardPrice> priceUpdateList) {
        this.getSQLManager().updateBatch(SqlId.of("somStandardPrice.updatePriceByAids"), priceUpdateList);
    }

    /**
     * queryByPlatformAndSite
     * 根据平台站点展示码查询定价信息
     *
     * @param waringVo
     * @return long
     * <AUTHOR>
     * @history
     */
    SomStandardPrice queryByPlatformAndSite(@Param("waringVo") EyaPriceWaringVo waringVo);

    /**
     * queryExportData
     * 导出
     *
     * @param exportVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomStandardPriceVo>}
     * <AUTHOR>
     * @history
     */
    List<SomStandardPriceExportVo> queryExportData(@Param("exportVo") SomStandardPricePageSearchVo exportVo);

    /**
     * updateGrossProfitMargins
     * 批量更新毛利率
     *
     * @param priceUpdateList
     * <AUTHOR>
     * @history
     */
    default void updateGrossProfitMargins(@Param("updateList") List<SomStandardPrice> priceUpdateList) {
        this.getSQLManager().updateBatch(SqlId.of("somStandardPrice.updateGrossProfitMargins"), priceUpdateList);
    }

    /**
     * updateGrossProfitMargins
     * 批量更新毛利率(平台站点展示码)
     *
     * @param priceUpdateList
     * <AUTHOR>
     * @history
     */
    default void updateGrossProfitMargin(@Param("updateList") List<SomStandardPrice> priceUpdateList) {
        this.getSQLManager().updateBatch(SqlId.of("somStandardPrice.updateGrossProfitMargin"), priceUpdateList);
    }

    /**
     * pageSearchStandPrice
     * 分页查询定价信息
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomStandardPriceVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomStandardPriceExtVo> pageSearchStandPrice(@Param("searchVo") SomStandardPricePageSearchVo searchVo, PageRequest pageRequest);

    List<SomStandardPriceExtVo> excelSearchStandPrice(@Param("searchVo") SomStandardPricePageSearchVo searchVo);


    List<SomStandardPriceExtVo> effectivePriceData(@Param("searchVo") SomStandardPriceExtVo priceExtVo,@Param("siteList")List<String> siteList);

    default void updateBatch(@Param("updateList") List<SomStandardPrice> updateList) {
        this.getSQLManager().updateBatch(SqlId.of("somStandardPrice.batchUpdate"), updateList);
    }
}
