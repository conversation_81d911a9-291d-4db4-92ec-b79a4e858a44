package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.zielsmart.mc.repository.entity.SomInventoryChannelConfig;
import com.zielsmart.mc.repository.mapper.McStockInfoMapper;
import com.zielsmart.mc.repository.mapper.SomInventoryChannelConfigMapper;
import com.zielsmart.mc.repository.mapper.SomInventoryInTransitMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomInventoryInTransitService {

    private static final String 自有仓库 = "T1";
    @Resource
    private SomInventoryInTransitMapper somInventoryInTransitMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McStockInfoMapper stockInfoMapper;
    @Resource
    private SomStorageLocationMapper storageLocationMapper;
    @Resource
    private SomInventoryChannelConfigMapper configMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomInventoryInTransitVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomInventoryInTransitVo> queryByPage(SomInventoryInTransitPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomInventoryInTransitVo> pageResult = dynamicSqlManager.getMapper(SomInventoryInTransitMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomInventoryInTransitVo> list = pageResult.getList();
            //平台、站点、发货方式
            List<SomInventoryChannelConfig> configList = configMapper.all();
            Map<String, List<SomInventoryChannelConfig>> configMap = configList.stream().collect(Collectors.groupingBy(e -> e.getPlatform() + e.getSite() + e.getDeliveryType()));
            //根据产品编码查询 mc_stock_info
            List<String> skuList = list.stream().map(x -> x.getProductMainCode()).distinct().collect(Collectors.toList());
            List<McStockInfoExtVo> stockList = stockInfoMapper.allWithMarketInSku(skuList);
            //产品编码+市场
            Map<String, List<McStockInfoExtVo>> skuMarketMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getStorageMarketCode()));
            //产品编码+仓库+库区
            Map<String, List<McStockInfoExtVo>> skuWhSmcMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getWarehouseCode() + e.getSlCode()));
            //产品编码+仓库+库区+类型
            Map<String, List<McStockInfoExtVo>> skuWhSmcTypeMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getWarehouseCode() + e.getSlCode() + e.getWarehouseType()));

            //查询仓库库区市场
            List<SomStorageLocationVo> whMarketListData = storageLocationMapper.queryWhMarket();
            Map<String, List<SomStorageLocationVo>> whMarketMap = whMarketListData.stream().collect(Collectors.groupingBy(e -> e.getMarket()));

            list.forEach(vo -> {
                String key = vo.getPlatform() + vo.getSite() + vo.getIsConsignmentSales();
                List<SomInventoryChannelConfig> config = configMap.getOrDefault(key, null);
                if (config == null) {
                    return;
                }
                String storageMarket = vo.getMarket();
                String warehouseCode = config.get(0).getWarehouseCode();
                List<McStockInfoExtVo> stockInfoList = skuMarketMap.getOrDefault(vo.getProductMainCode() + storageMarket, new ArrayList<>());
                List<SomStorageLocationVo> whMarketList = whMarketMap.getOrDefault(storageMarket, new ArrayList<>());
//                if (stockInfoList == null) {
//                    return;
//                }
                //基础款SKU总库存
                stockInfoList.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setSkuTotalStock(x <= 0 ? 0 : x));
                vo.setTransitQuantity(vo.getTransitQuantity() < 0 ? 0 : vo.getTransitQuantity());
                List<McStockInfoExtVo> stockWhSmcList = config.stream()
                        .map(tmp -> skuWhSmcMap.getOrDefault(vo.getProductMainCode() + tmp.getWarehouseCode() + tmp.getStorageLocation(), null))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

//                if (stockWhSmcList.isEmpty()) {
//                    return;
//                }
                //基础款SKU渠道库存
                stockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setSkuWarehouseStock(x < 0 ? 0 : x));
                //基础款SKU渠道库存 可售天数
                stockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getSevenDayNumber()) && x.getSevenDayNumber().compareTo(BigDecimal.ZERO) > 0).map(x -> x.getSevenDayNumber()).reduce(BigDecimal::add).ifPresent(x -> vo.setSkuWarehouseDay(new BigDecimal(vo.getSkuWarehouseStock()).divide(x, RoundingMode.UP).intValue()));
                vo.setSkuWarehouseDay(vo.getSkuWarehouseDay() <= 0 && vo.getSkuWarehouseStock() != 0 ? 1000 : vo.getSkuWarehouseDay());
                //当前日期 + 基础款SKU渠道可售天数
                if (ObjectUtil.isNotNull(vo.getSkuWarehouseDay())) {
                    vo.setSoldOutDate(DateUtils.addDays(new Date(), vo.getSkuWarehouseDay()));
                }
                //SKU 自有仓 库存
                List<McStockInfoExtVo> skuListByType = whMarketList.stream()
                        .map(tmp -> skuWhSmcTypeMap.getOrDefault(vo.getProductMainCode() + tmp.getWhCode() + tmp.getSlCode() + 自有仓库, null))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

//                if (skuListByType.isEmpty()) {
//                    return;
//                }
                skuListByType.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setSkuSelfStock(x < 0 ? 0 : x));
                //SKU 自有仓 库存 可售天数
                skuListByType.stream().filter(x -> ObjectUtil.isNotNull(x.getSevenDayNumber()) && x.getSevenDayNumber().compareTo(BigDecimal.ZERO) > 0).map(x -> x.getSevenDayNumber()).reduce(BigDecimal::add).ifPresent(x -> vo.setSkuSelfDay(new BigDecimal(vo.getSkuSelfStock()).divide(x, RoundingMode.UP).intValue()));
                vo.setSkuSelfDay(vo.getSkuSelfDay() <= 0 && vo.getSkuSelfStock() != 0 ? 1000 : vo.getSkuSelfDay());
                //基础款SKU自有仓可售天数 < 基础款SKU渠道可售天数 时展示↓ 否则，展示↑
                if (ObjectUtil.isNotNull(vo.getSkuSelfDay()) && ObjectUtil.isNotNull(vo.getSkuWarehouseDay())) {
                    vo.setIsLater(vo.getSkuSelfDay() < vo.getSkuWarehouseDay());
                }
                setSKUInventoryConsumptionSchedule(vo);
                vo.setLaterStr(vo.getIsLater() ? "是" : "否");
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomInventoryInTransitVo.class, searchVo);
    }

    private void setSKUInventoryConsumptionSchedule(SomInventoryInTransitVo vo) {
        //基础款SKU总库存=0则标注“所有库存消耗完”；
        //基础款SKU总库存>0且基础款SKU渠道库存=0，标注“渠道库存消耗完”；
        //基础款SKU总库存>0且基础款SKU渠道库存<20且基础款SKU渠道可售天数<10，标注“渠道库存即将售罄”；
        //基础款SKU总库存>0且基础款SKU渠道库存<20，标注“渠道可售库存少”；
        //基础款SKU渠道可售天数<10，标注“渠道可售天数低”
        if (vo.getSkuTotalStock() == null || vo.getSkuWarehouseStock() == null || vo.getSkuWarehouseDay() == null) {
            return;
        }
        if (vo.getSkuTotalStock() == 0) {
            vo.setConsumptionProgress("所有库存消耗完");
            return;
        }
        if (vo.getSkuTotalStock() > 0 && vo.getSkuWarehouseStock() == 0) {
            vo.setConsumptionProgress("渠道库存消耗完");
            return;
        }
        if (vo.getSkuTotalStock() > 0 && vo.getSkuWarehouseStock() < 20 && vo.getSkuWarehouseDay() < 10) {
            vo.setConsumptionProgress("渠道库存即将售罄");
            return;
        }
        if (vo.getSkuTotalStock() > 0 && vo.getSkuWarehouseStock() < 20) {
            vo.setConsumptionProgress("渠道可售库存少");
            return;
        }
        if (vo.getSkuWarehouseDay() < 10) {
            vo.setConsumptionProgress("渠道可售天数低");
            return;
        }
        vo.setConsumptionProgress("大量库存需要消耗");
    }


    public PageVo<SomInventoryInTransitNewVo> queryByPageNew(SomInventoryInTransitPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomInventoryInTransitNewVo> pageResult = dynamicSqlManager.getMapper(SomInventoryInTransitMapper.class).queryByPageNew(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomInventoryInTransitNewVo> list = pageResult.getList();
            //平台、站点、发货方式
            List<SomInventoryChannelConfig> configList = configMapper.all();
            Map<String, List<SomInventoryChannelConfig>> configMap = configList.stream().collect(Collectors.groupingBy(e -> e.getPlatform() + e.getSite() + e.getDeliveryType()));
            //根据产品编码查询 mc_stock_info
            List<String> skuList = list.stream().map(SomInventoryInTransitNewVo::getProductMainCode).distinct().collect(Collectors.toList());
            skuList.addAll(list.stream().map(SomInventoryInTransitNewVo::getOldSkuCode).distinct().collect(Collectors.toList()));
            skuList.addAll(list.stream().map(SomInventoryInTransitNewVo::getNewSkuCode).distinct().collect(Collectors.toList()));
            List<McStockInfoExtVo> stockList = stockInfoMapper.allWithMarketInSku(skuList);
            //产品编码+市场
            Map<String, List<McStockInfoExtVo>> skuMarketMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getStorageMarketCode()));
            //产品编码+仓库+库区
            Map<String, List<McStockInfoExtVo>> skuWhSmcMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getWarehouseCode() + e.getSlCode()));
            //产品编码+仓库+库区+类型
            //Map<String, List<McStockInfoExtVo>> skuWhSmcTypeMap = stockList.stream().collect(Collectors.groupingBy(e -> e.getProductMainCode() + e.getWarehouseCode() + e.getSlCode() + e.getWarehouseType()));

            //查询仓库库区市场
            List<SomStorageLocationVo> whMarketListData = storageLocationMapper.queryWhMarket();
            Map<String, List<SomStorageLocationVo>> whMarketMap = whMarketListData.stream().collect(Collectors.groupingBy(e -> e.getMarket()));

            list.forEach(vo -> {
                String key = vo.getPlatform() + vo.getSite() + vo.getIsConsignmentSales();
                List<SomInventoryChannelConfig> config = configMap.getOrDefault(key, null);
                if (config == null) {
                    return;
                }
                String storageMarket = vo.getMarket();
                String warehouseCode = config.get(0).getWarehouseCode();
                //List<McStockInfoExtVo> stockInfoList = skuMarketMap.getOrDefault(vo.getProductMainCode() + storageMarket, new ArrayList<>());
                //List<SomStorageLocationVo> whMarketList = whMarketMap.getOrDefault(storageMarket, new ArrayList<>());
                List<McStockInfoExtVo> stockWhSmcList = config.stream()
                        .map(tmp -> skuWhSmcMap.getOrDefault(vo.getProductMainCode() + tmp.getWarehouseCode() + tmp.getStorageLocation(), null))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

                //在售SKU渠道库存
                stockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setSkuWarehouseStock(x < 0 ? 0 : x));
                //在售SKU渠道库存 可售天数
                stockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getSevenDayNumber()) && x.getSevenDayNumber().compareTo(BigDecimal.ZERO) > 0).map(x -> x.getSevenDayNumber()).reduce(BigDecimal::add).ifPresent(x -> vo.setSkuWarehouseDay(new BigDecimal(vo.getSkuWarehouseStock()).divide(x, RoundingMode.UP).intValue()));
                vo.setSkuWarehouseDay(vo.getSkuWarehouseDay() <= 0 && vo.getSkuWarehouseStock() != 0 ? 1000 : vo.getSkuWarehouseDay());

                List<McStockInfoExtVo> oldStockWhSmcList = config.stream()
                        .map(tmp -> skuWhSmcMap.getOrDefault(vo.getOldSkuCode() + tmp.getWarehouseCode() + tmp.getStorageLocation(), null))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

                //老款SKU渠道库存
                oldStockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setOldSkuWarehouseStock(x < 0 ? 0 : x));

                List<McStockInfoExtVo> newStockWhSmcList = config.stream()
                        .map(tmp -> skuWhSmcMap.getOrDefault(vo.getNewSkuCode() + tmp.getWarehouseCode() + tmp.getStorageLocation(), null))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

                //老款SKU渠道库存
                newStockWhSmcList.stream().filter(x -> ObjectUtil.isNotNull(x.getTotalStock())).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(x -> vo.setNewSkuWarehouseStock(x < 0 ? 0 : x));
                //新款是否到仓
                vo.setReadyFlag(vo.getNewSkuWarehouseStock()>0?"1":"0");
                if (null!=vo.getNewSkuStockInfo()){
                    //预计到仓时间
                    String newSkuInfoStr=vo.getNewSkuStockInfo().getValue();
                    JSONArray jsonArray=JSONUtil.parseArray(newSkuInfoStr);
                    List<SomInventoryInTransitWhVo> newSkuInfoList= JSONUtil.toList(jsonArray, SomInventoryInTransitWhVo.class);
                    //获取配置的所有仓库
                    List<String> whCodes=config.stream().map(SomInventoryChannelConfig::getWarehouseCode).distinct().collect(Collectors.toList());
                    List<SomInventoryInTransitWhVo> resultNewSkuList=newSkuInfoList.stream().filter(n->whCodes.contains(n.getWhCode())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(resultNewSkuList)){
                        vo.setPlanArriveDate(resultNewSkuList.get(0).getPlanArriveDate());
                        vo.setPlanArriveQuantity(resultNewSkuList.stream().filter(r->ObjectUtil.equal(r.getPlanArriveDate(),vo.getPlanArriveDate())).mapToInt(SomInventoryInTransitWhVo::getShippingNumber).sum());
                    }
                }
            });
        }
        return ConvertUtils.pageConvert(pageResult, SomInventoryInTransitNewVo.class, searchVo);
    }

    public String export(SomInventoryInTransitPageSearchVo searchVo) throws ValidateException {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomInventoryInTransitNewVo> records = queryByPageNew(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "改款SKU库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomInventoryInTransitNewVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出可售库存报表失败：" + e.getMessage());
            }
        }
        return null;
    }
}
