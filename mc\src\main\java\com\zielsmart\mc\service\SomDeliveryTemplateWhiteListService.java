package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomDeliveryTemplateWhiteList;
import com.zielsmart.mc.repository.mapper.SomDeliveryTemplateWhiteListMapper;
import com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomDeliveryTemplateWhiteListService {

    @Resource
    private SomDeliveryTemplateWhiteListMapper somDeliveryTemplateWhiteListMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomDeliveryTemplateWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomDeliveryTemplateWhiteListVo> queryByPage(SomDeliveryTemplateWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomDeliveryTemplateWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomDeliveryTemplateWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomDeliveryTemplateWhiteListVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param vo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomDeliveryTemplateWhiteListVo vo, TokenUserInfo tokenUser) throws ValidateException {
        // 非空校验
        if (ObjectUtil.isEmpty(vo) || StrUtil.isBlank(vo.getPlatform()) || StrUtil.isBlank(vo.getSite()) || StrUtil.isBlank(vo.getSellerSku())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        // 唯一性校验
        long count = somDeliveryTemplateWhiteListMapper.createLambdaQuery().andEq("platform", vo.getPlatform()).andEq("site", vo.getSite()).andEq("seller_sku", vo.getSellerSku()).count();
        if (count > 0) {
            throw new ValidateException("存在重复数据，请检查");
        }
        vo.setAid(IdUtil.fastSimpleUUID());
        vo.setCreateName(tokenUser.getUserName());
        vo.setCreateNum(tokenUser.getJobNumber());
        vo.setCreateTime(DateTime.now().toJdkDate());
        somDeliveryTemplateWhiteListMapper.insert(ConvertUtils.beanConvert(vo, SomDeliveryTemplateWhiteList.class));
    }

    /**
     * delete
     * 删除
     *
     * @param vo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomDeliveryTemplateWhiteListVo vo) throws ValidateException {
        if (ObjectUtil.isEmpty(vo) || ObjectUtil.isEmpty(vo.getAidList())) {
            throw new ValidateException("请选择要删除的数据");
        }
        //somDeliveryTemplateWhiteListMapper.createLambdaQuery().andEq("aid", vo.getAid()).delete();
        somDeliveryTemplateWhiteListMapper.createLambdaQuery().andIn("aid", vo.getAidList()).delete();
    }
}
