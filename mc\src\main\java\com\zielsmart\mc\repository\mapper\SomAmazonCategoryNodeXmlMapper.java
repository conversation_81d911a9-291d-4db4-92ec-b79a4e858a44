package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonCategoryNodeXml;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
* 
* gen by 代码生成器 mapper 2024-12-16
*/

@SqlResource("somAmazonCategoryNodeXml")
public interface SomAmazonCategoryNodeXmlMapper extends BaseMapper<SomAmazonCategoryNodeXml> {

    /**
     * 查询所有子节点
     *
     * @param browseNodeId 根节点
     * @param site 站点
     * @return List<SomAmazonCategoryNodeXml>
     */
    List<SomAmazonCategoryNodeXml> queryChild(@Param("browseNodeId") String browseNodeId, @Param("site") String site);
}
