package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomToSellStoreInfo;
import com.zielsmart.mc.repository.mapper.SomToSellStoreInfoMapper;
import com.zielsmart.mc.vo.SomToSellStoreInfoPageSearchVo;
import com.zielsmart.mc.vo.SomToSellStoreInfoVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.logging.log4j.util.Strings;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomToSellStoreInfoService {

    @Resource
    private SomToSellStoreInfoMapper somToSellStoreInfoMapper;

    /**
     * save
     * 添加或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void addOrEdit(SomToSellStoreInfoVo addOrEditVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addOrEditVo) || StrUtil.isEmpty(addOrEditVo.getSellerId()) || StrUtil.isEmpty(addOrEditVo.getSellerName())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isEmpty(addOrEditVo.getAid())) {
            long count = somToSellStoreInfoMapper.createLambdaQuery().andEq("seller_id", addOrEditVo.getSellerId()).count();
            if (count > 0) {
                throw new ValidateException("此跟卖店铺ID已维护「跟卖店铺名称」");
            }
            addOrEditVo.setAid(IdUtil.fastSimpleUUID());
            addOrEditVo.setCreateNum(tokenUser.getJobNumber());
            addOrEditVo.setCreateName(tokenUser.getUserName());
            addOrEditVo.setCreateTime(DateTime.now().toJdkDate());
            somToSellStoreInfoMapper.insert(ConvertUtils.beanConvert(addOrEditVo, SomToSellStoreInfo.class));
        } else {
            long count = somToSellStoreInfoMapper.createLambdaQuery().andEq("seller_id", addOrEditVo.getSellerId()).andNotEq("aid", addOrEditVo.getAid()).count();
            if (count > 0) {
                throw new ValidateException("此跟卖店铺ID已维护「跟卖店铺名称」");
            }
            SomToSellStoreInfo obj = somToSellStoreInfoMapper.createLambdaQuery().andEq("aid", addOrEditVo.getAid()).single();
            obj.setSellerName(addOrEditVo.getSellerName());
            obj.setModifyNum(tokenUser.getJobNumber());
            obj.setModifyName(tokenUser.getUserName());
            obj.setModifyTime(DateTime.now().toJdkDate());
            somToSellStoreInfoMapper.updateById(obj);
        }
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomToSellStoreInfoVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomToSellStoreInfoVo> queryByPage(SomToSellStoreInfoPageSearchVo pageSearchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(pageSearchVo.getCurrent(), pageSearchVo.getPageSize());
        PageResult<SomToSellStoreInfoVo> pageResult = somToSellStoreInfoMapper.queryByPage(pageSearchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomToSellStoreInfoVo.class, pageSearchVo);
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomToSellStoreInfoVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || StrUtil.isEmpty(deleteVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somToSellStoreInfoMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).delete();
    }

    /**
     * importExcel
     * 导入
     * @param list
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public String importExcel(List<SomToSellStoreInfoVo> list, TokenUserInfo tokenUser) throws ValidateException{
        StringBuilder msg = new StringBuilder();
        for(SomToSellStoreInfoVo importVo :list){
            if(StrUtil.isEmpty(importVo.getSellerId()) || StrUtil.isEmpty(importVo.getSellerName())){
                throw new ValidateException("数据存在空值,请检查");
            }
        }
        Map<String, List<SomToSellStoreInfoVo>> map = list.parallelStream().collect(Collectors.groupingBy(c -> c.getSellerId()));
        for(String key : map.keySet()){
            List<SomToSellStoreInfoVo> listVo = map.get(key);
            if(listVo.size() > 1){
                throw new ValidateException("数据存在重复,请检查");
            }
        }
        List<String> sellerIdList = list.stream().map(m -> m.getSellerId()).collect(Collectors.toList());
        List<SomToSellStoreInfo> existList = somToSellStoreInfoMapper.createLambdaQuery().andIn("seller_id", sellerIdList).select();
        List<SomToSellStoreInfo> insertList = new ArrayList<>();
        for(SomToSellStoreInfoVo importVo :list){
            SomToSellStoreInfo info = new SomToSellStoreInfo();
            long count = existList.stream().filter(f -> StrUtil.equals(importVo.getSellerId(), f.getSellerId())).count();
            if(count > 0){
                msg.append(importVo.getSellerId()).append("\n");
                continue;
            }
            info.setSellerId(importVo.getSellerId());
            info.setSellerName(importVo.getSellerName());
            info.setAid(IdUtil.fastSimpleUUID());
            info.setCreateNum(tokenUser.getJobNumber());
            info.setCreateName(tokenUser.getUserName());
            info.setCreateTime(DateTime.now().toJdkDate());
            insertList.add(info);
        }
        if(StrUtil.isNotEmpty(msg.toString())){
            msg.append("已维护「跟卖店铺名称」");
            return msg.toString();
        }
        somToSellStoreInfoMapper.insertBatch(insertList);
        return Strings.EMPTY;
    }
}
