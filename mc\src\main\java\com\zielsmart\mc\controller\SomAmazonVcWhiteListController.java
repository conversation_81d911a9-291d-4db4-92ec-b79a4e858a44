package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import com.zielsmart.mc.service.SomAmazonVcWhiteListService;
import com.zielsmart.mc.vo.SomAmazonVcWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomAmazonVcWhiteListVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomAmazonVcWhiteListController
 * @description
 * @date 2024-02-01 16:17:24
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somAmazonVcWhiteList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "VC库存白名单管理")
public class SomAmazonVcWhiteListController extends BasicController {

    @Resource
    SomAmazonVcWhiteListService somAmazonVcWhiteListService;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomAmazonVcWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomAmazonVcWhiteListVo>> queryByPage(@RequestBody SomAmazonVcWhiteListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somAmazonVcWhiteListService.queryByPage(searchVo));
    }


    /**
     * 删除
     *
     * @param somAmazonVcWhiteListVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomAmazonVcWhiteListVo somAmazonVcWhiteListVo) throws ValidateException {
        somAmazonVcWhiteListService.delete(somAmazonVcWhiteListVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载白名单模板
     *
     * @return {@link java.lang.String}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "下载白名单模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/SomVcWhiteListTemplate.xlsx";
    }

    /**
     * 导入白名单
     *
     * @param file
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link Exception}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入白名单")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"展示码", "供应商编码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<SomAmazonVcWhiteListVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), SomAmazonVcWhiteListVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误，请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空，请检查数据");
        }
        somAmazonVcWhiteListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * export
     * 导出
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody SomAmazonVcWhiteListPageSearchVo searchVo) {
        String data = somAmazonVcWhiteListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
