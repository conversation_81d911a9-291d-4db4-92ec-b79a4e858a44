package com.zielsmart.mc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomTiktokWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomStorageLocationMapper;
import com.zielsmart.mc.repository.mapper.SomTiktokWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokWarehouseConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-12-13 12:21:48
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTiktokWarehouseConfigService {

    @Resource
    private SomTiktokWarehouseConfigMapper somTiktokWarehouseConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private SomStorageLocationMapper somStorageLocationMapper;
    @Resource
    private McWarehouseMapper mcWarehouseMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTiktokWarehouseConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTiktokWarehouseConfigVo> queryByPage(SomTiktokWarehouseConfigPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        //分页先查询出平台仓库 名称
        PageResult<SomTiktokWarehouseConfigVo> pageResult = dynamicSqlManager.getMapper(SomTiktokWarehouseConfigMapper.class).queryByPage(searchVo, pageRequest);
        if (!pageResult.getList().isEmpty()) {
            List<SomTiktokWarehouseConfig> allList = somTiktokWarehouseConfigMapper.createLambdaQuery()
                    .andIn("warehouse_code", pageResult.getList().stream().map(x -> x.getWarehouseCode()).collect(Collectors.toList()))
                    .select();
            Map<String, List<SomTiktokWarehouseConfig>> allMap = allList.stream().collect(Collectors.groupingBy(e -> e.getShopCipher() + e.getWarehouseCode()));
            Map<String, String> warehouseMap = mcWarehouseMapper.all().stream().collect(Collectors.toMap(e -> e.getWarehouseCode(), y -> y.getWarehouseName(), (x1, x2) -> x1));
            Map<String, String> storageMap = somStorageLocationMapper.all().stream().collect(Collectors.toMap(e -> e.getslCode(), y -> y.getslName(), (x1, x2) -> x1));
            for (SomTiktokWarehouseConfigVo vo : pageResult.getList()) {
                List<SomTiktokWarehouseConfig> warehouseConfigs = allMap.get(vo.getShopCipher() + vo.getWarehouseCode());
                List<String> nameList = new ArrayList<>();
                vo.setList(warehouseConfigs.stream().map(x -> {
                    SomTiktokWarehouseConfigVo.UseableWarehouse useableWarehouse = new SomTiktokWarehouseConfigVo.UseableWarehouse();
                    useableWarehouse.setUseableStorageCode(x.getUseableStorageCode());
                    useableWarehouse.setUseableWarehouseCode(x.getUseableWarehouseCode());
                    nameList.add(warehouseMap.get(x.getUseableWarehouseCode()) + "-" + storageMap.get(x.getUseableStorageCode()));
                    return useableWarehouse;
                }).collect(Collectors.toList()));
                vo.setWarehouseNameList(nameList);
                SomTiktokWarehouseConfig config1 = warehouseConfigs.get(0);
                vo.setCreateName(config1.getCreateName());
                vo.setCreateNum(config1.getCreateNum());
                vo.setCreateTime(config1.getCreateTime());
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTiktokWarehouseConfigVo.class, searchVo);
    }

    /**
     * save
     * 添加
     *
     * @param somTiktokWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTiktokWarehouseConfigVo) || somTiktokWarehouseConfigVo.getShopCipher() == null || somTiktokWarehouseConfigVo.getWarehouseCode() == null || somTiktokWarehouseConfigVo.getList() == null || somTiktokWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somTiktokWarehouseConfigVo.getWarehouseCode();
        if (somTiktokWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).count() > 0) {
            throw new ValidateException("您输入的平台仓库在系统中已存在，不允许重复添加");
        }
        List<SomTiktokWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomTiktokWarehouseConfigVo.UseableWarehouse useableWarehouse : somTiktokWarehouseConfigVo.getList()) {
            SomTiktokWarehouseConfig config = new SomTiktokWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setShopCipher(somTiktokWarehouseConfigVo.getShopCipher());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somTiktokWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somTiktokWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * update
     * 修改
     *
     * @param somTiktokWarehouseConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(somTiktokWarehouseConfigVo) || somTiktokWarehouseConfigVo.getWarehouseCode() == null || somTiktokWarehouseConfigVo.getList().isEmpty()) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        String warehouseCode = somTiktokWarehouseConfigVo.getWarehouseCode();

        List<SomTiktokWarehouseConfig> insertList = new ArrayList<>();
        Date now = new Date();
        for (SomTiktokWarehouseConfigVo.UseableWarehouse useableWarehouse : somTiktokWarehouseConfigVo.getList()) {
            SomTiktokWarehouseConfig config = new SomTiktokWarehouseConfig();
            config.setAid(IdUtil.fastSimpleUUID());
            config.setShopCipher(somTiktokWarehouseConfigVo.getShopCipher());
            config.setWarehouseCode(warehouseCode);
            config.setWarehouseName(somTiktokWarehouseConfigVo.getWarehouseName());
            config.setUseableWarehouseCode(useableWarehouse.getUseableWarehouseCode());
            config.setUseableStorageCode(useableWarehouse.getUseableStorageCode());
            config.setCreateNum(tokenUser.getJobNumber());
            config.setCreateName(tokenUser.getUserName());
            config.setCreateTime(now);
            config.setLastModifyName(tokenUser.getUserName());
            config.setLastModifyTime(now);
            config.setLastModifyNum(tokenUser.getJobNumber());
            insertList.add(config);
        }
        if (!insertList.isEmpty()) {
            somTiktokWarehouseConfigMapper.createLambdaQuery().andEq("warehouse_code", warehouseCode).delete();
            somTiktokWarehouseConfigMapper.insertBatch(insertList);
        }
    }

    /**
     * delete
     * 删除
     *
     * @param somTiktokWarehouseConfigVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTiktokWarehouseConfigVo) || StrUtil.isEmpty(somTiktokWarehouseConfigVo.getWarehouseCode())) {
            throw new ValidateException("店铺ID 平台仓库ID不能为空");
        }
        somTiktokWarehouseConfigMapper.createLambdaQuery()
                .andEq("warehouse_code", somTiktokWarehouseConfigVo.getWarehouseCode())
                .delete();
    }

    public List<Map> shopCipher() {
        //店铺ID： tiktok下载下来的仓库信息中获取
        List<String> warehouseInfoList = somTiktokWarehouseConfigMapper.getShopCipher();
        List<Map> result = warehouseInfoList.stream().map(x -> {
            Map<String, Object> map = new HashMap<>();
            map.put("shipCipherName", x);
            map.put("shopCipher", x);
            return map;
        }).collect(Collectors.toList());
        return result;
    }

    public List<Map> warehouseList(SomTiktokWarehouseConfigVo somTiktokWarehouseConfigVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somTiktokWarehouseConfigVo) || StrUtil.isEmpty(somTiktokWarehouseConfigVo.getShopCipher())) {
            throw new ValidateException("店铺ID不能为空");
        }
        List<Map> warehouseByShopCipher = somTiktokWarehouseConfigMapper.getWarehouseByShopCipher(somTiktokWarehouseConfigVo.getShopCipher());
        List<Map> result = warehouseByShopCipher.stream().map(x -> {
            Map<String, Object> map = new HashMap<>();
            map.put("warehouseCode", x.get("warehouseId"));
            map.put("warehouseName", x.get("warehouseName"));
            return map;
        }).collect(Collectors.toList());
        return result;
    }
}
