package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.SomOttoPlatformPricePageSearchVo;
import com.zielsmart.mc.vo.SomOttoPlatformPriceVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description OTTO 平台价调整
 * @date 2025-04-01 15:49:26
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@SqlResource("somOttoPlatformPrice")
public interface SomOttoPlatformPriceMapper extends BaseMapper<SomOttoPlatformPrice> {

    /**
     * 分页查询
     * @param searchVo 入参
     * @param pageRequest 分页参数
     * @return SomOttoPlatformPriceVo
     */
    PageResult<SomOttoPlatformPriceVo> queryByPage(SomOttoPlatformPricePageSearchVo searchVo, PageRequest pageRequest);

    /**
     * 批量更新
     *
     * @param updateList 需要更新的数据
     */
    default void batchUpdate(List<SomOttoPlatformPrice> updateList){
        this.getSQLManager().updateBatch(SqlId.of("somOttoPlatformPrice.batchUpdate"), updateList);
    }
}
