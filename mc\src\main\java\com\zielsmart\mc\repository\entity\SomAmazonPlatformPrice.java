package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/*
* 亚马逊平台价管理
* gen by 代码生成器 2023-11-14
*/

@Table(name="mc.som_amazon_platform_price")
public class SomAmazonPlatformPrice implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 价格
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 调价状态
	 * price_status     history_status    显示
	 * 有值				有值/没数据			price_status
	 *  null     		 有值				history_status
	 *  没数据			 没数据 				-
	 *  0				 没数据				-
	 */
	@Column("adjust_price_status")
	private String adjustPriceStatus ;
	/**
	 * Quantity Bound 1
	 */
	@Column("quantity_bound1")
	private Integer quantityBound1 ;
	/**
	 * Quantity Price 1
	 */
	@Column("quantity_price1")
	private BigDecimal quantityPrice1 ;
	/**
	 * Quantity Bound 2
	 */
	@Column("quantity_bound2")
	private Integer quantityBound2 ;
	/**
	 * Quantity Price 2
	 */
	@Column("quantity_price2")
	private BigDecimal quantityPrice2 ;
	/**
	 * Quantity Bound 3
	 */
	@Column("quantity_bound3")
	private Integer quantityBound3 ;
	/**
	 * Quantity Price 3
	 */
	@Column("quantity_price3")
	private BigDecimal quantityPrice3 ;
	/**
	 * Quantity Bound 4
	 */
	@Column("quantity_bound4")
	private Integer quantityBound4 ;
	/**
	 * Quantity Price 4
	 */
	@Column("quantity_price4")
	private BigDecimal quantityPrice4 ;
	/**
	 * Quantity Bound 5
	 */
	@Column("quantity_bound5")
	private Integer quantityBound5 ;
	/**
	 * Quantity Price 5
	 */
	@Column("quantity_price5")
	private BigDecimal quantityPrice5 ;
	/**
	 * 价格类型 present/fixed
	 */
	@Column("quantity_price_type")
	private String quantityPriceType ;
	/**
	 * 修改人
	 */
	@Column("modify_num")
	private String modifyNum ;
	/**
	 * 修改时间
	 */
	@Column("modify_time")
	private Date modifyTime ;
	/**
	 * 调价原因Txt
	 */
	@Column("reason_txt")
	private String reasonTxt ;
	/**
	 * 调价原因
	 */
	@Column("reason")
	private Integer reason ;
	/**
	 * 修改人姓名
	 */
	@Column("modify_name")
	private String modifyName ;
	/**
	 * 最低价
	 */
	@Column("min_price")
	private BigDecimal minPrice ;
	/**
	 * 最高价
	 */
	@Column("max_price")
	private BigDecimal maxPrice ;
	/**
	 * Bussiness Price
	 */
	@Column("business_price")
	private BigDecimal businessPrice ;
	/**
	 * 调价幅度
	 */
	@Column("adjust_range")
	private BigDecimal adjustRange ;
	/**
	 * 未来八周可销天数
	 */
	@Column("sale_day")
	private String saleDay ;
	/**
	 * 近四周毛利率
	 */
	@Column("gross_margin")
	private String grossMargin ;
	/**
	 * 近四周销售预测达成率
	 */
	@Column("success_rate")
	private String successRate ;

	public SomAmazonPlatformPrice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 价格
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 价格
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 调价状态
	*@return
	*/
	public String getAdjustPriceStatus(){
		return  adjustPriceStatus;
	}
	/**
	* 调价状态
	*@param  adjustPriceStatus
	*/
	public void setAdjustPriceStatus(String adjustPriceStatus ){
		this.adjustPriceStatus = adjustPriceStatus;
	}
	/**
	* Quantity Bound 1
	*@return
	*/
	public Integer getQuantityBound1(){
		return  quantityBound1;
	}
	/**
	* Quantity Bound 1
	*@param  quantityBound1
	*/
	public void setQuantityBound1(Integer quantityBound1 ){
		this.quantityBound1 = quantityBound1;
	}
	/**
	* Quantity Price 1
	*@return
	*/
	public BigDecimal getQuantityPrice1(){
		return  quantityPrice1;
	}
	/**
	* Quantity Price 1
	*@param  quantityPrice1
	*/
	public void setQuantityPrice1(BigDecimal quantityPrice1 ){
		this.quantityPrice1 = quantityPrice1;
	}
	/**
	* Quantity Bound 2
	*@return
	*/
	public Integer getQuantityBound2(){
		return  quantityBound2;
	}
	/**
	* Quantity Bound 2
	*@param  quantityBound2
	*/
	public void setQuantityBound2(Integer quantityBound2 ){
		this.quantityBound2 = quantityBound2;
	}
	/**
	* Quantity Price 2
	*@return
	*/
	public BigDecimal getQuantityPrice2(){
		return  quantityPrice2;
	}
	/**
	* Quantity Price 2
	*@param  quantityPrice2
	*/
	public void setQuantityPrice2(BigDecimal quantityPrice2 ){
		this.quantityPrice2 = quantityPrice2;
	}
	/**
	* Quantity Bound 3
	*@return
	*/
	public Integer getQuantityBound3(){
		return  quantityBound3;
	}
	/**
	* Quantity Bound 3
	*@param  quantityBound3
	*/
	public void setQuantityBound3(Integer quantityBound3 ){
		this.quantityBound3 = quantityBound3;
	}
	/**
	* Quantity Price 3
	*@return
	*/
	public BigDecimal getQuantityPrice3(){
		return  quantityPrice3;
	}
	/**
	* Quantity Price 3
	*@param  quantityPrice3
	*/
	public void setQuantityPrice3(BigDecimal quantityPrice3 ){
		this.quantityPrice3 = quantityPrice3;
	}
	/**
	* Quantity Bound 4
	*@return
	*/
	public Integer getQuantityBound4(){
		return  quantityBound4;
	}
	/**
	* Quantity Bound 4
	*@param  quantityBound4
	*/
	public void setQuantityBound4(Integer quantityBound4 ){
		this.quantityBound4 = quantityBound4;
	}
	/**
	* Quantity Price 4
	*@return
	*/
	public BigDecimal getQuantityPrice4(){
		return  quantityPrice4;
	}
	/**
	* Quantity Price 4
	*@param  quantityPrice4
	*/
	public void setQuantityPrice4(BigDecimal quantityPrice4 ){
		this.quantityPrice4 = quantityPrice4;
	}
	/**
	* Quantity Bound 5
	*@return
	*/
	public Integer getQuantityBound5(){
		return  quantityBound5;
	}
	/**
	* Quantity Bound 5
	*@param  quantityBound5
	*/
	public void setQuantityBound5(Integer quantityBound5 ){
		this.quantityBound5 = quantityBound5;
	}
	/**
	* Quantity Price 5
	*@return
	*/
	public BigDecimal getQuantityPrice5(){
		return  quantityPrice5;
	}
	/**
	* Quantity Price 5
	*@param  quantityPrice5
	*/
	public void setQuantityPrice5(BigDecimal quantityPrice5 ){
		this.quantityPrice5 = quantityPrice5;
	}
	/**
	* 价格类型 present/fixed
	*@return
	*/
	public String getQuantityPriceType(){
		return  quantityPriceType;
	}
	/**
	* 价格类型 present/fixed
	*@param  quantityPriceType
	*/
	public void setQuantityPriceType(String quantityPriceType ){
		this.quantityPriceType = quantityPriceType;
	}
	/**
	* 修改人
	*@return
	*/
	public String getModifyNum(){
		return  modifyNum;
	}
	/**
	* 修改人
	*@param  modifyNum
	*/
	public void setModifyNum(String modifyNum ){
		this.modifyNum = modifyNum;
	}
	/**
	* 修改时间
	*@return
	*/
	public Date getModifyTime(){
		return  modifyTime;
	}
	/**
	* 修改时间
	*@param  modifyTime
	*/
	public void setModifyTime(Date modifyTime ){
		this.modifyTime = modifyTime;
	}
	/**
	* 调价原因Txt
	*@return
	*/
	public String getReasonTxt(){
		return  reasonTxt;
	}
	/**
	* 调价原因Txt
	*@param  reasonTxt
	*/
	public void setReasonTxt(String reasonTxt ){
		this.reasonTxt = reasonTxt;
	}
	/**
	* 调价原因
	*@return
	*/
	public Integer getReason(){
		return  reason;
	}
	/**
	* 调价原因
	*@param  reason
	*/
	public void setReason(Integer reason ){
		this.reason = reason;
	}
	/**
	* 修改人姓名
	*@return
	*/
	public String getModifyName(){
		return  modifyName;
	}
	/**
	* 修改人姓名
	*@param  modifyName
	*/
	public void setModifyName(String modifyName ){
		this.modifyName = modifyName;
	}
	/**
	* 最低价
	*@return
	*/
	public BigDecimal getMinPrice(){
		return  minPrice;
	}
	/**
	* 最低价
	*@param  minPrice
	*/
	public void setMinPrice(BigDecimal minPrice ){
		this.minPrice = minPrice;
	}
	/**
	* 最高价
	*@return
	*/
	public BigDecimal getMaxPrice(){
		return  maxPrice;
	}
	/**
	* 最高价
	*@param  maxPrice
	*/
	public void setMaxPrice(BigDecimal maxPrice ){
		this.maxPrice = maxPrice;
	}
	/**
	* Bussiness Price
	*@return
	*/
	public BigDecimal getBusinessPrice(){
		return  businessPrice;
	}
	/**
	* Bussiness Price
	*@param  businessPrice
	*/
	public void setBusinessPrice(BigDecimal businessPrice ){
		this.businessPrice = businessPrice;
	}
	/**
	* 调价幅度
	*@return
	*/
	public BigDecimal getAdjustRange(){
		return  adjustRange;
	}
	/**
	* 调价幅度
	*@param  adjustRange
	*/
	public void setAdjustRange(BigDecimal adjustRange ){
		this.adjustRange = adjustRange;
	}
	/**
	* 未来八周可销天数
	*@return
	*/
	public String getSaleDay(){
		return  saleDay;
	}
	/**
	* 未来八周可销天数
	*@param  saleDay
	*/
	public void setSaleDay(String saleDay ){
		this.saleDay = saleDay;
	}
	/**
	* 近四周毛利率
	*@return
	*/
	public String getGrossMargin(){
		return  grossMargin;
	}
	/**
	* 近四周毛利率
	*@param  grossMargin
	*/
	public void setGrossMargin(String grossMargin ){
		this.grossMargin = grossMargin;
	}
	/**
	* 近四周销售预测达成率
	*@return
	*/
	public String getSuccessRate(){
		return  successRate;
	}
	/**
	* 近四周销售预测达成率
	*@param  successRate
	*/
	public void setSuccessRate(String successRate ){
		this.successRate = successRate;
	}

}
