package com.zielsmart.mc.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.SomWayfairMultiparcelConfig;
import com.zielsmart.mc.repository.mapper.SomWayfairMultiparcelConfigMapper;
import com.zielsmart.mc.vo.SomWayfairMultiparcelConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWayfairMultiparcelConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomWayfairMultiparcelConfigService {

    @Resource
    private SomWayfairMultiparcelConfigMapper somWayfairMultiparcelConfigMapper;

    /**
     * addOrEdit
     * 新增或编辑
     *
     * @param addOrEditVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void addOrEdit(SomWayfairMultiparcelConfigVo addOrEditVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addOrEditVo) || ObjectUtil.isEmpty(addOrEditVo.getPlatform()) || StrUtil.isBlank(addOrEditVo.getSite()) | StrUtil.isBlank(addOrEditVo.getSellerSku()) || StrUtil.isBlank(addOrEditVo.getMultiparcelSku())) {
            throw new ValidateException("数据存在空值，请检查");
        }
        // 避免出现多输空格导致多逗号的情况
        // addOrEditVo.getMultiparcelSku().replace("\n", ",")
        List<String> list = Arrays.asList(addOrEditVo.getMultiparcelSku().split("\n"));
        String str = list.stream().collect(Collectors.joining(","));
        if (StrUtil.isEmpty(addOrEditVo.getAid())) {
            addOrEditVo.setPlatform(addOrEditVo.getPlatform());
            addOrEditVo.setSite(addOrEditVo.getSite());
            addOrEditVo.setSellerSku(addOrEditVo.getSellerSku());
            long count = somWayfairMultiparcelConfigMapper.createLambdaQuery().andEq("platform", addOrEditVo.getPlatform())
                    .andEq("site", addOrEditVo.getSite()).andEq("seller_sku", addOrEditVo.getSellerSku()).count();
            if (count > 0) {
                throw new ValidateException("当前数据已维护");
            }
            addOrEditVo.setMultiparcelSku(str);
            addOrEditVo.setAid(IdUtil.fastSimpleUUID());
            addOrEditVo.setCreateNum(tokenUser.getJobNumber());
            addOrEditVo.setCreateName(tokenUser.getUserName());
            addOrEditVo.setCreateTime(DateTime.now().toJdkDate());
            somWayfairMultiparcelConfigMapper.insert(ConvertUtils.beanConvert(addOrEditVo, SomWayfairMultiparcelConfig.class));
        } else {
            SomWayfairMultiparcelConfig obj = somWayfairMultiparcelConfigMapper.createLambdaQuery().andEq("aid", addOrEditVo.getAid()).single();
            if (ObjectUtil.isNotEmpty(obj)) {
                obj.setPlatform(addOrEditVo.getPlatform());
                obj.setSite(addOrEditVo.getSite());
                obj.setSellerSku(addOrEditVo.getSellerSku());
                long count = somWayfairMultiparcelConfigMapper.createLambdaQuery().andEq("platform", addOrEditVo.getPlatform()).andEq("site", addOrEditVo.getSite())
                        .andEq("seller_sku", addOrEditVo.getSellerSku()).andNotEq("aid", addOrEditVo.getAid()).count();
                if (count > 0) {
                    throw new ValidateException("当前数据已维护");
                }
                obj.setMultiparcelSku(str);
                obj.setModifyNum(tokenUser.getJobNumber());
                obj.setModifyName(tokenUser.getUserName());
                obj.setModifyTime(DateTime.now().toJdkDate());
                somWayfairMultiparcelConfigMapper.updateById(obj);
            }
        }
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param pageSearchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomWayfairMultiparcelConfigVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomWayfairMultiparcelConfigVo> queryByPage(SomWayfairMultiparcelConfigPageSearchVo pageSearchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(pageSearchVo.getCurrent(), pageSearchVo.getPageSize());
        PageResult<SomWayfairMultiparcelConfigVo> pageResult = somWayfairMultiparcelConfigMapper.queryByPage(pageSearchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomWayfairMultiparcelConfigVo.class, pageSearchVo);
    }

    /**
     * queryByAid
     * 查看
     *
     * @param searchVo
     * @return {@link com.zielsmart.mc.vo.SomWayfairMultiparcelConfigVo}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public SomWayfairMultiparcelConfigVo queryByAid(SomWayfairMultiparcelConfigVo searchVo) throws ValidateException {
        if (ObjectUtil.isEmpty(searchVo) || StrUtil.isEmpty(searchVo.getAid())) {
            throw new ValidateException("参数存在空值,请检查");
        }
        SomWayfairMultiparcelConfig obj = somWayfairMultiparcelConfigMapper.createLambdaQuery().andEq("aid", searchVo.getAid()).single();
        SomWayfairMultiparcelConfigVo vo = new SomWayfairMultiparcelConfigVo();
        if (ObjectUtil.isNotEmpty(obj)) {
            vo = ConvertUtils.beanConvert(obj, SomWayfairMultiparcelConfigVo.class);
            vo.setMultiparcelSku(obj.getMultiparcelSku().replace(",", "\n"));
        }
        return vo;
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWayfairMultiparcelConfigVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || StrUtil.isEmpty(deleteVo.getAid())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somWayfairMultiparcelConfigMapper.createLambdaQuery().andEq("aid", deleteVo.getAid()).delete();
    }
}
