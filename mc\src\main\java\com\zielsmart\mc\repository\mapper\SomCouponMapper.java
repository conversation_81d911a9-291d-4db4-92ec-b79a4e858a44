package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomCoupon;
import com.zielsmart.mc.vo.SomCouponExVo;
import com.zielsmart.mc.vo.SomCouponExportVo;
import com.zielsmart.mc.vo.SomCouponImportVo;
import com.zielsmart.mc.vo.SomCouponPageSearchVo;
import com.zielsmart.mc.vo.zbpm.ZBPMCouponSubmitVo;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-04-21
 */

@SqlResource("somCoupon")
public interface SomCouponMapper extends BaseMapper<SomCoupon> {

    /**
     * querytotalCount
     * 查询总数
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomFreeAddDealExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCouponExVo> querytotalCount(@Param("searchVo") SomCouponPageSearchVo searchVo);

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult< SomCouponExVo >}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<SomCouponExVo> queryByPage(@Param("searchVo") SomCouponPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryBySiteAndSellerSku
     * 根据站点/展示码查询
     *
     * @param site
     * @param sellerSkuList
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponImportVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCouponImportVo> queryBySiteAndSellerSkus(@Param("site") String site, @Param("sellerSkuList") List<String> sellerSkuList);

    /**
     * queryexportData
     * 查询导出数据
     *
     * @param exportVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponImportVo>}
     * <AUTHOR>
     * @history
     */
    List<SomCouponExportVo> queryExportData(@Param("exportVo") SomCouponPageSearchVo exportVo);

    /**
     * queryByAids
     * 通过aid查询数据
     *
     * @param aidList
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomCouponImportVo>}
     * <AUTHOR>
     * @history
     */
    List<ZBPMCouponSubmitVo> queryByAids(@Param("aidList") List<String> aidList);

    /**
     * batchSubmit
     * 批量提报
     *
     * @param couponList
     * <AUTHOR>
     * @history
     */
    default void batchSubmit(@Param("aidList") List<SomCoupon> couponList) {
        this.getSQLManager().updateBatch(SqlId.of("somCoupon.batchSubmit"), couponList);
    }

    /**
     * batchUpdate
     * 批量更新
     *
     * @param couponList
     * <AUTHOR>
     * @history
     */
    default void batchUpdate(@Param("aidList") List<SomCoupon> couponList) {
        this.getSQLManager().updateBatch(SqlId.of("somCoupon.batchUpdate"), couponList);
    }

    /**
     * batchFeedbackResult
     * 批量反馈提报结果
     *
     * @param couponList
     * <AUTHOR>
     * @history
     */
    default void batchFeedbackResult(@Param("couponList") List<SomCoupon> couponList) {
        this.getSQLManager().updateBatch(SqlId.of("somCoupon.batchFeedbackResult"), couponList);
    }

    /**
     * batchAudit
     * 批量审核通过数据
     * @param couponList
     * <AUTHOR>
     * @history
     */
    default void batchAudit(@Param("aidList") List<SomCoupon> couponList) {
        this.getSQLManager().updateBatch(SqlId.of("somCoupon.batchAudit"), couponList);
    }

    /**
     * batchAuditWithFailMsg
     * 批量审核未通过数据
     * @param couponList
     * <AUTHOR>
     * @history
     */
    default void batchAuditWithFailMsg(@Param("aidList") List<SomCoupon> couponList) {
        this.getSQLManager().updateBatch(SqlId.of("somCoupon.batchAuditWithFailMsg"), couponList);
    }
}
