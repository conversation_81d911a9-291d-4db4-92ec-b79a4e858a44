package com.zielsmart.mc.controller;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.service.ZlccAmazonListingService;
import com.zielsmart.mc.vo.ZlccAmazonListingPageSearchVo;
import com.zielsmart.mc.vo.ZlccAmazonListingVo;
import com.zielsmart.mc.vo.ZlccAmazonMdmVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title ZlccAmazonListingController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/zlccAmazonListing", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "商品中心亚马逊Listing管理")
public class ZlccAmazonListingController extends BasicController {

    @Resource
    ZlccAmazonListingService zlccAmazonListingService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< ZlccAmazonListingVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<ZlccAmazonListingVo>> queryByPage(@RequestBody ZlccAmazonListingPageSearchVo searchVo) {
        return ResultVo.ofSuccess(zlccAmazonListingService.queryByPage(searchVo));
    }

    @Operation(summary = "获取前端ui json")
    @PostMapping(value = "/ui-json")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<Map> uiJson(@RequestBody ZlccAmazonListingVo vo) throws JsonProcessingException, ValidateException, SQLException {
        return ResultVo.ofSuccess(zlccAmazonListingService.uiJson(vo));
    }

    @Operation(summary = "ProductId(EAN/ASIN)校验  传值{'value':'xx','type':'EAN'}")
    @PostMapping(value = "/check-product-id")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> checkProductId(@RequestBody Map jsonMap) throws Exception {
        zlccAmazonListingService.checkProductId(jsonMap);
        return ResultVo.ofSuccess(null);
    }

    /**
     * save
     *
     * @param zlccAmazonListingVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        zlccAmazonListingService.save(zlccAmazonListingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "发布listing")
    @PostMapping(value = "/publish")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> publish(@RequestBody ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, JsonProcessingException {
        zlccAmazonListingService.publish(zlccAmazonListingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param zlccAmazonListingVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        zlccAmazonListingService.delete(zlccAmazonListingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultVo<String> export(@RequestBody ZlccAmazonListingPageSearchVo searchVo) {
        String data = zlccAmazonListingService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }

    @Operation(summary = "查看发布记录")
    @PostMapping(value = "/record")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<ZlccAmazonListingVo> record(@RequestBody ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        return ResultVo.ofSuccess(zlccAmazonListingService.record(zlccAmazonListingVo, tokenUser));
    }

    @Operation(summary = "恢复指定记录")
    @PostMapping(value = "/recover")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> recover(@RequestBody ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        zlccAmazonListingService.recover(zlccAmazonListingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "获取单条ListingItem")
    @PostMapping(value = "/item")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> item(@RequestBody ZlccAmazonListingVo zlccAmazonListingVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException, SQLException {
        zlccAmazonListingService.item(zlccAmazonListingVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
