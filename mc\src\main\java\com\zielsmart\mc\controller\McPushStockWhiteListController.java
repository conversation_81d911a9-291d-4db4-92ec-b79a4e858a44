package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McPushStockWhiteListService;
import com.zielsmart.mc.vo.McPushStockWhiteListPageSearchVo;
import com.zielsmart.mc.vo.McPushStockWhiteListVo;
import com.zielsmart.mc.vo.SomAmazonFbaSearchVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McPushStockWhiteListController
 * @description
 * @date 2021-12-22 10:33:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcPushStockWhiteList", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "库存推送白名单管理")
public class McPushStockWhiteListController extends BasicController{

    @Resource
    McPushStockWhiteListService mcPushStockWhiteListService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< McPushStockWhiteListVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<McPushStockWhiteListVo>> queryByPage(@RequestBody McPushStockWhiteListPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcPushStockWhiteListService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param mcPushStockWhiteListVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated McPushStockWhiteListVo mcPushStockWhiteListVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcPushStockWhiteListService.save(mcPushStockWhiteListVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param mcPushStockWhiteListVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody McPushStockWhiteListVo mcPushStockWhiteListVo) throws ValidateException {
        mcPushStockWhiteListService.delete(mcPushStockWhiteListVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载导入模板
     * @param
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel(){
        return "forward:/static/excel/PushStockWhiteListTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"平台", "站点", "展示码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<McPushStockWhiteListVo> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McPushStockWhiteListVo.class, params);
        }catch (Exception e){
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if(CollectionUtil.isEmpty(list)){
            throw  new ValidateException("导入数据为空,请检查数据");
        }
        mcPushStockWhiteListService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody McPushStockWhiteListPageSearchVo searchVo) {
        String data = mcPushStockWhiteListService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
