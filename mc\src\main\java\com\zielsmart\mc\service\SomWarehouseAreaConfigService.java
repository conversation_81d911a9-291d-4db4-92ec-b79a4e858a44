package com.zielsmart.mc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McWarehouse;
import com.zielsmart.mc.repository.entity.SomWarehouseAreaConfig;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McWarehouseMapper;
import com.zielsmart.mc.repository.mapper.SomWarehouseAreaConfigMapper;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigPageSearchVo;
import com.zielsmart.mc.vo.SomWarehouseAreaConfigVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import org.apache.commons.lang3.StringUtils;
import org.beetl.sql.core.DynamicSqlManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomWarehouseAreaConfigService {

    @Resource
    private SomWarehouseAreaConfigMapper somWarehouseAreaConfigMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McWarehouseMapper warehouseMapper;
    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    /**
     * save
     * 添加
     *
     * @param somWarehouseAreaConfigVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void save(SomWarehouseAreaConfigVo somWarehouseAreaConfigVo, TokenUserInfo tokenUser) throws ValidateException {
        // 非空校验
        if (ObjectUtil.isEmpty(somWarehouseAreaConfigVo) || StringUtils.isEmpty(somWarehouseAreaConfigVo.getAreaCode()) || CollectionUtil.isEmpty(somWarehouseAreaConfigVo.getWarehouseCodeList())||ObjectUtil.isEmpty(somWarehouseAreaConfigVo.getBusinessType())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        if (StrUtil.isNotEmpty(somWarehouseAreaConfigVo.getAid())) {
            somWarehouseAreaConfigMapper.createLambdaQuery().andEq("area_code", somWarehouseAreaConfigVo.getAreaCode()).andEq("site",somWarehouseAreaConfigVo.getSite()).andEq("business_type", somWarehouseAreaConfigVo.getBusinessType()).delete();
        } else {
            // 唯一性校验
            long count = somWarehouseAreaConfigMapper.createLambdaQuery().andEq("area_code", somWarehouseAreaConfigVo.getAreaCode()).andEq("site",somWarehouseAreaConfigVo.getSite()).andEq("business_type", somWarehouseAreaConfigVo.getBusinessType()).count();
            if (count > 0) {
                throw new ValidateException("已存在该仓库区域的数据，不允许重复维护");
            }
        }
        // 保存数据
        List<SomWarehouseAreaConfigVo> voList = new ArrayList<>();
        somWarehouseAreaConfigVo.getWarehouseCodeList().stream().forEach(f -> {
            SomWarehouseAreaConfigVo vo = new SomWarehouseAreaConfigVo();
            vo.setAid(IdUtil.fastSimpleUUID());
            vo.setAreaCode(somWarehouseAreaConfigVo.getAreaCode());
            vo.setWarehouseCode(f);
            vo.setSite(somWarehouseAreaConfigVo.getSite());
            vo.setCreateNum(tokenUser.getJobNumber());
            vo.setCreateName(tokenUser.getUserName());
            vo.setCreateTime(DateTime.now().toJdkDate());
            vo.setBusinessType(somWarehouseAreaConfigVo.getBusinessType());
            voList.add(vo);
        });
        somWarehouseAreaConfigMapper.insertBatch(ConvertUtils.listConvert(voList, SomWarehouseAreaConfig.class));
    }


    /**
     * query
     *
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomWarehouseAreaConfigVo>}
     * <AUTHOR>
     * @history
     */
    public List<SomWarehouseAreaConfigVo> query(SomWarehouseAreaConfigPageSearchVo searchVo) {
        List<SomWarehouseAreaConfigVo> resultList = dynamicSqlManager.getMapper(SomWarehouseAreaConfigMapper.class).query(searchVo);
        if (CollectionUtil.isNotEmpty(resultList)) {
            List<SomWarehouseAreaConfigVo> result = new ArrayList<>();
            List<McWarehouse> warehouseList = warehouseMapper.all();
            List<McDictionaryInfo> dictionaryInfoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "WarehouseArea").select();
            List<McDictionaryInfo> businessTypeDicInfoList = dictionaryInfoMapper.createLambdaQuery().andEq("item_type_code", "SfpBusinessType").select();
            Map<String, List<SomWarehouseAreaConfigVo>> map = resultList.stream().collect(Collectors.groupingBy(v->v.getSite()+v.getAreaCode()+v.getBusinessType()));
            for (String key : map.keySet()) {
                List<String> warehouseNameList = new ArrayList<>();
                SomWarehouseAreaConfigVo vo = new SomWarehouseAreaConfigVo();
                List<SomWarehouseAreaConfigVo> voList = map.get(key);
                voList.stream().forEach(f -> {
                    warehouseList.stream().filter(s -> StrUtil.equalsIgnoreCase(f.getWarehouseCode(), s.getWarehouseCode())).findFirst().ifPresent(p -> {
                        warehouseNameList.add(p.getWarehouseName());
                    });
                });
                SomWarehouseAreaConfigVo somWarehouseAreaConfigVo = voList.get(0);
                vo.setAid(somWarehouseAreaConfigVo.getAid());
                vo.setAreaCode(somWarehouseAreaConfigVo.getAreaCode());
                vo.setSite(somWarehouseAreaConfigVo.getSite());
                dictionaryInfoList.stream().filter(f-> StrUtil.equalsIgnoreCase(f.getItemValue(),somWarehouseAreaConfigVo.getAreaCode())).findFirst().ifPresent(p ->{
                    vo.setAreaName(p.getItemLable());
                });
                vo.setWarehouseCodeList(voList.stream().map(m -> m.getWarehouseCode()).distinct().collect(Collectors.toList()));
                vo.setWarehouseNameList(warehouseNameList);
                vo.setCreateName(voList.get(0).getCreateName());
                vo.setCreateNum(voList.get(0).getCreateNum());
                vo.setCreateTime(voList.get(0).getCreateTime());
                vo.setBusinessType(voList.get(0).getBusinessType());
                businessTypeDicInfoList.stream().filter(f-> StrUtil.equalsIgnoreCase(f.getItemValue(),somWarehouseAreaConfigVo.getBusinessType().toString())).findFirst().ifPresent(p ->{
                    vo.setBusinessTypeName(p.getItemLable());
                });
                result.add(vo);
            }
            return result;
        }
        return resultList;
    }


    /**
     * delete
     * 删除
     *
     * @param vo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomWarehouseAreaConfigPageSearchVo vo) throws ValidateException {
        if (ObjectUtil.isEmpty(vo) || StrUtil.isEmpty(vo.getAreaCode())||StrUtil.isEmpty(vo.getSite())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somWarehouseAreaConfigMapper.createLambdaQuery().andEq("area_code", vo.getAreaCode()).andEq("site",vo.getSite()).delete();
    }
}
