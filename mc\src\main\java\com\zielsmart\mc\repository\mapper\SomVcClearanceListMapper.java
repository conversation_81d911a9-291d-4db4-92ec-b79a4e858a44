package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomVcClearanceList;
import com.zielsmart.mc.vo.SomVcClearanceListPageSearchVo;
import com.zielsmart.mc.vo.SomVcClearanceListVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
/*
* 
* gen by 代码生成器 mapper 2025-01-07
*/

@SqlResource("somVcClearanceList")
public interface SomVcClearanceListMapper extends BaseMapper<SomVcClearanceList> {
    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo    查询参数
     * @param pageRequest 分页参数
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomTemuClearanceListVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<SomVcClearanceListVo> queryByPage(@Param("searchVo") SomVcClearanceListPageSearchVo searchVo, PageRequest pageRequest);
}
