package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2022-10-26
 */

@Table(name = "mc.sys_dept_neweya")
public class SysDeptNeweya implements java.io.Serializable {
    @AssignID
    private String aid;
    @Column("dept_sap_num")
    private String deptSapNum;
    @Column("resource_flag")
    private Integer resourceFlag;
    @Column("syn_record_id")
    private Integer synRecordId;
    @Column("dept_code")
    private String deptCode;
    @Column("dept_name_cn")
    private String deptNameCn;
    @Column("dept_name_en")
    private String deptNameEn;
    @Column("parent_dept_id")
    private Integer parentDeptId;
    @Column("manager_id")
    private Integer managerId;
    @Column("manager_emp_num")
    private String managerEmpNum;
    @Column("company_id")
    private Integer companyId;
    @Column("company_code")
    private String companyCode;
    @Column("display_seq")
    private Integer displaySeq;
    @Column("is_enabled")
    private Integer isEnabled;
    @Column("start_time")
    private Date startTime;
    @Column("end_time")
    private Date endTime;
    @Column("create_num")
    private String createNum;
    @Column("create_time")
    private Date createTime;
    @Column("remark_desc")
    private String remarkDesc;
    @Column("deptflag")
    private Integer deptflag;
    @Column("create_name")
    private String createName;
    @Column("bus_id")
    private Integer busId;
    @Column("bus_emp_num")
    private String busEmpNum;

    public SysDeptNeweya() {
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getDeptSapNum() {
        return deptSapNum;
    }

    public void setDeptSapNum(String deptSapNum) {
        this.deptSapNum = deptSapNum;
    }

    public Integer getResourceFlag() {
        return resourceFlag;
    }

    public void setResourceFlag(Integer resourceFlag) {
        this.resourceFlag = resourceFlag;
    }

    public Integer getSynRecordId() {
        return synRecordId;
    }

    public void setSynRecordId(Integer synRecordId) {
        this.synRecordId = synRecordId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptNameCn() {
        return deptNameCn;
    }

    public void setDeptNameCn(String deptNameCn) {
        this.deptNameCn = deptNameCn;
    }

    public String getDeptNameEn() {
        return deptNameEn;
    }

    public void setDeptNameEn(String deptNameEn) {
        this.deptNameEn = deptNameEn;
    }

    public Integer getParentDeptId() {
        return parentDeptId;
    }

    public void setParentDeptId(Integer parentDeptId) {
        this.parentDeptId = parentDeptId;
    }

    public Integer getManagerId() {
        return managerId;
    }

    public void setManagerId(Integer managerId) {
        this.managerId = managerId;
    }

    public String getManagerEmpNum() {
        return managerEmpNum;
    }

    public void setManagerEmpNum(String managerEmpNum) {
        this.managerEmpNum = managerEmpNum;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getDisplaySeq() {
        return displaySeq;
    }

    public void setDisplaySeq(Integer displaySeq) {
        this.displaySeq = displaySeq;
    }

    public Integer getisEnabled() {
        return isEnabled;
    }

    public void setisEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCreateNum() {
        return createNum;
    }

    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemarkDesc() {
        return remarkDesc;
    }

    public void setRemarkDesc(String remarkDesc) {
        this.remarkDesc = remarkDesc;
    }

    public Integer getDeptflag() {
        return deptflag;
    }

    public void setDeptflag(Integer deptflag) {
        this.deptflag = deptflag;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getBusId() {
        return busId;
    }

    public void setBusId(Integer busId) {
        this.busId = busId;
    }

    public String getBusEmpNum() {
        return busEmpNum;
    }

    public void setBusEmpNum(String busEmpNum) {
        this.busEmpNum = busEmpNum;
    }
}
