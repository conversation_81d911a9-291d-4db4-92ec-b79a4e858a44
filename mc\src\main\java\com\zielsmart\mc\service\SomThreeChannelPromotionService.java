package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McSkuInfo;
import com.zielsmart.mc.repository.entity.SomThreeChannelPromotion;
import com.zielsmart.mc.repository.mapper.McDictionaryInfoMapper;
import com.zielsmart.mc.repository.mapper.McProductSalesMapper;
import com.zielsmart.mc.repository.mapper.McSkuInfoMapper;
import com.zielsmart.mc.repository.mapper.SomThreeChannelPromotionMapper;
import com.zielsmart.mc.vo.*;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zielsmart.mc.McConstants.NEED_CHECK;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description 三方渠道营销活动管理
 * @date 2025-05-13 14:16:30
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomThreeChannelPromotionService {

    @Resource
    private SomThreeChannelPromotionMapper somThreeChannelPromotionMapper;

    @Resource
    private McDictionaryInfoMapper dictionaryInfoMapper;

    @Resource
    private McProductSalesMapper productSalesMapper;

    @Resource
    private SomVcPromotionService vcPromotionService;

    @Resource
    private McSkuInfoMapper skuInfoMapper;

    /**
     * 分页查询
     *
     * @param searchVo 入参
     * @return 营销活动集合
     */
    public PageVo<SomThreeChannelPromotionVo> queryByPage(SomThreeChannelPromotionPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomThreeChannelPromotionVo> pageResult = somThreeChannelPromotionMapper.queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomThreeChannelPromotionVo.class, searchVo);
    }

    /**
     * 删除
     *
     * @param somThreeChannelPromotionVo 入参
     * @throws ValidateException ex
     */
    public void delete(SomThreeChannelPromotionVo somThreeChannelPromotionVo) throws ValidateException {
        if (ObjectUtil.isEmpty(somThreeChannelPromotionVo) || CollUtil.isEmpty(somThreeChannelPromotionVo.getAids())) {
            throw new ValidateException("请选择要删除的数据");
        }
        somThreeChannelPromotionMapper.createLambdaQuery().andIn("aid", somThreeChannelPromotionVo.getAids()).delete();
    }

    /**
     * 导出
     *
     * @param searchVo 入参
     * @return Base64编码
     */
    public String export(SomThreeChannelPromotionPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomThreeChannelPromotionVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            Workbook workbook;
            try {
                ExportParams params = new ExportParams(null, "三方渠道营销活动管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomThreeChannelPromotionVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] byteArray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(byteArray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 导入
     *
     * @param importVos 导入行数据
     * @param tokenUser 当前登录用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(List<SomThreeChannelPromotionImportVo> importVos, TokenUserInfo tokenUser) throws ValidateException {
        // 核验导入的必须是同一平台
        long count = importVos.stream().map(SomThreeChannelPromotionImportVo::getPlatform).distinct().count();
        if (count > 1) {
            throw new ValidateException("导入数据存在多个平台，请检查数据！");
        }
        boolean isShein = "Shein".equalsIgnoreCase(importVos.get(0).getPlatform());
        importVos.forEach(data -> data.setShein(isShein));
        // 核验数据，获取错误信息
        List<String> errors = checkImportData(importVos);
        if (CollUtil.isNotEmpty(errors)) {
            throw new ValidateException(String.join("\n", errors));
        }
        // 数据处理：更新数据
        Date now = DateTime.now().toJdkDate();
        List<SomThreeChannelPromotion> threeChannelPromotions = new ArrayList<>();
        for (SomThreeChannelPromotionImportVo importVo : importVos) {
            SomThreeChannelPromotion threeChannelPromotion = ConvertUtils.beanConvert(importVo, SomThreeChannelPromotion.class);
            // 销售视图信息
            McProductSalesVo productSalesVo = importVo.getProductSalesVo();
            threeChannelPromotion.setSku(productSalesVo.getProductMainCode());
            threeChannelPromotion.setSalesGroupCode(productSalesVo.getSalesGroupCode());
            threeChannelPromotion.setSalesGroupName(productSalesVo.getSalesGroupName());
            threeChannelPromotion.setOperationEmptCode(productSalesVo.getOperationEmptCode());
            threeChannelPromotion.setOperationEmptName(productSalesVo.getOperationEmptName());
            threeChannelPromotion.setSalesGroupEmptCode(productSalesVo.getSalesGroupEmptCode());
            threeChannelPromotion.setSalesGroupEmptName(productSalesVo.getSalesGroupEmptName());

            threeChannelPromotion.setAid(IdUtil.fastSimpleUUID());
            threeChannelPromotion.setCreateNum(tokenUser.getJobNumber());
            threeChannelPromotion.setCreateName(tokenUser.getUserName());
            threeChannelPromotion.setCreateTime(now);
            threeChannelPromotions.add(threeChannelPromotion);
        }
        somThreeChannelPromotionMapper.insertBatch(threeChannelPromotions);
    }

    /**
     * 核验三方渠道导入数据
     *
     * @param importVos 导入行数据
     * @return 错误信息集合
     */
    private List<String> checkImportData(List<SomThreeChannelPromotionImportVo> importVos) {
        List<String> errors = new ArrayList<>();
        Map<String, Map<String, McDictionaryInfo>> dictionaryMap = getImportDictionaryMap();
        // 查询导入的销售视图，[key:site+sellerSku, value:销售视图]
        Map<String, McProductSalesVo> productSalesMap = queryImportProductSales(importVos);
        // 查询BU，BU需要根据销售视图去查询，[key:sku，value:产品基础视图]
        Map<String, McSkuInfo> mcSkuInfoMap = queryImportSkuInfo(productSalesMap);
        // 核验重复
        Set<String> repeatCheckSet = new HashSet<>();
        for (SomThreeChannelPromotionImportVo importVo : importVos) {
            // 基础核验，核验必填项，填写的字段是否正确
            if (!checkImportFields(importVo, dictionaryMap, errors, repeatCheckSet)) {
                continue;
            }
            // 核验活动是否存在
            if (checkPromotionExist(importVo)) {
                errors.add(StrUtil.concat(true, "错误20：", importVo.getUniqueMsg(), "该产品已存在营销活动，为了防止叠加折扣，不允许重复新增！"));
                continue;
            }
            // 核验销售视图是否存在
            if (!checkImportProductSales(importVo, productSalesMap)) {
                errors.add(StrUtil.concat(true, "错误21：", importVo.getUniqueMsg(), "该产品销售视图不存在！"));
                continue;
            }
            // BU组信息
            McProductSalesVo productSalesVo = importVo.getProductSalesVo();
            String productMainCode = productSalesVo.getProductMainCode();
            if (mcSkuInfoMap.containsKey(productMainCode)) {
                McSkuInfo mcSkuInfo = mcSkuInfoMap.get(productMainCode);
                importVo.setProductGroupCode(mcSkuInfo.getProductGroupCode());
                importVo.setProductGroupName(mcSkuInfo.getProductGroupName());
            }
            // 处理导入价格
            handleImportPrice(importVo);
        }
        return errors;

    }

    /**
     * 处理导入价格
     *
     * @param importVo 行数据
     */
    private void handleImportPrice(SomThreeChannelPromotionImportVo importVo) {
        // 「折扣比例」或者「秒杀价格」任填其一，需要计算出另一个的值，如果两个都填写了，忽略，秒杀价格 = 现售价 * (1 - 折扣比例)
        if (importVo.getDiscount() == null && importVo.getDealPrice() != null) {
            //「折扣」 = 1 - 秒杀价格 / 现售价
            BigDecimal temp = importVo.getDealPrice().divide(importVo.getSellPrice(), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal discount = BigDecimal.ONE.subtract(temp).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setDiscount(discount);
        } else if (importVo.getDiscount() != null && importVo.getDealPrice() == null) {
            // 「秒杀价格」 = 现售价 * (1 - 折扣比例)
            BigDecimal dealPrice = importVo.getSellPrice().multiply(BigDecimal.ONE.subtract(importVo.getDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            importVo.setDealPrice(dealPrice);
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        } else {
            // 折扣比例，文件中"15%" 接收"0.15" 数据库"15"
            importVo.setDiscount(importVo.getDiscount().multiply(new BigDecimal("100")));
        }
    }

    /**
     * 核验导入销售视图是
     *
     * @param importVo 行数据
     * @param productSalesMap 销售视图
     * @return boolean
     */
    private boolean checkImportProductSales(SomThreeChannelPromotionImportVo importVo, Map<String, McProductSalesVo> productSalesMap) {
        McProductSalesVo productSalesVo = null;
        String platform = importVo.getPlatform();
        String site = importVo.getSite();
        String sellerSku = importVo.getSellerSku();
        // Wayfair 平台，站点 Wayfair.com/Wayfair.com-cg 需要特殊处理
        String productSalesExistKey = StrUtil.concat(true, site, sellerSku);
        String wayfairPlatform = "Wayfair";
        List<String> needHandleSites = Arrays.asList("Wayfair.com", "Wayfair.com-cg");
        if (wayfairPlatform.equals(platform) && needHandleSites.contains(site)) {
            productSalesVo = productSalesMap.get(productSalesExistKey);
            if (productSalesVo == null) {
                productSalesExistKey = StrUtil.concat(true, "Wayfair.com".equals(site) ? "Wayfair.com-cg" : "Wayfair.com", sellerSku);
                productSalesVo = productSalesMap.get(productSalesExistKey);
            }
        } else {
            productSalesVo = productSalesMap.get(productSalesExistKey);
        }
        if (productSalesVo == null) {
            return false;
        }
        importVo.setProductSalesVo(productSalesVo);
        return true;


    }

    /**
     * 核验活动是否存在
     *
     * @param importVo 导入行数据
     * @return boolean
     */
    private boolean checkPromotionExist(SomThreeChannelPromotionImportVo importVo) {
        SomThreeChannelPromotion somThreeChannelPromotion = new SomThreeChannelPromotion();
        somThreeChannelPromotion.setPlatform(importVo.getPlatform());
        somThreeChannelPromotion.setSite(importVo.getSite());
        somThreeChannelPromotion.setSellerSku(importVo.getSellerSku());
        somThreeChannelPromotion.setPromotionType(importVo.getPromotionType());
        somThreeChannelPromotion.setStartDate(importVo.getStartDate());
        somThreeChannelPromotion.setEndDate(importVo.getEndDate());
        somThreeChannelPromotion.setAccountId(importVo.isShein() ? importVo.getAccountId() : null);
        return somThreeChannelPromotionMapper.checkPromotionExist(somThreeChannelPromotion) > 0;
    }


    /**
     * 三方渠道活动导入行数据基础核验
     *
     * @param importVo       行数据
     * @param dictionaryMap  字典map
     * @param errors         错误集合
     * @param repeatCheckSet 核验重复Set
     * @return boolean
     */
    private boolean checkImportFields(SomThreeChannelPromotionImportVo importVo,
                                      Map<String, Map<String, McDictionaryInfo>> dictionaryMap,
                                      List<String> errors,
                                      Set<String> repeatCheckSet) {
        // 核验必填项
        String platform = importVo.getPlatform();
        String site = importVo.getSite();
        String sellerSku = importVo.getSellerSku();
        String promotionType = importVo.getPromotionType();
        String sellPriceStr = importVo.getSellPriceStr();
        String currency = importVo.getCurrency();
        String applyReasonStr = importVo.getApplyReasonStr();
        Date startDate = importVo.getStartDate();
        Date endDate = importVo.getEndDate();
        if (!(StrUtil.isAllNotEmpty(platform, site, sellerSku, promotionType, sellPriceStr, currency, applyReasonStr) && startDate != null && endDate != null)) {
            errors.add("错误0：请输入必填项！");
            return false;
        }
        // 核验店铺ID是否必填
        if (importVo.isShein() && StrUtil.isEmpty(importVo.getAccountId())) {
            errors.add("错误1：Shein平台店铺ID不能为空！");
            return false;
        }
        // 折扣比例、秒杀价格不能都为空
        String discountStr = importVo.getDiscountStr();
        String dealPriceStr = importVo.getDealPriceStr();
        if (StrUtil.isAllEmpty(discountStr, dealPriceStr)) {
            errors.add("错误2：折扣比例、秒杀价格不能都为空！");
            return false;
        }
        // 核验重复，以[平台+站点+展示码+活动类型]唯一
        String key = StrUtil.concat(true, site, sellerSku, promotionType);
        if (repeatCheckSet.contains(key)) {
            errors.add(StrUtil.concat(true, "错误3：", importVo.getUniqueMsg(), "数据重复！"));
            return false;
        }
        repeatCheckSet.add(key);
        // 核验活动日期
        try {
            vcPromotionService.checkPromotionDate(startDate, endDate, false);
        } catch (ValidateException ex) {
            errors.add(StrUtil.concat(true, "错误4：", importVo.getUniqueMsg(), ex.getMessage()));
            return false;
        }
        // 核验申请原因
        Map<String, McDictionaryInfo> applyReasonDictMap = dictionaryMap.get("VcPromotionApplyReason");
        McDictionaryInfo applyReasonDict = applyReasonDictMap.get(applyReasonStr);
        if (applyReasonDict == null) {
            errors.add(StrUtil.concat(true, "错误5：申请原因[", applyReasonStr, "]有误！"));
            return false;
        }
        importVo.setApplyReason(Integer.valueOf(applyReasonDict.getItemValue()));
        // 如果申请原因字典 itemValue=1 时，需要填写自定义申请原因
        String customReason = importVo.getCustomReason();
        if (NEED_CHECK.equals(applyReasonDict.getItemValue1()) && StrUtil.isEmpty(customReason)) {
            errors.add(StrUtil.concat(true, "错误6：申请原因为[", applyReasonStr, "]时，必须填写自定义申请原因！"));
            return false;
        }
        // 核验现售价
        if (vcPromotionService.isInvalidAmount(sellPriceStr)) {
            errors.add(StrUtil.concat(true, "错误7：现售价[", sellPriceStr, "]格式有误！"));
            return false;
        }
        importVo.setSellPrice(new BigDecimal(sellPriceStr));
        // 核验折扣比例
        if (vcPromotionService.isInvalidAmount(discountStr)) {
            errors.add(StrUtil.concat(true, "错误8：折扣比例[", discountStr, "]格式有误！"));
            return false;
        }
        importVo.setDiscount(StrUtil.isEmpty(discountStr) ? null : new BigDecimal(discountStr));
        // 核验秒杀价格
        if (vcPromotionService.isInvalidAmount(dealPriceStr)) {
            errors.add(StrUtil.concat(true, "错误9：秒杀价格[", dealPriceStr, "]格式有误！"));
            return false;
        }
        importVo.setDealPrice(StrUtil.isEmpty(dealPriceStr) ? null : new BigDecimal(dealPriceStr));
        // 核验花费
        String spendStr = importVo.getSpendStr();
        if (vcPromotionService.isInvalidAmount(spendStr)) {
            errors.add(StrUtil.concat(true, "错误10：花费[", spendStr, "]格式有误！"));
            return false;
        }
        importVo.setSpend(StrUtil.isEmpty(spendStr) ? null : new BigDecimal(spendStr));
        // 成交费占比，文件中"15%" 接收"0.15" 无计算，直接转换
        String successFeeRatioStr = importVo.getSuccessFeeRatioStr();
        if (vcPromotionService.isInvalidAmount(successFeeRatioStr)) {
            errors.add(StrUtil.concat(true, "错误11：成交费占比[", successFeeRatioStr, "]格式有误！"));
            return false;
        }
        importVo.setSuccessFeeRatio(StrUtil.isEmpty(successFeeRatioStr) ? null : new BigDecimal(successFeeRatioStr).multiply(new BigDecimal("100")));
        return true;
    }

    /**
     * 查询导入数据的销售视图
     *
     * @param importVos 导入行数据
     * @return 销售视图
     */
    private Map<String, McProductSalesVo> queryImportProductSales(List<SomThreeChannelPromotionImportVo> importVos) {
        String platform = importVos.get(0).getPlatform();
        List<String> sites = importVos.stream().map(SomThreeChannelPromotionImportVo::getSite).distinct().collect(Collectors.toList());
        List<String> sellerSkus = importVos.stream().map(SomThreeChannelPromotionImportVo::getSellerSku).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (StrUtil.isEmpty(platform) || CollUtil.isEmpty(sites) || CollUtil.isEmpty(sellerSkus)) {
            return new HashMap<>();
        }
        // Wayfair 平台，站点 Wayfair.com/Wayfair.com-cg 需要特殊处理
        String wayfairPlatform = "Wayfair";
        if (wayfairPlatform.equals(platform)) {
            sites.add("Wayfair.com");
            sites.add("Wayfair.com-cg");
        }
        McProductSalesSearchVo salesSearchVo = new McProductSalesSearchVo();
        salesSearchVo.setPlatform(platform);
        salesSearchVo.setSiteList(sites);
        salesSearchVo.setDisplayProductCodeList(sellerSkus);
        List<McProductSalesVo> productSales = productSalesMapper.getProductSales(salesSearchVo);
        return productSales.stream().collect(Collectors.toMap(data -> data.getSite() + data.getDisplayProductCode(), Function.identity(), (x1, x2) -> x1));
    }

    /**
     * 查询导入的BU信息
     *
     * @param productSalesMap 销售视图map
     * @return BU map
     */
    private Map<String, McSkuInfo> queryImportSkuInfo(Map<String, McProductSalesVo> productSalesMap) {
        if (CollUtil.isEmpty(productSalesMap)) {
            return new HashMap<>();
        }
        List<String> productMainCodes = productSalesMap.values().stream().map(McProductSalesVo::getProductMainCode).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(productMainCodes)) {
            return new HashMap<>();
        }
        List<McSkuInfo> skuInfos = skuInfoMapper.createLambdaQuery()
                .andIn("sku_code", productMainCodes)
                .select();
        return skuInfos.stream().collect(Collectors.toMap(McSkuInfo::getSkuCode, Function.identity(), (x1, x2) -> x1));
    }

    /**
     * 获取导入字典，导入文件中填充的是字典的 label
     * Platform：平台
     * VcPromotionApplyReason：申请原因
     *
     * @return 字典 Map
     */
    private Map<String, Map<String, McDictionaryInfo>> getImportDictionaryMap() {
        List<String> typeCodes = Arrays.asList("Platform", "VcPromotionApplyReason");
        List<McDictionaryInfo> dicList = dictionaryInfoMapper.createLambdaQuery().andIn("item_type_code", typeCodes).select();
        return dicList.stream().collect(Collectors.groupingBy(McDictionaryInfo::getItemTypeCode, Collectors.toMap(McDictionaryInfo::getItemLable, Function.identity(), (x1, x2) -> x1)));
    }
}
