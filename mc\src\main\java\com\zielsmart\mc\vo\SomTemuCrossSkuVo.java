package com.zielsmart.mc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/*
 * Temu跨境sku的VO实体对象
 * gen by 代码生成器 2025-06-26
 */

@Data
@Schema(title = "Temu跨境sku", name = "SomTemuCrossSkuVo")
public class SomTemuCrossSkuVo implements java.io.Serializable {

    @Schema(description = "主键", name = "aid")
    private String aid;

    @Schema(description = "店铺名称(冗余)", name = "accountTag")
    private String accountTag;

    @Schema(description = "店铺ID(冗余)", name = "accountId")
    private String accountId;

    @Schema(description = "商品ID(冗余)", name = "productId")
    private Long productId;

    @Schema(description = "商品名称(冗余)", name = "productName")
    private String productName;

    @Schema(description = "站点", name = "site")
    private String site;

    @Schema(description = "货品skuId", name = "productSkuId")
    private Long productSkuId;

    @Schema(description = "展示码(sku货号)", name = "sellerSku")
    private String sellerSku;

    @Schema(description = "虚拟库存", name = "virtualStock")
    private Integer virtualStock;

    @Schema(description = "供货价", name = "supplierPrice")
    private BigDecimal supplierPrice;

    @Schema(description = "主表ID", name = "goodsId")
    private String goodsId;

    @Schema(description = "主表product_skc_id", name = "productSkcId")
    private Long productSkcId;

}
