package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomDealRuleValidationConfigService;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigPageSearchVo;
import com.zielsmart.mc.vo.SomDealRuleValidationConfigVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.zielsmart.web.basic.BasicController;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomDealRuleValidationConfigController
 * @description
 * @date 2024-01-16 11:41:58
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somDealRuleValidationConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "校验规则配置表管理")
public class SomDealRuleValidationConfigController extends BasicController {

    @Resource
    SomDealRuleValidationConfigService somDealRuleValidationConfigService;

    /**
     * 分页查询
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomDealRuleValidationConfigVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomDealRuleValidationConfigVo>> queryByPage(@RequestBody SomDealRuleValidationConfigPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somDealRuleValidationConfigService.queryByPage(searchVo));
    }

    /**
     * 新增
     *
     * @param somDealRuleValidationConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomDealRuleValidationConfigVo somDealRuleValidationConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somDealRuleValidationConfigService.save(somDealRuleValidationConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 修改
     *
     * @param somDealRuleValidationConfigVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomDealRuleValidationConfigVo somDealRuleValidationConfigVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somDealRuleValidationConfigService.update(somDealRuleValidationConfigVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 删除
     *
     * @param somDealRuleValidationConfigVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomDealRuleValidationConfigVo somDealRuleValidationConfigVo) throws ValidateException {
        somDealRuleValidationConfigService.delete(somDealRuleValidationConfigVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 查看
     *
     * @param somDealRuleValidationConfigVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomDealRuleValidationConfigVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/query-by-aid")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomDealRuleValidationConfigVo> queryByAid(@RequestBody SomDealRuleValidationConfigVo somDealRuleValidationConfigVo) throws ValidateException {
        return ResultVo.ofSuccess(somDealRuleValidationConfigService.queryByAid(somDealRuleValidationConfigVo));
    }

}
