package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McHandlingTimeConfigPageSearchVo;
import com.zielsmart.mc.vo.McHandlingTimeConfigVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-12-01
*/

@SqlResource("mcHandlingTimeConfig")
public interface McHandlingTimeConfigMapper extends BaseMapper<McHandlingTimeConfig> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McHandlingTimeConfigVo>}
     * <AUTHOR>
     * @history
     */
    PageResult<McHandlingTimeConfigVo> queryByPage(@Param("searchVo")McHandlingTimeConfigPageSearchVo searchVo, PageRequest pageRequest);
}
