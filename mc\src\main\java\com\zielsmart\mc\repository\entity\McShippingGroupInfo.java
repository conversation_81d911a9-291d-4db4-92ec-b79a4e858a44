package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 * 运费模板配置表
 * gen by 代码生成器 2022-02-09
 */

@Table(name="mc.mc_shipping_group_info")
public class McShippingGroupInfo implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 平台
	 */
	@Column("platform")
	private String platform ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 运费模板名称
	 */
	@Column("shipping_group_name")
	private String shippingGroupName ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	/**
	 * 0不可用;1可用
	 */
	@Column("is_enabled")
	private Integer isEnabled ;
	/**
	 * 以逗号分割的仓库区域
	 */
	@Column("area_name_list")
	private String areaNameList ;

	@Column("business_type")
	private Integer businessType ;

	@Column("storage_type")
	private Integer storageType ;

	@Column("sort")
	private Integer sort ;

	public McShippingGroupInfo() {
	}

	/**
	 * 主键
	 *@return
	 */
	public String getAid(){
		return  aid;
	}
	/**
	 * 主键
	 *@param  aid
	 */
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	 * 平台
	 *@return
	 */
	public String getPlatform(){
		return  platform;
	}
	/**
	 * 平台
	 *@param  platform
	 */
	public void setPlatform(String platform ){
		this.platform = platform;
	}
	/**
	 * 站点
	 *@return
	 */
	public String getSite(){
		return  site;
	}
	/**
	 * 站点
	 *@param  site
	 */
	public void setSite(String site ){
		this.site = site;
	}
	/**
	 * 运费模板名称
	 *@return
	 */
	public String getShippingGroupName(){
		return  shippingGroupName;
	}
	/**
	 * 运费模板名称
	 *@param  shippingGroupName
	 */
	public void setShippingGroupName(String shippingGroupName ){
		this.shippingGroupName = shippingGroupName;
	}
	/**
	 * 创建人工号
	 *@return
	 */
	public String getCreateNum(){
		return  createNum;
	}
	/**
	 * 创建人工号
	 *@param  createNum
	 */
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	 * 创建人姓名
	 *@return
	 */
	public String getCreateName(){
		return  createName;
	}
	/**
	 * 创建人姓名
	 *@param  createName
	 */
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	 * 创建时间
	 *@return
	 */
	public Date getCreateTime (){
		return  createTime ;
	}
	/**
	 * 创建时间
	 *@param  createTime
	 */
	public void setCreateTime (Date createTime  ){
		this.createTime  = createTime ;
	}
	/**
	 * 最后修改人工号
	 *@return
	 */
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	 * 最后修改人工号
	 *@param  lastModifyNum
	 */
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	 * 最后修改人姓名
	 *@return
	 */
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	 * 最后修改人姓名
	 *@param  lastModifyName
	 */
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	 * 最后修改时间
	 *@return
	 */
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	 * 最后修改时间
	 *@param  lastModifyTime
	 */
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getAreaNameList() {
		return areaNameList;
	}

	public void setAreaNameList(String areaNameList) {
		this.areaNameList = areaNameList;
	}

	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}

	public Integer getStorageType() {
		return storageType;
	}

	public void setStorageType(Integer storageType) {
		this.storageType = storageType;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}
}
