package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomTemuCrossPromotionService;
import com.zielsmart.mc.vo.SomTemuCrossPromotionPageSearchVo;
import com.zielsmart.mc.vo.SomTemuCrossPromotionDetailVo;
import com.zielsmart.mc.vo.SomTemuCrossPromotionVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomTemuCrossPromotionDetailController
 * @description
 * @date 2025-03-12 09:10:57
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somTemuCrossPromotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Temu跨境（香港半托）营销活动明细管理")
public class SomTemuCrossPromotionController extends BasicController{

    @Resource
    SomTemuCrossPromotionService somTemuCrossPromotionService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<SomTemuCrossPromotionVo>> queryByPage(@RequestBody SomTemuCrossPromotionPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somTemuCrossPromotionService.queryByPage(searchVo));
    }

    @Operation(summary = "查看产品")
    @PostMapping(value = "/detail")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<List<SomTemuCrossPromotionDetailVo>> detail(@RequestBody @Validated SomTemuCrossPromotionVo somTemuCrossPromotionVo) throws ValidateException {
        List<SomTemuCrossPromotionDetailVo> itemVos = somTemuCrossPromotionService.detail(somTemuCrossPromotionVo);
        return ResultVo.ofSuccess(itemVos);
    }
}
