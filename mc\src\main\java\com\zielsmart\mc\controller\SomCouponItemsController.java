package com.zielsmart.mc.controller;

import com.zielsmart.mc.service.SomCouponItemsService;
import com.zielsmart.mc.vo.SomCouponItemsPageSearchVo;
import com.zielsmart.mc.vo.SomCouponItemsVo;
import com.zielsmart.mc.vo.SomCouponSearchVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.xml.bind.ValidationException;

import com.zielsmart.web.basic.BasicController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomCouponItemsController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping(value = "/somCouponItems", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "coupon活动从表管理")
public class SomCouponItemsController extends BasicController{

    @Resource
    SomCouponItemsService somCouponItemsService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomCouponItemsVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomCouponItemsVo>> queryByPage(@RequestBody SomCouponItemsPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somCouponItemsService.queryByPage(searchVo));
    }

    /**
     * save
     *
     * @param somCouponItemsVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomCouponItemsVo somCouponItemsVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somCouponItemsService.save(somCouponItemsVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * update
     *
     * @param somCouponItemsVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "修改")
    @PostMapping(value = "/update")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomCouponItemsVo somCouponItemsVo,@Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somCouponItemsService.update(somCouponItemsVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somCouponItemsVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomCouponItemsVo somCouponItemsVo) throws ValidateException {
        somCouponItemsService.delete(somCouponItemsVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryCouponPromotionDiscount
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询coupon和promotion折扣")
    @PostMapping(value = "/queryCouponPromotionDiscount")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomCouponItemsVo> queryCouponPromotionDiscount(@RequestBody SomCouponSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somCouponItemsService.queryCouponPromotionDiscount(searchVo));
    }

    /**
     * queryDiscountList
     * 查询coupon和promotion折扣集合
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomCouponItemsVo>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询coupon和promotion折扣集合")
    @PostMapping(value = "/queryDiscountList")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomCouponItemsVo>> queryDiscountList(@RequestBody SomCouponSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somCouponItemsService.queryDiscountList(searchVo));
    }
}
