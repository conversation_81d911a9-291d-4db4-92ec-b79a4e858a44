package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* Wayfair账单发票索赔表
* gen by 代码生成器 2024-05-20
*/

@Table(name="mc.som_wayfair_invoice")
public class SomWayfairInvoice implements java.io.Serializable,Cloneable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 索赔ticket编码
	 */
	@Column("ticket_number")
	private String ticketNumber ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;

	/**
	 * 类型。字典值：10. Storage  20. Outbound Orders  30. Adjustment RTV  40.Adjustment VAS
	 */
	@Column("type")
	private Integer type ;
	/**
	 * 导入日期
	 */
	@Column("import_date")
	private Date importDate ;
	/**
	 * ticket状态，字典值：10. 未索赔 20.索赔中 30.索赔成功 99.索赔失败
	 */
	@Column("status")
	private Integer status ;
	/**
	 * 索赔日期
	 */
	@Column("dispute_date")
	private Date disputeDate ;
	/**
	 * 反馈索赔结果的日期
	 */
	@Column("feedback_date")
	private Date feedbackDate ;
	/**
	 * 索赔结果备注
	 */
	@Column("claim_result_remark")
	private String claimResultRemark ;
	/**
	 * 数据创建日期
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;

	public SomWayfairInvoice() {
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 索赔ticket编码
	*@return
	*/
	public String getTicketNumber(){
		return  ticketNumber;
	}
	/**
	* 索赔ticket编码
	*@param  ticketNumber
	*/
	public void setTicketNumber(String ticketNumber ){
		this.ticketNumber = ticketNumber;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}

	/**
	* 类型。字典值：10. Storage  20. Outbound Orders  30. Adjustment RTV  40.Adjustment VAS
	*@return
	*/
	public Integer getType(){
		return  type;
	}
	/**
	* 类型。字典值：10. Storage  20. Outbound Orders  30. Adjustment RTV  40.Adjustment VAS
	*@param  type
	*/
	public void setType(Integer type ){
		this.type = type;
	}
	/**
	* 导入日期
	*@return
	*/
	public Date getImportDate(){
		return  importDate;
	}
	/**
	* 导入日期
	*@param  importDate
	*/
	public void setImportDate(Date importDate ){
		this.importDate = importDate;
	}
	/**
	* ticket状态，字典值：10. 未索赔 20.索赔中 30.索赔成功 99.索赔失败
	*@return
	*/
	public Integer getStatus(){
		return  status;
	}
	/**
	* ticket状态，字典值：10. 未索赔 20.索赔中 30.索赔成功 99.索赔失败
	*@param  status
	*/
	public void setStatus(Integer status ){
		this.status = status;
	}
	/**
	* 索赔日期
	*@return
	*/
	public Date getDisputeDate(){
		return  disputeDate;
	}
	/**
	* 索赔日期
	*@param  disputeDate
	*/
	public void setDisputeDate(Date disputeDate ){
		this.disputeDate = disputeDate;
	}
	/**
	* 反馈索赔结果的日期
	*@return
	*/
	public Date getFeedbackDate(){
		return  feedbackDate;
	}
	/**
	* 反馈索赔结果的日期
	*@param  feedbackDate
	*/
	public void setFeedbackDate(Date feedbackDate ){
		this.feedbackDate = feedbackDate;
	}
	/**
	* 索赔结果备注
	*@return
	*/
	public String getClaimResultRemark(){
		return  claimResultRemark;
	}
	/**
	* 索赔结果备注
	*@param  claimResultRemark
	*/
	public void setClaimResultRemark(String claimResultRemark ){
		this.claimResultRemark = claimResultRemark;
	}
	/**
	* 数据创建日期
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 数据创建日期
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  createNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}


	@Override
	public SomWayfairInvoice clone() throws CloneNotSupportedException {
		return (SomWayfairInvoice)super.clone();
	}
}
