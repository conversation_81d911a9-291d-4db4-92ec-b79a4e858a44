package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zielsmart.mc.repository.entity.McStockInfo;
import com.zielsmart.mc.repository.entity.SomTiktokWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McStockInfoMapper;
import com.zielsmart.mc.repository.mapper.SomTiktokListingMapper;
import com.zielsmart.mc.repository.mapper.SomTiktokWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomTiktokListingPageSearchVo;
import com.zielsmart.mc.vo.SomTiktokListingReport;
import com.zielsmart.mc.vo.SomTiktokListingVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.postgresql.util.PGobject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2024-12-12 18:00:47
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomTiktokListingService {
    @Resource
    private SomTiktokWarehouseConfigMapper somTiktokWarehouseConfigMapper;

    @Resource
    private SomTiktokListingMapper somTiktokListingMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private McStockInfoMapper mcStockInfoMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomTiktokListingVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomTiktokListingVo> queryByPage(SomTiktokListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTiktokListingVo> pageResult = dynamicSqlManager.getMapper(SomTiktokListingMapper.class).queryByPage(searchVo, pageRequest);
        pageResult.getList().forEach(vo -> {
            PGobject skus = vo.getSkus();
            String value = skus.getValue();
            List<Map<String, Object>> skuList = JSONUtil.toBean(value, new TypeReference<List<Map<String, Object>>>() {
            }, false);
            List<SomTiktokListingVo.SkuInfo> skuInfoList = new ArrayList<>();
            BigDecimal minPrice = BigDecimal.valueOf(Long.MAX_VALUE);
            BigDecimal maxPrice = BigDecimal.ZERO;

            for (Map<String, Object> sku : skuList) {
                SomTiktokListingVo.SkuInfo skuInfo = new SomTiktokListingVo.SkuInfo();
                skuInfo.setSkuId((String) sku.get("id"));
                skuInfo.setSellerSku((String) sku.get("seller_sku"));
                Map map = (Map) sku.get("price");
                skuInfo.setCurrency(map == null ? null : (String) map.get("currency"));
                vo.setCurrency(skuInfo.getCurrency());
                skuInfo.setPrice(map == null ? null : new BigDecimal((String) map.get("tax_exclusive_price")));
                if (skuInfo.getPrice() != null && skuInfo.getPrice().compareTo(minPrice) < 0) {
                    minPrice = skuInfo.getPrice();
                }
                if (skuInfo.getPrice() != null && skuInfo.getPrice().compareTo(maxPrice) > 0) {
                    maxPrice = skuInfo.getPrice();
                }
                List<Map<String, Object>> stockList = (List) sku.get("inventory");
                stockList.stream().map(x -> (int) x.get("quantity")).reduce(Integer::sum).ifPresent(x -> skuInfo.setQuantity(x));
                vo.setTotalQuantity(vo.getTotalQuantity() + skuInfo.getQuantity());
                skuInfoList.add(skuInfo);
            }
            if (minPrice.longValue() == Long.MAX_VALUE) {
                minPrice = BigDecimal.ZERO;
            }
            vo.setMinPrice(minPrice);
            vo.setMaxPrice(maxPrice);
            vo.setSkuList(skuInfoList);
        });

        return ConvertUtils.pageConvert(pageResult, SomTiktokListingVo.class, searchVo);
    }

    public String export(SomTiktokListingPageSearchVo searchVo) {
        searchVo.setCurrent(1);
        searchVo.setPageSize(Integer.MAX_VALUE);
        List<SomTiktokListingVo> records = queryByPage(searchVo).getRecords();
        if (!records.isEmpty()) {
            List<SomTiktokListingVo> newRecords = new ArrayList<>();
            for (SomTiktokListingVo record : records) {
                List<SomTiktokListingVo.SkuInfo> skuList = record.getSkuList();
                for (SomTiktokListingVo.SkuInfo skuInfo : skuList) {
                    SomTiktokListingVo newRecord = new SomTiktokListingVo();
                    newRecord.setProductId(record.getProductId());
                    newRecord.setSkuId(skuInfo.getSkuId());
                    newRecord.setSellerSku(skuInfo.getSellerSku());
                    newRecord.setQuantity(skuInfo.getQuantity());
                    newRecord.setPrice(skuInfo.getPrice());
                    newRecord.setCreateTime(record.getCreateTime());
                    newRecord.setUpdateTime(record.getUpdateTime());
                    newRecords.add(newRecord);
                }
            }
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "TikTok listing管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTiktokListingVo.class, newRecords);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public PageVo<SomTiktokListingReport> stockReport(SomTiktokListingPageSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTiktokListingReport> pageResult = somTiktokListingMapper.stockReport(searchVo, pageRequest);

        if (!pageResult.getList().isEmpty()) {
            List<SomTiktokWarehouseConfig> warehouseList = somTiktokWarehouseConfigMapper.all();
            //根据站点分组
            Map<String, List<SomTiktokWarehouseConfig>> siteWarehouseMap = warehouseList.stream().collect(Collectors.groupingBy(x -> x.getShopCipher() + x.getWarehouseCode()));

            Set<String> warehouseCodeSet = warehouseList.stream().map(x -> x.getUseableWarehouseCode()).collect(Collectors.toSet());
            if (warehouseCodeSet.isEmpty()) {
                return PageVo.<SomTiktokListingReport>builder().build();
            }

            List<McStockInfo> stockList = mcStockInfoMapper.createLambdaQuery().andIn("warehouse_code", warehouseCodeSet).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));


            for (SomTiktokListingReport report : pageResult.getList()) {
                if (StrUtil.isNotBlank(report.getInventoryStr())) {
                    String value = report.getInventoryStr();
                    Integer safeStock = report.getSafetyStock() == null ? 0 : report.getSafetyStock();
                    List<SomTiktokListingReport.Inventory> warehouseStockList = objectMapper.readValue(value, new com.fasterxml.jackson.core.type.TypeReference<List<SomTiktokListingReport.Inventory>>() {
                    });
                    warehouseStockList.stream().forEach(obj -> {
                        String warehouseId = obj.getWarehouse_id();
                        obj.setWarehouseId(warehouseId);
                        //获取站点配置的仓库信息
                        List<SomTiktokWarehouseConfig> somTiktokWarehouseConfigs = siteWarehouseMap.get(report.getShopcipher() + warehouseId);

                        if (CollUtil.isEmpty(somTiktokWarehouseConfigs)) {
                            return;
                        }
                        obj.setWarehouseName(somTiktokWarehouseConfigs.get(0).getWarehouseName());
                        List<McStockInfo> stockListTmp = somTiktokWarehouseConfigs.stream()
                                .map(k -> stockMap.getOrDefault(k.getUseableWarehouseCode() + k.getUseableStorageCode() + report.getProductMainCode(), new McStockInfo()))
                                .collect(Collectors.toList());

                        stockListTmp.stream().filter(x -> x.getTotalStock() != null).map(x -> x.getTotalStock()).reduce(Integer::sum).ifPresent(r -> obj.setTotalQuantity(r));
                        stockListTmp.stream().filter(x -> x.getSevenDayNumber() != null).map(x -> x.getSevenDayNumber()).reduce(BigDecimal::add).ifPresent(r -> obj.setSevenNum(r));
                    });
                    warehouseStockList.stream().filter(x -> x.getTotalQuantity() != null).map(x -> x.getTotalQuantity()).reduce(Integer::sum).ifPresent(r -> report.setStock(r));
                    BigDecimal sevenSum = warehouseStockList.stream().filter(x -> x.getSevenNum() != null).map(x -> x.getSevenNum()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                    if (report.getSafetyStock() == null) {
                        report.setSafetyStock(0);
                    }
                    int safeinventory = Math.max(sevenSum.divide(BigDecimal.valueOf(2), 5, BigDecimal.ROUND_DOWN).intValue(), report.getSafetyStock());
                    if (report.getStock() == null) {
                        report.setStock(0);
                    }
                    report.setStock(Math.max(report.getStock() - safeinventory, 0));
                    report.setStockList(warehouseStockList);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTiktokListingReport.class, searchVo);
    }

    public String exportStockReport(SomTiktokListingPageSearchVo searchVo) throws JsonProcessingException {
        searchVo.setPageSize(Integer.MAX_VALUE);
        searchVo.setCurrent(1);
        PageVo<SomTiktokListingReport> pageResult = stockReport(searchVo);
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return null;
        }
        List<SomTiktokListingReport> exportVos = pageResult.getRecords();
        Workbook workbook;
        try {
            ExportParams params = new ExportParams(null, "Tiktok Listing库存报表");
            params.setType(ExcelType.XSSF);
            params.setAutoSize(true);
            workbook = ExcelExportUtil.exportExcel(params, SomTiktokListingReport.class, exportVos);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return Base64.getEncoder().encodeToString(barray);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
