package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.McWayfairCustomStockService;
import com.zielsmart.mc.vo.McWayfairCustomStockExtVo;
import com.zielsmart.mc.vo.McWayfairCustomStockPageSearchVo;
import com.zielsmart.mc.vo.McWayfairCustomStockVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title McWayfairCustomStockController
 * @description
 * @date 2021-11-24 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/mcWayfairCustomStock", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "手工指定库存管理")
public class McWayfairCustomStockController extends BasicController {

    @Resource
    McWayfairCustomStockService mcWayfairCustomStockService;

    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<PageVo<McWayfairCustomStockExtVo>> queryByPage(@RequestBody McWayfairCustomStockPageSearchVo searchVo) {
        return ResultVo.ofSuccess(mcWayfairCustomStockService.queryByPage(searchVo));
    }

    @Operation(summary = "添加")
    @PostMapping(value = "/save")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> save(@RequestBody @Validated McWayfairCustomStockVo mcWayfairCustomStockVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWayfairCustomStockService.save(mcWayfairCustomStockVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "查看")
    @PostMapping(value = "/queryById")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<McWayfairCustomStockVo> queryById(@RequestBody McWayfairCustomStockVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(mcWayfairCustomStockService.queryById(searchVo));
    }

    @Operation(summary = "编辑")
    @PostMapping(value = "/edit")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> edit(@RequestBody @Validated McWayfairCustomStockVo editVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWayfairCustomStockService.edit(editVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> delete(@RequestBody McWayfairCustomStockVo mcWayfairCustomStockVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        mcWayfairCustomStockService.delete(mcWayfairCustomStockVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/WayfairCustomStockTemplate.xlsx";
    }

    @Operation(summary = "下载批量删除模板")
    @GetMapping(value = "/batch-delete/download-template")
    public String downloadBatchDeleteExcel() {
        return "forward:/static/excel/WayfairCustomStockDeleteTemplate.xlsx";
    }

    @Operation(summary = "导入数据")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"平台", "站点", "展示码", "平台仓库编码", "指定库存"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<McWayfairCustomStockVo> list = new ArrayList<>();
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McWayfairCustomStockVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        mcWayfairCustomStockService.importExcel(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "批量删除")
    @PostMapping(value = "/batch-delete", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    @ResponseBody
    public ResultVo<String> batchDelete(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        String[] importFields = {"平台", "站点", "展示码", "平台仓库编码"};
        ImportParams params = new ImportParams();
        params.setImportFields(importFields);
        List<McWayfairCustomStockVo> list;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), McWayfairCustomStockVo.class, params);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        mcWayfairCustomStockService.batchDelete(list, tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "导出报表")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultVo<String> export(@RequestBody McWayfairCustomStockPageSearchVo searchVo) {
        String data = mcWayfairCustomStockService.export(searchVo);
        if (StrUtil.isEmpty(data)) {
            return ResultVo.ofFail("导出数据为空");
        }
        return ResultVo.ofSuccess(data);
    }
}
