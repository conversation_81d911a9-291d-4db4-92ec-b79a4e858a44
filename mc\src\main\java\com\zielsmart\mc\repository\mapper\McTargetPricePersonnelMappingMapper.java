package com.zielsmart.mc.repository.mapper;
import com.zielsmart.mc.vo.McTargetPricePersonnelMappingPageSearchVo;
import com.zielsmart.mc.vo.McTargetPricePersonnelMappingVo;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;
import com.zielsmart.mc.repository.entity.*;
/*
* 
* gen by 代码生成器 mapper 2021-11-03
*/

@SqlResource("mcTargetPricePersonnelMapping")
public interface McTargetPricePersonnelMappingMapper extends BaseMapper<McTargetPricePersonnelMapping> {
	/**
     * queryByPage
     * 分页查询
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.McTargetPricePersonnelMappingVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<McTargetPricePersonnelMappingVo> queryByPage(@Param("searchVo")McTargetPricePersonnelMappingPageSearchVo searchVo, PageRequest pageRequest);
}
