package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
/*
 * VC价格信息表
 * gen by 代码生成器 2024-08-26
 */

@Table(name = "mc.som_amazon_vc_price")
public class SomAmazonVcPrice implements java.io.Serializable {
    /**
     * 主键aid
     */
    @AssignID
    private String aid;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * 展示码
     */
    @Column("seller_sku")
    private String sellerSku;
    /**
     * SKU
     */
    @Column("sku")
    private String sku;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * 建议零售价
     */
    @Column("recommended_retail_price")
    private BigDecimal recommendedRetailPrice;
    /**
     * 币种
     */
    @Column("currency")
    private String currency;
    /**
     * 前台售价
     */
    @Column("price")
    private BigDecimal price;
    /**
     * 前台售价来源：10.BI，20.RPA
     */
    @Column("price_source")
    private Integer priceSource;
    /**
     * 是否正在参加营销活动：10.正在参加，99.未参加
     */
    @Column("on_promotion")
    private Integer onPromotion;
    /**
     * BI数据更新时间
     */
    @Column("bi_update_time")
    private Date biUpdateTime;
    /**
     * 创建人工号
     */
    @Column("create_num")
    private String createNum;
    /**
     * 创建人姓名
     */
    @Column("create_name")
    private String createName;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    /**
     * 营销活动类型
     */
    @Column("deal_type")
    private String dealType;

    /**
     * 预估促销价
     */
    @Column("estimate_deal_price")
    private BigDecimal estimateDealPrice;

    /**
     * 专款专项
     */
    @Column("funding")
    private BigDecimal funding;

    /**
     * 30天DMS
     */
    @Column("month_dms")
    private BigDecimal monthDms;

    /**
     * 30天最低价
     */
    @Column("month_minimum_price")
    private BigDecimal monthMinimumPrice;




    public SomAmazonVcPrice() {
    }

    public Integer getOnPromotion() {
        return onPromotion;
    }

    public void setOnPromotion(Integer onPromotion) {
        this.onPromotion = onPromotion;
    }

    public Date getBiUpdateTime() {
        return biUpdateTime;
    }

    public void setBiUpdateTime(Date biUpdateTime) {
        this.biUpdateTime = biUpdateTime;
    }

    public String getDealType() {
        return dealType;
    }

    public void setDealType(String dealType) {
        this.dealType = dealType;
    }

    public BigDecimal getEstimateDealPrice() {
        return estimateDealPrice;
    }

    public void setEstimateDealPrice(BigDecimal estimateDealPrice) {
        this.estimateDealPrice = estimateDealPrice;
    }

    public BigDecimal getFunding() {
        return funding;
    }

    public void setFunding(BigDecimal funding) {
        this.funding = funding;
    }

    public BigDecimal getMonthDms() {
        return monthDms;
    }

    public void setMonthDms(BigDecimal monthDms) {
        this.monthDms = monthDms;
    }

    public BigDecimal getMonthMinimumPrice() {
        return monthMinimumPrice;
    }

    public void setMonthMinimumPrice(BigDecimal monthMinimumPrice) {
        this.monthMinimumPrice = monthMinimumPrice;
    }

    /**
     * 主键aid
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键aid
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 展示码
     *
     * @return
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * 展示码
     *
     * @param sellerSku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * SKU
     *
     * @return
     */
    public String getSku() {
        return sku;
    }

    /**
     * SKU
     *
     * @param sku
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * 建议零售价
     *
     * @return
     */
    public BigDecimal getRecommendedRetailPrice() {
        return recommendedRetailPrice;
    }

    /**
     * 建议零售价
     *
     * @param recommendedRetailPrice
     */
    public void setRecommendedRetailPrice(BigDecimal recommendedRetailPrice) {
        this.recommendedRetailPrice = recommendedRetailPrice;
    }

    /**
     * 币种
     *
     * @return
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 币种
     *
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 前台售价
     *
     * @return
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 前台售价
     *
     * @param price
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 前台售价来源：10.BI，20.RPA
     *
     * @return
     */
    public Integer getPriceSource() {
        return priceSource;
    }

    /**
     * 前台售价来源：10.BI，20.RPA
     *
     * @param priceSource
     */
    public void setPriceSource(Integer priceSource) {
        this.priceSource = priceSource;
    }

    /**
     * 是否正在参加营销活动：10.正在参加，99.未参加
     *
     * @return
     */
    public Integer getonPromotion() {
        return onPromotion;
    }

    /**
     * 是否正在参加营销活动：10.正在参加，99.未参加
     *
     * @param onPromotion
     */
    public void setonPromotion(Integer onPromotion) {
        this.onPromotion = onPromotion;
    }

    /**
     * BI数据更新时间
     *
     * @return
     */
    public Date getbiUpdateTime() {
        return biUpdateTime;
    }

    /**
     * BI数据更新时间
     *
     * @param biUpdateTime
     */
    public void setbiUpdateTime(Date biUpdateTime) {
        this.biUpdateTime = biUpdateTime;
    }

    /**
     * 创建人工号
     *
     * @return
     */
    public String getCreateNum() {
        return createNum;
    }

    /**
     * 创建人工号
     *
     * @param createNum
     */
    public void setCreateNum(String createNum) {
        this.createNum = createNum;
    }

    /**
     * 创建人姓名
     *
     * @return
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     *
     * @param createName
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
