package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomAmazonFeePreview;
import com.zielsmart.mc.vo.AmazonFbaClaimVo;
import com.zielsmart.mc.vo.SomAmazonFbaSearchVo;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
import java.util.Map;
/*
* 
* gen by 代码生成器 mapper 2023-03-16
*/

@SqlResource("somAmazonFeePreview")
public interface SomAmazonFeePreviewMapper extends BaseMapper<SomAmazonFeePreview> {

    /**
     * 分页查询
     * @param searchVo
     * @param pageRequest 页面请求
     * @return @return {@link PageResult }<{@link AmazonFbaClaimVo }>
     * @author: 王帅杰
     * @date: 2023-03-16 02:56:09
     */
    PageResult<AmazonFbaClaimVo> queryByPage(@Param("searchVo") SomAmazonFbaSearchVo searchVo, PageRequest pageRequest);

    List<Map<String,String>> queryCaseUrl();
}
