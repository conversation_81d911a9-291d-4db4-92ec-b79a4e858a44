package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
 *
 * gen by 代码生成器 2022-11-30
 */

@Table(name = "mc.som_offer")
public class SomOffer implements java.io.Serializable {
    /**
     * 主键
     */
    @AssignID
    private String aid;
    /**
     * Amazon市场ID
     */
    @Column("marketplace_id")
    private String marketplaceId;
    /**
     * Amazon市场名称
     */
    @Column("marketplace_name")
    private String marketplaceName;
    /**
     * 店铺ID
     */
    @Column("seller_id")
    private String sellerId;
    /**
     * 店铺名称
     */
    @Column("seller_store_name")
    private String sellerStoreName;
    /**
     * 平台
     */
    @Column("platform")
    private String platform;
    /**
     * 站点
     */
    @Column("site")
    private String site;
    /**
     * ASIN
     */
    @Column("asin")
    private String asin;
    /**
     * ItemCondition
     */
    @Column("item_condition")
    private String itemCondition;
    /**
     * 通知更新时间
     */
    @Column("time_of_offer_change")
    private Date timeOfOfferChange;
    /**
     * 导致通知更新的类型：External Internal Featured Offer
     */
    @Column("offer_change_type")
    private String offerChangeType;
    /**
     * 创建时间
     */
    @Column("create_time")
    private Date createTime;

    public SomOffer() {
    }

    /**
     * 主键
     *
     * @return
     */
    public String getAid() {
        return aid;
    }

    /**
     * 主键
     *
     * @param aid
     */
    public void setAid(String aid) {
        this.aid = aid;
    }

    /**
     * Amazon市场ID
     *
     * @return
     */
    public String getMarketplaceId() {
        return marketplaceId;
    }

    /**
     * Amazon市场ID
     *
     * @param marketplaceId
     */
    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    /**
     * Amazon市场名称
     *
     * @return
     */
    public String getMarketplaceName() {
        return marketplaceName;
    }

    /**
     * Amazon市场名称
     *
     * @param marketplaceName
     */
    public void setMarketplaceName(String marketplaceName) {
        this.marketplaceName = marketplaceName;
    }

    /**
     * 店铺ID
     *
     * @return
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 店铺ID
     *
     * @param sellerId
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    /**
     * 店铺名称
     *
     * @return
     */
    public String getSellerStoreName() {
        return sellerStoreName;
    }

    /**
     * 店铺名称
     *
     * @param sellerStoreName
     */
    public void setSellerStoreName(String sellerStoreName) {
        this.sellerStoreName = sellerStoreName;
    }

    /**
     * 平台
     *
     * @return
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * 平台
     *
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * 站点
     *
     * @return
     */
    public String getSite() {
        return site;
    }

    /**
     * 站点
     *
     * @param site
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * ASIN
     *
     * @return
     */
    public String getAsin() {
        return asin;
    }

    /**
     * ASIN
     *
     * @param asin
     */
    public void setAsin(String asin) {
        this.asin = asin;
    }

    /**
     * ItemCondition
     *
     * @return
     */
    public String getItemCondition() {
        return itemCondition;
    }

    /**
     * ItemCondition
     *
     * @param itemCondition
     */
    public void setItemCondition(String itemCondition) {
        this.itemCondition = itemCondition;
    }

    /**
     * 通知更新时间
     *
     * @return
     */
    public Date getTimeOfOfferChange() {
        return timeOfOfferChange;
    }

    /**
     * 通知更新时间
     *
     * @param timeOfOfferChange
     */
    public void setTimeOfOfferChange(Date timeOfOfferChange) {
        this.timeOfOfferChange = timeOfOfferChange;
    }

    /**
     * 导致通知更新的类型：External Internal Featured Offer
     *
     * @return
     */
    public String getOfferChangeType() {
        return offerChangeType;
    }

    /**
     * 导致通知更新的类型：External Internal Featured Offer
     *
     * @param offerChangeType
     */
    public void setOfferChangeType(String offerChangeType) {
        this.offerChangeType = offerChangeType;
    }

    /**
     * 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
