package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.service.SomPromotionProductSelectionService;
import com.zielsmart.mc.vo.SomPromotionProductSelectionPageSearchVo;
import com.zielsmart.mc.vo.SomPromotionProductSelectionVo;
import com.zielsmart.mc.vo.SomStandardPriceImportVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomPromotionProductSelectionController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somPromotionProductSelection", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Manage Product Selection")
public class SomPromotionProductSelectionController extends BasicController {

    @Resource
    SomPromotionProductSelectionService somPromotionProductSelectionService;

    /**
     * addOrUpdate
     *
     * @param addOrUpdateVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "新增/编辑")
    @PostMapping(value = "/addOrEdit")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> addOrEdit(@RequestBody @Validated SomPromotionProductSelectionVo addOrUpdateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somPromotionProductSelectionService.addOrEdit(addOrUpdateVo, tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomPromotionProductSelectionVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomPromotionProductSelectionVo>> queryByPage(@RequestBody SomPromotionProductSelectionPageSearchVo searchVo) {
        return ResultVo.ofSuccess(somPromotionProductSelectionService.queryByPage(searchVo));
    }

    /**
     * update
     *
     * @param updateVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "作废")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomPromotionProductSelectionVo updateVo) throws ValidateException {
        somPromotionProductSelectionService.update(updateVo);
        return ResultVo.ofSuccess(null);
    }

    /**
     * 下载导入模板
     *
     * @param
     * @return {@link java.lang.String}
     * <AUTHOR>
     */
    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download-template")
    public String downloadExcel() {
        return "forward:/static/excel/ProductSelectionImportTemplate.xlsx";
    }

    /**
     * import
     * 导入
     *
     * @param file
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importExcel(@RequestParam("file") MultipartFile file, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        ImportParams importParams = new ImportParams();
        // 表头占用的行数
        importParams.setHeadRows(1);
        // 标题占用的行数
        importParams.setTitleRows(0);
        // 导入字段
        String[] arr = {"平台","站点","Product Selection Type", "Tracking ID", "Internal Description", "List"};
        importParams.setImportFields(arr);
        ExcelImportResult<SomPromotionProductSelectionVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomPromotionProductSelectionVo.class, importParams);
        } catch (Exception e) {
            if (StrUtil.equalsIgnoreCase(e.getMessage(), "不是合法的Excel模板")) {
                throw new ValidateException("导入模板有误,请检查模板");
            }
        }
        if (result.getList().isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somPromotionProductSelectionService.importExcel(result.getList(), tokenUser);
        return ResultVo.ofSuccess();
    }

    /**
     * search
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.util.List<com.zielsmart.mc.vo.SomPromotionProductSelectionVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查询")
    @PostMapping(value = "/search")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomPromotionProductSelectionVo>> search(@RequestBody SomPromotionProductSelectionVo searchVo) throws ValidateException{
        return ResultVo.ofSuccess(somPromotionProductSelectionService.search(searchVo));
    }


    /**
     * searchDetail
     * 查询明细
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<com.zielsmart.mc.vo.SomPromotionProductSelectionVo>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "查看")
    @PostMapping(value = "/searchDetail")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<SomPromotionProductSelectionVo> searchDetail(@RequestBody SomPromotionProductSelectionVo searchVo) throws ValidateException{
        return ResultVo.ofSuccess(somPromotionProductSelectionService.searchDetail(searchVo));
    }
}
