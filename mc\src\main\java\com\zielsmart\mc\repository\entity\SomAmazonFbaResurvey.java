package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;
/*
* FBA重测记录表
* gen by 代码生成器 2023-03-16
*/

@Table(name="mc.som_amazon_fba_resurvey")
public class SomAmazonFbaResurvey implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String id ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * FNSKU
	 */
	@Column("fnsku")
	private String fnsku ;
	/**
	 * asin
	 */
	@Column("asin")
	private String asin ;
	/**
	 * case_id
	 */
	@Column("case_id")
	private String caseId ;
	/**
	 * 重测长边长度 
	 */
	@Column("resurvey_longest_side")
	private BigDecimal resurveyLongestSide ;
	/**
	 * 重测中边长度 
	 */
	@Column("resurvey_median_side")
	private BigDecimal resurveyMedianSide ;
	/**
	 * 重测短边长度
	 */
	@Column("resurvey_shortest_side")
	private BigDecimal resurveyShortestSide ;
	/**
	 * 商品最长边的长度（含包装）加上围长,计算公式：2 x（中长边 + 最短边）
	 */
	@Column("resurvey_length_and_girth")
	private BigDecimal resurveyLengthAndGirth ;
	/**
	 * 重测长度单位
	 */
	@Column("resurvey_unit_of_dimension")
	private String resurveyUnitOfDimension ;
	/**
	 * 重测单个带包装的重量
	 */
	@Column("resurvey_item_package_weight")
	private BigDecimal resurveyItemPackageWeight ;
	/**
	 * 重测重量单位
	 */
	@Column("resurvey_unit_of_weight")
	private String resurveyUnitOfWeight ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 重测发货费用,调用存储过程
	 */
	@Column("resurvey_standard_freught_amount")
	private BigDecimal resurveyStandardFreughtAmount ;
	/**
	 * 重测完成时间
	 */
	@Column("resurvey_date")
	private Date resurveyDate ;
	/**
	 * 重测结果，字典值：10.已改为正常尺寸  99.未改成正常尺寸
	 */
	@Column("resurvey_result")
	private Integer resurveyResult ;
	/**
	 * 备注
	 */
	@Column("remark")
	private String remark ;
	/**
	 * 创建人工号
	 */
	@Column("create_num")
	private String createNum ;
	/**
	 * 创建人姓名
	 */
	@Column("create_name")
	private String createName ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最后修改人工号
	 */
	@Column("last_modify_num")
	private String lastModifyNum ;
	/**
	 * 最后修改人姓名
	 */
	@Column("last_modify_name")
	private String lastModifyName ;
	/**
	 * 最后修改时间
	 */
	@Column("last_modify_time")
	private Date lastModifyTime ;

	public SomAmazonFbaResurvey() {
	}

	/**
	* 主键
	*@return
	*/
	public String getId(){
		return  id;
	}
	/**
	* 主键
	*@param  id
	*/
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* FNSKU
	*@return
	*/
	public String getFnsku(){
		return  fnsku;
	}
	/**
	* FNSKU
	*@param  fnsku
	*/
	public void setFnsku(String fnsku ){
		this.fnsku = fnsku;
	}
	/**
	* asin
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* asin
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* case_id
	*@return
	*/
	public String getCaseId(){
		return  caseId;
	}
	/**
	* case_id
	*@param  caseId
	*/
	public void setCaseId(String caseId ){
		this.caseId = caseId;
	}
	/**
	* 重测长边长度 
	*@return
	*/
	public BigDecimal getResurveyLongestSide(){
		return  resurveyLongestSide;
	}
	/**
	* 重测长边长度 
	*@param  resurveyLongestSide
	*/
	public void setResurveyLongestSide(BigDecimal resurveyLongestSide ){
		this.resurveyLongestSide = resurveyLongestSide;
	}
	/**
	* 重测中边长度 
	*@return
	*/
	public BigDecimal getResurveyMedianSide(){
		return  resurveyMedianSide;
	}
	/**
	* 重测中边长度 
	*@param  resurveyMedianSide
	*/
	public void setResurveyMedianSide(BigDecimal resurveyMedianSide ){
		this.resurveyMedianSide = resurveyMedianSide;
	}
	/**
	* 重测短边长度
	*@return
	*/
	public BigDecimal getResurveyShortestSide(){
		return  resurveyShortestSide;
	}
	/**
	* 重测短边长度
	*@param  resurveyShortestSide
	*/
	public void setResurveyShortestSide(BigDecimal resurveyShortestSide ){
		this.resurveyShortestSide = resurveyShortestSide;
	}
	/**
	* 商品最长边的长度（含包装）加上围长,计算公式：2 x（中长边 + 最短边）
	*@return
	*/
	public BigDecimal getResurveyLengthAndGirth(){
		return  resurveyLengthAndGirth;
	}
	/**
	* 商品最长边的长度（含包装）加上围长,计算公式：2 x（中长边 + 最短边）
	*@param  resurveyLengthAndGirth
	*/
	public void setResurveyLengthAndGirth(BigDecimal resurveyLengthAndGirth ){
		this.resurveyLengthAndGirth = resurveyLengthAndGirth;
	}
	/**
	* 重测长度单位
	*@return
	*/
	public String getResurveyUnitOfDimension(){
		return  resurveyUnitOfDimension;
	}
	/**
	* 重测长度单位
	*@param  resurveyUnitOfDimension
	*/
	public void setResurveyUnitOfDimension(String resurveyUnitOfDimension ){
		this.resurveyUnitOfDimension = resurveyUnitOfDimension;
	}
	/**
	* 重测单个带包装的重量
	*@return
	*/
	public BigDecimal getResurveyItemPackageWeight(){
		return  resurveyItemPackageWeight;
	}
	/**
	* 重测单个带包装的重量
	*@param  resurveyItemPackageWeight
	*/
	public void setResurveyItemPackageWeight(BigDecimal resurveyItemPackageWeight ){
		this.resurveyItemPackageWeight = resurveyItemPackageWeight;
	}
	/**
	* 重测重量单位
	*@return
	*/
	public String getResurveyUnitOfWeight(){
		return  resurveyUnitOfWeight;
	}
	/**
	* 重测重量单位
	*@param  resurveyUnitOfWeight
	*/
	public void setResurveyUnitOfWeight(String resurveyUnitOfWeight ){
		this.resurveyUnitOfWeight = resurveyUnitOfWeight;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 重测发货费用,调用存储过程
	*@return
	*/
	public BigDecimal getResurveyStandardFreughtAmount(){
		return  resurveyStandardFreughtAmount;
	}
	/**
	* 重测发货费用,调用存储过程
	*@param  resurveyStandardFreughtAmount
	*/
	public void setResurveyStandardFreughtAmount(BigDecimal resurveyStandardFreughtAmount ){
		this.resurveyStandardFreughtAmount = resurveyStandardFreughtAmount;
	}
	/**
	* 重测完成时间
	*@return
	*/
	public Date getResurveyDate(){
		return  resurveyDate;
	}
	/**
	* 重测完成时间
	*@param  resurveyDate
	*/
	public void setResurveyDate(Date resurveyDate ){
		this.resurveyDate = resurveyDate;
	}
	/**
	* 重测结果，字典值：10.已改为正常尺寸  99.未改成正常尺寸
	*@return
	*/
	public Integer getResurveyResult(){
		return  resurveyResult;
	}
	/**
	* 重测结果，字典值：10.已改为正常尺寸  99.未改成正常尺寸
	*@param  resurveyResult
	*/
	public void setResurveyResult(Integer resurveyResult ){
		this.resurveyResult = resurveyResult;
	}
	/**
	* 备注
	*@return
	*/
	public String getRemark(){
		return  remark;
	}
	/**
	* 备注
	*@param  remark
	*/
	public void setRemark(String remark ){
		this.remark = remark;
	}
	/**
	* 创建人工号
	*@return
	*/
	public String getCreateNum(){
		return  createNum;
	}
	/**
	* 创建人工号
	*@param  ceateNum
	*/
	public void setCreateNum(String createNum ){
		this.createNum = createNum;
	}
	/**
	* 创建人姓名
	*@return
	*/
	public String getCreateName(){
		return  createName;
	}
	/**
	* 创建人姓名
	*@param  createName
	*/
	public void setCreateName(String createName ){
		this.createName = createName;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最后修改人工号
	*@return
	*/
	public String getLastModifyNum(){
		return  lastModifyNum;
	}
	/**
	* 最后修改人工号
	*@param  lastModifyNum
	*/
	public void setLastModifyNum(String lastModifyNum ){
		this.lastModifyNum = lastModifyNum;
	}
	/**
	* 最后修改人姓名
	*@return
	*/
	public String getLastModifyName(){
		return  lastModifyName;
	}
	/**
	* 最后修改人姓名
	*@param  lastModifyName
	*/
	public void setLastModifyName(String lastModifyName ){
		this.lastModifyName = lastModifyName;
	}
	/**
	* 最后修改时间
	*@return
	*/
	public Date getLastModifyTime(){
		return  lastModifyTime;
	}
	/**
	* 最后修改时间
	*@param  lastModifyTime
	*/
	public void setLastModifyTime(Date lastModifyTime ){
		this.lastModifyTime = lastModifyTime;
	}

}
