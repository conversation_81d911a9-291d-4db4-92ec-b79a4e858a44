package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;

import java.util.Date;
/*
* 乐天Listing明细
* gen by 代码生成器 2023-03-13
*/

@Table(name="mc.som_rakuten_listing")
public class SomRakutenListing implements java.io.Serializable {
	@AssignID
	private String id ;
	/**
	 * 商品管理番号
	 */
	@Column("manage_number")
	private String manageNumber ;
	/**
	 * 商品详细信息
	 */
	@Column("data_json")
	private String dataJson ;
	/**
	 * Listing创建时间
	 */
	@Column("created")
	private Date created ;
	/**
	 * Listring最后更新时间
	 */
	@Column("updated")
	private Date updated ;
	/**
	 * 调用接口下载的时间
	 */
	@Column("download_time")
	private Date downloadTime ;
	/**
	 * 番号
	 */
	@Column("item_number")
	private String itemNumber ;

	/**
	 * 展示码  sku连携管理番号
	 */
	@Column("seller_sku")
	private String sellerSku ;

	/**
	 * SKU管理番号
	 */
	@Column("variant_id")
	private String variantId ;

	public SomRakutenListing() {
	}

	public String getSellerSku() {
		return sellerSku;
	}

	public void setSellerSku(String sellerSku) {
		this.sellerSku = sellerSku;
	}

	public String getVariantId() {
		return variantId;
	}

	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}

	public String getId(){
		return  id;
	}
	public void setId(String id ){
		this.id = id;
	}
	/**
	* 商品管理番号
	*@return
	*/
	public String getManageNumber(){
		return  manageNumber;
	}
	/**
	* 商品管理番号
	*@param  manageNumber
	*/
	public void setManageNumber(String manageNumber ){
		this.manageNumber = manageNumber;
	}
	/**
	* 商品详细信息
	*@return
	*/
	public String getDataJson(){
		return  dataJson;
	}
	/**
	* 商品详细信息
	*@param  dataJson
	*/
	public void setDataJson(String dataJson ){
		this.dataJson = dataJson;
	}
	/**
	* Listing创建时间
	*@return
	*/
	public Date getCreated(){
		return  created;
	}
	/**
	* Listing创建时间
	*@param  created
	*/
	public void setCreated(Date created ){
		this.created = created;
	}
	/**
	* Listring最后更新时间
	*@return
	*/
	public Date getUpdated(){
		return  updated;
	}
	/**
	* Listring最后更新时间
	*@param  updated
	*/
	public void setUpdated(Date updated ){
		this.updated = updated;
	}
	/**
	* 调用接口下载的时间
	*@return
	*/
	public Date getDownloadTime(){
		return  downloadTime;
	}
	/**
	* 调用接口下载的时间
	*@param  downloadTime
	*/
	public void setDownloadTime(Date downloadTime ){
		this.downloadTime = downloadTime;
	}
	/**
	* 番号
	*@return
	*/
	public String getItemNumber(){
		return  itemNumber;
	}
	/**
	* 番号
	*@param  itemNumber
	*/
	public void setItemNumber(String itemNumber ){
		this.itemNumber = itemNumber;
	}

}
