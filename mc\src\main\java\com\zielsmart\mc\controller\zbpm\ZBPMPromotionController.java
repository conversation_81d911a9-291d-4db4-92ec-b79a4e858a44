package com.zielsmart.mc.controller.zbpm;

import com.zielsmart.mc.service.zbpm.ZBPMPromotionService;
import com.zielsmart.mc.vo.zbpm.ZBPMPromotionSubmitVo;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/zbpm-promotion", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Promotion活动接口")
public class ZBPMPromotionController {
    @Resource
    private ZBPMPromotionService service;

    /**
     * updatePromotion
     * 更新promotion
     * @param updateVo
     * @param tokenUser
     * @return {@link com.zielsmart.web.basic.vo.ResultVo<java.lang.String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "更新Promotion")
    @PostMapping(value = "/updatePromotion")
    public ResultVo<String> updatePromotion(@RequestBody ZBPMPromotionSubmitVo updateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        service.updatePromotion(updateVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }
}
