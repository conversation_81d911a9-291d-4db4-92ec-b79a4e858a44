package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zielsmart.mc.repository.entity.McDictionaryInfo;
import com.zielsmart.mc.repository.entity.McPlatformProperties;
import com.zielsmart.mc.repository.entity.SomStandardPriceWhiteList;
import com.zielsmart.mc.repository.entity.SysUserNeweya;
import com.zielsmart.mc.repository.mapper.McPlatformPropertiesMapper;
import com.zielsmart.mc.repository.mapper.SomStandardPriceMapper;
import com.zielsmart.mc.repository.mapper.SomStandardPriceWhiteListMapper;
import com.zielsmart.mc.vo.SomStandardPriceExtVo;
import com.zielsmart.mc.vo.SomStandardPriceVo;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListPageSearchVo;
import com.zielsmart.mc.vo.SomStandardPriceWhiteListVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.beetl.sql.core.query.LambdaQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@Slf4j
public class SomStandardPriceWhiteListService {

    @Resource
    private SomStandardPriceWhiteListMapper somStandardPriceWhiteListMapper;
    @Resource
    private SomStandardPriceMapper somStandardPriceMapper;
    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McPlatformPropertiesMapper mcPlatformPropertiesMapper;

    /**
     * save
     * 添加
     *
     * @param addVo
     * @param tokenUser
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void add(SomStandardPriceWhiteListVo addVo, TokenUserInfo tokenUser) throws ValidateException {
        if (ObjectUtil.isEmpty(addVo) || StrUtil.isBlank(addVo.getPlatform()) || StrUtil.isBlank(addVo.getSite()) ||
                StrUtil.isBlank(addVo.getSellerSku()) || StrUtil.isBlank(addVo.getSku()) || ObjectUtil.isNull(addVo.getFulfillmentChannel())
                || ObjectUtil.isNull(addVo.getStartDate()) || ObjectUtil.isNull(addVo.getEndDate())) {
            throw new ValidateException("数据存在空值，请检查数据");
        }
        //校验结束时间大于等于开始时间
        if (addVo.getEndDate().getTime() < addVo.getStartDate().getTime()) {
            throw new ValidateException("生效结束时间必须大于等于生效开始时间");
        }
        //校验是否重复
        LambdaQuery<SomStandardPriceWhiteList> query = somStandardPriceWhiteListMapper.createLambdaQuery();
        query.andEq("platform", addVo.getPlatform())
                .andEq("site", addVo.getSite())
                .andEq("seller_sku", addVo.getSellerSku())
                .andEq("sku", addVo.getSku())
                .andEq("fulfillment_channel", addVo.getFulfillmentChannel())
                .andEq("start_date", addVo.getStartDate())
                .andEq("end_date", addVo.getEndDate());
        if (query.count() > 0) {
            throw new ValidateException("白名单已存在，请勿重复维护！");
        }

        addVo.setAid(IdUtil.fastSimpleUUID());
        addVo.setCreateNum(tokenUser.getJobNumber());
        addVo.setCreateName(tokenUser.getUserName());
        addVo.setCreateTime(DateTime.now().toJdkDate());
        somStandardPriceWhiteListMapper.insert(ConvertUtils.beanConvert(addVo, SomStandardPriceWhiteList.class));
    }

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomStandardPriceWhiteListVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomStandardPriceWhiteListVo> queryByPage(SomStandardPriceWhiteListPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomStandardPriceWhiteListVo> pageResult = dynamicSqlManager.getMapper(SomStandardPriceWhiteListMapper.class).queryByPage(searchVo, pageRequest);
        return ConvertUtils.pageConvert(pageResult, SomStandardPriceWhiteListVo.class, searchVo);
    }

    /**
     * delete
     * 删除
     *
     * @param deleteVo
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    public void delete(SomStandardPriceWhiteListVo deleteVo) throws ValidateException {
        if (ObjectUtil.isEmpty(deleteVo) || deleteVo.getAidList() == null || deleteVo.getAidList().isEmpty())
            throw new ValidateException("请选择要删除的数据");
        somStandardPriceWhiteListMapper.createLambdaQuery().andIn("aid", deleteVo.getAidList()).delete();
    }

    /**
     * importExcel
     * 导入
     *
     * @param importList
     * @param tokenUser
     * <AUTHOR>
     * @history
     */
    public void importExcel(List<SomStandardPriceWhiteListVo> importList, TokenUserInfo tokenUser) throws ValidateException {
        List<SomStandardPriceWhiteListVo> insertList = new ArrayList<>();

        List<McPlatformProperties> propertiesList = mcPlatformPropertiesMapper.all();
        Map<String, String> platformPropertiesMap = propertiesList.stream().collect(Collectors.toMap(x -> x.getSite(), x -> x.getPlatform(), (k1, k2) -> k1));

        List<SomStandardPriceWhiteList> whiteLists = somStandardPriceWhiteListMapper.all();
        Map<String, SomStandardPriceWhiteList> whiteMap =
                whiteLists.stream().collect(Collectors.toMap(x -> x.getPlatform() + x.getSite() + x.getSellerSku() + x.getSku() + x.getFulfillmentChannel() + x.getStartDate() + x.getEndDate(),
                        x -> x, (k1, k2) -> k1));
        List<String> siteList = importList.stream().filter(x->x.getSite()!=null).map(x->x.getSite()).distinct().collect(Collectors.toList());
        List<SomStandardPriceExtVo> existSaleList = somStandardPriceMapper.effectivePriceData(null, siteList);
        Map<String, Boolean> existSaleMap = existSaleList.stream().collect(Collectors.toMap(x -> x.getSite() +x.getProductMainCode()+ x.getSellerSku() + x.getIsConsignmentSales(), v -> true, (x1, x2) -> x1));

        for (SomStandardPriceWhiteListVo importVo : importList) {
            String platform = platformPropertiesMap.get(importVo.getSite());
            if (platform == null) {
                throw new ValidateException("平台站点属性配置中没有维护此站点:" + importVo.getSite());
            }
            if (StrUtil.isBlank(importVo.getSite())) {
                throw new ValidateException("站点不允许为空");
            }
            if (StrUtil.isBlank(importVo.getSku())) {
                throw new ValidateException("sku不允许为空");
            }
            if (StrUtil.isBlank(importVo.getSellerSku())) {
                throw new ValidateException("展示码不允许为空");
            }
            if (ObjectUtil.isEmpty(importVo.getFulfillmentChannelName())) {
                throw new ValidateException("发货方式不允许为空");
            }
            if (ObjectUtil.isEmpty(importVo.getStartDate())) {
                throw new ValidateException("生效开始日期不允许为空");
            }
            if (ObjectUtil.isEmpty(importVo.getEndDate())) {
                throw new ValidateException("生效结束日期不允许为空");
            }
            //校验时间
            if (importVo.getEndDate().getTime() < importVo.getStartDate().getTime()) {
                throw new ValidateException("文件中存在不符合”生效开始日期≤生效结束日期“规则的数据！");
            }

            switch (importVo.getFulfillmentChannelName()) {
                case "自发":
                    importVo.setFulfillmentChannel(0);
                    break;
                case "寄售":
                    importVo.setFulfillmentChannel(1);
                    break;
            }
            if (whiteMap.containsKey(platform + importVo.getSite() + importVo.getSellerSku() + importVo.getSku() + importVo.getFulfillmentChannel() + importVo.getStartDate() + importVo.getEndDate())) {
                throw new ValidateException("展示码 " + importVo.getSellerSku() + " 已存在，批量导入失败！");
            }
            if(!existSaleMap.containsKey(importVo.getSite() + importVo.getSku() + importVo.getSellerSku() + importVo.getFulfillmentChannel())){
                throw new ValidateException("展示码 " + importVo.getSellerSku() + " 发货方式 " + importVo.getFulfillmentChannelName() + " 在定价列表中不存在");
            }

            importVo.setAid(IdUtil.fastSimpleUUID());
            importVo.setPlatform(platform);
            importVo.setCreateNum(tokenUser.getJobNumber());
            importVo.setCreateName(tokenUser.getUserName());
            importVo.setCreateTime(DateTime.now().toJdkDate());
            insertList.add(importVo);
        }
        somStandardPriceWhiteListMapper.insertBatch(ConvertUtils.listConvert(importList, SomStandardPriceWhiteList.class));
    }

    public List<Map<String, Object>> selectSku(SomStandardPriceWhiteListVo addVo) {
        SomStandardPriceExtVo search = new SomStandardPriceExtVo();
        search.setSite(addVo.getSite());
        List<SomStandardPriceExtVo> list = somStandardPriceMapper.effectivePriceData(search, null);
        if (list == null || list.isEmpty()) {
            new ArrayList<>();
        }
        List<Map<String, Object>> result = new ArrayList<>();

        //根据sku分组
        Map<String, List<SomStandardPriceExtVo>> skuMap = list.stream().collect(Collectors.groupingBy(SomStandardPriceVo::getProductMainCode));
        for (Map.Entry<String, List<SomStandardPriceExtVo>> entry : skuMap.entrySet()) {
            String sku = entry.getKey();
            Map<String, Object> skuData = new HashMap<>();
            skuData.put("sku",sku);
            List<Map<String, Object>> sellerSkuResult = new ArrayList<>();

            List<SomStandardPriceExtVo> sellerSkuList = entry.getValue();
            //根据展示码分组 获取发货方式
            Map<String, List<SomStandardPriceExtVo>> sellerSkuMap = sellerSkuList.stream().collect(Collectors.groupingBy(SomStandardPriceVo::getSellerSku));
            for (Map.Entry<String, List<SomStandardPriceExtVo>> sellerSkuEntry : sellerSkuMap.entrySet()) {
                String sellerSku = sellerSkuEntry.getKey();
                Map<String, Object> sellerSkuData = new HashMap<>();
                List<SomStandardPriceExtVo> value = sellerSkuEntry.getValue();

                List<Map<String, Object>> consignmentSalesList = value.stream().filter(x -> x.getIsConsignmentSales() != null).map(x -> {
                    Integer isConsignmentSales = x.getIsConsignmentSales();
                    Map<String, Object> data = new HashMap<>();
                    data.put("isConsignmentSales", isConsignmentSales);
                    data.put("isConsignmentSalesName", isConsignmentSales == 1 ? "寄售" : "自发");
                    return data;
                }).collect(Collectors.toList());

                sellerSkuData.put("sellerSku",sellerSku);
                sellerSkuData.put("consignmentSalesList",consignmentSalesList);
                sellerSkuResult.add(sellerSkuData);
            }
            skuData.put("sellerSkuList",sellerSkuResult);
            result.add(skuData);
        }
        return result;
    }

    public String exportExcel(SomStandardPriceWhiteListPageSearchVo searchVo) {
        List<SomStandardPriceWhiteListVo> exportVoList = somStandardPriceWhiteListMapper.exportExcel(searchVo);
        if (exportVoList.isEmpty()) {
            return null;
        }

        if (CollectionUtil.isNotEmpty(exportVoList)) {
            try {
                Workbook workbook;
                ExportParams params = new ExportParams(null, "定价白名单管理");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomStandardPriceWhiteListVo.class, exportVoList);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                return Base64.getEncoder().encodeToString(barray);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
