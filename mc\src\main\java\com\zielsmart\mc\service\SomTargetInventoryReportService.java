package com.zielsmart.mc.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zielsmart.mc.repository.entity.McStockInfo;
import com.zielsmart.mc.repository.entity.SomTargetWarehouseConfig;
import com.zielsmart.mc.repository.mapper.McStockInfoMapper;
import com.zielsmart.mc.repository.mapper.SomTargetListingMapper;
import com.zielsmart.mc.repository.mapper.SomTargetWarehouseConfigMapper;
import com.zielsmart.mc.vo.SomTargetInventoryReportSearchVo;
import com.zielsmart.mc.vo.SomTargetInventoryReportVo;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.beetl.sql.core.DynamicSqlManager;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SomTargetInventoryReportService {

    @Resource
    private DynamicSqlManager dynamicSqlManager;
    @Resource
    private McStockInfoMapper mcStockInfoMapper;
    @Resource
    private SomTargetListingMapper targetListingMapper;
    @Resource
    private SomTargetWarehouseConfigMapper targetConfigMapper;

    public PageVo<SomTargetInventoryReportVo> queryTargetInventoryReport(SomTargetInventoryReportSearchVo searchVo) throws JsonProcessingException {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomTargetInventoryReportVo> pageResult = targetListingMapper.queryTargetInventoryReport(searchVo, pageRequest);

        if (!pageResult.getList().isEmpty()) {
            List<McStockInfo> stockList = mcStockInfoMapper.createQuery().andIn("product_main_code", pageResult.getList().stream().map(x -> x.getProductMainCode()).collect(Collectors.toList())).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
            List<SomTargetWarehouseConfig> warehouseList = targetConfigMapper.all();
            Map<String, List<SomTargetWarehouseConfig>> warehouseByCode = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode()));

            for (SomTargetInventoryReportVo report : pageResult.getList()) {
                List<SomTargetWarehouseConfig> configList = warehouseByCode.getOrDefault(report.getWarehouseCode(), new ArrayList<>());
                configList.stream().map(x -> formatInteger(stockMap.getOrDefault(x.getUseableWarehouseCode() + x.getUseableStorageCode() + report.getProductMainCode(), new McStockInfo()).getTotalStock())).reduce(Integer::sum).ifPresent(x -> report.setSaleInventory(x));
                if (report.getSaleInventory() == null) {
                    report.setSaleInventory(0);
                }
                if (report.getSafetyStock() == null) {
                    report.setSafetyStock(0);
                }
                report.setSaleInventory(report.getSaleInventory()-report.getSafetyStock());
                //if stock is less than 0,set the value to 0
                if (report.getSaleInventory() < 0) {
                    report.setSaleInventory(0);
                }
            }
        }
        return ConvertUtils.pageConvert(pageResult, SomTargetInventoryReportVo.class,searchVo);
    }

    private int formatInteger(Integer integer) {
        if (integer == null) {
            return 0;
        }
        return integer.intValue();
    }

    public String export(SomTargetInventoryReportSearchVo searchVo) throws ValidateException, JsonProcessingException {
        searchVo.setPageSize(Integer.MAX_VALUE);
        searchVo.setCurrent(1);
        List<SomTargetInventoryReportVo> records =targetListingMapper.exportTargetInventoryReport(searchVo);
        if (!records.isEmpty()) {
            List<McStockInfo> stockList = mcStockInfoMapper.createQuery().andIn("product_main_code", records.stream().map(x -> x.getProductMainCode()).distinct().collect(Collectors.toList())).select();
            Map<String, McStockInfo> stockMap = stockList.stream().collect(Collectors.toMap(x -> x.getWarehouseCode() + x.getSlCode() + x.getProductMainCode(), Function.identity(), (x1, x2) -> x1));
            List<SomTargetWarehouseConfig> warehouseList = targetConfigMapper.all();
            Map<String, List<SomTargetWarehouseConfig>> warehouseByCode = warehouseList.stream().collect(Collectors.groupingBy(e -> e.getWarehouseCode()));

            for (SomTargetInventoryReportVo report : records) {
                List<SomTargetWarehouseConfig> configList = warehouseByCode.getOrDefault(report.getWarehouseCode(), new ArrayList<>());
                configList.stream().map(x -> formatInteger(stockMap.getOrDefault(x.getUseableWarehouseCode() + x.getUseableStorageCode() + report.getProductMainCode(), new McStockInfo()).getTotalStock())).reduce(Integer::sum).ifPresent(x -> report.setSaleInventory(x));
                if (report.getSaleInventory() == null) {
                    report.setSaleInventory(0);
                }
                if (report.getSafetyStock() == null) {
                    report.setSafetyStock(0);
                }
                report.setSaleInventory(report.getSaleInventory()-report.getSafetyStock());
                //if stock is less than 0,set the value to 0
                if (report.getSaleInventory() < 0) {
                    report.setSaleInventory(0);
                }
            }
            Workbook workbook = null;
            try {
                ExportParams params = new ExportParams(null, "可售库存报表");
                params.setType(ExcelType.XSSF);
                params.setAutoSize(true);
                workbook = ExcelExportUtil.exportExcel(params, SomTargetInventoryReportVo.class, records);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                byte[] barray = bos.toByteArray();
                bos.close();
                workbook.close();

                return Base64.getEncoder().encodeToString(barray);
            } catch (Exception e) {
                throw new ValidateException("导出可售库存报表失败：" + e.getMessage());
            }
        }
        return null;
    }
}
