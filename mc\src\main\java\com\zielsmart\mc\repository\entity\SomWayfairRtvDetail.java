package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/*
* wayfair索赔明细表
* gen by 代码生成器 2024-05-20
*/

@Table(name="mc.som_wayfair_rtv_detail")
public class SomWayfairRtvDetail implements java.io.Serializable {
	/**
	 * 主键
	 */
	@AssignID
	private String aid ;
	/**
	 * 主表ID
	 */
	@Column("m_aid")
	private String mAid ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * 产品标准
	 */
	@Column("product_bucket")
	private String productBucket ;
	/**
	 * 展示码数量
	 */
	@Column("quantity")
	private Integer quantity ;
	/**
	 * RTV退回的退回单编码
	 */
	@Column("return_order_number")
	private String returnOrderNumber ;
	/**
	 * wayfair平台给到的此产品的长度
	 */
	@Column("wayfair_dimensions_l")
	private BigDecimal wayfairDimensionsL ;
	/**
	 * wayfair平台给到的此产品的宽度
	 */
	@Column("wayfair_dimensions_w")
	private BigDecimal wayfairDimensionsW ;
	/**
	 * wayfair平台给到的此产品的高度
	 */
	@Column("wayfair_dimensions_h")
	private BigDecimal wayfairDimensionsH ;
	/**
	 * wayfair平台给到的此产品的重量
	 */
	@Column("wayfair_weight")
	private BigDecimal wayfairWeight ;
	/**
	 * wayfair平台给到的此产品的周长
	 */
	@Column("wayfair_girth")
	private BigDecimal wayfairGirth ;
	/**
	 * 收取费用的件数
	 */
	@Column("charge_unit")
	private Integer chargeUnit ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 收取费用的总额
	 */
	@Column("charge_amount")
	private BigDecimal chargeAmount ;
	/**
	 * 单个产品的体积（m³）
	 */
	@Column("som_volume")
	private BigDecimal somVolume ;
	/**
	 * 产品的单个费用
	 */
	@Column("som_fee")
	private BigDecimal somFee ;
	/**
	 * 产品的总费用
	 */
	@Column("som_storage_fee_amount")
	private BigDecimal somStorageFeeAmount ;
	/**
	 * 仓储费多收的金额
	 */
	@Column("overcharged")
	private BigDecimal overcharged ;
	/**
	 * VAS标识 0.否 1.是
	 */
	@Column("vas_flag")
	private Integer vasFlag ;


	/**
	 * 发票编号
	 */
	@Column("invoice")
	private String invoice ;

	public SomWayfairRtvDetail() {
	}


	public String getInvoice() {
		return invoice;
	}

	public void setInvoice(String invoice) {
		this.invoice = invoice;
	}

	/**
	* 主键
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 主表ID
	*@return
	*/
	public String getMAid(){
		return  mAid;
	}
	/**
	* 主表ID
	*@param  mAid
	*/
	public void setMAid(String mAid ){
		this.mAid = mAid;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* 产品标准
	*@return
	*/
	public String getProductBucket(){
		return  productBucket;
	}
	/**
	* 产品标准
	*@param  productBucket
	*/
	public void setProductBucket(String productBucket ){
		this.productBucket = productBucket;
	}
	/**
	* 展示码数量
	*@return
	*/
	public Integer getQuantity(){
		return  quantity;
	}
	/**
	* 展示码数量
	*@param  quantity
	*/
	public void setQuantity(Integer quantity ){
		this.quantity = quantity;
	}
	/**
	* RTV退回的退回单编码
	*@return
	*/
	public String getReturnOrderNumber(){
		return  returnOrderNumber;
	}
	/**
	* RTV退回的退回单编码
	*@param  returnOrderNumber
	*/
	public void setReturnOrderNumber(String returnOrderNumber ){
		this.returnOrderNumber = returnOrderNumber;
	}
	/**
	* wayfair平台给到的此产品的长度
	*@return
	*/
	public BigDecimal getWayfairDimensionsL(){
		return  wayfairDimensionsL;
	}
	/**
	* wayfair平台给到的此产品的长度
	*@param  wayfairDimensionsL
	*/
	public void setWayfairDimensionsL(BigDecimal wayfairDimensionsL ){
		this.wayfairDimensionsL = wayfairDimensionsL;
	}
	/**
	* wayfair平台给到的此产品的宽度
	*@return
	*/
	public BigDecimal getWayfairDimensionsW(){
		return  wayfairDimensionsW;
	}
	/**
	* wayfair平台给到的此产品的宽度
	*@param  wayfairDimensionsW
	*/
	public void setWayfairDimensionsW(BigDecimal wayfairDimensionsW ){
		this.wayfairDimensionsW = wayfairDimensionsW;
	}
	/**
	* wayfair平台给到的此产品的高度
	*@return
	*/
	public BigDecimal getWayfairDimensionsH(){
		return  wayfairDimensionsH;
	}
	/**
	* wayfair平台给到的此产品的高度
	*@param  wayfairDimensionsH
	*/
	public void setWayfairDimensionsH(BigDecimal wayfairDimensionsH ){
		this.wayfairDimensionsH = wayfairDimensionsH;
	}
	/**
	* wayfair平台给到的此产品的重量
	*@return
	*/
	public BigDecimal getWayfairWeight(){
		return  wayfairWeight;
	}
	/**
	* wayfair平台给到的此产品的重量
	*@param  wayfairWeight
	*/
	public void setWayfairWeight(BigDecimal wayfairWeight ){
		this.wayfairWeight = wayfairWeight;
	}
	/**
	* wayfair平台给到的此产品的周长
	*@return
	*/
	public BigDecimal getWayfairGirth(){
		return  wayfairGirth;
	}
	/**
	* wayfair平台给到的此产品的周长
	*@param  wayfairGirth
	*/
	public void setWayfairGirth(BigDecimal wayfairGirth ){
		this.wayfairGirth = wayfairGirth;
	}
	/**
	* 收取费用的件数
	*@return
	*/
	public Integer getChargeUnit(){
		return  chargeUnit;
	}
	/**
	* 收取费用的件数
	*@param  chargeUnit
	*/
	public void setChargeUnit(Integer chargeUnit ){
		this.chargeUnit = chargeUnit;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 收取费用的总额
	*@return
	*/
	public BigDecimal getChargeAmount(){
		return  chargeAmount;
	}
	/**
	* 收取费用的总额
	*@param  chargeAmount
	*/
	public void setChargeAmount(BigDecimal chargeAmount ){
		this.chargeAmount = chargeAmount;
	}
	/**
	* 单个产品的体积（m³）
	*@return
	*/
	public BigDecimal getSomVolume(){
		return  somVolume;
	}
	/**
	* 单个产品的体积（m³）
	*@param  somVolume
	*/
	public void setSomVolume(BigDecimal somVolume ){
		this.somVolume = somVolume;
	}
	/**
	* 产品的单个费用
	*@return
	*/
	public BigDecimal getSomFee(){
		return  somFee;
	}
	/**
	* 产品的单个费用
	*@param  somFee
	*/
	public void setSomFee(BigDecimal somFee ){
		this.somFee = somFee;
	}
	/**
	* 产品的总费用
	*@return
	*/
	public BigDecimal getSomStorageFeeAmount(){
		return  somStorageFeeAmount;
	}
	/**
	* 产品的总费用
	*@param  somStorageFeeAmount
	*/
	public void setSomStorageFeeAmount(BigDecimal somStorageFeeAmount ){
		this.somStorageFeeAmount = somStorageFeeAmount;
	}
	/**
	* 仓储费多收的金额
	*@return
	*/
	public BigDecimal getOvercharged(){
		return  overcharged;
	}
	/**
	* 仓储费多收的金额
	*@param  overcharged
	*/
	public void setOvercharged(BigDecimal overcharged ){
		this.overcharged = overcharged;
	}
	/**
	* VAS标识 0.否 1.是
	*@return
	*/
	public Integer getVasFlag(){
		return  vasFlag;
	}
	/**
	* VAS标识 0.否 1.是
	*@param  vasFlag
	*/
	public void setVasFlag(Integer vasFlag ){
		this.vasFlag = vasFlag;
	}

}
