package com.zielsmart.mc.service;

import cn.hutool.core.util.ObjectUtil;
import com.zielsmart.mc.repository.mapper.SomKauflandListingMapper;
import com.zielsmart.mc.vo.SomKauflandListingExtVo;
import com.zielsmart.mc.vo.SomKauflandListingPageSearchVo;
import com.zielsmart.web.basic.util.ConvertUtils;
import com.zielsmart.web.basic.vo.PageVo;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.service
 * @title PlatformPropertiesService
 * @description
 * @date 2021-08-09 15:55:42
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class SomKauflandListingService {

    @Resource
    private SomKauflandListingMapper somKauflandListingMapper;

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @return {@link com.zielsmart.web.basic.vo.PageVo<com.zielsmart.mc.vo.SomKauflandListingExtVo>}
     * <AUTHOR>
     * @history
     */
    public PageVo<SomKauflandListingExtVo> queryByPage(SomKauflandListingPageSearchVo searchVo) {
        PageRequest pageRequest = DefaultPageRequest.of(searchVo.getCurrent(), searchVo.getPageSize());
        PageResult<SomKauflandListingExtVo> pageResult = somKauflandListingMapper.queryByPage(searchVo, pageRequest);
        pageResult.getList().forEach(f->{
            f.setPriceExt(ObjectUtil.isNotEmpty(f.getPrice())? BigDecimal.valueOf(f.getPrice()).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO);
            f.setListingPriceExt(ObjectUtil.isNotEmpty(f.getListingPrice())? BigDecimal.valueOf(f.getListingPrice()).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO);
            f.setMinimumPriceExt(ObjectUtil.isNotEmpty(f.getMinimumPrice())? BigDecimal.valueOf(f.getMinimumPrice()).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO);
        });
        return ConvertUtils.pageConvert(pageResult, SomKauflandListingExtVo.class, searchVo);
    }
}
