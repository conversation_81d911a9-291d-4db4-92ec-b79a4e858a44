package com.zielsmart.mc.repository.mapper;

import com.zielsmart.mc.repository.entity.SomOffersInfo;
import com.zielsmart.mc.vo.SomOffersInfoExtVo;
import com.zielsmart.mc.vo.SomOffersInfoPageSearchVo;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;
import org.beetl.sql.mapper.annotation.Param;
import org.beetl.sql.mapper.annotation.SqlResource;

import java.util.List;
/*
 *
 * gen by 代码生成器 mapper 2022-11-30
 */

@SqlResource("somOffersInfo")
public interface SomOffersInfoMapper extends BaseMapper<SomOffersInfo> {

    /**
     * querytotalCount
     * 查询总数
     * @param searchVo
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOffersInfoExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomOffersInfoExtVo> querytotalCount(@Param("searchVo") SomOffersInfoPageSearchVo searchVo);

    /**
     * queryByPage
     * 分页查询
     *
     * @param searchVo
     * @param pageRequest
     * @return {@link org.beetl.sql.core.page.PageResult<com.zielsmart.mc.vo.SomOffersInfoVo>}
     * <AUTHOR>
     * @history
     */
    DefaultPageResult<SomOffersInfoExtVo> queryByPage(@Param("searchVo") SomOffersInfoPageSearchVo searchVo, PageRequest pageRequest);

    /**
     * queryProdutNum
     * 查询产品跟卖数量
     * @param sellerIdList
     * @param marketplaceNameList
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOffersInfoExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomOffersInfoExtVo> queryProdutNum(@Param("sellerIdList")List<String> sellerIdList, @Param("marketplaceNameList")List<String> marketplaceNameList);

    /**
     * queryProdutNum
     * 查询产品跟卖数量
     * @param sellerIdList
     * @return {@link java.util.List<com.zielsmart.mc.vo.SomOffersInfoExtVo>}
     * <AUTHOR>
     * @history
     */
    List<SomOffersInfoExtVo> queryProdutNum(@Param("sellerIdList")List<String> sellerIdList);
}
