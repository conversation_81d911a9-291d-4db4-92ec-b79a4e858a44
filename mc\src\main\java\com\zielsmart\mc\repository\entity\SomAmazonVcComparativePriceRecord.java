package com.zielsmart.mc.repository.entity;

import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Column;
import org.beetl.sql.annotation.entity.Table;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
/*
* VC比价记录表
* gen by 代码生成器 2025-01-21
*/

@Table(name="mc.som_amazon_vc_comparative_price_record")
public class SomAmazonVcComparativePriceRecord implements java.io.Serializable {
	/**
	 * 主键aid
	 */
	@AssignID
	private String aid ;
	/**
	 * 站点
	 */
	@Column("site")
	private String site ;
	/**
	 * 展示码
	 */
	@Column("seller_sku")
	private String sellerSku ;
	/**
	 * SKU
	 */
	@Column("sku")
	private String sku ;
	/**
	 * ASIN
	 */
	@Column("asin")
	private String asin ;
	/**
	 * 建议零售价
	 */
	@Column("recommended_retail_price")
	private BigDecimal recommendedRetailPrice ;
	/**
	 * 前台售价
	 */
	@Column("price")
	private BigDecimal price ;
	/**
	 * 前台售价来源：10.BI 20.RPA
	 */
	@Column("price_source")
	private Integer priceSource ;
	/**
	 * 10.正在参加 99.未参加
	 */
	@Column("on_promotion")
	private Integer onPromotion ;
	/**
	 * 预估促销价
	 */
	@Column("estimate_deal_price")
	private BigDecimal estimateDealPrice ;
	/**
	 * 专款 专项
	 */
	@Column("funding")
	private BigDecimal funding ;
	/**
	 * 币种
	 */
	@Column("currency")
	private String currency ;
	/**
	 * 比价平台
	 */
	@Column("comparative_channel")
	private String comparativeChannel ;
	/**
	 * 比价价格
	 */
	@Column("competitive_price_threshold")
	private BigDecimal competitivePriceThreshold ;
	/**
	 * 比价类型：10.低价 20.高价
	 */
	@Column("comparative_type")
	private Integer comparativeType ;
	/**
	 * 三方比价的产品链接URL
	 */
	@Column("comparative_url")
	private String comparativeUrl ;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	/**
	 * 最新处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	 */
	@Column("handle_status")
	private Integer handleStatus ;
	/**
	 * 最新处理人工号
	 */
	@Column("handle_person_code")
	private String handlePersonCode ;
	/**
	 * 最新处理人姓名
	 */
	@Column("handle_person_name")
	private String handlePersonName ;
	/**
	 * 最新比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	 */
	@Column("comparative_reason")
	private Integer comparativeReason ;

	public SomAmazonVcComparativePriceRecord() {
	}

	/**
	* 主键aid
	*@return
	*/
	public String getAid(){
		return  aid;
	}
	/**
	* 主键aid
	*@param  aid
	*/
	public void setAid(String aid ){
		this.aid = aid;
	}
	/**
	* 站点
	*@return
	*/
	public String getSite(){
		return  site;
	}
	/**
	* 站点
	*@param  site
	*/
	public void setSite(String site ){
		this.site = site;
	}
	/**
	* 展示码
	*@return
	*/
	public String getSellerSku(){
		return  sellerSku;
	}
	/**
	* 展示码
	*@param  sellerSku
	*/
	public void setSellerSku(String sellerSku ){
		this.sellerSku = sellerSku;
	}
	/**
	* SKU
	*@return
	*/
	public String getSku(){
		return  sku;
	}
	/**
	* SKU
	*@param  sku
	*/
	public void setSku(String sku ){
		this.sku = sku;
	}
	/**
	* ASIN
	*@return
	*/
	public String getAsin(){
		return  asin;
	}
	/**
	* ASIN
	*@param  asin
	*/
	public void setAsin(String asin ){
		this.asin = asin;
	}
	/**
	* 建议零售价
	*@return
	*/
	public BigDecimal getRecommendedRetailPrice(){
		return  recommendedRetailPrice;
	}
	/**
	* 建议零售价
	*@param  recommendedRetailPrice
	*/
	public void setRecommendedRetailPrice(BigDecimal recommendedRetailPrice ){
		this.recommendedRetailPrice = recommendedRetailPrice;
	}
	/**
	* 前台售价
	*@return
	*/
	public BigDecimal getPrice(){
		return  price;
	}
	/**
	* 前台售价
	*@param  price
	*/
	public void setPrice(BigDecimal price ){
		this.price = price;
	}
	/**
	* 前台售价来源：10.BI 20.RPA
	*@return
	*/
	public Integer getPriceSource(){
		return  priceSource;
	}
	/**
	* 前台售价来源：10.BI 20.RPA
	*@param  priceSource
	*/
	public void setPriceSource(Integer priceSource ){
		this.priceSource = priceSource;
	}
	/**
	* 10.正在参加 99.未参加
	*@return
	*/
	public Integer getonPromotion(){
		return  onPromotion;
	}
	/**
	* 10.正在参加 99.未参加
	*@param  onPromotion
	*/
	public void setonPromotion(Integer onPromotion ){
		this.onPromotion = onPromotion;
	}
	/**
	* 预估促销价
	*@return
	*/
	public BigDecimal getEstimateDealPrice(){
		return  estimateDealPrice;
	}
	/**
	* 预估促销价
	*@param  estimateDealPrice
	*/
	public void setEstimateDealPrice(BigDecimal estimateDealPrice ){
		this.estimateDealPrice = estimateDealPrice;
	}
	/**
	* 专款 专项
	*@return
	*/
	public BigDecimal getFunding(){
		return  funding;
	}
	/**
	* 专款 专项
	*@param  funding
	*/
	public void setFunding(BigDecimal funding ){
		this.funding = funding;
	}
	/**
	* 币种
	*@return
	*/
	public String getCurrency(){
		return  currency;
	}
	/**
	* 币种
	*@param  currency
	*/
	public void setCurrency(String currency ){
		this.currency = currency;
	}
	/**
	* 比价平台
	*@return
	*/
	public String getComparativeChannel(){
		return  comparativeChannel;
	}
	/**
	* 比价平台
	*@param  comparativeChannel
	*/
	public void setComparativeChannel(String comparativeChannel ){
		this.comparativeChannel = comparativeChannel;
	}
	/**
	* 比价价格
	*@return
	*/
	public BigDecimal getCompetitivePriceThreshold(){
		return  competitivePriceThreshold;
	}
	/**
	* 比价价格
	*@param  competitivePriceThreshold
	*/
	public void setCompetitivePriceThreshold(BigDecimal competitivePriceThreshold ){
		this.competitivePriceThreshold = competitivePriceThreshold;
	}
	/**
	* 比价类型：10.低价 20.高价
	*@return
	*/
	public Integer getComparativeType(){
		return  comparativeType;
	}
	/**
	* 比价类型：10.低价 20.高价
	*@param  comparativeType
	*/
	public void setComparativeType(Integer comparativeType ){
		this.comparativeType = comparativeType;
	}
	/**
	* 三方比价的产品链接URL
	*@return
	*/
	public String getComparativeUrl(){
		return  comparativeUrl;
	}
	/**
	* 三方比价的产品链接URL
	*@param  comparativeUrl
	*/
	public void setComparativeUrl(String comparativeUrl ){
		this.comparativeUrl = comparativeUrl;
	}
	/**
	* 创建时间
	*@return
	*/
	public Date getCreateTime(){
		return  createTime;
	}
	/**
	* 创建时间
	*@param  createTime
	*/
	public void setCreateTime(Date createTime ){
		this.createTime = createTime;
	}
	/**
	* 最新处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	*@return
	*/
	public Integer getHandleStatus(){
		return  handleStatus;
	}
	/**
	* 最新处理状态：10.未处理 20.销售处理中 30.VM处理中 40.VM反馈结果 50.处理完成
	*@param  handleStatus
	*/
	public void setHandleStatus(Integer handleStatus ){
		this.handleStatus = handleStatus;
	}
	/**
	* 最新处理人工号
	*@return
	*/
	public String getHandlePersonCode(){
		return  handlePersonCode;
	}
	/**
	* 最新处理人工号
	*@param  handlePersonCode
	*/
	public void setHandlePersonCode(String handlePersonCode ){
		this.handlePersonCode = handlePersonCode;
	}
	/**
	* 最新处理人姓名
	*@return
	*/
	public String getHandlePersonName(){
		return  handlePersonName;
	}
	/**
	* 最新处理人姓名
	*@param  handlePersonName
	*/
	public void setHandlePersonName(String handlePersonName ){
		this.handlePersonName = handlePersonName;
	}
	/**
	* 最新比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	*@return
	*/
	public Integer getComparativeReason(){
		return  comparativeReason;
	}
	/**
	* 最新比价原因:10.内部三方渠道比价 20.非内部渠道比价 30.未找到比价平台 40.内部渠道价格已修复 50.错误比价 60.亚马逊自动调价 70.RRP异常 80.亚马逊自动促销 99.未知原因
	*@param  comparativeReason
	*/
	public void setComparativeReason(Integer comparativeReason ){
		this.comparativeReason = comparativeReason;
	}

}
