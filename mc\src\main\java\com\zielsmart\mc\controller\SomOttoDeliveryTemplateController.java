package com.zielsmart.mc.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.zielsmart.mc.repository.entity.SomOttoDeliveryTemplateProduct;
import com.zielsmart.mc.repository.entity.SomOttoListing;
import com.zielsmart.mc.service.SomOttoDeliveryTemplateService;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplatePageSearchVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplateProductVo;
import com.zielsmart.mc.vo.SomOttoDeliveryTemplateVo;
import com.zielsmart.web.basic.BasicController;
import com.zielsmart.web.basic.annotation.TokenUser;
import com.zielsmart.web.basic.exception.ValidateException;
import com.zielsmart.web.basic.vo.PageVo;
import com.zielsmart.web.basic.vo.ResultVo;
import com.zielsmart.web.basic.vo.TokenUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @package com.zielsmart.mc.controller
 * @title SomOttoDeliveryTemplateController
 * @description
 * @date 2021-08-09 15:48:53
 * @Copyright 2019 www.zielsmart.com Inc. All rights reserved
 * 注意：本内容仅限于郑州致欧网络科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Controller
@RequestMapping(value = "/somOttoDeliveryTemplate", consumes = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "OTTO运费模版管理管理")
public class SomOttoDeliveryTemplateController extends BasicController{

    @Resource
    SomOttoDeliveryTemplateService somOttoDeliveryTemplateService;

    /**
     * queryByPage
     *
     * @param searchVo
     * @return {@link ResultVo< PageVo< SomOttoDeliveryTemplateVo>>}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "分页查询")
    @PostMapping(value = "/query-by-page")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomOttoDeliveryTemplateVo>> queryByPage(@RequestBody SomOttoDeliveryTemplatePageSearchVo searchVo) {
        return ResultVo.ofSuccess(somOttoDeliveryTemplateService.queryByPage(searchVo));
    }

    /**
     * 添加/编辑
     *
     * @param somOttoDeliveryTemplateVo
     * @param tokenUser
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "添加/编辑")
    @PostMapping(value = "/save")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> save(@RequestBody @Validated SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somOttoDeliveryTemplateService.save(somOttoDeliveryTemplateVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }


    @Operation(summary = "更新")
    @PostMapping(value = "/update")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> update(@RequestBody SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somOttoDeliveryTemplateService.update(somOttoDeliveryTemplateVo,tokenUser);
        return ResultVo.ofSuccess(null);
    }

    /**
     * delete
     *
     * @param somOttoDeliveryTemplateVo
     * @return {@link ResultVo< String>}
     * @throws {@link ValidateException}
     * <AUTHOR>
     * @history
     */
    @Operation(summary = "删除")
    @PostMapping(value = "/delete")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delete(@RequestBody SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        somOttoDeliveryTemplateService.delete(somOttoDeliveryTemplateVo);
        return ResultVo.ofSuccess(null);
    }

    @Operation(summary = "OTTO Listing下拉框")
    @PostMapping(value = "/listing")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<PageVo<SomOttoListing>> listing(@RequestBody SomOttoDeliveryTemplatePageSearchVo searchVo) throws ValidateException {
        return ResultVo.ofSuccess(somOttoDeliveryTemplateService.listing(searchVo));
    }

    @Operation(summary = "获取所有绑定的展示码")
    @PostMapping(value = "/queryBindSellerSku")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<List<SomOttoDeliveryTemplateProductVo>> queryBindSellerSku(@RequestBody SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        return ResultVo.ofSuccess(somOttoDeliveryTemplateService.queryBindSellerSku(somOttoDeliveryTemplateVo));
    }

    @Operation(summary = "绑定新增展示码")
    @PostMapping(value = "/bindSellerSku")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> bindSellerSku(@RequestBody SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws ValidateException {
        somOttoDeliveryTemplateService.bindSellerSku(somOttoDeliveryTemplateVo,tokenUser);
        return ResultVo.ofSuccess();
    }

    @Operation(summary = "删除绑定展示码")
    @PostMapping(value = "/delBindSellerSku")
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> delBindSellerSku(@RequestBody SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo) throws ValidateException {
        somOttoDeliveryTemplateService.delBindSellerSku(somOttoDeliveryTemplateVo);
        return ResultVo.ofSuccess();
    }


    @Operation(summary = "导入绑定数据")
    @PostMapping(value = "/importBindData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(mediaType = "application/json"))
    public ResultVo<String> importBindData(@RequestParam("file") MultipartFile file,SomOttoDeliveryTemplateVo somOttoDeliveryTemplateVo, @Parameter(hidden = true) @TokenUser TokenUserInfo tokenUser) throws Exception {
        ImportParams importParams = new ImportParams();
        //头占用的行数
        importParams.setHeadRows(1);
        //标题占用的行数，没有写0
        importParams.setTitleRows(0);
        ExcelImportResult<SomOttoDeliveryTemplateVo> result = null;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), SomOttoDeliveryTemplateVo.class, importParams);
        } catch (Exception e) {
            throw new ValidateException("导入模板有误,请检查模板");
        }
        List<SomOttoDeliveryTemplateVo> list = result.getList();
        if (list.isEmpty()) {
            throw new ValidateException("导入数据为空,请检查数据");
        }
        somOttoDeliveryTemplateService.importBindData(list,somOttoDeliveryTemplateVo,tokenUser);
        return ResultVo.ofSuccess();
    }

//    @Operation(summary = "导出")
//    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public ResultVo<String> export(@RequestBody SomOttoDeliveryTemplatePageSearchVo searchVo){
//        String data = somOttoDeliveryTemplateService.export(searchVo);
//        if (StrUtil.isEmpty(data)) {
//            return ResultVo.ofFail("导出数据为空");
//        }
//        return ResultVo.ofSuccess(data);
//    }


    @Operation(summary = "下载导入模板")
    @GetMapping(value="/download")
    public String downloadExcel(){
        return "forward:/static/excel/OttoDeliveryTemplate.xlsx";
    }
}
